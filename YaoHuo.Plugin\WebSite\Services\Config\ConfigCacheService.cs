using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Web;
using System.Web.Caching;
using Newtonsoft.Json;

namespace YaoHuo.Plugin.WebSite.Tool
{
    /// <summary>
    /// 配置文件缓存服务
    /// 利用服务器充足内存，提供高性能的配置缓存机制
    /// </summary>
    public static class ConfigCacheService
    {
        private static readonly Dictionary<string, DateTime> _lastModified = new Dictionary<string, DateTime>();
        private static readonly object _lockObject = new object();

        /// <summary>
        /// 获取配置数据（带缓存）
        /// </summary>
        /// <typeparam name="T">配置数据类型</typeparam>
        /// <param name="configName">配置名称</param>
        /// <param name="relativePath">相对路径</param>
        /// <param name="cacheHours">缓存小时数，默认24小时</param>
        /// <returns>配置数据</returns>
        public static T GetConfig<T>(string configName, string relativePath, int cacheHours = 24) where T : class
        {
            string cacheKey = $"Config_{configName}";
            string fullPath = HttpContext.Current.Server.MapPath(relativePath);

            lock (_lockObject)
            {
                // 检查文件是否被修改
                if (File.Exists(fullPath))
                {
                    var fileInfo = new FileInfo(fullPath);
                    if (_lastModified.ContainsKey(cacheKey) &&
                        _lastModified[cacheKey] != fileInfo.LastWriteTime)
                    {
                        // 文件被修改，清除缓存
                        HttpContext.Current.Cache.Remove(cacheKey);
                    }
                }

                // 尝试从缓存获取
                var cached = HttpContext.Current.Cache[cacheKey] as T;
                if (cached != null)
                {
                    return cached;
                }

                // 缓存未命中，从文件加载
                return LoadAndCacheConfig<T>(configName, fullPath, cacheKey, cacheHours);
            }
        }

        /// <summary>
        /// 加载配置并存入缓存
        /// </summary>
        private static T LoadAndCacheConfig<T>(string configName, string fullPath, string cacheKey, int cacheHours) where T : class
        {
            if (!File.Exists(fullPath))
            {
                throw new FileNotFoundException($"配置文件不存在: {fullPath}");
            }

            // 读取并解析JSON文件
            string jsonContent = File.ReadAllText(fullPath, Encoding.UTF8);
            var configData = JsonConvert.DeserializeObject<T>(jsonContent);

            if (configData == null)
            {
                throw new InvalidOperationException($"配置文件解析失败: {configName}");
            }

            // 存入缓存 - 使用高优先级和长过期时间
            HttpContext.Current.Cache.Insert(
                cacheKey,
                configData,
                null, // 不使用文件依赖，手动检查文件修改时间
                DateTime.Now.AddHours(cacheHours),
                TimeSpan.Zero,
                CacheItemPriority.High, // 高优先级，充分利用大内存
                (key, value, reason) => {
                    // 缓存被移除时的回调
                    lock (_lockObject)
                    {
                        _lastModified.Remove(key);
                    }
                }
            );

            // 记录文件修改时间
            var fileInfo = new FileInfo(fullPath);
            _lastModified[cacheKey] = fileInfo.LastWriteTime;

            return configData;
        }

        /// <summary>
        /// 手动刷新特定配置
        /// </summary>
        /// <param name="configName">配置名称</param>
        public static void RefreshConfig(string configName)
        {
            string cacheKey = $"Config_{configName}";
            lock (_lockObject)
            {
                HttpContext.Current.Cache.Remove(cacheKey);
                _lastModified.Remove(cacheKey);
            }
        }

        /// <summary>
        /// 清空所有配置缓存
        /// </summary>
        public static void ClearAllConfigs()
        {
            lock (_lockObject)
            {
                var keysToRemove = new List<string>();
                foreach (System.Collections.DictionaryEntry entry in HttpContext.Current.Cache)
                {
                    if (entry.Key.ToString().StartsWith("Config_"))
                    {
                        keysToRemove.Add(entry.Key.ToString());
                    }
                }

                foreach (var key in keysToRemove)
                {
                    HttpContext.Current.Cache.Remove(key);
                }

                _lastModified.Clear();
            }
        }

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        /// <returns>缓存统计信息</returns>
        public static string GetCacheStats()
        {
            lock (_lockObject)
            {
                int configCacheCount = 0;
                foreach (System.Collections.DictionaryEntry entry in HttpContext.Current.Cache)
                {
                    if (entry.Key.ToString().StartsWith("Config_"))
                    {
                        configCacheCount++;
                    }
                }

                return $"配置缓存数量: {configCacheCount}, 文件监控数量: {_lastModified.Count}";
            }
        }
    }
}