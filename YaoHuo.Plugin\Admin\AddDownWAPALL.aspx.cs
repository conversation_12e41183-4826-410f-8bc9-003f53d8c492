﻿using System;
using KeLin.ClassManager;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.Admin
{
    public class AddDownWAPALL : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string string_11 = "";

        public string page = "";

        public string INFO = "";

        public string ERROR = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = base.Request.Form.Get("action");
            page = GetRequestValue("page");
            IsCheckUserManager(userid, userVo.managerlvl, "", "admin/BaseSiteModifyWML.aspx?siteid=" + siteid);
            if (action == "gomod")
            {
                try
                {
                    string requestValue = GetRequestValue("path");
                    requestValue = WapTool.URLtoWAP(requestValue).Replace("|", "｜");

                    // ✅ 使用DapperHelper进行安全的参数化更新操作
                    string connectionString = PubConstant.GetConnectionString(string_10);
                    string newVersion = WapTool.SetSiteDefault(siteVo.Version, requestValue, 11);
                    string updateSql = "UPDATE [user] SET version = @Version WHERE userid = @UserId";

                    DapperHelper.Execute(connectionString, updateSql, new {
                        Version = DapperHelper.LimitLength(newVersion, 8000), // version字段通常较长，限制长度防止溢出
                        UserId = DapperHelper.SafeParseLong(siteid, "站点ID")
                    });

                    INFO = "OK";
                }
                catch (Exception ex)
                {
                    ERROR = ex.ToString();
                }
            }
        }
    }
}