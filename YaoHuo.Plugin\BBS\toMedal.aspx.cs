﻿using System;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class ToMedal : MyPageWap
    {
        private readonly string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string INFO = "";

        public string ERROR = "";

        public string touserid = "";

        public string smedal = "";

        public string remark = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            IsCheckUserManager(userid, userVo.managerlvl, "", "admin/basesitemodifywml.aspx?siteid=" + siteid);
            action = GetRequestValue("action");
            if (action == "search")
            {
                touserid = GetRequestValue("touserid");
                if (!WapTool.IsNumeric(touserid))
                {
                    touserid = "0";
                }
                user_BLL user_BLL = new user_BLL(a);
                user_Model userInfo = user_BLL.getUserInfo(touserid, siteid);
                if (userInfo != null)
                {
                    smedal = userInfo.moneyname;
                }
            }
            if (!(action == "gomod"))
            {
                return;
            }
            smedal = GetRequestValue("smedal");
            touserid = GetRequestValue("touserid");
            remark = GetRequestValue("remark");
            try
            {
                if (WapTool.IsExistUser(siteid, touserid))
                {
                    // ✅ 使用Dapper修复SQL注入漏洞
                    UpdateUserMedalSafely(smedal, touserid);
                    INFO = "OK";
                    string text = "恭喜您，" + userVo.nickname + "奖励勋章给您！";
                    string text2 = "原因:" + remark;
                    // ✅ 使用Dapper修复SQL注入漏洞
                    InsertMedalMessageSafely(siteid, userid, userVo.nickname, text, text2, touserid);
                }
                else
                {
                    INFO = "NOTEXIST";
                }
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
        }

        /// <summary>
        /// 使用Dapper安全地更新用户勋章，避免SQL注入
        /// </summary>
        /// <param name="medal">勋章名称</param>
        /// <param name="userId">用户ID</param>
        private void UpdateUserMedalSafely(string medal, string userId)
        {
            string connectionString = PubConstant.GetConnectionString(a);
            string sql = "UPDATE [user] SET moneyname = @Medal WHERE userid = @UserId";

            // SafeParseLong内部已包含数字验证，无需重复调用ValidateNumeric
            DapperHelper.Execute(connectionString, sql, new {
                Medal = medal ?? "",
                UserId = DapperHelper.SafeParseLong(userId, "用户ID")
            });
        }

        /// <summary>
        /// 使用Dapper安全地插入勋章奖励消息，避免SQL注入
        /// </summary>
        /// <param name="siteId">站点ID</param>
        /// <param name="fromUserId">发送者用户ID</param>
        /// <param name="fromNickname">发送者昵称</param>
        /// <param name="title">消息标题</param>
        /// <param name="content">消息内容</param>
        /// <param name="toUserId">接收者用户ID</param>
        private void InsertMedalMessageSafely(string siteId, string fromUserId, string fromNickname, string title, string content, string toUserId)
        {
            string connectionString = PubConstant.GetConnectionString(a);
            string sql = "INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem) VALUES(@SiteId,@UserId,@Nickname,@Title,@Content,@ToUserId,1)";

            // SafeParseLong内部已包含数字验证，无需重复调用ValidateNumeric
            DapperHelper.Execute(connectionString, sql, new {
                SiteId = DapperHelper.SafeParseLong(siteId, "站点ID"),
                UserId = DapperHelper.SafeParseLong(fromUserId, "发送者用户ID"),
                Nickname = DapperHelper.LimitLength(fromNickname ?? "", 50),
                Title = DapperHelper.LimitLength(title ?? "", 255),
                Content = DapperHelper.LimitLength(content ?? "", 4000),
                ToUserId = DapperHelper.SafeParseLong(toUserId, "接收者用户ID")
            });
        }
    }
}