/* Google Font Import */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

/* Base Body Styles */
body {
    font-family: 'Noto Sans SC', sans-serif;
    background-color: #f8fafc; /* Equivalent to bg-gray-50 or bg-slate-50 */
}

/* Gradient <PERSON> */
.gradient-btn {
    background-image: linear-gradient(135deg, #38b2ac 0%, #2c7a7b 100%);
    transition: all 0.3s ease;
}

    .gradient-btn:hover {
        background-image: linear-gradient(135deg, #2c7a7b 0%, #285e61 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 6px rgba(44, 122, 123, 0.25);
    }

/* Coin Icon */
.coin-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #fbbf24 0%, #d97706 100%);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
}

/* Small Coin Icon (from top.html) */
.coin-icon-sm {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #fbbf24 0%, #d97706 100%); /* amber-400 to orange-600 approx */
    color: white;
    border-radius: 50%;
    width: 16px; /* Slightly smaller */
    height: 16px;
    font-size: 9px; /* Slightly smaller */
    vertical-align: middle;
}


/* Form Input Styles (from add.html, doit.html) */
.form-input {
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 0.75rem;
    width: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

    .form-input:focus {
        outline: none;
        border-color: #38b2ac;
        box-shadow: 0 0 0 3px rgba(56, 178, 172, 0.2);
    }

.input-label {
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
    /* display: block;  // 注释或删除这一行 */
}

.input-group {
    margin-bottom: 1.5rem;
}

/* Challenge Item Hover Effect (from index.html, challenge_list.html) */
.challenge-item {
    transition: all 0.2s ease;
}

    .challenge-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

/* Chat Bubble (from index.html) */
.chat-bubble {
    border-radius: 18px;
    max-width: 85%;
}

/* Tab Active State (from challenge_list.html, top.html) */
.tab-active {
    color: #0f766e; /* text-teal-700 */
    border-bottom: 3px solid #14b8a6; /* border-teal-500 */
    font-weight: 500; /* font-medium */
}

/* Challenge Options (from doit.html) */
.challenge-option {
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

    .challenge-option:hover {
        border-color: #38b2ac;
        transform: translateY(-2px);
    }

    .challenge-option.selected {
        border-color: #38b2ac;
        background-color: #e6fffa; /* bg-teal-50 approx */
    }

/* Detail Item Styles (from 吹牛详情 - 输.html) */
.detail-item {
    padding-bottom: 1rem;
    margin-bottom: 1rem;
    border-bottom: 1px solid #f3f4f6; /* border-gray-100 */
}

    .detail-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }

.detail-label {
    color: #6b7280; /* text-gray-500 */
    font-size: 0.875rem; /* text-sm */
    margin-bottom: 0.5rem; /* mb-2 */
    font-weight: 500; /* font-medium */
}

.detail-value {
    color: #1f2937; /* text-gray-800 approx */
}

.avatar-container { /* Although only used once, kept for completeness */
    background-image: linear-gradient(135deg, rgba(56, 178, 172, 0.1) 0%, rgba(49, 151, 149, 0.1) 100%);
    border: 1px solid rgba(56, 178, 172, 0.2);
}

/* 优化index挑战项标题和金额显示 */
.challenge-item .truncate {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.challenge-item p {
    line-height: 1.4;
}

/* 确保金额部分始终显示 */
.challenge-item .flex-shrink-0 {
    flex-shrink: 0;
}


/* 隐藏原始分页控件 */
.original-pager {
    display: none;
}

/* 让 animate-spin 的动画时长为2秒（用于吹牛详情页的沙漏icon） */
.slow-spin {
    animation-duration: 2s !important;
}

/* Rank/Book_List.aspx页面tab内容切换 */
#tab-times,
#tab-money {
    display: none;
}

    #tab-times.active,
    #tab-money.active {
        display: block;
    }

/* 消息提示样式 */
.newMessage {
    background: linear-gradient(135deg, rgba(56, 178, 172, 0.1) 0%, rgba(44, 122, 123, 0.2) 100%);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

    .newMessage:hover {
        background: linear-gradient(135deg, rgba(56, 178, 172, 0.15) 0%, rgba(44, 122, 123, 0.25) 100%);
        transform: translateY(-1px);
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
    }

    .newMessage a {
        display: flex;
        align-items: center;
        color: #0f766e;
        font-size: 0.9rem;
        font-weight: 500;
        text-decoration: none;
    }

        .newMessage a img {
            margin-right: 0.5rem;
            vertical-align: middle;
        }

        .newMessage a:last-of-type {
            margin-left: auto;
            padding: 0.3rem;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

            .newMessage a:last-of-type:hover {
                background-color: rgba(56, 178, 172, 0.1);
            }

            .newMessage a:last-of-type img {
                width: 18px;
                height: 18px;
                margin: 0;
            }

/* 聊天输入框相关样式 */
.chat-form {
    width: 100%;
}

.chat-input-container {
    display: flex;
    width: 100%;
}

.chat-input {
    flex: 1;
    min-width: 0; /* 关键属性，防止在Safari中溢出 */
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    border: 1px solid #e2e8f0;
    border-right: none;
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
    outline: none;
}

    .chat-input:focus {
        border-color: #38b2ac;
        box-shadow: 0 0 0 1px rgba(56, 178, 172, 0.2);
    }

.chat-send-btn {
    padding: 0.75rem 1rem;
    background-image: linear-gradient(135deg, #38b2ac 0%, #2c7a7b 100%);
    color: white;
    border: none;
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
    transition: all 0.3s ease;
    flex-shrink: 0;
    width: 48px; /* 固定宽度，防止按钮尺寸不确定导致的布局问题 */
    display: flex;
    align-items: center;
    justify-content: center;
}

    .chat-send-btn:hover {
        background-image: linear-gradient(135deg, #2c7a7b 0%, #285e61 100%);
    }

/* 响应式设计 - 在小屏幕或放大时隐藏用户头像 */
@media (max-width: 330px), (min-width: 331px) and (max-zoom: 125%) {
    .bg-gradient-to-r.from-blue-400.to-blue-600.rounded-full.w-12.h-12 {
        display: none;
    }
}
/* 优化今日流水和今日已吹的布局 */
.grid.grid-cols-2.gap-4.text-sm.bg-gray-50 {
    display: grid;
    grid-template-columns: minmax(80px, 0.7fr) minmax(auto, 1.3fr);
    padding: 0.875rem 1rem;
}

    /* 让今日已吹区域在空间不足时可以被压缩 */
    .grid.grid-cols-2.gap-4.text-sm.bg-gray-50 > div:first-child {
        min-width: 80px;
        overflow: hidden;
        text-align: center;
        padding-right: 0.5rem;
    }

    /* 让今日流水区域可以根据内容自动拓展 */
    .grid.grid-cols-2.gap-4.text-sm.bg-gray-50 > div:last-child {
        flex-grow: 1;
        overflow: hidden;
        text-align: center;
        padding-left: 0.5rem;
    }

/* 确保数字不会被截断 */
.font-bold.text-lg.text-amber-600.flex {
    white-space: nowrap;
    overflow: visible;
    justify-content: center;
    font-size: 1.2rem;
}

    /* 让金币图标保持在数字旁边 */
    .font-bold.text-lg.text-amber-600.flex .coin-icon {
        margin-left: 5px;
        flex-shrink: 0;
        align-self: center;
    }

/* 调整分隔线位置 */
.border-l.border-gray-200 {
    position: relative;
}

    .border-l.border-gray-200::before {
        content: "";
        position: absolute;
        left: -0.5rem;
        top: 10%;
        height: 80%;
        width: 1px;
        background-color: #e5e7eb;
    }
/* 优化今日流水和今日已吹的布局 */
.grid.grid-cols-2.gap-4.text-sm.bg-gray-50 {
    display: grid;
    grid-template-columns: minmax(80px, 0.8fr) minmax(auto, 1.2fr);
}

    /* 让今日已吹区域在空间不足时可以被压缩 */
    .grid.grid-cols-2.gap-4.text-sm.bg-gray-50 > div:first-child {
        min-width: 80px;
        overflow: hidden;
        text-align: center;
    }

    /* 让今日流水区域可以根据内容自动拓展 */
    .grid.grid-cols-2.gap-4.text-sm.bg-gray-50 > div:last-child {
        flex-grow: 1;
        overflow: hidden;
        text-align: center;
    }

/* 确保数字不会被截断 */
.font-bold.text-lg.text-amber-600.flex {
    white-space: nowrap;
    overflow: visible;
    justify-content: center;
}

    /* 让金币图标保持在数字旁边 */
    .font-bold.text-lg.text-amber-600.flex .coin-icon {
        margin-left: 4px;
        flex-shrink: 0;
    }

/* 顶部header通用样式 */
header.bg-gradient-to-r {
    padding: 0 !important;
    height: 48px !important;
    display: flex !important;
    align-items: center !important;
}

    /* Index页面标题居中样式 */
    header.bg-gradient-to-r.text-center {
        justify-content: center !important;
    }

    /* 其他页面带返回箭头的样式 */
    header.bg-gradient-to-r.flex.items-center {
        padding-left: 16px !important; /* 增加左侧间距 */
        padding-right: 16px !important;
        justify-content: center !important; /* 让内容居中 */
    }

/* 返回箭头 */
header a i.fas.fa-arrow-left {
    font-size: 1rem !important;
    margin-right: 8px !important; /* 减少与标题的间距 */
}

/* 返回按钮 */
header a.text-white.mr-4 {
    margin-right: 8px !important; /* 减少返回按钮的右边距 */
    position: absolute !important; /* 绝对定位 */
    left: 16px !important; /* 固定在左侧 */
}

/* 所有标题文字样式统一 */
header h1 {
    font-size: 1.1rem !important;
    line-height: 1.2 !important;
    flex-grow: 0 !important; /* 防止标题拉伸 */
}

/* 底部导航栏样式 */
footer.fixed.bottom-0 {
    padding: 0.15rem 0 !important;
}

footer .flex.justify-around {
    height: 48px !important;
}

footer a {
    padding: 4px 0 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
}

footer i.block {
    margin-bottom: 2px !important;
    font-size: 1rem !important;
    height: 18px !important;
    line-height: 1.2 !important;
}

footer .text-xs {
    font-size: 0.7rem !important;
    line-height: 1.1 !important;
    margin-top: 2px !important;
}

/* 添加全屏按钮样式 */
#chat-fullscreen-btn {
    background: transparent;
    color: #6b7280;
    border: none;
    cursor: pointer;
    transition: color 0.2s ease;
    padding: 4px;
    border-radius: 4px;
}

    #chat-fullscreen-btn:hover {
        color: #38b2ac;
        background-color: rgba(56, 178, 172, 0.1);
    }
