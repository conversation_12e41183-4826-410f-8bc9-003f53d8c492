using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
public class MagickCompressor
{
    private const int MAX_SIZE = 1048576;
    private const int WIDTH_LARGE = 1080;
    private const int INITIAL_JPEG_QUALITY = 100;
    private const int MIN_JPEG_QUALITY = 60;
    private const int QUALITY_STEP = 2;

    public static byte[] CompressImage(byte[] imageData)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"开始处理图片，原始大小：{imageData.Length / 1024}KB");

            using (var ms = new MemoryStream(imageData))
            using (var originalImage = Image.FromStream(ms))
            {
                // 获取图片格式
                bool isPng = originalImage.RawFormat.Equals(ImageFormat.Png);
                int originalWidth = originalImage.Width;
                System.Diagnostics.Debug.WriteLine($"原始图片宽度：{originalWidth}px，格式：{(isPng ? "PNG" : "JPEG")}");

                byte[] scaledData = null;

                // 只在宽度大于1080px时进行缩放
                if (originalWidth > WIDTH_LARGE)
                {
                    System.Diagnostics.Debug.WriteLine($"图片宽度大于{WIDTH_LARGE}px，开始缩放");

                    try
                    {
                        int newHeight = (int)((double)originalImage.Height * WIDTH_LARGE / originalWidth);
                        using (var scaledImage = new Bitmap(WIDTH_LARGE, newHeight))
                        {
                            using (var g = Graphics.FromImage(scaledImage))
                            {
                                g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                                g.DrawImage(originalImage, 0, 0, WIDTH_LARGE, newHeight);
                            }

                            using (var ms2 = new MemoryStream())
                            {
                                if (isPng)
                                    scaledImage.Save(ms2, ImageFormat.Png);
                                else
                                    SaveJpeg(ms2, scaledImage, 100);

                                scaledData = ms2.ToArray();

                                // 验证缩放后的图片是否有效
                                using (Image.FromStream(new MemoryStream(scaledData))) { }

                                System.Diagnostics.Debug.WriteLine($"缩放完成，新尺寸：{scaledImage.Width}x{scaledImage.Height}px，缩放后大小：{scaledData.Length / 1024}KB");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"图片缩放失败: {ex.Message}");
                        return null; // 缩放失败返回null
                    }
                }

                // 如果文件大于1MB，尝试压缩
                if ((scaledData == null && imageData.Length > MAX_SIZE) ||
                    (scaledData != null && scaledData.Length > MAX_SIZE))
                {
                    System.Diagnostics.Debug.WriteLine($"尝试压缩：文件大小{(imageData.Length > MAX_SIZE ? ">" : "<=")}1MB");

                    try
                    {
                        byte[] bestResult = null;
                        int currentQuality = INITIAL_JPEG_QUALITY;

                        // 使用要压缩的源图片
                        using (var imageToCompress = scaledData != null ?
                            Image.FromStream(new MemoryStream(scaledData)) : originalImage)
                        {
                            while (currentQuality >= MIN_JPEG_QUALITY)
                            {
                                using (var ms3 = new MemoryStream())
                                {
                                    SaveJpeg(ms3, imageToCompress, currentQuality);
                                    byte[] compressedData = ms3.ToArray();

                                    // 验证压缩后的图片是否有效
                                    using (Image.FromStream(new MemoryStream(compressedData))) { }

                                    System.Diagnostics.Debug.WriteLine($"压缩完成，质量：{currentQuality}，处理后大小：{compressedData.Length / 1024}KB");
                                    System.Diagnostics.Debug.WriteLine($"总压缩比：{(double)compressedData.Length / imageData.Length:P2}");

                                    if (compressedData.Length <= MAX_SIZE || currentQuality == MIN_JPEG_QUALITY)
                                    {
                                        bestResult = compressedData;
                                        break;
                                    }
                                    currentQuality -= QUALITY_STEP;
                                }
                            }
                            return bestResult; // 可能为null,表示压缩失败
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"图片压缩失败: {ex.Message}");
                        return null; // 压缩失败返回null
                    }
                }

                // 如果不需要处理，返回原图或缩放后的图
                System.Diagnostics.Debug.WriteLine("返回" + (scaledData != null ? "缩放后的图片" : "原图"));
                return scaledData ?? imageData;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"图片处理失败: {ex.Message}\n{ex.StackTrace}");
            return null;
        }
    }

    private static void SaveJpeg(Stream stream, Image image, int quality)
    {
        var encoder = ImageCodecInfo.GetImageEncoders()
            .First(c => c.FormatID == ImageFormat.Jpeg.Guid);
        var encoderParameters = new EncoderParameters(1);
        encoderParameters.Param[0] = new EncoderParameter(Encoder.Quality, quality);
        image.Save(stream, encoder, encoderParameters);
    }
}