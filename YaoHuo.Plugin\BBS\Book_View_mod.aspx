﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_View_mod.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Book_View_mod" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    if (this.INFO == "OK")
    {
        // 修改：根据是否有追加悬赏提示信息来设置跳转时间
        wmlVo.timer = string.IsNullOrEmpty(this.additionalRewardMessage) ? "0.5" : "3";
        wmlVo.strUrl = "bbs-" + id + ".html";
    }
    wmlVo.mycss += "\r\n<link href=\"/netcss/css/upload-resource.css?X9\" rel=\"stylesheet\" type=\"text/css\"/>";
    StringBuilder strhtml = new StringBuilder();
    Response.Write(WapTool.showTop(this.GetLang("修改帖子内容|修改帖子內容|content modification"), wmlVo));
    strhtml.Append("<div class=\"upload-container\">");
    
    // 添加面包屑导航
    strhtml.Append("<div class=\"breadcrumb\">");
    strhtml.Append("<a href=\"/\" class=\"breadcrumb-item\">");
    strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"></path><polyline points=\"9 22 9 12 15 12 15 22\"></polyline></svg>");
    strhtml.Append("<span>首页</span>");
    strhtml.Append("</a>");
    strhtml.Append("<span class=\"breadcrumb-separator\"></span>");
    strhtml.Append("<a href=\"/bbslist-" + this.classid + ".html\" class=\"breadcrumb-item\">" + classVo.classname + "</a>");
    strhtml.Append("<span class=\"breadcrumb-separator\"></span>");
    strhtml.Append("<span class=\"breadcrumb-item active\">修改帖子</span>");
    strhtml.Append("</div>");

    // 错误提示
    if (!string.IsNullOrEmpty(this.ERROR))
    {
        strhtml.Append("<div class=\"tip\">" + this.ERROR + "</div>");
    }

    // 新增：对标题超长和内容过短的提示
    if (this.INFO == "TITLEMAX")
    {
        strhtml.Append("<div class=\"tip\">标题最多25字！</div>");
    }
    else if (this.INFO == "NULL")
    {
        strhtml.Append("<div class=\"tip\">标题最少" + this.titlemax + "字，内容最少" + this.contentmax + "字！</div>");
    }

    // 成功提示
    if (this.INFO == "OK")
    {

        // 追加悬赏提示
        if (!string.IsNullOrEmpty(this.additionalRewardMessage))
        {
            strhtml.Append("<div class=\"tip\" id=\"additionalRewardMessage\"></div>");
            strhtml.Append("<script>");
            strhtml.Append("var message = '" + this.additionalRewardMessage + "';");
            strhtml.Append("var messageElement = document.getElementById('additionalRewardMessage');");
            strhtml.Append("if (message.startsWith('success')) {");
            strhtml.Append("    var amount = message.split(',')[1];");
            strhtml.Append("    messageElement.innerHTML = '追加悬赏成功，悬赏已增加:' + amount;");
            strhtml.Append("} else if (message === 'insufficient_balance') {");
            strhtml.Append("    messageElement.innerHTML = '追加悬赏失败，余额不足！';");
            strhtml.Append("} else if (message === 'not_author') {");
            strhtml.Append("    messageElement.innerHTML = '只有帖子作者可以追加悬赏！';");
            strhtml.Append("} else if (message === 'min_amount') {");
            strhtml.Append("    messageElement.innerHTML = '追加悬赏失败，最低金额为:1000';");
            strhtml.Append("}");
            strhtml.Append("</script>");
        }

        strhtml.Append("<div class=\"upload-success\">");
        strhtml.Append("<div class=\"upload-success-header\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-square-pen\"><path d=\"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"></path><path d=\"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z\"></path></svg>");
        strhtml.Append("<div class=\"upload-success-title\">修改帖子成功！</div>");
        strhtml.Append("</div>");
        strhtml.Append("<div class=\"upload-success-subtitle\">正在返回帖子...</div>");
        strhtml.Append("</div>");
    }
    // 只有在没有错误且有权限的情况下才显示表单
    else if (string.IsNullOrEmpty(this.ERROR) && hasPermission)
    {
        // 生成token
        string tokenKey = "formTokenList_mod_" + id;
        if (string.IsNullOrEmpty(formToken))
        {
            var tokenList = Session[tokenKey] as System.Collections.Generic.List<string>;
            if (tokenList != null && tokenList.Count > 0)
                formToken = tokenList[tokenList.Count - 1]; // 取最新Token
            else
            {
                formToken = GenerateFormToken(tokenKey);
                Session["formTokenExpire"] = DateTime.Now.AddMinutes(TOKEN_EXPIRE_MINUTES);
            }
        }

        strhtml.Append("<form name=\"go\" action=\"" + this.http_start + "bbs/book_view_mod.aspx\" method=\"post\">");
        
        // 表单内容
        strhtml.Append("<div class=\"form-group\">");
        strhtml.Append("<label>" + this.GetLang("标题|標題|Title") + "</label>");
        strhtml.Append("<input type=\"text\" name=\"book_title\" minlength=\"5\" maxlength=\"25\" required=\"required\" class=\"form-control\" value=\"" + bbsVo.book_title + "\"/>");
        strhtml.Append("</div>");

        strhtml.Append("<div class=\"form-group\">");
        strhtml.Append("<label>" + this.GetLang("内容|內容|Content") + "</label>");
        strhtml.Append("<textarea name=\"book_content\" oninput=\"adjustTextareaHeight(this)\" minlength=\"15\" required=\"required\" class=\"form-control\" style=\"min-height:200px;\">" + bbsVo.book_content.Replace("[br]", "\n") + "</textarea>");
        strhtml.Append("</div>");

        // 追加悬赏部分
        if (isAuthor && !isFreeMoney)
        {
            strhtml.Append("<div class=\"reward-section\">");
            strhtml.Append("<button type=\"button\" class=\"collapse-trigger\" onclick=\"toggleReward()\">");
            strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-gift\"><rect x=\"3\" y=\"8\" width=\"18\" height=\"4\" rx=\"1\"></rect><path d=\"M12 8v13\"></path><path d=\"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7\"></path><path d=\"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5\"></path></svg>");
            strhtml.Append("<span>追加悬赏</span>");
            strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"arrow-icon\"><polyline points=\"6 9 12 15 18 9\"></polyline></svg>");
            strhtml.Append("</button>");
            strhtml.Append("<div class=\"reward-content\" style=\"display:none\">");
            strhtml.Append("<div class=\"form-group half\" style=\"display:flex; align-items:center;gap:5px;flex-wrap:wrap;max-width:100%;background: white;\">");
            strhtml.Append("<input type=\"number\" name=\"additionalReward\" class=\"form-control\" min=\"1000\" max=\"10000000\" placeholder=\"选填，最少1000\" style=\"padding: 0.5rem; height: 32px;width: 240px;max-width: 45%;\"/>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
        }

        // 隐藏字段
        strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"id\" value=\"" + id + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"lpage\" value=\"" + lpage + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"token\" value=\"" + formToken + "\"/>");

        // 提交按钮
        strhtml.Append("<button type=\"submit\" id=\"submitBtn\" style=\"margin-right:auto; margin-top: 0; margin-left: 0;\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-square-pen\"><path d=\"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"/><path d=\"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z\"/></svg>");
        strhtml.Append("<span>确认修改</span>");
        strhtml.Append("</button>");
        strhtml.Append("</form>");
    }

    // 底部导航按钮 (始终显示)
    strhtml.Append("<div class=\"nav-buttons\">");
    strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs-" + id + ".html\">");
    strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-arrow-left\"><path d=\"m12 19-7-7 7-7\"></path><path d=\"M19 12H5\"></path></svg>");
    strhtml.Append("返回主题</a>");
    strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/book_view_admin.aspx?siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;id=" + this.id + "\">");
    strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-settings\"><path d=\"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z\"/><circle cx=\"12\" cy=\"12\" r=\"3\"/></svg>");
    strhtml.Append("返回管理</a>");
    strhtml.Append("</div>");

    strhtml.Append("</div>");
    
    // 修改后的JavaScript代码
    strhtml.Append(@"<script type=""text/javascript"">
        function adjustTextareaHeight(t) {
            // 保存当前滚动位置
            var st = window.pageYOffset || document.documentElement.scrollTop;
            
            // 重置高度以获取实际内容高度
            t.style.height = 'auto';
            
            // 设置新高度，额外加2px以避免出现滚动条
            t.style.height = (t.scrollHeight + 2) + 'px';
            
            // 恢复滚动位置
            window.scrollTo(0, st);
        }

        function initTextareas() {
            document.querySelectorAll('textarea[name=""book_content""]').forEach(function(t) {
                // 初始化时设置样式，确保不显示滚动条
                t.style.overflowY = 'hidden';
                
                // 初始化高度
                adjustTextareaHeight(t);
                
                // 监听输入事件
                t.addEventListener('input', function() {
                    adjustTextareaHeight(this);
                });
            });
        }

        // 页面加载完成后初始化
        document.readyState === 'loading' ? 
            document.addEventListener('DOMContentLoaded', initTextareas) : 
            initTextareas();
            
        function toggleReward() {
            const trigger = document.querySelector('.collapse-trigger');
            const content = document.querySelector('.reward-content');
            trigger.classList.toggle('active');
            content.style.display = content.style.display === 'none' ? 'block' : 'none';
        }
    </script>");

    Response.Write(strhtml);
    Response.Write(WapTool.showDown(wmlVo));
%>