using System;

namespace YaoHuo.Plugin.OAuth
{
    /// <summary>
    /// OAuth 访问令牌实体（对应 oauth_access_tokens 表）
    /// </summary>
    public class OAuthAccessToken
    {
        /// <summary>
        /// 令牌ID（主键）
        /// </summary>
        public string TokenId { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        /// 权限范围
        /// </summary>
        public string Scope { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 最后使用时间
        /// </summary>
        public DateTime? LastUsedAt { get; set; }

        /// <summary>
        /// 撤销时间
        /// </summary>
        public DateTime? RevokedAt { get; set; }

        /// <summary>
        /// 刷新令牌（预留字段）
        /// </summary>
        public string RefreshToken { get; set; }

        /// <summary>
        /// 刷新令牌过期时间（预留字段）
        /// </summary>
        public DateTime? RefreshExpiresAt { get; set; }

        #region 轻量业务方法

        /// <summary>
        /// 检查访问令牌是否已过期
        /// </summary>
        /// <returns>是否过期</returns>
        public bool IsExpired()
        {
            return DateTime.UtcNow > ExpiresAt;
        }

        /// <summary>
        /// 检查访问令牌是否已撤销
        /// </summary>
        /// <returns>是否已撤销</returns>
        public bool IsRevoked()
        {
            return RevokedAt.HasValue;
        }

        /// <summary>
        /// 检查访问令牌是否有效（未过期且未撤销）
        /// </summary>
        /// <returns>是否有效</returns>
        public bool IsValid()
        {
            return !IsExpired() && !IsRevoked();
        }

        /// <summary>
        /// 检查刷新令牌是否有效（预留功能）
        /// </summary>
        /// <returns>是否有效</returns>
        public bool IsRefreshTokenValid()
        {
            if (string.IsNullOrEmpty(RefreshToken) || !RefreshExpiresAt.HasValue)
                return false;

            return DateTime.UtcNow <= RefreshExpiresAt.Value && !IsRevoked();
        }

        /// <summary>
        /// 撤销令牌（轻量业务方法）
        /// </summary>
        public void Revoke()
        {
            RevokedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// 更新最后使用时间（轻量业务方法）
        /// </summary>
        public void UpdateLastUsed()
        {
            LastUsedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// 检查权限范围是否包含指定权限（轻量业务方法）
        /// </summary>
        /// <param name="requiredScope">需要的权限</param>
        /// <returns>是否包含</returns>
        public bool HasScope(string requiredScope)
        {
            if (string.IsNullOrEmpty(Scope) || string.IsNullOrEmpty(requiredScope))
                return false;

            var scopes = Scope.Split(new[] { OAuthConstants.SEPARATOR_SCOPE_PRIMARY, OAuthConstants.SEPARATOR_SCOPE_ALTERNATIVE }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var scope in scopes)
            {
                if (string.Equals(scope.Trim(), requiredScope.Trim(), StringComparison.OrdinalIgnoreCase))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// 获取令牌响应对象（用于API返回）
        /// </summary>
        /// <returns>令牌响应</returns>
        public object ToTokenResponse()
        {
            var response = new
            {
                access_token = TokenId,
                token_type = OAuthConstants.TOKEN_TYPE_BEARER,
                expires_in = Math.Max(0, (int)(ExpiresAt - DateTime.UtcNow).TotalSeconds),
                scope = Scope ?? OAuthConstants.SCOPE_PROFILE
            };

            return response;
        }

        /// <summary>
        /// 获取包含用户信息的令牌响应（扩展版本）
        /// </summary>
        /// <param name="userInfo">用户信息</param>
        /// <returns>扩展令牌响应</returns>
        public object ToTokenResponseWithUserInfo(object userInfo)
        {
            var response = new
            {
                access_token = TokenId,
                token_type = OAuthConstants.TOKEN_TYPE_BEARER,
                expires_in = Math.Max(0, (int)(ExpiresAt - DateTime.UtcNow).TotalSeconds),
                scope = Scope ?? OAuthConstants.SCOPE_PROFILE,
                user_info = userInfo
            };

            return response;
        }

        #endregion
    }
}