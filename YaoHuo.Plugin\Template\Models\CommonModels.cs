namespace YaoHuo.Plugin.Template.Models
{
    /// <summary>
    /// 基础页面模型 - 包含所有页面的公共属性
    /// </summary>
    public abstract class BasePageModel
    {
        /// <summary>
        /// 页面标题
        /// </summary>
        public string PageTitle { get; set; }

        /// <summary>
        /// 消息提示
        /// </summary>
        public MessageModel Message { get; set; } = new MessageModel();

        /// <summary>
        /// 站点信息
        /// </summary>
        public SiteInfoModel SiteInfo { get; set; } = new SiteInfoModel();

        /// <summary>
        /// 隐藏字段
        /// </summary>
        public HiddenFieldsModel HiddenFields { get; set; } = new HiddenFieldsModel();
    }

    /// <summary>
    /// 带分页的基础页面模型
    /// </summary>
    public abstract class BasePageModelWithPagination : BasePageModel
    {
        /// <summary>
        /// 分页信息
        /// </summary>
        public PaginationModel Pagination { get; set; } = new PaginationModel();
    }

    /// <summary>
    /// 消息提示模型
    /// </summary>
    public class MessageModel
    {
        /// <summary>
        /// 是否有消息
        /// </summary>
        public bool HasMessage { get; set; }

        /// <summary>
        /// 消息类型：success, error, warning, info
        /// </summary>
        public string Type { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 是否为成功状态
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 是否为错误状态
        /// </summary>
        public bool IsError => Type == "error";

        /// <summary>
        /// 设置成功消息
        /// </summary>
        public void SetSuccess(string message)
        {
            HasMessage = true;
            IsSuccess = true;
            Type = "success";
            Content = message;
        }

        /// <summary>
        /// 设置错误消息
        /// </summary>
        public void SetError(string message)
        {
            HasMessage = true;
            IsSuccess = false;
            Type = "error";
            Content = message;
        }

        /// <summary>
        /// 设置警告消息
        /// </summary>
        public void SetWarning(string message)
        {
            HasMessage = true;
            IsSuccess = false;
            Type = "warning";
            Content = message;
        }
    }

    /// <summary>
    /// 站点信息模型
    /// </summary>
    public class SiteInfoModel
    {
        /// <summary>
        /// 站点ID
        /// </summary>
        public string SiteId { get; set; }

        /// <summary>
        /// 站点名称
        /// </summary>
        public string SiteName { get; set; }

        /// <summary>
        /// 站点URL
        /// </summary>
        public string SiteUrl { get; set; }

        /// <summary>
        /// 栏目ID
        /// </summary>
        public string ClassId { get; set; }

        /// <summary>
        /// HTTP起始路径
        /// </summary>
        public string HttpStart { get; set; }

        /// <summary>
        /// 返回URL
        /// </summary>
        public string BackUrl { get; set; }

        /// <summary>
        /// 用户空间URL
        /// </summary>
        public string UserSpaceUrl { get; set; }

        /// <summary>
        /// 首页URL
        /// </summary>
        public string HomeUrl { get; set; }

        /// <summary>
        /// 是否需要审核
        /// </summary>
        public bool IsCheck { get; set; }
    }

    /// <summary>
    /// 分页模型
    /// </summary>
    public class PaginationModel
    {
        /// <summary>
        /// 是否有分页
        /// </summary>
        public bool HasPages { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage { get; set; } = 1;

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalItems { get; set; }

        /// <summary>
        /// 每页显示数量
        /// </summary>
        public int PageSize { get; set; } = 10;

        /// <summary>
        /// 是否是第一页
        /// </summary>
        public bool IsFirstPage { get; set; } = true;

        /// <summary>
        /// 是否是最后一页
        /// </summary>
        public bool IsLastPage { get; set; } = true;

        /// <summary>
        /// 总条数
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 是否显示分页
        /// </summary>
        public bool ShowPagination { get; set; }

        /// <summary>
        /// 当前页开始项目序号
        /// </summary>
        public int StartItem { get; set; }

        /// <summary>
        /// 当前页结束项目序号
        /// </summary>
        public int EndItem { get; set; }

        /// <summary>
        /// 上一页页码
        /// </summary>
        public int PrevPage { get; set; }

        /// <summary>
        /// 下一页页码
        /// </summary>
        public int NextPage { get; set; }
    }

    /// <summary>
    /// 选项项模型
    /// </summary>
    public class OptionItem
    {
        /// <summary>
        /// 选项值
        /// </summary>
        public string Value { get; set; }

        /// <summary>
        /// 显示文本
        /// </summary>
        public string Text { get; set; }

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool Selected { get; set; }
    }

    /// <summary>
    /// 隐藏字段模型
    /// </summary>
    public class HiddenFieldsModel
    {
        /// <summary>
        /// 操作类型
        /// </summary>
        public string Action { get; set; } = "gomod";

        /// <summary>
        /// 站点ID
        /// </summary>
        public string SiteId { get; set; }

        /// <summary>
        /// 栏目ID
        /// </summary>
        public string ClassId { get; set; }

        /// <summary>
        /// 返回URL
        /// </summary>
        public string BackUrl { get; set; }

        /// <summary>
        /// Session ID
        /// </summary>
        public string Sid { get; set; }
    }
}