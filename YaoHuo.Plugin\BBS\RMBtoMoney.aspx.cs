﻿using System;
using System.Data;
using System.Web;
using KeLin.ClassManager;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.BBS.Models;
using YaoHuo.Plugin.WebSite.Tool;
using Dapper;

namespace YaoHuo.Plugin.chinabank_WAP
{
    public class RMBtoMoney : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string KL_CheckIPTime = PubConstant.GetAppString("KL_CheckIPTime");

        public string action = "";

        public string tomoney = "";

        public string backurl = "";

        public string INFO = "";

        public string ERROR = "";

        public long STATE = 0L;

        public long changeMoney = 0L;

        public string changePW = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            // 基础初始化
            InitializePage();

            try
            {
                // 检查用户UI偏好并处理版本切换
                bool newVersionRendered = CheckAndHandleUIPreference();
                if (newVersionRendered)
                {
                    // 新版渲染成功，直接返回，不再执行后续的旧版代码
                    return;
                }
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }

            // 继续执行旧版逻辑...
            action = GetRequestValue("action");
            tomoney = GetRequestValue("tomoney");
            changePW = GetRequestValue("changePW");
            string siteDefault = WapTool.GetSiteDefault(siteVo.Version, 5);
            if (WapTool.IsNumeric(siteDefault))
            {
                STATE = long.Parse(siteDefault);
            }
            if (STATE < 1L)
            {
                INFO = "CLOSE";
            }
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "/myfile.aspx";
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            IsLogin(userid, backurl);
            string text = action;
            if (text != null && text == "add")
            {
                addMoney();
            }
        }

        public void addMoney()
        {
            if (STATE < 1L)
            {
                INFO = "CLOSE";
            }
            else if (PubConstant.md5(changePW).ToLower() != userVo.password.ToLower())
            {
                INFO = "PWERR";
            }
            else if (!WapTool.IsNumeric(tomoney) || tomoney.IndexOf('-') >= 0)
            {
                INFO = "NUM";
            }
            else if (isCheckIPTime(long.Parse(KL_CheckIPTime)))
            {
                INFO = "WAITING";
            }
            else if (userVo.RMB < 1m || userVo.RMB < (decimal)long.Parse(tomoney))
            {
                INFO = "NOTMONEY";
            }
            else if (long.Parse(tomoney) > 1000000L)
            {
                INFO = "MAXMONEY";
            }
            else
            {
                // ✅ 使用事务确保RMB兑换操作的原子性
                try
                {
                    ExecuteRMBExchangeTransaction();
                    INFO = "OK";
                }
                catch (Exception ex)
                {
                    ERROR = "兑换失败: " + ex.Message;
                }
            }
            VisiteCount("购买了" + siteVo.sitemoneyname);
        }

        /// <summary>
        /// 执行RMB兑换的事务操作（修复版 - 避免死锁并优化性能）
        /// </summary>
        private void ExecuteRMBExchangeTransaction()
        {
            string connectionString = PubConstant.GetConnectionString(string_10);
            long exchangeAmount = long.Parse(tomoney);
            long moneyToAdd = exchangeAmount * STATE;
            decimal rmbToDeduct = decimal.Parse(tomoney);
            long userIdLong = long.Parse(userid);
            long siteIdLong = long.Parse(siteid);
            string orderID = DateTime.Now.ToString("yyyyMMddhhmmss") + "-" + userid;

            TransactionHelper.ExecuteMoneyTransaction(connectionString, (connection, transaction) =>
            {
                // 1. 锁定用户账户并获取当前余额（一次性获取所有需要的数据，避免重复查询）
                string getUserDataSql = @"SELECT RMB, money FROM [user] WITH (UPDLOCK, ROWLOCK)
                                         WHERE userid = @UserId AND siteid = @SiteId";
                var userData = connection.QuerySingleOrDefault(getUserDataSql,
                    new { UserId = userIdLong, SiteId = siteIdLong }, transaction);

                if (userData == null)
                {
                    throw new InvalidOperationException("用户不存在");
                }

                decimal currentRMB = userData.RMB;
                long currentMoney = userData.money;

                if (currentRMB < rmbToDeduct)
                {
                    throw new InvalidOperationException($"RMB余额不足，当前余额：{currentRMB:F2}，需要：{rmbToDeduct:F2}");
                }

                // 2. 执行RMB兑换操作（扣除RMB，增加妖晶）
                string exchangeSql = "UPDATE [user] SET money = money + @AddMoney, RMB = RMB - @DeductRMB WHERE siteid = @SiteId AND userid = @UserId";
                connection.Execute(exchangeSql, new {
                    AddMoney = moneyToAdd,
                    DeductRMB = rmbToDeduct,
                    SiteId = siteIdLong,
                    UserId = userIdLong
                }, transaction);

                // 3. 计算操作后的余额（避免重复查询数据库）
                decimal newRMBBalance = currentRMB - rmbToDeduct;
                long newMoneyBalance = currentMoney + moneyToAdd;

                // 4. 记录RMB日志（使用安全方法，传入计算后的RMB余额）
                SaveRMBLogInTransactionSafe(connection, transaction, userid, "-1", "-" + exchangeAmount.ToString(),
                    userid, nickname, "购买" + siteVo.sitemoneyname, orderID, newRMBBalance);

                // 5. 记录银行日志（使用安全方法，传入计算后的金币余额）
                TransactionHelper.SaveBankLogWithBalance(connection, transaction, siteIdLong, userIdLong, "RMB转币",
                    moneyToAdd, userIdLong, nickname, "花了￥" + exchangeAmount + "购买", IP, newMoneyBalance);
            });
        }

        /// <summary>
        /// 在事务中安全保存RMB日志（传入余额，避免SELECT user表，消除死锁风险）
        /// </summary>
        private void SaveRMBLogInTransactionSafe(IDbConnection connection, IDbTransaction transaction,
            string localUserId, string type, string money, string operaUserId, string operaNickname,
            string remark, string orderID, decimal currentRMBBalance)
        {
            // 将orderID信息合并到remark中
            string fullRemark = remark;
            if (!string.IsNullOrEmpty(orderID))
            {
                fullRemark += $" [订单号:{orderID}]";
            }
            fullRemark = WapTool.Left(fullRemark, 200);

            // ✅ 直接INSERT，不再SELECT user表，避免死锁
            string sql = @"INSERT INTO wap_bankLog (siteid, userid, actionName, money, leftMoney, opera_userid, opera_nickname, remark, ip, addtime)
                          VALUES (@SiteId, @LocalUserId, @ActionName, @Money, @LeftMoney, @OperaUserId, @OperaNickname, @Remark, @IP, GETDATE())";

            connection.Execute(sql, new {
                SiteId = long.Parse(siteid),
                LocalUserId = long.Parse(localUserId),
                ActionName = WapTool.Left(type, 10), // actionName字段限制10个字符
                Money = money, // 保持字符串格式，因为wap_bankLog.money是nvarchar(20)
                LeftMoney = currentRMBBalance.ToString("F2"), // ✅ 传入计算后的RMB余额，避免SELECT user表
                OperaUserId = long.Parse(operaUserId),
                OperaNickname = WapTool.Left(operaNickname, 50),
                Remark = fullRemark,
                IP = WapTool.Left(IP, 30)
            }, transaction);
        }

        /// <summary>
        /// 基础页面初始化
        /// </summary>
        private void InitializePage()
        {
            // 这里可以添加一些基础的初始化逻辑
        }

        /// <summary>
        /// 初始化页面变量（新版UI使用）
        /// </summary>
        private void InitializePageVariables()
        {
            // 获取表单数据
            action = GetRequestValue("action");
            tomoney = GetRequestValue("tomoney");
            changePW = GetRequestValue("changePW");

            // 获取兑换比例设置
            string siteDefault = WapTool.GetSiteDefault(siteVo.Version, 5);
            if (WapTool.IsNumeric(siteDefault))
            {
                STATE = long.Parse(siteDefault);
            }

            // 检查功能是否关闭
            if (STATE < 1L)
            {
                INFO = "CLOSE";
            }

            // 设置返回URL
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "/myfile.aspx";
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);

            // 验证用户登录
            IsLogin(userid, backurl);
        }

        /// <summary>
        /// 检查用户UI偏好并处理版本切换
        /// </summary>
        private bool CheckAndHandleUIPreference()
        {
            string uiPreference = "";
            if (Request.Cookies["ui_preference"] != null)
            {
                uiPreference = Request.Cookies["ui_preference"].Value;
            }
            if (string.IsNullOrEmpty(uiPreference))
            {
                uiPreference = "old";
            }

            if (uiPreference == "new")
            {
                return TryRenderWithHandlebars();
            }
            return false;
        }

        /// <summary>
        /// 尝试使用Handlebars模板渲染页面
        /// </summary>
        private bool TryRenderWithHandlebars()
        {
            try
            {
                // 直接检查TemplateService可用性
                string viewMode = TemplateService.GetViewMode();
                if (viewMode == "new")
                {
                    RenderWithHandlebars();
                    return true;
                }
                return false;
            }
            catch (System.Threading.ThreadAbortException)
            {
                return true;
            }
            catch (Exception ex)
            {
                ERROR = "新版模板加载失败: " + WapTool.ErrorToString(ex.ToString());
                return false;
            }
        }

        /// <summary>
        /// 使用Handlebars模板渲染页面
        /// </summary>
        private void RenderWithHandlebars()
        {
            try
            {
                // 初始化必要的变量（与旧版逻辑保持一致）
                InitializePageVariables();

                // 处理表单提交（如果有）
                string action = base.Request.Form.Get("action");
                if (action == "add")
                {
                    ProcessExchangeRequest();
                }

                // 构建页面数据模型
                var pageModel = BuildRMBtoMoneyPageModel();

                // 调用新的 RenderPageWithLayout 方法
                string finalHtml = TemplateService.RenderPageWithLayout(
                    "~/Template/Pages/RMBtoMoney.hbs",
                    pageModel,
                    "充值与兑换",
                    new HeaderOptionsModel { ShowViewModeToggle = false }
                );

                // 输出渲染结果
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write(finalHtml);
                Response.End();
            }
            catch (System.Threading.ThreadAbortException)
            {
                // Response.End() 的正常行为，直接重新抛出
                throw;
            }
            catch (Exception ex)
            {
                // 错误处理
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}</div>");
                HttpContext.Current.ApplicationInstance.CompleteRequest();
            }
        }

        /// <summary>
        /// 处理兑换请求（新版UI）
        /// </summary>
        private void ProcessExchangeRequest()
        {
            // 重新获取表单数据
            action = GetRequestValue("action");
            tomoney = GetRequestValue("tomoney");
            changePW = GetRequestValue("changePW");

            // 调用原有的兑换逻辑
            addMoney();
        }

        /// <summary>
        /// 构建页面数据模型
        /// </summary>
        private RMBtoMoneyPageModel BuildRMBtoMoneyPageModel()
        {
            var model = new RMBtoMoneyPageModel();

            // 构建消息状态
            BuildMessageModel(model);

            // 构建用户资产信息
            BuildUserAssetsModel(model);

            // 构建兑换设置
            BuildExchangeSettingsModel(model);

            // 构建充值信息
            BuildRechargeInfoModel(model);

            // 构建表单数据
            BuildFormDataModel(model);

            return model;
        }

        /// <summary>
        /// 构建消息模型
        /// </summary>
        private void BuildMessageModel(RMBtoMoneyPageModel model)
        {
            if (!string.IsNullOrEmpty(ERROR))
            {
                model.Message.SetError(ERROR);
            }
            else if (INFO == "OK")
            {
                model.Message.SetSuccess("兑换成功！");
            }
            else if (INFO == "CLOSE")
            {
                model.Message.SetError("站长已关闭此功能！");
            }
            else if (INFO == "PWERR")
            {
                model.Message.SetError("密码错误！");
            }
            else if (INFO == "NUM")
            {
                model.Message.SetError("金额需要数字！");
            }
            else if (INFO == "NOTMONEY")
            {
                model.Message.SetError($"你的RMB只有{userVo.RMB:F2}！");
            }
            else if (INFO == "MAXMONEY")
            {
                model.Message.SetError("每次不能大于￥1000.00！");
            }
            else if (INFO == "WAITING")
            {
                model.Message.SetWarning("操作过于频繁，请稍后再试！");
            }
        }

        /// <summary>
        /// 构建用户资产模型
        /// </summary>
        private void BuildUserAssetsModel(RMBtoMoneyPageModel model)
        {
            model.UserAssets.CurrentRMB = userVo.RMB;
            model.UserAssets.CurrentMoney = userVo.money;
            model.UserAssets.MoneyName = siteVo.sitemoneyname;
        }

        /// <summary>
        /// 构建兑换设置模型
        /// </summary>
        private void BuildExchangeSettingsModel(RMBtoMoneyPageModel model)
        {
            model.ExchangeSettings.ExchangeRate = STATE;
            model.ExchangeSettings.IsEnabled = STATE >= 1L;
            model.ExchangeSettings.MoneyName = siteVo.sitemoneyname;
            model.ExchangeSettings.MaxExchangeAmount = 1000.00m;
        }

        /// <summary>
        /// 构建充值信息模型
        /// </summary>
        private void BuildRechargeInfoModel(RMBtoMoneyPageModel model)
        {
            model.RechargeInfo.UserId = userid.ToString();
            model.RechargeInfo.SupportsAlipay = true;
            model.RechargeInfo.SupportsWechat = true;
        }

        /// <summary>
        /// 构建表单数据模型
        /// </summary>
        private void BuildFormDataModel(RMBtoMoneyPageModel model)
        {
            model.FormData.ActionUrl = http_start + "bbs/RMBtoMoney.aspx";
            model.FormData.ExchangeAmount = tomoney;
            model.FormData.HiddenFields.Action = "add";
            model.FormData.HiddenFields.SiteId = siteid.ToString();
            model.FormData.HiddenFields.BackUrl = backurl;
        }
    }
}