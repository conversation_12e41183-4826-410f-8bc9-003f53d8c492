# DapperHelper & TransactionHelper 使用手册

> 适用范围：.NET Framework + Dapper + SQL Server。
> 
> **入口源码**：`YaoHuo.Plugin/WebSite/Tool/DapperHelper.cs`

---

## 1️⃣ DapperHelper 快捷方法

| 方法 | 说明 |
|------|------|
| `Execute(sql, params)` | 执行 INSERT/UPDATE/DELETE，返回受影响行数 |
| `Query<T>(sql, params)` | 查询返回 `IEnumerable<T>` |
| `ExecuteScalar<T>(sql, params)` | 查询单值 |
| `ExecuteInTransaction(connStr, actions[])` | 自定义事务，批量执行 |

### 示例：更新用户昵称
```csharp
var sql = "UPDATE [user] SET nickname=@Nickname WHERE userid=@Userid AND siteid=@Siteid";
int rows = DapperHelper.Execute(connStr, sql, new { Nickname = nickname, Userid = userId, Siteid = siteId });
```


## 2️⃣ TransactionHelper 资金事务

确保余额、积分等关键字段 **一致性&原子性**。

```csharp
TransactionHelper.ExecuteMoneyTransaction(connStr, (conn, tran) =>
{
    // 1. 扣钱
    TransactionHelper.UpdateUserBankMoney(conn, tran, fromUserId, siteId, -amount);

    // 2. 加钱
    TransactionHelper.UpdateUserBankMoney(conn, tran, toUserId, siteId, +amount);

    // 3. 记录流水
    var sql = "INSERT INTO money_log(siteid,userid,amount,remark) VALUES(@Siteid,@Userid,@Amount,@Remark)";
    conn.Execute(sql, new { Siteid = siteId, Userid = fromUserId, Amount = -amount, Remark = "转账" }, tran);
});
```

> ⚠️ **注意**：`ExecuteMoneyTransaction` 已封装超时时间与异常回滚，操作失败自动 `Rollback` 并抛出异常。


## 3️⃣ 常见坑

| 场景 | 建议 |
|------|------|
| 死锁 & 并发 | 控制事务粒度；必要时加 `UPDLOCK` |
| 批量 `INSERT` | 使用 `SqlBulkCopy` 或分批提交 |
| 参数化 `IN` | 传 `IEnumerable<int>`，Dapper 自动展开 |
| 事务嵌套 | 避免；必要时显式传入同一 `IDbTransaction` |

---

> **完整示例**：`SendMoney.aspx.cs`、`GuessVote.aspx.cs` 中的资金操作实现。
