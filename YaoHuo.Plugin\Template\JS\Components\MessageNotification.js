/**
 * 消息通知管理类
 * 负责消息通知的UI交互、数据更新、用户操作处理
 * 
 * 简化版本：每个Tab独立轮询，无多Tab协调机制
 *
 * @version 2.0 (简化版)
 * <AUTHOR>
 * @date 2025-01-21
 * @location /Template/JS/Components/MessageNotification.js
 */
class MessageNotification {
    constructor() {
        this.updateInterval = null;
        this.isDropdownOpen = false;
        this.isLoading = false;
        this.currentUnread = 0; // 当前未读计数
        this.init();
    }

    /**
     * 初始化消息通知系统
     */
    init() {
        this.bindEvents();
        this.loadMessages(); // 初始加载
        this.startAutoUpdate(); // 开始独立轮询
    }



    /**
     * 绑定事件监听器
     */
    bindEvents() {
        const toggle = document.getElementById('message-notification');
        const dropdown = document.getElementById('message-dropdown');
        const markAllRead = document.getElementById('mark-all-read');

        // 下拉菜单切换
        if (toggle && dropdown) {
            toggle.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleDropdown();
            });
        }

        // 全部已读功能
        if (markAllRead) {
            markAllRead.addEventListener('click', (e) => {
                e.preventDefault();
                this.markAllAsRead();
            });
        }

        // 点击外部关闭下拉菜单
        document.addEventListener('click', () => this.closeDropdown());

        // 阻止下拉菜单内部点击事件冒泡
        if (dropdown) {
            dropdown.addEventListener('click', (e) => e.stopPropagation());
        }
    }

    /**
     * 切换下拉菜单显示状态
     */
    toggleDropdown() {
        const dropdown = document.getElementById('message-dropdown');
        if (!dropdown) return;

        this.isDropdownOpen = !this.isDropdownOpen;
        if (this.isDropdownOpen) {
            dropdown.classList.add('show');
            // 打开时刷新数据
            this.loadMessages();
        } else {
            dropdown.classList.remove('show');
        }
    }

    /**
     * 关闭下拉菜单
     */
    closeDropdown() {
        const dropdown = document.getElementById('message-dropdown');
        if (dropdown) {
            dropdown.classList.remove('show');
            this.isDropdownOpen = false;
            // 如果已无未读消息，恢复其他图标
            if (this.currentUnread === 0 && typeof restoreHeaderIcons === 'function') {
                restoreHeaderIcons();
            }
        }
    }

    /**
     * 开始独立轮询
     */
    startAutoUpdate() {
        // 每个Tab独立30秒轮询，简单可靠
        this.updateInterval = setInterval(() => {
            if (!this.isDropdownOpen && !document.hidden && !this.isLoading) {
                this.loadMessages();
            }
        }, 30000);
    }

    /**
     * 停止自动更新
     */
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }



    /**
     * 加载消息数据
     */
    async loadMessages() {
        if (this.isLoading) return;

        this.isLoading = true;

        try {
            const response = await fetch('/bbs/MessagePreview.aspx', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                cache: 'no-cache'
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.success) {
                this.updateBadge(data.unreadCount);
                this.updateMessageList(data.messages);
            } else {
                console.warn('消息加载失败:', data.error);
                if (this.isDropdownOpen) {
                    this.showError('加载消息失败');
                }
            }
        } catch (error) {
            console.error('消息加载异常:', error);
            if (this.isDropdownOpen) {
                this.showError('网络连接异常');
            }
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * 更新消息数量徽章
     */
    updateBadge(count) {
        const badge = document.getElementById('message-badge');
        if (!badge) return;

        this.currentUnread = count;

        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count.toString();
            badge.style.display = 'flex';
            // 显示铃铛 & 隐藏其他图标
            const notificationIcon = document.getElementById('message-notification');
            if (notificationIcon) notificationIcon.style.display = '';
            if (typeof hideOtherHeaderIcons === 'function') hideOtherHeaderIcons();
        } else {
            badge.style.display = 'none';
            // 只有在下拉菜单已关闭时才恢复图标
            if (!this.isDropdownOpen && typeof restoreHeaderIcons === 'function') {
                restoreHeaderIcons();
            }
        }
    }

    /**
     * 更新消息列表显示
     */
    updateMessageList(messages) {
        const list = document.getElementById('message-list');
        if (!list) return;

        if (messages.length === 0) {
            list.innerHTML = `
                <div class="p-8 text-center text-gray-500">
                    <i data-lucide="bell-off" class="w-8 h-8 mx-auto mb-2 opacity-50"></i>
                    <p class="text-sm">暂无消息</p>
                </div>
            `;
        } else {
            list.innerHTML = messages.map(msg => this.createMessageItem(msg)).join('');
            this.bindMessageItemEvents();
        }

        // 重新初始化Lucide图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    }

    /**
     * 创建消息项HTML
     */
    createMessageItem(msg) {
        const iconClass = msg.displayTitle === '系统通知' ? 'bell' : 'mail';
        const iconColor = msg.displayTitle === '系统通知' ? 'text-orange-500' : 'text-primary';
        const unreadClass = msg.isUnread ? 'unread' : '';

        return `
            <div class="notification-item ${unreadClass}" data-id="${msg.id}">
                <div class="flex items-start space-x-3">
                    <div class="icon-container w-8 h-8 ${msg.isUnread ? 'bg-primary-alpha-10' : 'bg-gray-100'} rounded-full flex items-center justify-center flex-shrink-0">
                        <i data-lucide="${iconClass}" class="w-4 h-4 ${iconColor}"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex items-center justify-between mb-1">
                            <p class="text-sm font-medium text-gray-800">${this.escapeHtml(msg.displayTitle)}</p>
                            <span class="text-xs text-gray-500 flex-shrink-0 ml-2">${msg.time}</span>
                        </div>
                        <p class="text-sm text-gray-600 truncate">${this.escapeHtml(msg.displayContent)}</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定消息项事件
     */
    bindMessageItemEvents() {
        const items = document.querySelectorAll('.notification-item');
        items.forEach(item => {
            item.addEventListener('click', () => {
                const messageId = item.dataset.id;
                if (messageId) {
                    // 跳转到消息详情页面
                    window.location.href = `/bbs/messagelist_view.aspx?id=${messageId}`;
                }
            });
        });
    }

    /**
     * 全部标记为已读
     */
    async markAllAsRead() {
        try {
            const response = await fetch('/bbs/messagelist_clear.aspx?action=godelall&issystem=3', {
                method: 'GET',
                cache: 'no-cache'
            });

            if (response.ok) {
                this.currentUnread = 0; // 更新状态
                this.updateBadge(0);
                await this.loadMessages(); // 重新加载消息列表
                this.showToast('所有消息已标记为已读');
            } else {
                throw new Error('标记已读请求失败');
            }
        } catch (error) {
            console.error('标记已读失败:', error);
            this.showToast('操作失败，请重试');
        }
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        const list = document.getElementById('message-list');
        if (list) {
            list.innerHTML = `
                <div class="p-8 text-center text-red-500">
                    <i data-lucide="alert-circle" class="w-8 h-8 mx-auto mb-2"></i>
                    <p class="text-sm">${message}</p>
                    <button onclick="messageNotification.loadMessages()" class="mt-2 text-xs text-primary hover:text-primary-dark">
                        重试
                    </button>
                </div>
            `;
            
            // 重新初始化Lucide图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }
    }

    /**
     * 显示Toast提示
     */
    showToast(message) {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.className = 'fixed bottom-5 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 text-white py-2.5 px-5 rounded z-[1000] text-sm';
        toast.style.opacity = '0';
        toast.style.transition = 'opacity 0.3s ease';

        document.body.appendChild(toast);

        setTimeout(() => toast.style.opacity = '1', 10);
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    document.body.removeChild(toast);
                }
            }, 300);
        }, 2000);
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 销毁实例，清理资源
     */
    destroy() {
        this.stopAutoUpdate();
    }
}

// 全局实例
let messageNotification = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    const notificationElement = document.getElementById('message-notification');
    if (notificationElement) {
        messageNotification = new MessageNotification();
    }
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (messageNotification) {
        messageNotification.destroy();
    }
}); 