using System;
using System.Collections.Generic;
using YaoHuo.Plugin.WebSite.Services.Config;

namespace YaoHuo.Plugin.OAuth
{
    /// <summary>
    /// OAuth 业务服务类（业务流程编排）
    /// </summary>
    public class OAuthService
    {
        private readonly IOAuthRepository _repository;

        public OAuthService(IOAuthRepository repository)
        {
            _repository = repository;
        }

        #region 完整业务流程

        /// <summary>
        /// 创建OAuth客户端（完整业务流程）
        /// </summary>
        /// <param name="appName">应用名称</param>
        /// <param name="appDescription">应用描述</param>
        /// <param name="redirectUris">重定向URI列表</param>
        /// <returns>客户端信息（包含明文密钥，仅此一次）</returns>
        public (OAuthClient client, string plainAppKey) CreateClient(string appName, string appDescription, string redirectUris)
        {
            var appId = Guid.NewGuid().ToString("N").ToUpper();
            var plainAppKey = GenerateSecureKey();
            var salt = GenerateAppSpecificSalt(appId);
            var hash = ComputeAppKeyHash(plainAppKey, salt);

            var client = new OAuthClient
            {
                AppId = appId,
                AppName = appName,
                AppDescription = appDescription,
                AppKeyHash = hash,
                AppKeySalt = salt,
                RedirectUris = redirectUris,
                AllowedScopes = OAuthConstants.SCOPE_PROFILE, // 默认权限
                IsValid = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _repository.CreateClient(client);

            return (client, plainAppKey);
        }

        /// <summary>
        /// 创建授权流程（完整业务流程）
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="redirectUri">重定向URI</param>
        /// <param name="scope">权限范围</param>
        /// <param name="codeChallenge">PKCE代码挑战</param>
        /// <param name="codeChallengeMethod">PKCE方法</param>
        /// <returns>授权码</returns>
        public string CreateAuthorizationFlow(string clientId, long userId, string redirectUri, string scope, string codeChallenge, string codeChallengeMethod)
        {
            try
            {
                // 1. 验证客户端
                var client = _repository.GetClient(clientId);

                if (client == null)
                    throw new ArgumentException($"客户端不存在: {clientId}");

                if (!client.IsActive())
                    throw new ArgumentException($"客户端已停用: {clientId}, IsValid={client.IsValid}, AppName={client.AppName}");

                // 2. 验证重定向URI
                if (!client.IsRedirectUriAllowed(redirectUri))
                    throw new ArgumentException($"重定向URI不在白名单中: {redirectUri}, 允许的URI: {client.RedirectUris}");

                // 3. 验证权限范围
                if (!client.IsScopeAllowed(scope))
                    throw new ArgumentException($"请求的权限范围不被允许: '{scope}', 允许的范围: '{client.AllowedScopes}'");

                // 4. 生成授权码
                var code = GenerateSecureCode();

                // 正确处理PKCE参数：空字符串和null都使用默认值，如果没有提供codeChallenge则设为null
                string finalCodeChallenge = string.IsNullOrEmpty(codeChallenge) ? null : codeChallenge;
                string finalCodeChallengeMethod = !string.IsNullOrEmpty(codeChallengeMethod) ? codeChallengeMethod :
                                                 finalCodeChallenge != null ? OAuthConstants.PKCE_METHOD_S256 : null;

                var authCode = new OAuthAuthorizationCode
                {
                    Code = code,
                    ClientId = clientId,
                    UserId = userId,
                    RedirectUri = redirectUri,
                    Scope = scope ?? OAuthConstants.SCOPE_PROFILE,
                    CodeChallenge = finalCodeChallenge,
                    CodeChallengeMethod = finalCodeChallengeMethod,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(OAuthConfigService.GetAuthorizationCodeLifetimeMinutes()),
                    CreatedAt = DateTime.UtcNow
                };

                _repository.CreateAuthorizationCode(authCode);

                return code;
            }
            catch (Exception)
            {
                throw; // 重新抛出原始异常，保持堆栈跟踪
            }
        }

        /// <summary>
        /// 交换授权码为访问令牌（完整业务流程）
        /// </summary>
        /// <param name="code">授权码</param>
        /// <param name="clientId">客户端ID</param>
        /// <param name="redirectUri">重定向URI</param>
        /// <param name="codeVerifier">PKCE代码验证器</param>
        /// <returns>访问令牌</returns>
        public OAuthAccessToken ExchangeCodeForToken(string code, string clientId, string redirectUri, string codeVerifier)
        {
            // 1. 获取并验证授权码
            var authCode = _repository.GetAuthorizationCode(code, clientId, redirectUri);
            if (authCode == null || !authCode.IsValid())
                throw new OAuth2Exception(OAuthConstants.INVALID_GRANT, "无效或已过期的授权码");

            // 2. 验证PKCE
            if (!string.IsNullOrEmpty(authCode.CodeChallenge) && !authCode.VerifyCodeChallenge(codeVerifier))
                throw new OAuth2Exception(OAuthConstants.INVALID_GRANT, "PKCE验证失败");

            // 3. 标记授权码为已使用
            _repository.MarkAuthorizationCodeAsUsed(code);

            // 4. 创建访问令牌（可配置的令牌管理策略）
            var tokenId = GenerateSecureToken();
            var accessToken = new OAuthAccessToken
            {
                TokenId = tokenId,
                ClientId = clientId,
                UserId = authCode.UserId,
                Scope = authCode.Scope,
                ExpiresAt = DateTime.UtcNow.AddHours(OAuthConfigService.GetAccessTokenLifetimeHours()),
                CreatedAt = DateTime.UtcNow
            };

            // 根据配置选择令牌创建策略
            var tokenManagementMode = OAuthConfigService.GetTokenManagementMode(); // 配置项：normal, smart, aggressive
            int affectedCount = 0;
            
            switch (tokenManagementMode.ToLower())
            {
                case OAuthConstants.TOKEN_MODE_AGGRESSIVE:
                    // 激进模式：物理删除现有令牌（无审计记录）
                    affectedCount = _repository.CreateAccessTokenAggressive(accessToken);
                    break;
                
                case OAuthConstants.TOKEN_MODE_SMART:
                default:
                    // 智能模式：撤销现有令牌（保留审计记录）
                    affectedCount = _repository.CreateAccessTokenSmart(accessToken);
                    break;
                
                case OAuthConstants.TOKEN_MODE_NORMAL:
                    // 普通模式：不处理现有令牌
                    _repository.CreateAccessToken(accessToken);
                    break;
            }

            return accessToken;
        }

        /// <summary>
        /// 撤销令牌（完整业务流程）
        /// </summary>
        /// <param name="tokenId">令牌ID</param>
        /// <param name="clientId">客户端ID</param>
        /// <param name="clientSecret">客户端密钥</param>
        /// <returns>是否成功</returns>
        public bool RevokeToken(string tokenId, string clientId, string clientSecret)
        {
            // 1. 验证客户端凭据
            if (!VerifyClientCredentials(clientId, clientSecret))
                return false;

            // 2. 撤销令牌
            return _repository.RevokeAccessToken(tokenId);
        }

        /// <summary>
        /// 直接撤销令牌（不验证客户端凭据，用于兼容性场景）
        /// </summary>
        /// <param name="tokenId">令牌ID</param>
        /// <returns>是否成功</returns>
        public bool RevokeTokenDirect(string tokenId)
        {
            return _repository.RevokeAccessToken(tokenId);
        }

        #endregion

        #region 安全验证与工具方法

        /// <summary>
        /// 验证客户端凭据
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="clientSecret">客户端密钥</param>
        /// <returns>是否验证通过</returns>
        public bool VerifyClientCredentials(string clientId, string clientSecret)
        {
            var client = _repository.GetClient(clientId);
            if (client == null || !client.IsActive())
                return false;

            return VerifyAppKey(clientSecret, client.AppKeyHash, client.AppKeySalt);
        }

        /// <summary>
        /// 验证访问令牌并获取用户信息（优化版 - 单次JOIN查询）
        /// </summary>
        /// <param name="tokenId">令牌ID</param>
        /// <returns>用户信息对象</returns>
        public object GetUserInfo(string tokenId)
        {
            // 使用优化的单次JOIN查询获取令牌和用户信息
            var tokenUserInfo = _repository.GetTokenWithUserInfo(tokenId);
            if (tokenUserInfo == null)
                return null;

            // 更新最后使用时间（异步执行，不影响响应时间）
            _repository.UpdateTokenLastUsed(tokenId);

            return new
            {
                userid = tokenUserInfo.userid,
                nickname = tokenUserInfo.nickname ?? "未知用户",
                level = tokenUserInfo.user_level, // 已在SQL中计算
                scope = tokenUserInfo.scope
            };
        }

        /// <summary>
        /// 验证重定向URI
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="redirectUri">重定向URI</param>
        /// <returns>是否允许</returns>
        public bool ValidateRedirectUri(string clientId, string redirectUri)
        {
            var client = _repository.GetClient(clientId);
            return client?.IsRedirectUriAllowed(redirectUri) ?? false;
        }

        #endregion

        #region 数据访问代理方法

        /// <summary>
        /// 获取客户端信息
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>客户端信息</returns>
        public OAuthClient GetClient(string clientId)
        {
            return _repository.GetClient(clientId);
        }

        /// <summary>
        /// 获取所有客户端列表
        /// </summary>
        /// <returns>客户端列表</returns>
        public List<OAuthClient> GetAllClients()
        {
            return _repository.GetAllClients();
        }

        /// <summary>
        /// 删除客户端
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>是否成功</returns>
        public bool DeleteClient(string clientId)
        {
            return _repository.DeleteClient(clientId);
        }

        /// <summary>
        /// 获取用户授权历史
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>授权历史列表</returns>
        public List<dynamic> GetUserAuthorizations(long userId)
        {
            return _repository.GetUserAuthorizations(userId);
        }

        /// <summary>
        /// 撤销用户在指定应用的所有令牌
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="clientId">客户端ID</param>
        /// <returns>撤销的令牌数量</returns>
        public int RevokeUserTokensForClient(long userId, string clientId)
        {
            return _repository.RevokeUserTokensForClient(userId, clientId);
        }

        /// <summary>
        /// 撤销用户的所有令牌
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>撤销的令牌数量</returns>
        public int RevokeAllUserTokens(long userId)
        {
            return _repository.RevokeAllUserTokens(userId);
        }

        #endregion

        #region 静态工具方法

        /// <summary>
        /// 生成安全的应用密钥
        /// </summary>
        /// <returns>应用密钥</returns>
        public static string GenerateSecureKey()
        {
            using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
            {
                byte[] keyBytes = new byte[32];
                rng.GetBytes(keyBytes);
                return Convert.ToBase64String(keyBytes);
            }
        }

        /// <summary>
        /// 生成安全的授权码
        /// </summary>
        /// <returns>授权码</returns>
        public static string GenerateSecureCode()
        {
            using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
            {
                byte[] codeBytes = new byte[32];
                rng.GetBytes(codeBytes);
                return Convert.ToBase64String(codeBytes).TrimEnd('=').Replace('+', '-').Replace('/', '_');
            }
        }

        /// <summary>
        /// 生成安全的访问令牌
        /// </summary>
        /// <returns>访问令牌</returns>
        public static string GenerateSecureToken()
        {
            using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
            {
                byte[] tokenBytes = new byte[48];
                rng.GetBytes(tokenBytes);
                return Convert.ToBase64String(tokenBytes).TrimEnd('=').Replace('+', '-').Replace('/', '_');
            }
        }

        /// <summary>
        /// 生成应用专用盐值
        /// </summary>
        /// <param name="appId">应用ID</param>
        /// <returns>盐值</returns>
        public static string GenerateAppSpecificSalt(string appId)
        {
            var globalSalt = OAuthConfigService.GetAppKeySalt();
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                var combined = globalSalt + appId + DateTime.UtcNow.ToString("yyyyMMdd");
                var hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(combined));
                return Convert.ToBase64String(hashBytes);
            }
        }

        /// <summary>
        /// 计算应用密钥Hash值
        /// </summary>
        /// <param name="appKey">明文密钥</param>
        /// <param name="salt">盐值</param>
        /// <returns>Hash值</returns>
        public static string ComputeAppKeyHash(string appKey, string salt)
        {
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                var saltedKey = appKey + salt;
                var hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(saltedKey));
                return Convert.ToBase64String(hashBytes);
            }
        }

        /// <summary>
        /// 验证应用密钥
        /// </summary>
        /// <param name="appKey">明文密钥</param>
        /// <param name="storedHash">存储的Hash值</param>
        /// <param name="salt">盐值</param>
        /// <returns>是否匹配</returns>
        public static bool VerifyAppKey(string appKey, string storedHash, string salt)
        {
            var computedHash = ComputeAppKeyHash(appKey, salt);
            return computedHash == storedHash;
        }



        #endregion
    }
}