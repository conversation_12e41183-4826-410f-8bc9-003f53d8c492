﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ToGroupBuy.aspx.cs" Inherits="YaoHuo.Plugin.BBS.toGroupBuy" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %><!DOCTYPE html>
<html >
<head>
<title>RMB购买身份</title>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
<link rel="stylesheet" href="/NetCSS/CSS/BBS/GroupBuy.css?5"/>
</head>
<body>
<div class="modern-identity-purchase">

    <!-- 添加页面头部 -->
    <div class="header">
        <a href="/bbs/BuyGroup.aspx" class="back-button">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-6-6 6-6"/></svg>
        </a>
        <h1 class="header-title">身份购买/充值</h1>
    </div>

    <!-- 添加选项卡栏 -->
    <div class="tab-bar">
        <a href="<%= this.http_start %>bbs/togroupcoinbuy.aspx?toid=<%= this.toid %>" class="tab-button"><%= this.GetLang("妖晶购买") %></a>
        <a href="<%= this.http_start %>bbs/togroupbuy.aspx?siteid=<%= this.siteid %>&classid=<%= this.classid %>&toid=<%= this.toid %>" class="tab-button active">RMB购买</a>
    </div>

    <!-- 添加内容区域 -->
    <div class="content">

        <%
            // 显示错误或成功信息
            if (!string.IsNullOrEmpty(this.ERROR))
            {
        %>
                <div class="tip">
                    <%= this.ERROR %>
                </div>
        <%
            }

            if (this.INFO == "OK")
            {
        %>
                <div class="success">
                    <div style="display:none;">
                        <b>您已成功购买身份</b><br/>
                        [<a href="/bbs/userinfo.aspx?touserid=<%= this.userid %>">进入空间查看</a>]
                    </div>

                    <%
                        // 添加隐藏的购买信息用于弹窗显示
                        string identityName = WapTool.ShowImg(idVo.subclassName);
                        string numValue = Request.QueryString["num"] ?? (toid == "105" ? "12" : this.num);
                        string priceValue = "￥" + (idVo.rank * int.Parse(numValue)).ToString("F2");
                    %>
                    <span id="boughtIdentity" style="display:none;"><%= identityName %></span>
                    <span id="boughtMonths" style="display:none;"><%= numValue %></span>
                    <span id="paidAmount" style="display:none;"><%= priceValue %></span>
                </div>
        <%
            }
            else if (this.INFO == "CLOSE")
            {
        %>
                <div class="tip">
                    <b>暂时无法购买此身份！</b>
                </div>
        <%
            }
            else if (this.INFO == "NOTRIGHT")
            {
        %>
                <div class="tip">
                    <b>*一次只能购买大于[当前我的身份级别]一级的身份级别！请购买其它身份级别！</b>
                </div>
        <%
            }
            else if (this.INFO == "PWERR")
            {
        %>
                <div class="tip">
                    <b>密码错误！</b>
                </div>
        <%
            }
            else if (this.INFO == "NUM")
            {
        %>
                <div class="tip">
                    <b>金额需要数字！</b>
                </div>
        <%
            }
            else if (this.INFO == "NOTMONEY")
            {
        %>
                <div class="tip">
                    <b>您的RMB数量不足！</b>
                </div>
        <%
            }

            if (idVo != null && this.INFO != "CLOSE" && this.INFO != "NOTRIGHT" && this.INFO != "MASTERNO")
            {
                // 我的资产卡片
        %>
                <div class="card">
                    <h2 class="card-title">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><path d="M20 12V8H6a2 2 0 0 1-2-2c0-1.1.9-2 2-2h12v4"/><path d="M4 6v12c0 1.1.9 2 2 2h14v-4"/><path d="M18 12a2 2 0 0 0-2 2c0 1.1.9 2 2 2h4v-4h-4z"/></svg>
                        我的资产
                    </h2>

                    <!-- 我的RMB -->
                    <div class="card-row">
                        <span class="card-label">我的RMB</span>
                        <div class="flex items-center">
                            <span class="card-value green-text large-text">￥<%= userVo.RMB.ToString("f2") %></span>
                            <a href="<%= this.http_start %>chinabank_wap/selbank_wap.aspx?siteid=<%= this.siteid %>" class="button button-outline">充值</a>
                        </div>
                    </div>

                    <!-- 当前身份 -->
                    <div class="card-row">
                        <span class="card-label">当前身份</span>
                        <span class="card-value" id="currentIdentity"><%= WapTool.GetMyID(userVo.idname, lang) %></span>
                    </div>

                    <!-- 到期日期 -->
                    <div class="card-row">
                        <div class="flex items-center">
                            <span class="card-label">到期时间</span>
                        </div>
                        <div class="flex items-center">
                            <div class="calendar-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"/><line x1="16" y1="2" x2="16" y2="6"/><line x1="8" y1="2" x2="8" y2="6"/><line x1="3" y1="10" x2="21" y2="10"/></svg>
                                <div class="tooltip tooltip-right">剩余<span id="remainingDaysDisplay">0</span>天</div>
                            </div>
                            <span class="card-value" id="currentExpiryDateDisplay"><%= WapTool.showIDEndTime(userVo.siteid, userVo.userid, userVo.endTime, this.lang).Replace("<br/>", "") %></span>
                        </div>
                    </div>
                </div>

                <!-- 选购方案卡片 -->
                <div class="card">
                    <h2 class="card-title">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><rect x="2" y="5" width="20" height="14" rx="2"/><line x1="2" y1="10" x2="22" y2="10"/></svg>
                        选购方案
                    </h2>

                    <!-- 购买身份 -->
                    <div class="card-row">
                        <span class="card-label">购买身份</span>
                        <span class="card-value" id="targetIdentity"><%= WapTool.ShowImg(idVo.subclassName) %></span>
                    </div>

                    <!-- 购买月数 -->
                    <div class="card-row">
                        <span class="card-label">购买月数</span>
                        <div class="counter">
                            <button type="button" class="counter-button decrease-month">-</button>
                            <%
                                string numValue = Request.QueryString["num"] ?? (toid == "105" ? "12" : this.num);
                            %>
                            <span class="counter-value" id="month-count"><%= numValue %></span>
                            <button type="button" class="counter-button increase-month">+</button>
                        </div>
                    </div>

                    <!-- 需要RMB -->
                    <div class="card-row">
                        <span class="card-label">需要RMB</span>
                        <span class="card-value green-text large-text" id="price" data-initial-price="<%= idVo.rank %>">￥<%= (idVo.rank * int.Parse(numValue)).ToString("F2") %></span>
                    </div>

                    <!-- 有效期至 -->
                    <div class="card-row">
                        <div class="flex items-center">
                            <span class="card-label">有效期至</span>
                            <div class="info-icon">i
                                <div class="tooltip tooltip-left">旧身份可折抵<span id="convertibleDaysInfo">0</span>天</div>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="calendar-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"/><line x1="16" y1="2" x2="16" y2="6"/><line x1="8" y1="2" x2="8" y2="6"/><line x1="3" y1="10" x2="21" y2="10"/></svg>
                                <div class="tooltip tooltip-right">预计<span id="totalDays">0</span>天</div>
                            </div>
                            <span class="card-value" id="expiryDate"></span>
                        </div>
                    </div>
                </div>

                <!-- 安全验证卡片 -->
                <div class="card">
                    <h2 class="card-title">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/><path d="m9 12 2 2 4-4"/></svg>
                        安全验证
                    </h2>

                    <!-- 表单 -->
                    <form name="f" action="<%= http_start %>bbs/togroupbuy.aspx" method="post">
                        <div class="input-wrapper">
                            <input type="password" class="input" name="changePW" id="password" placeholder="请输入您的密码" required />
                            <div class="input-icon" id="toggle-password">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" id="eye-icon"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>
                            </div>
                        </div>
                        <input type="hidden" name="siteid" value="<%= siteid %>" />
                        <input type="hidden" name="backurl" value="<%= backurl %>" />
                        <input type="hidden" name="toid" value="<%= toid %>" />
                        <input type="hidden" name="num" value="<%= numValue %>" id="hiddenNum" />
                        <input type="hidden" name="action" value="add" />
                        <button type="submit" class="button button-primary">
                            <%= this.GetLang("确定支付|确定支付|submit play") %>
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-1"><polyline points="9 18 15 12 9 6"/></svg>
                        </button>
                    </form>
                </div>

                <!-- 隐藏的表单用于更改月数 -->
                <form name="g1" id="monthForm" action="<%= http_start %>bbs/togroupbuy.aspx" method="get" style="display:none;">
                    <input type="number" name="num" id="numInput" value="<%= numValue %>" min="1" <%= (toid == "105" ? "min=\"12\"" : "") %> />
                    <input type="hidden" name="classid" value="<%= classid %>" />
                    <input type="hidden" name="siteid" value="<%= siteid %>" />
                    <input type="hidden" name="toid" value="<%= toid %>" />
                </form>
        <%
            }
        %>

    </div> <%-- 关闭 content --%>

</div> <%-- 关闭 modern-identity-purchase --%>

<%
    // 将后端数据写入页面供JavaScript使用
%>
<script>
    var currentRank = '<%= CurrentRank %>';
    var remainingDays = '<%= RemainingDays %>';
    var targetRank = '<%= TargetRank %>';
    var currentId = '<%= CurrentId %>';
    var toid = '<%= ToidNum %>';
</script>
<script src="/NetCSS/JS/BBS/ModalManager.js?1"></script>
<script src="/NetCSS/JS/BBS/IdentityPurchase.js?1"></script>
<%
    Response.Write(WapTool.showDown(wmlVo));
%>