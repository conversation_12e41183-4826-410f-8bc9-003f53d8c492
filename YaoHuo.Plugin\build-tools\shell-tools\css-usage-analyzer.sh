#!/bin/bash
# css-usage-analyzer.sh - CSS使用情况分析工具
# 分析CSS类名的使用频率和分布情况

echo "=== CSS使用情况分析工具 ==="
echo "分析时间: $(date)"
echo "分析范围: style.css 中的自定义类名在整个项目中的使用情况"
echo ""

# 检查必要文件和目录
if [ ! -f "style.css" ]; then
    echo "❌ 错误: style.css 文件不存在"
    exit 1
fi

if [ ! -d "../Template" ]; then
    echo "❌ 错误: Template 目录不存在"
    exit 1
fi

# 1. 提取所有自定义类名
echo "📊 1. 提取自定义类名..."
echo "----------------------------------------"

# 从style.css中提取自定义类名（排除Tailwind标准类名）
CUSTOM_CLASSES=$(grep -o "\.[a-zA-Z][a-zA-Z0-9_-]*" style.css | \
    grep -v "^\.bg-\|^\.text-\|^\.border-\|^\.p-\|^\.m-\|^\.w-\|^\.h-\|^\.flex\|^\.grid\|^\.rounded\|^\.shadow\|^\.transition\|^\.duration\|^\.ease" | \
    sort | uniq)

total_custom_classes=$(echo "$CUSTOM_CLASSES" | wc -l)
echo "发现 $total_custom_classes 个自定义类名"
echo ""

# 2. 分析使用频率
echo "📊 2. 分析使用频率..."
echo "----------------------------------------"

# 创建临时文件存储结果
temp_file=$(mktemp)
echo "类名,模板使用次数,ASPX使用次数,CS使用次数,JS使用次数,总使用次数,使用文件列表" > "$temp_file"

high_usage=0    # 高频使用 (>10次)
medium_usage=0  # 中频使用 (3-10次)
low_usage=0     # 低频使用 (1-2次)
unused=0        # 未使用

echo "正在分析各类名使用情况..."

for class in $CUSTOM_CLASSES; do
    class_name=${class#.}
    
    # 在不同类型文件中搜索使用情况
    template_count=0
    aspx_count=0
    cs_count=0
    js_count=0
    file_list=""
    
    # 搜索模板文件 (.hbs)
    if [ -d "../Template" ]; then
        template_files=$(find ../Template -name "*.hbs" -exec grep -l "$class_name" {} \; 2>/dev/null)
        template_count=$(echo "$template_files" | grep -c . 2>/dev/null || echo 0)
        if [ $template_count -gt 0 ]; then
            file_list="$file_list Template:$(echo "$template_files" | tr '\n' ',' | sed 's/,$//')"
        fi
    fi
    
    # 搜索ASPX文件
    aspx_files=$(find .. -name "*.aspx" -exec grep -l "$class_name" {} \; 2>/dev/null)
    aspx_count=$(echo "$aspx_files" | grep -c . 2>/dev/null || echo 0)
    if [ $aspx_count -gt 0 ]; then
        file_list="$file_list ASPX:$(echo "$aspx_files" | tr '\n' ',' | sed 's/,$//')"
    fi
    
    # 搜索CS文件
    cs_files=$(find .. -name "*.cs" -exec grep -l "$class_name" {} \; 2>/dev/null)
    cs_count=$(echo "$cs_files" | grep -c . 2>/dev/null || echo 0)
    if [ $cs_count -gt 0 ]; then
        file_list="$file_list CS:$(echo "$cs_files" | tr '\n' ',' | sed 's/,$//')"
    fi
    
    # 搜索JS文件
    js_files=$(find .. -name "*.js" -exec grep -l "$class_name" {} \; 2>/dev/null)
    js_count=$(echo "$js_files" | grep -c . 2>/dev/null || echo 0)
    if [ $js_count -gt 0 ]; then
        file_list="$file_list JS:$(echo "$js_files" | tr '\n' ',' | sed 's/,$//')"
    fi
    
    total_count=$((template_count + aspx_count + cs_count + js_count))
    
    # 分类统计
    if [ $total_count -eq 0 ]; then
        unused=$((unused + 1))
        echo "⚠️  $class - 未使用"
    elif [ $total_count -le 2 ]; then
        low_usage=$((low_usage + 1))
        echo "🟡 $class - 低频使用 ($total_count 次)"
    elif [ $total_count -le 10 ]; then
        medium_usage=$((medium_usage + 1))
        echo "🟢 $class - 中频使用 ($total_count 次)"
    else
        high_usage=$((high_usage + 1))
        echo "🔥 $class - 高频使用 ($total_count 次)"
    fi
    
    # 保存详细数据到临时文件
    echo "$class_name,$template_count,$aspx_count,$cs_count,$js_count,$total_count,\"$file_list\"" >> "$temp_file"
done

echo ""

# 3. 生成统计报告
echo "📊 3. 使用频率统计..."
echo "----------------------------------------"

echo "📈 使用频率分布:"
echo "   🔥 高频使用 (>10次): $high_usage 个类名"
echo "   🟢 中频使用 (3-10次): $medium_usage 个类名"
echo "   🟡 低频使用 (1-2次): $low_usage 个类名"
echo "   ⚠️  未使用: $unused 个类名"

echo ""
echo "📊 使用率: $(( (total_custom_classes - unused) * 100 / total_custom_classes ))%"

# 4. 高频使用类名详情
echo ""
echo "📊 4. 高频使用类名详情..."
echo "----------------------------------------"

echo "以下类名使用频率最高，是核心组件类："
awk -F, '$6 > 10 {print $1 " - " $6 "次使用"}' "$temp_file" | sort -k3 -nr | head -10

# 5. 未使用类名详情
echo ""
echo "📊 5. 未使用类名详情..."
echo "----------------------------------------"

unused_classes=$(awk -F, '$6 == 0 {print $1}' "$temp_file")
if [ -n "$unused_classes" ]; then
    echo "以下类名可能可以安全删除："
    echo "$unused_classes" | while read class; do
        # 检查是否在注释中被提及
        comment_usage=$(grep -c "// .*$class\|/\* .*$class" style.css 2>/dev/null || echo 0)
        if [ $comment_usage -gt 0 ]; then
            echo "⚠️  .$class - 未使用但在注释中被提及，需要进一步确认"
        else
            echo "🗑️  .$class - 可能可以安全删除"
        fi
    done
else
    echo "✅ 所有自定义类名都有被使用"
fi

# 6. 按文件类型分析
echo ""
echo "📊 6. 按文件类型使用分析..."
echo "----------------------------------------"

template_total=$(awk -F, '{sum += $2} END {print sum}' "$temp_file")
aspx_total=$(awk -F, '{sum += $3} END {print sum}' "$temp_file")
cs_total=$(awk -F, '{sum += $4} END {print sum}' "$temp_file")
js_total=$(awk -F, '{sum += $5} END {print sum}' "$temp_file")

echo "📁 各文件类型中的CSS类名使用情况:"
echo "   📄 模板文件 (.hbs): $template_total 次使用"
echo "   🌐 ASPX文件: $aspx_total 次使用"
echo "   💻 C#文件 (.cs): $cs_total 次使用"
echo "   ⚡ JavaScript文件 (.js): $js_total 次使用"

# 7. 生成优化建议
echo ""
echo "📊 7. 优化建议..."
echo "----------------------------------------"

echo "🎯 基于使用情况分析的优化建议:"

if [ $unused -gt 0 ]; then
    echo "1. 🗑️  清理未使用类名: 发现 $unused 个未使用的类名，建议进一步验证后删除"
fi

if [ $low_usage -gt 5 ]; then
    echo "2. 🔍 审查低频类名: $low_usage 个低频使用类名，考虑是否可以合并或重构"
fi

if [ $high_usage -gt 0 ]; then
    echo "3. 📚 文档化核心类名: $high_usage 个高频类名是核心组件，建议完善文档"
fi

echo "4. 📊 定期监控: 建议定期运行此分析，跟踪CSS使用情况变化"

# 8. 生成详细报告文件
echo ""
echo "📊 8. 生成详细报告..."
echo "----------------------------------------"

report_file="css-usage-report-$(date +%Y%m%d-%H%M%S).csv"
cp "$temp_file" "$report_file"
echo "📄 详细报告已保存到: $report_file"

# 清理临时文件
rm "$temp_file"

echo ""
echo "✅ 分析完成！"
echo "💡 提示: 使用生成的CSV报告可以进行更深入的数据分析"
