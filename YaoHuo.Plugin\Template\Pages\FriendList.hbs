<div class="friend-type-{{FriendType}} pb-4">
{{#if HasError}}
<div class="toast-error" id="errorToast">
    <div class="flex items-center flex-1">
        <i class="w-5 h-5 mr-3 flex-shrink-0" data-lucide="x-circle"></i>
        <span class="flex-1 leading-[1.4]">{{Error}}</span>
    </div>
    <button class="bg-transparent border-none text-white cursor-pointer p-1 ml-3 rounded flex items-center justify-center transition-colors duration-200 flex-shrink-0 hover:bg-white/20" onclick="closeToast('errorToast')">
        <i class="w-4 h-4" data-lucide="x"></i>
    </button>
</div>
{{/if}}

{{#if HasInfo}}
<div class="{{#eq Info "OK_FRIEND"}}toast-success{{else}}{{#eq Info "OK_BLACKLIST"}}toast-success{{else}}{{#eq Info "OK_LOVE"}}toast-success{{else}}{{#eq Info "HASEXIST"}}toast-warning{{else}}{{#eq Info "NOTBLACK"}}toast-warning{{else}}{{#eq Info "UPMAX"}}toast-warning{{else}}toast-info{{/eq}}{{/eq}}{{/eq}}{{/eq}}{{/eq}}{{/eq}}" id="infoToast">
    <div class="flex items-center flex-1">
        {{#eq Info "OK_FRIEND"}}
        <i class="w-5 h-5 mr-3 flex-shrink-0" data-lucide="check-circle"></i>
        {{else}}{{#eq Info "OK_BLACKLIST"}}
        <i class="w-5 h-5 mr-3 flex-shrink-0" data-lucide="check-circle"></i>
        {{else}}{{#eq Info "OK_LOVE"}}
        <i class="w-5 h-5 mr-3 flex-shrink-0" data-lucide="check-circle"></i>
        {{else}}{{#eq Info "HASEXIST"}}
        <i class="w-5 h-5 mr-3 flex-shrink-0" data-lucide="alert-triangle"></i>
        {{else}}{{#eq Info "NOTBLACK"}}
        <i class="w-5 h-5 mr-3 flex-shrink-0" data-lucide="alert-triangle"></i>
        {{else}}{{#eq Info "UPMAX"}}
        <i class="w-5 h-5 mr-3 flex-shrink-0" data-lucide="alert-triangle"></i>
        {{else}}
        <i class="w-5 h-5 mr-3 flex-shrink-0" data-lucide="info"></i>
        {{/eq}}{{/eq}}{{/eq}}{{/eq}}{{/eq}}{{/eq}}
        <span class="flex-1 leading-[1.4]">
            {{#eq Info "NOTUSER"}}用户不存在{{/eq}}
            {{#eq Info "MAX"}}今日添加好友数量已达上限{{/eq}}
            {{#eq Info "MY"}}不能添加自己{{#eq FriendType "0"}}为好友{{/eq}}{{#eq FriendType "1"}}到黑名单{{/eq}}{{#eq FriendType "2"}}为追求对象{{/eq}}{{/eq}}
            {{#eq Info "LOCK"}}您的账号已被锁定{{/eq}}
            {{#eq Info "HASEXIST"}}该用户已在列表中{{/eq}}
            {{#eq Info "NOTBLACK"}}不允许拉黑管理员哦{{/eq}}
            {{#eq Info "UPMAX"}}拉黑失败，已达到当前用户等级黑名单上限{{/eq}}
            {{#eq Info "OK_FRIEND"}}添加好友成功{{/eq}}
            {{#eq Info "OK_BLACKLIST"}}已加入黑名单{{/eq}}
            {{#eq Info "OK_LOVE"}}添加追求成功{{/eq}}
            {{#eq Info "OK"}}操作成功{{/eq}}
        </span>
    </div>
    <button class="bg-transparent border-none text-white cursor-pointer p-1 ml-3 rounded flex items-center justify-center transition-colors duration-200 flex-shrink-0 hover:bg-white/20" onclick="closeToast('infoToast')">
        <i class="w-4 h-4" data-lucide="x"></i>
    </button>
</div>
{{/if}}

<div class="card">
    <div class="card-header">
        <div class="card-title justify-between">
            <div class="flex items-center">
                <i class="card-icon" data-lucide="{{#eq FriendType "0"}}users{{/eq}}{{#eq FriendType "1"}}user-x{{/eq}}{{#eq FriendType "2"}}heart{{/eq}}{{#eq FriendType "4"}}heart{{/eq}}{{#eq FriendType "5"}}user-plus{{/eq}}"></i>
                <span>{{PageTitle}}</span>
            </div>
            {{#if FriendsList}}
            <span class="text-sm text-text-secondary font-normal inline" id="friend-count-display">共 <span id="friend-count-number">{{Pagination.Total}}</span> 个{{#eq FriendType "0"}}好友{{/eq}}{{#eq FriendType "1"}}用户{{/eq}}</span>
            {{/if}}
        </div>
    </div>
    <div class="card-body">
        {{#if ShowSearchBox}}
        <div class="mb-4">
            <form method="post" action="?action=class&siteid={{SiteId}}&friendtype={{FriendType}}&backurl={{BackUrl}}">
                <div class="relative flex items-center">
                    <input type="text" name="key" value="{{SearchKey}}" placeholder="输入ID或昵称搜索" class="search-input">
                    <button type="submit" class="search-button">
                        <i data-lucide="search" class="w-5 h-5"></i>
                    </button>
                </div>
            </form>
        </div>
        {{/if}}

        {{!-- 黑名单页面的隐藏搜索功能，预留给未来使用 --}}
        {{#eq FriendType "1"}}
        {{#if EnableBlacklistSearch}}
        <div class="mb-4 hidden" id="blacklist-search-section">
            <form method="post" action="?action=class&siteid={{SiteId}}&friendtype={{FriendType}}&backurl={{BackUrl}}">
                <div class="relative flex items-center">
                    <input type="text" name="key" value="{{SearchKey}}" placeholder="输入ID或昵称搜索" class="search-input">
                    <button type="submit" class="search-button">
                        <i data-lucide="search" class="w-5 h-5"></i>
                    </button>
                </div>
            </form>
        </div>
        {{/if}}
        {{/eq}}

        <div class="flex flex-col gap-3 min-h-[100px]">
            {{#if FriendsList}}
            {{#each FriendsList}}
            <div class="friend-item">
                <div class="w-12 h-12 {{#eq ../FriendType "0"}}bg-gradient-to-br from-primary-light to-primary{{/eq}}{{#eq ../FriendType "1"}}bg-gradient-to-br from-red-300 to-danger{{/eq}}{{#eq ../FriendType "2"}}bg-gradient-to-br from-yellow-400 to-warning{{/eq}}{{#eq ../FriendType "4"}}bg-gradient-to-br from-yellow-400 to-warning{{/eq}}{{#eq ../FriendType "5"}}bg-gradient-to-br from-violet-300 to-violet-600{{/eq}} rounded-full flex items-center justify-center mr-4 flex-shrink-0 relative">
                    {{!-- 首字母文本，默认显示 --}}
                    <span class="text-white font-bold text-lg relative z-0" data-fallback="true">{{firstChar FriendNickname}}</span>
                    {{!-- 头像图片，默认隐藏，只有在成功加载后才显示 --}}
                    {{#if AvatarUrl}}
                        <img src="{{AvatarUrl}}"
                             alt="{{FriendNickname}}"
                             class="w-12 h-12 object-fill rounded-full absolute top-0 left-0 z-[1] hidden"
                             data-avatar-src="{{AvatarUrl}}"
                             onload="handleAvatarLoad(this)"
                             onerror="handleAvatarError(this)">
                    {{/if}}
                    {{!-- 在线状态圆点，只在在线时显示绿点 --}}
                    {{#if IsOnline}}
                    <div class="absolute bottom-0.5 right-0.5 w-3 h-3 rounded-full border-2 border-white bg-success z-[2]"></div>
                    {{/if}}
                </div>
                <div class="flex-1">
                    <div class="text-text-primary font-medium text-base cursor-pointer transition-colors duration-200 hover:text-primary" onclick="location.href='{{UserDetailUrl}}'">{{FriendNickname}}</div>
                    {{!-- 好友备注，只在备注存在时显示 --}}
                    {{#if FriendUserName}}
                    <p class="bg-primary-alpha-05 text-text-secondary text-xs py-1 px-2 rounded inline-block">{{FriendUserName}}</p>
                    {{/if}}
                    <div class="">
                        <span class="text-text-light text-[13px]">@{{FriendUserId}}</span>
                    </div>
                </div>
                <div class="flex items-center gap-2 self-center relative">
                    {{#eq ../FriendType "0"}}
                    <button class="inline-flex items-center justify-center p-1.5 rounded text-sm font-medium transition cursor-pointer select-none border border-transparent bg-transparent text-text-light hover:bg-border-light hover:text-text-secondary" data-dropdown-target="#itemOptionsDropdown-{{Id}}" data-item-id="{{Id}}">
                        <i data-lucide="more-vertical" class="w-4 h-4"></i>
                    </button>
                    <div id="itemOptionsDropdown-{{Id}}" class="absolute right-0 w-auto bg-white rounded-md shadow-lg border border-border-normal z-dropdown hidden dropdown-menu-smart">
                        <a href="/bbs/messagelist_add.aspx?touserid={{FriendUserId}}" class="dropdown-item send-message-dropdown">
                            <i data-lucide="message-circle" class="w-4 h-4 mr-2"></i> 发送私信
                        </a>
                        <a href="#" class="dropdown-item add-note-dropdown" data-item-id="{{Id}}" data-current-note="{{FriendUserName}}">
                            <i data-lucide="edit-3" class="w-4 h-4 mr-2"></i> 添加备注
                        </a>
                        <a href="{{DeleteUrl}}" class="dropdown-item delete-friend-link">
                            <i data-lucide="user-minus" class="w-4 h-4 mr-2"></i> 删除好友
                        </a>
                    </div>
                    {{/eq}}

                    {{#eq ../FriendType "1"}}
                    <button class="inline-flex items-center justify-center p-1.5 rounded text-sm font-medium transition cursor-pointer select-none border border-transparent bg-transparent text-text-light hover:bg-border-light hover:text-danger blacklist-remove-btn" data-item-id="{{Id}}" aria-label="移出黑名单">
                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                    </button>
                    {{/eq}}
                </div>
            </div>
            {{/each}}
            {{else}}
            <div class="text-center py-12 px-4 flex flex-col items-center justify-center">
                <div class="mb-4 w-20 h-20">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 500" fill="none" class="w-full h-full">
                        <circle cx="250" cy="250" r="200" fill="#F5F5F5"></circle>
                        <path d="M165 220C165 198.402 182.402 181 204 181H296C317.598 181 335 198.402 335 220V280C335 301.598 317.598 319 296 319H204C182.402 319 165 301.598 165 280V220Z" fill="white" stroke="#5EBCB0" stroke-width="4"></path>
                        <circle cx="190" cy="240" r="20" fill="#5EBCB0" opacity="0.7"></circle>
                        <circle cx="250" cy="240" r="20" fill="#5EBCB0" opacity="0.5"></circle>
                        <circle cx="310" cy="240" r="20" fill="#5EBCB0" opacity="0.3"></circle>
                        <path d="M170 340C170 329.507 178.507 321 189 321H311C321.493 321 330 329.507 330 340V340C330 350.493 321.493 359 311 359H189C178.507 359 170 350.493 170 340V340Z" fill="white" stroke="#5EBCB0" stroke-width="4"></path>
                        <path d="M190 160C190 149.507 198.507 141 209 141H291C301.493 141 310 149.507 310 160V160C310 170.493 301.493 179 291 179H209C198.507 179 190 170.493 190 160V160Z" fill="white" stroke="#5EBCB0" stroke-width="4"></path>
                        <path d="M130 250C130 239.507 138.507 231 149 231H161C171.493 231 180 239.507 180 250V250C180 260.493 171.493 269 161 269H149C138.507 269 130 260.493 130 250V250Z" fill="white" stroke="#5EBCB0" stroke-width="4"></path>
                        <path d="M320 250C320 239.507 328.507 231 339 231H351C361.493 231 370 239.507 370 250V250C370 260.493 361.493 269 351 269H339C328.507 269 320 260.493 320 250V250Z" fill="white" stroke="#5EBCB0" stroke-width="4"></path>
                    </svg>
                </div>
                <div class="text-lg font-medium text-text-primary mb-1">
                    {{#eq FriendType "0"}}这里还没有好友呢{{/eq}}
                    {{#eq FriendType "1"}}黑名单列表为空{{/eq}}
                    {{#eq FriendType "2"}}暂无追求对象{{/eq}}
                    {{#eq FriendType "4"}}暂无人追求您{{/eq}}
                    {{#eq FriendType "5"}}暂无推荐用户{{/eq}}
                </div>
                <div class="text-sm text-text-secondary">
                    {{#eq FriendType "0"}}快去寻找你的朋友吧{{/eq}}
                    {{#eq FriendType "1"}}您的黑名单很干净{{/eq}}
                    {{#eq FriendType "2"}}去寻找心仪的人吧{{/eq}}
                    {{#eq FriendType "4"}}还没有人对您表达爱意{{/eq}}
                    {{#eq FriendType "5"}}暂时没有推荐的用户{{/eq}}
                </div>
            </div>
            {{/if}}
        </div>

        {{#if Pagination.ShowPagination}}
        <div class="flex items-center justify-center gap-4 mt-4 mb-2">
            <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light" id="prevPageBtn" {{#if Pagination.IsFirstPage}}disabled{{/if}}>
                <i data-lucide="chevron-left" class="w-5 h-5"></i>
            </button>
            <div class="flex-1 text-center text-sm text-text-secondary px-2">
                第 {{Pagination.CurrentPage}} / {{Pagination.TotalPages}} 页
            </div>
            <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light" id="nextPageBtn" {{#if Pagination.IsLastPage}}disabled{{/if}}>
                <i data-lucide="chevron-right" class="w-5 h-5"></i>
            </button>
        </div>
        {{/if}}
    </div>
</div>

{{!-- 清空黑名单按钮 - 只在黑名单页面显示，放在卡片容器外 --}}
{{#eq FriendType "1"}}
{{#if HasOperations}}
{{#if FriendsList}}
<div class="mx-4 mt-4">
    <button class="w-full bg-danger text-white border border-danger rounded-md px-4 py-3 font-medium text-sm transition-all duration-200 hover:bg-danger-dark hover:border-danger-dark flex items-center justify-center" id="clear-blacklist-btn">
        <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
        清空黑名单
    </button>
</div>
{{/if}}
{{/if}}
{{/eq}}


</div>

<!-- 引入统一的头像处理模块 -->
<script src="/Template/JS/Components/AvatarHandler.js?v=1"></script>

<script>
// Handlebars 模板中的前端脚本

// Toast 通知处理函数
function closeToast(toastId) {
    const toast = document.getElementById(toastId);
    if (toast) {
        toast.classList.add('fade-out');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300); // 等待淡出动画完成
    }
}

function autoCloseToast(toastId, delay = 3000) {
    setTimeout(() => {
        closeToast(toastId);
    }, delay);
}

// 通用关闭所有下拉菜单的函数
function closeAllDropdowns() {
    document.querySelectorAll('.absolute.right-0.show').forEach(openDropdown => {
        openDropdown.classList.remove('show');
        openDropdown.classList.add('hidden');
        const openListItem = openDropdown.closest('.friend-item');
        if (openListItem) openListItem.classList.remove('dropdown-open');
    });
}

let currentItemTarget = null;

// 头像加载处理已移至 AvatarHandler.js 统一管理

// 自定义确认对话框函数
function showCustomConfirm(message, onConfirm) {
    const confirmDialogOverlay = document.createElement('div');
    confirmDialogOverlay.style.cssText = `
        position: fixed; inset: 0; background-color: rgba(0,0,0,0.5);
        display: flex; align-items: center; justify-content: center;
        z-index: 1030;
    `;

    const confirmDialogContent = document.createElement('div');
    confirmDialogContent.style.cssText = `
        background-color: white;
        border-radius: 12px;
        padding: 24px 20px 20px;
        width: 85%; max-width: 360px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        text-align: center;
    `;

    confirmDialogContent.innerHTML = `
        <h3 style="font-size: 18px; font-weight: 600; color: #1f2937; margin-bottom: 12px;">确认操作</h3>
        <p style="color: #6b7280; margin-bottom: 24px; font-size: 15px; line-height: 1.5;">${message}</p>
        <div style="display: flex; justify-content: center; gap: 12px;">
            <button class="custom-confirm-btn custom-confirm-delete" id="confirmCustomConfirm">确定</button>
            <button class="custom-confirm-btn custom-confirm-cancel" id="cancelCustomConfirm">取消</button>
        </div>
    `;

    confirmDialogOverlay.appendChild(confirmDialogContent);
    document.body.appendChild(confirmDialogOverlay);

    // 关闭对话框函数
    function closeDialog() {
        if (document.body.contains(confirmDialogOverlay)) {
            document.body.removeChild(confirmDialogOverlay);
        }
    }

    const cancelBtn = document.getElementById('cancelCustomConfirm');
    if(cancelBtn) cancelBtn.onclick = closeDialog;

    const confirmBtn = document.getElementById('confirmCustomConfirm');
    if(confirmBtn) confirmBtn.onclick = () => {
        onConfirm();
        closeDialog();
    };

    // 点击遮罩关闭 - 只有点击遮罩本身才关闭，点击内容区域不关闭
    confirmDialogOverlay.addEventListener('click', function(e) {
        if (e.target === confirmDialogOverlay) {
            closeDialog();
        }
    });
}

// 页面初始化时绑定事件
document.addEventListener('DOMContentLoaded', function() {
    // 初始化 Toast 自动关闭
    const errorToast = document.getElementById('errorToast');
    const infoToast = document.getElementById('infoToast');
    
    if (errorToast) {
        autoCloseToast('errorToast', 3000);
    }
    
    if (infoToast) {
        autoCloseToast('infoToast', 3000);
    }

    // 初始化 Lucide 图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // 头像处理已移至 AvatarHandler.js 自动管理

    // 绑定好友列表下拉菜单显示/隐藏事件
    document.querySelectorAll('[data-dropdown-target]').forEach(button => {
        button.addEventListener('click', function(event) {
            event.stopPropagation();
            const targetId = this.dataset.dropdownTarget;
            const dropdownMenu = document.querySelector(targetId);
            const listItem = this.closest('.friend-item');

            if (dropdownMenu) {
                const isCurrentlyOpen = dropdownMenu.classList.contains('show');

                closeAllDropdowns(); // 关闭其他已打开的下拉菜单

                if (!isCurrentlyOpen) {
                    dropdownMenu.classList.add('show');
                    dropdownMenu.classList.remove('hidden');
                    if (listItem) listItem.classList.add('dropdown-open');
                    currentItemTarget = { itemElement: listItem, dropdownElement: dropdownMenu, buttonElement: this };
                } else {
                     currentItemTarget = null;
                }
            }
        });
    });

    // 绑定好友列表删除操作事件
    document.querySelectorAll('.delete-friend-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault(); // 阻止默认链接跳转
            e.stopPropagation(); // 阻止事件冒泡到下拉菜单按钮

            const deleteUrl = this.href;
            const listItem = this.closest('.friend-item');
            const itemId = listItem ? listItem.querySelector('[data-dropdown-target]').dataset.itemId : null; // 好友ID，不是用户ID
            const itemName = listItem ? listItem.querySelector('.text-text-primary.font-medium').textContent : '此用户';

            if (deleteUrl && itemId) {
                 showCustomConfirm('确定要删除好友 ' + itemName + ' 吗？', function() {
                    // 构建正确的删除URL并异步提交
                    const finalDeleteUrl = `/bbs/friendlist_del.aspx?action=godel&id=${itemId}`; // 使用 action=godel 和好友ID
                    
                    // 执行异步删除操作
                    fetch(finalDeleteUrl, { method: 'GET' })
                    .then(res => res.text())
                    .then(html => {
                        if (html.includes('删除成功') || html.includes('Deleted successfully')) {
                            showToast('删除好友成功！', 'success');
                            // 从DOM中移除对应的列表项
                            if (listItem) {
                                listItem.remove();
                            }
                            // 更新右上角数字
                            updateItemCount();
                            // 如果没有更多列表项，刷新页面显示空状态
                            if (!document.querySelector('.friend-item')) {
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1000);
                            }
                        } else {
                            showToast('删除失败，请重试！', 'error');
                        }
                    })
                    .catch(() => {
                        showToast('删除失败，请重试！', 'error');
                    });
                 });
            }

            // 关闭下拉菜单
            const dropdownMenu = this.closest('.absolute.right-0');
            if (dropdownMenu) {
                dropdownMenu.classList.remove('show');
                dropdownMenu.classList.add('hidden');
            }
            if (listItem) listItem.classList.remove('dropdown-open');
            currentItemTarget = null;
        });
    });

    // 绑定黑名单列表移出操作事件
    document.querySelectorAll('.blacklist-remove-btn').forEach(button => {
        button.addEventListener('click', function(event) {
            event.preventDefault(); // ✅ 阻止默认行为
            event.stopPropagation(); // 阻止事件冒泡
            const listItem = this.closest('.friend-item');
            const itemId = this.dataset.itemId; // 黑名单ID
            const itemName = listItem ? listItem.querySelector('.text-text-primary.font-medium').textContent : '此用户';

            if (itemId) {
                showCustomConfirm('确定要将用户 ' + itemName + ' 移出黑名单吗？', function() {
                    // 构建正确的移出黑名单URL并异步提交
                    const finalRemoveUrl = `/bbs/friendlist_del.aspx?action=godel&id=${itemId}`; // 使用 action=godel 和黑名单ID
                    
                    // 执行异步移出操作
                    fetch(finalRemoveUrl, { method: 'GET' })
                    .then(res => res.text())
                    .then(html => {
                        if (html.includes('删除成功') || html.includes('Deleted successfully')) {
                            showToast('移出黑名单成功！', 'success');
                            // 从DOM中移除对应的列表项
                            if (listItem) {
                                listItem.remove();
                            }
                            // 更新右上角数字
                            updateItemCount();
                            // 如果没有更多列表项，刷新页面显示空状态
                            if (!document.querySelector('.friend-item')) {
                                setTimeout(() => {
                                    window.location.reload();
                                }, 1000);
                            }
                        } else {
                            showToast('移出失败，请重试！', 'error');
                        }
                    })
                    .catch(() => {
                        showToast('移出失败，请重试！', 'error');
                    });
                });
            }
        });
    });

    // 点击页面其他区域关闭下拉菜单
    document.addEventListener('click', function(event) {
        let clickedInsideDropdownRelated = false;
        if (currentItemTarget && currentItemTarget.dropdownElement && currentItemTarget.buttonElement) {
            if (currentItemTarget.dropdownElement.contains(event.target) ||
                currentItemTarget.buttonElement.contains(event.target)) {
                clickedInsideDropdownRelated = true;
            }
        }
        if (!clickedInsideDropdownRelated) {
            closeAllDropdowns();
            currentItemTarget = null;
        }
    });

    // 清空列表按钮事件处理
    document.querySelectorAll('#clear-blacklist-btn').forEach(button => {
        button.addEventListener('click', function(event) {
            event.stopPropagation();
            const currentFriendType = '{{FriendType}}'; // 从后端数据模型获取 FriendType
            let confirmMessage = '';
            let redirectUrl = ''; // 清空操作成功后跳转的 URL

            if (currentFriendType === '1') {
                confirmMessage = '确定要清空黑名单吗？此操作不可恢复。';
                redirectUrl = '/bbs/friendlist_del.aspx?action=godelall&friendtype=1';
            }

            if (confirmMessage && redirectUrl) {
                showCustomConfirm(confirmMessage, function() {
                    // 执行异步清空操作
                    button.disabled = true;
                    button.style.opacity = '0.5';
                    
                    fetch(redirectUrl, { method: 'GET' })
                    .then(res => res.text())
                    .then(html => {
                        if (html.includes('删除成功') || html.includes('Deleted successfully')) {
                            showToast('清空成功！');
                            
                            // 直接隐藏数字显示和清空按钮
                            const countDisplayElement = document.getElementById('friend-count-display');
                            const clearButton = document.getElementById('clear-blacklist-btn');
                            
                            if (countDisplayElement) {
                                countDisplayElement.style.display = 'none';
                            }
                            if (clearButton) {
                                clearButton.style.display = 'none';
                            }
                            
                            // 刷新页面以显示空状态
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            showToast('操作失败，请重试！');
                        }
                    })
                    .catch(() => {
                        showToast('操作失败，请重试！');
                    })
                    .finally(() => {
                        button.disabled = false;
                        button.style.opacity = '';
                    });
                });
            } else {
                alert('清空功能暂未实现或配置错误。');
            }
        });
    });

    // 私信按钮事件处理 - 改为异步提交
    document.querySelectorAll('.send-message-dropdown').forEach(link => {
        link.addEventListener('click', function(e) {
             e.preventDefault();
             e.stopPropagation();
             
             // 从链接URL中提取收信人ID
             const messageUrl = this.href;
             const urlParams = new URLSearchParams(messageUrl.split('?')[1]);
             const touserid = urlParams.get('touserid');
             
             if (touserid) {
                 showSendMessageModal(touserid);
             } else {
                 showToast('无法获取收信人信息', 'error');
             }
             
             // 关闭下拉菜单
             const dropdownMenu = this.closest('.absolute.right-0');
             if (dropdownMenu) {
                 dropdownMenu.classList.remove('show');
                 dropdownMenu.classList.add('hidden');
             }
             const listItem = this.closest('.friend-item');
             if (listItem) listItem.classList.remove('dropdown-open');
             currentItemTarget = null;
        });
    });

    // 添加备注按钮事件处理
    document.querySelectorAll('.add-note-dropdown').forEach(link => {
        link.addEventListener('click', function(e) {
             e.preventDefault();
             e.stopPropagation();
             
             const itemId = this.dataset.itemId;
             const currentNote = this.dataset.currentNote || '';
             
             if (itemId) {
                 showAddNoteModal(itemId, currentNote);
             } else {
                 showToast('无法获取好友信息', 'error');
             }
             
             // 关闭下拉菜单
             const dropdownMenu = this.closest('.absolute.right-0');
             if (dropdownMenu) {
                 dropdownMenu.classList.remove('show');
                 dropdownMenu.classList.add('hidden');
             }
             const listItem = this.closest('.friend-item');
             if (listItem) listItem.classList.remove('dropdown-open');
             currentItemTarget = null;
        });
    });

    // 分页按钮事件处理
    const prevPageBtn = document.getElementById('prevPageBtn');
    const nextPageBtn = document.getElementById('nextPageBtn');
    
    if (prevPageBtn) {
        prevPageBtn.addEventListener('click', function() {
            if (!this.disabled) {
                const currentPage = {{Pagination.CurrentPage}};
                if (currentPage > 1) {
                    const newPage = currentPage - 1;
                    navigateToPage(newPage);
                }
            }
        });
    }
    
    if (nextPageBtn) {
        nextPageBtn.addEventListener('click', function() {
            if (!this.disabled) {
                const currentPage = {{Pagination.CurrentPage}};
                const totalPages = {{Pagination.TotalPages}};
                if (currentPage < totalPages) {
                    const newPage = currentPage + 1;
                    navigateToPage(newPage);
                }
            }
        });
    }

}); // DOMContentLoaded end

// 分页导航函数
function navigateToPage(page) {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('page', page);
    window.location.href = currentUrl.toString();
}

// URL参数更新函数 (已经存在，保留)
// function updateUrlParameter(url, param, paramVal) { ... }

// 删除确认函数 (旧版逻辑，已替换为 showCustomConfirm，可以移除)
// function confirmDelete(friendId, friendName) { ... }

// 搜索功能 (已经存在，保留)
// function performSearch() { ... }
// 绑定搜索事件 (已经存在，保留)
// document.addEventListener('DOMContentLoaded', function() { ... });

// 显示简约Toast提示
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.textContent = message;
    toast.className = 'fixed bottom-5 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 text-white py-2.5 px-5 rounded-lg z-toast shadow-lg transition-all duration-300 opacity-0';

    // 设置初始位置（向下偏移20px）
    toast.style.transform = 'translate(-50%, 20px)';
    document.body.appendChild(toast);

    // 显示动画（淡入+上滑）
    setTimeout(() => {
        toast.classList.remove('opacity-0');
        toast.classList.add('opacity-100');
        toast.style.transform = 'translate(-50%, 0)';
    }, 10);

    // 隐藏动画（淡出+下滑）
    setTimeout(() => {
        toast.classList.remove('opacity-100');
        toast.classList.add('opacity-0');
        toast.style.transform = 'translate(-50%, 20px)';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 2000);
}

// 发送私信弹窗
function showSendMessageModal(touserid) {
    // 创建弹窗HTML
    const modalHtml = `
        <div id="send-msg-modal" class="fixed inset-0 z-modal flex items-center justify-center">
            <div class="absolute inset-0 bg-black/50"></div>
            <div class="relative bg-white rounded-md shadow-[0_10px_25px_rgba(0,0,0,0.15)] w-[90%] max-w-[360px] max-h-[80vh] overflow-hidden">
                <div class="flex items-center justify-between px-5 pt-5 pb-3">
                    <h3 class="text-lg font-semibold text-text-primary m-0">发送私信</h3>
                    <button class="bg-transparent border-none text-2xl text-text-light cursor-pointer p-0 w-8 h-8 flex items-center justify-center rounded-md transition-colors duration-200 hover:bg-bg-gray-100 hover:text-text-secondary" id="send-msg-close">×</button>
                </div>
                <form id="send-msg-form">
                    <div class="px-5 pb-4">
                        <div class="mb-3">
                            <input type="number" name="touseridlist" id="send-msg-touserid" placeholder="收信人ID" readonly value="${touserid}" class="w-full px-3 py-2 border border-border-normal rounded text-sm transition-all duration-200 bg-gray-50 text-gray-600 cursor-not-allowed" maxlength="9" inputmode="numeric" min="1" max="999999999">
                        </div>
                        <div class="mb-0">
                            <textarea name="content" id="send-msg-content" placeholder="请输入消息内容..." required rows="4" class="w-full px-3 py-2 border border-border-normal rounded text-sm transition-all duration-200 focus:outline-none focus:border-primary focus:shadow-[0_0_0_3px_rgba(88,180,176,0.1)] resize-none"></textarea>
                        </div>
                    </div>
                    <div class="flex gap-3 px-5 pb-5">
                        <button type="submit" class="flex-1 px-4 py-2 rounded text-sm font-medium transition cursor-pointer select-none border border-transparent bg-primary text-white hover:bg-primary-dark">发送</button>
                        <button type="button" class="flex-1 px-4 py-2 rounded text-sm font-medium transition cursor-pointer select-none border border-border-normal bg-transparent text-text-secondary hover:bg-bg-gray-50" id="send-msg-cancel">取消</button>
                    </div>
                </form>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    const modal = document.getElementById('send-msg-modal');
    const form = document.getElementById('send-msg-form');
    const closeBtn = document.getElementById('send-msg-close');
    const cancelBtn = document.getElementById('send-msg-cancel');
    const overlay = modal.querySelector('.absolute.inset-0');
    const contentTextarea = document.getElementById('send-msg-content');

    // 关闭弹窗函数
    function closeModal() {
        if (modal && document.body.contains(modal)) {
            document.body.removeChild(modal);
        }
    }

    // 绑定事件
    if (closeBtn) closeBtn.addEventListener('click', closeModal);
    if (cancelBtn) cancelBtn.addEventListener('click', closeModal);

    // 点击遮罩关闭
    if (overlay) {
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) closeModal();
        });
    }

    // 聚焦到文本框
    if (contentTextarea) contentTextarea.focus();

    // 表单提交处理
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(form);
            formData.append('action', 'gomod');
            formData.append('ajax', '1');

            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.textContent = '发送中...';
            }

            fetch('/bbs/messagelist_add.aspx', {
                method: 'POST',
                body: formData
            })
            .then(res => res.text())
            .then(code => {
                code = code.trim();
                if (code === 'OK') {
                    showToast('发送成功');
                    closeModal();
                } else if (code === 'REPEAT') {
                    showToast('内容重复，请修改后再发');
                } else if (code === 'NULL') {
                    showToast('请完整填写表单');
                } else if (code === 'WAITING') {
                    showToast('操作过快，请稍后再试');
                } else if (code === 'PWERROR') {
                    showToast('密码错误，请重新输入');
                } else if (code === 'MAX1' || code === 'MAX') {
                    showToast('已达今日发信上限');
                } else if (code === 'LOCK') {
                    showToast('你已被加入黑名单');
                } else if (code === 'CANTSELF') {
                    showToast('不能给自己发消息');
                } else if (code === 'NOTEXSIT') {
                    showToast('用户ID不存在');
                } else {
                    showToast('发送失败，请重试');
                }
            })
            .catch(() => {
                showToast('发送失败，请重试');
            })
            .finally(() => {
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.textContent = '发送';
                }
            });
        });
    }
}

// 动态创建添加备注弹窗
function showAddNoteModal(itemId, currentNote) {
    const modalOverlay = document.createElement('div');
    modalOverlay.style.cssText = `
        position: fixed; inset: 0; background-color: rgba(0,0,0,0.5);
        display: flex; align-items: center; justify-content: center;
        z-index: 1000;
    `;

    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background-color: white;
        border-radius: 12px;
        width: 90%; max-width: 360px;
        max-height: 80vh;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    `;

    modalContent.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: space-between; padding: 20px 20px 12px;">
            <h3 style="font-size: 18px; font-weight: 600; color: #1f2937; margin: 0;">修改备注</h3>
            <button id="add-note-close" style="background: transparent; border: none; font-size: 24px; color: #9ca3af; cursor: pointer; padding: 0; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; border-radius: 6px; transition: all 0.2s;">×</button>
        </div>
        <form id="add-note-form">
            <div style="padding: 0 20px 16px;">
                <div style="margin-bottom: 0;">
                    <input type="text" name="remark" id="add-note-remark" placeholder="请输入备注（最多8个字符，留空则清空）" maxlength="8" value="${currentNote}" style="width: 100%; padding: 12px; border: 1px solid #e5e7eb; border-radius: 6px; font-size: 14px; transition: all 0.2s; outline: none;">
                </div>
                <input type="hidden" name="action" value="gomod">
                <input type="hidden" name="classid" value="{{ClassId}}">
                <input type="hidden" name="siteid" value="{{SiteId}}">
                <input type="hidden" name="id" value="${itemId}">
                <input type="hidden" name="page" value="{{Pagination.CurrentPage}}">
                <input type="hidden" name="backurl" value="{{BackUrl}}">
                <input type="hidden" name="friendtype" value="{{FriendType}}">
            </div>
            <div style="display: flex; gap: 12px; padding: 0 20px 20px;">
                <button type="submit" style="flex: 1; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; transition: all 0.2s; cursor: pointer; border: none; background-color: #58b4b0; color: white;">保存</button>
                <button type="button" id="add-note-cancel" style="flex: 1; padding: 8px 16px; border-radius: 6px; font-size: 14px; font-weight: 500; transition: all 0.2s; cursor: pointer; border: 1px solid #e5e7eb; background-color: transparent; color: #6b7280;">取消</button>
            </div>
        </form>
    `;

    modalOverlay.appendChild(modalContent);
    document.body.appendChild(modalOverlay);

    // 关闭弹窗函数
    function closeModal() {
        if (document.body.contains(modalOverlay)) {
            document.body.removeChild(modalOverlay);
        }
    }

    // 绑定事件
    const closeBtn = modalContent.querySelector('#add-note-close');
    const cancelBtn = modalContent.querySelector('#add-note-cancel');
    const form = modalContent.querySelector('#add-note-form');
    const remarkInput = modalContent.querySelector('#add-note-remark');

    if (closeBtn) closeBtn.onclick = closeModal;
    if (cancelBtn) cancelBtn.onclick = closeModal;
    
    // 点击遮罩关闭
    modalOverlay.addEventListener('click', function(e) {
        if (e.target === modalOverlay) closeModal();
    });

    // 聚焦到输入框并选中文本
    if (remarkInput) {
        remarkInput.focus();
        remarkInput.select();
    }

    // 表单提交处理
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(form);
            const remarkValue = remarkInput.value;
            
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.textContent = '保存中...';
            }
            
            fetch('/bbs/friendlist_mod.aspx', {
                method: 'POST',
                body: formData
            })
            .then(res => res.text())
            .then(html => {
                if (html.includes('修改备注成功') || html.includes('保存成功')) {
                    if (remarkValue.trim()) {
                        showToast('备注保存成功！', 'success');
                    } else {
                        showToast('备注已清空！', 'success');
                    }
                    
                    // 更新页面上的备注显示
                    updateNoteDisplay(itemId, remarkValue);
                    closeModal();
                } else if (html.includes('REPEAT')) {
                    showToast('备注名称重复，请修改后再保存', 'error');
                } else if (html.includes('NULL')) {
                    showToast('请填写完整', 'error');
                } else if (html.includes('WAITING')) {
                    showToast('操作过快，请稍后再试', 'error');
                } else if (html.includes('LOCK')) {
                    showToast('你已被加入黑名单', 'error');
                } else {
                    showToast('保存失败，请重试', 'error');
                }
            })
            .catch(() => {
                showToast('保存失败，请重试', 'error');
            })
            .finally(() => {
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.textContent = '保存';
                }
            });
        });
    }
}

// 更新页面上的备注显示
function updateNoteDisplay(itemId, newNote) {
    const items = document.querySelectorAll('.friend-item');
    items.forEach(item => {
        const dropdownButton = item.querySelector('[data-dropdown-target]');
        if (dropdownButton && dropdownButton.dataset.itemId === itemId) {
            const nicknameSpan = item.querySelector('.text-text-primary.font-medium');
            if (nicknameSpan) {
                const currentText = nicknameSpan.textContent;
                const baseNickname = currentText.split('(')[0].trim(); // 移除现有备注
                
                if (newNote && newNote.trim()) {
                    nicknameSpan.textContent = `${baseNickname}(${newNote.trim()})`;
                } else {
                    nicknameSpan.textContent = baseNickname;
                }
            }
            
            // 更新下拉菜单中的备注按钮数据
            const addNoteBtn = item.querySelector('.add-note-dropdown');
            if (addNoteBtn) {
                addNoteBtn.dataset.currentNote = newNote || '';
            }
        }
    });
}

// 更新右上角数字显示
function updateItemCount() {
    const currentItems = document.querySelectorAll('.friend-item');
    const countDisplayElement = document.getElementById('friend-count-display');
    
    if (countDisplayElement) {
        const newCount = currentItems.length;
        
        if (newCount > 0) {
            const countSpan = countDisplayElement.querySelector('span');
            if (countSpan) {
                countSpan.textContent = newCount.toString();
            }
        } else {
            // 没有项目时隐藏数字显示
            countDisplayElement.style.display = 'none';
        }
    }
}

// 黑名单帮助弹窗
function showBlacklistHelp() {
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'fixed inset-0 z-modal flex items-center justify-center';
    modalOverlay.style.zIndex = '1000';

    const bgOverlay = document.createElement('div');
    bgOverlay.className = 'absolute inset-0 bg-black/50 blacklist-help-overlay';

    const modalContent = document.createElement('div');
    modalContent.className = 'relative bg-white rounded-md shadow-[0_10px_25px_rgba(0,0,0,0.15)] w-[90%] max-w-[300px] max-h-[80vh] overflow-hidden';

    modalContent.innerHTML = `
        <div class="grid grid-cols-3 items-center px-4 pt-4 pb-3 border-b border-border-light">
            <span></span>
            <h3 class="text-lg font-semibold text-text-primary m-0 text-center whitespace-nowrap">黑名单说明</h3>
            <button class="bg-transparent border-none text-2xl text-text-light cursor-pointer p-0 w-8 h-8 flex items-center justify-center rounded-md transition-colors duration-200 hover:bg-bg-gray-100 hover:text-text-secondary justify-self-end" id="blacklist-help-close">
                <i data-lucide="x" class="w-5 h-5"></i>
            </button>
        </div>
        <div class="px-4 py-3">
            <ul class="space-y-2 list-none px-[5px] pt-[5px] pb-0 m-0 text-[15px] text-text-tertiary leading-[2.2]">
                <li class="custom-bullet">黑名单用户无法向您发送私信</li>
                <li class="custom-bullet">浏览时不会看到对方发的内容</li>
            </ul>
        </div>
        <div class="flex justify-center px-4 pb-4 pt-3">
            <button type="button" class="max-w-[200px] w-full px-6 py-2.5 rounded text-[15px] font-medium transition cursor-pointer select-none border border-transparent bg-primary text-white hover:bg-primary-dark" id="blacklist-help-ok">我知道了</button>
        </div>
    `;

    modalOverlay.appendChild(bgOverlay);
    modalOverlay.appendChild(modalContent);
    document.body.appendChild(modalOverlay);

    // 关闭弹窗函数
    function closeModal() {
        if (document.body.contains(modalOverlay)) {
            document.body.removeChild(modalOverlay);
        }
    }

    // 绑定事件
    const closeBtn = modalContent.querySelector('#blacklist-help-close');
    const okBtn = modalContent.querySelector('#blacklist-help-ok');

    if (closeBtn) closeBtn.onclick = closeModal;
    if (okBtn) okBtn.onclick = closeModal;
    
    // 点击遮罩关闭
    bgOverlay.addEventListener('click', closeModal);

    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
}
</script> 