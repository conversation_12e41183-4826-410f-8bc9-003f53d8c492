using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.Template.Models;
using YaoHuo.Plugin.BBS.Models;

namespace YaoHuo.Plugin.BBS
{
    public class BankList : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string linkURL = "";

        public string condition = "";

        public string ERROR = "";

        public string key = "";

        public string friendtype = "";

        public string backurl = "";

        public string linkTOP = "";

        public string toyear = DateTime.Now.Year.ToString();

        public string tomonth = DateTime.Now.Month.ToString();

        public bool isadmin = false;

        public string typeid = "";

        public string typekey = "";

        public List<wap_bankLog_Model> listVo = null;

        public long long_0 = 1L;

        public long index = 0L;

        public long total = 0L;

        public long pageSize = 10L;

        public long CurrentPage = 1L;

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            isadmin = IsUserManager(userid, userVo.managerlvl, "");
            IsLogin(userid, backurl);

            // 检查UI偏好并尝试渲染新版UI
            if (CheckAndHandleUIPreference())
            {
                return; // 如果成功渲染新版UI，则直接返回
            }

            // 继续执行原有逻辑
            switch (action)
            {
                case "class":
                    showclass();
                    break;
                default:
                    showclass();
                    break;
                case "godel":
                    break;
            }
        }

        private bool CheckAndHandleUIPreference()
        {
            try
            {
                string viewMode = TemplateService.GetViewMode();
                if (viewMode == "new")
                {
                    return TryRenderWithHandlebars();
                }
            }
            catch (System.Threading.ThreadAbortException)
            {
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"BankList新版UI渲染检查失败: {ex.Message}");
            }
            return false;
        }

        private bool TryRenderWithHandlebars()
        {
            try
            {
                PrepareDataForHandlebars();
                var pageModel = BuildBankListPageModel();
                RenderWithHandlebars(pageModel);
                return true;
            }
            catch (System.Threading.ThreadAbortException)
            {
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"BankList Handlebars渲染失败: {ex.Message}");
                return false;
            }
        }

        private void PrepareDataForHandlebars()
        {
            if (string.IsNullOrEmpty(action))
            {
                action = "class";
            }
            showclass();
        }

        /// <summary>
        /// 构建BankList页面数据模型
        /// </summary>
        /// <returns>BankListPageModel实例</returns>
        private BankListPageModel BuildBankListPageModel()
        {
            var model = new BankListPageModel
            {
                PageTitle = "账目明细",
                IsAdmin = isadmin,
                ShowStats = false
            };

            // 构建站点信息
            model.SiteInfo = new SiteInfoModel
            {
                SiteId = siteid,
                ClassId = classid,
                HttpStart = http_start,
                BackUrl = backurl
            };

            // 构建筛选条件
            model.Filter = BuildFilterModel();

            // 构建账目记录列表
            model.BankLogList = BuildBankLogItemList();

            // 构建分页信息
            model.Pagination = BuildPaginationModel();

            // 构建消息模型
            if (!string.IsNullOrEmpty(ERROR))
            {
                model.Message.HasMessage = true;
                model.Message.Type = "error";
                model.Message.Content = ERROR;
                model.Message.IsSuccess = false;
            }

            // 构建隐藏字段
            model.HiddenFields.Action = "gomod";
            model.HiddenFields.SiteId = siteid;
            model.HiddenFields.ClassId = classid;
            model.HiddenFields.BackUrl = backurl;

            return model;
        }

        /// <summary>
        /// 构建筛选条件模型
        /// </summary>
        /// <returns>FilterModel实例</returns>
        private FilterModel BuildFilterModel()
        {
            var filter = new FilterModel
            {
                ToYear = toyear,
                ToMonth = tomonth,
                Key = key,
                TypeId = typeid,
                TypeKey = typekey
            };

            // 构建年份选项（最近三年）
            var currentYear = DateTime.Now.Year;
            for (int year = currentYear; year >= currentYear - 2; year--)
            {
                filter.YearOptions.Add(new OptionItem
                {
                    Value = year.ToString(),
                    Text = year + "年",
                    Selected = year.ToString() == toyear
                });
            }

            // 构建月份选项
            for (int month = 1; month <= 12; month++)
            {
                filter.MonthOptions.Add(new OptionItem
                {
                    Value = month.ToString(),
                    Text = month + "月",
                    Selected = month.ToString() == tomonth
                });
            }

            // 设置搜索类型选中状态
            foreach (var option in filter.SearchTypeOptions)
            {
                option.Selected = option.Value == typeid;
            }

            return filter;
        }

        /// <summary>
        /// 构建账目记录列表模型
        /// </summary>
        /// <returns>BankLogItemModel列表</returns>
        private List<BankLogItemModel> BuildBankLogItemList()
        {
            var result = new List<BankLogItemModel>();

            if (listVo != null && listVo.Count > 0)
            {
                foreach (var item in listVo)
                {
                    result.Add(new BankLogItemModel
                    {
                        Id = item.id,
                        ActionName = item.actionName ?? "",
                        Money = item.money.ToString(),
                        LeftMoney = item.leftMoney.ToString(),
                        OperaUserId = item.opera_userid.ToString(),
                        OperaNickname = item.opera_nickname ?? "",
                        AddTime = item.addtime,
                        Remark = item.remark ?? ""
                    });
                }
            }

            return result;
        }

        /// <summary>
        /// 构建分页信息模型
        /// </summary>
        /// <returns>PaginationModel实例</returns>
        private YaoHuo.Plugin.Template.Models.PaginationModel BuildPaginationModel()
        {
            var pagination = new YaoHuo.Plugin.Template.Models.PaginationModel
            {
                CurrentPage = (int)CurrentPage,
                TotalPages = (int)Math.Ceiling((double)total / pageSize),
                TotalItems = (int)total,
                PageSize = (int)pageSize,
                HasPages = total > pageSize,
                ShowPagination = total > pageSize
            };

            pagination.IsFirstPage = pagination.CurrentPage <= 1;
            pagination.IsLastPage = pagination.CurrentPage >= pagination.TotalPages;

            return pagination;
        }

        /// <summary>
        /// 使用Handlebars渲染页面
        /// </summary>
        /// <param name="pageModel">页面数据模型</param>
        private void RenderWithHandlebars(BankListPageModel pageModel)
        {
            // 构建头部选项
            var headerOptions = new HeaderOptionsModel
            {
                ShowViewModeToggle = false,
                CustomButtons = new List<HeaderButtonModel>
                {
                    new HeaderButtonModel
                    {
                        Id = "stats",
                        Icon = "bar-chart-3",
                        OnClick = "toggleStats()",
                        Tooltip = "统计信息"
                    }
                }
            };

            // 渲染页面
            string html = TemplateService.RenderPageWithLayout(
                "~/Template/Pages/BankList.hbs",
                pageModel,
                pageModel.PageTitle,
                headerOptions
            );

            // 输出HTML并结束响应
            Response.ContentType = "text/html";
            Response.Write(html);
            Response.End();
        }

        public void showclass()
        {
            key = GetRequestValue("key");
            typeid = GetRequestValue("typeid");
            typekey = GetRequestValue("typekey");

            // 验证和清理输入
            if (!ValidateAndCleanInputs())
            {
                ERROR = "输入参数不合法";
                return;
            }

            if (action == "search")
            {
                toyear = GetRequestValue("toyear");
                tomonth = GetRequestValue("tomonth");

                // 验证年月输入
                if (!ValidateYearMonth(toyear, tomonth))
                {
                    ERROR = "年份或月份参数不合法";
                    return;
                }
            }
            if (action == "mod")
            {
                ShowTipInfo("确定删除吗？<a href=\"" + http_start + "bbs/banklist.aspx?action=gomod&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;key=" + HttpUtility.UrlEncode(key) + "&amp;toyear=" + toyear + "&amp;tomonth=" + tomonth + "&amp;backurl=" + HttpUtility.UrlEncode(backurl) + " \">确定</a>", "bbs/banklist.aspx?action=gomod&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;key=" + HttpUtility.UrlEncode(key) + "&amp;toyear=" + toyear + "&amp;tomonth=" + tomonth + "&amp;backurl=" + HttpUtility.UrlEncode(backurl));
            }
            else if (action == "gomod")
            {
                // 验证管理员权限
                if (!isadmin)
                {
                    ShowTipInfo("权限不足", backurl);
                    return;
                }

                try
                {
                    // 记录操作日志
                    LogAdminOperation("删除历史银行记录", $"删除{DateTime.Now.Year}年{DateTime.Now.Month}月之前的记录");

                    // 使用安全的参数化查询删除历史记录
                    DeleteOldRecordsSafely(siteid, DateTime.Now.Year, DateTime.Now.Month);

                    ShowTipInfo("历史记录删除成功", backurl);
                }
                catch (Exception ex)
                {
                    ERROR = "删除操作失败：" + ex.Message;
                    System.Diagnostics.Debug.WriteLine($"删除银行记录失败: {ex.Message}");
                }
            }
            // ✅ 使用QueryBuilder和PaginationHelper进行安全的分页查询
            try
            {
                pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
                string connectionString = PubConstant.GetConnectionString(string_10);

                // 构建查询条件
                var queryBuilder = BuildSafeQueryBuilder(siteid, key, userid, isadmin, typekey, typeid, toyear, tomonth);

                // 处理分页参数
                if (GetRequestValue("page") != "")
                {
                    CurrentPage = long.Parse(GetRequestValue("page"));
                }

                // ✅ 使用PaginationHelper执行分页查询 - 一行代码完成所有分页逻辑
                var result = PaginationHelper.GetPagedDataWithBuilder<wap_bankLog_Model>(
                    connectionString,
                    "SELECT *",
                    "wap_bankLog",
                    queryBuilder,
                    (int)CurrentPage,
                    (int)pageSize,
                    "ORDER BY id DESC"
                );

                // 设置结果
                listVo = result.Data;
                total = result.Total;
                CurrentPage = result.Page;
                index = pageSize * (CurrentPage - 1L);

                // 构建分页链接（保持与原有逻辑兼容）
                linkURL = http_start + "bbs/banklist.aspx?action=search&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;typeid=" + HttpUtility.UrlEncode(typeid) + "&amp;typekey=" + HttpUtility.UrlEncode(typekey) + "&amp;key=" + HttpUtility.UrlEncode(key) + "&amp;toyear=" + toyear + "&amp;tomonth=" + tomonth + "&amp;backurl=" + HttpUtility.UrlEncode(backurl) + "&amp;getTotal=" + total;
                linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, total, pageSize, CurrentPage, linkURL);
                linkURL = WapTool.GetPageLink(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL);
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }

        #region 安全的数据库操作方法

        /// <summary>
        /// 安全地删除历史记录
        /// </summary>
        private void DeleteOldRecordsSafely(string siteId, int year, int month)
        {
            string sql = @"DELETE FROM wap_bankLog
                           WHERE siteid = @SiteId
                           AND addtime < @CutoffDate";

            var cutoffDate = new DateTime(year, month, 1);
            var parameters = new List<SqlParameter>
            {
                new SqlParameter("@SiteId", SqlDbType.NVarChar, 50) { Value = siteId },
                new SqlParameter("@CutoffDate", SqlDbType.DateTime) { Value = cutoffDate }
            };

            ExecuteParameterizedQuery(sql, parameters);
        }

        /// <summary>
        /// 构建安全的查询参数（使用QueryBuilder - 简化版）
        /// </summary>
        private QueryBuilder BuildSafeQueryBuilder(
            string siteId, string key, string userId, bool isAdmin,
            string typekey, string typeid, string toyear, string tomonth)
        {
            var queryBuilder = new QueryBuilder()
                .Where("siteid = @ParamN", DapperHelper.SafeParseLong(siteId, "站点ID"));

            // 用户ID条件
            if (isAdmin)
            {
                // 管理员可以搜索指定用户ID，留空表示搜索所有用户
                if (!string.IsNullOrEmpty(key) && key.Trim() != "0" && WapTool.IsNumeric(key))
                {
                    queryBuilder.Where("userid = @ParamN", DapperHelper.SafeParseLong(key, "用户ID"));
                }
                // 如果key为空或为"0"，不添加userid条件，表示搜索所有用户
            }
            else
            {
                // 普通用户只能查看自己的记录
                queryBuilder.Where("userid = @ParamN", DapperHelper.SafeParseLong(userId, "用户ID"));
            }

            // 搜索条件
            if (!string.IsNullOrEmpty(typekey) && !string.IsNullOrEmpty(typeid) && ValidateSearchInput(typekey, typeid))
            {
                switch (typeid)
                {
                    case "1": // 项目名称
                        queryBuilder.Where("actionname LIKE @ParamN", $"%{typekey}%");
                        break;
                    case "2": // 操作人ID
                        if (WapTool.IsNumeric(typekey))
                        {
                            queryBuilder.Where("opera_userid = @ParamN", DapperHelper.SafeParseLong(typekey, "操作人ID"));
                        }
                        break;
                    case "3": // 操作人昵称
                        queryBuilder.Where("opera_nickname LIKE @ParamN", $"%{typekey}%");
                        break;
                    case "4": // 备注
                        queryBuilder.Where("remark LIKE @ParamN", $"%{typekey}%");
                        break;
                    case "5": // 记录ID
                        if (WapTool.IsNumeric(typekey))
                        {
                            queryBuilder.Where("id = @ParamN", DapperHelper.SafeParseLong(typekey, "记录ID"));
                        }
                        break;
                }
            }

            // 时间条件
            if (WapTool.IsNumeric(toyear))
            {
                int year = int.Parse(toyear);
                queryBuilder.WhereIf(year >= 2000 && year <= DateTime.Now.Year + 1,
                                    "YEAR(addtime) = @ParamN", year);
            }

            if (WapTool.IsNumeric(tomonth))
            {
                int month = int.Parse(tomonth);
                queryBuilder.WhereIf(month >= 1 && month <= 12,
                                    "MONTH(addtime) = @ParamN", month);
            }

            return queryBuilder;
        }



        /// <summary>
        /// 验证搜索输入
        /// </summary>
        private bool ValidateSearchInput(string typekey, string typeid)
        {
            if (string.IsNullOrEmpty(typekey))
                return true;

            // 长度验证
            if (typekey.Length > 50)
                return false;

            // XSS防护
            var dangerousChars = new[] { '<', '>', '"', '&', ';', '(', ')', '{', '}' };
            if (typekey.Any(c => dangerousChars.Contains(c)))
                return false;

            // 验证typeid
            var validTypeIds = new[] { "1", "2", "3", "4", "5" };
            if (!string.IsNullOrEmpty(typeid) && !validTypeIds.Contains(typeid))
                return false;

            return true;
        }

        /// <summary>
        /// 验证和清理输入参数
        /// </summary>
        private bool ValidateAndCleanInputs()
        {
            // 验证key（用户ID）
            if (!string.IsNullOrEmpty(key) && key != "0")
            {
                if (!WapTool.IsNumeric(key) || key.Length > 20)
                {
                    return false;
                }
            }

            // 验证typeid
            if (!string.IsNullOrEmpty(typeid))
            {
                var validTypeIds = new[] { "1", "2", "3", "4", "5" };
                if (!validTypeIds.Contains(typeid))
                {
                    return false;
                }
            }

            // 验证typekey
            if (!string.IsNullOrEmpty(typekey))
            {
                if (typekey.Length > 50)
                {
                    return false;
                }

                // XSS防护
                var dangerousChars = new[] { '<', '>', '"', '\'', '&', ';', '(', ')', '{', '}', '[', ']' };
                if (typekey.Any(c => dangerousChars.Contains(c)))
                {
                    return false;
                }

                // 清理typekey
                typekey = typekey.Trim();
            }

            return true;
        }

        /// <summary>
        /// 验证年月输入
        /// </summary>
        private bool ValidateYearMonth(string year, string month)
        {
            if (!string.IsNullOrEmpty(year))
            {
                if (!WapTool.IsNumeric(year))
                    return false;

                int yearValue = int.Parse(year);
                if (yearValue < 2000 || yearValue > DateTime.Now.Year + 1)
                    return false;
            }

            if (!string.IsNullOrEmpty(month))
            {
                if (!WapTool.IsNumeric(month))
                    return false;

                int monthValue = int.Parse(month);
                if (monthValue < 1 || monthValue > 12)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 执行参数化查询
        /// </summary>
        private void ExecuteParameterizedQuery(string sql, List<SqlParameter> parameters)
        {
            string connectionString = PubConstant.GetConnectionString(string_10);
            using (var connection = new SqlConnection(connectionString))
            {
                using (var command = new SqlCommand(sql, connection))
                {
                    if (parameters != null)
                    {
                        command.Parameters.AddRange(parameters.ToArray());
                    }
                    connection.Open();
                    command.ExecuteNonQuery();
                }
            }
        }

        /// <summary>
        /// 记录管理员操作日志
        /// </summary>
        private void LogAdminOperation(string operation, string details)
        {
            try
            {
                string logMessage = $"管理员操作 - 用户ID: {userid}, 操作: {operation}, 详情: {details}, 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}, IP: {IP}";
                System.Diagnostics.Debug.WriteLine(logMessage);

                // 可以在这里添加更完善的日志记录机制
                // 比如写入专门的操作日志表
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"记录操作日志失败: {ex.Message}");
            }
        }

        #endregion
    }
}