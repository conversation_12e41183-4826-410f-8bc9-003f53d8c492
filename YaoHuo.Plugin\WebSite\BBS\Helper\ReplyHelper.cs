﻿using System.Collections.Generic;
using System.Linq;
using System.Web;
using KeLin.ClassManager.Model;
using KeLin.ClassManager.BLL;
using YaoHuo.Plugin.WebSite.Tool;
using KeLin.ClassManager;

namespace YaoHuo.Plugin.Tool
{
    /// <summary>
    /// 回复数据相关的静态帮助类，封装过滤、转换、用户信息提取等常用逻辑。
    /// </summary>
    public static class ReplyHelper
    {
        /// <summary>
        /// 判断是否应启用SQL层初步过滤（hideUseless），依据cookie和帖子类型。
        /// </summary>
        /// <param name="request">HttpRequest对象</param>
        /// <param name="bookVo">当前帖子模型</param>
        /// <returns>是否启用SQL过滤</returns>
        public static bool ShouldApplySqlFilter(HttpRequest request, wap_bbs_Model bookVo)
        {
            HttpCookie cookie = request.Cookies["hideUseless"];
            if (cookie != null && cookie.Value == "1")
            {
                if (bookVo != null && bookVo.freeMoney > 0)
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// 对回复列表进行C#层二次过滤，仅在shouldApplySqlFilter为true时生效。
        /// </summary>
        /// <param name="replies">原始ReplyData列表</param>
        /// <param name="shouldApplySqlFilter">是否启用SQL过滤</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>过滤后的ReplyData列表</returns>
        public static List<ReplyData> ApplySecondaryFilter(List<ReplyData> replies, bool shouldApplySqlFilter, long currentUserId)
        {
            if (!shouldApplySqlFilter)
            {
                return replies;
            }
            int before = replies?.Count ?? 0;
            var filtered = replies.Where(r => !BBSHelper.IsUselessReply(r.Content) || r.UserId == currentUserId).ToList();
            int after = filtered.Count;
            return filtered;
        }

        /// <summary>
        /// 将ReplyData列表转换为wap_bbsre_Model列表。
        /// </summary>
        /// <param name="replyDataList">ReplyData列表</param>
        /// <returns>wap_bbsre_Model列表</returns>
        public static List<wap_bbsre_Model> ConvertToBbsReModels(List<ReplyData> replyDataList)
        {
            var result = new List<wap_bbsre_Model>();
            foreach (var replyData in replyDataList)
            {
                var model = new wap_bbsre_Model
                {
                    id = replyData.Id,
                    devid = replyData.SiteId,
                    userid = replyData.UserId,
                    nickname = replyData.Nickname,
                    classid = replyData.ClassId,
                    bookid = replyData.BookId,
                    content = replyData.Content,
                    redate = replyData.ReplyDate,
                    myGetMoney = replyData.MyGetMoney,
                    book_top = replyData.BookTop,
                    isdown = replyData.AttachCount,
                    reply = replyData.ReplyToFloor,
                    ischeck = 0
                };
                result.Add(model);
            }
            return result;
        }

        /// <summary>
        /// 从ReplyData列表提取原始楼层号。
        /// </summary>
        /// <param name="replyDataList">ReplyData列表</param>
        /// <returns>原始楼层号列表</returns>
        public static List<int> ExtractOriginalFloors(List<ReplyData> replyDataList)
        {
            return replyDataList.Select(r => r.OriginalFloor).ToList();
        }

        /// <summary>
        /// 根据回复模型列表获取用户信息。
        /// </summary>
        /// <param name="replies">wap_bbsre_Model列表</param>
        /// <param name="userBll">user_BLL实例 (注意: 类型从 Main_BLL 改为 user_BLL)</param>
        /// <returns>用户信息列表</returns>
        public static List<user_Model> GetUserInfoForReplies(List<wap_bbsre_Model> replies, user_BLL userBll)
        {
            if (replies == null || !replies.Any()) return new List<user_Model>();
            var distinctUserIds = replies.Select(v => v.userid).Distinct().ToList();
            if (!distinctUserIds.Any()) return new List<user_Model>();

            // ✅ 使用安全的方法获取用户信息，避免SQL注入
            return GetUserInfoSafely(distinctUserIds, userBll);
        }

        /// <summary>
        /// 根据 ReplyData 列表获取用户信息。
        /// </summary>
        /// <param name="replyDataList">ReplyData列表</param>
        /// <param name="userBll">user_BLL实例</param>
        /// <returns>用户信息列表</returns>
        public static List<user_Model> GetUserInfoForReplies(List<ReplyData> replyDataList, user_BLL userBll)
        {
            if (replyDataList == null || !replyDataList.Any()) return new List<user_Model>();
            var distinctUserIds = replyDataList.Select(r => r.UserId).Distinct().ToList();
            if (!distinctUserIds.Any()) return new List<user_Model>();

            // ✅ 使用安全的方法获取用户信息，避免SQL注入
            return GetUserInfoSafely(distinctUserIds, userBll);
        }

        /// <summary>
        /// 安全获取用户信息列表，避免SQL注入
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="userBll">user_BLL实例</param>
        /// <returns>用户信息列表</returns>
        private static List<user_Model> GetUserInfoSafely(List<long> userIds, user_BLL userBll)
        {
            try
            {
                if (userIds == null || !userIds.Any()) return new List<user_Model>();

                // 验证所有用户ID都是有效的正数
                var validUserIds = userIds.Where(id => id > 0).Distinct().ToList();
                if (!validUserIds.Any()) return new List<user_Model>();

                // 使用DapperHelper进行安全查询
                // 获取默认实例名称，因为user_BLL没有公开的InstanceName属性
                string instanceName = PubConstant.GetAppString("InstanceName");
                string connectionString = PubConstant.GetConnectionString(instanceName);

                // 构建安全的参数化查询
                var parameters = new Dictionary<string, object>();
                var paramNames = new List<string>();

                for (int i = 0; i < validUserIds.Count; i++)
                {
                    string paramName = $"@UserId{i}";
                    paramNames.Add(paramName);
                    parameters.Add(paramName, validUserIds[i]);
                }

                string userSql = $"SELECT * FROM [user] WHERE userid IN ({string.Join(",", paramNames)})";

                return DapperHelper.Query<user_Model>(connectionString, userSql, parameters)?.ToList() ?? new List<user_Model>();
            }
            catch (System.Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取用户信息失败: {ex.Message}");
                return new List<user_Model>();
            }
        }

        /// <summary>
        /// 获取置顶回复列表（调用wap_bbsre_BLL.GetListTopVo）。
        /// </summary>
        /// <param name="siteId">站点ID</param>
        /// <param name="bookId">帖子ID</param>
        /// <param name="connectionStringName">数据库连接实例名</param>
        /// <param name="sortOrder">排序方式（"ASC"/"DESC"）</param>
        /// <returns>置顶回复列表</returns>
        public static List<wap_bbsre_Model> GetTopReplies(string siteId, long bookId, string connectionStringName, string sortOrder)
        {
            string baseConditionForTop = $"devid='{siteId}' and bookid={bookId} and ischeck=0 and book_top=1";
            int topSortOrder = (sortOrder == "ASC" ? 1 : 2);
            var bll = new wap_bbsre_BLL(connectionStringName);
            return bll.GetListTopVo(baseConditionForTop, topSortOrder);
        }
    }
}