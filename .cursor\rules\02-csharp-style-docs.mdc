---
description: "C# 编码风格、文档规范和最佳实践，包括 C# 7+ 特性使用、命名约定、错误处理、性能优化和安全注意事项。"
globs: *.aspx,*.ascx,*.aspx.cs,*.ascx.cs,*.cs
alwaysApply: false
---
## C# Style, Docs & Best Practices
- 遵循现代C# 7+风格，推荐用表达式体、字符串插值、模式匹配、**内联`out`变量声明、解构赋值、元组**等特性提升代码简洁性和可读性。鼓励使用 C# 6.0 及以上版本提供的集合初始化器（包括**索引初始化器**）简化代码。
- 变量/方法/类命名请参见 `00-project-overview-workflow.mdc` 第8节。
- 单一职责，结构清晰，变量命名清楚。
    - **静态帮助类的使用场景和优势**: 对于那些完全无状态、不依赖任何实例成员 (`this` 或 `base`)、仅根据输入参数进行操作的纯工具方法，应优先考虑将其实现为静态帮助类（Static Helper Classes）中的静态方法。这样做的好处包括提高代码的可测试性、可重用性，以及更清晰的职责分离——即使这些方法本身逻辑简单或代码行数不多。确保此类静态方法的所有依赖都通过参数明确传入。
    - **字段只读性**: 对于在声明时或构造函数中赋值后不再改变的字段，应使用 `readonly` 修饰符 (如 IDE0044 建议)，以增强不变性和代码清晰度。
- 复杂逻辑需中英文注释，公共方法建议XML注释。
    - **重构后的文档更新**: 在进行代码重构，特别是当提取出新的方法或类时，必须确保为这些新的代码单元（尤其是 `public` 或 `internal` 成员，以及职责复杂的 `private` 成员）编写清晰、准确的XML文档注释 (`///`)。注释应详细说明其功能、每个参数的含义、返回值以及任何重要的行为或前置条件。
- 错误处理仅对预期异常用try-catch，异常需日志记录。
- 日志用System.Diagnostics.Debug.WriteLine，生产建议集成更完善日志。
- 所有用户输入需服务端校验。
- SQL操作必须参数化，严禁拼接SQL。
- 循环高效，避免不必要嵌套。
- 合理使用缓存。
- 所有敏感操作需校验用户身份和权限。
- 防范XSS、SQL注入。
- 仅允许.NET Framework 4.8兼容库。
- **代码简洁性与IDE建议**: 积极采纳IDE（如Visual Studio, Rider, Cursor）提供的代码风格和现代化建议（例如消除冗余赋值 IDE0059、简化集合初始化 IDE0028、使用 `nameof` 等），以保持代码的简洁、高效和与时俱进，前提是不违反项目核心约束和兼容性要求。

**新增规则点 (基于重构经验):**

- **重构与可测试性**: 代码重构的一个重要目标应包括提升其可测试性。在分解复杂方法或重新组织逻辑时，应有意识地减少代码对 `HttpContext`、页面状态（如 `ViewState`, `Session`）以及其他难以模拟的全局状态的直接依赖。优先提取那些可以被改造成纯函数（输入决定输出，无副作用）或易于通过参数注入依赖的逻辑单元。如果可能，将这些单元设计为静态方法或独立服务类的方法，以便于编写单元测试。

**现代化数据访问模式:**

- **DapperHelper 优先原则**: 项目已引入现代化数据访问工具，推荐优先级：
    1. **DapperHelper**: 用于常规的参数化查询操作 (`Query<T>`, `Execute`, `ExecuteScalar`)
    2. **OptimizedTransactionHelper**: 用于复杂事务和批量操作
    3. **传统 DbHelperSQL**: 仅在特殊场景下使用，如需要特定的 KeLin 框架功能时

- **现代化服务类**: 项目提供多种业务服务类：
    - **UserInfoCacheService**: 用户信息缓存，减少数据库查询
    - **ConfigService**: JSON配置文件管理，替代硬编码配置
    - **BBSConfigService**: BBS业务配置统一管理
    - **TemplateService**: Handlebars模板渲染服务

**渐进式重构指导:**

- **模板驱动开发**: 新功能推荐使用Handlebars模板系统：
    - 构建强类型数据模型类（继承自 `PageModel` 基类）
    - 使用 `TemplateService.RenderPageWithLayout()` 进行统一渲染
    - 数据模型与视图逻辑分离，避免在模板中编写复杂业务逻辑
    - 模板文件位于 `~/Template/Pages/` 目录，支持布局和部分模板

- **双系统兼容性**: 在渐进式重构过程中：
    - 新功能优先使用现代化技术栈（Handlebars + Tailwind CSS）
    - 旧功能保持现有实现，确保系统稳定性
    - 通过 ViewState 或查询参数支持新旧UI切换
    - 避免在同一页面混用新旧技术栈

- 相关Web Forms结构、性能等请参见 `01-webforms-style-structure.mdc` 和 `03-frontend-development-rules.mdc`。

### SQL 注入风险与参数化查询实践

在 FriendList.aspx 的开发过程中，我们在 `goaddfriend` 方法中发现了一个因**字符串拼接**用户输入而导致的**SQL 注入漏洞**。这是一个典型的安全风险，攻击者可以构造恶意输入来修改或窃取数据库信息。

**问题示例 (存在漏洞的代码思路)**:
```csharp
// 假设的、存在漏洞的代码片段
string userName = GetRequestValue("userName"); // 直接获取用户输入
string sql = "INSERT INTO Friends (UserName) VALUES ('" + userName + "')"; // 直接拼接，存在风险
// 执行 SQL...
// 如果 userName 输入是 'abc'); DROP TABLE Friends; --
// 最终 SQL 会变成 INSERT INTO Friends (UserName) VALUES ('abc'); DROP TABLE Friends; --')
```

**解决方案 (使用参数化查询)**:
彻底消除 SQL 注入风险的**唯一安全方式**是使用**参数化查询**。本项目推荐的数据访问优先级：

1. **DapperHelper** (推荐): 现代化的参数化查询接口
2. **BLL/DAL 层** (KeLin.ClassManager): 传统业务逻辑层
3. **直接SQL执行**: 仅在特殊场景使用

对于直接执行 SQL 的场景，优先使用 `DapperHelper.Execute()` 进行参数化查询，其次才考虑 `KeLin.ClassManager.ExUtility.DbHelperSQL.ExecuteNonQuery` 等传统方法。

**修复后的代码示例 (推荐使用DapperHelper)**:
```csharp
// FriendList.aspx.cs 中的 goaddfriend 方法片段 (现代化修复版本)
string _n = GetRequestValue("n"); // 获取用户输入的待添加好友用户名

// ... 其他业务逻辑和输入校验 ...

// 推荐：使用 DapperHelper 进行参数化查询
string cmdText = "INSERT INTO FriendList (myid, youid, addTime, state, isdel, [bz], fen组) " +
                 "VALUES (@myid, @youid, @addTime, @state, @isdel, @bz, @fenZu)";

var parameters = new
{
    myid = userVo.ID,
    youid = youVo.ID, // youVo.ID 是经过查库获取的安全用户ID
    addTime = DateTime.Now,
    state = 0, // 0 代表待确认状态
    isdel = 0, // 0 代表未删除
    bz = "", // 备注
    fenZu = "默认分组" // 分组
};

int rowsAffected = DapperHelper.Execute(PubConstant.GetConnectionString("KeLin"), cmdText, parameters);

if (rowsAffected > 0)
{
    // 添加好友请求成功处理...
}
else
{
    // 添加好友请求失败处理...
}
```

**传统方式 (兼容性考虑)**:
```csharp
// 如需使用传统 DbHelperSQL 方式
SqlParameter[] parameters = new SqlParameter[]
{
    new SqlParameter("@myid", SqlDbType.Int) { Value = userVo.ID },
    new SqlParameter("@youid", SqlDbType.Int) { Value = youVo.ID },
    new SqlParameter("@addTime", SqlDbType.DateTime) { Value = DateTime.Now },
    new SqlParameter("@state", SqlDbType.Int) { Value = 0 },
    new SqlParameter("@isdel", SqlDbType.Int) { Value = 0 },
    new SqlParameter("@bz", SqlDbType.NVarChar, 50) { Value = "" },
    new SqlParameter("@fenZu", SqlDbType.NVarChar, 50) { Value = "默认分组" }
};

int rowsAffected = KeLin.ClassManager.ExUtility.DbHelperSQL.ExecuteNonQuery(cmdText, parameters);
```

**核心要点**:
*   永远不要将用户输入直接拼接到 SQL 字符串中。
*   **DapperHelper 优势**: 使用匿名对象作为参数，代码更简洁，类型安全
*   **传统方式**: 使用 `SqlParameter` 对象，需要指定数据类型和长度
*   **方法选择**: `DapperHelper.Execute()` 用于 INSERT/UPDATE/DELETE，`DapperHelper.Query<T>()` 用于 SELECT
*   **连接字符串**: 统一使用 `PubConstant.GetConnectionString()` 获取

**现代化数据访问最佳实践**:
*   优先使用 `DapperHelper` 进行数据访问，代码更简洁且类型安全
*   复杂事务操作使用 `OptimizedTransactionHelper`
*   需要缓存的用户信息查询使用 `UserInfoCacheService`
*   配置相关操作使用 `ConfigService` 管理JSON配置

这条实践经验再次强调了在本项目中，**参数化查询是进行所有数据库写入和修改操作的强制要求，推荐使用现代化的 DapperHelper 工具。**






