/* 主容器样式 */
.upload-container {
    max-width: 48rem;
    margin: 0 auto;
    padding: .5rem;
}

/* 标签页样式 */
.tab-header {
    display: flex;
    margin: 0 0 .5rem;
    border-radius: 0.5rem;
    overflow: hidden;
    background: #f3f4f6;
    padding: 0.25rem;
    gap: 0.5rem;
}

.tab-btn {
    flex: 1;
    padding: 0.35rem 0;
    text-align: center;
    color: #374151;
    border-radius: 0.375rem;
    transition: all 0.2s;
    text-decoration: none;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background: transparent;
}

.tab-btn svg {
    width: 20px;
    height: 20px;
}

.tab-btn.active {
    background: white;
    color: #374151;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.tab-btn:hover:not(.active) {
    background: #e5e7eb;
}
.tab-header .tab-btn:hover {
  text-decoration: none;
}
/* 表单样式 */
.form-group {
    margin-bottom: .5rem;
}

.form-group label {
    display: block;
    color: #374151;
    font-weight: bold;
}

.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: border-color 0.2s;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: #6b7280;
}

/* 内容头部样式 */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.textarea-actions {
    display: flex;
    gap: 0.2rem;
}

/* 操作按钮 */
.action-btn {
    padding: 0.75rem 1rem;
    background: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    color: #374151;
    font-size: 0.975rem;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 120px;
    font-weight: 500;
}

.action-btn:hover {
    background: #e5e7eb;
}

/* 小按钮样式 */
.action-btn-small {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
    padding: 0.25rem 0.45rem;
    background: #f3f4f638;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    color: #374151;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s;
    height: 28px;
    line-height: 1;
}

.action-btn-small svg {
    width: 14px;
    height: 14px;
    flex-shrink: 0;
    vertical-align: middle;
}

.action-btn-small span {
    vertical-align: middle;
    line-height: 1;
}

.action-btn-small:hover {
    background: #e5e7eb;
}

/* 提交按钮样式 */
#submitBtn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    max-width: 200px;
    margin: 1.5rem auto;
    padding: 0.75rem 1.5rem;
    background: #1f2937;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
	line-height: 1;
}

#submitBtn:hover {
    background: #000;
    transform: translateY(-1px);
}

#submitBtn:active {
    transform: translateY(0);
}

/* 提示框样式 */
.tip {
    background: #fef3c7;
    border-left: 4px solid #f59e0b;
    padding: .5rem 1rem;
    border-radius: 0.375rem;
    color: #92400e;
}

/* 警告提示样式 */
.triangle-alert {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px;
    margin: 0 auto;
    width: calc(100% - 1rem);
    box-sizing: border-box;
    background-color: #F4F4F580;
    border-radius: 8px;
    color: #374151;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.triangle-alert svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
}

/* 导航按钮样式 */
.nav-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    padding: 1rem 0 1rem;
}

.nav-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    color: #374151;
    font-size: 0.875rem;
    text-decoration: none;
    transition: all 0.2s;
    text-align: center;
}

.nav-btn svg {
    width: 16px;
    height: 16px;
}

.nav-btn:hover {
    background: #f9f9f9;
    transform: translateY(-1px);
    color: #374151;
    text-decoration: none;
}

/* 上传成功提示样式 */
.upload-success {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    margin: 1rem 0;
    background-color: #ecfdf5;
    border-radius: 0.5rem;
}

.upload-success-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.upload-success svg {
    width: 16px;
    height: 16px;
    color: #059669;
}

.upload-success-title {
    color: #065f46;
    font-size: 0.875rem;
    font-weight: 600;
}

.upload-success-subtitle {
    color: #065f46;
    font-size: 0.875rem;
    margin-left: 1.5rem;
}

/* 通知样式 */
.custom-notification {
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    top: 80px;
}

.custom-notification-container {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    text-align: center;
}

.custom-notification-content {
    margin: 0;
    font-size: 14px;
}

.file-number {
    flex-shrink: 0;
    width: 2rem;
    height: 2rem;
    background: #f3f4f6;
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: #374151;
}
/* 字体样式 */
.file-name, .file-number, .upload-success-text, .nav-buttons, .upload-success, .dialog-url-description {
    font-family: GeistSans, GeistSans Fallback;
}

.big-upload-icon {
    color: #222;
    font-size: 48px;
    margin-bottom: 10px;
    line-height: 1;
}
.collapse-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #6b7280;
}

.collapse-btn svg {
    width: 1.25rem;
    height: 1.25rem;
    transition: transform 0.2s;
}

.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 15px;
}

.form-group.half {
    flex: 1;
    margin-bottom: 0;
}

.form-content {
    transition: all 0.3s;
}


/* 本地上传页面专属 */

/* 文件项目样式 */
.fileUploadArea {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.file-item {
    width: 100%;
    position: relative;
}

.file-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    padding-right: 4rem;
}

.file-select-area {
    border: 2px dashed #e5e7eb;
    border-radius: 0.5rem;
    padding: 2rem 1rem 3.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    background: white;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.file-select-area:hover {
    border-color: #6b7280;
    background: #f9fafb;
}

.file-select-area.drag-over {
    border-color: #2563eb;
    background-color: #eff6ff;
}

.file-select-area::after {
    content: '支持拖拽文件或粘贴文件';
    display: block;
    width: 100%;
    text-align: center;
    position: absolute;
    bottom: 1.5rem;
    left: 0;
    font-size: 0.75rem;
    color: #6b7280;
}

/* 添加媒体查询以适应移动端 */
@media (max-width: 768px) {
    .file-select-area {
        padding: 2rem 1rem 3rem;
    }
    
    .file-select-area::after {
        bottom: 1rem;
    }
}

.file-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
    min-width: 0;
}

.file-name {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.875rem;
    color: #374151;
}

.removeBtn {
    position: absolute;
    right: .6rem;
    padding: 0.5rem 1rem;
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.2s;
}

.removeBtn:hover {
    background: #dc2626;
}

.button-container {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin: 0 0 1.5rem 0;
}

.upload-text {
    color: #374151;
}

#submitBtn svg {
    width: 18px;
    height: 18px;
    position: relative;
    top: -1px;
    display: inline-block;
    vertical-align: middle;
}

/* URL上传页面专属 */
.file-header-url {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: -1rem -1rem 0 -1rem;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.2s;
    border-top-left-radius: 0.5rem;
    border-top-right-radius: 0.5rem;
}

.file-header-url:hover {
    background-color: #f3f4f6;
}

.file-header-url:hover .file-number {
    background-color: white;
}

.file-header-url.active {
    background-color: transparent;
    margin-bottom: 0;
}

.file-header-url.active:hover {
    background-color: #f3f4f6;
}

.file-header-url.active .file-number {
    background-color: #f3f4f6;
}

.file-header-url.active:hover .file-number {
    background-color: white;
}

.file-title-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.num-input-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    background: white;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
}

.num-input-group label {
    font-size: 0.875rem;
    color: #374151;
    font-weight: 500;
}

.num-input-group input[type='number'] {
    width: 60px;
    padding: 0.5rem;
    text-align: center;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #374151;
}

.confirm-btn {
    padding: 0.43rem 1.5rem;
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
    margin-left: -5px;
}

.confirm-btn:hover {
    background: #e5e7eb;
    transform: translateY(-1px);
}

.confirm-btn:active {
    transform: translateY(0);
    background: #d1d5db;
}

.file-upload-section {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.file-title-url {
    font-size: 1rem;
    font-weight: 600;
    color: #111827;
}




/* 文件回帖页面专属 */
.emoji-button-wrapper {
    position: relative;
    margin-bottom: 0.5rem;
}

.emoji-button {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    color: #374151;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
}

.emoji-button:hover {
    background: #f9fafb;
    border-color: #d1d5db;
}

.emoji-button svg {
    width: 16px;
    height: 16px;
    margin-right: 0.5rem;
}
.content-textarea {
    width: 100%;
    min-height: 150px;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    margin-bottom: 5px;
    resize: vertical;
    font-size: 0.875rem;
    box-sizing: border-box;
}

.content-textarea:focus {
    outline: none;
    border-color: #6b7280;
}

/* 页面标题样式 */
.page-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
    padding-bottom: 0.75rem;
    margin-left: 7px;
}



/* 文件续传页面专属 */
.book_file_info {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    resize: vertical;
    min-height: 3rem;
    box-sizing: border-box;
    margin-bottom: 5px;
}

.book_file_info:focus {
    outline: none;
    border-color: #6b7280;
    ring: 2px solid #6b7280;
}

#multiFileSelect {
    display: none;
}


/* URL续传页面更新 */
        .dialog-url {
            background-color: white;
            border-radius: 8px;
            padding: 24px;
            width: 90%;
            max-width: 400px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            animation: fadeIn 0.3s ease-out;
            font-family: -apple-system, BlinkMacSystemFont, ""Segoe UI"", Roboto, ""Helvetica Neue"", Arial, sans-serif;
            border: none;
        }

        .dialog-url::backdrop {
            background-color: rgba(0, 0, 0, 0.5);
        }

        .dialog-url-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .dialog-url-icon {
            width: 24px;
            height: 24px;
            margin-right: 8px;
        }

        .dialog-url-title {
            color: #e11d48;
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }

        .dialog-url-description {
            color: #4b5563;
            font-size: 16px;
            margin-bottom: 24px;
        }

        .dialog-url-footer {
            display: flex;
            justify-content: flex-end;
        }

        .dialog-url-button {
            background-color: #212936;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .dialog-url-button:hover {
            background-color: #18181B;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .num-selector {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }
		
        .num-top {
            padding: 10px;
            margin: 15px 0;
        }

        .num-selector label {
            font-size: 15px;
            color: #333;
            font-weight: bold;
            font-size: 1.075rem;
        }

        .number-control {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 8px;
        }

        .num-btn {
            width: 32px;
            height: 32px;
            background: #fff;
            border: 1px solid #e5e5e5;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            padding: 0;
        }

        .num-btn:hover {
            background: #f5f5f5;
        }

        .num-btn svg {
            width: 16px;
            height: 16px;
        }

        #numInput {
            width: 30px;
            text-align: center;
            border: none;
            background: transparent;
            font-size: 16px;
            padding: 0;
            margin: 0;
            -moz-appearance: textfield;
            caret-color: transparent;
            user-select: none;
        }

        #numInput::-webkit-outer-spin-button,
        #numInput::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        #numInput:read-only {
            background: transparent;
        }

        .num-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .num-btn:disabled {
            pointer-events: none;
        }

/* 发表普通帖页面新增 */
.nav-buttons.grid-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    padding: 0.5rem;
}

.nav-buttons.grid-2 .nav-btn {
    margin: 0;
}
/* 面包屑导航样式 */
.breadcrumb {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    color: #6b7280;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    color: #6b7280;
    text-decoration: none;
    transition: color 0.2s;
	font-size: 16px;
}

.breadcrumb-item:hover {
    color: #374151;
}

.breadcrumb-item.active {
font-size: 16px;
cursor: pointer;
}

.breadcrumb-item svg {
    width: 16px;
    height: 16px;
}

.breadcrumb-separator {
    margin: 0 0.5rem;
    color: #d1d5db;
}

/* 修改面包屑导航分隔符样式 */
.breadcrumb-separator {
    position: relative;
    width: 14px; /* 减小整体宽度 */
    height: 14px; /* 减小整体高度 */
}

.breadcrumb-separator::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;  /* 减小箭头大小 */
    height: 5px; /* 减小箭头大小 */
    border-right: 1.5px solid #d1d5db; /* 减小边框宽度 */
    border-top: 1.5px solid #d1d5db;   /* 减小边框宽度 */
    transform: translate(-50%, -50%) rotate(45deg);
}

/* 悬赏妖晶折叠部分样式 */
.reward-section {
    margin-bottom: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    overflow: hidden;
    width: 198px; /* 设置固定宽度 */
}

.collapse-trigger {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background: white;
    border: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.collapse-trigger svg {
    width: 16px;
    height: 16px;
    color: #374151;
    stroke: currentColor;
}

.collapse-trigger span {
    flex: 1;
    text-align: left;
    margin-left: 0.5rem;
    font-size: 0.875rem;
    color: #374151;
}

.arrow-icon {
    width: 16px;
    height: 16px;
    transition: transform 0.2s;
}

.collapse-trigger.active .arrow-icon {
    transform: rotate(180deg);
}

.reward-content {
    padding: .8rem;
    background: #f9fafb;
}

/* 修改输入框样式以适应新宽度 */
.reward-content .form-control {
    width: 100% !important; /* 覆盖内联样式 */
    max-width: 100% !important; /* 覆盖内联样式 */
}

@media (max-width: 640px) {
    .breadcrumb-separator {
        width: 10px;
        height: 10px;
        margin: 0 0.25rem;
    }
    .textarea-actions {
        gap: 0.1rem;
    }
}