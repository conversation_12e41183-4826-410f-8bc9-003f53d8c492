﻿using System;
using System.Collections.Generic;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.Album
{
    public class AlbumList : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string linkURL = "";

        public string condition = "";

        public string ERROR = "";

        public string key = "";

        public string friendtype = "";

        public string backurl = "";

        public string linkTOP = "";

        public string smalltypeid = "";

        public string touserid = "";

        public List<wap_album_Model> listVo = null;

        public wap_albumSubject_Model bookVo = null;

        public long kk = 1L;

        public long index = 0L;

        public long total = 0L;

        public long pageSize = 12L;

        public long CurrentPage = 1L;

        protected void Page_Load(object sender, EventArgs e)
        {
            IsLogin(userid, backurl);
            action = GetRequestValue("action");
            smalltypeid = GetRequestValue("smalltypeid");
            if (smalltypeid == "")
            {
                smalltypeid = "0";
            }
            touserid = GetRequestValue("touserid");
            if (touserid == "")
            {
                touserid = userid;
            }
            // 新增跳转逻辑，如果不是自己的相册
            if (touserid != userid)
            {
                Response.Redirect("/album/albumlist.aspx");
                return;
            }
            switch (action)
            {
                case "class":
                    showclass();
                    break;
                default:
                    showclass();
                    break;
                case "godel":
                    break;
            }
        }

        public void showclass()
        {
            key = GetRequestValue("key");

            try
            {
                pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
                //指定每页多少张图片
                pageSize = 12;

                string connectionString = PubConstant.GetConnectionString(string_10);

                // ✅ 使用QueryBuilder构建安全的WHERE条件
                var queryBuilder = new QueryBuilder();

                // 基础条件：userid和makerid
                queryBuilder.Where("userid = @ParamN", DapperHelper.SafeParseLong(siteid, "站点ID"));
                queryBuilder.Where("makerid = @ParamN", DapperHelper.SafeParseLong(touserid, "用户ID"));

                // 隐私条件：如果不是自己或未登录，只显示公开相册
                queryBuilder.WhereIf(userid == "0" || touserid != userid, "ishidden = @ParamN", 0);

                // 分类条件
                queryBuilder.WhereIf(!string.IsNullOrEmpty(smalltypeid) && smalltypeid != "0",
                    "smalltype = @ParamN", DapperHelper.SafeParseLong(smalltypeid, "分类ID"));

                // 关键词搜索条件
                queryBuilder.WhereIf(!string.IsNullOrEmpty(key?.Trim()),
                    "book_title LIKE @ParamN", $"%{DapperHelper.LimitLength(key.Trim(), 100)}%");

                // 获取总数
                if (GetRequestValue("getTotal") != "")
                {
                    total = long.Parse(GetRequestValue("getTotal"));
                }
                else
                {
                    // ✅ 使用DapperHelper进行安全的计数查询
                    var (countSql, countParameters) = queryBuilder.Build("SELECT COUNT(*) FROM wap_album");
                    total = DapperHelper.ExecuteScalar<long>(connectionString, countSql, countParameters);
                }

                if (GetRequestValue("page") != "")
                {
                    CurrentPage = long.Parse(GetRequestValue("page"));
                }

                CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
                index = pageSize * (CurrentPage - 1L);

                // 构建分页链接
                linkURL = http_start + "album/albumlist.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;touserid=" + touserid + "&amp;smalltypeid=" + smalltypeid + "&amp;key=" + HttpUtility.UrlEncode(key) + "&amp;getTotal=" + total;
                linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, total, pageSize, CurrentPage, linkURL);
                linkURL = WapTool.GetPageLink(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL);

                // ✅ 使用安全的分页查询获取相册列表
                var result = PaginationHelper.GetPagedDataWithBuilder<wap_album_Model>(
                    connectionString,
                    "SELECT *",
                    "wap_album",
                    queryBuilder,
                    (int)CurrentPage,
                    (int)pageSize,
                    "ORDER BY id DESC"
                );

                listVo = result.Data;

                // 获取分类信息（使用安全方法）
                if (smalltypeid != "0")
                {
                    wap_albumSubject_BLL wap_albumSubject_BLL = new wap_albumSubject_BLL(string_10);
                    bookVo = wap_albumSubject_BLL.GetModel(DapperHelper.SafeParseLong(smalltypeid, "分类ID"));
                }
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
        }
    }
}