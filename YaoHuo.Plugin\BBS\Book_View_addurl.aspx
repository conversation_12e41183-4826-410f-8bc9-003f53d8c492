﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_View_addurl.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Book_View_addURL" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    wmlVo.mycss += "\r\n<link href=\"/netcss/css/upload-resource.css?X13\" rel=\"stylesheet\" type=\"text/css\"/>";
    if (this.INFO == "OK")
    {
        wmlVo.timer = "2";
        wmlVo.strUrl = "bbs-" + this.getid + ".html";
    }
    StringBuilder strhtml = new StringBuilder();
    Response.Write(WapTool.showTop(this.GetLang("发表资源帖|发表资源帖|add subject"), wmlVo));
    if (num > 9) num = 9;
    if (num < 1) num = 1;
    strhtml.Append("<div class=\"upload-container\">");
    strhtml.Append("<div class=\"breadcrumb\">");
    strhtml.Append("<a href=\"/\" class=\"breadcrumb-item\">");
    strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-home h-4 w-4\" data-id=\"7\"><path d=\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"></path><polyline points=\"9 22 9 12 15 12 15 22\"></polyline></svg>");
    strhtml.Append("<span>首页</span>");
    strhtml.Append("</a>");
    strhtml.Append("<span class=\"breadcrumb-separator\"></span>");
    strhtml.Append("<a href=\"/bbslist-" + this.classid + ".html\" class=\"breadcrumb-item\">" + classVo.classname + "</a>");
    strhtml.Append("<span class=\"breadcrumb-separator\"></span>");
    strhtml.Append("<span class=\"breadcrumb-item active\">发表资源帖</span>");
    strhtml.Append("</div>");
    strhtml.Append("<div class=\"tab-header\">");
    strhtml.Append("<a class=\"tab-btn\" href=\"" + this.http_start + "bbs/Book_View_addfile.aspx?action=class&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;page=" + this.page + "\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242\"></path><path d=\"M12 21v-9\"></path><path d=\"m8 16 4-4 4 4\"></path></svg>本地上传</a> "); strhtml.Append("<a class=\"tab-btn active\" href=\"#\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\"></path><path d=\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\"></path></svg>外站资源</a> ");
    strhtml.Append("</div>");
    strhtml.Append("<div style=\"display:none\" class=\"notification-container\"><div class=\"custom-notification\"> <div class=\"custom-notification-container\"> <p class=\"custom-notification-content\">草稿保存成功!</p> </div> </div></div>");
    strhtml.Append(this.ERROR);
    if (!string.IsNullOrEmpty(this.INFO))
    {
        if (this.INFO == "EXTERR")
        {
            strhtml.Append("<div class=\"tip\"><b>上传文件格式错误，只允许上传：" + siteVo.UpFileType + "</b></div>");
        }
        else if (this.INFO == "NOTSPACE")
        {
            strhtml.Append("<div class=\"tip\"><b>网站总空间已经大于系统分配给此网站的最大空间了，网站空间：" + siteVo.sitespace + "M；此网站已使用：" + (siteVo.myspace) + "KB</b></div>");
        }
        else if (this.INFO == "MAXFILE")
        {
            strhtml.Append("<div class=\"tip\"><b>你上传的单个文件超出了最大限制" + siteVo.MaxFileSize + "KB</b></div>");
        }
        else if (this.INFO == "NULL")
        {
            strhtml.Append("<div class=\"tip\"><b>标题最少" + this.titlemax + "字，内容最少" + this.contentmax + "字！</b></div>");
        }
        else if (this.INFO == "TITLEMAX")
        {
            if (title_max != "0")
            {
                strhtml.Append("<div class=\"tip\"><b>标题最大" + this.title_max + "字。</b></div>");
            }
            if (content_max != "0")
            {
                strhtml.Append("<div class=\"tip\"><b>内容最大" + this.content_max + "字。</b></div>");
            }
        }
        else if (this.INFO == "ERR_FORMAT")
        {
            strhtml.Append("<div class=\"tip\"><b>取到非法值:\"$$\"请更换手机浏览器或重新编辑！</b></div>");
        }
        else if (this.INFO == "REPEAT")
        {
            strhtml.Append("<div class=\"tip\"><b>请不要发表重复内容</b></div>");
        }
        else if (this.INFO == "PWERROR")
        {
            strhtml.Append("<div class=\"tip\"><b>密码错误，请重新录入我的密码！</b></div>");
        }
        else if (this.INFO == "ERROR_Secret")
        {
            strhtml.Append("<div class=\"tip\"><b>暗号错误，如果忘记联系站长索取！</b></div>");
        }
        else if (this.INFO == "MAX")
        {
            strhtml.Append("<div class=\"tip\"><b>今天已超过发帖限制，请明天再来！</b></div>");
        }
        else if (this.INFO == "SENDMONEY")
        {
            strhtml.Append("<div class=\"tip\"><b>你当前的只有:" + userVo.money + "个，所以你悬赏值只能小于或等于" + userVo.money + "个</b></div>");
        }
        else if (this.INFO == "NOMONEY")
        {
            strhtml.Append("<div class=\"tip\"><b>你当前的只有:" + userVo.money + "个，发帖需要扣掉：" + getmoney2 + "个</b></div>");
        }
        else if (this.INFO == "LOCK")
        {
            strhtml.Append("<div class=\"tip\"><b>抱歉，您已经被加入黑名单，请注意发帖规则！</b></div>");
        }
    }
    if (this.INFO == "OK")
    {
        strhtml.Append("<div class=\"upload-success\">");
        strhtml.Append("<div class=\"upload-success-header\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\"></path><path d=\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\"></path></svg>");
        strhtml.Append("<div class=\"upload-success-title\">发表资源帖成功！</div>");
        strhtml.Append("</div>");
        if (siteVo.isCheck == 1)
        {
            strhtml.Append("<div class=\"upload-success-subtitle\">审核后显示！</div>");
        }
        strhtml.Append("<div class=\"upload-success-subtitle\">获得" + WapTool.GetSiteMoneyName(siteVo.sitemoneyname, this.lang) + ":" + getmoney + "，获得经验:" + getexpr + "</div>");
        strhtml.Append("</div>");
        strhtml.Append("<script type=\"text/javascript\" src=\"/netcss/js/ClearDraft.js?X1\"></script>");

        strhtml.Append("<div class=\"nav-buttons\">");
        strhtml.Append("<a class=\"nav-btn\" href=\"/bbs-" + this.getid + ".html\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-circle-arrow-right\"><circle cx=\"12\" cy=\"12\" r=\"10\"/><path d=\"M8 12h8\"/><path d=\"m12 16 4-4-4-4\"/></svg>");
        strhtml.Append("进入主题</a>");
        strhtml.Append("<a class=\"nav-btn\" href=\"/\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-house\"><path d=\"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\"/><path d=\"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"/></svg>");
        strhtml.Append("返回首页</a>");
        strhtml.Append("</div>");
    }
    else
    {
        strhtml.Append("<div class=\"content\">");
        strhtml.Append("<form name=\"f\" action=\"" + http_start + "bbs/Book_View_addURL.aspx\" method=\"post\">");
        strhtml.Append("<div class=\"form-group\">");
        strhtml.Append("<label>标题</label>");
        strhtml.Append("<input type=\"text\" minlength=\"5\" maxlength=\"25\" required=\"required\" name=\"book_title\" class=\"form-control\" value=\"" + book_title + "\"/>");
        strhtml.Append("</div>");
        strhtml.Append("<div class=\"form-group\">");
        strhtml.Append("<div class=\"content-header\">");
        strhtml.Append("<label>内容</label>");
        strhtml.Append("<div class=\"textarea-actions\">");
        strhtml.Append("<button type=\"button\" class=\"action-btn-small\" id=\"saveDraftButton\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z\"></path><path d=\"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7\"></path><path d=\"M7 3v4a1 1 0 0 0 1 1h7\"></path></svg>");
        strhtml.Append("<span>保存草稿</span></button>");
        strhtml.Append("<button type=\"button\" class=\"action-btn-small\" style=\"display:none\" id=\"clearDraftButton\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M3 6h18\"></path><path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"></path><path d=\"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\"></path><line x1=\"10\" x2=\"10\" y1=\"11\" y2=\"17\"></line><line x1=\"14\" x2=\"14\" y1=\"11\" y2=\"17\"></line></svg>");
        strhtml.Append("<span>清除草稿</span></button>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("<textarea name=\"book_content\" class=\"form-control\" oninput=\"adjustTextareaHeight(this)\" minlength=\"15\" required=\"required\" rows=\"3\" style=\"min-height:25vh;\">" + book_content + "</textarea>");
        strhtml.Append("</div>");
        strhtml.Append("<div class=\"num-selector\">");
        strhtml.Append("<label>" + this.GetLang("资源数量|上传数量|Upload Number") + "</label>");
        strhtml.Append("<div class=\"number-control\">");
        strhtml.Append("<button type=\"button\" class=\"num-btn\" onclick=\"updateNum(1)\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M18 15 12 9 6 15\"/></svg></button>");
        strhtml.Append("<input type=\"number\" id=\"numInput\" readonly name=\"displayNum\" value=\"" + this.num + "\" data-current=\"" + this.num + "\"/>");
        strhtml.Append("<button type=\"button\" class=\"num-btn\" onclick=\"updateNum(-1)\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M6 9 12 15 18 9\"/></svg></button>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        for (int i = 0; i < this.num; i++)
        {
            strhtml.Append("<div class=\"file-upload-section\">");
            strhtml.Append("<div class=\"file-header-url\" onclick=\"toggleCollapse(this)\">");
            strhtml.Append("<div class=\"file-title-group\">");
            strhtml.Append("<div class=\"file-number\">" + (i + 1) + "</div>");
            strhtml.Append("<div class=\"file-title-url\">资源文件<span style=\"padding-left: 2px;\">" + (i + 1) + "</span></div>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"collapse-btn\">");
            strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"chevron-up\"><polyline points=\"18 15 12 9 6 15\"></polyline></svg>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"form-content\">");
            strhtml.Append("<div class=\"form-group\">");
            strhtml.Append("<label>资源名称</label>");
            strhtml.Append("<input type=\"text\" maxlength=\"35\" placeholder=\"必填项\" required=\"required\" name=\"file_title\" class=\"form-control\"/>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"form-group\">");
            strhtml.Append("<label>链接地址</label>");
            strhtml.Append("<input type=\"text\" placeholder=\"http 或 https 开头的链接\" required=\"required\" name=\"file_url\" class=\"form-control\"/>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"form-row\">");
            strhtml.Append("<div class=\"form-group half\">");
            strhtml.Append("<label>文件大小</label>");
            strhtml.Append("<input type=\"text\" maxlength=\"7\" placeholder=\"选填，例如: 8MB\" name=\"file_size\" class=\"form-control\"/>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"form-group half\">");
            strhtml.Append("<label>文件后缀</label>");
            strhtml.Append("<input type=\"text\" maxlength=\"5\" placeholder=\"选填，例如: zip\" name=\"file_ext\" class=\"form-control\"/>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"form-group\">");
            strhtml.Append("<label>" + this.GetLang("附件说明|附件说明|Source") + "</label>");
            strhtml.Append("<textarea name=\"file_info\" oninput=\"adjustTextareaHeight(this)\" placeholder=\"选填备注信息，例如网盘提取密码、文件解压密码\" class=\"form-control\" rows=\"2\"></textarea>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
        }
        strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"page\" value=\"" + page + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"num\" value=\"" + num + "\"/>");
        strhtml.Append("<button type=\"submit\" id=\"submitBtn\" class=\"submit-btn\" name=\"g\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\"></path><path d=\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\"></path></svg>");
        strhtml.Append("<span>发表资源帖</span>");
        strhtml.Append("</button>");
        strhtml.Append("</form>");

        strhtml.Append("<div class=\"triangle-alert\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-triangle-alert\"><path d=\"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3\"/><path d=\"M12 9v4\"/><path d=\"M12 17h.01\"/></svg>严禁上传色情文件、病毒文件和恶意软件。");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        strhtml.Append("<div class=\"nav-buttons grid-2\">");
        strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/book_view_add.aspx?classid=" + this.classid + "\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"></path><path d=\"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z\"></path></svg>");
        strhtml.Append("发表普通帖</a>");
        strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/book_view_sendmoney.aspx?classid=" + this.classid + "\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-database w-4 h-4 mr-2\" data-id=\"40\"><ellipse cx=\"12\" cy=\"5\" rx=\"9\" ry=\"3\"></ellipse><path d=\"M3 5V19A9 3 0 0 0 21 19V5\"></path><path d=\"M3 12A9 3 0 0 0 21 12\"></path></svg>");
        strhtml.Append("发表派币帖</a>");
        strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/book_view_addvote.aspx?classid=" + this.classid + "\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-vote mr-2 h-4 w-4\" data-id=\"31\"><path d=\"m9 12 2 2 4-4\"></path><path d=\"M5 7c0-1.1.9-2 2-2h10a2 2 0 0 1 2 2v12H5V7Z\"></path><path d=\"M22 19H2\"></path></svg>");
        strhtml.Append("发表投票帖</a>");
        strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/book_view_ubb.aspx?classid=" + this.classid + "&amp;backurl=" + HttpUtility.UrlEncode("bbs/book_view_add.aspx?classid=" + this.classid) + "\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12 20h9\"></path><path d=\"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z\"></path></svg>");
        strhtml.Append("UBB方法</a>");
        strhtml.Append("</div>");
    }
    strhtml.Append(@"<dialog id=""urlDialog"" class=""dialog-url"">
        <div class=""dialog-url-header"">
            <svg class=""dialog-url-icon"" xmlns=""http://www.w3.org/2000/svg"" fill=""none"" viewBox=""0 0 24 24"" stroke=""currentColor"">
                <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"" />
            </svg>
            <h2 id=""dialog-title"" class=""dialog-url-title"">链接格式错误</h2>
        </div>
        <p id=""dialog-description"" class=""dialog-url-description"">
            链接地址必须以 http:// 或 https:// 开头
        </p>
        <div class=""dialog-url-footer"">
            <button class=""dialog-url-button"" onclick=""closeUrlDialog()"">确定</button>
        </div>
    </dialog>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/netcss/js/SaveDraft.js?001\"></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/netcss/js/fileupload/url-common.js?021\"></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/netcss/js/fileupload/post-editor.js?8\" defer></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/netcss/js/fileupload/image-uploader.js\" defer></script>");
    Response.Write(strhtml);
    Response.Write(WapTool.showDown(wmlVo));
%>