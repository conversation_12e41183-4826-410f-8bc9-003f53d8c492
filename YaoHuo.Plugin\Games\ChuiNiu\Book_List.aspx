﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_List.aspx.cs" Inherits="YaoHuo.Plugin.Games.ChuiNiu.Book_List" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    string title = "我的吹牛记录";
    
    string headHtml = "<meta charset=\"UTF-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\">\n<link rel=\"stylesheet\" href=\"/games/chuiniu/tailwind.min.css?2\"/> <link rel=\"stylesheet\" href=\"/games/chuiniu/styles.css?25\"/>\n<link rel=\"stylesheet\" href=\"//lf6-cdn-tos.bytecdntp.com/cdn/expire-1-y/font-awesome/6.0.0/css/all.min.css\"/>";
    Response.Write(WapTool.showTop(title, wmlVo, false, headHtml));
    
    // 顶部导航栏
    strhtml.Append("<header class=\"bg-gradient-to-r from-teal-500 to-teal-700 shadow-md p-4 flex items-center sticky top-0 z-20 text-white\">");
    strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/index.aspx\" class=\"text-white mr-4 hover:opacity-80 transition-opacity\">");
    strhtml.Append("<i class=\"fas fa-arrow-left text-lg\"></i>");
    strhtml.Append("</a>");
    strhtml.Append("<h1 class=\"text-xl font-semibold\">" + title + "</h1>");
    strhtml.Append("</header>");
    
    // 主要内容
    strhtml.Append("<main class=\"p-4 pb-24 max-w-lg mx-auto\">");
    
    // 选项卡导航 - 将按钮改为链接但保持外观
    strhtml.Append("<div class=\"flex border-b border-gray-200 mb-5 bg-white rounded-t-lg shadow-sm\">");
    if (type == "0") {
        strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/book_list.aspx?type=0&touserid=" + this.touserid + "\" class=\"tab-button tab-active flex-1 py-3 px-4 text-sm font-medium text-center transition-colors duration-200 ease-in-out\">我发起的</a>");
        strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/book_list.aspx?type=1&touserid=" + this.touserid + "\" class=\"tab-button flex-1 py-3 px-4 text-sm font-medium text-center text-gray-500 hover:text-teal-600 transition-colors duration-200 ease-in-out\">我应战的</a>");
    } else {
        strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/book_list.aspx?type=0&touserid=" + this.touserid + "\" class=\"tab-button flex-1 py-3 px-4 text-sm font-medium text-center text-gray-500 hover:text-teal-600 transition-colors duration-200 ease-in-out\">我发起的</a>");
        strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/book_list.aspx?type=1&touserid=" + this.touserid + "\" class=\"tab-button tab-active flex-1 py-3 px-4 text-sm font-medium text-center transition-colors duration-200 ease-in-out\">我应战的</a>");
    }
    strhtml.Append("</div>");
    
    // 我发起的列表（type=0）
    string createdListClass = (type == "0") ? "" : "hidden";
    strhtml.Append("<section id=\"created-list\" class=\"space-y-4 " + createdListClass + "\">");
    strhtml.Append("<div class=\"bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden divide-y divide-gray-100\">");
    
    if (type == "0") {
        // 显示"我发起的"内容
        if (listVo != null && listVo.Count > 0) {
            for (int i = 0; i < listVo.Count; i++) {
                string statusClass = "";
                string statusText = "";
                
                if (listVo[i].state == 0) {
                    statusClass = "bg-yellow-100 text-yellow-700";
                    statusText = "进行中";
                } else if (listVo[i].state == 2) {
                    statusClass = "bg-green-100 text-green-700";
                    statusText = "已获胜";
                } else if (listVo[i].state == 1) {
                    statusClass = "bg-red-100 text-red-700";
                    statusText = "已失败";
                } else if (listVo[i].state == 3) {
                    statusClass = "bg-gray-100 text-gray-700";
                    statusText = "平局";
                }
                
                strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/book_view.aspx?type=" + this.type + "&amp;touserid=" + this.touserid + "&amp;id=" + listVo[i].id + "\" class=\"challenge-item block p-4 hover:bg-gray-50 transition duration-150\">");
                strhtml.Append("<div class=\"flex justify-between items-center mb-1\">");
                strhtml.Append("<p class=\"font-semibold text-gray-800 truncate w-3/4\">ID:" + listVo[i].id); // 假设这里有标题或问题字段
                strhtml.Append("<span class=\"inline-flex items-center text-sm font-medium text-amber-600 ml-1\">");
                strhtml.Append(listVo[i].myMoney + " <i class=\"fas fa-coins text-amber-500 ml-1 text-xs\"></i>");
                strhtml.Append("</span>");
                strhtml.Append("</p>");
                strhtml.Append("<span class=\"text-xs font-medium px-2 py-1 rounded-full " + statusClass + "\">" + statusText + "</span>");
                strhtml.Append("</div>");
                
                if (listVo[i].state != 0) {
                    strhtml.Append("<div class=\"flex items-center mt-1 text-sm text-gray-600\">");
                    strhtml.Append("<div class=\"bg-red-100 text-red-600 rounded-full w-5 h-5 flex items-center justify-center mr-1\">");
                    strhtml.Append("<i class=\"fas fa-user text-xs\"></i>");
                    strhtml.Append("</div>");
                    strhtml.Append("<span>应战者: " + listVo[i].winNickname + "</span>");
                    strhtml.Append("</div>");
                } else {
                    strhtml.Append("<div class=\"flex items-center mt-1 text-sm text-gray-600\">");
                    strhtml.Append("<span class=\"italic\">等待应战...</span>");
                    strhtml.Append("</div>");
                }
                
                // 这里假设有时间字段，如果没有可以根据实际情况修改
                strhtml.Append("<p class=\"text-xs text-gray-400 mt-2\">发布时间: " + listVo[i].addtime + "</p>");
                strhtml.Append("</a>");
            }
        } else {
            // 空状态显示
            strhtml.Append("<div class=\"p-8 text-center text-gray-500\">");
            strhtml.Append("<i class=\"fas fa-list-alt text-4xl mb-3 text-gray-400\"></i>");
            strhtml.Append("<p class=\"font-medium\">你还没有发起过任何挑战</p>");
            strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/add.aspx\" class=\"mt-3 inline-block px-4 py-2 rounded-lg gradient-btn text-white text-sm\">");
            strhtml.Append("<i class=\"fas fa-plus-circle mr-1\"></i>发起挑战");
            strhtml.Append("</a>");
            strhtml.Append("</div>");
        }
    }
    
    strhtml.Append("</div>");
    
    // 我发起的列表（type=0）的分页部分
    if (type == "0" && listVo != null && listVo.Count > 0) {
        // 解析linkURL获取页码信息 - 直接在服务器端设置初始值
        string pageInfoText = "第 1/1 页";
        string nextUrl = "#";
        string prevUrl = "#";
        bool hasNext = false;
        bool hasPrev = false;
        bool showPagination = false; // 添加一个标志，决定是否显示分页
        
        // 从linkURL中提取页码信息
        if (!string.IsNullOrEmpty(linkURL)) {
            // 查找"第 1/2 页"这样的文本
            System.Text.RegularExpressions.Match match = System.Text.RegularExpressions.Regex.Match(linkURL, "第\\s*(\\d+)/(\\d+)\\s*页");
            if (match.Success) {
                pageInfoText = "第 " + match.Groups[1].Value + "/" + match.Groups[2].Value + " 页";
                // 只有当总页数大于1时才显示分页控件
                showPagination = match.Groups[2].Value != "1";
            }
            
            // 查找下一页链接
            match = System.Text.RegularExpressions.Regex.Match(linkURL, "<a[^>]*href=\"([^\"]*)\"[^>]*>下一页</a>");
            if (match.Success) {
                nextUrl = match.Groups[1].Value;
                hasNext = true;
            }
            
            // 查找上一页链接
            match = System.Text.RegularExpressions.Regex.Match(linkURL, "<a[^>]*href=\"([^\"]*)\"[^>]*>上一页</a>");
            if (match.Success) {
                prevUrl = match.Groups[1].Value;
                hasPrev = true;
            }
        }
        
        // 隐藏的原始分页控件
        strhtml.Append("<div id=\"original-pager-type0\" class=\"original-pager\">");
        strhtml.Append(linkURL);
        strhtml.Append("</div>");
        
        // 只有当需要显示分页时才显示美化的分页控件
        if (showPagination) {
            strhtml.Append("<div id=\"modern-pager-type0\" class=\"flex justify-between items-center pt-4\">");
            
            // 上一页按钮
            if (hasPrev) {
                strhtml.Append("<a href=\"" + prevUrl + "\" class=\"flex items-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 transition duration-150 ease-in-out\">");
            } else {
                strhtml.Append("<a href=\"#\" class=\"flex items-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 transition duration-150 ease-in-out opacity-50 cursor-not-allowed\">");
            }
            strhtml.Append("<i class=\"fas fa-chevron-left text-xs\"></i>");
            strhtml.Append("</a>");
            
            // 页码信息
            strhtml.Append("<span id=\"page-info-type0\" class=\"text-sm text-gray-500\">" + pageInfoText + "</span>");
            
            // 下一页按钮
            if (hasNext) {
                strhtml.Append("<a href=\"" + nextUrl + "\" class=\"flex items-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 transition duration-150 ease-in-out\">");
            } else {
                strhtml.Append("<a href=\"#\" class=\"flex items-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 transition duration-150 ease-in-out opacity-50 cursor-not-allowed\">");
            }
            strhtml.Append("<i class=\"fas fa-chevron-right text-xs\"></i>");
            strhtml.Append("</a>");
            
            strhtml.Append("</div>");
        }
    }
    
    strhtml.Append("</section>");
    
    // 我应战的列表（type=1）
    string respondedListClass = (type == "1") ? "" : "hidden";
    strhtml.Append("<section id=\"responded-list\" class=\"space-y-4 " + respondedListClass + "\">");
    strhtml.Append("<div class=\"bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden divide-y divide-gray-100\">");
    
    if (type == "1") {
        // 显示"我应战的"内容
        if (listVo != null && listVo.Count > 0) {
            for (int i = 0; i < listVo.Count; i++) {
                string statusClass = "";
                string statusText = "";
                
                if (listVo[i].state == 0) {
                    statusClass = "bg-yellow-100 text-yellow-700";
                    statusText = "进行中";
                } else if (listVo[i].state == 1) {
                    statusClass = "bg-green-100 text-green-700";
                    statusText = "已获胜";
                } else if (listVo[i].state == 2) {
                    statusClass = "bg-red-100 text-red-700";
                    statusText = "已失败";
                } else if (listVo[i].state == 3) {
                    statusClass = "bg-gray-100 text-gray-700";
                    statusText = "平局";
                }
                
                strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/book_view.aspx?type=" + this.type + "&amp;touserid=" + this.touserid + "&amp;id=" + listVo[i].id + "\" class=\"challenge-item block p-4 hover:bg-gray-50 transition duration-150\">");
                strhtml.Append("<div class=\"flex justify-between items-center mb-1\">");
                strhtml.Append("<p class=\"font-semibold text-gray-800 truncate w-3/4\">ID:" + listVo[i].id); // 假设这里有标题或问题字段
                strhtml.Append("<span class=\"inline-flex items-center text-sm font-medium text-amber-600 ml-1\">");
                strhtml.Append(listVo[i].myMoney + " <i class=\"fas fa-coins text-amber-500 ml-1 text-xs\"></i>");
                strhtml.Append("</span>");
                strhtml.Append("</p>");
                strhtml.Append("<span class=\"text-xs font-medium px-2 py-1 rounded-full " + statusClass + "\">" + statusText + "</span>");
                strhtml.Append("</div>");
                
                strhtml.Append("<div class=\"flex items-center mt-1 text-sm text-gray-600\">");
                strhtml.Append("<div class=\"bg-green-100 text-green-600 rounded-full w-5 h-5 flex items-center justify-center mr-1\">");
                strhtml.Append("<i class=\"fas fa-user text-xs\"></i>");
                strhtml.Append("</div>");
                strhtml.Append("<span>发起者: " + listVo[i].nickName + "</span>");
                strhtml.Append("</div>");
                
                // 这里假设有时间字段，如果没有可以根据实际情况修改
                strhtml.Append("<p class=\"text-xs text-gray-400 mt-2\">应战时间: " + listVo[i].winTime + "</p>");
                strhtml.Append("</a>");
            }
        } else {
            // 空状态显示
            strhtml.Append("<div class=\"p-8 text-center text-gray-500\">");
            strhtml.Append("<i class=\"fas fa-list-alt text-4xl mb-3 text-gray-400\"></i>");
            strhtml.Append("<p class=\"font-medium\">你还没有应战过任何挑战</p>");
            strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/index.aspx\" class=\"mt-3 inline-block px-4 py-2 rounded-lg gradient-btn text-white text-sm\">");
            strhtml.Append("<i class=\"fas fa-fire-alt mr-1\"></i>浏览挑战");
            strhtml.Append("</a>");
            strhtml.Append("</div>");
        }
    }
    
    strhtml.Append("</div>");
    
    // 我应战的列表（type=1）的分页部分
    if (type == "1" && listVo != null && listVo.Count > 0) {
        // 解析linkURL获取页码信息 - 直接在服务器端设置初始值
        string pageInfoText = "第 1/1 页";
        string nextUrl = "#";
        string prevUrl = "#";
        bool hasNext = false;
        bool hasPrev = false;
        bool showPagination = false; // 添加一个标志，决定是否显示分页
        
        // 从linkURL中提取页码信息
        if (!string.IsNullOrEmpty(linkURL)) {
            // 查找"第 1/2 页"这样的文本
            System.Text.RegularExpressions.Match match = System.Text.RegularExpressions.Regex.Match(linkURL, "第\\s*(\\d+)/(\\d+)\\s*页");
            if (match.Success) {
                pageInfoText = "第 " + match.Groups[1].Value + "/" + match.Groups[2].Value + " 页";
                // 只有当总页数大于1时才显示分页控件
                showPagination = match.Groups[2].Value != "1";
            }
            
            // 查找下一页链接
            match = System.Text.RegularExpressions.Regex.Match(linkURL, "<a[^>]*href=\"([^\"]*)\"[^>]*>下一页</a>");
            if (match.Success) {
                nextUrl = match.Groups[1].Value;
                hasNext = true;
            }
            
            // 查找上一页链接
            match = System.Text.RegularExpressions.Regex.Match(linkURL, "<a[^>]*href=\"([^\"]*)\"[^>]*>上一页</a>");
            if (match.Success) {
                prevUrl = match.Groups[1].Value;
                hasPrev = true;
            }
        }
        
        // 隐藏的原始分页控件
        strhtml.Append("<div id=\"original-pager-type1\" class=\"original-pager\">");
        strhtml.Append(linkURL);
        strhtml.Append("</div>");
        
        // 只有当需要显示分页时才显示美化的分页控件
        if (showPagination) {
            strhtml.Append("<div id=\"modern-pager-type1\" class=\"flex justify-between items-center pt-4\">");
            
            // 上一页按钮
            if (hasPrev) {
                strhtml.Append("<a href=\"" + prevUrl + "\" class=\"flex items-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 transition duration-150 ease-in-out\">");
            } else {
                strhtml.Append("<a href=\"#\" class=\"flex items-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 transition duration-150 ease-in-out opacity-50 cursor-not-allowed\">");
            }
            strhtml.Append("<i class=\"fas fa-chevron-left text-xs\"></i>");
            strhtml.Append("</a>");
            
            // 页码信息
            strhtml.Append("<span id=\"page-info-type1\" class=\"text-sm text-gray-500\">" + pageInfoText + "</span>");
            
            // 下一页按钮
            if (hasNext) {
                strhtml.Append("<a href=\"" + nextUrl + "\" class=\"flex items-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 transition duration-150 ease-in-out\">");
            } else {
                strhtml.Append("<a href=\"#\" class=\"flex items-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 transition duration-150 ease-in-out opacity-50 cursor-not-allowed\">");
            }
            strhtml.Append("<i class=\"fas fa-chevron-right text-xs\"></i>");
            strhtml.Append("</a>");
            
        strhtml.Append("</div>");
    }
    }
    
    strhtml.Append("</section>");
    
    strhtml.Append("</main>");
    strhtml.Append("</body>");
    strhtml.Append("</html>");
    
    // 输出整个页面
    Response.Write(strhtml.ToString());
%>