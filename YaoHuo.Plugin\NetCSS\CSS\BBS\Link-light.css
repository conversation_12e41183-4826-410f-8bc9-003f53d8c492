@media screen and (min-width: 1441px) {
body{width:100vw;height:100vh;display:flex;justify-content:center;align-items:center;background:#f4f6f7;margin:0;padding:0;text-shadow:#acacac 0 0 1px}
.wrapper{max-width:90%;max-height:90%;width:700px;height:400px;background:#fff;border:1px solid #e6e8eb;box-shadow:rgba(0,0,0,.05) 0 2px 10px 0;border-radius:16px;box-sizing:border-box;padding:70px 70px 40px}
.url{color:#fc5531}
a[type=button]{position:absolute;text-decoration:none;bottom:24px;right:24px;font-size:14px;color:#fc5531;border:1px solid #fc5531;display:inline-block;box-sizing:border-box;padding:6px 18px;border-radius:18px;margin-left:8px}
a{outline:0;text-decoration:none;color:#4a90e2}
.content__note{font-size:13px;color:#959da6;line-height:18px}
.content-btns{width:100%;height:28px;display:flex;justify-content:flex-end;align-items:center}
.content__btn{width:100px;height:28px;line-height:28px;text-align:center;border:1px solid #c8cacc;border-radius:5px;font-size:13px;color:#2e3033;cursor:pointer}
.content__btn.blue{color:#fff;border:none;margin-right:10px;background-image:linear-gradient(270deg,#60abfc 0,#459afa 100%)}
.url{font-size:14px;color:#2E3033;padding-bottom:22px;margin-bottom:15px;border-bottom:1px solid #e6e8eb;word-break:break-all;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:6;overflow:hidden}
.content__title{font-size:16px;color:#2e3033;line-height:22px;margin-bottom:7px}
.content-wrapper{height:290px;display:flex;justify-content:space-between;flex-direction:column}
.content-box{display:block}
}
@media screen and (max-width: 1440px) {
body { width: 100vw; height: 100vh; display: flex; justify-content: center; align-items: center; margin: 0px; padding: 0px; }
.wrapper {text-align: center; width: 100vw; height: 90vh; font-size: 3.733vw; line-height: 5.4vw; box-sizing: border-box; padding: 10vh 8vw; display: flex; flex-direction: column; justify-content: space-between; }
a[type="button"] { position: absolute; text-decoration: none; bottom: 24px; right: 24px; font-size: 14px; color: rgb(252, 85, 49); border: 1px solid rgb(252, 85, 49); display: inline-block; box-sizing: border-box; padding: 6px 18px; border-radius: 18px; margin-left: 8px; }
a { outline: 0px; text-decoration: none; color: rgb(74, 144, 226); }
.content__note { font-size: 3.733vw; line-height: 5.4vw; color: rgb(142, 142, 147); }
.content__btn { display: block; width: 42.7vw; height: 10.667vw; line-height: 10.667vw; border-radius:7px; font-size: 4.533vw; text-align: center; background: rgb(237, 241, 245); color: rgb(23, 126, 230); margin-left: auto; margin-right: auto; }
.content-wrapper,.wrapper,.content-box{    z-index: 2;background:#fff;}
.content__btn.blue { background: rgb(23, 126, 230); color: rgb(255, 255, 255); margin-bottom: 2.61vw; }
.url { font-size: 4.533vw; color: rgb(46, 48, 51); margin: 0.6vh 0px 3.7vh; line-height: 5.4vw; word-break: break-all; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 6; overflow: hidden; }
.content-btns{    z-index: -1;position: absolute; bottom: 15%; left: 28.65vw;}
.content__title { color: rgb(46, 48, 51); font-size: 5.867vw; line-height: 1vw; }
}