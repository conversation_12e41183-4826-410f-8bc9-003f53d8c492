﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_View_modadd.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Book_View_modadd" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    StringBuilder strhtml = new StringBuilder();
    Response.Write(WapTool.showTop(this.GetLang("续帖内容|續貼內容|Continued posted"), wmlVo));
    strhtml.Append("<div class=\"title\">" + this.GetLang("续写帖子|續貼內容|Continued posted") + "</div>");
    
    if (!string.IsNullOrEmpty(this.ERROR))
    {
        strhtml.Append("<div class=\"tip\">" + this.ERROR + "</div>");
    }
    else if (this.INFO == "OK")
    {
        strhtml.Append("<div class=\"tip\"><b>");
        strhtml.Append(this.GetLang("续帖成功！|續貼成功！|Successfully modified"));
        strhtml.Append("<a href=\"" + this.http_start + "bbs-" + id + ".html\">返回主题</a>");
        strhtml.Append("</b></div>");
    }
    else if (this.INFO == "NULL")
    {
        strhtml.Append("<div class=\"tip\"><b>");
        strhtml.Append("<b>标题最少" + this.titlemax + "字，内容最少" + this.contentmax + "字！</b>");
        strhtml.Append("</b></div>");
    }
    else if (this.hasPermission)  // 只有在有权限时才显示表单
    {
        strhtml.Append("<div class=\"content\">");
        // 保证formToken有值
        string tokenKey = "formTokenList_modadd_" + id;
        string formToken = null;
        var tokenList = Session[tokenKey] as System.Collections.Generic.List<string>;
        if (tokenList != null && tokenList.Count > 0)
            formToken = tokenList[tokenList.Count - 1];
        else
            formToken = GenerateFormToken(tokenKey);
        strhtml.Append("<form name=\"go\" action=\"" + this.http_start + "bbs/book_view_modadd.aspx\" method=\"post\">");
        strhtml.Append("<input type=\"text\" style=\"display:none;\" name=\"book_title\" value=\"" + bbsVo.book_title + "\"/>");
        strhtml.Append("<script> function adjustTextareaHeight(textarea) { if (textarea.scrollHeight > textarea.offsetHeight) { textarea.style.height = textarea.scrollHeight + 'px'; } } </script>");
        strhtml.Append("<div class='centered-container'>");
        strhtml.Append("<textarea name=\"book_content\" oninput=\"adjustTextareaHeight(this)\" minlength=\"15\" required=\"required\" placeholder=\"请输入你要续写的内容\" rows=\"12\" style=\"width:98.6%;margin-bottom:5px;min-height:70vh;\">" + this.book_content + "</textarea>");
        strhtml.Append("</div>");
        strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"id\" value=\"" + id + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"lpage\" value=\"" + lpage + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"token\" value=\"" + formToken + "\"/>");
        strhtml.Append("<input type=\"submit\" name=\"bt\" value=\"" + this.GetLang("提 交|提 交|Submit") + "\"/>");
        strhtml.Append("</form>");
        strhtml.Append("</div>");
    }

    string isWebHtml = this.ShowWEB_view(this.classid);
    if (isWebHtml != "")
    {
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml, wmlVo).Replace("[view]", strhtml.ToString()));
        Response.End();
    }

    strhtml.Append("<div class=\"btBox\"><div class=\"bt3\">");
    strhtml.Append("<a href=\"" + this.http_start + "bbs-" + id + ".html\">返回主题</a>");
    strhtml.Append("<a href=\"" + this.http_start + "bbs/book_list.aspx?action=class&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;page=" + this.lpage + "\">" + this.GetLang("返回列表|返回列表|Back to list") + "</a> ");
    if (this.hasPermission)  // 只有在有权限时才显示管理链接
    {
        strhtml.Append("<a href=\"" + this.http_start + "bbs/book_view_admin.aspx?siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;id=" + this.id + "\">" + this.GetLang("返回管理|返回上級|Back to admin") + "</a> ");
    }
    strhtml.Append("</div></div>");
    
    Response.Write(strhtml);
    Response.Write(WapTool.showDown(wmlVo));
%>