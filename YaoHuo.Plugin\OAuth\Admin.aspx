﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Admin.aspx.cs" Inherits="YaoHuo.Plugin.OAuth.Admin" Async="true" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth 应用管理</title>

    <!-- 本地 Tailwind CSS -->
    <link href="/Template/CSS/output.css" rel="stylesheet">

    <!-- 本地 Lucide Icons -->
    <script src="/NetCSS/JS/BBS/Lucide.0.511.0.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- 固定顶部导航栏 -->
        <header class="header">
            <div class="header-content">
                <div class="header-icon" onclick="window.location.href='/OAuth/Index.aspx'">
                    <i data-lucide="arrow-left" class="w-6 h-6"></i>
                </div>
                <div class="header-title">OAuth 应用管理</div>
                <div class="header-actions-right">
                    <% if (string.IsNullOrEmpty(Action)) { %>
                    <div class="header-icon" onclick="window.location.href='/OAuth/Admin.aspx?action=addview'" title="新增应用">
                        <i data-lucide="plus" class="w-5 h-5"></i>
                    </div>
                    <% } %>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <div class="main-content">
            <%
            // 显示页面内容（来自后端处理）
            if (!string.IsNullOrEmpty(strhtml.ToString()))
            {
                if (Action == "addview")
                {
                    // 新增应用表单
                    %>
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <i data-lucide="plus-circle" class="card-icon"></i>
                                新增授权应用
                            </h2>
                        </div>
                        <div class="card-body">
                            <form method="post" action="?action=add">
                                <%
                                string csrfToken = this.GenerateFormToken("OAuth_Admin_Form");
                                %>
                                <input type="hidden" name="__CSRFToken" value="<%=csrfToken%>" />

                                <div class="form-group">
                                    <label class="form-label required">应用名称</label>
                                    <input type="text" name="name" class="form-input" placeholder="请输入应用名称" required />
                                </div>

                                <div class="form-group">
                                    <label class="form-label">应用说明</label>
                                    <textarea name="remark" class="form-input" rows="3" placeholder="请输入应用说明（可选）"></textarea>
                                </div>

                                <div class="form-group">
                                    <label class="form-label required">重定向URL白名单</label>
                                    <input type="text" name="redirectUris" class="form-input" placeholder="多个URL用分号分隔，如：https://example.com/callback;https://app.com/auth" required />
                                    <div class="form-hint">
                                        <i data-lucide="info" class="w-4 h-4 inline mr-1"></i>
                                        支持多个URL，用分号分隔。建议使用HTTPS协议确保安全。
                                    </div>
                                </div>

                                <div class="grid-2">
                                    <button type="button" class="btn btn-outline" onclick="history.back()">
                                        <i data-lucide="arrow-left" class="w-4 h-4 mr-1"></i>
                                        返回
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i data-lucide="check" class="w-4 h-4 mr-1"></i>
                                        创建应用
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <%
                }
                else
                {
                    // 其他操作的消息显示
                    string htmlContent = strhtml.ToString();
                    if (htmlContent.Contains("新增成功") || htmlContent.Contains("删除成功"))
                    {
                        %>
                        <div class="message success">
                            <i data-lucide="check-circle" class="w-4 h-4 inline mr-2"></i>
                            <%=htmlContent.Replace("<div class='tip'>", "").Replace("</div>", "").Replace("<div class='title'>", "").Replace("<strong>", "<strong>").Replace("</strong>", "</strong>")%>
                        </div>
                        <%
                    }
                    else if (htmlContent.Contains("失败") || htmlContent.Contains("错误"))
                    {
                        %>
                        <div class="message error">
                            <i data-lucide="x-circle" class="w-4 h-4 inline mr-2"></i>
                            <%=htmlContent.Replace("<div class='tip'>", "").Replace("</div>", "").Replace("<div class='title'>", "")%>
                        </div>
                        <%
                    }
                    else if (htmlContent.Contains("⚠️"))
                    {
                        %>
                        <div class="message warning">
                            <i data-lucide="alert-triangle" class="w-4 h-4 inline mr-2"></i>
                            <%=htmlContent.Replace("<div class='tip'>", "").Replace("</div>", "").Replace("<div class='title'>", "")%>
                        </div>
                        <%
                    }
                }
            }

            if (string.IsNullOrEmpty(Action))
            {
                // 应用列表显示
                %>
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i data-lucide="shield-check" class="card-icon"></i>
                            授权应用列表
                        </h2>
                    </div>
                    <div class="card-body">
                        <%
                        if (listVo != null && listVo.Count > 0)
                        {
                            for (int i = 0; i < listVo.Count; i++)
                            {
                                index = index + kk;
                                %>
                                <div class="friend-item mb-4">
                                    <div class="flex-1">
                                        <div class="flex items-center justify-between mb-2">
                                            <h3 class="text-lg font-medium text-text-primary">
                                                <i data-lucide="app-window" class="w-4 h-4 inline mr-2"></i>
                                                <%=listVo[i].AppName%>
                                            </h3>
                                            <span class="text-xs text-text-secondary">
                                                #<%=index%> · <%=listVo[i].CreatedAt.ToString("yyyy-MM-dd HH:mm")%>
                                            </span>
                                        </div>

                                        <% if (!string.IsNullOrEmpty(listVo[i].AppDescription)) { %>
                                        <p class="text-sm text-text-secondary mb-3">
                                            <i data-lucide="file-text" class="w-4 h-4 inline mr-1"></i>
                                            <%=listVo[i].AppDescription%>
                                        </p>
                                        <% } %>

                                        <div class="space-y-2 mb-3">
                                            <!-- AppID -->
                                            <div class="flex items-center text-sm">
                                                <span class="text-text-secondary w-16">AppID:</span>
                                                <code class="bg-gray-100 px-2 py-1 rounded text-xs font-mono"><%=listVo[i].AppId%></code>
                                            </div>

                                            <!-- 密钥状态 -->
                                            <div class="flex items-center text-sm">
                                                <span class="text-text-secondary w-16">密钥:</span>
                                                <%
                                                if (!string.IsNullOrEmpty(listVo[i].AppKeyHash))
                                                {
                                                    %>
                                                    <span class="text-success flex items-center">
                                                        <i data-lucide="shield-check" class="w-4 h-4 mr-1"></i>
                                                        已加密存储
                                                    </span>
                                                    <%
                                                }
                                                else
                                                {
                                                    // 新架构中不再有明文AppKey，这个分支应该不会执行
                                                    %>
                                                    <span class="text-warning flex items-center">
                                                        <i data-lucide="alert-triangle" class="w-4 h-4 mr-1"></i>
                                                        密钥状态异常
                                                    </span>
                                                    <%
                                                }

                                                %>
                                            </div>

                                            <!-- 重定向URL -->
                                            <div class="flex items-start text-sm">
                                                <span class="text-text-secondary w-16 mt-0.5">重定向:</span>
                                                <div class="flex-1">
                                                    <%
                                                    var redirectUris = listVo[i].RedirectUris ?? "未设置";
                                                    if (redirectUris != "未设置" && redirectUris.Contains(";"))
                                                    {
                                                        var uris = redirectUris.Split(';');
                                                        for (int j = 0; j < uris.Length; j++)
                                                        {
                                                            if (!string.IsNullOrEmpty(uris[j].Trim()))
                                                            {
                                                                %>
                                                                <div class="text-xs text-text-tertiary mb-1">
                                                                    <i data-lucide="link" class="w-3 h-3 inline mr-1"></i>
                                                                    <%=uris[j].Trim()%>
                                                                </div>
                                                                <%
                                                            }
                                                        }
                                                    }
                                                    else
                                                    {
                                                        %>
                                                        <span class="text-xs text-text-tertiary">
                                                            <i data-lucide="link" class="w-3 h-3 inline mr-1"></i>
                                                            <%=redirectUris%>
                                                        </span>
                                                        <%
                                                    }
                                                    %>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 操作按钮 -->
                                        <div class="flex justify-end">
                                            <%
                                            var deleteToken = this.GenerateFormToken("OAuth_Index_Delete");
                                            %>
                                            <button type="button" class="btn btn-outline text-danger border-danger hover:bg-danger hover:text-white"
                                                    onclick="confirmDelete('<%=listVo[i].AppId%>', '<%=listVo[i].AppName%>', '<%=deleteToken%>')">
                                                <i data-lucide="trash-2" class="w-4 h-4 mr-1"></i>
                                                删除
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <%
                            }
                        }
                        else
                        {
                            %>
                            <div class="text-center py-8">
                                <i data-lucide="inbox" class="w-12 h-12 text-text-light mx-auto mb-3"></i>
                                <p class="text-text-secondary">暂无授权应用</p>
                                <button type="button" class="btn btn-primary mt-3" onclick="window.location.href='/OAuth/Admin.aspx?action=addview'">
                                    <i data-lucide="plus" class="w-4 h-4 mr-1"></i>
                                    创建第一个应用
                                </button>
                            </div>
                            <%
                        }
                        %>
                    </div>
                </div>
                <%
            }
            %>
        </div>
    </div>

    <!-- 确认删除对话框 -->
    <div id="deleteConfirmModal" class="confirm-overlay" style="display: none;">
        <div class="confirm-content">
            <div class="confirm-title">
                <i data-lucide="alert-triangle" class="w-5 h-5 inline mr-2 text-warning"></i>
                确认删除
            </div>
            <div class="confirm-message" id="deleteMessage">
                确定要删除此应用吗？删除后无法恢复。
            </div>
            <div class="confirm-actions">
                <button type="button" class="btn btn-outline" onclick="closeDeleteModal()">
                    <i data-lucide="x" class="w-4 h-4 mr-1"></i>
                    取消
                </button>
                <button type="button" class="btn btn-destructive" id="confirmDeleteBtn">
                    <i data-lucide="trash-2" class="w-4 h-4 mr-1"></i>
                    确认删除
                </button>
            </div>
        </div>
    </div>

    <!-- 密钥显示对话框 -->
    <div id="keyDisplayModal" class="confirm-overlay" style="display: none;">
        <div class="confirm-content">
            <div class="confirm-title">
                <i data-lucide="key" class="w-5 h-5 inline mr-2 text-primary"></i>
                应用密钥
            </div>
            <div class="confirm-message">
                <div class="mb-4">
                    <div class="text-sm text-text-secondary mb-2">完整密钥：</div>
                    <div class="bg-gray-100 p-3 rounded font-mono text-sm break-all" id="fullKeyDisplay"></div>
                </div>
                <div class="text-warning text-xs">
                    <i data-lucide="alert-triangle" class="w-3 h-3 inline mr-1"></i>
                    请妥善保管，不要泄露给无关人员！此操作已被记录。
                </div>
            </div>
            <div class="confirm-actions">
                <button type="button" class="btn btn-outline" onclick="closeKeyModal()">
                    <i data-lucide="x" class="w-4 h-4 mr-1"></i>
                    关闭
                </button>
                <button type="button" class="btn btn-primary" id="copyKeyBtn">
                    <i data-lucide="clipboard" class="w-4 h-4 mr-1"></i>
                    复制密钥
                </button>
            </div>
        </div>
    </div>



    <script>
        // 全局变量
        let currentAppKey = '';
        let deleteUrl = '';

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化 Lucide 图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }

            // 绑定复制按钮事件
            const copyKeyBtn = document.getElementById('copyKeyBtn');
            if (copyKeyBtn) {
                copyKeyBtn.addEventListener('click', function() {
                    copyToClipboard(currentAppKey);
                });
            }

            // 绑定确认删除按钮事件
            const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
            if (confirmDeleteBtn) {
                confirmDeleteBtn.addEventListener('click', function() {
                    if (deleteUrl) {
                        window.location.href = deleteUrl;
                    }
                });
            }


        });

        // 显示完整密钥
        function showFullKey(appId, appKey) {
            currentAppKey = appKey;
            document.getElementById('fullKeyDisplay').textContent = appKey;
            document.getElementById('keyDisplayModal').style.display = 'flex';

            // 记录查看操作
            console.log('[OAuth] 管理员查看密钥: AppID=' + appId);

            // 重新初始化图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }

        // 关闭密钥显示对话框
        function closeKeyModal() {
            document.getElementById('keyDisplayModal').style.display = 'none';
            currentAppKey = '';
        }

        // 确认删除应用
        function confirmDelete(appId, appName, token) {
            deleteUrl = '/OAuth/Admin.aspx?action=del&aid=' + appId + '&token=' + token;
            document.getElementById('deleteMessage').innerHTML =
                '确定要删除应用 "<strong>' + appName + '</strong>" 吗？<br/><br/>' +
                '<span class="text-danger text-xs">⚠️ 删除后无法恢复，相关的授权令牌也将失效。</span>';
            document.getElementById('deleteConfirmModal').style.display = 'flex';

            // 重新初始化图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }

        // 关闭删除确认对话框
        function closeDeleteModal() {
            document.getElementById('deleteConfirmModal').style.display = 'none';
            deleteUrl = '';
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            if (navigator.clipboard && window.isSecureContext) {
                // 现代浏览器
                navigator.clipboard.writeText(text).then(function() {
                    showToast('密钥已复制到剪贴板！', 'success');
                    closeKeyModal();
                }).catch(function(err) {
                    console.error('复制失败:', err);
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                // 兼容旧浏览器
                fallbackCopyTextToClipboard(text);
            }
        }

        // 兼容旧浏览器的复制方法
        function fallbackCopyTextToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                var successful = document.execCommand('copy');
                if (successful) {
                    showToast('密钥已复制到剪贴板！', 'success');
                    closeKeyModal();
                } else {
                    showToast('复制失败，请手动复制密钥', 'error');
                }
            } catch (err) {
                console.error('复制失败:', err);
                showToast('复制失败，请手动复制密钥', 'error');
            }

            document.body.removeChild(textArea);
        }

        // 显示 Toast 提示
        function showToast(message, type = 'info') {
            // 创建 toast 容器（如果不存在）
            let container = document.getElementById('toast-container');
            if (!container) {
                container = document.createElement('div');
                container.id = 'toast-container';
                container.className = 'toast-container';
                document.body.appendChild(container);
            }

            // 创建 toast 元素
            const toast = document.createElement('div');
            toast.className = `toast-dynamic toast-dynamic-${type}`;

            // 设置图标
            let icon = 'info';
            if (type === 'success') icon = 'check-circle';
            else if (type === 'error') icon = 'x-circle';
            else if (type === 'warning') icon = 'alert-triangle';

            toast.innerHTML = `
                <i data-lucide="${icon}" class="w-4 h-4"></i>
                <span>${message}</span>
            `;

            container.appendChild(toast);

            // 初始化图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }

            // 显示动画
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (container.contains(toast)) {
                        container.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }



        // 点击对话框外部关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('confirm-overlay')) {
                if (e.target.id === 'deleteConfirmModal') {
                    closeDeleteModal();
                } else if (e.target.id === 'keyDisplayModal') {
                    closeKeyModal();
                }
            }
        });
    </script>
</body>
</html>