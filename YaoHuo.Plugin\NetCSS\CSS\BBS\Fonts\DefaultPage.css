:where([class^="ri-"])::before {
    content: "\f3c2";
}

body {
    background-color: #e8e8e8;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.search-container {
    position: relative;
}

.search-options {
    position: absolute;
    left: 0;
    right: 0;
    top: 100%;
    margin-top: 1px;
    background-color: white;
    border-radius: 0 0 8px 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 10;
}

    .search-options.active {
        display: block;
    }

    .search-options div {
        font-size: 16px;
        transition: background 0.15s;
    }

        .search-options div:hover {
            background: #f1f5f9;
        }

.group:focus-within, .group:hover {
    border-color: #378d8d;
}

.card-hover {
    transition: box-shadow 0.2s, background 0.2s, transform 0.15s;
    cursor: pointer;
}

    .card-hover:hover {
        background: #f9f9f9;
        box-shadow: 0 4px 16px rgba(55,141,141,0.13);
        transform: scale(1.025);
    }

.logo-grab, .logo-grab:hover {
    cursor: grab;
}

.bottom-nav-btn {
    transition: color 0.15s;
    border-radius: 12px;
}

    .bottom-nav-btn:hover {
        background: none;
        color: #222;
    }

    .bottom-nav-btn.active {
        color: #378d8d;
        font-weight: 500;
    }

.post-btn {
    transition: background 0.15s;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

    .post-btn:hover {
        background: #2f7777;
    }

a:focus, button:focus, .cursor-pointer:focus {
    outline: none;
    box-shadow: none;
}

.noselect {
    user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
}

.main-container {
    background: #f9f9f9;
    max-width: 720px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    box-shadow: 0 7px 17px #ccc;
}

.main-header, .main-footer {
    left: 0;
    right: 0;
    margin-left: auto;
    margin-right: auto;
    max-width: 720px;
}

@media (max-width: 720px) {
    .main-header, .main-footer, .main-container {
        max-width: 100vw;
    }
}

.search-btn {
    transition: background 0.15s, color 0.15s, box-shadow 0.15s, transform 0.08s;
}

    .search-btn:hover {
        background: #e0f2f2;
        color: #378d8d;
    }

    .search-btn:active {
        background: #c2e6e6;
        color: #246b6b;
        transform: scale(0.96);
    }

.ri-arrow-down-s-line {
    transition: transform 0.2s;
}

#searchType.active .ri-arrow-down-s-line {
    transform: rotate(180deg);
}

@media (max-width: 330px) {
    .grid.grid-cols-2 {
        gap: 0.5rem;
    }

        .grid.grid-cols-2 > a {
            padding: 0.5rem;
        }

            .grid.grid-cols-2 > a span {
                font-size: 0.8rem;
            }

            .grid.grid-cols-2 > a .w-10.h-10 {
                flex-shrink: 0;
            }

    .post-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }

        .post-btn .ri-edit-line {
            width: 0.8rem;
            height: 0.8rem;
        }

    .main-footer {
        padding-top: 0.3rem;
        padding-bottom: 0.3rem;
    }

        .main-footer .bottom-nav-btn span {
            margin-top: 0.1rem;
        }
}

.post-btn .ri-edit-line {
    width: 1rem;
    height: 1rem;
}