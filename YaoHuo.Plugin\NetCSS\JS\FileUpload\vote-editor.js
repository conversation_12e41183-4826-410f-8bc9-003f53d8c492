// vote-editor.js - 仅用于投票帖

// 投票相关函数
function updateVoteNum(delta) {
    var input = document.getElementById('numInput');
    var currentValue = parseInt(input.value) || 2;
    var newValue = Math.max(2, Math.min(9, currentValue + delta));
    
    // 更新显示的input
    input.value = newValue;
    
    // 更新隐藏的input
    var form = document.querySelector('form[name="f"]');
    if (form) {
        var hiddenInput = form.querySelector('input[name="num"]');
        if (hiddenInput) {
            hiddenInput.value = newValue;
        }
    }
    
    // 更新投票选项
    updateVoteOptions(newValue);
    updateButtonStates(newValue);
}

function updateVoteOptions(num) {
    // 找到投票选项容器
    var container = document.querySelector('.num-selector');
    if (!container) return;

    // 移除现有选项
    var existingOptions = document.querySelectorAll('.form-row');
    existingOptions.forEach(function(option) {
        option.remove();
    });

    // 找到提交按钮
    var submitBtn = document.querySelector('#submitBtn');
    var form = document.querySelector('form[name="f"]');
    
    // 添加新选项
    for (var i = 0; i < num; i++) {
        var row = document.createElement('div');
        row.className = 'form-row';
        row.style.marginBottom = '8px';
        
        row.innerHTML = `
            <div class="form-group half" style="display:flex;align-items:center;gap:8px;margin:0;">
                <div class="file-number">${i + 1}</div>
                <input type="text" minlength="1" maxlength="10" required="required" 
                       name="vote" class="form-control" style="margin:0"/>
            </div>`;
        
        // 在提交按钮前插入新选项
        form.insertBefore(row, submitBtn);
    }
}

function updateButtonStates(value) {
    const decreaseBtn = document.getElementById('decreaseBtn');
    const increaseBtn = document.getElementById('increaseBtn');
    
    decreaseBtn.disabled = value <= 2;
    increaseBtn.disabled = value >= 9;
    
    decreaseBtn.classList.toggle('disabled', value <= 2);
    increaseBtn.classList.toggle('disabled', value >= 9);
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    const initialValue = parseInt(document.getElementById('numInput')?.value || '2');
    updateButtonStates(initialValue);
});