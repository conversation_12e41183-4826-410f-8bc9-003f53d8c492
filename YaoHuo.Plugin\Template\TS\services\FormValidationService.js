export class FormValidationService {
    constructor() {
        this.validationConfigs = new Map();
        this.activeValidators = new Map();
    }
    static getInstance() {
        if (!FormValidationService.instance) {
            FormValidationService.instance = new FormValidationService();
        }
        return FormValidationService.instance;
    }
    registerForm(formId, config) {
        this.validationConfigs.set(formId, config);
    }
    initializeForm(formElement) {
        const formId = formElement.id;
        const config = this.validationConfigs.get(formId);
        if (!config) {
            console.warn(`No validation config found for form: ${formId}`);
            return;
        }
        this.activeValidators.set(formElement, new Map());
        Object.entries(config.fields).forEach(([fieldName, fieldConfig]) => {
            const fieldElement = formElement.querySelector(`[name="${fieldName}"]`);
            if (fieldElement) {
                this.setupFieldValidation(fieldElement, fieldConfig, formElement);
            }
        });
        this.setupFormSubmitValidation(formElement, config);
    }
    setupFieldValidation(fieldElement, fieldConfig, formElement) {
        const inputElement = fieldElement;
        if (fieldConfig.validateOnInput) {
            inputElement.addEventListener('input', () => {
                this.clearFieldError(inputElement);
            });
        }
        if (fieldConfig.validateOnBlur !== false) {
            inputElement.addEventListener('blur', () => {
                this.validateField(inputElement, fieldConfig);
            });
        }
        const validators = this.activeValidators.get(formElement);
        if (validators) {
            validators.set(inputElement.name, inputElement);
        }
    }
    setupFormSubmitValidation(formElement, config) {
        formElement.addEventListener('submit', (e) => {
            if (!this.validateForm(formElement)) {
                e.preventDefault();
                return;
            }
            if (config.submitButton) {
                this.showSubmitLoading(config.submitButton);
            }
        });
    }
    validateField(fieldElement, fieldConfig) {
        const value = fieldElement.value.trim();
        const result = { isValid: true, errorMessage: '' };
        for (const rule of fieldConfig.rules) {
            const ruleResult = this.executeValidationRule(value, rule, fieldElement);
            if (!ruleResult.isValid) {
                result.isValid = false;
                result.errorMessage = ruleResult.errorMessage;
                break;
            }
        }
        if (!result.isValid) {
            this.showFieldError(fieldElement, result.errorMessage || '验证失败');
        }
        else {
            this.clearFieldError(fieldElement);
        }
        return result;
    }
    validateForm(formElement) {
        const formId = formElement.id;
        const config = this.validationConfigs.get(formId);
        if (!config) {
            return true;
        }
        let isFormValid = true;
        const validators = this.activeValidators.get(formElement);
        if (validators) {
            validators.forEach((fieldElement, fieldName) => {
                const fieldConfig = config.fields[fieldName];
                if (fieldConfig) {
                    const result = this.validateField(fieldElement, fieldConfig);
                    if (!result.isValid) {
                        isFormValid = false;
                    }
                }
            });
        }
        return isFormValid;
    }
    executeValidationRule(value, rule, fieldElement) {
        const result = { isValid: true, errorMessage: '' };
        switch (rule.type) {
            case 'required':
                if (value.length === 0) {
                    result.isValid = false;
                    result.errorMessage = rule.message || '此字段不能为空';
                }
                break;
            case 'minLength':
                if (value.length > 0 && value.length < rule.value) {
                    result.isValid = false;
                    result.errorMessage = rule.message || `最少需要${rule.value}个字符`;
                }
                break;
            case 'maxLength':
                if (value.length > rule.value) {
                    result.isValid = false;
                    result.errorMessage = rule.message || `最多允许${rule.value}个字符`;
                }
                break;
            case 'pattern':
                if (value.length > 0 && !rule.value.test(value)) {
                    result.isValid = false;
                    result.errorMessage = rule.message || '格式不正确';
                }
                break;
            case 'email':
                if (value.length > 0 && !/^.+@.+\..+$/.test(value)) {
                    result.isValid = false;
                    result.errorMessage = rule.message || '邮箱格式不正确';
                }
                break;
            case 'mobile':
                if (value.length > 0 && !/^\d{11}$/.test(value)) {
                    result.isValid = false;
                    result.errorMessage = rule.message || '手机号必须为11位数字';
                }
                break;
            case 'qq':
                if (value.length > 0 && !/^\d{5,11}$/.test(value)) {
                    result.isValid = false;
                    result.errorMessage = rule.message || 'QQ号必须为5-11位数字';
                }
                break;
            case 'number':
                if (value.length > 0 && !/^\d+$/.test(value)) {
                    result.isValid = false;
                    result.errorMessage = rule.message || '必须为数字';
                }
                break;
            case 'range':
                const numValue = parseFloat(value);
                const range = rule.value;
                if (value.length > 0 && (isNaN(numValue) || numValue < range.min || numValue > range.max)) {
                    result.isValid = false;
                    result.errorMessage = rule.message || `必须为${range.min}-${range.max}之间的数字`;
                }
                break;
            case 'custom':
                if (rule.validator) {
                    const customResult = rule.validator(value, fieldElement);
                    if (!customResult.isValid) {
                        result.isValid = false;
                        result.errorMessage = customResult.errorMessage;
                    }
                }
                break;
        }
        return result;
    }
    showFieldError(fieldElement, message) {
        this.clearFieldError(fieldElement);
        fieldElement.classList.add('border-red-500', 'error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'form-error text-xs text-red-500 mt-1';
        errorDiv.textContent = message;
        const parent = fieldElement.parentNode;
        if (parent) {
            parent.appendChild(errorDiv);
        }
    }
    clearFieldError(fieldElement) {
        fieldElement.classList.remove('border-red-500', 'error', 'border-danger');
        const parent = fieldElement.parentNode;
        if (parent) {
            const existingError = parent.querySelector('.form-error, .text-xs.text-red-500');
            if (existingError) {
                existingError.remove();
            }
        }
    }
    showSubmitLoading(buttonConfig) {
        const button = document.querySelector(buttonConfig.selector);
        if (button) {
            const iconClass = buttonConfig.iconClass || 'loader-2';
            button.innerHTML = `<i data-lucide="${iconClass}" class="w-5 h-5 animate-spin mr-2"></i>${buttonConfig.loadingText}`;
            button.disabled = true;
            setTimeout(() => {
                if (typeof window.lucide !== 'undefined') {
                    window.lucide.createIcons();
                }
            }, 10);
        }
    }
    validatePasswordStrength(password) {
        return {
            hasLength: password.length >= 6,
            hasUppercase: /[A-Z]/.test(password),
            hasLowercase: /[a-z]/.test(password),
            hasNumber: /\d/.test(password),
            score: this.calculatePasswordScore(password)
        };
    }
    calculatePasswordScore(password) {
        let score = 0;
        if (password.length >= 6)
            score += 25;
        if (/[A-Z]/.test(password))
            score += 25;
        if (/[a-z]/.test(password))
            score += 25;
        if (/\d/.test(password))
            score += 25;
        return score;
    }
    updatePasswordRules(password, confirmPassword, ruleElements) {
        const strength = this.validatePasswordStrength(password);
        if (ruleElements.length) {
            this.updateRuleStatus(ruleElements.length, strength.hasLength);
        }
        if (ruleElements.uppercase) {
            this.updateRuleStatus(ruleElements.uppercase, strength.hasUppercase);
        }
        if (ruleElements.lowercase) {
            this.updateRuleStatus(ruleElements.lowercase, strength.hasLowercase);
        }
        if (ruleElements.number) {
            this.updateRuleStatus(ruleElements.number, strength.hasNumber);
        }
        if (ruleElements.match) {
            const passwordsMatch = password.length > 0 && password === confirmPassword;
            this.updateRuleStatus(ruleElements.match, passwordsMatch);
        }
    }
    updateRuleStatus(element, isValid) {
        const icon = element.querySelector('.w-4.h-4') || element.querySelector('i');
        if (icon) {
            if (isValid) {
                if (element.querySelector('.w-4.h-4')) {
                    element.classList.add('text-success');
                    element.classList.remove('text-text-light');
                    icon.classList.remove('opacity-30');
                    icon.classList.add('opacity-100');
                    icon.classList.add('text-success');
                }
                else {
                    icon.setAttribute('data-lucide', 'check');
                    element.classList.remove('text-gray-500');
                    element.classList.add('text-green-600');
                }
            }
            else {
                if (element.querySelector('.w-4.h-4')) {
                    element.classList.remove('text-success');
                    element.classList.add('text-text-light');
                    icon.classList.add('opacity-30');
                    icon.classList.remove('opacity-100');
                    icon.classList.remove('text-success');
                }
                else {
                    icon.setAttribute('data-lucide', 'x');
                    element.classList.remove('text-green-600');
                    element.classList.add('text-gray-500');
                }
            }
            if (!element.querySelector('.w-4.h-4')) {
                setTimeout(() => {
                    if (typeof window.lucide !== 'undefined') {
                        window.lucide.createIcons();
                    }
                }, 10);
            }
        }
    }
    static createProfileValidationConfig() {
        return {
            fields: {
                'tonickname': {
                    rules: [
                        { type: 'required', message: '昵称不能为空' },
                        { type: 'maxLength', value: 15, message: '昵称不能超过15个字符' }
                    ],
                    validateOnBlur: true,
                    validateOnInput: true
                },
                'mobile': {
                    rules: [
                        { type: 'mobile', message: '手机号必须为11位数字' }
                    ],
                    validateOnBlur: true
                },
                'email': {
                    rules: [
                        { type: 'email', message: '邮箱格式不正确' }
                    ],
                    validateOnBlur: true
                },
                'qq': {
                    rules: [
                        { type: 'qq', message: 'QQ号必须为5-11位数字' }
                    ],
                    validateOnBlur: true
                }
            },
            submitButton: {
                selector: '.form-submit',
                loadingText: '保存中...'
            }
        };
    }
    static createPasswordValidationConfig() {
        return {
            fields: {
                'txtoldPW': {
                    rules: [
                        { type: 'required', message: '原密码不能为空' }
                    ],
                    validateOnBlur: true
                },
                'txtnewPW': {
                    rules: [
                        { type: 'required', message: '新密码不能为空' },
                        { type: 'minLength', value: 6, message: '密码至少需要6个字符' }
                    ],
                    validateOnBlur: true,
                    validateOnInput: true
                },
                'txtrePW': {
                    rules: [
                        { type: 'required', message: '确认密码不能为空' },
                        {
                            type: 'custom',
                            message: '两次密码输入不一致',
                            validator: (value, element) => {
                                const newPasswordElement = element.form?.querySelector('[name="txtnewPW"]');
                                const isValid = newPasswordElement ? value === newPasswordElement.value : false;
                                return { isValid, errorMessage: '两次密码输入不一致' };
                            }
                        }
                    ],
                    validateOnBlur: true,
                    validateOnInput: true
                }
            },
            submitButton: {
                selector: '.form-submit',
                loadingText: '修改中...'
            }
        };
    }
    static createRMBtoMoneyValidationConfig() {
        return {
            fields: {
                'exchangeAmount': {
                    rules: [
                        { type: 'required', message: '请输入兑换金额' },
                        { type: 'number', message: '金额必须为数字' },
                        { type: 'range', value: { min: 0.01, max: 1000 }, message: '金额必须在0.01-1000之间' }
                    ],
                    validateOnBlur: true,
                    validateOnInput: true
                },
                'password': {
                    rules: [
                        { type: 'required', message: '请输入密码' }
                    ],
                    validateOnBlur: true
                }
            },
            submitButton: {
                selector: '#exchangeBtn',
                loadingText: '兑换中...'
            }
        };
    }
}
