// 公共变量
var selectedFiles = [];
var maxFiles = 9;

// 拖拽相关函数
function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById('fileSelectArea').classList.add('drag-over');
}

function handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById('fileSelectArea').classList.remove('drag-over');
}

function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById('fileSelectArea').classList.remove('drag-over');
}

// 粘贴处理
document.addEventListener('paste', function(e) {
    var items = (e.clipboardData || e.originalEvent.clipboardData).items;
    var files = [];
    for (var i = 0; i < items.length; i++) {
        if (items[i].kind === 'file') {
            files.push(items[i].getAsFile());
        }
    }
    if (files.length > 0) {
        handleFiles(files);
    }
});

// 文件处理核心函数
async function handleFiles(files) {
    var totalFiles = selectedFiles.length + files.length;
    if(totalFiles > maxFiles) {
        showErrorDialog('文件数量超限', `最多只能选择${maxFiles}个文件，当前已选择${selectedFiles.length}个文件`);
        return;
    }

    // 逐个验证并添加文件
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        // 如果文件不存在且验证通过，则添加
        if (!isFileAlreadySelected(file)) {
            const isValid = await validateFileSelection(file);
            if (isValid) {
                selectedFiles.push(file);
            }
        }
    }

    // 更新显示
    updateFileDisplay();
}

// 检查文件是否已存在
function isFileAlreadySelected(newFile) {
    return selectedFiles.some(existingFile => 
        existingFile.name === newFile.name && 
        existingFile.size === newFile.size &&
        existingFile.type === newFile.type
    );
}

// 统一的文件显示更新函数
function updateFileDisplay(options = {}) {
    // 清空当前的文件显示区域
    var container = document.getElementById('fileUploadContainer');
    container.innerHTML = '';

    // 显示每个文件
    selectedFiles.forEach(function(file, i) {
        var fileArea = document.createElement('div');
        fileArea.className = 'fileUploadArea';
        fileArea.style.display = 'block';
        
        var fileName = document.createElement('span');
        fileName.textContent = file.name;
        fileArea.appendChild(fileName);

        var removeBtn = document.createElement('button');
        removeBtn.textContent = '移除';
        removeBtn.addEventListener('click', function() {
            selectedFiles.splice(i, 1);
            updateFileDisplay();
        });
        fileArea.appendChild(removeBtn);

        container.appendChild(fileArea);
    });

    // 更新文件数量
    document.getElementById('numInput').value = selectedFiles.length || 1;
    
    // 根据不同页面类型处理提交按钮显示
    var submitBtn = document.getElementById('submitBtn');
    if(submitBtn) {
        if(options.isNewPost) {
            submitBtn.style.display = selectedFiles.length > 0 ? 'flex' : 'none';
        } else if(options.isReply || options.isAppend) {
            submitBtn.style.display = selectedFiles.length > 0 ? 'flex' : 'none';
        }
    }
}

// 文本框高度自适应
function adjustTextareaHeight(textarea) { 
    if (textarea.scrollHeight > textarea.offsetHeight) { 
        textarea.style.height = textarea.scrollHeight + 'px'; 
    } 
}

// 表单验证与AJAX提交
document.addEventListener('DOMContentLoaded', function() {
    var form = document.querySelector('form[name="f"]');
    if(form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault(); // 阻止默认提交

            // 文件验证
            if(selectedFiles.length === 0) {
                alert('请至少选择一个文件');
                return false;
            }

            // 发新帖时的标题和内容验证
            var titleInput = document.querySelector('input[name="book_title"]');
            var contentTextarea = document.querySelector('textarea[name="book_content"]');
            
            if(titleInput && contentTextarea) {  // 发新帖页面
                if(titleInput.value.length < 5) {
                    alert('标题最少5个字符');
                    return false;
                }
                
                if(contentTextarea.value.length < 15) {
                    alert('内容最少15个字符');
                    return false;
                }
            }

            // 使用 FormData 进行 AJAX 提交
            var formData = new FormData(form);
            selectedFiles.forEach((file, index) => {
                formData.append('book_file', file, file.name);
            });
            formData.append('num', selectedFiles.length || 1);

            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(data => {
                // 处理服务器响应
                document.open();
                document.write(data);
                document.close();
            })
            .catch(error => {
                console.error('提交错误:', error);
                alert('提交失败，请重试。');
            });
        });
    }
});

// 文件选择验证函数
function validateFileSelection(file) {
    const imageTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    const maxSize = 1024 * 1024; // 1MB in bytes
    
    // 检查是否是图片文件
    const isImage = imageTypes.includes(file.type.toLowerCase());
    
    // 如果声称是图片文件，进行进一步验证
    if (isImage) {
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    // 图片加载成功，说明是真实图片
                    resolve(true);
                };
                img.onerror = function() {
                    // 图片加载失败，可能是伪装的文件
                    showErrorDialog('无效的图片文件', `文件 "${file.name}" 不是有效的图片文件。`);
                    resolve(false);
                };
                img.src = e.target.result;
            };
            reader.onerror = function() {
                showErrorDialog('文件读取错误', `文件 "${file.name}" 读取失败。`);
                resolve(false);
            };
            reader.readAsDataURL(file);
        });
    }
    
    // 非图片文件检查大小
    if (!isImage && file.size > maxSize) {
        showErrorDialog('文件大小超限', `文件 "${file.name}" 大小超过限制。当前文件大小: ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
        return Promise.resolve(false);
    }
    
    return Promise.resolve(true);
}

// 显示错误对话框
function showErrorDialog(title, message) {
    const dialog = document.createElement('dialog');
    dialog.className = 'dialog-url';
    
    dialog.innerHTML = `
        <div class="dialog-url-header">
            <svg class="dialog-url-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <h2 class="dialog-url-title">${title}</h2>
        </div>
        <p class="dialog-url-description">${message}</p>
        <div class="dialog-url-footer">
            <button class="dialog-url-button" onclick="this.closest('dialog').close()">确定</button>
        </div>
    `;
    
    document.body.appendChild(dialog);
    dialog.showModal();
    
    // 点击背景关闭
    dialog.addEventListener('click', (e) => {
        if (e.target === dialog) dialog.close();
    });
}

// 修改文件选择处理函数
function handleFileSelect(event) {
    event.preventDefault();
    const files = event.target.files || event.dataTransfer.files;
    handleFiles(files);
    
    // 清空文件输入框的值，允许重复选择相同文件
    if (event.target && event.target.value) {
        event.target.value = '';
    }
}

// DOM加载完成后绑定事件
document.addEventListener('DOMContentLoaded', function() {
    // 文件选择事件
    const fileInput = document.getElementById('multiFileSelect');
    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelect);
    }
    
    // 拖放事件
    const dropZone = document.querySelector('.file-select-area');
    if (dropZone) {
        dropZone.addEventListener('drop', handleFileSelect);
        dropZone.addEventListener('dragover', handleDragOver);
        dropZone.addEventListener('dragleave', handleDragLeave);
    }
});