<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
  <title>选择购买身份</title>
  <script src="/NetCSS/JS/BBS/Lucide.0.511.0.min.js"></script>
  <link rel="stylesheet" href="/NetCSS/CSS/BBS/GroupBuy.css?5" />
</head>
<body>
  <div class="header">
    <a href="/myfile.aspx" class="back-button">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m15 18-6-6 6-6"></path></svg>
    </a>
    <h1 class="header-title">选择购买身份</h1>
  </div>


  <div class="identity-grid">
    <div class="identity-card" id="basic-color-card">
      <div class="identity-card-header">
        <span class="identity-name" id="selected-color-name">彩色昵称</span>
        <span class="identity-badge">
          <i data-lucide="tag" width="14" height="14" class="icon"></i>
          基础
        </span>
      </div>
      <div class="identity-card-body">
        <div class="identity-price">¥3 / 月</div>
        <div class="identity-price-alt">或 37,500妖晶 / 月</div>
        
        <div class="color-name-display" id="color-name-display">
          <span id="color-name-text" class="red-text">红色昵称</span>
        </div>
        
        <div class="color-selector">
          <div class="color-option color-green" data-color="green" data-name="绿色昵称"></div>
          <div class="color-option color-red selected" data-color="red" data-name="红色昵称"></div>
          <div class="color-option color-blue" data-color="blue" data-name="蓝色昵称"></div>
          <div class="color-option color-purple" data-color="purple" data-name="紫色昵称"></div>
          <div class="color-option color-pink" data-color="pink" data-name="粉色昵称"></div>
          <div class="color-option color-pink-purple" data-color="pink-purple" data-name="粉紫昵称"></div>
        </div>
      </div>
      <div class="identity-card-footer">
        <button class="purchase-button" id="basic-purchase-button">
          <i data-lucide="shopping-cart" width="16" height="16" class="icon"></i>
          购买
        </button>
      </div>
    </div>

    <div class="identity-card" id="red-vip-card">
      <div class="identity-card-header">
        <div class="flex items-center">
          <span class="identity-name red-text">红名VIP</span> <img src="/NetImages/vip.gif" class="identity-icon" alt="红名VIP图标">
        </div>
        <span class="identity-badge">
          <i data-lucide="star" width="14" height="14" class="icon"></i>
          高级
        </span>
      </div>
      <div class="identity-card-body">
        <div class="identity-price">¥5 / 月</div>
        <div class="identity-price-alt">或 62,500妖晶 / 月</div>
        
        <div class="identity-privileges">
          <div class="identity-privileges-title">
            <i data-lucide="award" width="16" height="16" class="icon"></i>
            特权
          </div>
          <div class="identity-privilege">
            <i data-lucide="check" width="14" height="14" class="icon"></i>
            <span>双向拉黑</span>
          </div>
          <div class="identity-privilege">
            <i data-lucide="check" width="14" height="14" class="icon"></i>
            <span>黑名单上限+10</span>
          </div>
        </div>
      </div>
      <div class="identity-card-footer">
        <button class="purchase-button" id="red-vip-purchase-button">
          <i data-lucide="shopping-cart" width="16" height="16" class="icon"></i>
          购买
        </button>
      </div>
    </div>

    <div class="identity-card">
      <div class="identity-card-header">
        <div class="flex items-center">
          <span class="identity-name gold-text">金名VIP</span> <img src="/NetImages/newvip.gif" class="identity-icon" alt="金名VIP图标">
        </div>
        <span class="identity-badge">
          <i data-lucide="star" width="14" height="14" class="icon"></i>
          高级
        </span>
      </div>
      <div class="identity-card-body">
        <div class="identity-price">¥6 / 月</div>
        <div class="identity-price-alt">或 75,000妖晶 / 月</div>
        
        <div class="identity-privileges">
          <div class="identity-privileges-title">
            <i data-lucide="award" width="16" height="16" class="icon"></i>
            特权
          </div>
          <div class="identity-privilege">
            <i data-lucide="check" width="14" height="14" class="icon"></i>
            <span>双向拉黑</span>
          </div>
          <div class="identity-privilege">
            <i data-lucide="check" width="14" height="14" class="icon"></i>
            <span>黑名单上限+10</span>
          </div>
        </div>
      </div>
      <div class="identity-card-footer">
        <button class="purchase-button">
          <i data-lucide="shopping-cart" width="16" height="16" class="icon"></i>
          购买
        </button>
      </div>
    </div>

    <div class="identity-card">
      <div class="identity-card-header">
        <div class="flex items-center">
          <span class="identity-name purple-text">紫名年费VIP</span> <img src="/NetImages/年费vip.gif" class="identity-icon" alt="紫名年费VIP图标">
        </div>
        <span class="identity-badge">
          <i data-lucide="crown" width="14" height="14" class="icon"></i>
          尊贵
        </span>
      </div>
      <div class="identity-card-body">
        <div class="identity-price">¥60 / 年</div>
        <div class="identity-price-alt">或 750,000妖晶 / 年</div>
        
        <div class="identity-privileges">
          <div class="identity-privileges-title">
            <i data-lucide="award" width="16" height="16" class="icon"></i>
            特权
          </div>
          <div class="identity-privilege">
            <i data-lucide="check" width="14" height="14" class="icon"></i>
            <span>双向拉黑</span>
          </div>
          <div class="identity-privilege">
            <i data-lucide="check" width="14" height="14" class="icon"></i>
            <span>黑名单上限+20</span>
          </div>
        </div>
      </div>
      <div class="identity-card-footer">
        <button class="purchase-button">
          <i data-lucide="shopping-cart" width="16" height="16" class="icon"></i>
          购买
        </button>
      </div>
    </div>

    <div class="identity-card">
      <div class="identity-card-header">
        <div class="flex items-center">
          <span class="identity-name gold-text">金名靓</span> <img src="/NetImages/靓号.gif" class="identity-icon" alt="金名靓图标">
        </div>
        <span class="identity-badge">
          <i data-lucide="crown" width="14" height="14" class="icon"></i>
          尊贵
        </span>
      </div>
      <div class="identity-card-body">
        <div class="identity-price">¥8 / 月</div>
        <div class="identity-price-alt">或 100,000妖晶 / 月</div>
        
        <div class="identity-privileges">
          <div class="identity-privileges-title">
            <i data-lucide="award" width="16" height="16" class="icon"></i>
            特权
          </div>
          <div class="identity-privilege">
            <i data-lucide="check" width="14" height="14" class="icon"></i>
            <span>双向拉黑</span>
          </div>
          <div class="identity-privilege">
            <i data-lucide="check" width="14" height="14" class="icon"></i>
            <span>黑名单上限+20</span>
          </div>
        </div>
      </div>
      <div class="identity-card-footer">
        <button class="purchase-button">
          <i data-lucide="shopping-cart" width="16" height="16" class="icon"></i>
          购买
        </button>
      </div>
    </div>

    <div class="identity-card">
      <div class="identity-card-header">
        <div class="flex items-center">
          <span class="identity-name blue-text">蓝名帅</span> <img src="/NetImages/帅.gif" class="identity-icon" alt="蓝名帅图标">
        </div>
        <span class="identity-badge">
          <i data-lucide="crown" width="14" height="14" class="icon"></i>
          尊贵
        </span>
      </div>
      <div class="identity-card-body">
        <div class="identity-price">¥8 / 月</div>
        <div class="identity-price-alt">或 100,000妖晶 / 月</div>
        
        <div class="identity-privileges">
          <div class="identity-privileges-title">
            <i data-lucide="award" width="16" height="16" class="icon"></i>
            特权
          </div>
          <div class="identity-privilege">
            <i data-lucide="check" width="14" height="14" class="icon"></i>
            <span>双向拉黑</span>
          </div>
          <div class="identity-privilege">
            <i data-lucide="check" width="14" height="14" class="icon"></i>
            <span>黑名单上限+20</span>
          </div>
        </div>
      </div>
      <div class="identity-card-footer">
        <button class="purchase-button">
          <i data-lucide="shopping-cart" width="16" height="16" class="icon"></i>
          购买
        </button>
      </div>
    </div>
  </div>

  <script>
    lucide.createIcons();

    const colorOptions = document.querySelectorAll('.color-option');
    const selectedColorName = document.getElementById('selected-color-name');
    const colorNameDisplay = document.getElementById('color-name-display');
    const colorNameText = document.getElementById('color-name-text');
    const basicPurchaseButton = document.getElementById('basic-purchase-button');
    const redVipPurchaseButton = document.getElementById('red-vip-purchase-button');

    colorOptions.forEach(option => {
      option.addEventListener('click', () => {
        colorOptions.forEach(opt => opt.classList.remove('selected'));
        
        option.classList.add('selected');
        
        const color = option.dataset.color;
        const name = option.dataset.name;
        
        updateColorDisplay(color, name);        
        
        lucide.createIcons();
      });
    });

    function updateColorDisplay(color, name) {
      colorNameText.textContent = name;
      colorNameText.classList.remove('green-text', 'red-text', 'blue-text', 'purple-text', 'pink-text', 'pink-purple-text', 'basic-purple-text');
      
      if (color === 'purple') {
        colorNameText.classList.add('basic-purple-text');
      } else {
        colorNameText.classList.add(`${color}-text`);
      }
      
      selectedColorName.textContent = '彩色昵称';
    }

    const purchaseButtons = document.querySelectorAll('.purchase-button');
    purchaseButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        const card = e.target.closest('.identity-card');
        let identityName;
        let toid;
        
        if (card.querySelector('#color-name-display')) {
          const selectedOption = document.querySelector('.color-option.selected');
          identityName = selectedOption.dataset.name;
          const color = selectedOption.dataset.color;
          
          switch(color) {
            case 'green': toid = 120; break;
            case 'red': toid = 340; break;
            case 'blue': toid = 341; break;
            case 'purple': toid = 342; break;
            case 'pink': toid = 355; break;
            case 'pink-purple': toid = 356; break;
          }
        } else {
          identityName = card.querySelector('.identity-name').textContent;
          
          switch(identityName) {
            case '红名VIP': toid = 101; break;
            case '金名VIP': toid = 358; break;
            case '紫名年费VIP': toid = 105; break;
            case '金名靓': toid = 140; break;
            case '蓝名帅': toid = 180; break;
          }
        }
        
        if (toid) {
          const purchaseUrl = `/bbs/togroupcoinbuy.aspx?toid=${toid}`;
          window.location.href = purchaseUrl;
        } else {
          alert(`未找到 ${identityName} 的购买链接。`);
        }
      });
    });
  </script>
</body>
</html>