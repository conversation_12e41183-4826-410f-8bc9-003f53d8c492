#!/usr/bin/env node
/**
 * css-quality-monitor.js - CSS代码质量监控工具
 * 用于监控CSS构建质量、性能指标和代码健康度
 */

const fs = require('fs');
const path = require('path');

class CSSQualityMonitor {
    constructor() {
        this.outputCSSPath = '../Template/CSS/output.css';
        this.styleCSSPath = './style.css';
        this.configPath = './tailwind.config.js';
        this.baselineFile = './css-baseline.txt';
        this.reportFile = `./css-quality-report-${new Date().toISOString().slice(0, 10)}.json`;
    }

    /**
     * 运行完整的质量检查
     */
    async runQualityCheck() {
        console.log('=== CSS代码质量监控 ===');
        console.log(`检查时间: ${new Date().toLocaleString()}`);
        console.log('');

        const report = {
            timestamp: new Date().toISOString(),
            fileSize: this.checkFileSize(),
            duplicates: this.checkDuplicates(),
            performance: this.checkPerformance(),
            codeHealth: this.checkCodeHealth(),
            recommendations: []
        };

        // 生成建议
        report.recommendations = this.generateRecommendations(report);

        // 保存报告
        this.saveReport(report);

        // 显示结果
        this.displayReport(report);

        return report;
    }

    /**
     * 检查文件大小变化
     */
    checkFileSize() {
        console.log('📏 1. 检查文件大小...');
        
        const sizeInfo = {
            current: 0,
            baseline: 0,
            change: 0,
            changePercent: 0
        };

        try {
            // 当前文件大小
            if (fs.existsSync(this.outputCSSPath)) {
                const stats = fs.statSync(this.outputCSSPath);
                sizeInfo.current = stats.size;
                console.log(`   当前CSS文件大小: ${sizeInfo.current} bytes`);
            } else {
                console.log('   ⚠️  CSS输出文件不存在');
                return sizeInfo;
            }

            // 基准大小
            if (fs.existsSync(this.baselineFile)) {
                const baseline = fs.readFileSync(this.baselineFile, 'utf8');
                const sizeMatch = baseline.match(/文件大小:\s*(\d+)\s*bytes/);
                if (sizeMatch) {
                    sizeInfo.baseline = parseInt(sizeMatch[1]);
                    sizeInfo.change = sizeInfo.current - sizeInfo.baseline;
                    sizeInfo.changePercent = ((sizeInfo.change / sizeInfo.baseline) * 100).toFixed(2);
                    
                    console.log(`   基准文件大小: ${sizeInfo.baseline} bytes`);
                    console.log(`   大小变化: ${sizeInfo.change > 0 ? '+' : ''}${sizeInfo.change} bytes (${sizeInfo.changePercent}%)`);
                    
                    if (sizeInfo.change < 0) {
                        console.log('   ✅ 文件大小减少，优化效果良好');
                    } else if (sizeInfo.change > 1000) {
                        console.log('   ⚠️  文件大小显著增加，需要检查');
                    }
                }
            } else {
                console.log('   ℹ️  未找到基准文件，无法比较大小变化');
            }

        } catch (error) {
            console.log(`   ❌ 检查文件大小时出错: ${error.message}`);
        }

        console.log('');
        return sizeInfo;
    }

    /**
     * 检查重复定义
     */
    checkDuplicates() {
        console.log('🔍 2. 检查重复定义...');
        
        const duplicates = {
            selectors: [],
            colors: [],
            properties: []
        };

        try {
            if (fs.existsSync(this.styleCSSPath)) {
                const css = fs.readFileSync(this.styleCSSPath, 'utf8');
                
                // 检查重复的选择器
                const selectors = css.match(/\.[a-zA-Z][a-zA-Z0-9_-]*\s*{/g) || [];
                const selectorCounts = {};
                
                selectors.forEach(selector => {
                    const cleanSelector = selector.replace(/\s*{.*/, '').trim();
                    selectorCounts[cleanSelector] = (selectorCounts[cleanSelector] || 0) + 1;
                });

                Object.entries(selectorCounts).forEach(([selector, count]) => {
                    if (count > 1) {
                        duplicates.selectors.push({ selector, count });
                        console.log(`   🔴 重复选择器: ${selector} (${count}次)`);
                    }
                });

                if (duplicates.selectors.length === 0) {
                    console.log('   ✅ 未发现重复的选择器定义');
                }
            }

            // 检查配置文件中的重复颜色
            if (fs.existsSync(this.configPath)) {
                const config = fs.readFileSync(this.configPath, 'utf8');
                const colorMatches = config.match(/'[^']*':\s*'#[0-9a-fA-F]{6}'/g) || [];
                const colorValues = {};

                colorMatches.forEach(match => {
                    const [name, value] = match.split(':').map(s => s.trim().replace(/'/g, ''));
                    if (!colorValues[value]) {
                        colorValues[value] = [];
                    }
                    colorValues[value].push(name);
                });

                Object.entries(colorValues).forEach(([value, names]) => {
                    if (names.length > 1) {
                        duplicates.colors.push({ value, names });
                        console.log(`   🟡 重复颜色值 ${value}: ${names.join(', ')}`);
                    }
                });

                if (duplicates.colors.length === 0) {
                    console.log('   ✅ 未发现重复的颜色定义');
                }
            }

        } catch (error) {
            console.log(`   ❌ 检查重复定义时出错: ${error.message}`);
        }

        console.log('');
        return duplicates;
    }

    /**
     * 检查性能指标
     */
    checkPerformance() {
        console.log('⚡ 3. 检查性能指标...');
        
        const performance = {
            fileSize: 0,
            gzipSize: 0,
            selectorCount: 0,
            ruleCount: 0,
            mediaQueryCount: 0
        };

        try {
            if (fs.existsSync(this.outputCSSPath)) {
                const css = fs.readFileSync(this.outputCSSPath, 'utf8');
                performance.fileSize = Buffer.byteLength(css, 'utf8');

                // 计算选择器数量
                const selectors = css.match(/[^{}]+{/g) || [];
                performance.selectorCount = selectors.length;

                // 计算规则数量
                const rules = css.match(/{[^}]*}/g) || [];
                performance.ruleCount = rules.length;

                // 计算媒体查询数量
                const mediaQueries = css.match(/@media[^{]*{/g) || [];
                performance.mediaQueryCount = mediaQueries.length;

                console.log(`   📊 文件大小: ${performance.fileSize} bytes`);
                console.log(`   📊 选择器数量: ${performance.selectorCount}`);
                console.log(`   📊 CSS规则数量: ${performance.ruleCount}`);
                console.log(`   📊 媒体查询数量: ${performance.mediaQueryCount}`);

                // 性能评估
                if (performance.fileSize > 100000) {
                    console.log('   ⚠️  文件大小较大，考虑进一步优化');
                } else {
                    console.log('   ✅ 文件大小合理');
                }

                if (performance.selectorCount > 4000) {
                    console.log('   ⚠️  选择器数量较多，可能影响性能');
                } else {
                    console.log('   ✅ 选择器数量合理');
                }

            } else {
                console.log('   ❌ CSS输出文件不存在');
            }

        } catch (error) {
            console.log(`   ❌ 检查性能指标时出错: ${error.message}`);
        }

        console.log('');
        return performance;
    }

    /**
     * 检查代码健康度
     */
    checkCodeHealth() {
        console.log('🏥 4. 检查代码健康度...');
        
        const health = {
            commentCoverage: 0,
            organizationScore: 0,
            maintainabilityScore: 0,
            issues: []
        };

        try {
            if (fs.existsSync(this.styleCSSPath)) {
                const css = fs.readFileSync(this.styleCSSPath, 'utf8');
                const lines = css.split('\n');
                
                // 计算注释覆盖率
                const commentLines = lines.filter(line => line.trim().startsWith('/*') || line.trim().startsWith('//')).length;
                const codeLines = lines.filter(line => line.trim() && !line.trim().startsWith('/*') && !line.trim().startsWith('//')).length;
                health.commentCoverage = ((commentLines / codeLines) * 100).toFixed(1);

                console.log(`   📝 注释覆盖率: ${health.commentCoverage}%`);

                // 检查组织结构
                const hasLayerComments = css.includes('/* ') && css.includes(' */');
                const hasSectionDividers = css.includes('===') || css.includes('---');
                health.organizationScore = (hasLayerComments ? 50 : 0) + (hasSectionDividers ? 50 : 0);

                console.log(`   📁 组织结构评分: ${health.organizationScore}/100`);

                // 检查可维护性问题
                if (css.includes('!important')) {
                    const importantCount = (css.match(/!important/g) || []).length;
                    health.issues.push(`使用了 ${importantCount} 次 !important`);
                }

                if (css.includes('position: absolute') && css.includes('position: fixed')) {
                    health.issues.push('混合使用了绝对定位和固定定位');
                }

                // 计算维护性评分
                health.maintainabilityScore = Math.max(0, 100 - (health.issues.length * 20));

                console.log(`   🔧 可维护性评分: ${health.maintainabilityScore}/100`);

                if (health.issues.length > 0) {
                    console.log('   ⚠️  发现的问题:');
                    health.issues.forEach(issue => console.log(`      - ${issue}`));
                } else {
                    console.log('   ✅ 未发现明显的维护性问题');
                }
            }

        } catch (error) {
            console.log(`   ❌ 检查代码健康度时出错: ${error.message}`);
        }

        console.log('');
        return health;
    }

    /**
     * 生成优化建议
     */
    generateRecommendations(report) {
        const recommendations = [];

        // 基于文件大小的建议
        if (report.fileSize.change > 1000) {
            recommendations.push({
                type: 'size',
                priority: 'high',
                message: '文件大小显著增加，建议检查是否引入了不必要的样式'
            });
        }

        // 基于重复定义的建议
        if (report.duplicates.selectors.length > 0) {
            recommendations.push({
                type: 'duplicates',
                priority: 'high',
                message: `发现 ${report.duplicates.selectors.length} 个重复选择器，建议立即清理`
            });
        }

        if (report.duplicates.colors.length > 0) {
            recommendations.push({
                type: 'duplicates',
                priority: 'medium',
                message: `发现 ${report.duplicates.colors.length} 个重复颜色值，考虑统一为设计令牌`
            });
        }

        // 基于性能的建议
        if (report.performance.fileSize > 100000) {
            recommendations.push({
                type: 'performance',
                priority: 'medium',
                message: 'CSS文件较大，考虑启用代码分割或更激进的压缩'
            });
        }

        // 基于代码健康度的建议
        if (report.codeHealth.commentCoverage < 10) {
            recommendations.push({
                type: 'health',
                priority: 'low',
                message: '注释覆盖率较低，建议增加代码注释'
            });
        }

        if (report.codeHealth.maintainabilityScore < 80) {
            recommendations.push({
                type: 'health',
                priority: 'medium',
                message: '代码维护性有待改善，请查看具体问题列表'
            });
        }

        return recommendations;
    }

    /**
     * 保存报告
     */
    saveReport(report) {
        try {
            fs.writeFileSync(this.reportFile, JSON.stringify(report, null, 2));
            console.log(`📄 质量报告已保存到: ${this.reportFile}`);
        } catch (error) {
            console.log(`❌ 保存报告时出错: ${error.message}`);
        }
    }

    /**
     * 显示报告摘要
     */
    displayReport(report) {
        console.log('📊 5. 质量报告摘要...');
        console.log('----------------------------------------');

        // 总体评分
        const overallScore = Math.round(
            (report.codeHealth.maintainabilityScore + 
             report.codeHealth.organizationScore + 
             (report.duplicates.selectors.length === 0 ? 100 : 50)) / 3
        );

        console.log(`🎯 总体质量评分: ${overallScore}/100`);

        // 优先级建议
        const highPriorityRecs = report.recommendations.filter(r => r.priority === 'high');
        const mediumPriorityRecs = report.recommendations.filter(r => r.priority === 'medium');

        if (highPriorityRecs.length > 0) {
            console.log('\n🔴 高优先级建议:');
            highPriorityRecs.forEach(rec => console.log(`   - ${rec.message}`));
        }

        if (mediumPriorityRecs.length > 0) {
            console.log('\n🟡 中优先级建议:');
            mediumPriorityRecs.forEach(rec => console.log(`   - ${rec.message}`));
        }

        if (highPriorityRecs.length === 0 && mediumPriorityRecs.length === 0) {
            console.log('\n✅ 代码质量良好，无紧急问题需要处理');
        }

        console.log('\n✅ 质量检查完成！');
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const monitor = new CSSQualityMonitor();
    monitor.runQualityCheck().catch(console.error);
}

module.exports = CSSQualityMonitor;
