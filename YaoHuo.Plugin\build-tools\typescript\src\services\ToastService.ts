/**
 * Toast通知服务
 * 统一管理所有页面的Toast通知显示
 * 消除重复代码，提供一致的用户体验
 * 
 * @version 1.0
 * <AUTHOR>
 * @date 2025-01-07
 */

import { ToastConfig, CSS_CLASSES, DEFAULT_CONFIG } from '../types/CommonTypes.js';

/**
 * Toast通知服务类
 * 提供统一的Toast显示、关闭和管理功能
 */
export class ToastService {
    private static instance: ToastService;
    private activeToasts: Map<string, HTMLElement> = new Map();
    private toastCounter: number = 0;

    /**
     * 获取单例实例
     */
    public static getInstance(): ToastService {
        if (!ToastService.instance) {
            ToastService.instance = new ToastService();
        }
        return ToastService.instance;
    }

    /**
     * 显示成功Toast
     */
    public static showSuccess(message: string, duration?: number): string {
        return ToastService.getInstance().show({
            type: 'success',
            message,
            duration
        });
    }

    /**
     * 显示错误Toast
     */
    public static showError(message: string, duration?: number): string {
        return ToastService.getInstance().show({
            type: 'error',
            message,
            duration
        });
    }

    /**
     * 显示警告Toast
     */
    public static showWarning(message: string, duration?: number): string {
        return ToastService.getInstance().show({
            type: 'warning',
            message,
            duration
        });
    }

    /**
     * 显示信息Toast
     */
    public static showInfo(message: string, duration?: number): string {
        return ToastService.getInstance().show({
            type: 'info',
            message,
            duration
        });
    }

    /**
     * 关闭指定Toast
     */
    public static close(toastId: string): void {
        ToastService.getInstance().closeToast(toastId);
    }

    /**
     * 关闭所有Toast
     */
    public static closeAll(): void {
        ToastService.getInstance().closeAllToasts();
    }

    /**
     * 显示Toast通知
     */
    public show(config: ToastConfig): string {
        const toastId = this.generateToastId();
        const duration = config.duration ?? DEFAULT_CONFIG.TOAST_DURATION;
        const autoClose = config.autoClose ?? true;

        // 创建Toast元素
        const toastElement = this.createToastElement(toastId, config);
        
        // 添加到页面
        document.body.appendChild(toastElement);
        this.activeToasts.set(toastId, toastElement);

        // 触发显示动画
        requestAnimationFrame(() => {
            toastElement.style.opacity = '1';
            toastElement.style.transform = 'translate(-50%, 0)';
        });

        // 自动关闭
        if (autoClose && duration > 0) {
            setTimeout(() => {
                this.closeToast(toastId);
            }, duration);
        }

        return toastId;
    }

    /**
     * 关闭指定Toast
     */
    public closeToast(toastId: string): void {
        const toastElement = this.activeToasts.get(toastId);
        if (!toastElement) return;

        // 添加淡出动画
        toastElement.classList.add('fade-out');
        
        // 动画完成后移除元素
        setTimeout(() => {
            if (toastElement.parentNode) {
                toastElement.parentNode.removeChild(toastElement);
            }
            this.activeToasts.delete(toastId);
        }, 300);
    }

    /**
     * 关闭所有Toast
     */
    public closeAllToasts(): void {
        for (const toastId of this.activeToasts.keys()) {
            this.closeToast(toastId);
        }
    }

    /**
     * 创建Toast元素
     */
    private createToastElement(toastId: string, config: ToastConfig): HTMLElement {
        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = this.getToastClassName(config.type);

        // 初始样式（用于动画）
        toast.style.opacity = '0';
        toast.style.transform = 'translate(-50%, -20px)';

        // 检查是否使用MessageDetail风格（默认为true）
        const isMessageDetailStyle = (window as any).useMessageDetailToast !== false;

        if (isMessageDetailStyle) {
            // MessageDetail风格：只显示文本，无图标和关闭按钮
            toast.textContent = config.message;
        } else {
            // 标准风格：图标 + 文本 + 关闭按钮
            // 添加图标
            const icon = document.createElement('i');
            icon.className = 'w-5 h-5 flex-shrink-0 toast-icon';
            icon.setAttribute('data-lucide', this.getIconName(config.type));
            toast.appendChild(icon);

            // 添加消息文本
            const messageSpan = document.createElement('span');
            messageSpan.className = 'flex-1 leading-relaxed';
            messageSpan.textContent = config.message;
            toast.appendChild(messageSpan);

            // 添加关闭按钮
            const closeButton = document.createElement('button');
            closeButton.className = 'bg-transparent border-none cursor-pointer p-1 rounded flex items-center justify-center transition-colors duration-200 flex-shrink-0 hover:bg-gray-100 text-gray-400 hover:text-gray-600';
            closeButton.onclick = () => this.closeToast(toastId);

            const closeIcon = document.createElement('i');
            closeIcon.className = 'w-4 h-4';
            closeIcon.setAttribute('data-lucide', 'x');
            closeButton.appendChild(closeIcon);

            toast.appendChild(closeButton);

            // 初始化Lucide图标（如果可用）
            if (typeof (window as any).lucide !== 'undefined') {
                (window as any).lucide.createIcons();
            }
        }

        return toast;
    }

    /**
     * 获取Toast的CSS类名
     */
    private getToastClassName(type: ToastConfig['type']): string {
        // 默认使用MessageDetail风格，除非明确指定使用其他风格
        if ((window as any).useMessageDetailToast !== false) {
            return 'toast-messagedetail';
        }

        switch (type) {
            case 'success':
                return CSS_CLASSES.TOAST_SUCCESS;
            case 'error':
                return CSS_CLASSES.TOAST_ERROR;
            case 'warning':
                return CSS_CLASSES.TOAST_WARNING;
            case 'info':
                return CSS_CLASSES.TOAST_INFO;
            default:
                return CSS_CLASSES.TOAST_INFO;
        }
    }

    /**
     * 获取图标名称
     */
    private getIconName(type: ToastConfig['type']): string {
        switch (type) {
            case 'success':
                return 'check-circle';
            case 'error':
                return 'x-circle';
            case 'warning':
                return 'alert-triangle';
            case 'info':
                return 'info';
            default:
                return 'info';
        }
    }

    /**
     * 生成唯一的Toast ID
     */
    private generateToastId(): string {
        return `toast-${++this.toastCounter}-${Date.now()}`;
    }
}

// ==================== 全局函数，供模板调用 ====================

/**
 * 关闭Toast（向后兼容）
 * @param toastId Toast ID
 */
export function closeToast(toastId: string): void {
    ToastService.close(toastId);
}

/**
 * 自动关闭Toast（向后兼容）
 * @param toastId Toast ID
 * @param delay 延迟时间（毫秒）
 */
export function autoCloseToast(toastId: string, delay: number = DEFAULT_CONFIG.TOAST_DURATION): void {
    setTimeout(() => {
        ToastService.close(toastId);
    }, delay);
}

/**
 * 显示Toast通知（简化接口）
 * @param type 类型
 * @param message 消息
 * @param duration 持续时间
 */
export function showToast(type: 'success' | 'error' | 'warning' | 'info', message: string, duration?: number): string {
    switch (type) {
        case 'success':
            return ToastService.showSuccess(message, duration);
        case 'error':
            return ToastService.showError(message, duration);
        case 'warning':
            return ToastService.showWarning(message, duration);
        case 'info':
            return ToastService.showInfo(message, duration);
        default:
            return ToastService.showInfo(message, duration);
    }
}

// 导出默认实例
export default ToastService;
