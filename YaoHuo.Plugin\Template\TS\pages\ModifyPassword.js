import { FormValidationService } from '../services/FormValidationService.js';
import { NavigationService } from '../services/NavigationService.js';
export class ModifyPasswordPage {
    constructor() {
        this.ruleElements = {};
        this.formValidationService = FormValidationService.getInstance();
        this.navigationService = NavigationService.getInstance();
    }
    init() {
        const countdownElement = document.getElementById('countdown-text');
        if (countdownElement) {
            this.startCountdown();
            this.initializeLucideIcons();
            return;
        }
        this.setupFormValidation();
        this.setupPasswordStrengthValidation();
        this.setupNavigationService();
        this.initializeLucideIcons();
    }
    setupFormValidation() {
        const form = document.getElementById('password-form');
        if (!form) {
            console.warn('Password form not found');
            return;
        }
        const config = FormValidationService.createPasswordValidationConfig();
        this.formValidationService.registerForm('password-form', config);
        this.formValidationService.initializeForm(form);
        this.setupFormSubmit(form);
    }
    setupPasswordStrengthValidation() {
        this.ruleElements = {
            length: document.getElementById('rule-length') || undefined,
            uppercase: document.getElementById('rule-uppercase') || undefined,
            lowercase: document.getElementById('rule-lowercase') || undefined,
            number: document.getElementById('rule-number') || undefined,
            match: document.getElementById('rule-match') || undefined
        };
        const newPasswordInput = document.querySelector('[name="txtnewPW"]');
        const confirmPasswordInput = document.querySelector('[name="txtrePW"]');
        if (newPasswordInput && confirmPasswordInput) {
            newPasswordInput.addEventListener('input', () => {
                this.updatePasswordRules(newPasswordInput.value, confirmPasswordInput.value);
            });
            confirmPasswordInput.addEventListener('input', () => {
                this.updatePasswordRules(newPasswordInput.value, confirmPasswordInput.value);
            });
        }
    }
    updatePasswordRules(newPassword, confirmPassword) {
        this.formValidationService.updatePasswordRules(newPassword, confirmPassword, this.ruleElements);
    }
    setupFormSubmit(form) {
        form.addEventListener('submit', (e) => {
            if (!this.formValidationService.validateForm(form)) {
                e.preventDefault();
                return;
            }
            const newPasswordInput = form.querySelector('[name="txtnewPW"]');
            const oldPasswordInput = form.querySelector('[name="txtoldPW"]');
            if (newPasswordInput && oldPasswordInput) {
                if (newPasswordInput.value === oldPasswordInput.value) {
                    e.preventDefault();
                    this.showPasswordError('新旧密码不能相同');
                    return;
                }
                const strength = this.formValidationService.validatePasswordStrength(newPasswordInput.value);
                if (!strength.hasLength || !strength.hasUppercase || !strength.hasLowercase || !strength.hasNumber) {
                    e.preventDefault();
                    this.showPasswordError('密码强度不符合要求');
                    return;
                }
            }
            const submitButton = form.querySelector('.form-submit');
            if (submitButton) {
                submitButton.innerHTML = '<i data-lucide="loader-2" class="w-5 h-5 animate-spin"></i>修改中...';
                submitButton.disabled = true;
                setTimeout(() => {
                    this.initializeLucideIcons();
                }, 10);
            }
        });
    }
    setupNavigationService() {
        this.navigationService.initializeBackButton('#back-button');
    }
    initializeLucideIcons() {
        if (typeof window.lucide !== 'undefined') {
            window.lucide.createIcons();
        }
    }
    showPasswordError(message) {
        let alertMessage = document.getElementById('alert-message');
        if (!alertMessage) {
            alertMessage = document.createElement('div');
            alertMessage.id = 'alert-message';
            alertMessage.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            document.body.appendChild(alertMessage);
        }
        const alertTitle = alertMessage.querySelector('.alert-title') || alertMessage;
        alertTitle.textContent = message;
        alertMessage.style.display = 'block';
        setTimeout(() => {
            alertMessage.style.display = 'none';
        }, 3000);
    }
    getPasswordStrength(password) {
        const strength = this.formValidationService.validatePasswordStrength(password);
        return strength.score;
    }
    isPasswordValid(password) {
        const strength = this.formValidationService.validatePasswordStrength(password);
        return strength.hasLength && strength.hasUppercase && strength.hasLowercase && strength.hasNumber;
    }
    resetForm() {
        const form = document.getElementById('password-form');
        if (form) {
            form.reset();
            Object.values(this.ruleElements).forEach(element => {
                if (element) {
                    const icon = element.querySelector('i');
                    if (icon) {
                        icon.setAttribute('data-lucide', 'x');
                        element.classList.remove('text-green-600');
                        element.classList.add('text-gray-500');
                    }
                }
            });
            const errorElements = form.querySelectorAll('.form-error, .text-xs.text-red-500');
            errorElements.forEach(element => element.remove());
            const inputElements = form.querySelectorAll('.form-input');
            inputElements.forEach(element => {
                element.classList.remove('border-red-500', 'error', 'border-danger');
            });
            this.initializeLucideIcons();
        }
    }
    showChangeSuccess(message = '密码修改成功') {
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        toast.textContent = message;
        document.body.appendChild(toast);
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
    showChangeError(message = '密码修改失败') {
        this.showPasswordError(message);
    }
    startCountdown() {
        let countdown = 10;
        const countdownElement = document.getElementById('countdown-text');
        if (!countdownElement) {
            console.warn('Countdown element not found');
            return;
        }
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown + '秒后自动返回登录';
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = '/waplogin.aspx';
            }
        }, 1000);
    }
}
document.addEventListener('DOMContentLoaded', () => {
    const modifyPasswordPage = new ModifyPasswordPage();
    modifyPasswordPage.init();
    window.modifyPasswordPage = modifyPasswordPage;
});
export default ModifyPasswordPage;
