/**
 * UserInfo 页面专用逻辑
 * 处理用户信息页面的交互功能
 * 
 * @version 1.0
 * <AUTHOR>
 * @date 2025-06-12
 * @location /Template/JS/Pages/UserInfo.js
 */

class UserInfoPage {
    static config = null;
    static isSubmitting = false; // 防重复提交标志

    /**
     * 初始化配置
     */
    static async loadConfig() {
        if (this.config) return this.config;

        try {
            const response = await fetch('/Data/StaticData/UIComponents.json');
            const data = await response.json();
            this.config = data.userInfoPage || this.getDefaultConfig();
            return this.config;
        } catch (error) {
            this.config = this.getDefaultConfig();
            return this.config;
        }
    }

    /**
     * 获取默认配置
     */
    static getDefaultConfig() {
        return {
            enabled: true,
            avatarTimeout: 3000,
            tooltips: {
                enabled: true,
                offset: 16
            }
        };
    }

    /**
     * 页面初始化
     */
    static async init() {
        await this.loadConfig();
        
        if (!this.config.enabled) return;

        // 头像处理已移至 AvatarHandler.js 统一管理

        // 初始化tooltip
        this.initTooltips();

        // 初始化数据显示
        this.initDataDisplays();

        // 初始化表单提交
        this.initFormSubmission();

        // 初始化个性签名对齐
        this.initRemarkAlignment();

        // 使用统一的动态图标匹配系统
        if (window.DynamicIconMatcher) {
            DynamicIconMatcher.init('.dynamic-icon');
        }

        // 初始化动态链接样式
        this.initDynamicLinks();


    }





    /**
     * 初始化tooltip
     */
    static initTooltips() {
        if (!this.config.tooltips.enabled) return;

        document.querySelectorAll('[data-tooltip]').forEach(element => {
            element.addEventListener('mouseenter', function() {
                UserInfoPage.showTooltip(this, this.getAttribute('data-tooltip'));
            });

            element.addEventListener('mouseleave', function() {
                UserInfoPage.hideTooltip();
            });
        });
    }

    /**
     * 显示tooltip
     */
    static showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'absolute z-50 bg-gray-800 text-white text-sm px-3 py-1 rounded-md shadow-lg pointer-events-none';
        tooltip.textContent = text;
        tooltip.id = 'userinfo-tooltip';

        document.body.appendChild(tooltip);

        const rect = element.getBoundingClientRect();
        tooltip.style.left = `${rect.left + rect.width / 2 - tooltip.offsetWidth / 2}px`;
        tooltip.style.top = `${rect.top - tooltip.offsetHeight - 8}px`;
    }

    /**
     * 隐藏tooltip
     */
    static hideTooltip() {
        const tooltip = document.getElementById('userinfo-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    /**
     * 初始化数据显示
     */
    static initDataDisplays() {
        this.initGoldCoinsDisplay();
        this.initLevelDisplay();
        this.initRegTimeDisplay();

        // 统一的resize事件监听（避免重复添加）
        this.initTooltipResizeHandler();
    }

    /**
     * 初始化tooltip resize处理器
     */
    static initTooltipResizeHandler() {
        // 防止重复添加监听器
        if (this._resizeHandlerAdded) return;

        const debouncedHandler = this.debounce(() => {
            // 检查所有可见的tooltip并重新定位
            const tooltips = [
                { tooltip: document.getElementById('goldCoinsTooltip'), card: document.getElementById('goldCoinsDisplay')?.parentElement, element: document.getElementById('goldCoinsDisplay') },
                { tooltip: document.getElementById('levelTooltip'), card: document.getElementById('levelDisplay')?.parentElement, element: document.getElementById('levelDisplay') },
                { tooltip: document.getElementById('regTimeTooltip'), card: document.getElementById('regTimeDisplay')?.parentElement, element: document.getElementById('regTimeDisplay') }
            ];

            tooltips.forEach(({ tooltip, card, element }) => {
                if (tooltip && card && element && tooltip.classList.contains('opacity-100')) {
                    this.positionTooltipSmart(tooltip, card, element);
                }
            });
        }, 100);

        window.addEventListener('resize', debouncedHandler);
        this._resizeHandlerAdded = true;
    }

    /**
     * 初始化妖晶显示
     */
    static initGoldCoinsDisplay() {
        const goldCoinsElement = document.getElementById('goldCoinsDisplay');
        const goldCoinsTooltip = document.getElementById('goldCoinsTooltip');
        const goldCoinsCard = goldCoinsElement?.parentElement;

        if (goldCoinsElement && goldCoinsTooltip && goldCoinsCard) {
            const fullValue = goldCoinsElement.getAttribute('data-full-value');

            goldCoinsCard.addEventListener('mouseenter', function() {
                goldCoinsTooltip.textContent = '妖晶：' + parseInt(fullValue, 10).toLocaleString();
                UserInfoPage.positionTooltipSmart(goldCoinsTooltip, goldCoinsCard, goldCoinsElement);

                goldCoinsTooltip.classList.remove('opacity-0', 'pointer-events-none');
                goldCoinsTooltip.classList.add('opacity-100', 'pointer-events-auto');
            });

            goldCoinsCard.addEventListener('mouseleave', function() {
                goldCoinsTooltip.classList.remove('opacity-100', 'pointer-events-auto');
                goldCoinsTooltip.classList.add('opacity-0', 'pointer-events-none');
            });
        }
    }

    /**
     * 初始化等级显示
     */
    static initLevelDisplay() {
        const levelElement = document.getElementById('levelDisplay');
        const levelTooltip = document.getElementById('levelTooltip');
        const levelCard = levelElement?.parentElement;

        if (levelElement && levelTooltip && levelCard) {
            const titleValue = levelElement.getAttribute('data-title');

            levelCard.addEventListener('mouseenter', function() {
                levelTooltip.textContent = '头衔：' + (titleValue || '暂无头衔');
                UserInfoPage.positionTooltipSmart(levelTooltip, levelCard, levelElement);

                levelTooltip.classList.remove('opacity-0', 'pointer-events-none');
                levelTooltip.classList.add('opacity-100', 'pointer-events-auto');
            });

            levelCard.addEventListener('mouseleave', function() {
                levelTooltip.classList.remove('opacity-100', 'pointer-events-auto');
                levelTooltip.classList.add('opacity-0', 'pointer-events-none');
            });
        }
    }

    /**
     * 初始化注册时间显示
     */
    static initRegTimeDisplay() {
        const regTimeElement = document.getElementById('regTimeDisplay');
        const regTimeTooltip = document.getElementById('regTimeTooltip');
        const regTimeCard = regTimeElement?.parentElement;

        if (regTimeElement && regTimeTooltip && regTimeCard) {
            const fullValue = regTimeElement.getAttribute('data-full-value');

            regTimeCard.addEventListener('mouseenter', function() {
                regTimeTooltip.textContent = '注册时间：' + fullValue;
                UserInfoPage.positionTooltipSmart(regTimeTooltip, regTimeCard, regTimeElement);

                regTimeTooltip.classList.remove('opacity-0', 'pointer-events-none');
                regTimeTooltip.classList.add('opacity-100', 'pointer-events-auto');
            });

            regTimeCard.addEventListener('mouseleave', function() {
                regTimeTooltip.classList.remove('opacity-100', 'pointer-events-auto');
                regTimeTooltip.classList.add('opacity-0', 'pointer-events-none');
            });
        }
    }

    /**
     * 智能定位tooltip，避免溢出容器边界
     * @param {HTMLElement} tooltip - tooltip元素
     * @param {HTMLElement} card - 卡片元素
     * @param {HTMLElement} targetElement - 目标元素（用于垂直定位）
     */
    static positionTooltipSmart(tooltip, card, targetElement) {
        // 获取白色容器（整个用户信息卡片）
        const whiteContainer = card.closest('.bg-white.rounded-xl');
        if (!whiteContainer) {
            console.warn('[UserInfo] 未找到白色容器，使用默认定位');
            this.positionTooltipDefault(tooltip, card, targetElement);
            return;
        }

        // 临时显示tooltip以获取正确尺寸，但设置为不可见
        const originalVisibility = tooltip.style.visibility;
        const originalOpacity = tooltip.style.opacity;
        const wasHidden = tooltip.classList.contains('opacity-0');

        // 临时设置为可见但透明，以获取尺寸
        tooltip.style.visibility = 'hidden';
        tooltip.style.opacity = '1';
        tooltip.classList.remove('opacity-0');
        tooltip.classList.add('opacity-100');

        // 获取各元素的位置和尺寸信息
        const containerRect = whiteContainer.getBoundingClientRect();
        const cardRect = card.getBoundingClientRect();
        const targetRect = targetElement.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();

        // 计算相对于卡片的位置（tooltip是卡片的子元素）
        const cardCenterX = cardRect.width / 2;
        const defaultLeft = cardCenterX - tooltipRect.width / 2;

        // 安全边距（移动端适配）
        const safeMargin = this.isMobile() ? 8 : 12;

        // 计算相对于白色容器的边界检测
        const cardRelativeLeft = cardRect.left - containerRect.left;
        const tooltipAbsoluteLeft = cardRelativeLeft + defaultLeft;
        const tooltipAbsoluteRight = tooltipAbsoluteLeft + tooltipRect.width;

        // 检测溢出情况
        const leftOverflow = tooltipAbsoluteLeft < safeMargin;
        const rightOverflow = tooltipAbsoluteRight > containerRect.width - safeMargin;

        let finalLeft;

        if (leftOverflow) {
            // 左溢出：左对齐到卡片左边缘
            finalLeft = 0;
        } else if (rightOverflow) {
            // 右溢出：右对齐到卡片右边缘
            finalLeft = cardRect.width - tooltipRect.width;
        } else {
            // 无溢出：居中显示
            finalLeft = defaultLeft;
        }

        // 垂直定位（相对于目标元素，在卡片内）
        const targetRelativeTop = targetRect.top - cardRect.top;
        const finalTop = targetRelativeTop - tooltipRect.height - this.config.tooltips.offset;

        // 应用定位
        tooltip.style.left = `${finalLeft}px`;
        tooltip.style.top = `${finalTop}px`;

        // 恢复原始状态
        tooltip.style.visibility = originalVisibility;
        tooltip.style.opacity = originalOpacity;
        if (wasHidden) {
            tooltip.classList.remove('opacity-100');
            tooltip.classList.add('opacity-0');
        }
    }

    /**
     * 默认tooltip定位（fallback）
     */
    static positionTooltipDefault(tooltip, card, targetElement) {
        const targetRect = targetElement.getBoundingClientRect();
        const cardRect = card.getBoundingClientRect();

        tooltip.style.left = `${targetRect.left - cardRect.left + targetRect.width / 2}px`;
        tooltip.style.top = `${targetRect.top - cardRect.top - tooltip.offsetHeight - this.config.tooltips.offset}px`;
    }

    /**
     * 检测是否为移动端
     */
    static isMobile() {
        return window.innerWidth <= 768 || 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }

    /**
     * 防抖函数
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 初始化表单提交 - AJAX方式
     */
    static initFormSubmission() {
        const messageForm = document.getElementById('messageForm');
        if (messageForm) {
            messageForm.addEventListener('submit', function(e) {
                e.preventDefault(); // 阻止默认表单提交
                UserInfoPage.submitMessageAjax();
            });
        }
    }

    /**
     * AJAX提交留言
     */
    static submitMessageAjax() {
        // 防重复提交检查
        if (this.isSubmitting) {
            console.log('[UserInfo] 正在提交中，忽略重复请求');
            return;
        }

        const messageInput = document.getElementById('messageInput');
        const messageForm = document.getElementById('messageForm');
        const sendButton = messageForm.querySelector('button[type="submit"]');
        const content = messageInput.value.trim();

        // 前端验证
        if (content.length < 2) {
            this.showToast('warning', '留言内容至少需要2个字符！');
            messageInput.focus();
            return;
        }

        // 检查提交频率限制（客户端10秒限制）
        const lastSubmitTime = localStorage.getItem('lastMessageSubmitTime');
        const currentTime = Date.now();
        if (lastSubmitTime && (currentTime - parseInt(lastSubmitTime)) < 10000) {
            const remainingSeconds = Math.ceil((10000 - (currentTime - parseInt(lastSubmitTime))) / 1000);
            this.showToast('warning', `操作过快，请${remainingSeconds}秒后再试`);
            return;
        }

        // 设置提交状态
        this.isSubmitting = true;

        // 显示提交中状态
        const originalText = sendButton.innerHTML;
        sendButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>提交中...';
        sendButton.disabled = true;

        // 构建表单数据
        const formData = new FormData();
        formData.append('action', 'add_ajax');
        formData.append('content', content);

        // 从隐藏字段获取必要参数
        const form = document.getElementById('messageForm');
        const touserid = form.querySelector('input[name="touserid"]').value;
        const siteid = form.querySelector('input[name="siteid"]').value;
        const classid = form.querySelector('input[name="classid"]').value;

        formData.append('touserid', touserid);
        formData.append('siteid', siteid);
        formData.append('classid', classid);

        // 发送AJAX请求
        fetch('/bbs/userGuessBook.aspx', {
            method: 'POST',
            body: formData
        })
        .then(response => response.text())
        .then(responseText => {
            try {
                const data = JSON.parse(responseText);

                if (data.success) {
                    // 成功处理
                    this.showToast('success', data.message);
                    messageInput.value = ''; // 清空输入框

                    // 更新最后提交时间
                    localStorage.setItem('lastMessageSubmitTime', currentTime.toString());

                    // 动态插入新留言到列表顶部
                    if (data.newMessage) {
                        this.addNewMessageToList(data.newMessage);
                    } else {
                        // 如果没有新留言数据，fallback到刷新页面
                        setTimeout(() => {
                            location.reload();
                        }, 1000);
                    }
                } else {
                    // 失败处理
                    const toastType = data.type || 'error';
                    this.showToast(toastType, data.message);

                    // 特殊错误处理
                    this.handleSubmitError(data.type, data.message);
                }
            } catch (jsonError) {
                console.error('JSON解析失败:', jsonError);
                console.log('原始响应:', responseText);
                this.showToast('error', '服务器返回了意外的响应格式');
            }
        })
        .catch(error => {
            console.error('提交请求失败:', error);
            this.showToast('error', '网络错误，请重试');
        })
        .finally(() => {
            // 恢复按钮状态
            sendButton.innerHTML = originalText;
            sendButton.disabled = false;
            // 重置提交状态
            this.isSubmitting = false;
            console.log('[UserInfo] 提交完成，重置提交状态');
        });
    }

    /**
     * 初始化个性签名对齐
     */
    static initRemarkAlignment() {
        const remarkText = document.getElementById('remarkText');
        if (!remarkText) return;

        // 检测文本是否换行
        function checkTextWrapping() {
            const lineHeight = parseInt(window.getComputedStyle(remarkText).lineHeight);
            const actualHeight = remarkText.offsetHeight;
            const isWrapped = actualHeight > lineHeight * 1.2;

            if (isWrapped) {
                remarkText.classList.remove('text-center');
                remarkText.classList.add('text-left');
            } else {
                remarkText.classList.remove('text-left');
                remarkText.classList.add('text-center');
            }
        }

        setTimeout(checkTextWrapping, 100);
        window.addEventListener('resize', checkTextWrapping);
    }

    /**
     * 初始化动态链接样式
     */
    static initDynamicLinks() {
        document.querySelectorAll('.space-y-4 .text-gray-700 a').forEach(link => {
            if (!link.classList.contains('enhanced-link') &&
                !link.classList.contains('btn-primary') &&
                !link.classList.contains('text-primary')) {
                link.classList.add('dynamic-link');
            }
        });
    }



    /**
     * 动态添加新留言到列表顶部
     */
    static addNewMessageToList(messageData) {
        const messageListContainer = document.querySelector('.space-y-0');

        // 检查是否有空状态显示
        const emptyState = document.querySelector('.text-center.py-8');
        if (emptyState) {
            emptyState.remove();
            // 如果之前是空状态，需要先创建标题和容器
            const section = document.querySelector('section .bg-white.rounded-xl.card-shadow');
            const titleHtml = '<h4 class="text-sm font-medium text-text-secondary mb-2">最新留言</h4>';
            const containerHtml = '<div class="space-y-0"></div>';
            section.insertAdjacentHTML('beforeend', titleHtml + containerHtml);
        }

        // 构建新留言HTML
        const newMessageHtml = this.buildMessageHtml(messageData);

        // 创建新留言元素
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = newMessageHtml;
        const newMessage = tempDiv.firstElementChild;

        // 添加滑入动画类
        newMessage.classList.add('animate-slide-in-from-top');

        // 插入到列表顶部
        const messageList = document.querySelector('.space-y-0');
        if (messageList) {
            messageList.insertBefore(newMessage, messageList.firstChild);

            // 头像处理由 AvatarHandler.js 自动管理
        }
    }

    /**
     * 构建留言HTML结构（UserInfo版本，不显示楼层号）
     */
    static buildMessageHtml(messageData) {
        // 处理头像显示逻辑
        let avatarHtml = '';
        if (messageData.authorAvatarUrl && !messageData.isDefaultAvatar) {
            avatarHtml = `
                <span class="avatar-fallback-small" data-fallback="true">${messageData.authorNickname.charAt(0)}</span>
                <img src="${messageData.authorAvatarUrl}"
                     alt="${messageData.authorNickname}"
                     class="w-8 h-8 object-fill absolute top-0 left-0 z-[1] hidden rounded-lg"
                     data-avatar-src="${messageData.authorAvatarUrl}"
                     onload="handleSmallAvatarLoad(this)"
                     onerror="handleSmallAvatarError(this)">
            `;
        } else {
            avatarHtml = `<span class="avatar-fallback-small" data-fallback="true">${messageData.authorNickname.charAt(0)}</span>`;
        }

        return `
            <div class="py-4 border-b border-gray-100">
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center overflow-hidden relative">
                        ${avatarHtml}
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            <a href="${messageData.authorSpaceUrl}" class="font-medium text-sm text-primary hover:text-primary/80 transition-colors">${messageData.authorNickname}</a>
                            <span class="text-xs text-gray-500 cursor-pointer relative"
                                  data-detail-time="${messageData.detailTime}"
                                  onmouseenter="showTimeTooltip(this)"
                                  onmouseleave="hideTimeTooltip(this)">${messageData.friendlyTime}</span>
                        </div>
                        <p class="text-sm text-gray-700">${messageData.content}</p>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 处理特殊错误类型
     */
    static handleSubmitError(errorType, message) {
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.querySelector('#messageForm button[type="submit"]');

        switch (errorType) {
            case 'warning':
                // 警告类型错误，焦点回到输入框
                setTimeout(() => {
                    messageInput.focus();
                }, 100);
                break;
            case 'error':
                // 严重错误，可能需要禁用提交功能
                if (message.includes('黑名单')) {
                    // 被拉黑，禁用提交功能
                    sendButton.disabled = true;
                    sendButton.innerHTML = '<i class="fas fa-ban mr-2"></i>已被禁止';
                    messageInput.disabled = true;
                    messageInput.placeholder = '您已被加入黑名单，无法发表留言';
                }
                break;
        }
    }

    /**
     * 显示Toast通知，使用 Tailwind CSS 类
     */
    static showToast(type, message) {
        // 创建toast容器
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'fixed top-20 right-5 z-toast flex flex-col gap-2';
            document.body.appendChild(toastContainer);
        }

        // 图标映射
        const iconMap = {
            success: 'check-circle',
            error: 'x-circle',
            warning: 'alert-triangle',
            info: 'info'
        };

        // 样式类映射
        const classMap = {
            success: 'toast-dynamic-success',
            error: 'toast-dynamic-error',
            warning: 'toast-dynamic-warning',
            info: 'toast-dynamic-info'
        };

        const icon = iconMap[type] || 'info';
        const toastClass = classMap[type] || 'toast-dynamic-info';
        const toastId = 'toast-' + Date.now();

        // 创建toast元素
        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = toastClass;

        toast.innerHTML = `
            <i data-lucide="${icon}" class="w-4 h-4 flex-shrink-0"></i>
            <span class="flex-1">${message}</span>
            <button onclick="UserInfoPage.closeToast('${toastId}')" class="bg-transparent border-none text-white cursor-pointer p-0 ml-2 hover:opacity-80">
                <i data-lucide="x" class="w-3.5 h-3.5"></i>
            </button>
        `;

        toastContainer.appendChild(toast);

        // 初始化Lucide图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }

        // 显示动画
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // 自动关闭
        setTimeout(() => {
            this.closeToast(toastId);
        }, 4000);
    }

    /**
     * 关闭Toast通知
     */
    static closeToast(toastId) {
        const toast = document.getElementById(toastId);
        if (toast) {
            toast.classList.remove('show');
            setTimeout(() => {
                toast.remove();
            }, 300);
        }
    }

    /**
     * 刷新配置
     */
    static async refreshConfig() {
        this.config = null;
        return await this.loadConfig();
    }
}

// 全局暴露
window.UserInfoPage = UserInfoPage;

// 兼容性：提供全局函数
window.goBack = function() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        // 这里需要从模板传递BackUrl
        window.location.href = document.querySelector('[data-back-url]')?.getAttribute('data-back-url') || '/';
    }
};

window.sendMessage = function() {
    const url = document.querySelector('[data-send-message-url]')?.getAttribute('data-send-message-url');
    if (url) window.location.href = url;
};

window.viewPosts = function() {
    const url = document.querySelector('[data-posts-url]')?.getAttribute('data-posts-url');
    if (url) window.location.href = url;
};

window.viewReplies = function() {
    const url = document.querySelector('[data-replies-url]')?.getAttribute('data-replies-url');
    if (url) window.location.href = url;
};

window.viewProfile = function() {
    const url = document.querySelector('[data-profile-url]')?.getAttribute('data-profile-url');
    if (url) window.location.href = url;
};

window.loadMoreDynamics = function() {
    const url = document.querySelector('[data-more-dynamics-url]')?.getAttribute('data-more-dynamics-url');
    if (url) window.location.href = url;
};

window.loadMoreMessages = function() {
    const url = document.querySelector('[data-more-messages-url]')?.getAttribute('data-more-messages-url');
    if (url) window.location.href = url;
};

// 头像处理已移至 AvatarHandler.js 统一管理

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', () => {
    UserInfoPage.init();
});
