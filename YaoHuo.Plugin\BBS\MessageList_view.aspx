﻿<%@ Page Language="C#" AutoEventWireup="true" Inherits="YaoHuo.Plugin.BBS.MessageList_view" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
var extraHeadHtml = "<link rel='stylesheet' href='/NetCSS/CSS/ChatMessage.css'/><script type='text/javascript' defer src='/NetCSS/JS/HyperLink.js'></script><script src='/NetCSS/JS/BBS/ui-switcher.js'></script>";
%>
<%= WapTool.showTop(this.GetLang("查看信息|查看信息|view Message"), wmlVo, true, extraHeadHtml) %>
<div class="title"><a href="/">首页</a>&gt;<a href="/bbs/messagelist.aspx">信箱</a>&gt;查看信息<span style="float: right; cursor: pointer; font-size: 14px; color: #000;" onclick="switchToNewUI()" title="切换到新版">[新版]</span></div>
<div style="padding: 7px 8px 0px 8px;" class="content">
    <b><%= bookVo.title %></b><br/>
    <b>发件人：</b><a href="<%= http_start %>bbs/userinfo.aspx?touserid=<%= bookVo.userid %>"><%= bookVo.nickname %></a><br/>
    <b>时间：</b><%= string.Format("{0:yyyy/M/d HH:mm}", bookVo.addtime) %><br/>
    <b>内容：</b><span class="retext"><%= WapTool.ToWML(bookVo.content, wmlVo) %></span><br/>
</div>
<div class="content">
    <% if (bookVo.isnew == 2) { %>
        <a class="urlbtn" href="<%= http_start %>bbs/messagelist_add.aspx?types=2&amp;touserid=<%= bookVo.userid %>&amp;id=<%= this.id %><%=string.IsNullOrEmpty(this.issystem) ? "" : "&amp;issystem=" + this.issystem %>">重发/转发</a><br/>
    <% } else { %>
        <form name="f" action="<%= http_start %>bbs/messagelist_add.aspx" method="post">
            <% if (this.needpwFlag == "1") { %>
                回复内容：<input type="text" name="content" value="" size="8" /><br/>
                我的密码：<input type="text" name="needpw" value="<%= needpw %>" size="10" /><br/>
            <% } else { %>
                <script>function adjustTextareaHeight(textarea) { if (textarea.scrollHeight > textarea.offsetHeight) { textarea.style.height = textarea.scrollHeight + 'px'; } }</script>
                <div class='centered-container'>
                    <textarea name="content" oninput="adjustTextareaHeight(this)" rows="5" style="width:98.6%;margin-bottom:2px;"></textarea>
                </div>
            <% } %>
            <input type="hidden" name="action" value="gomod" />
            <input type="hidden" name="classid" value="<%= classid %>" />
            <input type="hidden" name="siteid" value="<%= siteid %>" />
            <input type="hidden" name="types" value="<%= types %>" />
            <input type="hidden" name="issystem" value="<%= issystem %>" />
            <input type="hidden" name="toid" value="<%= id %>" />
            <input type="hidden" name="backurl" value="<%= backurl %>" />
            <input type="hidden" name="title" value="" />
            <input type="hidden" name="touseridlist" value="<%= bookVo.userid %>" />
            <input type="submit" name="g" style="margin-right:10px;" class="btn" value="发送消息" />
        </form><br/>
        <div style="padding: 6px;"></div>
        <div class="mmscontent">
            <% if (listVo != null && listVo.Count > 0) { %>
                <% for (int i = 0; i < listVo.Count; i++) { %>
                    <% if (listVo[i].userid.ToString() == this.userid) { %>
                        <div class="listmms the_me">
                            <div class="bubble"><div class="con"><%= WapTool.ToWML(listVo[i].content, wmlVo) %></div></div>
                            <div class="info"><div class="u_name">我 <label><%= string.Format("{0:yyyy/M/d HH:mm}", listVo[i].addtime) %></label></div></div>
                        </div>
                    <% } else { %>
                        <div class="listmms the_user">
                            <div class="bubble"><div class="con"><%= WapTool.ToWML(listVo[i].content, wmlVo) %></div></div>
                            <div class="info"><div class="u_name"><a href="<%= this.http_start %>bbs/userinfo.aspx?touserid=<%= listVo[i].userid %>"><%= listVo[i].nickname %>(<%= listVo[i].userid %>)</a><label><%= string.Format("{0:yyyy/M/d HH:mm}", listVo[i].addtime) %></label></div></div>
                        </div>
                    <% } %>
                <% } %>
            <% } %>
        </div>
    <% } %>
    <% if (this.isclose != "1") { %>
        <div class="line1">显示最新50条对话记录</div>
    <% } %>
</div>
<div class="btBox"><div class="bt1">
    <a class="noafter" href="<%= http_start %>bbs/messagelist.aspx?types=<%= this.types %>&amp;page=<%= this.page %><%=string.IsNullOrEmpty(this.issystem) ? "" : "&amp;issystem=" + this.issystem %>">返回上级</a>
    <a href="<%= http_start %>">返回首页</a>
</div></div>
<%= WapTool.showDown(wmlVo) %>