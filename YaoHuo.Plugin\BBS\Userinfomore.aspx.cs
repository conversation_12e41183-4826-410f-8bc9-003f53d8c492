﻿using System;
using KeLin.ClassManager.BLL;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.BBS.Models;
using YaoHuo.Plugin.WebSite.BBS.Service;

namespace YaoHuo.Plugin.BBS
{
	public class UserInfoMore : MyPageWap
	{
		private string string_10 = PubConstant.GetAppString("InstanceName");

		public string touserid = "";

		public string INFO = "";

		public string ERROR = "";

		public string backurl = "";

		public string idtype = "";

		public string myMobile = "";

		public string type = "";

		public user_Model toUserVo = new user_Model();

		protected void Page_Load(object sender, EventArgs e)
		{
			// 检查UI偏好并尝试渲染新版UI
			bool newVersionRendered = CheckAndHandleUIPreference();
			if (newVersionRendered)
			{
				return; // 阻止旧版代码执行
			}

			backurl = base.Request.QueryString.Get("backurl");
			if (backurl == null || backurl == "")
			{
				backurl = base.Request.Form.Get("backurl");
			}
			if (backurl == null || backurl == "")
			{
				backurl = "myfile.aspx?siteid=" + siteid;
			}
			backurl = ToHtm(backurl);
			backurl = HttpUtility.UrlDecode(backurl);
			backurl = WapTool.URLtoWAP(backurl);
			type = WapTool.GetSiteDefault(siteVo.Version, 27);
			touserid = GetRequestValue("touserid");
			// ✅ 使用UserService获取用户信息（使用BLL方法确保功能完整性）
			toUserVo = UserService.GetUserInfoWithBLL(touserid, siteid, string_10);
			if (toUserVo == null)
			{
				ShowTipInfo(GetLang("无资料记录，当前您查看的是匿名，也可能被管理员删除！|無資料記錄，當前您查看的是匿名，也可能被管理員刪除！|No data records, may be anonymous, may be deleted!"), backurl);
			}
			idtype = WapTool.GetIDName(siteid, touserid, toUserVo.managerlvl, lang);
			myMobile = WapTool.GetMobielUA(toUserVo.MailServerUserName, lang);
		}

		/// <summary>
		/// 检查UI偏好并处理新版UI渲染
		/// </summary>
		/// <returns>是否成功渲染新版UI</returns>
		private bool CheckAndHandleUIPreference()
		{
			string uiPreference = Request.Cookies["ui_preference"]?.Value ?? "old";

			if (uiPreference == "new")
			{
				try
				{
					RenderWithHandlebars();
					return true;
				}
				catch (System.Threading.ThreadAbortException)
				{
					return true; // 成功渲染
				}
				catch (Exception ex)
				{
					ERROR = "新版模板加载失败: " + ex.Message;
					return false;
				}
			}
			return false;
		}

		/// <summary>
		/// 使用Handlebars渲染新版UI
		/// </summary>
		private void RenderWithHandlebars()
		{
			try
			{
				// 初始化基础数据
				InitializeBasicData();

				// 构建页面数据模型
				var pageModel = BuildUserInfoMorePageModel();

				// 渲染页面
				string finalHtml = TemplateService.RenderPageWithLayout(
					"~/Template/Pages/UserInfoMore.hbs",
					pageModel,
					pageModel.PageTitle,
					new HeaderOptionsModel { ShowViewModeToggle = false }
				);

				// 输出渲染结果
				Response.Clear();
				Response.ContentType = "text/html; charset=utf-8";
				Response.Write(finalHtml);
				Response.End();
			}
			catch (System.Threading.ThreadAbortException)
			{
				throw; // Response.End()的正常行为
			}
			catch (Exception ex)
			{
				// 错误处理
				Response.Clear();
				Response.ContentType = "text/html; charset=utf-8";
				Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}</div>");
				HttpContext.Current.ApplicationInstance.CompleteRequest();
			}
		}

		/// <summary>
		/// 初始化基础数据（用于新版UI）
		/// </summary>
		private void InitializeBasicData()
		{
			backurl = base.Request.QueryString.Get("backurl");
			if (backurl == null || backurl == "")
			{
				backurl = base.Request.Form.Get("backurl");
			}
			if (backurl == null || backurl == "")
			{
				backurl = "myfile.aspx?siteid=" + siteid;
			}
			backurl = ToHtm(backurl);
			backurl = HttpUtility.UrlDecode(backurl);
			backurl = WapTool.URLtoWAP(backurl);
			type = WapTool.GetSiteDefault(siteVo.Version, 27);
			touserid = GetRequestValue("touserid");
			// ✅ 使用UserService获取用户信息（使用BLL方法确保功能完整性）
			toUserVo = UserService.GetUserInfoWithBLL(touserid, siteid, string_10);
			if (toUserVo == null)
			{
				ShowTipInfo(GetLang("无资料记录，当前您查看的是匿名，也可能被管理员删除！|無資料記錄，當前您查看的是匿名，也可能被管理員刪除！|No data records, may be anonymous, may be deleted!"), backurl);
			}
			idtype = WapTool.GetIDName(siteid, touserid, toUserVo.managerlvl, lang);
			myMobile = WapTool.GetMobielUA(toUserVo.MailServerUserName, lang);
		}

		/// <summary>
		/// 构建详细资料页面数据模型
		/// </summary>
		/// <returns>页面数据模型</returns>
		private UserInfoMorePageModel BuildUserInfoMorePageModel()
		{
			var pageModel = new UserInfoMorePageModel();

			// 设置页面标题
			pageModel.PageTitle = toUserVo.nickname + "的详细资料";

			// 设置基本信息
			pageModel.TargetUserId = touserid;
			pageModel.IsOwnProfile = (userid == touserid);

			// 构建基本信息（论坛资料）
			pageModel.BasicInfo = BuildBasicInfo();

			// 构建个人资料
			pageModel.PersonalInfo = BuildPersonalInfo();

			// 构建权限控制
			pageModel.Permission = BuildPermissionModel();

			// 设置站点信息
			pageModel.SiteInfo.SiteId = siteid;
			pageModel.SiteInfo.BackUrl = backurl;
			pageModel.SiteInfo.HttpStart = http_start;
			pageModel.SiteInfo.HomeUrl = "/";

			// 设置隐藏字段
			pageModel.HiddenFields.SiteId = siteid;
			pageModel.HiddenFields.Sid = sid;
			pageModel.HiddenFields.BackUrl = backurl;

			return pageModel;
		}

		/// <summary>
		/// 构建用户基本信息（论坛资料）
		/// </summary>
		/// <returns>基本信息模型</returns>
		private UserInfoMoreBasicModel BuildBasicInfo()
		{
			var basicInfo = new UserInfoMoreBasicModel();

			basicInfo.UserId = toUserVo.userid.ToString();
			basicInfo.Nickname = toUserVo.nickname;
			basicInfo.DisplayNickname = toUserVo.nickname; // 可以后续添加HTML格式化
			basicInfo.Money = toUserVo.money;
			basicInfo.MoneyDisplay = toUserVo.money.ToString();
			basicInfo.Experience = toUserVo.expr;
			basicInfo.Level = WapTool.GetLevl(siteVo.lvlNumer, toUserVo.expr, toUserVo.money, type);
			basicInfo.Title = WapTool.GetHandle(siteVo.lvlNumer, toUserVo.expr, toUserVo.money, type);
			basicInfo.Identity = WapTool.GetMyID(toUserVo.idname, this.lang);
			basicInfo.Permission = idtype;
			basicInfo.MedalHtml = WapTool.GetMedal(toUserVo.moneyname, this.http_start);
			basicInfo.OnlineStatus = WapTool.GetOnline(this.http_start, toUserVo.isonline.ToString(), toUserVo.sex.ToString());
			basicInfo.LoginTimeDisplay = WapTool.DateToString(toUserVo.LoginTimes, this.lang, 0);
			basicInfo.RegisterTime = string.Format("{0:yyyy/MM/dd HH:mm}", toUserVo.RegTime);
			basicInfo.Remark = WapTool.ToWML(toUserVo.remark, wmlVo);

			return basicInfo;
		}

		/// <summary>
		/// 构建用户个人资料
		/// </summary>
		/// <returns>个人资料模型</returns>
		private UserPersonalInfoModel BuildPersonalInfo()
		{
			var personalInfo = new UserPersonalInfoModel();

			personalInfo.Sex = (int)toUserVo.sex;
			personalInfo.SexDisplay = (toUserVo.sex == 1) ? GetLang("男|男|Male") : GetLang("女|女|Female");
			personalInfo.Age = (int)toUserVo.age;
			personalInfo.Height = toUserVo.shenggao;
			personalInfo.Weight = toUserVo.tizhong;
			personalInfo.Constellation = toUserVo.xingzuo;
			personalInfo.MaritalStatus = toUserVo.fenfuo;
			personalInfo.Profession = toUserVo.zhiye;
			personalInfo.City = toUserVo.city;

			// 解析爱好信息
			if (!string.IsNullOrEmpty(toUserVo.aihao))
			{
				string aihaoData = toUserVo.aihao + "__";
				string[] arryqq = aihaoData.Split('_');
				if (arryqq.Length > 0)
				{
					personalInfo.Hobby = arryqq[0];
				}
				if (arryqq.Length > 1)
				{
					personalInfo.QQ = arryqq[1];
				}
			}

			personalInfo.Email = toUserVo.email;

			return personalInfo;
		}

		/// <summary>
		/// 构建权限控制模型
		/// </summary>
		/// <returns>权限控制模型</returns>
		private UserPermissionModel BuildPermissionModel()
		{
			var permission = new UserPermissionModel();

			// 检查权限
			string seeRight = WapTool.GetSiteDefault(siteVo.Version, 35);
			string seeInfo = this.http_start + "bbs/toGroupInfo.aspx?siteid=" + this.siteid + "&amp;sid=" + this.sid;

			if (this.userid == this.touserid || this.IsCheckManagerLvl("|00|01|", "") == true)
			{
				seeRight = "0," + seeRight;
			}
			seeRight = "," + seeRight + ",";

			// 设置权限
			permission.CanViewDetails = this.IsCheckManagerLvl("|00|01|02|03|04|", "");
			permission.IsOwnerOrAdmin = (this.userid == this.touserid || this.IsCheckManagerLvl("|00|01|", "") == true);
			permission.CanViewSensitiveInfo = (seeRight.IndexOf(",0,") >= 0 ||
				(seeRight.IndexOf("," + userVo.SessionTimeout + ",") >= 0 &&
				 WapTool.showIDEndTime(siteVo.siteid, userVo.userid, userVo.endTime) > 0));
			permission.VipUpgradeUrl = seeInfo;
			permission.PermissionLevel = seeRight;

			return permission;
		}


	}
}