using System;
using System.Collections.Generic;

namespace YaoHuo.Plugin.WebSite.Services.Config.Models
{
    /// <summary>
    /// 身份配置根模型
    /// </summary>
    public class IdentityConfigRoot
    {
        /// <summary>
        /// 配置元数据
        /// </summary>
        public ConfigMetadata Config { get; set; } = new ConfigMetadata();

        /// <summary>
        /// 身份类型列表（对应JSON中的identityTypes字段）
        /// </summary>
        public List<IdentityOption> IdentityTypes { get; set; } = new List<IdentityOption>();
    }

    /// <summary>
    /// 配置元数据
    /// </summary>
    public class ConfigMetadata
    {
        /// <summary>
        /// 配置版本
        /// </summary>
        public string Version { get; set; } = "1.0";

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;

        /// <summary>
        /// 配置描述
        /// </summary>
        public string Description { get; set; } = "";

        /// <summary>
        /// 配置作者
        /// </summary>
        public string Author { get; set; } = "";

        /// <summary>
        /// 环境标识
        /// </summary>
        public string Environment { get; set; } = "production";
    }

    /// <summary>
    /// 身份选项模型
    /// </summary>
    public class IdentityOption
    {
        /// <summary>
        /// 身份ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 身份名称
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// 身份显示名称
        /// </summary>
        public string DisplayName { get; set; } = "";

        /// <summary>
        /// 价格（RMB）
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 价格（妖晶）
        /// </summary>
        public long CoinPrice { get; set; }

        /// <summary>
        /// 计费周期（月/年）
        /// </summary>
        public string Period { get; set; } = "月";

        /// <summary>
        /// 身份类型（basic, premium, vip）
        /// </summary>
        public string Type { get; set; } = "basic";

        /// <summary>
        /// 是否为彩色昵称类型
        /// </summary>
        public bool IsColorNickname { get; set; } = false;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 名称CSS类
        /// </summary>
        public string NameCssClass { get; set; } = "";

        /// <summary>
        /// 图标URL
        /// </summary>
        public string IconUrl { get; set; } = "";

        /// <summary>
        /// 颜色代码（用于旧版UI）
        /// </summary>
        public string ColorCode { get; set; } = "";

        /// <summary>
        /// 图标文件名（用于旧版UI）
        /// </summary>
        public string IconFileName { get; set; } = "";

        /// <summary>
        /// 特权列表
        /// </summary>
        public List<string> Privileges { get; set; } = new List<string>();

        /// <summary>
        /// 颜色选项（仅用于彩色昵称）
        /// </summary>
        public List<ColorOption> ColorOptions { get; set; } = new List<ColorOption>();
    }

    /// <summary>
    /// 颜色选项模型
    /// </summary>
    public class ColorOption
    {
        /// <summary>
        /// 颜色代码（如"red"）
        /// </summary>
        public string Color { get; set; } = "";

        /// <summary>
        /// 颜色名称
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// 目标ID
        /// </summary>
        public int TargetId { get; set; }

        /// <summary>
        /// 颜色代码（十六进制，如"#FF0000"）
        /// </summary>
        public string ColorCode { get; set; } = "";

        /// <summary>
        /// 是否为默认选中
        /// </summary>
        public bool IsDefault { get; set; }
    }
}