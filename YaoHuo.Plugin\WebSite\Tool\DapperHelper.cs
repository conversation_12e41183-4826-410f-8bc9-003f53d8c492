using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using Dapper;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.Template.Models;

namespace YaoHuo.Plugin.WebSite.Tool
{
    /// <summary>
    /// Dapper数据访问辅助类
    /// 提供统一的数据库连接管理和参数验证
    /// </summary>
    public static class DapperHelper
    {
        /// <summary>
        /// 获取数据库连接（仅供内部使用，避免资源泄露）
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        /// <returns>数据库连接对象</returns>
        private static IDbConnection GetConnection(string connectionString)
        {
            if (string.IsNullOrWhiteSpace(connectionString))
                throw new ArgumentException("连接字符串不能为空", nameof(connectionString));
                
            return new SqlConnection(connectionString);
        }

        /// <summary>
        /// 验证参数是否为数字
        /// </summary>
        /// <param name="values">要验证的参数值</param>
        /// <exception cref="ArgumentException">当参数不是数字时抛出异常</exception>
        public static void ValidateNumeric(params string[] values)
        {
            if (values == null || values.Length == 0)
                return;

            foreach (var value in values)
            {
                if (string.IsNullOrWhiteSpace(value) || !WapTool.IsNumeric(value))
                    throw new ArgumentException($"参数必须为数字: {value}");
            }
        }

        /// <summary>
        /// 安全地将字符串转换为长整型
        /// </summary>
        /// <param name="value">要转换的字符串</param>
        /// <param name="paramName">参数名称（用于异常信息）</param>
        /// <returns>转换后的长整型值</returns>
        public static long SafeParseLong(string value, string paramName = "参数")
        {
            if (string.IsNullOrWhiteSpace(value))
                throw new ArgumentException($"{paramName}不能为空");

            if (!long.TryParse(value, out long result))
                throw new ArgumentException($"{paramName}必须为有效的数字: {value}");

            return result;
        }

        /// <summary>
        /// 安全地将字符串转换为整型
        /// </summary>
        /// <param name="value">要转换的字符串</param>
        /// <param name="paramName">参数名称（用于异常信息）</param>
        /// <returns>转换后的整型值</returns>
        public static int SafeParseInt(string value, string paramName = "参数")
        {
            if (string.IsNullOrWhiteSpace(value))
                throw new ArgumentException($"{paramName}不能为空");

            if (!int.TryParse(value, out int result))
                throw new ArgumentException($"{paramName}必须为有效的数字: {value}");

            return result;
        }

        /// <summary>
        /// 限制字符串长度（用于数据库字段长度限制）
        /// </summary>
        /// <param name="value">原始字符串</param>
        /// <param name="maxLength">最大长度</param>
        /// <returns>截取后的字符串</returns>
        public static string LimitLength(string value, int maxLength)
        {
            if (string.IsNullOrEmpty(value))
                return value ?? "";

            return value.Length > maxLength ? value.Substring(0, maxLength) : value;
        }

        /// <summary>
        /// 执行SQL查询并返回单个值
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="connectionString">连接字符串</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="parameters">参数对象</param>
        /// <returns>查询结果</returns>
        public static T ExecuteScalar<T>(string connectionString, string sql, object parameters = null)
        {
            using (var connection = GetConnection(connectionString))
            {
                return connection.ExecuteScalar<T>(sql, parameters);
            }
        }

        /// <summary>
        /// 执行SQL命令（INSERT、UPDATE、DELETE）
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="parameters">参数对象</param>
        /// <returns>受影响的行数</returns>
        public static int Execute(string connectionString, string sql, object parameters = null)
        {
            using (var connection = GetConnection(connectionString))
            {
                return connection.Execute(sql, parameters);
            }
        }

        /// <summary>
        /// 执行SQL查询并返回结果集
        /// </summary>
        /// <typeparam name="T">结果类型</typeparam>
        /// <param name="connectionString">连接字符串</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="parameters">参数对象</param>
        /// <returns>查询结果集</returns>
        public static System.Collections.Generic.IEnumerable<T> Query<T>(string connectionString, string sql, object parameters = null)
        {
            using (var connection = GetConnection(connectionString))
            {
                return connection.Query<T>(sql, parameters);
            }
        }

        /// <summary>
        /// 执行SQL查询并返回第一个结果或默认值
        /// </summary>
        /// <typeparam name="T">结果类型</typeparam>
        /// <param name="connectionString">连接字符串</param>
        /// <param name="sql">SQL语句</param>
        /// <param name="parameters">参数对象</param>
        /// <returns>第一个查询结果或默认值</returns>
        public static T QueryFirstOrDefault<T>(string connectionString, string sql, object parameters = null)
        {
            using (var connection = GetConnection(connectionString))
            {
                return connection.QueryFirstOrDefault<T>(sql, parameters);
            }
        }

        /// <summary>
        /// 在事务中执行多个SQL命令
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        /// <param name="actions">要执行的操作列表</param>
        public static void ExecuteInTransaction(string connectionString, params System.Action<IDbConnection, IDbTransaction>[] actions)
        {
            using (var connection = GetConnection(connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        foreach (var action in actions)
                        {
                            action(connection, transaction);
                        }
                        transaction.Commit();
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        // ❌ 已删除 BuildInCondition 方法 - 存在SQL注入风险
        // 正确做法：直接在SQL中写字段名，将值列表作为参数传递
        // 示例：string sql = "SELECT * FROM [User] WHERE userId IN @Ids";
        //      var result = Query<User>(connectionString, sql, new { Ids = idList });
    }

    /// <summary>
    /// 统一查询构建器 - 支持链式调用构建安全的WHERE条件
    /// </summary>
    public class QueryBuilder
    {
        private readonly List<string> _conditions = new List<string>();
        private readonly Dictionary<string, object> _parameters = new Dictionary<string, object>();
        private int _parameterIndex = 0;

        /// <summary>
        /// 添加WHERE条件
        /// </summary>
        /// <param name="condition">条件表达式（使用@ParamN占位符）</param>
        /// <param name="value">参数值</param>
        /// <returns>当前QueryBuilder实例，支持链式调用</returns>
        public QueryBuilder Where(string condition, object value)
        {
            var paramName = $"Param{_parameterIndex++}";
            var actualCondition = condition.Replace("@ParamN", $"@{paramName}");
            _conditions.Add(actualCondition);
            _parameters[paramName] = value;
            return this;
        }

        /// <summary>
        /// 条件性添加WHERE条件
        /// </summary>
        /// <param name="predicate">判断条件</param>
        /// <param name="condition">条件表达式（使用@ParamN占位符）</param>
        /// <param name="value">参数值</param>
        /// <returns>当前QueryBuilder实例，支持链式调用</returns>
        public QueryBuilder WhereIf(bool predicate, string condition, object value)
        {
            if (predicate)
            {
                return Where(condition, value);
            }
            return this;
        }

        /// <summary>
        /// 条件性添加WHERE条件（支持延迟求值）
        /// </summary>
        /// <param name="predicate">判断条件</param>
        /// <param name="condition">条件表达式（使用@ParamN占位符）</param>
        /// <param name="valueFactory">参数值工厂函数，仅在条件为true时执行</param>
        /// <returns>当前QueryBuilder实例，支持链式调用</returns>
        public QueryBuilder WhereIf(bool predicate, string condition, Func<object> valueFactory)
        {
            if (predicate)
            {
                return Where(condition, valueFactory());
            }
            return this;
        }

        /// <summary>
        /// 构建最终的SQL和参数
        /// </summary>
        /// <param name="baseQuery">基础查询语句</param>
        /// <returns>包含完整SQL和参数的元组</returns>
        public (string Sql, object Parameters) Build(string baseQuery)
        {
            var whereClause = _conditions.Count > 0 ?
                $"WHERE {string.Join(" AND ", _conditions)}" : "";

            var finalSql = string.IsNullOrWhiteSpace(whereClause) ?
                baseQuery : $"{baseQuery} {whereClause}";

            return (finalSql, _parameters);
        }

        /// <summary>
        /// 构建COUNT查询和数据查询的SQL
        /// </summary>
        /// <param name="selectClause">SELECT子句</param>
        /// <param name="fromClause">FROM子句</param>
        /// <param name="orderByClause">ORDER BY子句（仅用于数据查询）</param>
        /// <returns>包含COUNT SQL、数据SQL和参数的元组</returns>
        public (string CountSql, string DataSql, object Parameters) BuildWithCount(
            string selectClause, string fromClause, string orderByClause = "")
        {
            var whereClause = _conditions.Count > 0 ?
                $"WHERE {string.Join(" AND ", _conditions)}" : "";

            var countSql = string.IsNullOrWhiteSpace(whereClause) ?
                $"SELECT COUNT(*) FROM {fromClause}" :
                $"SELECT COUNT(*) FROM {fromClause} {whereClause}";

            var dataSql = string.IsNullOrWhiteSpace(whereClause) ?
                $"{selectClause} FROM {fromClause}" :
                $"{selectClause} FROM {fromClause} {whereClause}";

            if (!string.IsNullOrWhiteSpace(orderByClause))
            {
                dataSql += $" {orderByClause}";
            }

            return (countSql, dataSql, _parameters);
        }

        /// <summary>
        /// 添加原始WHERE条件（不进行参数替换）
        /// </summary>
        /// <param name="condition">原始条件表达式</param>
        /// <param name="value">参数值（可选）</param>
        /// <returns>当前QueryBuilder实例，支持链式调用</returns>
        public QueryBuilder WhereRaw(string condition, object value = null)
        {
            if (value != null)
            {
                var paramName = $"Param{_parameterIndex++}";
                var actualCondition = condition.Replace("@ParamN", $"@{paramName}");
                _conditions.Add(actualCondition);
                _parameters[paramName] = value;
            }
            else
            {
                _conditions.Add(condition);
            }
            return this;
        }

        /// <summary>
        /// 添加原始WHERE条件（支持多个参数）
        /// </summary>
        /// <param name="condition">原始条件表达式，使用@Param0, @Param1, @Param2等占位符</param>
        /// <param name="values">参数值数组</param>
        /// <returns>当前QueryBuilder实例，支持链式调用</returns>
        public QueryBuilder WhereRaw(string condition, params object[] values)
        {
            if (values != null && values.Length > 0)
            {
                var actualCondition = condition;
                for (int i = 0; i < values.Length; i++)
                {
                    var paramName = $"Param{_parameterIndex++}";
                    actualCondition = actualCondition.Replace($"@Param{i}", $"@{paramName}");
                    _parameters[paramName] = values[i];
                }
                _conditions.Add(actualCondition);
            }
            else
            {
                _conditions.Add(condition);
            }
            return this;
        }

        /// <summary>
        /// 添加NOT IN条件
        /// </summary>
        /// <param name="column">列名</param>
        /// <param name="values">值列表</param>
        /// <returns>当前QueryBuilder实例，支持链式调用</returns>
        public QueryBuilder WhereNotIn(string column, IEnumerable<object> values)
        {
            var valueList = values.ToList();
            if (valueList.Count == 0) return this;

            var paramNames = new List<string>();
            foreach (var value in valueList)
            {
                var paramName = $"Param{_parameterIndex++}";
                paramNames.Add($"@{paramName}");
                _parameters[paramName] = value;
            }

            var condition = $"{column} NOT IN ({string.Join(",", paramNames)})";
            _conditions.Add(condition);
            return this;
        }

        /// <summary>
        /// 添加IN条件
        /// </summary>
        /// <param name="column">列名</param>
        /// <param name="values">值列表</param>
        /// <returns>当前QueryBuilder实例，支持链式调用</returns>
        public QueryBuilder WhereIn(string column, IEnumerable<object> values)
        {
            var valueList = values.ToList();
            if (valueList.Count == 0) return this;

            var paramNames = new List<string>();
            foreach (var value in valueList)
            {
                var paramName = $"Param{_parameterIndex++}";
                paramNames.Add($"@{paramName}");
                _parameters[paramName] = value;
            }

            var condition = $"{column} IN ({string.Join(",", paramNames)})";
            _conditions.Add(condition);
            return this;
        }

        /// <summary>
        /// 获取WHERE子句（不包含WHERE关键字）
        /// </summary>
        /// <returns>WHERE条件字符串</returns>
        public string GetWhereClause()
        {
            return _conditions.Count > 0 ? string.Join(" AND ", _conditions) : "";
        }

        /// <summary>
        /// 获取参数字典
        /// </summary>
        /// <returns>参数字典</returns>
        public object GetParameters()
        {
            return _parameters;
        }

        /// <summary>
        /// 克隆当前QueryBuilder
        /// </summary>
        /// <returns>新的QueryBuilder实例</returns>
        public QueryBuilder Clone()
        {
            var clone = new QueryBuilder();
            foreach (var condition in _conditions)
            {
                clone._conditions.Add(condition);
            }
            foreach (var param in _parameters)
            {
                clone._parameters[param.Key] = param.Value;
            }
            clone._parameterIndex = _parameterIndex;
            return clone;
        }

        /// <summary>
        /// 重置构建器状态
        /// </summary>
        public void Reset()
        {
            _conditions.Clear();
            _parameters.Clear();
            _parameterIndex = 0;
        }
    }

    /// <summary>
    /// 分页查询结果
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class PagedResult<T>
    {
        /// <summary>
        /// 数据列表
        /// </summary>
        public List<T> Data { get; set; } = new List<T>();

        /// <summary>
        /// 总记录数
        /// </summary>
        public long Total { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int Page { get; set; }

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)Total / PageSize);

        /// <summary>
        /// 是否有下一页
        /// </summary>
        public bool HasNextPage => Page < TotalPages;

        /// <summary>
        /// 是否有上一页
        /// </summary>
        public bool HasPreviousPage => Page > 1;

        /// <summary>
        /// 转换为PaginationModel
        /// </summary>
        /// <returns>PaginationModel实例</returns>
        public PaginationModel ToPaginationModel()
        {
            return new PaginationModel
            {
                CurrentPage = Page,
                TotalPages = TotalPages,
                TotalItems = (int)Total,
                PageSize = PageSize,
                HasPages = Total > PageSize,
                ShowPagination = Total > PageSize,
                IsFirstPage = Page <= 1,
                IsLastPage = Page >= TotalPages,
                Total = (int)Total
            };
        }
    }

    /// <summary>
    /// 事务操作辅助类 - 专门用于资金等关键操作
    /// </summary>
    public static class TransactionHelper
    {
        /// <summary>
        /// 执行事务性资金操作（使用默认隔离级别）
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        /// <param name="operation">事务内的操作</param>
        /// <param name="timeoutSeconds">事务超时时间（秒）</param>
        public static void ExecuteMoneyTransaction(string connectionString, Action<IDbConnection, IDbTransaction> operation, int timeoutSeconds = 30)
        {
            ExecuteMoneyTransaction(connectionString, operation, System.Transactions.IsolationLevel.ReadCommitted, timeoutSeconds);
        }

        /// <summary>
        /// 执行事务性资金操作（可指定隔离级别）
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        /// <param name="operation">事务内的操作</param>
        /// <param name="isolationLevel">事务隔离级别</param>
        /// <param name="timeoutSeconds">事务超时时间（秒）</param>
        public static void ExecuteMoneyTransaction(string connectionString, Action<IDbConnection, IDbTransaction> operation,
            System.Transactions.IsolationLevel isolationLevel, int timeoutSeconds = 30)
        {
            using (var scope = new System.Transactions.TransactionScope(
                System.Transactions.TransactionScopeOption.Required,
                new System.Transactions.TransactionOptions
                {
                    IsolationLevel = isolationLevel,
                    Timeout = TimeSpan.FromSeconds(timeoutSeconds)
                }))
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            operation(connection, transaction);
                            transaction.Commit();
                            scope.Complete();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            // 统一的错误日志记录
                            System.Diagnostics.Debug.WriteLine($"[TransactionHelper] 资金事务失败: {ex.Message}");
                            System.Diagnostics.Debug.WriteLine($"[TransactionHelper] 堆栈跟踪: {ex.StackTrace}");
                            throw; // 重新抛出异常，让业务层处理
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获取用户余额并加行级锁
        /// </summary>
        public static long GetUserBalanceWithLock(IDbConnection connection, IDbTransaction transaction, long userId, long siteId)
        {
            string sql = "SELECT money FROM [user] WITH (UPDLOCK, ROWLOCK) WHERE userid = @UserId AND siteid = @SiteId";
            return connection.QuerySingleOrDefault<long>(sql, new { UserId = userId, SiteId = siteId }, transaction);
        }

        /// <summary>
        /// 获取用户银行余额并加行级锁
        /// </summary>
        public static long GetUserBankBalanceWithLock(IDbConnection connection, IDbTransaction transaction, long userId, long siteId)
        {
            string sql = "SELECT mybankmoney FROM [user] WITH (UPDLOCK, ROWLOCK) WHERE userid = @UserId AND siteid = @SiteId";
            return connection.QuerySingleOrDefault<long>(sql, new { UserId = userId, SiteId = siteId }, transaction);
        }

        /// <summary>
        /// 更新用户余额（统一的余额操作方法）
        /// </summary>
        /// <param name="connection">数据库连接</param>
        /// <param name="transaction">事务</param>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">站点ID</param>
        /// <param name="amount">金额变动（正数为增加，负数为减少）</param>
        public static void UpdateUserMoney(IDbConnection connection, IDbTransaction transaction, long userId, long siteId, long amount)
        {
            string sql = "UPDATE [user] SET money = money + @Amount WHERE userid = @UserId AND siteid = @SiteId";
            connection.Execute(sql, new { Amount = amount, UserId = userId, SiteId = siteId }, transaction);
        }

        /// <summary>
        /// 更新用户银行余额（统一的银行余额操作方法）
        /// </summary>
        /// <param name="connection">数据库连接</param>
        /// <param name="transaction">事务</param>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">站点ID</param>
        /// <param name="amount">金额变动（正数为增加，负数为减少）</param>
        public static void UpdateUserBankMoney(IDbConnection connection, IDbTransaction transaction, long userId, long siteId, long amount)
        {
            string sql = "UPDATE [user] SET mybankmoney = mybankmoney + @Amount WHERE userid = @UserId AND siteid = @SiteId";
            connection.Execute(sql, new { Amount = amount, UserId = userId, SiteId = siteId }, transaction);
        }

        /// <summary>
        /// 发送系统消息（统一的消息发送方法）
        /// </summary>
        /// <param name="connection">数据库连接</param>
        /// <param name="transaction">事务</param>
        /// <param name="siteId">站点ID</param>
        /// <param name="fromUserId">发送者ID</param>
        /// <param name="fromNickname">发送者昵称</param>
        /// <param name="toUserId">接收者ID</param>
        /// <param name="title">消息标题</param>
        /// <param name="content">消息内容</param>
        public static void SendSystemMessage(IDbConnection connection, IDbTransaction transaction,
            long siteId, long fromUserId, string fromNickname, long toUserId, string title, string content)
        {
            string sql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                          VALUES(@SiteId,@UserId,@Nickname,@Title,@Content,@ToUserId,1)";
            connection.Execute(sql, new {
                SiteId = siteId,
                UserId = fromUserId,
                Nickname = fromNickname,
                Title = title,
                Content = content,
                ToUserId = toUserId
            }, transaction);
        }

        /// <summary>
        /// 记录银行日志（已弃用，建议使用SaveBankLogWithBalance避免死锁）
        /// </summary>
        /// <param name="connection">数据库连接</param>
        /// <param name="transaction">事务</param>
        /// <param name="siteId">站点ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="actionName">操作名称</param>
        /// <param name="money">金额变动</param>
        /// <param name="operaUserId">操作者ID</param>
        /// <param name="operaNickname">操作者昵称</param>
        /// <param name="remark">备注</param>
        /// <param name="ip">IP地址</param>
        [Obsolete("此方法存在死锁风险，请使用SaveBankLogWithBalance方法并传入当前余额")]
        public static void SaveBankLog(IDbConnection connection, IDbTransaction transaction,
            long siteId, long userId, string actionName, decimal money, long operaUserId, string operaNickname, string remark, string ip)
        {
            // ❌ 此方法已弃用：使用INSERT...SELECT模式存在死锁风险
            // ✅ 建议使用：SaveBankLogWithBalance方法，传入准确的当前余额
            throw new NotSupportedException("SaveBankLog方法已弃用，存在死锁风险。请使用SaveBankLogWithBalance方法，并传入准确的当前余额。");
        }

        /// <summary>
        /// 记录银行日志（事务内安全版本，传入余额避免死锁）
        /// </summary>
        /// <param name="connection">数据库连接</param>
        /// <param name="transaction">事务</param>
        /// <param name="siteId">站点ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="actionName">操作名称</param>
        /// <param name="money">金额变动</param>
        /// <param name="operaUserId">操作者ID</param>
        /// <param name="operaNickname">操作者昵称</param>
        /// <param name="remark">备注</param>
        /// <param name="ip">IP地址</param>
        /// <param name="currentBalance">当前余额（避免SELECT user表）</param>
        public static void SaveBankLogWithBalance(IDbConnection connection, IDbTransaction transaction,
            long siteId, long userId, string actionName, decimal money, long operaUserId, string operaNickname, string remark, string ip, long currentBalance)
        {
            actionName = WapTool.Left(actionName, 10);
            remark = WapTool.Left(remark, 200);

            // ✅ 直接INSERT，不再SELECT user表，避免死锁
            string sql = @"INSERT INTO wap_bankLog
                          (siteid, userid, actionName, money, leftMoney, opera_userid, opera_nickname, remark, ip, addtime, HangBiaoShi)
                          VALUES (@SiteId, @UserId, @ActionName, @Money, @LeftMoney, @OperaUserId, @OperaNickname, @Remark, @IP, GETDATE(), NULL)";

            connection.Execute(sql, new {
                SiteId = siteId,
                UserId = userId,
                ActionName = actionName,
                Money = money,
                LeftMoney = currentBalance.ToString(),
                OperaUserId = operaUserId,
                OperaNickname = operaNickname,
                Remark = remark,
                IP = ip
            }, transaction);
        }
    }

    /// <summary>
    /// 分页查询辅助类
    /// </summary>
    public static class PaginationHelper
    {
        /// <summary>
        /// 执行分页查询
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="connectionString">连接字符串</param>
        /// <param name="baseQuery">基础查询语句（不包含ORDER BY）</param>
        /// <param name="parameters">查询参数</param>
        /// <param name="page">页码（从1开始）</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="orderBy">排序子句（如："ORDER BY id DESC"）</param>
        /// <returns>分页查询结果</returns>
        public static PagedResult<T> GetPagedData<T>(
            string connectionString,
            string baseQuery,
            object parameters,
            int page,
            int pageSize,
            string orderBy = "ORDER BY id DESC")
        {
            if (page < 1) page = 1;
            if (pageSize < 1) pageSize = 10;

            // 构建COUNT查询
            var countSql = $"SELECT COUNT(*) FROM ({baseQuery}) AS CountQuery";
            var total = DapperHelper.ExecuteScalar<long>(connectionString, countSql, parameters);

            // 构建分页数据查询 - 使用兼容SQL Server 2008+的ROW_NUMBER()语法
            var offset = (page - 1) * pageSize;

            // 提取ORDER BY子句中的排序部分（去掉ORDER BY关键字）
            var orderByClause = orderBy.Replace("ORDER BY", "").Trim();

            var dataSql = $@"
                SELECT * FROM (
                    SELECT ROW_NUMBER() OVER (ORDER BY {orderByClause}) AS RowNum, *
                    FROM ({baseQuery}) AS BaseQuery
                ) AS PagedQuery
                WHERE RowNum > {offset} AND RowNum <= {offset + pageSize}";

            var data = DapperHelper.Query<T>(connectionString, dataSql, parameters);

            return new PagedResult<T>
            {
                Data = data.ToList(),
                Total = total,
                Page = page,
                PageSize = pageSize
            };
        }

        /// <summary>
        /// 使用QueryBuilder执行分页查询
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="connectionString">连接字符串</param>
        /// <param name="selectClause">SELECT子句</param>
        /// <param name="fromClause">FROM子句</param>
        /// <param name="queryBuilder">查询构建器</param>
        /// <param name="page">页码（从1开始）</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="orderBy">排序子句（如："ORDER BY id DESC"）</param>
        /// <param name="useNoLock">是否使用WITH(NOLOCK)提示</param>
        /// <returns>分页查询结果</returns>
        public static PagedResult<T> GetPagedDataWithBuilder<T>(
            string connectionString,
            string selectClause,
            string fromClause,
            QueryBuilder queryBuilder,
            int page,
            int pageSize,
            string orderBy = "ORDER BY id DESC",
            bool useNoLock = false)
        {
            if (page < 1) page = 1;
            if (pageSize < 1) pageSize = 10;

            // 如果需要使用NOLOCK，添加到fromClause
            if (useNoLock && !fromClause.ToUpper().Contains("WITH(NOLOCK)"))
            {
                // 检查是否已经有别名，如果没有则添加NOLOCK
                if (fromClause.Trim().Split(' ').Length == 1)
                {
                    fromClause += " WITH(NOLOCK)";
                }
                else
                {
                    // 如果有别名，在表名后添加NOLOCK
                    var parts = fromClause.Split(' ');
                    if (parts.Length >= 2)
                    {
                        fromClause = parts[0] + " WITH(NOLOCK) " + string.Join(" ", parts.Skip(1));
                    }
                }
            }

            // 使用QueryBuilder构建SQL
            var (countSql, baseSql, parameters) = queryBuilder.BuildWithCount(selectClause, fromClause);

            // 执行COUNT查询
            var total = DapperHelper.ExecuteScalar<long>(connectionString, countSql, parameters);

            // 构建分页数据查询 - 使用兼容SQL Server 2008+的ROW_NUMBER()语法
            var offset = (page - 1) * pageSize;

            // 提取ORDER BY子句中的排序部分（去掉ORDER BY关键字）
            var orderByClause = orderBy.Replace("ORDER BY", "").Trim();

            var dataSql = $@"
                SELECT * FROM (
                    SELECT ROW_NUMBER() OVER (ORDER BY {orderByClause}) AS RowNum, *
                    FROM ({baseSql}) AS BaseQuery
                ) AS PagedQuery
                WHERE RowNum > {offset} AND RowNum <= {offset + pageSize}";

            var data = DapperHelper.Query<T>(connectionString, dataSql, parameters);

            return new PagedResult<T>
            {
                Data = data.ToList(),
                Total = total,
                Page = page,
                PageSize = pageSize
            };
        }
    }


}