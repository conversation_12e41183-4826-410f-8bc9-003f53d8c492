﻿using System;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.BBS
{
    public class Book_view_tovote : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string vid = "";

        public string lpage = "";

        public string vpage = "";

        public string INFO = "";

        public string ERROR = "";

        public wap_bbs_vote_Model bookVo = null;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID对应非论坛模块，请联系站长处理。", "");
            }
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            vid = GetRequestValue("vid");
            lpage = GetRequestValue("lpage");
            vpage = GetRequestValue("vpage");
            IsLogin(userid, GetUrlQueryString());
            try
            {
                wap_bbs_vote_BLL wap_bbs_vote_BLL = new wap_bbs_vote_BLL(a);
                string whoVote = wap_bbs_vote_BLL.GetWhoVote(long.Parse(id));
                if (whoVote.IndexOf(userid + ",") >= 0)
                {
                    INFO = "ERR";
                    return;
                }
                string whoVoteFromVid = wap_bbs_vote_BLL.GetWhoVoteFromVid(long.Parse(vid));
                wap_bbs_vote_BLL.Update(siteid, whoVoteFromVid + userid + ",", long.Parse(vid));
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}