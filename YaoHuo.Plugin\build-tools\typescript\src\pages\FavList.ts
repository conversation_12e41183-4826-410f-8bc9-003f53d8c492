/**
 * 收藏列表页面
 * 应用ToastService、ModalService、PaginationService和AjaxService
 * 实现完整的收藏管理功能，包括删除、清空和分页
 * 
 * @version 1.0
 * <AUTHOR>
 * @date 2025-01-07
 */

import { ToastService } from '../services/ToastService.js';
import { ModalService } from '../services/ModalService.js';
import { PaginationService } from '../services/PaginationService.js';
import { AjaxService } from '../services/AjaxService.js';

/**
 * FavList页面类
 * 负责收藏列表页面的所有交互功能
 */
export class FavListPage {
    private static instance: FavListPage;

    /**
     * 获取单例实例
     */
    public static getInstance(): FavListPage {
        if (!FavListPage.instance) {
            FavListPage.instance = new FavListPage();
        }
        return FavListPage.instance;
    }

    /**
     * 初始化页面
     */
    public static init(): void {
        FavListPage.getInstance().initialize();
    }

    /**
     * 初始化页面功能
     */
    public initialize(): void {
        console.log('FavList页面初始化开始');

        // 初始化Lucide图标
        this.initLucideIcons();

        // 初始化Toast自动关闭
        this.initToastAutoClose();

        // 初始化分页功能
        this.initPagination();

        // 绑定删除收藏事件
        this.bindDeleteFavEvents();

        // 绑定清空收藏事件
        this.bindClearFavEvents();

        console.log('FavList页面初始化完成');
    }

    /**
     * 初始化Lucide图标
     */
    private initLucideIcons(): void {
        if (typeof (window as any).lucide !== 'undefined') {
            (window as any).lucide.createIcons();
            console.log('FavList页面: Lucide图标初始化完成');
        }
    }

    /**
     * 初始化Toast自动关闭
     */
    private initToastAutoClose(): void {
        // 自动关闭错误Toast
        const errorToast = document.getElementById('errorToast');
        if (errorToast) {
            setTimeout(() => {
                ToastService.close('errorToast');
            }, 3000);
        }

        // 自动关闭信息Toast
        const infoToast = document.getElementById('infoToast');
        if (infoToast) {
            setTimeout(() => {
                ToastService.close('infoToast');
            }, 3000);
        }
    }

    /**
     * 初始化分页功能
     */
    private initPagination(): void {
        // 从页面元素中读取分页信息
        const paginationInfo = this.extractPaginationInfo();
        
        if (paginationInfo) {
            const config = {
                currentPage: paginationInfo.currentPage,
                totalPages: paginationInfo.totalPages,
                baseUrl: window.location.href,
                pageParam: 'page',
                showPrevNext: true
            };
            
            PaginationService.init(config);
            console.log(`FavList页面: 分页初始化完成 (${paginationInfo.currentPage}/${paginationInfo.totalPages})`);
        }
    }

    /**
     * 从页面元素中提取分页信息
     */
    private extractPaginationInfo(): { currentPage: number; totalPages: number } | null {
        // 从分页文本中提取 "第 X / Y 页"
        const paginationText = document.querySelector('.flex-1.text-center.text-sm.text-text-secondary.px-2');
        if (paginationText && paginationText.textContent) {
            const match = paginationText.textContent.match(/第\s*(\d+)\s*\/\s*(\d+)\s*页/);
            if (match) {
                return {
                    currentPage: parseInt(match[1]),
                    totalPages: parseInt(match[2])
                };
            }
        }

        // 从URL参数获取当前页
        const urlParams = new URLSearchParams(window.location.search);
        const currentPage = parseInt(urlParams.get('page') || '1');
        
        // 检查是否有分页按钮
        const prevBtn = document.getElementById('prevPageBtn') as HTMLButtonElement;
        const nextBtn = document.getElementById('nextPageBtn') as HTMLButtonElement;
        
        if (prevBtn || nextBtn) {
            let totalPages = currentPage;
            if (nextBtn && !nextBtn.disabled) {
                totalPages = currentPage + 1; // 至少还有下一页
            }
            return { currentPage, totalPages };
        }

        return null;
    }

    /**
     * 绑定删除收藏事件
     */
    private bindDeleteFavEvents(): void {
        document.querySelectorAll('.delete-fav-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                const deleteUrl = (button as HTMLElement).dataset.deleteUrl;
                const itemTitle = (button as HTMLElement).dataset.itemTitle;
                const itemCard = button.closest('.fav-item-card') as HTMLElement;

                if (deleteUrl && itemTitle) {
                    this.deleteFavorite(deleteUrl, itemTitle, button as HTMLElement, itemCard);
                }
            });
        });
    }

    /**
     * 绑定清空收藏事件
     */
    private bindClearFavEvents(): void {
        const clearFavoritesBtn = document.getElementById('clear-favorites-btn');
        if (clearFavoritesBtn) {
            clearFavoritesBtn.addEventListener('click', () => {
                this.clearAllFavorites(clearFavoritesBtn as HTMLElement);
            });
        }
    }

    /**
     * 删除单个收藏
     */
    private async deleteFavorite(deleteUrl: string, itemTitle: string, button: HTMLElement, itemCard: HTMLElement): Promise<void> {
        try {
            const confirmed = await ModalService.confirmDelete(`确定要删除收藏"${itemTitle}"吗？`);
            
            if (confirmed) {
                await AjaxService.delete(deleteUrl, {
                    showLoading: true,
                    loadingElement: button,
                    loadingText: '删除中...',
                    showToast: true,
                    successMessage: '删除成功',
                    errorMessage: '删除失败，请重试',
                    onSuccess: () => {
                        // 删除成功，移除卡片
                        this.removeItemCard(itemCard);
                        this.updateEmptyState();
                    }
                });
            }
        } catch (error) {
            console.error('删除收藏失败:', error);
        }
    }

    /**
     * 清空所有收藏
     */
    private async clearAllFavorites(button: HTMLElement): Promise<void> {
        try {
            const confirmed = await ModalService.confirmDelete('确定要清空所有收藏吗？此操作不可恢复！');
            
            if (confirmed) {
                // 构建清空收藏的URL
                const currentUrl = new URL(window.location.href);
                const clearUrl = `/bbs/favlist.aspx?action=deleteall&siteid=${currentUrl.searchParams.get('siteid') || ''}&favtypeid=${currentUrl.searchParams.get('favtypeid') || '0'}`;

                await AjaxService.delete(clearUrl, {
                    showLoading: true,
                    loadingElement: button,
                    loadingText: '清空中...',
                    showToast: true,
                    successMessage: '清空成功',
                    errorMessage: '清空失败，请重试',
                    onSuccess: () => {
                        // 清空成功，刷新页面
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    }
                });
            }
        } catch (error) {
            console.error('清空收藏失败:', error);
        }
    }

    /**
     * 移除收藏项卡片
     */
    private removeItemCard(itemCard: HTMLElement): void {
        itemCard.style.transition = 'all 0.3s ease';
        itemCard.style.opacity = '0';
        itemCard.style.transform = 'translateX(-100%)';
        
        setTimeout(() => {
            if (itemCard.parentNode) {
                itemCard.parentNode.removeChild(itemCard);
            }
        }, 300);
    }

    /**
     * 更新空状态显示
     */
    private updateEmptyState(): void {
        const favItems = document.querySelectorAll('.fav-item-card');
        const emptyState = document.querySelector('.empty-state');
        
        if (favItems.length === 0 && !emptyState) {
            // 创建空状态提示
            const emptyDiv = document.createElement('div');
            emptyDiv.className = 'empty-state text-center py-12';
            emptyDiv.innerHTML = `
                <i data-lucide="heart" class="w-16 h-16 mx-auto text-gray-300 mb-4"></i>
                <p class="text-gray-500">暂无收藏内容</p>
            `;
            
            const container = document.querySelector('.card-body');
            if (container) {
                container.appendChild(emptyDiv);
                
                // 初始化图标
                if (typeof (window as any).lucide !== 'undefined') {
                    (window as any).lucide.createIcons();
                }
            }
        }
    }

    /**
     * 获取页面统计信息
     */
    public getPageStats(): { totalItems: number; currentPage: number; totalPages: number } {
        const favItems = document.querySelectorAll('.fav-item-card');
        const paginationInfo = this.extractPaginationInfo();
        
        return {
            totalItems: favItems.length,
            currentPage: paginationInfo?.currentPage || 1,
            totalPages: paginationInfo?.totalPages || 1
        };
    }
}

// ==================== 页面初始化 ====================

/**
 * 页面DOM加载完成后自动初始化
 */
document.addEventListener('DOMContentLoaded', () => {
    FavListPage.init();
});

// ==================== 全局函数，供模板调用（向后兼容） ====================

/**
 * 关闭Toast（向后兼容）
 */
export function closeToast(toastId: string): void {
    ToastService.close(toastId);
}

/**
 * 自动关闭Toast（向后兼容）
 */
export function autoCloseToast(toastId: string, delay: number = 3000): void {
    setTimeout(() => {
        ToastService.close(toastId);
    }, delay);
}

/**
 * 显示Toast（向后兼容）
 */
export function showToast(type: 'success' | 'error' | 'warning' | 'info', message: string): void {
    switch (type) {
        case 'success':
            ToastService.showSuccess(message);
            break;
        case 'error':
            ToastService.showError(message);
            break;
        case 'warning':
            ToastService.showWarning(message);
            break;
        case 'info':
            ToastService.showInfo(message);
            break;
    }
}

/**
 * 显示确认对话框（向后兼容）
 */
export function showCustomConfirm(message: string, onConfirm: () => void): void {
    ModalService.confirm(message, onConfirm);
}

/**
 * 获取页面实例
 */
export function getFavListPage(): FavListPage {
    return FavListPage.getInstance();
}

// 导出默认类
export default FavListPage;
