<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Revoke.aspx.cs" Inherits="YaoHuo.Plugin.OAuth.Revoke" %>

<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OAuth 令牌撤销</title>

    <!-- 本地 Tailwind CSS -->
    <link href="/Template/CSS/output.css" rel="stylesheet">

    <!-- 本地 Lucide Icons -->
    <script src="/NetCSS/JS/BBS/Lucide.0.511.0.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- 固定顶部导航栏 -->
        <header class="header">
            <div class="header-content">
                <div class="header-icon" onclick="history.back()">
                    <i data-lucide="arrow-left" class="w-6 h-6"></i>
                </div>
                <div class="header-title">令牌撤销</div>
                <div class="header-actions-right">
                    <!-- 可以添加帮助按钮等 -->
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 撤销说明 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h2 class="card-title">
                        <i data-lucide="shield-x" class="card-icon"></i>
                        OAuth 令牌撤销
                    </h2>
                </div>
                <div class="card-body">
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                        <div class="flex items-start space-x-3">
                            <i data-lucide="alert-triangle" class="w-5 h-5 text-yellow-600 mt-0.5"></i>
                            <div>
                                <h4 class="text-sm font-medium text-yellow-900 mb-2">关于令牌撤销</h4>
                                <ul class="text-sm text-yellow-800 space-y-1">
                                    <li class="flex items-center">
                                        <i data-lucide="check" class="w-3 h-3 mr-2"></i>
                                        撤销后，相关的访问令牌将立即失效
                                    </li>
                                    <li class="flex items-center">
                                        <i data-lucide="check" class="w-3 h-3 mr-2"></i>
                                        应用需要重新获得用户授权才能继续访问
                                    </li>
                                    <li class="flex items-center">
                                        <i data-lucide="check" class="w-3 h-3 mr-2"></i>
                                        此操作符合 OAuth 2.0 标准的令牌撤销规范
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="grid-2">
                        <div class="text-center">
                            <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                                <i data-lucide="key-off" class="w-6 h-6 text-red-600"></i>
                            </div>
                            <h3 class="font-medium text-text-primary mb-2">访问令牌</h3>
                            <p class="text-sm text-text-secondary">撤销特定的访问令牌</p>
                        </div>
                        <div class="text-center">
                            <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center mx-auto mb-3">
                                <i data-lucide="refresh-ccw" class="w-6 h-6 text-orange-600"></i>
                            </div>
                            <h3 class="font-medium text-text-primary mb-2">刷新令牌</h3>
                            <p class="text-sm text-text-secondary">撤销相关的刷新令牌</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 令牌撤销表单 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h2 class="card-title">
                        <i data-lucide="trash-2" class="card-icon"></i>
                        撤销令牌
                    </h2>
                </div>
                <div class="card-body">
                    <form method="post" action="Revoke.aspx" id="revokeForm">
                        <div class="form-group">
                            <label class="form-label required">令牌 (Token)</label>
                            <textarea name="token" class="form-input" rows="3"
                                      placeholder="请输入要撤销的访问令牌或刷新令牌..." required></textarea>
                            <div class="form-hint">
                                <i data-lucide="info" class="w-4 h-4 inline mr-1"></i>
                                支持撤销访问令牌 (access_token) 和刷新令牌 (refresh_token)
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">令牌类型提示</label>
                            <select name="token_type_hint" class="form-select">
                                <option value="">自动检测</option>
                                <option value="access_token">访问令牌</option>
                                <option value="refresh_token">刷新令牌</option>
                            </select>
                            <div class="form-hint">
                                <i data-lucide="lightbulb" class="w-4 h-4 inline mr-1"></i>
                                提供令牌类型提示可以提高撤销效率
                            </div>
                        </div>

                        <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                            <div class="flex items-start space-x-3">
                                <i data-lucide="alert-circle" class="w-5 h-5 text-red-600 mt-0.5"></i>
                                <div>
                                    <h4 class="text-sm font-medium text-red-900 mb-1">重要提醒</h4>
                                    <p class="text-sm text-red-800">
                                        令牌撤销后将立即生效且不可恢复。请确认您要撤销的令牌信息正确无误。
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="grid-2">
                            <button type="button" class="btn btn-outline" onclick="clearForm()">
                                <i data-lucide="x" class="w-4 h-4 mr-1"></i>
                                清空
                            </button>
                            <button type="submit" class="btn btn-destructive">
                                <i data-lucide="shield-x" class="w-4 h-4 mr-1"></i>
                                撤销令牌
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 撤销结果显示 -->
            <%
            // 这里可以显示后端处理结果，如果有的话
            // Response.Write(strhtml); 的内容会在这里处理
            %>

            <!-- 快速操作 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i data-lucide="zap" class="card-icon"></i>
                        相关操作
                    </h2>
                </div>
                <div class="card-body">
                    <div class="grid-2">
                        <button type="button" class="btn btn-outline" onclick="window.open('/OAuth/Index.aspx', '_blank')">
                            <i data-lucide="list" class="w-4 h-4 mr-1"></i>
                            授权管理
                        </button>
                        <button type="button" class="btn btn-outline" onclick="window.open('/OAuth/DebugToken.aspx', '_blank')">
                            <i data-lucide="bug" class="w-4 h-4 mr-1"></i>
                            令牌调试
                        </button>
                    </div>

                    <div class="mt-4 pt-4 border-t">
                        <div class="text-center">
                            <button type="button" class="btn btn-ghost text-xs" onclick="window.open('/OAuth/Admin.aspx', '_blank')">
                                <i data-lucide="settings" class="w-3 h-3 mr-1"></i>
                                应用管理
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });

        // 清空表单
        function clearForm() {
            document.querySelector('textarea[name="token"]').value = '';
            document.querySelector('select[name="token_type_hint"]').selectedIndex = 0;
        }

        // 表单提交前确认
        document.getElementById('revokeForm').addEventListener('submit', function(e) {
            const token = document.querySelector('textarea[name="token"]').value.trim();

            if (!token) {
                e.preventDefault();
                showToast('请输入要撤销的令牌', 'error');
                return;
            }

            if (!confirm('确定要撤销此令牌吗？此操作不可恢复！')) {
                e.preventDefault();
                return;
            }

            // 添加加载状态
            const submitBtn = document.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-1 animate-spin"></i>撤销中...';

            // 重新初始化图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });

        // 显示 Toast 提示
        function showToast(message, type = 'info') {
            let container = document.getElementById('toast-container');
            if (!container) {
                container = document.createElement('div');
                container.id = 'toast-container';
                container.className = 'toast-container';
                document.body.appendChild(container);
            }

            const toast = document.createElement('div');
            toast.className = `toast-dynamic toast-dynamic-${type}`;

            let icon = 'info';
            if (type === 'success') icon = 'check-circle';
            else if (type === 'error') icon = 'x-circle';
            else if (type === 'warning') icon = 'alert-triangle';

            toast.innerHTML = `
                <i data-lucide="${icon}" class="w-4 h-4"></i>
                <span>${message}</span>
            `;

            container.appendChild(toast);

            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }

            setTimeout(() => {
                toast.classList.add('show');
            }, 100);

            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (container.contains(toast)) {
                        container.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
