﻿using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.Template.Models;
using YaoHuo.Plugin.BBS.Models;
using Newtonsoft.Json;

namespace YaoHuo.Plugin.BBS
{
    public class MessageList_view : MyPageWap
    {

        private string a = PubConstant.GetAppString("InstanceName");

        // 消息加载配置常量
        // 新版UI配置
        private const int NEW_UI_INITIAL_CONTEXT_BEFORE_COUNT = 2;   // 新版UI锚点前加载的消息数
        private const int NEW_UI_INITIAL_CONTEXT_AFTER_COUNT = 2;    // 新版UI锚点后加载的消息数
        private const int NEW_UI_DYNAMIC_LOAD_MESSAGE_COUNT = 50;     // 新版UI动态加载时的消息数量
        private const int NEW_UI_MINIMUM_INITIAL_MESSAGE_COUNT = 5;  // 新版UI初始加载的最少消息数量

        // 旧版UI配置
        private const int OLD_UI_MESSAGE_COUNT = 50;  // 旧版UI显示的消息数量

        public string action = "";

        public string linkURL = "";

        public string condition = "";

        public string ERROR = "";

        public string key = "";

        public string types = "";

        public string id = "";

        public string backurl = "";

        public string INFO = "";

        public string page = "";

        public string needpwFlag = "";

        public string needpw = "";

        public string issystem = "";

        public string isclose = "0";

        public string touserNickname = "";

        public wap_message_Model bookVo = new wap_message_Model();

        public List<wap_message_Model> listVo = null;

        public string senderNickname = "";

        // 标记当前是否正在使用新版UI渲染
        private bool isUsingNewUIRendering = false;

        /// <summary>
        /// 获取当前用户ID（安全解析）
        /// </summary>
        private long GetCurrentUserId()
        {
            return DapperHelper.SafeParseLong(userid, "用户ID");
        }

        /// <summary>
        /// 根据消息记录确定对话伙伴的用户ID
        /// </summary>
        /// <returns>对话伙伴的用户ID</returns>
        private long GetConversationPartnerId()
        {
            return (bookVo.userid.ToString() == userid) ? bookVo.touserid : bookVo.userid;
        }

        /// <summary>
        /// 获取对话双方的用户ID
        /// </summary>
        /// <returns>(当前用户ID, 对话伙伴ID)</returns>
        private (long currentUserId, long partnerId) GetConversationUserIds()
        {
            long currentUserId = GetCurrentUserId();
            long partnerId = GetConversationPartnerId();
            return (currentUserId, partnerId);
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            // 检查是否为动态加载AJAX请求
            if (GetRequestValue("action") == "loadMore")
            {
                HandleDynamicLoadRequest();
                Response.End();
                return;
            }

            // ✅ 统一加载对话数据
            LoadConversationData();

            // ✅ 检查UI偏好并处理新版渲染（复用已加载的数据）
            if (CheckAndHandleUIPreference())
            {
                return; // 新版渲染成功，阻止旧版代码执行
            }
        }

        /// <summary>
        /// 加载对话数据的统一方法 - 消除重复代码
        /// </summary>
        private void LoadConversationData()
        {
            action = GetRequestValue("action");
            issystem = GetRequestValue("issystem");
            isclose = GetRequestValue("isclose");
            backurl = base.Request.QueryString.Get("backurl");
            id = base.Request.QueryString.Get("id");
            page = base.Request.QueryString.Get("page");
            types = base.Request.QueryString.Get("types");
            backurl = base.Request.QueryString.Get("backurl");
            needpw = GetRequestValue("needpw");
            
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            
            if (!WapTool.IsNumeric(id))
            {
                id = "0";
            }
            
            IsLogin(userid, backurl);
            
            if (WapTool.GetArryString(siteVo.Version, '|', 53) == "1")
            {
                needPassWordToAdmin();
            }
            
            if (isclose == "")
            {
                if (base.Request.Cookies["KL_MESSAGE_TIMES"] != null)
                {
                    isclose = base.Request.Cookies["KL_MESSAGE_TIMES"].Value;
                }
            }
            else
            {
                base.Response.Cookies["KL_MESSAGE_TIMES"].Expires = DateTime.Now.AddYears(1);
                base.Response.Cookies["KL_MESSAGE_TIMES"].Value = isclose;
            }
            
            wap_message_BLL wap_message_BLL = new wap_message_BLL(a);
            bookVo = wap_message_BLL.GetModel(long.Parse(id));

            // 修复锚点消息的字段方向异常
            var anchorMessages = new List<wap_message_Model> { bookVo };
            FixSingleCopyMessageOrientation(anchorMessages, GetCurrentUserId());



            if (bookVo.userid.ToString() != userid && bookVo.touserid.ToString() != userid)
            {
                ShowTipInfo(GetLang("你没有权限！|你沒有權限|You do not have permission"), "");
            }
            
            needpwFlag = WapTool.GetArryString(siteVo.Version, '|', 31);
            
            if (bookVo.isnew == 1L)
            {
                // ✅ 使用DapperHelper进行安全的参数化更新操作
                string connectionString = PubConstant.GetConnectionString(a);
                string updateSql = "UPDATE wap_message SET isnew = 0 WHERE id = @MessageId";
                DapperHelper.Execute(connectionString, updateSql, new {
                    MessageId = DapperHelper.SafeParseLong(id, "消息ID")
                });
            }

            if (isclose != "1")
            {
                // 获取对话双方的用户ID
                var (currentUserId, partnerId) = GetConversationUserIds();

                // 根据是否正在使用新版UI渲染来判断加载策略
                if (isUsingNewUIRendering)
                {
                    // 新版UI：使用原有的上下文加载逻辑（5条）
                    var (messages, _) = LoadContextualMessagesWithMapping(bookVo.id, currentUserId, partnerId, "desc");
                    listVo = messages;
                }
                else
                {
                    // 旧版UI：加载最新50条对话记录
                    listVo = LoadLatestMessagesForOldUI(currentUserId, partnerId);
                }

                // 修复消息列表的字段方向异常
                FixSingleCopyMessageOrientation(listVo, currentUserId);
            }

            // 获取用户昵称
            user_BLL userBll = new user_BLL(a);
            user_Model senderUser = userBll.getUserInfo(bookVo.userid.ToString(), siteid);
            if (senderUser != null && !string.IsNullOrEmpty(senderUser.nickname))
            {
                this.senderNickname = senderUser.nickname;
            }
            else
            {
                this.senderNickname = bookVo.userid.ToString();
            }

            touserNickname = "";
            user_Model touser = userBll.getUserInfo(bookVo.touserid.ToString(), siteid);
            if (touser != null && !string.IsNullOrEmpty(touser.nickname))
            {
                touserNickname = touser.nickname;
            }
            else
            {
                touserNickname = bookVo.touserid.ToString();
            }
        }

        /// <summary>
        /// 检查UI偏好并处理新版渲染
        /// </summary>
        /// <returns>是否成功渲染新版</returns>
        private bool CheckAndHandleUIPreference()
        {
            string uiPreference = Request.Cookies["ui_preference"]?.Value ?? "old";

            if (uiPreference == "new")
            {
                try
                {
                    RenderWithHandlebars();
                    return true;
                }
                catch (System.Threading.ThreadAbortException)
                {
                    return true; // 成功渲染
                }
                catch (Exception ex)
                {
                    ERROR = "新版模板加载失败: " + ex.Message;
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// 使用Handlebars渲染新版界面 - 使用锚点上下文加载
        /// </summary>
        private void RenderWithHandlebars()
        {
            try
            {
                // 标记正在使用新版UI渲染
                isUsingNewUIRendering = true;

                // 获取锚点消息ID
                long anchorMessageId = long.Parse(id);

                // 使用新的锚点上下文数据构建页面模型
                var pageModel = BuildPageModelForNewUI(anchorMessageId);

                // 直接调用，避免反射
                string finalHtml = TemplateService.RenderPageWithLayout(
                    "~/Template/Pages/MessageDetail.hbs",
                    pageModel,
                    pageModel.PageTitle, // 使用动态标题
                    new HeaderOptionsModel {
                        ShowViewModeToggle = false,
                        ShowMessageNotification = false, // 消息详情页面不显示铃铛图标
                        CustomButtons = new List<HeaderButtonModel>
                        {
                            new HeaderButtonModel
                            {
                                Id = "delete-conversation-btn",
                                Icon = "trash-2",
                                Tooltip = "删除对话",
                                OnClick = "showDeleteConversationConfirm()"
                            }
                        }
                    }
                );

                // 输出渲染结果
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write(finalHtml);
                Response.End();
            }
            catch (System.Threading.ThreadAbortException)
            {
                throw; // Response.End()的正常行为
            }
            catch (Exception ex)
            {
                // 错误处理
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}</div>");
                HttpContext.Current.ApplicationInstance.CompleteRequest();
            }
        }



        /// <summary>
        /// 构建页面数据模型
        /// </summary>
        /// <returns>MessageDetailPageModel实例</returns>
        private MessageDetailPageModel BuildPageModel()
        {
            var pageModel = new MessageDetailPageModel();

            // 修正：根据 userid 判断当前用户是否是主消息的发送方
            bool isCurrentUserSender = bookVo.userid.ToString() == userid;
            string conversationPartnerNickname = isCurrentUserSender ? touserNickname : senderNickname;
            long partnerId = isCurrentUserSender ? bookVo.touserid : bookVo.userid;
            
            // 动态设置页面标题
            pageModel.PageTitle = $"与 {conversationPartnerNickname} 的对话";

            // ✅ 获取对话伙伴头像信息
            var partnerAvatar = AvatarHelper.GetUserAvatar(partnerId.ToString(), siteid, a, http_start);

            // 构建对话对象信息
            pageModel.ConversationPartner = new UserInfoModel
            {
                Nickname = conversationPartnerNickname,
                UserId = partnerId.ToString(),
                AvatarUrl = partnerAvatar.AvatarUrl,
                IsDefaultAvatar = partnerAvatar.IsDefaultAvatar,
                FirstChar = partnerAvatar.FirstChar,
                UserSpaceUrl = $"{http_start}bbs/userinfo.aspx?siteid={siteid}&touserid={partnerId}"
            };

            // 构建当前消息信息
            pageModel.MessageInfo = new MessageInfoModel
            {
                Id = bookVo.id.ToString(),
                Title = bookVo.title ?? "",
                Content = bookVo.content ?? "",
                ProcessedContent = WapTool.ToWML(bookVo.content ?? "", wmlVo),
                AddTime = bookVo.addtime,
                SenderId = bookVo.userid.ToString(),
                SenderNickname = senderNickname,
                Status = (int)bookVo.isnew
            };

            // 构建聊天记录
            if (listVo != null && listVo.Count > 0)
            {
                // 新版UI：翻转列表顺序，使最新消息显示在底部（符合现代聊天应用习惯）
                // 旧版UI：保持原顺序不变，最新消息显示在顶部
                if (isUsingNewUIRendering)
                {
                    listVo.Reverse(); // 从"新→旧"翻转为"旧→新"
                }
                
                var chatMessages = new List<ChatMessageModel>();
                foreach (var msg in listVo)
                {
                    var isFromCurrentUser = msg.userid.ToString() == userid;

                    // ✅ 获取发送者头像信息
                    var senderAvatar = AvatarHelper.GetUserAvatar(msg.userid.ToString(), siteid, a, http_start);

                    var chatMsg = new ChatMessageModel
                    {
                        Id = msg.id.ToString(),
                        Content = msg.content ?? "",
                        ProcessedContent = WapTool.ToWML(msg.content ?? "", wmlVo),
                        AddTime = msg.addtime,
                        SenderId = msg.userid,
                        SenderNickname = isFromCurrentUser ? "我" : conversationPartnerNickname,
                        SenderAvatarUrl = senderAvatar.AvatarUrl,
                        SenderIsDefaultAvatar = senderAvatar.IsDefaultAvatar,
                        SenderFirstChar = senderAvatar.FirstChar,
                        IsFromCurrentUser = isFromCurrentUser,
                        Status = (int)msg.isnew,
                        TimeDisplay = msg.addtime.ToString("HH:mm")
                    };
                    chatMessages.Add(chatMsg);
                }

                // 旧版UI：简单处理，不需要复杂的连续性逻辑
                ProcessLegacyUIMessages(chatMessages);
                pageModel.ChatHistory = chatMessages;
            }

            // ✅ 获取发送方和接收方头像信息
            var messageSenderAvatar = AvatarHelper.GetUserAvatar(bookVo.userid.ToString(), siteid, a, http_start);
            var messageReceiverAvatar = AvatarHelper.GetUserAvatar(bookVo.touserid.ToString(), siteid, a, http_start);

            // 构建用户信息
            pageModel.UserInfo = new ConversationUsersModel
            {
                CurrentUserId = userid,
                Sender = new UserInfoModel
                {
                    UserId = bookVo.userid.ToString(),
                    Nickname = senderNickname,
                    AvatarUrl = messageSenderAvatar.AvatarUrl,
                    IsDefaultAvatar = messageSenderAvatar.IsDefaultAvatar,
                    FirstChar = messageSenderAvatar.FirstChar,
                    UserSpaceUrl = $"{http_start}bbs/userinfo.aspx?siteid={siteid}&touserid={bookVo.userid}"
                },
                Receiver = new UserInfoModel
                {
                    UserId = bookVo.touserid.ToString(),
                    Nickname = touserNickname,
                    AvatarUrl = messageReceiverAvatar.AvatarUrl,
                    IsDefaultAvatar = messageReceiverAvatar.IsDefaultAvatar,
                    FirstChar = messageReceiverAvatar.FirstChar,
                    UserSpaceUrl = $"{http_start}bbs/userinfo.aspx?siteid={siteid}&touserid={bookVo.touserid}"
                }
            };

            // 构建回复表单
            pageModel.ReplyForm = new ReplyFormModel
            {
                NeedPassword = needpwFlag == "1",
                Password = needpw ?? "",
                ActionUrl = $"{http_start}bbs/messagelist_add.aspx",
                TargetUserIds = partnerId.ToString(),
                TargetMessageId = id,
                HiddenFields = new Dictionary<string, string>
                {
                    ["classid"] = classid,
                    ["siteid"] = siteid,
                    ["types"] = types,
                    ["issystem"] = issystem,
                    ["backurl"] = backurl
                }
            };

            // 构建对话设置
            pageModel.ConversationSettings = new ConversationSettingsModel
            {
                IsConversationClosed = isclose == "1",
                ShowChatHistory = isclose != "1",
                HistoryLimit = 50,
                CanReply = bookVo.isnew != 2L // 2表示重发状态，不能回复
            };

            // 构建公共模型数据
            BuildCommonModelData(pageModel);

            return pageModel;
        }

        /// <summary>
        /// 旧版UI：简单的消息处理（不需要复杂的连续性逻辑）
        /// </summary>
        /// <param name="messages">消息列表</param>
        private void ProcessLegacyUIMessages(List<ChatMessageModel> messages)
        {
            // 旧版UI直接在模板中显示所有信息，不需要复杂处理
            // 只需要设置基本的显示属性
            for (int i = 0; i < messages.Count; i++)
            {
                var current = messages[i];
                current.ShowAvatar = true;  // 旧版UI总是显示用户信息
                current.ShowTime = true;    // 旧版UI总是显示时间
                current.IsCompact = false;  // 旧版UI不使用紧密布局
                current.IsNewDateGroup = false; // 旧版UI不使用日期分组
            }
        }

        /// <summary>
        /// 新版UI：处理消息连续性和日期分组逻辑
        /// </summary>
        /// <param name="messages">消息列表</param>
        private void ProcessNewUIMessages(List<ChatMessageModel> messages)
        {
            for (int i = 0; i < messages.Count; i++)
            {
                var current = messages[i];
                var previous = i > 0 ? messages[i - 1] : null;
                var next = i < messages.Count - 1 ? messages[i + 1] : null;

                // ✅ 设置是否为第一条消息（用于SpacingClass计算属性）
                current.IsFirst = i == 0;

                // 判断是否显示头像（发送者变更或超过5分钟间隔）
                current.ShowAvatar = previous == null || 
                                   current.SenderId != previous.SenderId ||
                                   (current.AddTime - previous.AddTime).TotalMinutes > 5;

                // 判断是否为紧密消息（与前一条消息间距较小）
                // 条件：不是第一条 && 同一发送者 && 5分钟内 && 前一条不显示头像
                current.IsCompact = previous != null && 
                                   current.SenderId == previous.SenderId &&
                                   (current.AddTime - previous.AddTime).TotalMinutes <= 5 &&
                                   !current.ShowAvatar;

                // 修正时间显示逻辑：时间应显示在连续消息块的最后一个消息下方
                current.ShowTime = next == null || 
                                   next.SenderId != current.SenderId || 
                                   (next.AddTime - current.AddTime).TotalMinutes > 5;

                // 处理日期分组
                var currentDate = current.AddTime.Date;
                var previousDate = previous?.AddTime.Date;
                current.IsNewDateGroup = previousDate == null || currentDate != previousDate;

                // 设置日期显示格式（当年不显示年份）
                if (current.IsNewDateGroup)
                {
                    current.DateGroup = current.AddTime.Year == DateTime.Now.Year 
                        ? current.AddTime.ToString("M月d日")
                        : current.AddTime.ToString("yyyy年M月d日");
                }
            }
        }

        /// <summary>
        /// 构建公共模型数据
        /// </summary>
        /// <param name="pageModel">页面模型</param>
        private void BuildCommonModelData(MessageDetailPageModel pageModel)
        {
            // 构建消息提示
            if (!string.IsNullOrEmpty(ERROR))
            {
                pageModel.Message.HasMessage = true;
                pageModel.Message.Type = "error";
                pageModel.Message.Content = ERROR;
                pageModel.Message.IsSuccess = false;
            }
            else if (!string.IsNullOrEmpty(INFO))
            {
                pageModel.Message.HasMessage = true;
                pageModel.Message.Type = "info";
                pageModel.Message.Content = INFO;
                pageModel.Message.IsSuccess = true;
            }

            // 构建站点信息
            pageModel.SiteInfo.SiteId = siteid;
            pageModel.SiteInfo.ClassId = classid;
            pageModel.SiteInfo.HttpStart = http_start;
            pageModel.SiteInfo.BackUrl = $"{http_start}bbs/messagelist.aspx?siteid={siteid}&classid={classid}&types={types}&issystem={issystem}&page={page}";
            pageModel.SiteInfo.HomeUrl = $"{http_start}wapindex.aspx?siteid={siteid}&classid=0";

            // 构建隐藏字段
            pageModel.HiddenFields.SiteId = siteid;
            pageModel.HiddenFields.ClassId = classid;
            pageModel.HiddenFields.BackUrl = backurl;
        }

        /// <summary>
        /// 为新版UI构建页面数据模型（使用锚点上下文数据）
        /// 注意：此方法与现有的 BuildPageModel 方法并行存在，专门服务于新版UI
        /// </summary>
        private MessageDetailPageModel BuildPageModelForNewUI(long anchorMessageId)
        {
            // 获取对话双方的用户ID
            var (currentUserId, partnerId) = GetConversationUserIds();

            // 1. 智能加载消息：确保至少有5条消息
            var (rawMessages, contextMessages, mappedAnchorId, requestedBeforeCount, requestedAfterCount) = LoadMessagesWithMinimumCount(anchorMessageId, currentUserId, partnerId, NEW_UI_MINIMUM_INITIAL_MESSAGE_COUNT);

            // 验证映射后的锚点ID是否在消息列表中
            var anchorMessage = contextMessages?.FirstOrDefault(m => m.id == mappedAnchorId);
            if (anchorMessage == null)
            {
                // 如果映射后的锚点不在列表中，尝试使用原始锚点ID
                anchorMessage = contextMessages?.FirstOrDefault(m => m.id == anchorMessageId);
                if (anchorMessage != null)
                {
                    mappedAnchorId = anchorMessageId;
                }
            }

            // 构建页面模型
            var pageModel = new MessageDetailPageModel();

            // 设置锚点消息ID（使用映射后的ID）
            pageModel.AnchorMessageId = mappedAnchorId;

            // 设置页面基础信息 - 确定对话伙伴昵称
            string conversationPartnerNickname = (bookVo.userid.ToString() == userid) ? touserNickname : senderNickname;
            pageModel.PageTitle = $"与 {conversationPartnerNickname} 的对话";
            pageModel.SiteInfo.SiteName = siteVo.sitename;
            pageModel.SiteInfo.SiteId = siteid;
            pageModel.SiteInfo.ClassId = "0"; // 消息页面使用默认classid
            pageModel.SiteInfo.HttpStart = http_start;

            // ✅ 获取对话伙伴头像信息
            var partnerAvatar = AvatarHelper.GetUserAvatar(partnerId.ToString(), siteid, a, http_start);

            // 构建对话对象信息
            pageModel.ConversationPartner = new UserInfoModel
            {
                Nickname = conversationPartnerNickname,
                UserId = partnerId.ToString(),
                AvatarUrl = partnerAvatar.AvatarUrl,
                IsDefaultAvatar = partnerAvatar.IsDefaultAvatar,
                FirstChar = partnerAvatar.FirstChar,
                UserSpaceUrl = $"{http_start}bbs/userinfo.aspx?siteid={siteid}&touserid={partnerId}"
            };

            // 设置用户信息（包含当前用户ID）
            pageModel.UserInfo.CurrentUserId = userid;

            // 构建当前消息信息
            pageModel.MessageInfo = new MessageInfoModel
            {
                Id = bookVo.id.ToString(),
                Title = bookVo.title ?? "",
                Content = bookVo.content ?? "",
                ProcessedContent = WapTool.ToWML(bookVo.content ?? "", wmlVo),
                AddTime = bookVo.addtime,
                SenderId = bookVo.userid.ToString(),
                SenderNickname = senderNickname,
                Status = (int)bookVo.isnew
            };

            // 使用已加载的、为新版UI排序和处理过的消息列表
            if (contextMessages != null && contextMessages.Count > 0)
            {
                // 修复消息字段方向异常
                FixSingleCopyMessageOrientation(contextMessages, currentUserId);

                // 新版UI：SQL查询已经按时间升序排列（旧→新），无需翻转
                // LoadContextualMessages方法中的ORDER BY addtime, id已确保正确顺序

                var chatMessages = new List<ChatMessageModel>();
                for (int i = 0; i < contextMessages.Count; i++)
                {
                    var msg = contextMessages[i];
                    var isFromCurrentUser = msg.userid.ToString() == userid;

                    // ✅ 获取发送者头像信息
                    var senderAvatar = AvatarHelper.GetUserAvatar(msg.userid.ToString(), siteid, a, http_start);

                    var chatMsg = new ChatMessageModel
                    {
                        Id = msg.id.ToString(),
                        Content = msg.content ?? "",
                        ProcessedContent = WapTool.ToWML(msg.content ?? "", wmlVo),
                        AddTime = msg.addtime,
                        SenderId = msg.userid,
                        SenderNickname = isFromCurrentUser ? "我" : conversationPartnerNickname,
                        SenderAvatarUrl = senderAvatar.AvatarUrl,
                        SenderIsDefaultAvatar = senderAvatar.IsDefaultAvatar,
                        SenderFirstChar = senderAvatar.FirstChar,
                        IsFromCurrentUser = isFromCurrentUser,
                        IsAnchor = msg.id == mappedAnchorId, // 标识锚点消息（使用映射后的ID）
                        Status = (int)msg.isnew,
                        TimeDisplay = msg.addtime.ToString("HH:mm"),
                        IsFirst = i == 0,
                        IsCompact = i > 0 && Math.Abs((msg.addtime - contextMessages[i-1].addtime).TotalMinutes) < 5,
                        ShowTime = i == 0 || i == contextMessages.Count - 1 || Math.Abs((msg.addtime - contextMessages[i-1].addtime).TotalMinutes) >= 5,
                        ShowAvatar = !isFromCurrentUser && (i == 0 || contextMessages[i-1].userid != msg.userid)
                    };
                    chatMessages.Add(chatMsg);
                }

                // 新版UI：处理消息连续性和日期分组
                ProcessNewUIMessages(chatMessages);

                pageModel.ChatHistory = chatMessages;
            }

            // 设置对话相关设置
            pageModel.ConversationSettings.ShowChatHistory = true;

            // 3. 基于动态加载结果进行智能边界判断
            bool hasOlder, hasNewer;
            if (rawMessages == null || rawMessages.Count == 0)
            {
                hasOlder = false;
                hasNewer = false;
            }
            else
            {
                // 找到原始锚点在 RAW 列表中的位置
                var rawAnchorIndex = rawMessages.FindIndex(m => m.id == anchorMessageId);

                if (rawAnchorIndex == -1)
                {
                    // 极端情况：锚点消息未找到（可能已被删除），安全起见假定没有更多
                    hasOlder = false;
                    hasNewer = false;
                }
                else
                {
                    int actualRawBeforeCount = rawAnchorIndex;
                    int actualRawAfterCount = rawMessages.Count - 1 - rawAnchorIndex;

                    // 智能边界判断：基于实际请求的数量进行判断
                    // 如果实际获得的前面消息数等于我们请求的数量，说明可能还有更早的
                    hasOlder = actualRawBeforeCount == requestedBeforeCount;

                    // 如果实际获得的后面消息数等于我们请求的数量，说明可能还有更新的
                    hasNewer = actualRawAfterCount == requestedAfterCount;
                }
            }
            
            pageModel.ConversationSettings.HasMoreMessages = hasOlder;
            pageModel.ConversationSettings.HasMoreNewerMessages = hasNewer;

            return pageModel;
        }

        /// <summary>
        /// 动态加载消息：基于锚点消息加载指定数量的前后消息（原始数据，未去重）
        /// 支持向上/向下滚动加载更多消息的核心方法
        /// </summary>
        private List<wap_message_Model> LoadMessagesAroundAnchorRaw(long anchorMessageId, long myUserId, long partnerUserId, int beforeCount, int afterCount)
        {
            const string query = @"
;WITH RankedMessages AS (
    SELECT
        *,
        ROW_NUMBER() OVER (ORDER BY addtime, id) as RowNum
    FROM
        wap_message
    WHERE
        siteid = @p_siteid
        AND isnew <= 2
        AND issystem <> 2
        AND ((userid = @p_myUserId AND touserid = @p_partnerUserId)
             OR (userid = @p_partnerUserId AND touserid = @p_myUserId))
),
AnchorInfo AS (
    SELECT RowNum
    FROM RankedMessages
    WHERE id = @p_anchorMessageId
)
SELECT
    rm.*
FROM
    RankedMessages rm, AnchorInfo ai
WHERE
    rm.RowNum BETWEEN (ai.RowNum - @p_beforeCount) AND (ai.RowNum + @p_afterCount)
ORDER BY
    rm.RowNum ASC;
";

            var parameters = new
            {
                p_anchorMessageId = anchorMessageId,
                p_myUserId = myUserId,
                p_partnerUserId = partnerUserId,
                p_siteid = DapperHelper.SafeParseLong(siteid, "站点ID"),
                p_beforeCount = beforeCount,
                p_afterCount = afterCount
            };

            // 返回原始消息，不进行去重
            return DapperHelper.Query<wap_message_Model>(PubConstant.GetConnectionString(a), query, parameters).ToList();
        }

        /// <summary>
        /// AJAX API：动态加载更多消息
        /// 支持向上滚动（加载更早消息）和向下滚动（加载更新消息）
        /// </summary>
        private void HandleDynamicLoadRequest()
        {
            try
            {
                // 获取请求参数
                string direction = GetRequestValue("direction"); // "up" 或 "down"
                string lastMessageId = GetRequestValue("lastMessageId");
                string partnerIdStr = GetRequestValue("partnerId");

                if (string.IsNullOrEmpty(direction) || string.IsNullOrEmpty(lastMessageId) || string.IsNullOrEmpty(partnerIdStr))
                {
                    Response.Write("{\"success\":false,\"error\":\"参数不完整\"}");
                    return;
                }

                long lastMsgId = DapperHelper.SafeParseLong(lastMessageId, "消息ID");
                long partnerId = DapperHelper.SafeParseLong(partnerIdStr, "对话伙伴ID");
                long currentUserId = GetCurrentUserId();

                // 根据方向加载消息
                List<wap_message_Model> messages;
                int rawCount = 0; // 用于判断是否还有更多

                if (direction == "up")
                {
                    // 向上滚动：加载更早的消息（lastMessageId之前的N条）
                    var rawMsgs = LoadMessagesAroundAnchorRaw(lastMsgId, currentUserId, partnerId, NEW_UI_DYNAMIC_LOAD_MESSAGE_COUNT, 0);
                    rawCount = rawMsgs.Count;
                    
                    var deduplicatedMsgs = DeduplicateMessages(rawMsgs, currentUserId);
                    
                    // 🔧 增强型边界过滤：移除锚点消息及其所有副本
                    messages = FilterBoundaryMessages(deduplicatedMsgs, rawMsgs, lastMsgId);
                }
                else if (direction == "down")
                {
                    // 向下滚动：加载更新的消息（lastMessageId之后的N条）
                    var rawMsgs = LoadMessagesAroundAnchorRaw(lastMsgId, currentUserId, partnerId, 0, NEW_UI_DYNAMIC_LOAD_MESSAGE_COUNT);
                    rawCount = rawMsgs.Count;
                    
                    var deduplicatedMsgs = DeduplicateMessages(rawMsgs, currentUserId);
                    
                    // 🔧 增强型边界过滤：移除锚点消息及其所有副本
                    messages = FilterBoundaryMessages(deduplicatedMsgs, rawMsgs, lastMsgId);
                }
                else
                {
                    Response.Write("{\"success\":false,\"error\":\"无效的方向参数\"}");
                    return;
                }

                // 修复消息字段方向异常
                FixSingleCopyMessageOrientation(messages, currentUserId);

                // 构建返回数据
                var result = new
                {
                    success = true,
                    messages = messages.Select(msg => {
                        var isFromCurrentUser = msg.userid.ToString() == userid;
                        // ✅ 获取发送者头像信息
                        var senderAvatar = AvatarHelper.GetUserAvatar(msg.userid.ToString(), siteid, a, http_start);

                        return new
                        {
                            id = msg.id.ToString(),
                            content = msg.content ?? "",
                            processedContent = WapTool.ToWML(msg.content ?? "", wmlVo),
                            addTime = msg.addtime,
                            senderId = msg.userid,
                            isFromCurrentUser = isFromCurrentUser,
                            timeDisplay = msg.addtime.ToString("HH:mm"),
                            senderNickname = isFromCurrentUser ? "我" : GetUserNickname(msg.userid),
                            senderAvatarUrl = senderAvatar.AvatarUrl,
                            senderIsDefaultAvatar = senderAvatar.IsDefaultAvatar,
                            senderFirstChar = senderAvatar.FirstChar
                        };
                    }).ToArray(),
                    hasMore = rawCount >= NEW_UI_DYNAMIC_LOAD_MESSAGE_COUNT // 以原始记录数判断是否可能还有更多
                };

                Response.ContentType = "application/json; charset=utf-8";
                Response.Write(JsonConvert.SerializeObject(result));
            }
            catch (Exception ex)
            {
                Response.Write($"{{\"success\":false,\"error\":\"{ex.Message}\"}}");
            }
        }

        /// <summary>
        /// 消息去重：从同一条消息的收/发件箱副本中选择最适合当前用户角色的那条
        /// 带锚点映射功能：记录原始锚点ID对应的最终选择的消息ID
        /// </summary>
        /// <param name="messages">原始消息列表（可能包含重复）</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <param name="originalAnchorId">原始锚点消息ID</param>
        /// <returns>(去重后的消息列表, 映射后的锚点ID)</returns>
        private (List<wap_message_Model> messages, long mappedAnchorId) DeduplicateMessagesWithMapping(
            List<wap_message_Model> messages, 
            long currentUserId, 
            long originalAnchorId,
            string sortOrder = "asc") // 增加排序参数，默认为升序
        {
            if (messages == null || messages.Count == 0)
                return (messages, originalAnchorId);

            // 分组键：无序对(userid, touserid) + 时间 + 内容，确保同一条消息的双副本合并到一组
            var groupedMessages = messages
                .GroupBy(m => new
                {
                    User1 = Math.Min(m.userid, m.touserid),
                    User2 = Math.Max(m.userid, m.touserid),
                    AddTime = m.addtime,
                    Content = m.content ?? ""
                })
                .ToList();

            var deduplicatedMessages = new List<wap_message_Model>();
            long mappedAnchorId = originalAnchorId; // 默认值

            foreach (var group in groupedMessages)
            {
                var messagesInGroup = group.ToList();
                
                if (messagesInGroup.Count == 1)
                {
                    // 只有一条记录，直接使用
                    var singleMessage = messagesInGroup[0];
                    deduplicatedMessages.Add(singleMessage);
                    
                    // 检查是否是锚点消息
                    if (singleMessage.id == originalAnchorId)
                    {
                        mappedAnchorId = singleMessage.id; // 保持不变
                    }
                }
                else
                {
                    // 多条记录，选择最适合当前用户角色的那条
                    var selectedMessage = SelectBestMessageVersion(messagesInGroup, currentUserId);
                    deduplicatedMessages.Add(selectedMessage);
                    
                    // 检查这个组是否包含原始锚点，如果是，更新映射
                    if (messagesInGroup.Any(m => m.id == originalAnchorId))
                    {
                        mappedAnchorId = selectedMessage.id;
                    }
                }
            }

            // 根据指定的排序顺序对最终结果进行排序
            if (sortOrder.Equals("desc", StringComparison.OrdinalIgnoreCase))
            {
                // 降序：新消息在前
                var sortedMessages = deduplicatedMessages.OrderByDescending(m => m.addtime).ThenByDescending(m => m.id).ToList();
                return (sortedMessages, mappedAnchorId);
            }
            else
            {
                // 升序：旧消息在前（默认行为）
                var sortedMessages = deduplicatedMessages.OrderBy(m => m.addtime).ThenBy(m => m.id).ToList();
                return (sortedMessages, mappedAnchorId);
            }
        }

        /// <summary>
        /// 消息去重：从同一条消息的收/发件箱副本中选择最适合当前用户角色的那条
        /// 核心逻辑：发送方看发件箱记录，接收方看收件箱记录
        /// </summary>
        /// <param name="messages">原始消息列表（可能包含重复）</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>去重后的消息列表</returns>
        private List<wap_message_Model> DeduplicateMessages(List<wap_message_Model> messages, long currentUserId, string sortOrder = "asc")
        {
            // 调用带映射的方法，但忽略映射结果（保持向后兼容）
            var (deduplicatedMessages, _) = DeduplicateMessagesWithMapping(messages, currentUserId, 0, sortOrder);

            return deduplicatedMessages;
        }

        /// <summary>
        /// 从同一条消息的多个副本中选择最适合的版本（角色优选模式）
        /// </summary>
        /// <param name="versions">同一条消息的所有版本</param>
        /// <param name="currentUserId">当前用户ID</param>
        /// <returns>最适合的消息版本</returns>
        private wap_message_Model SelectBestMessageVersion(List<wap_message_Model> versions, long currentUserId)
        {
            if (versions.Count == 1)
            {
                return versions[0]; // 只有一条记录，直接返回
            }

            // 分析组内消息的发送关系，确定真实的发送方
            var firstMessage = versions.First();
            var realSenderId = firstMessage.userid;
            var realReceiverId = firstMessage.touserid;

            // 判断当前用户在这条消息中的角色
            bool currentUserIsSender = (realSenderId == currentUserId);
            bool currentUserIsReceiver = (realReceiverId == currentUserId);

            if (currentUserIsSender)
            {
                // 当前用户是发送方 → 优先选择发件箱记录 (isnew=2)
                var senderVersion = versions.FirstOrDefault(v => v.userid == currentUserId && v.isnew == 2);
                if (senderVersion != null)
                {
                    return senderVersion;
                }
            }
            else if (currentUserIsReceiver)
            {
                // 当前用户是接收方 → 优先选择收件箱记录 (isnew=0/1，未读优先)
                var receiverVersions = versions.Where(v => v.touserid == currentUserId && v.isnew < 2)
                                              .OrderByDescending(v => v.isnew) // 未读(1)优先于已读(0)
                                              .ToList();
                if (receiverVersions.Count > 0)
                {
                    var selectedReceiver = receiverVersions.First();
                    return selectedReceiver;
                }
            }

            // 兜底：按时间最早、ID最小排序选择
            var fallbackChoice = versions.OrderBy(v => v.addtime).ThenBy(v => v.id).First();
            return fallbackChoice;
        }

        /// <summary>
        /// 增强型边界过滤：基于内容和时间移除锚点消息及其所有副本
        /// </summary>
        private List<wap_message_Model> FilterBoundaryMessages(List<wap_message_Model> deduplicatedMessages, List<wap_message_Model> rawMessages, long anchorMessageId)
        {
            var originalAnchor = rawMessages.FirstOrDefault(m => m.id == anchorMessageId);

            if (originalAnchor != null)
            {
                // 使用锚点消息的 addtime 和 content 来精确识别并过滤所有副本
                var finalMessages = deduplicatedMessages
                    .Where(m => !(m.addtime == originalAnchor.addtime && m.content == originalAnchor.content))
                    .ToList();

                return finalMessages;
            }
            else
            {
                // 回退机制：如果原始锚点未找到，则使用旧的、仅基于ID的过滤
                return deduplicatedMessages.Where(m => m.id != anchorMessageId).ToList();
            }
        }

        /// <summary>
        /// 获取用户昵称的辅助方法
        /// </summary>
        private string GetUserNickname(long userId)
        {
            try
            {
                user_BLL userBll = new user_BLL(a);
                user_Model user = userBll.getUserInfo(userId.ToString(), siteid);
                return user?.nickname ?? userId.ToString();
            }
            catch
            {
                return userId.ToString();
            }
        }

        /// <summary>
        /// 智能加载消息：确保至少有指定数量的消息
        /// 当锚点在边界位置时，会自动在另一方向补充加载以达到最少消息数量要求
        /// </summary>
        /// <param name="anchorMessageId">锚点消息ID</param>
        /// <param name="myUserId">当前用户ID</param>
        /// <param name="partnerUserId">对话伙伴ID</param>
        /// <param name="minimumCount">最少消息数量</param>
        /// <returns>(原始消息列表, 去重后的消息列表, 映射后的锚点ID, 实际请求的前面数量, 实际请求的后面数量)</returns>
        private (List<wap_message_Model> rawMessages, List<wap_message_Model> contextMessages, long mappedAnchorId, int requestedBeforeCount, int requestedAfterCount) LoadMessagesWithMinimumCount(
            long anchorMessageId, long myUserId, long partnerUserId, int minimumCount)
        {
            // 1. 先用标准的前后各2条加载
            var initialRawMessages = LoadMessagesAroundAnchorRaw(anchorMessageId, myUserId, partnerUserId, NEW_UI_INITIAL_CONTEXT_BEFORE_COUNT, NEW_UI_INITIAL_CONTEXT_AFTER_COUNT);
            var (initialContextMessages, mappedAnchorId) = DeduplicateMessagesWithMapping(initialRawMessages, myUserId, anchorMessageId, "asc");

            // 2. 检查是否已经满足最少数量要求
            if (initialContextMessages.Count >= minimumCount)
            {
                return (initialRawMessages, initialContextMessages, mappedAnchorId, NEW_UI_INITIAL_CONTEXT_BEFORE_COUNT, NEW_UI_INITIAL_CONTEXT_AFTER_COUNT);
            }

            // 3. 不足最少数量，需要补充加载
            int needMoreCount = minimumCount - initialContextMessages.Count;
            
            // 4. 分析锚点在初始结果中的位置，判断哪个方向可以补充
            var anchorIndex = initialRawMessages.FindIndex(m => m.id == anchorMessageId);
            if (anchorIndex == -1)
            {
                // 锚点未找到，返回初始结果
                return (initialRawMessages, initialContextMessages, mappedAnchorId, NEW_UI_INITIAL_CONTEXT_BEFORE_COUNT, NEW_UI_INITIAL_CONTEXT_AFTER_COUNT);
            }

            int actualBeforeCount = anchorIndex;
            int actualAfterCount = initialRawMessages.Count - 1 - anchorIndex;

            // 5. 判断补充方向和数量
            int additionalBefore = 0;
            int additionalAfter = 0;

            if (actualBeforeCount < NEW_UI_INITIAL_CONTEXT_BEFORE_COUNT)
            {
                // 前面已经到边界，只能在后面补充
                additionalAfter = needMoreCount;
            }
            else if (actualAfterCount < NEW_UI_INITIAL_CONTEXT_AFTER_COUNT)
            {
                // 后面已经到边界，只能在前面补充
                additionalBefore = needMoreCount;
            }
            else
            {
                // 两个方向都有空间，优先在消息较少的方向补充
                if (actualBeforeCount <= actualAfterCount)
                {
                    additionalBefore = needMoreCount;
                }
                else
                {
                    additionalAfter = needMoreCount;
                }
            }

            // 6. 执行补充加载
            int finalBeforeCount = NEW_UI_INITIAL_CONTEXT_BEFORE_COUNT + additionalBefore;
            int finalAfterCount = NEW_UI_INITIAL_CONTEXT_AFTER_COUNT + additionalAfter;
            
            var finalRawMessages = LoadMessagesAroundAnchorRaw(
                anchorMessageId, 
                myUserId, 
                partnerUserId, 
                finalBeforeCount, 
                finalAfterCount
            );

            // 7. 对最终结果进行去重和映射
            var (finalContextMessages, finalMappedAnchorId) = DeduplicateMessagesWithMapping(finalRawMessages, myUserId, anchorMessageId, "asc");

            return (finalRawMessages, finalContextMessages, finalMappedAnchorId, finalBeforeCount, finalAfterCount);
        }

        /// <summary>
        /// 为新版UI加载锚点消息的上下文数据
        /// </summary>
        /// <param name="anchorMessageId">原始锚点消息ID</param>
        /// <param name="myUserId">当前用户ID</param>
        /// <param name="partnerUserId">对话伙伴ID</param>
        /// <param name="sortOrder">排序顺序</param>
        /// <returns>(去重后的消息列表, 映射后的锚点ID)</returns>
        private (List<wap_message_Model> messages, long mappedAnchorId) LoadContextualMessagesWithMapping(
            long anchorMessageId, long myUserId, long partnerUserId, string sortOrder = "asc")
        {
            // 使用新版UI配置常量加载上下文消息
            var rawMessages = LoadMessagesAroundAnchorRaw(anchorMessageId, myUserId, partnerUserId, NEW_UI_INITIAL_CONTEXT_BEFORE_COUNT, NEW_UI_INITIAL_CONTEXT_AFTER_COUNT);
            return DeduplicateMessagesWithMapping(rawMessages, myUserId, anchorMessageId, sortOrder);
        }

        /// <summary>
        /// 为旧版UI加载最新的对话记录
        /// </summary>
        /// <param name="myUserId">当前用户ID</param>
        /// <param name="partnerUserId">对话伙伴ID</param>
        /// <returns>最新的消息列表</returns>
        private List<wap_message_Model> LoadLatestMessagesForOldUI(long myUserId, long partnerUserId)
        {
            const string query = @"
SELECT TOP (@p_count) *
FROM wap_message
WHERE siteid = @p_siteid
    AND isnew <= 2
    AND issystem <> 2
    AND ((userid = @p_myUserId AND touserid = @p_partnerUserId)
         OR (userid = @p_partnerUserId AND touserid = @p_myUserId))
ORDER BY addtime DESC, id DESC";

            var parameters = new
            {
                p_count = OLD_UI_MESSAGE_COUNT,
                p_myUserId = myUserId,
                p_partnerUserId = partnerUserId,
                p_siteid = DapperHelper.SafeParseLong(siteid, "站点ID")
            };

            var rawMessages = DapperHelper.Query<wap_message_Model>(PubConstant.GetConnectionString(a), query, parameters).ToList();

            // 对旧版UI的消息进行去重处理
            var deduplicatedMessages = DeduplicateMessages(rawMessages, myUserId, "desc");

            return deduplicatedMessages;
        }

        /// <summary>
        /// 修复因对方删除收件箱记录导致仅剩单副本 (isnew = 2) 时的消息方向错误。
        /// 逻辑：
        /// 1. 如果当前用户出现在 touserid 且 isnew = 2，则该记录应视为"我"发送，强制 userid = currentUserId。
        /// 2. 如果当前用户出现在 userid 且 isnew = 2，但 touserid 不是当前用户，则说明这是对方发件箱副本，应视为"对方"发送，强制 userid = touserid。
        /// </summary>
        /// <param name="messages">聊天记录列表</param>
        /// <param name="currentUserId">当前用户ID</param>
        private void FixSingleCopyMessageOrientation(List<wap_message_Model> messages, long currentUserId)
        {
            if (messages == null || messages.Count == 0) return;

            foreach (var msg in messages)
            {
                bool needsFix = false;

                // 情况1：标准发件箱副本 (isnew = 2)
                if (msg.isnew == 2)
                {
                    needsFix = true;
                }
                // 情况2：检测nickname与userid不匹配的异常数据
                else if (!string.IsNullOrEmpty(msg.nickname))
                {
                    try
                    {
                        user_BLL userBll = new user_BLL(a);
                        user_Model actualUser = userBll.getUserInfo(msg.userid.ToString(), siteid);

                        // 如果nickname与userid不匹配，但与touserid匹配，说明字段颠倒
                        if (actualUser != null && actualUser.nickname != msg.nickname)
                        {
                            user_Model toUser = userBll.getUserInfo(msg.touserid.ToString(), siteid);
                            if (toUser != null && toUser.nickname == msg.nickname)
                            {
                                needsFix = true;
                            }
                        }
                    }
                    catch { }
                }

                if (needsFix)
                {
                    // 交换字段，恢复正确的发送关系
                    long temp = msg.userid;
                    msg.userid = msg.touserid;
                    msg.touserid = temp;
                }
            }
        }


    }
}