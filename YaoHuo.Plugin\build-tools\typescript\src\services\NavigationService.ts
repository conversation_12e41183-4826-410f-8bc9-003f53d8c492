/**
 * 导航服务
 * 从Header.hbs中提取的智能返回逻辑，创建独立的导航服务
 */
export class NavigationService {
    private static instance: NavigationService;

    private constructor() {}

    public static getInstance(): NavigationService {
        if (!NavigationService.instance) {
            NavigationService.instance = new NavigationService();
        }
        return NavigationService.instance;
    }

    /**
     * 智能返回函数
     * 根据当前页面类型和来源页面智能决定返回目标
     */
    public smartBack(): void {
        try {
            const currentUrl = window.location.href;
            const currentPath = window.location.pathname;
            const referrer = document.referrer;

            // 判断当前页面类型
            const isMyFilePage = currentPath.includes('myfile.aspx');
            const isUserInfoPage = currentPath.includes('userinfo.aspx');
            const isUserSpaceSubPage = this.isUserSpaceSubPage(currentPath);

            // 如果是个人中心页面，始终跳转到首页
            if (isMyFilePage) {
                window.location.href = '/';
                return;
            }

            // 如果是userinfo页面，始终跳转到首页
            if (isUserInfoPage) {
                window.location.href = '/';
                return;
            }

            // 如果是个人空间子页面且没有有效referrer，返回对应的userinfo页面
            if (isUserSpaceSubPage && (!referrer || !this.isValidReferrer(referrer) || this.isSamePageNavigation(referrer, currentUrl))) {
                const userInfoUrl = this.buildUserInfoUrl(currentUrl);
                window.location.href = userInfoUrl;
                return;
            }

            // 其他页面的处理逻辑
            // 检查是否有有效的来源页面且不是翻页操作
            if (referrer && referrer !== currentUrl && this.isValidReferrer(referrer) && !this.isSamePageNavigation(referrer, currentUrl)) {
                // 尝试返回上一页
                window.history.back();

                // 设置超时检查，如果返回失败则使用fallback逻辑
                setTimeout(() => {
                    if (window.location.href === currentUrl) {
                        const fallbackUrl = this.getFallbackUrl(currentPath, currentUrl);
                        window.location.href = fallbackUrl;
                    }
                }, 500);
            } else {
                // 没有有效来源页面或是翻页操作，使用fallback逻辑
                const fallbackUrl = this.getFallbackUrl(currentPath, currentUrl);
                window.location.href = fallbackUrl;
            }

        } catch (error) {
            // 如果出现任何错误，根据当前页面类型决定跳转目标
            console.log('Back button error:', error);
            const fallbackUrl = this.getFallbackUrl(window.location.pathname, window.location.href);
            window.location.href = fallbackUrl;
        }
    }

    /**
     * 检查来源页面是否有效
     */
    private isValidReferrer(referrer: string): boolean {
        const currentDomain = window.location.origin;
        return referrer.startsWith(currentDomain) || referrer.startsWith('http');
    }

    /**
     * 检查是否为个人空间子页面
     */
    private isUserSpaceSubPage(currentPath: string): boolean {
        const userSpaceSubPages = [
            'book_list_log.aspx',
            'userguessbook.aspx',
            'userinfomore.aspx',
            'book_re_my.aspx'
        ];
        return userSpaceSubPages.some(page => currentPath.includes(page));
    }

    /**
     * 构建对应的userinfo页面URL（简化版，只传递touserid参数）
     */
    private buildUserInfoUrl(currentUrl: string): string {
        try {
            const urlObj = new URL(currentUrl);
            const params = new URLSearchParams(urlObj.search);
            const touserid = params.get('touserid');

            if (touserid) {
                // 直接返回对应的userinfo页面，保留touserid参数
                return `/bbs/userinfo.aspx?touserid=${touserid}`;
            } else {
                // 如果没有touserid参数，返回首页
                return '/';
            }
        } catch (error) {
            console.log('[SmartBack] Build userinfo URL error:', error);
            return '/';
        }
    }

    /**
     * 获取fallback URL
     */
    private getFallbackUrl(currentPath: string, currentUrl: string): string {
        const isMyFilePage = currentPath.includes('myfile.aspx');
        const isUserInfoPage = currentPath.includes('userinfo.aspx');
        const isUserSpaceSubPage = this.isUserSpaceSubPage(currentPath);

        if (isMyFilePage) {
            return '/';
        } else if (isUserInfoPage) {
            // userinfo页面始终返回首页
            return '/';
        } else if (isUserSpaceSubPage) {
            // 个人空间子页面返回对应的userinfo页面，保留touserid参数
            return this.buildUserInfoUrl(currentUrl);
        } else {
            return '/myfile.aspx';
        }
    }

    /**
     * 检查是否为同一页面的翻页导航（避免翻页时的返回问题）
     */
    private isSamePageNavigation(referrer: string, currentUrl: string): boolean {
        try {
            const referrerUrl = new URL(referrer);
            const currentUrlObj = new URL(currentUrl);

            // 如果路径相同，只是参数不同，认为是同一页面的翻页
            if (referrerUrl.pathname === currentUrlObj.pathname) {
                const referrerParams = new URLSearchParams(referrerUrl.search);
                const currentParams = new URLSearchParams(currentUrlObj.search);

                // 检查翻页相关参数
                const pageParams = ['page', 'lpage', 'CurrentPage', 'ot'];
                for (const param of pageParams) {
                    if (referrerParams.has(param) || currentParams.has(param)) {
                        return true;
                    }
                }

                // 检查标签切换参数（messagelist页面特有）
                if (currentUrlObj.pathname.includes('messagelist.aspx')) {
                    const tabParams = ['types']; // messagelist的标签参数
                    for (const param of tabParams) {
                        if (referrerParams.has(param) || currentParams.has(param)) {
                            return true;
                        }
                    }
                }
            }

            return false;
        } catch (error) {
            // URL解析失败，保守处理
            return false;
        }
    }

    /**
     * 初始化返回按钮
     * 为指定的返回按钮元素绑定智能返回功能
     */
    public initializeBackButton(buttonSelector: string = '#back-button'): void {
        const backButton = document.querySelector(buttonSelector) as HTMLElement;
        
        if (backButton) {
            backButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.smartBack();
            });
        }
    }

    /**
     * 简单返回（不使用智能逻辑）
     */
    public simpleBack(): void {
        if (window.history.length > 1) {
            window.history.back();
        } else {
            window.location.href = '/';
        }
    }

    /**
     * 返回到指定URL
     */
    public backTo(url: string): void {
        window.location.href = url;
    }

    /**
     * 返回到首页
     */
    public backToHome(): void {
        window.location.href = '/';
    }

    /**
     * 返回到个人中心
     */
    public backToMyFile(): void {
        window.location.href = '/myfile.aspx';
    }

    /**
     * 获取当前页面的推荐返回URL（不执行跳转）
     */
    public getRecommendedBackUrl(): string {
        const currentPath = window.location.pathname;
        const currentUrl = window.location.href;
        return this.getFallbackUrl(currentPath, currentUrl);
    }
}
