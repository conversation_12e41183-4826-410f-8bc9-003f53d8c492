import { ToastService } from '../services/ToastService.js';
import { ModalService } from '../services/ModalService.js';
import { PaginationService } from '../services/PaginationService.js';
import { AjaxService } from '../services/AjaxService.js';
export class FavListPage {
    static getInstance() {
        if (!FavListPage.instance) {
            FavListPage.instance = new FavListPage();
        }
        return FavListPage.instance;
    }
    static init() {
        FavListPage.getInstance().initialize();
    }
    initialize() {
        console.log('FavList页面初始化开始');
        this.initLucideIcons();
        this.initToastAutoClose();
        this.initPagination();
        this.bindDeleteFavEvents();
        this.bindClearFavEvents();
        console.log('FavList页面初始化完成');
    }
    initLucideIcons() {
        if (typeof window.lucide !== 'undefined') {
            window.lucide.createIcons();
            console.log('FavList页面: Lucide图标初始化完成');
        }
    }
    initToastAutoClose() {
        const errorToast = document.getElementById('errorToast');
        if (errorToast) {
            setTimeout(() => {
                ToastService.close('errorToast');
            }, 3000);
        }
        const infoToast = document.getElementById('infoToast');
        if (infoToast) {
            setTimeout(() => {
                ToastService.close('infoToast');
            }, 3000);
        }
    }
    initPagination() {
        const paginationInfo = this.extractPaginationInfo();
        if (paginationInfo) {
            const config = {
                currentPage: paginationInfo.currentPage,
                totalPages: paginationInfo.totalPages,
                baseUrl: window.location.href,
                pageParam: 'page',
                showPrevNext: true
            };
            PaginationService.init(config);
            console.log(`FavList页面: 分页初始化完成 (${paginationInfo.currentPage}/${paginationInfo.totalPages})`);
        }
    }
    extractPaginationInfo() {
        const paginationText = document.querySelector('.flex-1.text-center.text-sm.text-text-secondary.px-2');
        if (paginationText && paginationText.textContent) {
            const match = paginationText.textContent.match(/第\s*(\d+)\s*\/\s*(\d+)\s*页/);
            if (match) {
                return {
                    currentPage: parseInt(match[1]),
                    totalPages: parseInt(match[2])
                };
            }
        }
        const urlParams = new URLSearchParams(window.location.search);
        const currentPage = parseInt(urlParams.get('page') || '1');
        const prevBtn = document.getElementById('prevPageBtn');
        const nextBtn = document.getElementById('nextPageBtn');
        if (prevBtn || nextBtn) {
            let totalPages = currentPage;
            if (nextBtn && !nextBtn.disabled) {
                totalPages = currentPage + 1;
            }
            return { currentPage, totalPages };
        }
        return null;
    }
    bindDeleteFavEvents() {
        document.querySelectorAll('.delete-fav-btn').forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const deleteUrl = button.dataset.deleteUrl;
                const itemTitle = button.dataset.itemTitle;
                const itemCard = button.closest('.fav-item-card');
                if (deleteUrl && itemTitle) {
                    this.deleteFavorite(deleteUrl, itemTitle, button, itemCard);
                }
            });
        });
    }
    bindClearFavEvents() {
        const clearFavoritesBtn = document.getElementById('clear-favorites-btn');
        if (clearFavoritesBtn) {
            clearFavoritesBtn.addEventListener('click', () => {
                this.clearAllFavorites(clearFavoritesBtn);
            });
        }
    }
    async deleteFavorite(deleteUrl, itemTitle, button, itemCard) {
        try {
            const confirmed = await ModalService.confirmDelete(`确定要删除收藏"${itemTitle}"吗？`);
            if (confirmed) {
                await AjaxService.delete(deleteUrl, {
                    showLoading: true,
                    loadingElement: button,
                    loadingText: '删除中...',
                    showToast: true,
                    successMessage: '删除成功',
                    errorMessage: '删除失败，请重试',
                    onSuccess: () => {
                        this.removeItemCard(itemCard);
                        this.updateEmptyState();
                    }
                });
            }
        }
        catch (error) {
            console.error('删除收藏失败:', error);
        }
    }
    async clearAllFavorites(button) {
        try {
            const confirmed = await ModalService.confirmDelete('确定要清空所有收藏吗？此操作不可恢复！');
            if (confirmed) {
                const currentUrl = new URL(window.location.href);
                const clearUrl = `/bbs/favlist.aspx?action=deleteall&siteid=${currentUrl.searchParams.get('siteid') || ''}&favtypeid=${currentUrl.searchParams.get('favtypeid') || '0'}`;
                await AjaxService.delete(clearUrl, {
                    showLoading: true,
                    loadingElement: button,
                    loadingText: '清空中...',
                    showToast: true,
                    successMessage: '清空成功',
                    errorMessage: '清空失败，请重试',
                    onSuccess: () => {
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    }
                });
            }
        }
        catch (error) {
            console.error('清空收藏失败:', error);
        }
    }
    removeItemCard(itemCard) {
        itemCard.style.transition = 'all 0.3s ease';
        itemCard.style.opacity = '0';
        itemCard.style.transform = 'translateX(-100%)';
        setTimeout(() => {
            if (itemCard.parentNode) {
                itemCard.parentNode.removeChild(itemCard);
            }
        }, 300);
    }
    updateEmptyState() {
        const favItems = document.querySelectorAll('.fav-item-card');
        const emptyState = document.querySelector('.empty-state');
        if (favItems.length === 0 && !emptyState) {
            const emptyDiv = document.createElement('div');
            emptyDiv.className = 'empty-state text-center py-12';
            emptyDiv.innerHTML = `
                <i data-lucide="heart" class="w-16 h-16 mx-auto text-gray-300 mb-4"></i>
                <p class="text-gray-500">暂无收藏内容</p>
            `;
            const container = document.querySelector('.card-body');
            if (container) {
                container.appendChild(emptyDiv);
                if (typeof window.lucide !== 'undefined') {
                    window.lucide.createIcons();
                }
            }
        }
    }
    getPageStats() {
        const favItems = document.querySelectorAll('.fav-item-card');
        const paginationInfo = this.extractPaginationInfo();
        return {
            totalItems: favItems.length,
            currentPage: paginationInfo?.currentPage || 1,
            totalPages: paginationInfo?.totalPages || 1
        };
    }
}
document.addEventListener('DOMContentLoaded', () => {
    FavListPage.init();
});
export function closeToast(toastId) {
    ToastService.close(toastId);
}
export function autoCloseToast(toastId, delay = 3000) {
    setTimeout(() => {
        ToastService.close(toastId);
    }, delay);
}
export function showToast(type, message) {
    switch (type) {
        case 'success':
            ToastService.showSuccess(message);
            break;
        case 'error':
            ToastService.showError(message);
            break;
        case 'warning':
            ToastService.showWarning(message);
            break;
        case 'info':
            ToastService.showInfo(message);
            break;
    }
}
export function showCustomConfirm(message, onConfirm) {
    ModalService.confirm(message, onConfirm);
}
export function getFavListPage() {
    return FavListPage.getInstance();
}
export default FavListPage;
