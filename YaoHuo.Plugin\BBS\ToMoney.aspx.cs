﻿using KeLin.ClassManager;
using System;
using System.Data;
using System.Web;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using Dapper;

namespace YaoHuo.Plugin.BBS
{
    public class ToMoney : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string tomoney = "";

        public string backurl = "";

        public string INFO = "";

        public string ERROR = "";

        public long STATE = 0L;

        public string touserid = "";

        public string remark = "";

        public string type = "";

        public string maxs = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = base.Request.Form.Get("action");
            tomoney = GetRequestValue("tomoney");
            touserid = GetRequestValue("touserid");
            remark = GetRequestValue("remark");
            type = GetRequestValue("type");
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            IsLogin(userid, backurl);
            maxs = WapTool.GetArryString(siteVo.Version, '|', 22);
            if (!WapTool.IsNumeric(maxs))
            {
                maxs = "0";
            }
            if (long.Parse(maxs) < 2L)
            {
                maxs = "1000";
            }
            STATE = WapTool.GetLvLRegular(siteVo.Version, 3);
            if ((STATE == 0L && userVo.managerlvl == "02") || STATE == 2L)
            {
                ShowTipInfo(GetLang("此功能暂时关闭，如有需要，请联系站长启用！(网站默认配置--[3]会员转虚拟币功能)"), backurl);
            }
            NeedPassWordToAdminNew();
            switch (action)
            {
                case "sub":
                    subMoney();
                    break;
                case "add":
                    addMoney();
                    break;
            }
        }

        public void addMoney()
        {
            if (!WapTool.IsNumeric(tomoney) || tomoney.IndexOf('-') >= 0 || !WapTool.IsNumeric(touserid))
            {
                INFO = "NUM";
            }
            else if (!WapTool.IsExistUser(siteid, touserid))
            {
                INFO = "NOTUSER";
            }
            else if (userVo.managerlvl == "00" || userVo.managerlvl == "01")
            {
                // ✅ 使用事务确保资金操作的原子性
                try
                {
                    ExecuteAdminRewardTransaction();
                    INFO = "OK";
                }
                catch (Exception ex)
                {
                    ERROR = "操作失败: " + ex.Message;
                }
            }
            else if (STATE == 0L)
            {
                INFO = "CLOSE";
            }
            else if (userVo.money < 500L || userVo.money < long.Parse(tomoney))
            {
                INFO = "NOTMONEY";
            }
            else if (long.Parse(tomoney) > long.Parse(maxs))
            {
                INFO = "MAXMONEY";
            }
            else
            {
                // ✅ 使用事务确保转账操作的原子性
                try
                {
                    ExecuteUserTransferTransaction();
                    INFO = "OK";
                }
                catch (Exception ex)
                {
                    ERROR = "转账失败: " + ex.Message;
                }
            }
        }

        /// <summary>
        /// 执行管理员奖励的事务操作
        /// </summary>
        private void ExecuteAdminRewardTransaction()
        {
            string connectionString = PubConstant.GetConnectionString(string_10);
            long moneyAmount = long.Parse(tomoney);
            long adminUserId = long.Parse(userid);
            long targetUserId = long.Parse(touserid);
            long siteIdLong = long.Parse(siteid);

            TransactionHelper.ExecuteMoneyTransaction(connectionString, (connection, transaction) =>
            {
                // 1. 获取目标用户当前余额（事务内锁定）
                long targetUserCurrentBalance = TransactionHelper.GetUserBalanceWithLock(connection, transaction, targetUserId, siteIdLong);

                // 2. 给目标用户加钱
                TransactionHelper.UpdateUserMoney(connection, transaction, targetUserId, siteIdLong, moneyAmount);

                // 3. 发送系统消息
                string messageTitle = "恭喜您，" + userVo.nickname + "奖励" + tomoney + "个币给您！";
                string messageContent = string.IsNullOrEmpty(remark) ?
                    userVo.nickname + "奖励" + tomoney + "个币给您！" :
                    "原因:" + remark;

                TransactionHelper.SendSystemMessage(connection, transaction, siteIdLong, adminUserId,
                    userVo.nickname, targetUserId, messageTitle, messageContent);

                // 4. 记录银行日志（使用安全方法，传入计算后的余额）
                long targetUserNewBalance = targetUserCurrentBalance + moneyAmount;
                TransactionHelper.SaveBankLogWithBalance(connection, transaction, siteIdLong, targetUserId, "转币操作",
                    moneyAmount, adminUserId, nickname, "操作人转币给我", IP, targetUserNewBalance);

                if (siteid != touserid)
                {
                    // 管理员自己的余额不变（管理员转币不扣自己的钱）
                    TransactionHelper.SaveBankLogWithBalance(connection, transaction, siteIdLong, adminUserId, "转币操作",
                        -moneyAmount, adminUserId, nickname, "我转币给会员ID(" + touserid + ")", IP, userVo.money);
                }
            });
        }

        public void subMoney()
        {
            if (!WapTool.IsNumeric(tomoney) || tomoney.IndexOf('-') >= 0 || !WapTool.IsNumeric(touserid))
            {
                INFO = "NUM";
            }
            else if (!WapTool.IsExistUser(siteid, touserid))
            {
                INFO = "NOTUSER";
            }
            else if (userVo.managerlvl == "00" || userVo.managerlvl == "01")
            {
                // ✅ 使用事务确保扣钱操作的原子性
                try
                {
                    ExecuteAdminDeductTransaction();
                    INFO = "OK";
                }
                catch (Exception ex)
                {
                    ERROR = "扣钱失败: " + ex.Message;
                }
            }
        }

        /// <summary>
        /// 执行用户转账的事务操作
        /// </summary>
        private void ExecuteUserTransferTransaction()
        {
            string connectionString = PubConstant.GetConnectionString(string_10);
            long originalAmount = long.Parse(tomoney);
            long senderUserId = long.Parse(userid);
            long receiverUserId = long.Parse(touserid);
            long siteIdLong = long.Parse(siteid);

            // 计算手续费
            var handlingFee = SendMoneyService.GetHandlingFee(tomoney);
            long actualAmount = originalAmount - handlingFee;

            TransactionHelper.ExecuteMoneyTransaction(connectionString, (connection, transaction) =>
            {
                // 1. 锁定发送者账户并检查余额
                var senderCurrentBalance = TransactionHelper.GetUserBalanceWithLock(connection, transaction, senderUserId, siteIdLong);
                if (senderCurrentBalance < originalAmount)
                {
                    throw new InvalidOperationException($"余额不足，当前余额：{senderCurrentBalance}，需要：{originalAmount}");
                }

                // 2. 锁定接收者账户并获取余额
                var receiverCurrentBalance = TransactionHelper.GetUserBalanceWithLock(connection, transaction, receiverUserId, siteIdLong);

                // 3. 扣除发送者的钱（包含手续费）
                TransactionHelper.UpdateUserMoney(connection, transaction, senderUserId, siteIdLong, -originalAmount);

                // 4. 给接收者加钱（扣除手续费后的金额）
                TransactionHelper.UpdateUserMoney(connection, transaction, receiverUserId, siteIdLong, actualAmount);

                // 5. 发送系统消息
                string messageTitle = userVo.nickname + "转账" + actualAmount + "个妖晶给您！";
                string messageContent = string.IsNullOrEmpty(remark) ? "对方未填写转账备注。" : "备注:" + remark;

                TransactionHelper.SendSystemMessage(connection, transaction, siteIdLong, senderUserId,
                    userVo.nickname, receiverUserId, messageTitle, messageContent);

                // 6. 记录银行日志（使用安全方法，传入计算后的余额）
                // 接收者余额：原余额 + 实际到账金额
                long receiverNewBalance = receiverCurrentBalance + actualAmount;
                TransactionHelper.SaveBankLogWithBalance(connection, transaction, siteIdLong, receiverUserId, "转币操作",
                    actualAmount, senderUserId, nickname, "操作人转币给我", IP, receiverNewBalance);

                // 发送者余额：原余额 - 原始金额
                long senderNewBalance = senderCurrentBalance - originalAmount;
                TransactionHelper.SaveBankLogWithBalance(connection, transaction, siteIdLong, senderUserId, "转币操作",
                    -originalAmount, senderUserId, nickname, "我转币给会员ID(" + touserid + ")", IP, senderNewBalance);
            });
        }

        /// <summary>
        /// 执行管理员扣钱的事务操作
        /// </summary>
        private void ExecuteAdminDeductTransaction()
        {
            string connectionString = PubConstant.GetConnectionString(string_10);
            long moneyAmount = long.Parse(tomoney);
            long adminUserId = long.Parse(userid);
            long targetUserId = long.Parse(touserid);
            long siteIdLong = long.Parse(siteid);

            TransactionHelper.ExecuteMoneyTransaction(connectionString, (connection, transaction) =>
            {
                // 1. 锁定目标用户账户并检查余额
                var targetUserCurrentBalance = TransactionHelper.GetUserBalanceWithLock(connection, transaction, targetUserId, siteIdLong);
                if (targetUserCurrentBalance < moneyAmount)
                {
                    throw new InvalidOperationException($"用户余额不足，当前余额：{targetUserCurrentBalance}，需要扣除：{moneyAmount}");
                }

                // 2. 扣除目标用户的钱
                TransactionHelper.UpdateUserMoney(connection, transaction, targetUserId, siteIdLong, -moneyAmount);

                // 3. 发送系统消息
                string messageTitle = "抱歉，" + userVo.nickname + "扣除您" + tomoney + "个币！";
                string messageContent = string.IsNullOrEmpty(remark) ?
                    userVo.nickname + "扣除您" + tomoney + "个币！" :
                    "原因:" + remark;

                TransactionHelper.SendSystemMessage(connection, transaction, siteIdLong, adminUserId,
                    userVo.nickname, targetUserId, messageTitle, messageContent);

                // 4. 记录银行日志（使用安全方法，传入计算后的余额）
                long targetUserNewBalance = targetUserCurrentBalance - moneyAmount;
                TransactionHelper.SaveBankLogWithBalance(connection, transaction, siteIdLong, targetUserId, "转币操作",
                    -moneyAmount, adminUserId, nickname, "操作人扣除我币", IP, targetUserNewBalance);
            });
        }


    }
}