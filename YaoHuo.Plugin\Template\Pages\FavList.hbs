<div class="fav-type-{{FavTypeId}} pb-4">
{{#if HasError}}
<div class="toast-error" id="errorToast">
    <div class="flex items-center flex-1">
        <i class="w-5 h-5 mr-3 flex-shrink-0" data-lucide="x-circle"></i>
        <span class="flex-1 leading-[1.4]">{{Error}}</span>
    </div>
    <button class="bg-transparent border-none text-white cursor-pointer p-1 ml-3 rounded flex items-center justify-center transition-colors duration-200 flex-shrink-0 hover:bg-white/20" onclick="closeToast('errorToast')">
        <i class="w-4 h-4" data-lucide="x"></i>
    </button>
</div>
{{/if}}

{{#if HasInfo}}
<div class="toast-info" id="infoToast">
    <div class="flex items-center flex-1">
        <i class="w-5 h-5 mr-3 flex-shrink-0" data-lucide="info"></i>
        <span class="flex-1 leading-[1.4]">{{Info}}</span>
    </div>
    <button class="bg-transparent border-none text-white cursor-pointer p-1 ml-3 rounded flex items-center justify-center transition-colors duration-200 flex-shrink-0 hover:bg-white/20" onclick="closeToast('infoToast')">
        <i class="w-4 h-4" data-lucide="x"></i>
    </button>
</div>
{{/if}}

<div class="card">
    <div class="card-header">
        <div class="card-title justify-between">
            <div class="flex items-center">
                <i class="card-icon" data-lucide="bookmark"></i>
                <span>{{PageTitle}}</span>
            </div>
            {{#if FavoritesList}}
            <span class="text-sm text-text-secondary font-normal inline" id="fav-count-display">共 <span id="fav-count-number">{{Pagination.Total}}</span> 个收藏</span>
            {{/if}}
        </div>
    </div>
    <div class="card-body">
        {{#if ShowSearchBox}}
        <div class="mb-4">
            <form method="post" action="?action=class&siteid={{SiteId}}&favtypeid={{FavTypeId}}&backurl={{BackUrl}}">
                <div class="relative flex items-center">
                    <input type="text" name="key" value="{{SearchKey}}" placeholder="输入标题搜索收藏" class="search-input">
                    <button type="submit" class="search-button">
                        <i data-lucide="search" class="w-5 h-5"></i>
                    </button>
                </div>
            </form>
        </div>
        {{/if}}

        <div class="flex flex-col gap-3 min-h-[100px]">
            {{#if FavoritesList}}
            {{#each FavoritesList}}
            <div class="bg-white rounded-lg border border-border-normal p-4 transition-all duration-200 hover:shadow-md fav-item-card">
                <div class="flex items-center">
                    <div class="flex-1 min-w-0">
                        <div class="text-text-primary font-medium text-base cursor-pointer transition-colors duration-200 hover:text-primary truncate" onclick="location.href='{{FullUrl}}'" {{#if IsExternalLink}}target="_blank"{{/if}}>{{{Title}}}</div>
                        <div class="flex items-center gap-2 mt-1">
                            <span class="text-text-light text-[13px]">{{FriendlyAddDate}}</span>
                        </div>
                    </div>
                    <div class="flex items-center ml-4">
                        <button class="inline-flex items-center justify-center p-1.5 rounded text-sm font-medium transition cursor-pointer select-none border border-transparent bg-transparent text-text-light hover:bg-border-light hover:text-danger delete-fav-btn" data-delete-url="{{DeleteUrl}}" data-item-title="{{{Title}}}" aria-label="删除收藏">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                    </div>
                </div>
            </div>
            {{/each}}
            {{else}}
            <div class="text-center py-12 px-4 flex flex-col items-center justify-center">
                <div class="mb-4 w-20 h-20">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 500" fill="none" class="w-full h-full">
                        <circle cx="250" cy="250" r="200" fill="#F5F5F5"></circle>
                        <path d="M165 220C165 198.402 182.402 181 204 181H296C317.598 181 335 198.402 335 220V280C335 301.598 317.598 319 296 319H204C182.402 319 165 301.598 165 280V220Z" fill="white" stroke="#F59E0B" stroke-width="4"></path>
                        <path d="M200 200L300 200" stroke="#F59E0B" stroke-width="3" stroke-linecap="round"></path>
                        <path d="M200 230L280 230" stroke="#F59E0B" stroke-width="3" stroke-linecap="round"></path>
                        <path d="M200 260L260 260" stroke="#F59E0B" stroke-width="3" stroke-linecap="round"></path>
                        <path d="M220 320L280 320" stroke="#F59E0B" stroke-width="4" stroke-linecap="round"></path>
                    </svg>
                </div>
                <div class="text-lg font-medium text-text-primary mb-1">
                    {{#eq FavTypeId "0"}}还没有收藏任何内容{{else}}该分类下暂无收藏{{/eq}}
                </div>
                <div class="text-sm text-text-secondary">
                    {{#eq FavTypeId "0"}}快去收藏你喜欢的内容吧{{else}}去其他地方看看吧{{/eq}}
                </div>
            </div>
            {{/if}}
        </div>

        {{#if Pagination.ShowPagination}}
        <div class="flex items-center justify-center gap-4 mt-4">
            <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light" id="prevPageBtn" {{#if Pagination.IsFirstPage}}disabled{{/if}}>
                <i data-lucide="chevron-left" class="w-5 h-5"></i>
            </button>
            <div class="flex-1 text-center text-sm text-text-secondary px-2">
                第 {{Pagination.CurrentPage}} / {{Pagination.TotalPages}} 页
            </div>
            <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light" id="nextPageBtn" {{#if Pagination.IsLastPage}}disabled{{/if}}>
                <i data-lucide="chevron-right" class="w-5 h-5"></i>
            </button>
        </div>
        {{/if}}
    </div>
</div>

{{!-- 清空收藏按钮 - 放在卡片容器外 --}}
{{#if HasOperations}}
{{#if FavoritesList}}
<div class="mx-4 mt-4">
    <button class="w-full bg-danger text-white border border-danger rounded-md px-4 py-3 font-medium text-sm transition-all duration-200 hover:bg-danger-dark hover:border-danger-dark flex items-center justify-center" id="clear-favorites-btn">
        <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
        清空收藏
    </button>
</div>
{{/if}}
{{/if}}
</div>

<!-- TypeScript模块加载 -->
<script type="module">
    // 加载FavList页面的TypeScript模块
    import '/Template/TS/pages/FavList.js?v=1752033421938';

    console.log('FavList页面TypeScript模块已加载');
</script>



