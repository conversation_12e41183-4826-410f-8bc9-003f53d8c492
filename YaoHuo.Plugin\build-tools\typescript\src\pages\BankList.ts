/**
 * 银行记录列表页面
 * 应用ToastService、ModalService、PaginationService和AjaxService
 * 实现筛选表单、分页导航、交易详情模态框和记录管理功能
 * 
 * @version 1.0
 * <AUTHOR>
 * @date 2025-01-07
 */

import { ModalService } from '../services/ModalService.js';
import { PaginationService } from '../services/PaginationService.js';
import { AjaxService } from '../services/AjaxService.js';

/**
 * BankList页面类
 * 负责银行记录列表页面的所有交互功能
 */
export class BankListPage {
    private static instance: BankListPage;

    /**
     * 获取单例实例
     */
    public static getInstance(): BankListPage {
        if (!BankListPage.instance) {
            BankListPage.instance = new BankListPage();
        }
        return BankListPage.instance;
    }

    /**
     * 初始化页面
     */
    public static init(): void {
        BankListPage.getInstance().initialize();
    }

    /**
     * 初始化页面功能
     */
    public initialize(): void {
        console.log('BankList页面初始化开始');

        // 初始化Lucide图标
        this.initLucideIcons();

        // 初始化筛选表单
        this.initializeFilter();

        // 初始化分页功能
        this.initPagination();

        // 绑定交易详情事件
        this.bindTransactionDetailEvents();

        // 绑定清空记录事件
        this.bindClearRecordsEvents();

        // 绑定其他功能
        this.bindOtherEvents();

        console.log('BankList页面初始化完成');
    }

    /**
     * 初始化Lucide图标
     */
    private initLucideIcons(): void {
        if (typeof (window as any).lucide !== 'undefined') {
            (window as any).lucide.createIcons();
            console.log('BankList页面: Lucide图标初始化完成');
        }
    }

    /**
     * 初始化筛选表单
     */
    private initializeFilter(): void {
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1;

        const yearSelect = document.getElementById('toyear') as HTMLSelectElement;
        const monthSelect = document.getElementById('tomonth') as HTMLSelectElement;

        if (!yearSelect || !monthSelect) return;

        // 生成年份选项（当前年份往前5年）
        for (let year = currentYear; year >= currentYear - 5; year--) {
            const option = document.createElement('option');
            option.value = year.toString();
            option.textContent = year + '年';
            yearSelect.appendChild(option);
        }

        // 生成月份选项
        this.updateMonthOptions(monthSelect, currentMonth);

        // 设置默认选中值
        yearSelect.value = currentYear.toString();
        monthSelect.value = currentMonth.toString();

        // 监听年份变化
        yearSelect.addEventListener('change', () => {
            const selectedYear = parseInt(yearSelect.value);
            const maxMonth = (selectedYear === currentYear) ? currentMonth : 12;
            this.updateMonthOptions(monthSelect, maxMonth);
        });

        // 绑定表单提交事件
        const filterForm = document.getElementById('filter-form') as HTMLFormElement;
        if (filterForm) {
            filterForm.addEventListener('submit', (e) => this.handleSearchSubmit(e));
        }

        console.log('BankList页面: 筛选表单初始化完成');
    }

    /**
     * 更新月份选项
     */
    private updateMonthOptions(monthSelect: HTMLSelectElement, maxMonth: number): void {
        monthSelect.innerHTML = '';
        
        for (let month = 1; month <= maxMonth; month++) {
            const option = document.createElement('option');
            option.value = month.toString();
            option.textContent = month + '月';
            monthSelect.appendChild(option);
        }
    }

    /**
     * 初始化分页功能
     */
    private initPagination(): void {
        // 从页面元素中读取分页信息
        const paginationInfo = this.extractPaginationInfo();
        
        if (paginationInfo) {
            const config = {
                currentPage: paginationInfo.currentPage,
                totalPages: paginationInfo.totalPages,
                baseUrl: window.location.href,
                pageParam: 'page',
                showPrevNext: true
            };
            
            PaginationService.init(config);
            console.log(`BankList页面: 分页初始化完成 (${paginationInfo.currentPage}/${paginationInfo.totalPages})`);
        }
    }

    /**
     * 从页面元素中提取分页信息
     */
    private extractPaginationInfo(): { currentPage: number; totalPages: number } | null {
        // 从分页文本中提取 "第 X / Y 页"
        const paginationText = document.querySelector('.flex-1.text-center.text-sm.text-text-secondary.px-2');
        if (paginationText && paginationText.textContent) {
            const match = paginationText.textContent.match(/第\s*(\d+)\s*\/\s*(\d+)\s*页/);
            if (match) {
                return {
                    currentPage: parseInt(match[1]),
                    totalPages: parseInt(match[2])
                };
            }
        }

        return null;
    }

    /**
     * 绑定交易详情事件
     */
    private bindTransactionDetailEvents(): void {
        document.querySelectorAll('tr[onclick*="showTransactionDetail"]').forEach(row => {
            // 移除原有的onclick属性，避免重复触发
            row.removeAttribute('onclick');
            row.addEventListener('click', (e) => {
                e.preventDefault();
                this.showTransactionDetail(row as HTMLElement);
            });
        });
    }

    /**
     * 绑定清空记录事件
     */
    private bindClearRecordsEvents(): void {
        const clearRecordsBtn = document.getElementById('clear-records-btn');
        if (clearRecordsBtn) {
            clearRecordsBtn.addEventListener('click', () => {
                this.clearAllRecords();
            });
        }
    }

    /**
     * 绑定其他功能事件
     */
    private bindOtherEvents(): void {
        // 将全局函数暴露到window对象，供HTML onclick调用
        (window as any).resetFilter = () => this.resetFilter();
        (window as any).navigatePage = (page: number) => this.navigatePage(page);
        (window as any).toggleStats = () => this.toggleStats();
        (window as any).goBack = () => this.goBack();
        (window as any).showTransactionDetail = (row: HTMLElement) => this.showTransactionDetail(row);
        (window as any).closeTransactionDetail = () => this.closeTransactionDetail();
        (window as any).handleSearchSubmit = (event: Event) => this.handleSearchSubmit(event);

        console.log('BankList页面: 全局函数已暴露到window对象');
    }

    /**
     * 处理搜索表单提交
     */
    private handleSearchSubmit(event: Event): void {
        event.preventDefault();

        const form = event.target as HTMLFormElement;
        const formData = new FormData(form);
        
        // 构建URL参数
        const params = new URLSearchParams();
        params.set('action', 'search');
        
        const year = formData.get('year') as string;
        const month = formData.get('month') as string;
        
        if (year) params.set('year', year);
        if (month) params.set('month', month);
        
        // 保留siteid参数
        const currentUrl = new URL(window.location.href);
        const siteid = currentUrl.searchParams.get('siteid');
        if (siteid) params.set('siteid', siteid);

        // 跳转到搜索结果页面
        const newUrl = `${window.location.pathname}?${params.toString()}`;
        window.location.href = newUrl;
    }

    /**
     * 重置筛选条件
     */
    public resetFilter(): void {
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1;

        const yearSelect = document.getElementById('toyear') as HTMLSelectElement;
        const monthSelect = document.getElementById('tomonth') as HTMLSelectElement;

        if (yearSelect) yearSelect.value = currentYear.toString();
        if (monthSelect) {
            this.updateMonthOptions(monthSelect, currentMonth);
            monthSelect.value = currentMonth.toString();
        }

        // 跳转到默认页面
        const currentUrl = new URL(window.location.href);
        const siteid = currentUrl.searchParams.get('siteid');
        const newUrl = siteid ? `${window.location.pathname}?siteid=${siteid}` : window.location.pathname;
        window.location.href = newUrl;
    }

    /**
     * 分页导航
     */
    public navigatePage(page: number): void {
        if (page < 1) return;

        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set('page', page.toString());
        window.location.href = currentUrl.toString();
    }

    /**
     * 切换统计面板
     */
    public toggleStats(): void {
        const statsPanel = document.getElementById('stats-panel');
        if (statsPanel) {
            statsPanel.classList.toggle('hidden');
        }
    }

    /**
     * 返回上一页
     */
    public goBack(): void {
        history.back();
    }

    /**
     * 显示交易详情
     */
    public showTransactionDetail(row: HTMLElement): void {
        const actionName = row.getAttribute('data-action-name') || '';
        const remark = row.getAttribute('data-remark') || '无';
        const money = row.getAttribute('data-money') || '';
        const isPositive = row.getAttribute('data-is-positive') === 'True';
        const leftMoney = row.getAttribute('data-left-money') || '';
        const operaNickname = row.getAttribute('data-opera-nickname') || '';
        const operaUserId = row.getAttribute('data-opera-userid') || '';
        const addTime = row.getAttribute('data-add-time') || '';

        const modalContent = `
            <div class="detail-modal-header">
                <h3 class="font-medium text-lg">交易详情</h3>
                <button class="w-6 h-6 flex items-center justify-center text-gray-500 hover:text-gray-700" onclick="closeTransactionDetail()">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            </div>
            <div class="detail-modal-content">
                <div class="flex justify-between items-start">
                    <span class="text-gray-500 text-sm">交易类型</span>
                    <span class="text-sm font-medium text-right">${actionName}</span>
                </div>
                <div class="flex justify-between items-start">
                    <span class="text-gray-500 text-sm">交易金额</span>
                    <span class="text-sm font-medium ${isPositive ? 'text-success' : 'text-danger'}">${isPositive ? '+' : ''}${money}</span>
                </div>
                <div class="flex justify-between items-start">
                    <span class="text-gray-500 text-sm">账户余额</span>
                    <span class="text-sm">${parseFloat(leftMoney).toLocaleString()}</span>
                </div>
                <div class="flex justify-between items-start">
                    <span class="text-gray-500 text-sm">操作人</span>
                    <span class="text-sm text-right">${operaNickname}</span>
                </div>
                <div class="flex justify-between items-start">
                    <span class="text-gray-500 text-sm">操作人ID</span>
                    <span class="text-sm text-right">${operaUserId}</span>
                </div>
                <div class="flex justify-between items-start">
                    <span class="text-gray-500 text-sm">交易时间</span>
                    <span class="text-sm text-right">${addTime}</span>
                </div>
                <div class="flex justify-between items-start">
                    <span class="text-gray-500 text-sm">备注</span>
                    <span class="text-sm text-right break-words max-w-48">${remark}</span>
                </div>
            </div>
            <div class="detail-modal-footer">
                <button class="h-9 px-4 bg-primary text-white rounded-md text-sm font-medium hover:bg-primary-dark transition-colors" onclick="closeTransactionDetail()">确定</button>
            </div>
        `;

        // 使用ModalService显示模态框
        ModalService.getInstance().showCustomModal({
            title: '',
            content: modalContent,
            showCloseButton: false,
            customClass: 'transaction-detail-modal',
            contentClass: 'detail-modal-container'
        });
    }

    /**
     * 关闭交易详情模态框
     */
    public closeTransactionDetail(): void {
        // 关闭最新的模态框
        const modals = document.querySelectorAll('.confirm-overlay');
        const latestModal = modals[modals.length - 1];
        if (latestModal) {
            latestModal.remove();
        }
    }

    /**
     * 清空所有记录
     */
    private async clearAllRecords(): Promise<void> {
        try {
            const confirmed = await ModalService.confirmDelete(
                '确定要删除所有会员的历史交易记录吗？<br/>此操作将保留本月记录，删除本月之前的所有记录。<br/>此操作不可恢复！'
            );
            
            if (confirmed) {
                // 构建清空URL
                const currentUrl = new URL(window.location.href);
                const siteid = currentUrl.searchParams.get('siteid') || '';
                const clearUrl = `/bbs/banklist.aspx?action=deleteall&siteid=${siteid}`;

                // 使用POST方法发送请求
                await AjaxService.post(clearUrl, null, {
                    showToast: true,
                    successMessage: '清空成功',
                    errorMessage: '清空失败，请重试',
                    onSuccess: () => {
                        // 清空成功，刷新页面
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    }
                });
            }
        } catch (error) {
            console.error('清空记录失败:', error);
        }
    }

    /**
     * 获取页面统计信息
     */
    public getPageStats(): { totalRecords: number; currentPage: number; totalPages: number } {
        const records = document.querySelectorAll('tbody tr');
        const paginationInfo = this.extractPaginationInfo();
        
        return {
            totalRecords: records.length,
            currentPage: paginationInfo?.currentPage || 1,
            totalPages: paginationInfo?.totalPages || 1
        };
    }
}

// ==================== 页面初始化 ====================

/**
 * 页面DOM加载完成后自动初始化
 */
document.addEventListener('DOMContentLoaded', () => {
    BankListPage.init();
});

// ==================== 全局函数，供模板调用（向后兼容） ====================

/**
 * 获取页面实例
 */
export function getBankListPage(): BankListPage {
    return BankListPage.getInstance();
}

// 导出默认类
export default BankListPage;
