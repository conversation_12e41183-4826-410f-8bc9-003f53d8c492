﻿# YaoHuo.Plugin

## 🚀 项目简介

YaoHuo.Plugin 是基于 ASP.NET Web Forms (.NET Framework 4.8) 的现代化社区论坛系统，支持WAP/WEB多端访问。项目采用**渐进式现代化改造**策略，实现新旧UI并存的双系统架构，集成论坛、勋章、游戏、银行等丰富功能。

### ✨ 核心特色
- 🔄 **渐进式现代化**：新旧UI平滑切换，降低升级风险
- 🎨 **双UI系统**：传统Web Forms + 现代Handlebars模板
- 🛡️ **安全优先**：DapperHelper参数化查询，全面防护SQL注入
- ⚡ **性能优化**：多层缓存架构、数据库RCSI优化
- 🛠️ **现代工具链**：Tailwind CSS + Node.js构建 + CSS质量监控

---

## 🏗️ 现代化架构

```mermaid
graph TB
    subgraph "双UI系统"
        UI1[传统Web Forms UI]
        UI2[现代Handlebars模板]
        UI3[用户可切换]
    end

    subgraph "前端技术栈"
        F1[Tailwind CSS 4.1.8]
        F2[Handlebars.Net 2.1.6]
        F3[Node.js构建工具]
        F4[CSS质量监控]
    end

    subgraph "后端服务层"
        B1[TemplateService]
        B2[DapperHelper]
        B3[UserInfoCacheService]
        B4[ConfigService]
        B5[OptimizedTransactionHelper]
    end

    subgraph "数据访问层"
        D1[KeLin.ClassManager.BLL/DAL]
        D2[Dapper 2.1.66]
        D3[SQL Server 2022]
        D4[RCSI + 索引优化]
    end

    UI1 --> B1
    UI2 --> B1
    UI3 -.-> UI1
    UI3 -.-> UI2

    F1 --> UI2
    F2 --> UI2
    F3 --> F1
    F4 --> F1

    B1 --> B2
    B2 --> D2
    B3 --> D2
    B4 --> D2
    B5 --> D2

    D2 --> D3
    D1 --> D3
    D4 --> D3
```

## 📁 项目结构

```
YaoHuo.Plugin/
├── 🎨 Template/                    # Handlebars模板系统
│   ├── Layouts/MainLayout.hbs      # 主布局模板
│   ├── Pages/                      # 页面模板 (14个)
│   ├── Partials/                   # 可复用组件
│   └── CSS/output.css              # Tailwind构建输出
├── 🛠️ build-tools/                 # 前端构建工具
│   ├── package.json                # Node.js依赖配置
│   ├── tailwind.config.js          # Tailwind CSS配置
│   ├── style.css                   # 样式源文件
│   └── shell-tools/                # CSS质量监控工具
├── 🏛️ BBS/                         # 论坛核心模块
├── 👑 Admin/                       # 后台管理系统
├── 🎮 Games/                       # 游戏娱乐模块
├── 🏅 XinZhang/                    # 勋章系统
├── 🔐 OAuth/                       # OAuth认证系统
├── 🔧 WebSite/                     # 核心服务层
│   ├── Services/                   # 现代化业务服务
│   ├── Tool/                       # 工具类库
│   └── Models/                     # 数据模型
├── 📚 Documentation/               # 完整技术文档
│   ├── 开发文档/                   # 开发指南与规范
│   ├── 技术文档/                   # 架构设计文档
│   └── 安全文档/                   # 安全实践指南
└── 📋 .cursor/rules/               # Cursor开发规则文档
```

---

## 🎯 核心功能

### 🗣️ 社区功能
- **论坛系统**：发帖回帖、点赞打赏、投票竞猜、黑名单管理
- **消息中心**：私信系统、系统通知、一键阅读
- **好友系统**：好友管理、空间访问、互动记录
- **勋章系统**：勋章展示、购买过滤、VIP身份标识

### 💰 经济系统
- **虚拟货币**：安全转账、打赏机制、手续费管理
- **银行系统**：存取款功能、利息计算
- **充值系统**：多种支付方式集成

### 🎮 娱乐功能
- **小游戏**：吹牛游戏、聊天室互动
- **相册系统**：图片上传、相册管理
- **排行榜**：多维度用户排名展示

### 🛡️ 安全与管理
- **SQL安全**：DapperHelper参数化查询，全面防护SQL注入
- **权限控制**：多级权限验证，精细化访问控制
- **内容审核**：自动化和人工审核机制
- **OAuth认证**：标准OAuth 2.0认证系统

---

## 💻 技术栈

### 🔧 后端技术
- **.NET Framework 4.8** + **C# 7+**
- **ASP.NET Web Forms** (传统三层架构)
- **SQL Server 2022** (兼容级别160)
- **Dapper 2.1.66** (ORM框架)
- **Microsoft.CodeDom.Providers.DotNetCompilerPlatform** (Roslyn编译器)

### 🎨 前端技术
- **Handlebars.Net 2.1.6** (模板引擎)
- **Tailwind CSS 4.1.8** (样式框架)
- **Node.js + PostCSS + Autoprefixer** (构建工具)
- **原生JavaScript** + **Vue.js 2.6.10**
- **响应式设计** (支持WAP/WEB多端)

### 🛠️ 现代化服务
- **DapperHelper** - 安全的参数化数据访问封装
- **TemplateService** - Handlebars模板渲染服务
- **UserInfoCacheService** - 用户信息缓存管理
- **ConfigService** - JSON配置文件管理
- **OptimizedTransactionHelper** - 优化的数据库事务处理

### 📦 核心依赖
- **KeLin.ClassManager** - 自定义业务逻辑层
- **Dapper 2.1.66** - 轻量级ORM框架
- **Handlebars.Net 2.1.6** - 模板引擎
- **Newtonsoft.Json** - JSON数据处理

---

## 🔄 渐进式现代化改造

### 🎨 双UI系统架构
项目采用**新旧UI并存**的渐进式重构策略：

- **🔧 传统UI**：ASP.NET Web Forms + 内联样式
- **✨ 现代UI**：Handlebars模板 + Tailwind CSS + 响应式设计
- **🔀 平滑切换**：用户可在新旧界面间自由切换
- **📈 渐进迁移**：新功能优先采用现代技术栈

### 📊 现代化成果
| 模块 | 状态 | 技术栈 | 说明 |
|------|------|--------|------|
| **模板系统** | ✅ 完成 | Handlebars.Net | 14个页面模板 |
| **样式系统** | ✅ 完成 | Tailwind CSS | 响应式设计 |
| **构建工具** | ✅ 完成 | Node.js + PostCSS | 自动化构建 |
| **数据访问** | ✅ 完成 | DapperHelper | 参数化查询 |
| **缓存系统** | ✅ 完成 | 多层缓存架构 | 性能优化 |
| **OAuth认证** | ✅ 完成 | OAuth 2.0 | 标准认证 |

### 🛡️ 安全改造成果
- **SQL注入防护**：DapperHelper全面参数化查询
- **XSS防护**：严格的输入验证和输出编码
- **权限控制**：多级权限验证机制
- **OAuth认证**：标准OAuth 2.0认证系统

---

## 🚀 快速开始

### 📋 环境要求
- **Visual Studio 2022** (推荐)
- **.NET Framework 4.8**
- **SQL Server 2022** (兼容级别160)
- **Node.js 16+** (前端构建)

### 🛠️ 开发环境设置

#### 1. 克隆项目
```bash
git clone <repository-url>
cd yaohuo
```

#### 2. 数据库配置
```xml
<!-- Web.config -->
<connectionStrings>
    <add name="KeLin" connectionString="..." />
</connectionStrings>
<appSettings>
    <add key="KL_DatabaseName" value="NETOK" />
</appSettings>
```

#### 3. 前端构建工具设置
```bash
# 进入构建工具目录
cd YaoHuo.Plugin/build-tools

# 安装依赖
npm install

# 开发模式 (监听文件变化)
npm run watch:tailwind

# 生产构建 (压缩优化)
npm run build:tailwind
```

#### 4. 启动项目
- 在Visual Studio中打开 `YaoHuo.Plugin.sln`
- 设置启动项目并运行
- 访问 `http://localhost:port` 查看效果

---

## 💡 开发示例

### 🔧 现代化数据访问 (DapperHelper)
```csharp
// ✅ 推荐：使用DapperHelper进行安全的参数化查询
public class UserService
{
    private readonly string connectionString = PubConstant.GetConnectionString("KeLin");

    // 查询用户信息
    public UserModel GetUser(int userId)
    {
        return DapperHelper.QueryFirstOrDefault<UserModel>(
            connectionString,
            "SELECT * FROM users WHERE id = @id",
            new { id = userId }
        );
    }

    // 更新用户信息
    public bool UpdateUser(UserModel user)
    {
        var rowsAffected = DapperHelper.Execute(
            connectionString,
            "UPDATE users SET nickname = @nickname, email = @email WHERE id = @id",
            new { nickname = user.Nickname, email = user.Email, id = user.Id }
        );
        return rowsAffected > 0;
    }
}
```

### 🎨 Handlebars模板集成
```csharp
// ✅ 现代化页面渲染
public partial class MyFile : MyPageWap
{
    private void RenderWithHandlebars()
    {
        try
        {
            // 构建页面数据模型
            var pageModel = new MyFilePageModel
            {
                PageTitle = "我的地盘",
                UserInfo = GetCurrentUserInfo(),
                Statistics = GetUserStatistics()
            };

            // 使用TemplateService渲染页面
            string finalHtml = TemplateService.RenderPageWithLayout(
                "~/Template/Pages/MyFile.hbs",
                pageModel,
                pageModel.PageTitle,
                new HeaderOptionsModel { ShowViewModeToggle = false }
            );

            // 输出渲染结果
            Response.Clear();
            Response.ContentType = "text/html; charset=utf-8";
            Response.Write(finalHtml);
            Response.End();
        }
        catch (System.Threading.ThreadAbortException)
        {
            throw; // Response.End()的正常行为
        }
        catch (Exception ex)
        {
            // 错误处理，回退到旧版UI
            System.Diagnostics.Debug.WriteLine($"模板渲染失败: {ex.Message}");
        }
    }
}
```

### 🎯 前端构建工具使用
```bash
# CSS质量监控
cd YaoHuo.Plugin/build-tools/shell-tools
node css-quality-monitor.js

# 重复代码检测
./duplicate-detector.sh

# CSS使用情况分析
./css-usage-analyzer.sh
```

---

## 📚 技术文档

### 📖 核心文档
- **[Handlebars集成参考手册](Documentation/技术文档/Handlebars集成参考手册.md)** - 模板系统集成指南
- **[前端UI设计与开发规范](Documentation/开发文档/前端UI设计与开发规范.md)** - UI设计规范
- **[JSON配置系统技术手册](Documentation/技术文档/JSON配置系统技术手册.md)** - 配置管理指南

### 🛠️ 开发指南
- **[Handlebars新页面开发清单](Documentation/开发文档/Handlebars新页面开发清单.md)** - 页面开发流程
- **[CSS优化工具使用指南](Documentation/开发文档/CSS优化工具使用指南.md)** - 代码质量工具

### 🔐 安全文档
- **[Dapper安全实践](Documentation/安全文档/Dapper_Security_Practices.md)** - 数据访问安全
- **[SQL注入防护指南](Documentation/安全文档/OWASP_SQL_Injection_Cheat_Sheet_CN.md)** - 安全最佳实践

---

## 🏆 项目特色

### 🔒 安全性
- **SQL注入防护**：DapperHelper全面参数化查询
- **XSS防护**：严格的输入验证和输出编码
- **OAuth认证**：标准OAuth 2.0认证系统
- **权限控制**：多级权限验证机制

### ⚡ 性能优化
- **多层缓存**：用户信息、配置、查询结果缓存
- **数据库优化**：RCSI、索引优化、事务优化
- **前端优化**：Tailwind CSS压缩、响应式设计
- **异步处理**：批量操作、优化事务处理

### 🛠️ 可维护性
- **现代化技术栈**：Handlebars + Tailwind + DapperHelper
- **完整文档体系**：技术文档、开发规范、安全指南
- **代码质量工具**：CSS监控、重复检测、使用分析
- **渐进式重构**：平滑的技术升级路径

---

## 🤝 贡献指南

### 📝 开发规范
- 遵循项目编码规范和命名约定
- 新功能优先使用现代化技术栈（Handlebars + Tailwind + DapperHelper）
- 确保代码安全性，必须使用参数化查询
- 添加适当的文档和注释
- 进行充分的功能测试验证

### 🔧 贡献流程
1. Fork项目到您的账户
2. 创建功能分支 (`git checkout -b feature/NewFeature`)
3. 提交更改 (`git commit -m 'Add NewFeature'`)
4. 推送到分支 (`git push origin feature/NewFeature`)
5. 创建Pull Request

---

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

---

*最后更新: 2025年06月*