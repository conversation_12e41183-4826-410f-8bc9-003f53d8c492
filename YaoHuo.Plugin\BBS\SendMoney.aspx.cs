﻿using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using System;
using Dapper;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class SendMoney : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string reid = "";

        public string page = "";

        public string lpage = "";

        public string ot = "";

        public string INFO = "";

        public string ERROR = "";

        public wap_bbsre_Model bbsReVo = null;

        public wap_bbs_Model bbsVo = null;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
            }
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            reid = GetRequestValue("reid");
            page = GetRequestValue("page");
            lpage = GetRequestValue("lpage");
            ot = GetRequestValue("ot");
            IsLogin(userid, GetUrlQueryString());
            if (!(action == "gomod"))
            {
                return;
            }
            try
            {
                string requestValue = GetRequestValue("sendmoney");
                if (!WapTool.IsNumeric(requestValue))
                {
                    INFO = "NULL";
                    return;
                }
                if (long.Parse(requestValue) < 1L)
                {
                    INFO = "NULL";
                    return;
                }
                wap_bbsre_BLL wap_bbsre_BLL = new wap_bbsre_BLL(a);
                bbsReVo = wap_bbsre_BLL.GetModel(long.Parse(reid));
                wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(a);
                bbsVo = wap_bbs_BLL.GetModel(long.Parse(id));
                long hasMoney = bbsVo.hasMoney;
                long sendMoney = bbsVo.sendMoney;
                long num = long.Parse(requestValue);
                if (bbsReVo.bookid != bbsVo.id)
                {
                    INFO = "ERR";
                }
                else if (bbsVo.book_pub != userid)
                {
                    INFO = "ERR";
                }
                else if (bbsVo.book_pub == bbsReVo.userid.ToString())
                {
                    INFO = "ERR";
                }
                else if (num + hasMoney <= sendMoney && sendMoney > 0L)
                {
                    if (bbsReVo.bookid == bbsVo.id && num != 0L && userid == bbsVo.book_pub && bbsVo.book_pub != bbsReVo.userid.ToString())
                    {
                        // ✅ 使用TransactionHelper进行安全的事务性资金操作
                        string connectionString = PubConstant.GetConnectionString(a);

                        TransactionHelper.ExecuteMoneyTransaction(connectionString, (connection, transaction) =>
                        {
                            // 1. 更新帖子已发放金额
                            string updateBbsSql = "UPDATE [wap_bbs] SET hasMoney = @NewHasMoney WHERE id = @PostId";
                            connection.Execute(updateBbsSql, new {
                                NewHasMoney = hasMoney + num,
                                PostId = bbsVo.id
                            }, transaction);

                            // 2. 更新回复获得金额
                            string updateReplySql = "UPDATE [wap_bbsre] SET myGetMoney = @NewGetMoney WHERE id = @ReplyId";
                            connection.Execute(updateReplySql, new {
                                NewGetMoney = num + bbsReVo.myGetMoney,
                                ReplyId = bbsReVo.id
                            }, transaction);

                            // 3. 给用户加钱
                            string updateUserSql = "UPDATE [user] SET money = money + @Amount WHERE userid = @UserId";
                            connection.Execute(updateUserSql, new {
                                Amount = num,
                                UserId = bbsReVo.userid
                            }, transaction);

                            // 4. 发送系统消息
                            string messageTitle = "您得到赏分:" + num + "喽~";
                            string messageContent = "时间：" + DateTime.Now + "[br]点击查看:[url=/bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + bbsVo.id + "]" + bbsVo.book_title.Replace("[", "［").Replace("]", "］") + "[/url]";

                            string insertMessageSql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                                                       VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, 1)";
                            connection.Execute(insertMessageSql, new {
                                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                                UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                                Nickname = DapperHelper.LimitLength(nickname, 50),
                                Title = DapperHelper.LimitLength(messageTitle, 100),
                                Content = DapperHelper.LimitLength(messageContent, 500),
                                ToUserId = bbsReVo.userid
                            }, transaction);
                        });

                        // ✅ 先获取用户当前余额，避免SaveBankLog中的SELECT操作导致死锁
                        string bankLogConnectionString = PubConstant.GetConnectionString(a);
                        string getUserMoneySql = "SELECT money FROM [user] WHERE userid = @UserId AND siteid = @SiteId";
                        long currentMoney = DapperHelper.ExecuteScalar<long>(bankLogConnectionString, getUserMoneySql, new {
                            UserId = bbsReVo.userid,
                            SiteId = DapperHelper.SafeParseLong(siteid, "站点ID")
                        });
                        long newBalance = currentMoney; // 用户余额已在事务中更新，这里记录更新后的余额

                        // ✅ 使用SaveBankLogWithBalance替换SaveBankLog，避免死锁
                        SaveBankLogWithBalance(bbsReVo.userid.ToString(), "论坛赏分", num.ToString(), userid, nickname, "得到帖子赏分[" + bbsVo.id + "]", newBalance);
                        INFO = "OK";
                        WapTool.ClearDataBBSRe("bbsRe" + siteid + id);
                    }
                    else
                    {
                        INFO = "ERR";
                    }
                }
                else
                {
                    INFO = "ERR";
                }
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}