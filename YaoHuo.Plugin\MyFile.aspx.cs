﻿using KeLin.ClassManager;
using System;
using System.Linq;
using System.Web;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.BBS.Models;

namespace YaoHuo.Plugin
{
    public class MyFile : MyPageWap
    {
        public string INFO = "";

        public string ERROR = "";

        public string strHtml = "";

        public string strShowHtml = "";

        public string messagecount = "0";

        public string messageAll = "0";

        public string goodfriend = "0";

        public string type = "";

        /// <summary>
        /// 检查用户UI偏好并处理版本切换
        /// </summary>
        /// <returns>如果成功渲染新版则返回true，否则返回false</returns>
        private bool CheckAndHandleUIPreference()
        {
            string uiPreference = "";

            // 检查Cookie中的UI偏好
            if (Request.Cookies["ui_preference"] != null)
            {
                uiPreference = Request.Cookies["ui_preference"].Value;
            }

            // 默认使用旧版本
            if (string.IsNullOrEmpty(uiPreference))
            {
                uiPreference = "old";
            }

            // 如果偏好是新版本，尝试使用Handlebars模板
            if (uiPreference == "new")
            {
                return TryRenderWithHandlebars(); // 返回是否成功渲染新版
            }

            return false; // 使用旧版
        }

        /// <summary>
        /// 尝试使用Handlebars模板渲染页面
        /// </summary>
        /// <returns>如果成功渲染新版则返回true，否则返回false</returns>
        private bool TryRenderWithHandlebars()
        {
            try
            {
                // 检查是否存在TemplateService
                var templateServiceType = Type.GetType("YaoHuo.Plugin.WebSite.Tool.TemplateService, YaoHuo.Plugin");
                if (templateServiceType != null)
                {
                    // 使用反射调用TemplateService
                    var getViewModeMethod = templateServiceType.GetMethod("GetViewMode");
                    var renderPageMethod = templateServiceType.GetMethod("RenderPageWithLayout");

                    if (getViewModeMethod != null && renderPageMethod != null)
                    {
                        string viewMode = (string)getViewModeMethod.Invoke(null, null);
                        if (viewMode == "new")
                        {
                            RenderWithHandlebars();
                            return true; // 成功渲染新版
                        }
                    }
                }

                // 如果Handlebars不可用，记录错误但不回退
                ERROR = "Handlebars模板服务不可用";
                System.Diagnostics.Debug.WriteLine("Handlebars模板服务不可用，继续使用旧版");
                return false;
            }
            catch (System.Threading.ThreadAbortException)
            {
                return true;
            }
            catch (Exception ex)
            {
                // 记录错误但不回退到静态模板
                ERROR = "新版模板加载失败: " + WapTool.ErrorToString(ex.ToString());
                System.Diagnostics.Debug.WriteLine($"模板渲染错误: {ex.Message}，继续使用旧版");
                return false;
            }
        }

        /// <summary>
        /// 使用Handlebars模板渲染页面
        /// </summary>
        private void RenderWithHandlebars()
        {
            // 原有渲染逻辑的备份注释：
            // 旧的渲染方式使用了双重RenderPage调用：
            // 1. 先渲染页面主体 "~/Template/Pages/MyFile.hbs"
            // 2. 再将页面主体作为content传递给主布局 "~/Template/Layouts/MainLayout.hbs"
            // 这种方式已被新的RenderPageWithLayout统一方法替换

            try
            {
                // 1. 构建页面主体所需的数据模型
                var pageModel = BuildUserPageModel(); // 保持BuildUserPageModel方法不变

                // 2. 构建头部选项（业务逻辑在页面层处理）
                var headerOptions = BuildHeaderOptions();
                headerOptions.ShowViewModeToggle = true;

                // 3. 调用新的 RenderPageWithLayout 方法
                string finalHtml = TemplateService.RenderPageWithLayout(
                    "~/Template/Pages/MyFile.hbs",    // 页面模板路径
                    pageModel,                         // 页面数据模型
                    "个人中心",                        // 页面标题
                    headerOptions                     // 头部选项，包含消息通知状态
                );

                // 3. 输出渲染结果
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write(finalHtml);
                Response.End(); // 使用Response.End()确保页面执行完全终止
            }
            catch (System.Threading.ThreadAbortException)
            {
                // ThreadAbortException 是 Response.End() 的正常行为，不需要处理
                // 直接重新抛出，让它正常终止线程
                throw;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Handlebars 渲染失败 (MyFile.aspx): {ex.ToString()}");
                // 记录错误但不回退到旧版，让新的TemplateService返回错误HTML信息
                ERROR = "新版界面加载失败: " + WapTool.ErrorToString(ex.ToString());
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}。请联系管理员。</div>");
                HttpContext.Current.ApplicationInstance.CompleteRequest(); // 在异常处理中使用CompleteRequest避免嵌套异常
            }
        }

        /// <summary>
        /// 构建用户页面数据模型
        /// </summary>
        private MyFilePageModel BuildUserPageModel()
        {
            var model = new MyFilePageModel();

            // 设置用户基本信息
            model.UserInfo.UserId = userVo.userid.ToString();
            model.UserInfo.Nickname = userVo.nickname ?? "用户";
            model.UserInfo.DisplayName = FilterHtmlTags(WapTool.GetColorNickName(userVo.idname, userVo.nickname, lang, ver));
            model.UserInfo.Level = WapTool.GetLevl(siteVo.lvlNumer, userVo.expr, userVo.money, type);
            model.UserInfo.Identity = FilterHtmlTags(WapTool.GetMyID(userVo.idname, lang));
            model.UserInfo.IdentityHtml = WapTool.GetMyID(userVo.idname, lang);
            model.UserInfo.EndTime = ProcessEndTime();
            model.UserInfo.Experience = userVo.expr;
            model.UserInfo.IsVip = IsVipUser();
            model.UserInfo.HasEndTime = HasValidEndTime();

            // 设置统计信息
            model.Statistics.MessageCount = messagecount;
            model.Statistics.MessageAll = messageAll;
            model.Statistics.FriendCount = goodfriend;
            model.Statistics.PostCount = userVo.bbsCount.ToString();
            model.Statistics.ReplyCount = userVo.bbsReCount.ToString();
            model.Statistics.MessageDisplay = messagecount + "/" + messageAll;

            // 设置资产信息
            model.Assets.Money = userVo.money;
            model.Assets.BankMoney = userVo.myBankMoney;
            model.Assets.RMB = userVo.RMB;
            model.Assets.MoneyName = WapTool.GetSiteMoneyName(siteVo.sitemoneyname, lang);
            model.Assets.MoneyDisplay = userVo.money.ToString("N0");
            model.Assets.BankDisplay = userVo.myBankMoney.ToString("N0");
            model.Assets.RMBDisplay = userVo.RMB.ToString("f2");
            model.Assets.HasBankMoney = userVo.myBankMoney > 0;
            model.Assets.HasRMB = userVo.RMB > 0;

            // 设置权限信息
            model.Permissions.ManagerLevel = userVo.managerlvl;
            model.Permissions.AdminDisplay = WapTool.GetIDName(siteid, userid, userVo.managerlvl, lang);
            model.Permissions.IsAdmin = userVo.managerlvl == "00" || userVo.managerlvl == "01";
            model.Permissions.IsSuperAdmin = userVo.managerlvl == "00";
            model.Permissions.HasAdminPermission = userVo.managerlvl != "普通" && !string.IsNullOrEmpty(userVo.managerlvl);

            // 设置勋章信息
            string medalContent = GetMedalContent();
            model.Medals.MedalHtml = medalContent;
            model.Medals.HasMedals = !string.IsNullOrEmpty(medalContent);

            // 设置功能链接
            BuildLinksModel(model.Links);

            // 设置站点信息
            model.SiteInfo.SiteId = siteid.ToString();
            model.SiteInfo.HttpStart = http_start;
            model.SiteInfo.MoneyName = siteVo.sitemoneyname;

            return model;
        }

        /// <summary>
        /// 过滤HTML标签，提取纯文本
        /// </summary>
        private string FilterHtmlTags(string html)
        {
            if (string.IsNullOrEmpty(html))
                return string.Empty;

            // 移除所有HTML标签
            string result = System.Text.RegularExpressions.Regex.Replace(html, "<.*?>", string.Empty);

            return result;
        }

        /// <summary>
        /// 构建功能链接模型
        /// </summary>
        private void BuildLinksModel(MyFileLinksModel links)
        {
            links.MailboxUrl = http_start + "bbs/messagelist.aspx?types=0";
            links.FriendsUrl = http_start + "bbs/FriendList.aspx?friendtype=0";
            links.PostsUrl = http_start + "bbs/book_list.aspx?action=search&key=" + userid + "&type=pub";
            links.RepliesUrl = http_start + "bbs/book_re_my.aspx?touserid=" + userid;
            links.EditProfileUrl = http_start + "bbs/EditProfile.aspx";
            links.RechargeUrl = http_start + "bbs/RMBtoMoney.aspx";
            links.AccountDetailUrl = http_start + "bbs/banklist.aspx?key=" + userid;
            links.RMBRechargeUrl = http_start + "chinabank_wap/selbank_wap.aspx";
            links.RMBDetailUrl = http_start + "chinabank_wap/banklist.aspx?tositeid=" + siteid + "&touserid=" + userid;
            links.ApplyMedalUrl = "/bbs/medal.aspx";
            links.BuyMedalUrl = "/bbs/medal.aspx?type=buy";
            links.FavoritesUrl = http_start + "bbs/favlist.aspx";
            links.AlbumUrl = "/album/albumlist.aspx?touserid=" + userid;
            links.ClanUrl = http_start + "clan/main.aspx";
            links.BlacklistUrl = http_start + "bbs/FriendList.aspx?friendtype=1";
            links.AdminUrl = http_start + "admin/basesitemodifywml.aspx";
            links.SuperAdminUrl = http_start + "admin/basesitemodifywml00.aspx";
            links.LogoutUrl = http_start + "waplogout.aspx?isGO=OK";
            links.VipUrl = "/bbs/BuyGroup.aspx";
            links.ChangePasswordUrl = http_start + "bbs/ModifyPW.aspx";
            links.ChangeAvatarUrl = http_start + "bbs/ModifyHead.aspx";
            links.MySpaceUrl = "/bbs/userinfo.aspx?touserid=" + userid;
        }

        /// <summary>
        /// 处理有效期时间显示
        /// </summary>
        private string ProcessEndTime()
        {
            string endTimeDisplay = WapTool.showIDEndTime(userVo.siteid, userVo.userid, userVo.endTime, lang);

            // 提取日期部分
            if (endTimeDisplay.Contains("有效期至:"))
            {
                if (endTimeDisplay.Contains("无期限"))
                {
                    return "无期限";
                }

                // 使用正则表达式提取日期
                var match = System.Text.RegularExpressions.Regex.Match(endTimeDisplay, @"有效期至:(\d{4}-\d{2}-\d{2})");
                if (match.Success)
                {
                    return match.Groups[1].Value;
                }
            }

            return endTimeDisplay;
        }

        /// <summary>
        /// 判断是否为VIP用户
        /// </summary>
        private bool IsVipUser()
        {
            string identity = WapTool.GetMyID(userVo.idname, lang);

            // 检查身份是否包含VIP相关文本
            bool isVip = identity.Contains("VIP") || identity.Contains("vip");

            // 检查是否含有特定的昵称颜色类型
            if (!isVip && !string.IsNullOrEmpty(userVo.idname))
            {
                string[] vipTypes = new[] { "5", "6", "7", "8", "9", "10", "b1", "b2", "b3", "a1", "a2", "a3", "a4" };
                foreach (var vipType in vipTypes)
                {
                    if (userVo.idname.Contains(vipType))
                        return true;
                }
            }

            return isVip;
        }

        /// <summary>
        /// 判断是否有有效期
        /// </summary>
        private bool HasValidEndTime()
        {
            string endTimeDisplay = WapTool.showIDEndTime(userVo.siteid, userVo.userid, userVo.endTime, lang);
            return !endTimeDisplay.Contains("无期限") && endTimeDisplay.Contains("有效期至:");
        }



        /// <summary>
        /// ✅ 使用DapperHelper获取消息统计信息
        /// </summary>
        private void GetMessageStatistics(string connectionString)
        {
            try
            {
                string sql = @"SELECT
                                (SELECT COUNT(id) FROM wap_message WHERE siteid = @SiteId AND isnew = 1 AND touserid = @UserId) AS NewMessageCount,
                                (SELECT COUNT(id) FROM wap_message WHERE siteid = @SiteId AND isnew <> 2 AND touserid = @UserId) AS TotalMessageCount";

                var parameters = new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    UserId = DapperHelper.SafeParseLong(userid, "用户ID")
                };

                var result = DapperHelper.Query<dynamic>(connectionString, sql, parameters).FirstOrDefault();

                if (result != null)
                {
                    messagecount = result.NewMessageCount?.ToString() ?? "0";
                    messageAll = result.TotalMessageCount?.ToString() ?? "0";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取消息统计失败: {ex.Message}");
                messagecount = "0";
                messageAll = "0";
            }
        }

        /// <summary>
        /// ✅ 使用DapperHelper获取好友统计信息
        /// </summary>
        private void GetFriendStatistics(string connectionString)
        {
            try
            {
                string sql = "SELECT COUNT(id) FROM wap_friends WHERE friendtype = 0 AND userid = @UserId";

                var parameters = new {
                    UserId = DapperHelper.SafeParseLong(userid, "用户ID")
                };

                var result = DapperHelper.ExecuteScalar<long>(connectionString, sql, parameters);
                goodfriend = result.ToString();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取好友统计失败: {ex.Message}");
                goodfriend = "0";
            }
        }

        /// <summary>
        /// 获取勋章内容
        /// </summary>
        private string GetMedalContent()
        {
            try
            {
                // 使用WapTool.GetMedal获取勋章HTML
                string medalHtml = WapTool.GetMedal(userVo.userid.ToString(), userVo.moneyname,
                    WapTool.GetSiteDefault(siteVo.Version, 47), wmlVo);

                if (string.IsNullOrEmpty(medalHtml))
                {
                    return "";
                }

                // 直接返回原始勋章HTML，样式由Handlebars模板中的Tailwind类控制
                // 如果未来需要其他转换，可以在这里添加，但不再添加内联style
                return medalHtml;
            }
            catch (Exception)
            {
                // 如果获取失败，返回空字符串
                return "";
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            IsLogin(userid, "myfile.aspx?siteid=" + siteid);
            if (WapTool.GetArryString(siteVo.Version, '|', 54) == "1")
            {
                needPassWordToAdmin();
            }
            type = WapTool.GetSiteDefault(siteVo.Version, 27);

            try
            {
                strHtml = siteVo.siterowremark;
                var appString = PubConstant.GetAppString("InstanceName");
                var connectionString = PubConstant.GetConnectionString(appString);

                // ✅ 使用DapperHelper进行安全的消息统计查询
                GetMessageStatistics(connectionString);

                // ✅ 使用DapperHelper进行安全的好友统计查询
                GetFriendStatistics(connectionString);
                addMoneyToMyBank();

                // 在数据准备完成后检查UI偏好
                bool newVersionRendered = CheckAndHandleUIPreference();
                if (newVersionRendered)
                {
                    // 新版渲染成功，直接返回，不再执行后续的旧版代码
                    return;
                }
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
            VisiteCount("进入我的地盘。");
        }
    }
}