using System;

namespace YaoHuo.Plugin.BBS.Base.Helper
{
    /// <summary>
    /// 时间处理帮助类
    /// 提供统一的友好时间转换功能
    /// </summary>
    public static class TimeHelper
    {
        /// <summary>
        /// 将DateTime转换为友好时间显示
        /// </summary>
        /// <param name="dateTime">要转换的时间</param>
        /// <returns>友好时间字符串（如：刚刚、5分钟前、2小时前等）</returns>
        public static string ToFriendlyTime(DateTime dateTime)
        {
            DateTime now = DateTime.Now;
            TimeSpan timeSpan = now - dateTime;

            // 刚刚（小于1分钟）
            if (timeSpan.TotalMinutes < 1)
                return "刚刚";

            // 分钟前（小于1小时）
            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes}分钟前";

            // 小时前（小于1天）
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours}小时前";

            // 天前（小于1周）
            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays}天前";

            // 周前（小于1个月）
            if (timeSpan.TotalDays < 30)
                return $"{(int)(timeSpan.TotalDays / 7)}周前";

            // 个月前（小于1年）
            if (timeSpan.TotalDays < 365)
                return $"{(int)(timeSpan.TotalDays / 30)}个月前";

            // 年前（超过1年）
            return $"{(int)(timeSpan.TotalDays / 365)}年前";
        }

        /// <summary>
        /// 将DateTime转换为详细时间显示
        /// </summary>
        /// <param name="dateTime">要转换的时间</param>
        /// <param name="format">时间格式，默认为 "yyyy-MM-dd HH:mm:ss"</param>
        /// <returns>格式化的时间字符串</returns>
        public static string ToDetailTime(DateTime dateTime, string format = "yyyy-MM-dd HH:mm:ss")
        {
            return dateTime.ToString(format);
        }

        /// <summary>
        /// 将DateTime转换为短格式时间显示（不显示年份）
        /// </summary>
        /// <param name="dateTime">要转换的时间</param>
        /// <returns>短格式时间字符串（如：12-25 14:30）</returns>
        public static string ToShortTime(DateTime dateTime)
        {
            return dateTime.ToString("MM-dd HH:mm");
        }

        /// <summary>
        /// 计算两个时间之间的差异
        /// </summary>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <returns>时间差异信息</returns>
        public static TimeSpan GetTimeDifference(DateTime startTime, DateTime endTime)
        {
            return endTime - startTime;
        }

        /// <summary>
        /// 检查时间是否在指定范围内
        /// </summary>
        /// <param name="dateTime">要检查的时间</param>
        /// <param name="hours">小时范围</param>
        /// <returns>是否在范围内</returns>
        public static bool IsWithinHours(DateTime dateTime, int hours)
        {
            TimeSpan timeSpan = DateTime.Now - dateTime;
            return timeSpan.TotalHours <= hours;
        }

        /// <summary>
        /// 检查时间是否在今天
        /// </summary>
        /// <param name="dateTime">要检查的时间</param>
        /// <returns>是否在今天</returns>
        public static bool IsToday(DateTime dateTime)
        {
            return dateTime.Date == DateTime.Now.Date;
        }

        /// <summary>
        /// 检查时间是否在昨天
        /// </summary>
        /// <param name="dateTime">要检查的时间</param>
        /// <returns>是否在昨天</returns>
        public static bool IsYesterday(DateTime dateTime)
        {
            return dateTime.Date == DateTime.Now.Date.AddDays(-1);
        }

        /// <summary>
        /// 获取时间的开始（00:00:00）
        /// </summary>
        /// <param name="dateTime">原始时间</param>
        /// <returns>当天开始时间</returns>
        public static DateTime GetStartOfDay(DateTime dateTime)
        {
            return dateTime.Date;
        }

        /// <summary>
        /// 获取时间的结束（23:59:59）
        /// </summary>
        /// <param name="dateTime">原始时间</param>
        /// <returns>当天结束时间</returns>
        public static DateTime GetEndOfDay(DateTime dateTime)
        {
            return dateTime.Date.AddDays(1).AddTicks(-1);
        }

        /// <summary>
        /// 格式化注册时长显示
        /// </summary>
        /// <param name="registerTime">注册时间</param>
        /// <returns>注册时长的友好显示</returns>
        public static string FormatRegisterDuration(DateTime registerTime)
        {
            DateTime now = DateTime.Now;
            TimeSpan duration = now - registerTime;

            // 计算年、月、天
            int years = now.Year - registerTime.Year;
            int months = now.Month - registerTime.Month;
            int days = (int)duration.TotalDays;

            // 调整月份计算
            if (months < 0)
            {
                years--;
                months += 12;
            }

            // 如果注册时间的日期大于当前日期，需要调整月份
            if (now.Day < registerTime.Day)
            {
                months--;
                if (months < 0)
                {
                    years--;
                    months += 12;
                }
            }

            // 简化显示：只显示最大的时间单位
            if (years > 0)
                return $"{years}年";
            else if (months > 0)
                return $"{months}个月";
            else if (days > 0)
                return $"{days}天";
            else
                return "今天";
        }

        /// <summary>
        /// 获取Unix时间戳
        /// </summary>
        /// <param name="dateTime">要转换的时间</param>
        /// <returns>Unix时间戳</returns>
        public static long ToUnixTimestamp(DateTime dateTime)
        {
            return ((DateTimeOffset)dateTime).ToUnixTimeSeconds();
        }

        /// <summary>
        /// 从Unix时间戳转换为DateTime
        /// </summary>
        /// <param name="timestamp">Unix时间戳</param>
        /// <returns>DateTime对象</returns>
        public static DateTime FromUnixTimestamp(long timestamp)
        {
            return DateTimeOffset.FromUnixTimeSeconds(timestamp).DateTime;
        }
    }
}
