<!-- 消息通知图标组件 -->
<div class="header-icon notification-icon dropdown" id="message-notification" {{#unless InitialUnreadCount}}style="display:none;"{{/unless}}>
    <i data-lucide="bell" class="header-icon-size"></i>
    <span class="notification-badge" id="message-badge" style="display: none;">{{InitialUnreadCount}}</span>
    
    <!-- 消息预览下拉面板 -->
    <div class="notification-dropdown" id="message-dropdown">
        <div class="p-4 bg-gray-50 border-b">
            <div class="flex items-center justify-between">
                <h3 class="font-semibold text-gray-800 text-sm">消息通知</h3>
                <button class="text-xs text-primary hover:text-primary-dark transition-colors" id="mark-all-read">
                    全部已读
                </button>
            </div>
        </div>
        
        <div class="max-h-96 overflow-y-auto" id="message-list">
            <div class="p-8 text-center text-gray-500">
                <i data-lucide="loader" class="w-6 h-6 mx-auto mb-2 animate-spin"></i>
                <p class="text-sm">加载中...</p>
            </div>
        </div>
        
        <div class="p-3 bg-gray-50 border-t text-center">
            <a href="/bbs/messagelist.aspx?siteid={{siteid}}" class="text-sm text-primary hover:text-primary-dark transition-colors">
                查看更多消息
            </a>
        </div>
    </div>
</div> 