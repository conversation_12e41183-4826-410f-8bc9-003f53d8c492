.rectangle-container {
    max-width: 720px;
    position: relative;
}

.rectangle {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    right: 0;
    width: 30px;
    height: 43px;
    background: #378d8d;
    transform: scale(0);
    border-radius: 50%;
    color: white;
    opacity: 0;
    overflow: hidden;
    z-index: 1;
    -webkit-animation: scale-in 0.3s ease-out forwards, expand 0.35s 0.25s ease-out forwards, fade-out 1.5s 2.5s forwards;
    animation: scale-in 0.3s ease-out forwards, expand 0.35s 0.25s ease-out forwards, fade-out 1.5s 2.5s forwards;
}

.notification-text {
    display: flex;
    align-items: center;
    padding: 0 5px;
    font-family: "Roboto", sans-serif;
    font-size: 14px;
    -webkit-animation: fade-in 0.65s ease-in forwards;
    animation: fade-in 0.65s ease-in forwards;
    white-space: nowrap;
}

@-webkit-keyframes scale-in {
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes scale-in {
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@-webkit-keyframes expand {
    50% {
        width: 170px;
        border-radius: 6px;
    }
    100% {
        width: 170px;
        border-radius: 4px;
        box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 3px 3px -1px rgba(0, 0, 0, 0.12);
    }
}

@keyframes expand {
    50% {
        width: 170px;
        border-radius: 6px;
    }
    100% {
        width: 170px;
        border-radius: 4px;
        box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.2), 0px 1px 1px 0px rgba(0, 0, 0, 0.14), 0px 3px 3px -1px rgba(0, 0, 0, 0.12);
    }
}

@-webkit-keyframes fade-in {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 0.8;
    }
}

@keyframes fade-in {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 0.8;
    }
}

@-webkit-keyframes fade-out {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        display: none;
    }
}

@keyframes fade-out {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
        display: none;
    }
}

.material-icons svg {
    fill: white;
    vertical-align: middle;
}

.material-icons {
    line-height: 0;
}
