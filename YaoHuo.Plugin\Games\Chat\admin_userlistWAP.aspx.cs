﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.Games.Chat
{
    public class Admin_userlistWAP : MyPageWap
    {
        private string string_0 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string linkURL = "";

        public string linkTOP = "";

        public string condition = "";

        public string ERROR = "";

        public string key = "";

        public string gamecn = "";

        public List<wap2_games_chat_Model> listVo = null;

        public StringBuilder strhtml = new StringBuilder();

        public long kk = 1L;

        public long index = 0L;

        public long total = 0L;

        public long pageSize = 10L;

        public long CurrentPage = 1L;

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            IsCheckManagerLvl("|00|01|03|", "", GetUrlQueryString());
            switch (action)
            {
                case "gocheckall":
                    gocheckall();
                    break;
                case "gocheck":
                    gocheck();
                    break;
                case "godelall":
                    geDelall();
                    break;
                case "class":
                    showclass();
                    break;
                default:
                    showclass();
                    break;
            }
        }

        public void showclass()
        {
            key = GetRequestValue("key");
            gamecn = GetRequestValue("gamecn");
            if (classid == "0")
            {
                condition = " siteid=" + siteid;
                classVo.classid = 0L;
                classVo.position = "left";
                classVo.classname = "管理 所有聊天内容:" + key;
                classVo.siteimg = "NetImages/no.gif";
            }
            else
            {
                classVo.classname = "管理 " + classVo.classname + ":" + key;
                condition = " siteid=" + siteid;
            }
            if (key.Trim() != "")
            {
                condition = condition + " and content like '%" + key + "%' ";
            }
            if (gamecn.Trim() != "")
            {
                condition = condition + " and gamecn ='" + gamecn + "' ";
            }
            try
            {
                pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
                wap2_games_chat_BLL wap2_games_chat_BLL = new wap2_games_chat_BLL(string_0);
                if (GetRequestValue("getTotal") != "")
                {
                    total = long.Parse(GetRequestValue("getTotal"));
                }
                else
                {
                    total = wap2_games_chat_BLL.GetListCount(condition);
                }
                if (GetRequestValue("page") != "")
                {
                    CurrentPage = long.Parse(GetRequestValue("page"));
                }
                CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
                index = pageSize * (CurrentPage - 1L);
                linkURL = http_start + "games/chat/admin_userlistWAP.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;key=" + HttpUtility.UrlEncode(key) + "&amp;gamecn=" + HttpUtility.UrlEncode(gamecn) + "&amp;getTotal=" + total;
                linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, total, pageSize, CurrentPage, linkURL);
                linkURL = WapTool.GetPageLink(ver, lang, total, pageSize, CurrentPage, linkURL);
                if (CurrentPage != 1L || classVo.total == total)
                {
                }
                listVo = wap2_games_chat_BLL.GetListVo(pageSize, CurrentPage, condition, "*", "id", total, 1);
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }

        public void geDelall()
        {
            CheckManagerLvl("01", "", GetUrlQueryString());
            GetRequestValue("id");
            GetRequestValue("state");
            try
            {
                // ✅ 使用DapperHelper进行安全的聊天记录批量删除操作
                string connectionString = PubConstant.GetConnectionString(string_0);
                string deleteAllChatSql = "DELETE FROM wap2_games_chat WHERE siteid = @SiteId";
                DapperHelper.Execute(connectionString, deleteAllChatSql, new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID")
                });
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
            showclass();
        }

        public void gocheck()
        {
            CheckManagerLvl("01", "", GetUrlQueryString());
            if (userVo.managerlvl != "00" && siteVo.isCheckSite == 1L)
            {
                ShowTipInfo("超级管理员设置您网站内容需要审核，请联系超级管理员审核！", GetUrlQueryString().Replace("gocheck", "class"));
                return;
            }
            string requestValue = GetRequestValue("id");
            string requestValue2 = GetRequestValue("state");
            try
            {
                // ✅ 使用DapperHelper进行安全的聊天记录审核操作
                string connectionString = PubConstant.GetConnectionString(string_0);
                string updateCheckSql = "UPDATE wap2_games_chat SET ischeck = @IsCheck WHERE id = @Id AND siteid = @SiteId";
                DapperHelper.Execute(connectionString, updateCheckSql, new {
                    IsCheck = DapperHelper.SafeParseLong(requestValue2, "审核状态"),
                    Id = DapperHelper.SafeParseLong(requestValue, "聊天记录ID"),
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID")
                });
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
            showclass();
        }

        public void gocheckall()
        {
            CheckManagerLvl("01", "", GetUrlQueryString());
            if (userVo.managerlvl != "00" && siteVo.isCheckSite == 1L)
            {
                ShowTipInfo("超级管理员设置您网站内容需要审核，请联系超级管理员审核！", GetUrlQueryString().Replace("gocheckall", "class"));
                return;
            }
            string requestValue = GetRequestValue("state");
            try
            {
                // ✅ 使用DapperHelper进行安全的聊天记录批量审核操作
                string connectionString = PubConstant.GetConnectionString(string_0);
                string updateAllCheckSql = "UPDATE wap2_games_chat SET ischeck = @IsCheck WHERE ischeck <> @IsCheck AND siteid = @SiteId";
                DapperHelper.Execute(connectionString, updateAllCheckSql, new {
                    IsCheck = DapperHelper.SafeParseLong(requestValue, "审核状态"),
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID")
                });
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
            showclass();
        }
    }
}