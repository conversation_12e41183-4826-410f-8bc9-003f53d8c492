using System;
using System.Collections.Generic;

namespace YaoHuo.Plugin.Template.Models
{
    /// <summary>
    /// 用户空间页面数据模型
    /// </summary>
    public class UserInfoPageModel : BasePageModel
    {
        public UserInfoPageModel()
        {
            PageTitle = "用户空间";
        }

        /// <summary>
        /// 当前用户是否为管理员
        /// </summary>
        public bool IsAdmin { get; set; }

        /// <summary>
        /// 用户基本信息
        /// </summary>
        public UserBasicInfoModel UserInfo { get; set; } = new UserBasicInfoModel();

        /// <summary>
        /// 用户统计数据
        /// </summary>
        public UserStatsModel Stats { get; set; } = new UserStatsModel();

        /// <summary>
        /// 用户动态列表
        /// </summary>
        public List<UserDynamicModel> DynamicsList { get; set; } = new List<UserDynamicModel>();

        /// <summary>
        /// 留言列表
        /// </summary>
        public List<UserMessageModel> MessagesList { get; set; } = new List<UserMessageModel>();

        /// <summary>
        /// 勋章信息
        /// </summary>
        public UserSpaceMedalModel Medals { get; set; } = new UserSpaceMedalModel();

        /// <summary>
        /// 操作按钮配置
        /// </summary>
        public UserActionButtonsModel ActionButtons { get; set; } = new UserActionButtonsModel();

        /// <summary>
        /// 是否是查看自己的空间
        /// </summary>
        public bool IsOwnSpace { get; set; }

        /// <summary>
        /// 目标用户ID
        /// </summary>
        public string TargetUserId { get; set; }

        /// <summary>
        /// 空间访问统计
        /// </summary>
        public UserSpaceStatsModel SpaceStats { get; set; } = new UserSpaceStatsModel();

        /// <summary>
        /// 当前访问用户的头像URL（用于留言输入框显示）
        /// </summary>
        public string CurrentUserAvatarUrl { get; set; } = "";

        /// <summary>
        /// 当前访问用户是否为默认头像
        /// </summary>
        public bool CurrentUserIsDefaultAvatar { get; set; } = true;
    }

    /// <summary>
    /// 用户基本信息模型
    /// </summary>
    public class UserBasicInfoModel
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 昵称
        /// </summary>
        public string Nickname { get; set; }

        /// <summary>
        /// 显示昵称（带颜色等格式）
        /// </summary>
        public string DisplayNickname { get; set; }

        /// <summary>
        /// 头像URL
        /// </summary>
        public string AvatarUrl { get; set; }

        /// <summary>
        /// 是否为默认头像
        /// </summary>
        public bool IsDefaultAvatar { get; set; }

        /// <summary>
        /// 性别 (1=男, 0=女)
        /// </summary>
        public int Sex { get; set; }

        /// <summary>
        /// 性别显示文本
        /// </summary>
        public string SexDisplay { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// 是否在线
        /// </summary>
        public bool IsOnline { get; set; }

        /// <summary>
        /// 在线状态显示
        /// </summary>
        public string OnlineStatusDisplay { get; set; }

        /// <summary>
        /// 用户身份/权限
        /// </summary>
        public string IdentityType { get; set; }

        /// <summary>
        /// 个人签名/备注
        /// </summary>
        public string Remark { get; set; }

        /// <summary>
        /// 注册时间（占位符）
        /// </summary>
        public string RegisterTime { get; set; } = "暂未实现";

        /// <summary>
        /// 注册时长（占位符）
        /// </summary>
        public string RegisterDuration { get; set; } = "暂未实现";
    }

    /// <summary>
    /// 用户统计数据模型
    /// </summary>
    public class UserStatsModel
    {
        /// <summary>
        /// 妖晶数量
        /// </summary>
        public long Money { get; set; }

        /// <summary>
        /// 妖晶显示文本
        /// </summary>
        public string MoneyDisplay { get; set; }

        /// <summary>
        /// 经验值
        /// </summary>
        public long Experience { get; set; }

        /// <summary>
        /// 经验值显示文本
        /// </summary>
        public string ExperienceDisplay { get; set; }

        /// <summary>
        /// 等级
        /// </summary>
        public string Level { get; set; }

        /// <summary>
        /// 头衔
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 帖子数量
        /// </summary>
        public long PostCount { get; set; }

        /// <summary>
        /// 回复数量
        /// </summary>
        public long ReplyCount { get; set; }

        /// <summary>
        /// 帖子链接
        /// </summary>
        public string PostsUrl { get; set; }

        /// <summary>
        /// 回复链接
        /// </summary>
        public string RepliesUrl { get; set; }
    }

    /// <summary>
    /// 用户动态模型
    /// </summary>
    public class UserDynamicModel
    {
        /// <summary>
        /// 动态ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 动态内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 动态时间
        /// </summary>
        public DateTime OperTime { get; set; }

        /// <summary>
        /// 友好时间显示
        /// </summary>
        public string FriendlyTime { get; set; }

        /// <summary>
        /// 动态类型图标
        /// </summary>
        public string TypeIcon { get; set; } = "fas fa-circle";
    }

    /// <summary>
    /// 用户留言模型
    /// </summary>
    public class UserMessageModel
    {
        /// <summary>
        /// 留言ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 留言内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 留言者昵称
        /// </summary>
        public string AuthorNickname { get; set; }

        /// <summary>
        /// 留言者用户ID
        /// </summary>
        public string AuthorUserId { get; set; } = "";

        /// <summary>
        /// 留言者用户空间链接
        /// </summary>
        public string AuthorSpaceUrl { get; set; } = "";

        /// <summary>
        /// 留言者头像URL（占位符）
        /// </summary>
        public string AuthorAvatarUrl { get; set; } = "";

        /// <summary>
        /// 留言者是否为默认头像
        /// </summary>
        public bool IsDefaultAvatar { get; set; }

        /// <summary>
        /// 留言时间
        /// </summary>
        public DateTime AddTime { get; set; }

        /// <summary>
        /// 友好时间显示
        /// </summary>
        public string FriendlyTime { get; set; }

        /// <summary>
        /// 详细时间显示（用于tooltip）
        /// </summary>
        public string DetailTime { get; set; } = "";
    }

    /// <summary>
    /// 用户空间勋章模型
    /// </summary>
    public class UserSpaceMedalModel
    {
        /// <summary>
        /// 是否有勋章
        /// </summary>
        public bool HasMedals { get; set; }

        /// <summary>
        /// 勋章HTML内容
        /// </summary>
        public string MedalHtml { get; set; }

        /// <summary>
        /// 勋章数量
        /// </summary>
        public int MedalCount { get; set; }
    }

    /// <summary>
    /// 用户操作按钮配置模型
    /// </summary>
    public class UserActionButtonsModel
    {
        /// <summary>
        /// 发送私信URL
        /// </summary>
        public string SendMessageUrl { get; set; }

        /// <summary>
        /// 加为好友URL
        /// </summary>
        public string AddFriendUrl { get; set; }

        /// <summary>
        /// 加入黑名单URL
        /// </summary>
        public string AddBlacklistUrl { get; set; }

        /// <summary>
        /// 查看详细资料URL
        /// </summary>
        public string ViewDetailUrl { get; set; }

        /// <summary>
        /// 留言提交URL
        /// </summary>
        public string SubmitMessageUrl { get; set; }

        /// <summary>
        /// 查看更多动态URL
        /// </summary>
        public string ViewMoreDynamicsUrl { get; set; }

        /// <summary>
        /// 查看更多留言URL
        /// </summary>
        public string ViewMoreMessagesUrl { get; set; }
    }

    /// <summary>
    /// 空间访问统计模型
    /// </summary>
    public class UserSpaceStatsModel
    {
        /// <summary>
        /// 总访问量
        /// </summary>
        public long TotalVisits { get; set; }

        /// <summary>
        /// 今日访问量
        /// </summary>
        public long TodayVisits { get; set; }

        /// <summary>
        /// 访问统计显示文本
        /// </summary>
        public string VisitStatsDisplay { get; set; }
    }
}
