using System.Collections.Generic;

namespace YaoHuo.Plugin.WebSite.Services.Config.Models
{
    /// <summary>
    /// 勋章配置根模型
    /// </summary>
    public class MedalConfigRoot
    {
        /// <summary>
        /// 配置元数据
        /// </summary>
        public ConfigMetadata Config { get; set; } = new ConfigMetadata();

        /// <summary>
        /// 申请勋章列表
        /// </summary>
        public List<ApplyMedalConfig> ApplyMedals { get; set; } = new List<ApplyMedalConfig>();

        /// <summary>
        /// 购买勋章列表
        /// </summary>
        public List<PurchaseMedalConfig> PurchaseMedals { get; set; } = new List<PurchaseMedalConfig>();
    }

    /// <summary>
    /// 申请勋章配置
    /// </summary>
    public class ApplyMedalConfig
    {
        /// <summary>
        /// 勋章唯一标识
        /// </summary>
        public string Id { get; set; } = "";

        /// <summary>
        /// 勋章名称
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// 勋章文件名（用于比对）
        /// </summary>
        public string FileName { get; set; } = "";

        /// <summary>
        /// 勋章图标URL
        /// </summary>
        public string IconUrl { get; set; } = "";

        /// <summary>
        /// 勋章描述（详细版，用于新版UI）
        /// </summary>
        public string Description { get; set; } = "";

        /// <summary>
        /// 勋章简短描述（用于旧版UI）
        /// </summary>
        public string ShortDescription { get; set; } = "";

        /// <summary>
        /// 勋章分类
        /// </summary>
        public string Category { get; set; } = "";

        /// <summary>
        /// 申请条件链接
        /// </summary>
        public string ConditionUrl { get; set; } = "";

        /// <summary>
        /// 申请条件链接文本（自定义链接显示文本）
        /// </summary>
        public string ConditionLinkText { get; set; } = "";

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; } = 0;

        /// <summary>
        /// 是否有条件链接
        /// </summary>
        public bool HasConditionUrl => !string.IsNullOrEmpty(ConditionUrl);
    }

    /// <summary>
    /// 购买勋章配置
    /// </summary>
    public class PurchaseMedalConfig
    {
        /// <summary>
        /// 勋章唯一标识
        /// </summary>
        public string Id { get; set; } = "";

        /// <summary>
        /// 勋章名称
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// 勋章文件名（用于比对）
        /// </summary>
        public string FileName { get; set; } = "";

        /// <summary>
        /// 勋章图标URL
        /// </summary>
        public string IconUrl { get; set; } = "";

        /// <summary>
        /// 勋章描述
        /// </summary>
        public string Description { get; set; } = "";

        /// <summary>
        /// 勋章分类
        /// </summary>
        public string Category { get; set; } = "";

        /// <summary>
        /// 价格（妖晶）
        /// </summary>
        public string Price { get; set; } = "";

        /// <summary>
        /// 购买链接
        /// </summary>
        public string BuyUrl { get; set; } = "";

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 排序顺序
        /// </summary>
        public int SortOrder { get; set; } = 0;
    }
}