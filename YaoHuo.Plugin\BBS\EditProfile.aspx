﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="EditProfile.aspx.cs" Inherits="YaoHuo.Plugin.BBS.EditProfile" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    Response.Write(WapTool.showTop(this.GetLang("编辑个人资料|编辑个人资料|Edit Profile"), wmlVo));
    StringBuilder strhtml = new StringBuilder();
    strhtml.Append("<div class=\"title\">编辑个人资料</div>");
    
    // 显示错误信息
    if (!string.IsNullOrEmpty(ERROR))
    {
        strhtml.Append("<div class=\"tip\"><b>" + ERROR + "</b></div>");
    }
    
    // 显示成功或其他状态信息
    switch (INFO)
    {
        case "OK":
            strhtml.Append("<div class=\"tip\"><b>更新成功！</b></div>");
            break;
        case "NICK_NULL":
            strhtml.Append("<div class=\"tip\"><b>昵称不能为空！</b></div>");
            break;
        case "NICK_HASEXIST":
            strhtml.Append("<div class=\"tip\"><b>本网站中此昵称已存在！</b></div>");
            break;
        case "NICK_TOOFREQUENT":
            strhtml.Append("<div class=\"tip\"><b>每自然月只能修改1次昵称，请下个月再试！</b></div>");
            break;
        case "NICK_FORBIDDEN":
            strhtml.Append("<div class=\"tip\"><b>昵称包含禁用词，请重新输入！</b></div>");
            break;
    }
    
    strhtml.Append("<div class=\"content\">");
    strhtml.Append("<form name=\"f\" action=\"" + http_start + "bbs/EditProfile.aspx\" method=\"post\">");
    
    // 昵称字段
    strhtml.Append("<fieldset style=\"border:1px solid #ccc;margin:10px;padding:10px;\">");
    strhtml.Append("<legend style=\"color:#666;font-weight:bold;\">论坛信息</legend>");
    strhtml.Append("昵称 ");
    strhtml.Append("<input type=\"text\" maxlength=\"9\" name=\"tonickname\" class=\"txt\" style=\"width:60%;\" value=\"" + tonickname + "\"/><br/>");
    strhtml.Append("<small style=\"color:#666;\">(每月仅可修改1次)</small><br/>");
    
    // 个性签名字段 - 移除提示文本
    strhtml.Append("签名 ");
    strhtml.Append("<input type=\"text\" maxlength=\"15\" style=\"width:60%;\" name=\"remark\" class=\"txt\" value=\"" + userVo.remark + "\" /><br/>");
    strhtml.Append("</fieldset>");
    
    // 详细资料字段
    strhtml.Append("<fieldset style=\"border:1px solid #ccc;margin:10px;padding:10px;\">");
    strhtml.Append("<legend style=\"color:#666;font-weight:bold;\">个人资料</legend>");
    
    // 隐藏性别字段（保持原有逻辑）
    strhtml.Append("<select style=\"display:none;\" name=\"sex\"><option value=\"" + userVo.sex.ToString() + "\">" + userVo.sex.ToString() + "</option><option value=\"1\">1_男</option><option value=\"0\">0_女</option></select>");
    
    strhtml.Append("年龄 ");
    strhtml.Append("<input style=\"width:30%\" type=\"text\" name=\"age\" value=\"" + (userVo.age == 0 ? "" : userVo.age.ToString()) + "\" maxlength=\"2\" placeholder=\"如：25\" /><br/>");
    
    strhtml.Append("身高 ");
    strhtml.Append("<input style=\"width:30%\" type=\"text\" name=\"shenggao\" value=\"" + userVo.shenggao + "\" maxlength=\"3\" placeholder=\"如：175（单位cm）\" />");
    strhtml.Append("<br/>");
    
    strhtml.Append("体重 ");
    strhtml.Append("<input style=\"width:30%\" type=\"text\" name=\"tizhong\" value=\"" + userVo.tizhong + "\" maxlength=\"3\" placeholder=\"如：65（单位kg）\" />");
    strhtml.Append("<br/>");
    
    strhtml.Append("爱好 ");
    strhtml.Append("<input style=\"width:30%\" type=\"text\" name=\"aihao\" value=\"" + aihao + "\" maxlength=\"10\" placeholder=\"如：篮球\" /><br/>");
    
    strhtml.Append("职业 ");
    strhtml.Append("<input style=\"width:30%\" type=\"text\" name=\"zhiye\" value=\"" + userVo.zhiye + "\" maxlength=\"5\" placeholder=\"如：程序员\" /><br/>");
    
    strhtml.Append("城市 ");
    strhtml.Append("<input style=\"width:30%\" type=\"text\" name=\"city\" value=\"" + userVo.city + "\" maxlength=\"8\" placeholder=\"如：北京\" /><br/>");
    
    // 婚否字段 - 改为下拉选项
    strhtml.Append("婚否 ");
    strhtml.Append("<select style=\"width:30%\" name=\"fenfuo\">");
    string[] maritalStatus = new string[] {"", "未婚", "已婚"};
    foreach (string status in maritalStatus)
    {
        strhtml.Append("<option value=\"" + status + "\"" + (userVo.fenfuo == status ? " selected=\"selected\"" : "") + ">" + status + "</option>");
    }
    strhtml.Append("</select><br/>");
    
    // 星座字段 - 改为下拉选项
    strhtml.Append("星座 ");
    strhtml.Append("<select style=\"width:30%\" name=\"xingzuo\">");
    string[] zodiacSigns = new string[] {"", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座", "摩羯座", "水瓶座", "双鱼座"};
    foreach (string sign in zodiacSigns)
    {
        strhtml.Append("<option value=\"" + sign + "\"" + (userVo.xingzuo == sign ? " selected=\"selected\"" : "") + ">" + sign + "</option>");
    }
    strhtml.Append("</select><br/>");
    strhtml.Append("</fieldset>");
    
    // 联系信息字段
    strhtml.Append("<fieldset style=\"border:1px solid #ccc;margin:10px;padding:10px;\">");
    strhtml.Append("<legend style=\"color:#666;font-weight:bold;\">联系方式</legend>");
    
    strhtml.Append("手机 ");
    strhtml.Append("<input style=\"width:50%\" placeholder=\"非登录手机号\" type=\"tel\" name=\"mobile\" value=\"" + userVo.mobile + "\" maxlength=\"11\" pattern=\"[0-9]*\" /><br/>");
    
    strhtml.Append("邮箱 ");
    strhtml.Append("<input style=\"width:50%\" type=\"email\" name=\"email\" value=\"" + userVo.email + "\" maxlength=\"30\" placeholder=\"如：<EMAIL>\" /><br/>");
    
    strhtml.Append("QQ号 ");
    strhtml.Append("<input style=\"width:50%\" type=\"text\" name=\"qq\" value=\"" + qq + "\" maxlength=\"11\" pattern=\"[0-9]*\" placeholder=\"如：12345678\" /><br/>");
    strhtml.Append("</fieldset>");
    
    // 隐藏字段
    strhtml.Append("<input name=\"action\" type=\"hidden\" value=\"gomod\" />");
    strhtml.Append("<input name=\"siteid\" type=\"hidden\" value=\"" + this.siteid + "\" />");
    strhtml.Append("<input name=\"classid\" type=\"hidden\" value=\"" + this.classid + "\" />");
    strhtml.Append("<input name=\"backurl\" type=\"hidden\" value=\"" + this.backurl + "\" />");
    
    strhtml.Append("<input type=\"submit\" id=\"submit\" name=\"submit\" class=\"btn\" value=\"保存资料\" style=\"width:97%;padding:10px;font-size:16px;margin: 10px;\" /><br/>");
    strhtml.Append("</form>");
    strhtml.Append("</div>");
    
    string isWebHtml = this.ShowWEB_view(this.classid);
    strhtml.Append("<div class=\"btBox\"><div class=\"bt2\">");
    strhtml.Append("<a href=\"" + this.http_start + (backurl) + "\">返回上级</a> ");
    strhtml.Append("<a href=\"" + this.http_start + "\">" + this.GetLang("返回首页|返回首页|Back to index") + "</a> ");
    strhtml.Append("</div></div>");
    
    if (isWebHtml != "")
    {
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml, wmlVo).Replace("[view]", strhtml.ToString()));
        Response.End();
    }
    Response.Write(strhtml);
    Response.Write(WapTool.showDown(wmlVo));
%> 