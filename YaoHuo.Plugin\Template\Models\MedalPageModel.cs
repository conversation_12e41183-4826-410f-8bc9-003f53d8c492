using System.Collections.Generic;

namespace YaoHuo.Plugin.Template.Models
{
    /// <summary>
    /// 勋章页面数据模型
    /// </summary>
    public class MedalPageModel : BasePageModel
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MedalPageModel()
        {
            PageTitle = "荣誉勋章";
        }

        /// <summary>
        /// 用户勋章信息
        /// </summary>
        public UserMedalModel UserMedals { get; set; } = new UserMedalModel();

        /// <summary>
        /// 申请勋章列表
        /// </summary>
        public List<ApplyMedalModel> ApplyMedals { get; set; } = new List<ApplyMedalModel>();

        /// <summary>
        /// 购买勋章列表
        /// </summary>
        public List<PurchaseMedalModel> PurchaseMedals { get; set; } = new List<PurchaseMedalModel>();

        /// <summary>
        /// 当前页面类型（apply/buy）
        /// </summary>
        public string PageType { get; set; } = "apply";

        /// <summary>
        /// 是否为申请页面
        /// </summary>
        public bool IsApplyPage => PageType == "apply";

        /// <summary>
        /// 是否为购买页面
        /// </summary>
        public bool IsBuyPage => PageType == "buy";

        /// <summary>
        /// 问候语
        /// </summary>
        public string Greeting { get; set; } = "";

        /// <summary>
        /// 当前时间
        /// </summary>
        public string CurrentTime { get; set; } = "";

        /// <summary>
        /// 是否为体验模式（通过?new参数访问）
        /// </summary>
        public bool IsExperienceMode { get; set; } = false;
    }

    /// <summary>
    /// 用户勋章模型
    /// </summary>
    public class UserMedalModel
    {
        /// <summary>
        /// 是否有勋章
        /// </summary>
        public bool HasMedals { get; set; }

        /// <summary>
        /// 勋章HTML内容
        /// </summary>
        public string MedalHtml { get; set; } = "";

        /// <summary>
        /// 用户拥有的勋章文件名列表
        /// </summary>
        public List<string> OwnedMedalFiles { get; set; } = new List<string>();
    }

    /// <summary>
    /// 申请勋章模型
    /// </summary>
    public class ApplyMedalModel
    {
        /// <summary>
        /// 勋章名称
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// 勋章图标URL
        /// </summary>
        public string IconUrl { get; set; } = "";

        /// <summary>
        /// 勋章描述
        /// </summary>
        public string Description { get; set; } = "";

        /// <summary>
        /// 勋章分类
        /// </summary>
        public string Category { get; set; } = "";

        /// <summary>
        /// 申请条件链接
        /// </summary>
        public string ConditionUrl { get; set; } = "";

        /// <summary>
        /// 是否有条件链接
        /// </summary>
        public bool HasConditionUrl => !string.IsNullOrEmpty(ConditionUrl);

        /// <summary>
        /// 是否已拥有
        /// </summary>
        public bool IsOwned { get; set; } = false;

        /// <summary>
        /// 勋章文件名（用于比对）
        /// </summary>
        public string MedalFileName { get; set; } = "";
    }

    /// <summary>
    /// 购买勋章模型
    /// </summary>
    public class PurchaseMedalModel
    {
        /// <summary>
        /// 勋章名称
        /// </summary>
        public string Name { get; set; } = "";

        /// <summary>
        /// 勋章图标URL
        /// </summary>
        public string IconUrl { get; set; } = "";

        /// <summary>
        /// 勋章描述
        /// </summary>
        public string Description { get; set; } = "";

        /// <summary>
        /// 勋章分类
        /// </summary>
        public string Category { get; set; } = "";

        /// <summary>
        /// 价格（妖晶）
        /// </summary>
        public string Price { get; set; } = "";

        /// <summary>
        /// 购买链接
        /// </summary>
        public string BuyUrl { get; set; } = "";

        /// <summary>
        /// 是否已拥有
        /// </summary>
        public bool IsOwned { get; set; } = false;

        /// <summary>
        /// 勋章文件名（用于比对）
        /// </summary>
        public string MedalFileName { get; set; } = "";
    }
}
