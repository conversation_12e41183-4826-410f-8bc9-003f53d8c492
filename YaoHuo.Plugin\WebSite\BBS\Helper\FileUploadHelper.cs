using System;
using System.IO;
using System.Web;
using System.Collections.Generic;
using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.WebSite;
using System.Drawing;
using System.Drawing.Imaging;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.Tool
{
    /// <summary>
    /// 文件上传助手类
    /// </summary>
    public class FileUploadHelper
    {
        private readonly MyPageWap _page;
        private readonly string _instanceName;

        public FileUploadHelper(MyPageWap page)
        {
            _page = page;
            _instanceName = PubConstant.GetAppString("InstanceName");
        }

        /// <summary>
        /// 处理文件上传结果
        /// </summary>
        public class UploadResult
        {
            public string INFO { get; set; } = "";
            public List<wap2_attachment_Model> Attachments { get; set; } = new List<wap2_attachment_Model>();
        }

        /// <summary>
        /// 处理文件上传
        /// </summary>
        public UploadResult ProcessFileUpload(
            HttpFileCollection files,
            string[] fileInfos,
            string siteid,
            string userid,
            string uploadType,
            string siteUpFileType,
            string notDownAndUpload,
            string myspace,
            string sitespace,
            string maxFileSize,
            string isCheck,
            string bookWidth = "",
            string bookHeight = "",
            string addWatermark = "0",
            string watermarkPath = "",
            string watermarkFontSize = "0",
            string watermarkText = "",
            bool useLegacyFileName = false)
        {
            var result = new UploadResult();

            try
            {
                string uploadPath = "";
                string fileName = "";
                string fileExt = "";
                long fileSize = 0;
                long fileSizeKB = 0;
                string filePath = "";

                // 创建上传目录
                uploadPath = HttpContext.Current.Server.MapPath("upload/" + siteid + "/" + WapTool.GetDatePathString());
                if (!Directory.Exists(uploadPath))
                {
                    Directory.CreateDirectory(uploadPath);
                }

                System.Diagnostics.Debug.WriteLine("========== 开始文件上传流程 ==========");
                System.Diagnostics.Debug.WriteLine($"上传文件总数：{files.Count}");

                // 处理每个上传文件
                for (int i = 0; i < files.Count; i++)
                {
                    var attachment = new wap2_attachment_Model();
                    HttpPostedFile file = files[i];
                    FileInfo fileInfo = new FileInfo(file.FileName.ToLower());
                    string safeTitleOnly;

                    fileExt = fileInfo.Extension;

                    // 根据参数决定使用哪种文件命名方式
                    if (useLegacyFileName)
                    {
                        // 使用旧的命名方式
                        fileName = $"{userid}_{DateTime.Now.ToString("HHmmss")}{i}{fileExt}";
                        safeTitleOnly = Path.GetFileNameWithoutExtension(fileInfo.Name);
                    }
                    else
                    {
                        // 使用新的安全文件名
                        fileName = GenerateSafeFileName(userid, i, fileInfo.Name, out safeTitleOnly);
                    }

                    filePath = "upload/" + siteid + "/" + WapTool.GetDatePathString() + fileName;
                    fileSize = file.InputStream.Length;
                    fileSizeKB = fileSize / 1024;

                    // 首先检查文件类型
                    if (!siteUpFileType.ToLower().Contains(fileExt.Replace(".", "").ToLower()))
                    {
                        result.INFO = "EXTERR";
                        return result;
                    }

                    if (!WapTool.IsNotChinese(fileExt.Replace(".", "")))
                    {
                        result.INFO = "EXTERR";
                        return result;
                    }

                    if (notDownAndUpload.IndexOf(fileExt.Replace(".", "").ToLower()) >= 0)
                    {
                        result.INFO = "EXTERR";
                        return result;
                    }

                    bool isImage = (".jpg|.jpeg|.png").IndexOf(fileExt.ToLower()) >= 0 && IsValidImage(file);

                    // 如果是图片，先进行压缩处理
                    if (isImage && fileSize > 1048576) // 1MB
                    {
                        if (TryCompressAndSaveImage(file, uploadPath + fileName, fileSize))
                        {
                            // 更新文件大小信息
                            var compressedFileInfo = new FileInfo(uploadPath + fileName);
                            fileSize = compressedFileInfo.Length;
                            fileSizeKB = fileSize / 1024;
                        }
                    }

                    // 检查压缩后的文件大小
                    if (!isImage && fileSizeKB > Convert.ToInt64(maxFileSize))
                    {
                        result.INFO = "MAXFILE";
                        return result;
                    }

                    // 检查空间是否足够
                    long totalSpace = Convert.ToInt64(myspace) + fileSizeKB;
                    if (totalSpace > Convert.ToInt64(sitespace) * 1024L)
                    {
                        result.INFO = "NOTSPACE";
                        return result;
                    }

                    // 如果文件还没保存（非图片或不需要压缩的图片），现在保存
                    if (!File.Exists(uploadPath + fileName))
                    {
                        file.SaveAs(uploadPath + fileName);
                    }

                    // 更新用户空间使用量
                    UpdateUserSpace(siteid, userid, fileSizeKB);

                    // 记录上传日志
                    WapTool.SaveUploadFileToLog(siteid, userid, "1", uploadType, fileExt,
                        fileSizeKB.ToString(), "bbs/" + filePath, isCheck);

                    // 处理图片（缩放和水印）
                    if (isImage)
                    {
                        ProcessImage(uploadPath + fileName, bookWidth, bookHeight,
                            addWatermark, watermarkPath, watermarkFontSize,
                            watermarkText, HttpContext.Current.Request.ServerVariables["HTTP_HOST"]);
                    }

                    // 添加附件信息
                    attachment.book_content = fileInfos != null ? ToHtm(fileInfos[i]) : "";
                    attachment.book_title = ToHtm(safeTitleOnly);
                    attachment.book_ext = fileExt.Replace(".", "");
                    attachment.book_size = WapTool.ShowSizeInfo(fileSize);
                    attachment.book_file = filePath;
                    result.Attachments.Add(attachment);
                }

                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"文件上传错误: {ex.Message}\n{ex.StackTrace}");
                result.INFO = "ERROR";
                return result;
            }
        }

        private string GenerateSafeFileName(string userId, int index, string originalFileName, out string safeTitleOnly)
        {
            try
            {
                string extension = Path.GetExtension(originalFileName);
                string nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);

                System.Text.StringBuilder sb = new System.Text.StringBuilder();
                foreach (char c in nameWithoutExtension)
                {
                    if (char.IsLetterOrDigit(c) || c == '_' || (c >= 0x4E00 && c <= 0x9FFF))
                    {
                        sb.Append(c);
                    }
                }
                string safeFileName = sb.ToString();

                if (safeFileName.Length > 10)
                {
                    safeFileName = safeFileName.Substring(0, 10);
                }

                safeTitleOnly = safeFileName;
                string baseFileName = $"{userId}_{DateTime.Now.ToString("HHmmss")}{index}";

                return baseFileName + safeFileName + extension.ToLower();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"文件名处理错误: {ex.Message}");
                safeTitleOnly = "";
                return $"{userId}_{DateTime.Now.ToString("HHmmss")}{index}{Path.GetExtension(originalFileName).ToLower()}";
            }
        }

        private bool ValidateFile(string fileExt, string siteUpFileType,
            string notDownAndUpload, long fileSizeKB, string myspace,
            string sitespace, string maxFileSize, ref string error)
        {
            error = "";

            // 检查文件类型是否允许
            if (!siteUpFileType.ToLower().Contains(fileExt.Replace(".", "").ToLower()))
            {
                error = "EXTERR";
                return false;
            }

            if (!WapTool.IsNotChinese(fileExt.Replace(".", "")))
            {
                error = "EXTERR";
                return false;
            }

            if (notDownAndUpload.IndexOf(fileExt.Replace(".", "").ToLower()) >= 0)
            {
                error = "EXTERR";
                return false;
            }

            // 检查空间是否足够
            long totalSpace = Convert.ToInt64(myspace) + fileSizeKB;
            if (totalSpace > Convert.ToInt64(sitespace) * 1024L)
            {
                error = "NOTSPACE";
                return false;
            }

            // 对于图片文件，不检查单文件大小限制
            bool isImage = (".jpg|.jpeg|.png").IndexOf(fileExt.ToLower()) >= 0;
            if (!isImage && fileSizeKB > Convert.ToInt64(maxFileSize))
            {
                error = "MAXFILE";
                return false;
            }

            // 如果是图片且大于10MB，仍然需要限制（防止过大的图片）
            if (isImage && fileSizeKB > 10240) // 10MB = 10240KB
            {
                error = "MAXFILE";
                return false;
            }

            return true;
        }

        private void UpdateUserSpace(string siteid, string userid, long fileSizeKB)
        {
            try
            {
                // 计算新的总空间
                long totalSpace = Convert.ToInt64(_page.siteVo.myspace) + fileSizeKB;

                // ✅ 使用DapperHelper进行安全的参数化更新操作
                string connectionString = PubConstant.GetConnectionString(_instanceName);
                string updateSql = "UPDATE [user] SET myspace = @TotalSpace WHERE userid = @UserId";

                DapperHelper.Execute(connectionString, updateSql, new {
                    TotalSpace = totalSpace,
                    UserId = DapperHelper.SafeParseLong(siteid, "站点ID")
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新用户空间失败: {ex.Message}");
                // 记录错误但不影响文件上传流程
                throw new Exception($"更新用户空间失败: {ex.Message}", ex);
            }
        }

        private bool TryCompressAndSaveImage(HttpPostedFile file, string savePath, long originalSize)
        {
            try
            {
                file.InputStream.Position = 0;
                byte[] originalData = new byte[file.InputStream.Length];
                file.InputStream.Read(originalData, 0, originalData.Length);

                byte[] compressedData = MagickCompressor.CompressImage(originalData);
                if (compressedData != null && compressedData.Length < originalSize)
                {
                    File.WriteAllBytes(savePath, compressedData);
                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"图片压缩失败: {ex.Message}");
            }
            return false;
        }

        private void ProcessImage(string imagePath, string width, string height,
            string addWatermark, string watermarkPath, string watermarkFontSize,
            string watermarkText, string hostName)
        {
            if (width != "" && height != "")
            {
                WapTool.MakeThumbnail(imagePath, imagePath, int.Parse(width), int.Parse(height), "HW");
            }
            else if (width != "" && height == "")
            {
                WapTool.MakeThumbnail(imagePath, imagePath, int.Parse(width), 0, "W");
            }
            else if (width == "" && height != "")
            {
                WapTool.MakeThumbnail(imagePath, imagePath, 0, int.Parse(height), "H");
            }

            if (addWatermark == "1" && ".jpg|.jpeg|.png".IndexOf(Path.GetExtension(imagePath).ToLower()) >= 0)
            {
                if (!WapTool.IsNumeric(watermarkFontSize))
                {
                    watermarkFontSize = "0";
                }

                if (watermarkPath.StartsWith("/"))
                {
                    watermarkPath = HttpContext.Current.Server.MapPath(watermarkPath);
                }

                WapTool.AddWater(imagePath, imagePath, hostName, watermarkPath,
                    Convert.ToInt64(watermarkFontSize), watermarkText);
            }
        }

        private string ToHtm(string str)
        {
            return str.Replace("'", "''").Replace("<", "&lt;").Replace(">", "&gt;");
        }

        private bool IsValidImage(HttpPostedFile file)
        {
            try
            {
                // 先检查文件头部特征码
                byte[] fileHeader = new byte[8];
                file.InputStream.Position = 0;
                file.InputStream.Read(fileHeader, 0, fileHeader.Length);
                file.InputStream.Position = 0;

                // JPEG文件头: FF D8 FF
                if (fileHeader[0] == 0xFF && fileHeader[1] == 0xD8 && fileHeader[2] == 0xFF)
                {
                    return true;
                }

                // PNG文件头: 89 50 4E 47 0D 0A 1A 0A
                if (fileHeader[0] == 0x89 && fileHeader[1] == 0x50 && fileHeader[2] == 0x4E && fileHeader[3] == 0x47
                    && fileHeader[4] == 0x0D && fileHeader[5] == 0x0A && fileHeader[6] == 0x1A && fileHeader[7] == 0x0A)
                {
                    return true;
                }

                // 再尝试通过Image.FromStream验证
                using (var image = Image.FromStream(file.InputStream))
                {
                    // 验证图片格式
                    return image.RawFormat.Equals(ImageFormat.Jpeg) ||
                           image.RawFormat.Equals(ImageFormat.Png);
                }
            }
            catch
            {
                return false;
            }
            finally
            {
                // 确保文件流位置重置
                file.InputStream.Position = 0;
            }
        }
    }
}