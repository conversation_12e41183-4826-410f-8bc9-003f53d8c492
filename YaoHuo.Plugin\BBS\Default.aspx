﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Default" EnableViewState="false" %>
<%
Response.Write(@"<!DOCTYPE html>
<html lang=""zh-CN"">
<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0, user-scalable=no"">
    <title>妖火论坛</title>
    <link rel=""stylesheet"" href=""/NetCSS/CSS/BBS/Fonts/Tailwind.css"">
    <link rel=""stylesheet"" href=""/NetCSS/CSS/BBS/Fonts/Remixicon.css"">
    <link rel=""stylesheet"" href=""/NetCSS/CSS/BBS/Fonts/DefaultPage.css"">
</head>
<body class=""pb-16"">
    <div class=""main-container"">
        <!-- 顶部导航栏 -->
        <header class=""fixed top-0 w-full bg-white shadow-sm z-10 main-header"">
            <div class=""flex items-center justify-between px-4 py-3"">
                <a href=""/"" class=""text-2xl font-medium text-primary cursor-pointer noselect"">妖火</a>
                <a href=""/wapindex.aspx?classid=206"" class=""bg-primary text-white px-4 py-2 rounded-button flex items-center !rounded-button post-btn"">
                    <i class=""ri-edit-line ri-lg mr-1 w-5 h-5 flex items-center justify-center""></i>
                    <span>发帖</span>
                </a>
            </div>
        </header>

        <!-- 搜索栏 -->
        <div class=""search-container mt-4 px-4 pt-16 pb-2 bg-gray-50"">
            <form id=""scbar_form"" name=""fs"" class=""flex items-center bg-white rounded-lg shadow-sm border border-gray-200 relative group focus-within:border-primary hover:border-primary transition-colors"" method=""get"" action=""/search.aspx"" _lpchecked=""1"">
                <div id=""searchType"" class=""flex items-center pl-3 py-2.5 cursor-pointer noselect whitespace-nowrap min-w-[60px]"">
                    <span id=""currentSearchType"">标题</span>
                    <i class=""ri-arrow-down-s-line ri-lg ml-1""></i>
                </div>
                <div class=""w-px h-6 bg-gray-200 mx-2""></div>
                <input type=""hidden"" id=""srhrange"" name=""function"" value=""bbs_0"">
                <input type=""hidden"" name=""siteid"" value=""1000"">
                <input type=""hidden"" name=""classid"" value=""0"">
                <input id=""searchInput"" name=""key"" type=""text"" required=""required"" placeholder=""善用搜索"" class=""flex-1 min-w-0 py-2.5 px-2 text-sm border-none focus:outline-none text-gray-700 bg-transparent"">
                <button type=""submit"" class=""w-10 h-10 flex items-center justify-center text-gray-500 cursor-pointer rounded-full search-btn"" style=""border:none;background:transparent;"">
                    <i class=""ri-search-line ri-lg""></i>
                </button>
                <div id=""searchOptions"" class=""search-options"">
                    <div class=""py-2.5 px-4 cursor-pointer noselect"" data-type=""标题"">标题</div>
                    <div class=""py-2.5 px-4 cursor-pointer noselect"" data-type=""内容"">内容</div>
                    <div class=""py-2.5 px-4 cursor-pointer noselect"" data-type=""昵称"">昵称</div>
                    <div class=""py-2.5 px-4 cursor-pointer noselect"" data-type=""ID号"">ID号</div>
                </div>
            </form>
        </div>

        <!-- 主要内容区域 -->
        <main class=""px-4 py-4"">
            <!-- 综合论坛 -->
            <section class=""mb-6"">
                <h2 class=""text-xl font-bold mb-3"">综合论坛</h2>
                <div class=""grid grid-cols-2 gap-3"">
                    <a href=""/bbs/book_list.aspx?classid=201"" class=""bg-white rounded-lg p-3 shadow-sm card-hover block"">
                        <div class=""flex items-center"">
                            <div class=""w-10 h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mr-3"">
                                <i class=""ri-cloud-line ri-lg text-primary w-6 h-6 flex items-center justify-center""></i>
                            </div>
                            <span class=""text-gray-800 font-medium"">资源共享</span>
                        </div>
                    </a>
                    <a href=""/bbs/book_list.aspx?classid=197"" class=""bg-white rounded-lg p-3 shadow-sm card-hover block"">
                        <div class=""flex items-center"">
                            <div class=""w-10 h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mr-3"">
                                <i class=""ri-code-s-slash-line ri-lg text-primary w-6 h-6 flex items-center justify-center""></i>
                            </div>
                            <span class=""text-gray-800 font-medium"">综合技术</span>
                        </div>
                    </a>
                    <a href=""/bbs/book_list.aspx?classid=177"" class=""bg-white rounded-lg p-3 shadow-sm card-hover block"">
                        <div class=""flex items-center"">
                            <div class=""w-10 h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mr-3"">
                                <i class=""ri-cup-line ri-lg text-primary w-6 h-6 flex items-center justify-center""></i>
                            </div>
                            <span class=""text-gray-800 font-medium"">妖火茶馆</span>
                        </div>
                    </a>
                    <a href=""/bbs/book_list.aspx?classid=240"" class=""bg-white rounded-lg p-3 shadow-sm card-hover block"">
                        <div class=""flex items-center"">
                            <div class=""w-10 h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mr-3"">
                                <i class=""ri-image-line ri-lg text-primary w-6 h-6 flex items-center justify-center""></i>
                            </div>
                            <span class=""text-gray-800 font-medium"">贴图晒照</span>
                        </div>
                    </a>
                    <a href=""/bbs/book_list.aspx?classid=204"" class=""bg-white rounded-lg p-3 shadow-sm card-hover block"">
                        <div class=""flex items-center"">
                            <div class=""w-10 h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mr-3"">
                                <i class=""ri-lightbulb-line ri-lg text-primary w-6 h-6 flex items-center justify-center""></i>
                            </div>
                            <span class=""text-gray-800 font-medium"">活动线报</span>
                        </div>
                    </a>
                    <a href=""/bbs/book_list.aspx?classid=203"" class=""bg-white rounded-lg p-3 shadow-sm card-hover block"">
                        <div class=""flex items-center"">
                            <div class=""w-10 h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mr-3"">
                                <i class=""ri-wifi-line ri-lg text-primary w-6 h-6 flex items-center justify-center""></i>
                            </div>
                            <span class=""text-gray-800 font-medium"">免流讨论</span>
                        </div>
                    </a>
                    <a href=""/bbs/book_list.aspx?classid=213"" class=""bg-white rounded-lg p-3 shadow-sm card-hover block"">
                        <div class=""flex items-center"">
                            <div class=""w-10 h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mr-3"">
                                <i class=""ri-question-answer-line ri-lg text-primary w-6 h-6 flex items-center justify-center""></i>
                            </div>
                            <span class=""text-gray-800 font-medium"">悬赏问答</span>
                        </div>
                    </a>
                    <a href=""/bbs/book_list.aspx?classid=299"" class=""bg-white rounded-lg p-3 shadow-sm card-hover block"">
                        <div class=""flex items-center"">
                            <div class=""w-10 h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mr-3"">
                                <i class=""ri-team-line ri-lg text-primary w-6 h-6 flex items-center justify-center""></i>
                            </div>
                            <span class=""text-gray-800 font-medium"">拼团互助</span>
                        </div>
                    </a>
                </div>
            </section>
            <!-- 服务中心 -->
            <section>
                <h2 class=""text-xl font-bold mb-3"">服务中心</h2>
                <div class=""grid grid-cols-2 gap-3"">
                    <a href=""/bbs/book_list.aspx?classid=288"" class=""bg-white rounded-lg p-3 shadow-sm card-hover block"">
                        <div class=""flex items-center"">
                            <div class=""w-10 h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mr-3"">
                                <i class=""ri-volume-up-line ri-lg text-primary w-6 h-6 flex items-center justify-center""></i>
                            </div>
                            <span class=""text-gray-800 font-medium"">网站公告</span>
                        </div>
                    </a>
                    <a href=""/bbs/book_list.aspx?classid=198"" class=""bg-white rounded-lg p-3 shadow-sm card-hover block"">
                        <div class=""flex items-center"">
                            <div class=""w-10 h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mr-3"">
                                <i class=""ri-feedback-line ri-lg text-primary w-6 h-6 flex items-center justify-center""></i>
                            </div>
                            <span class=""text-gray-800 font-medium"">投诉建议</span>
                        </div>
                    </a>
                    <a href=""/bbs/book_list.aspx?classid=221"" class=""bg-white rounded-lg p-3 shadow-sm card-hover block"">
                        <div class=""flex items-center"">
                            <div class=""w-10 h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mr-3"">
                                <i class=""ri-calendar-event-line ri-lg text-primary w-6 h-6 flex items-center justify-center""></i>
                            </div>
                            <span class=""text-gray-800 font-medium"">站内活动</span>
                        </div>
                    </a>
                    <a href=""/bbs/book_list.aspx?classid=199"" class=""bg-white rounded-lg p-3 shadow-sm card-hover block"">
                        <div class=""flex items-center"">
                            <div class=""w-10 h-10 bg-primary bg-opacity-10 rounded-full flex items-center justify-center mr-3"">
                                <i class=""ri-settings-3-line ri-lg text-primary w-6 h-6 flex items-center justify-center""></i>
                            </div>
                            <span class=""text-gray-800 font-medium"">站务处理</span>
                        </div>
                    </a>
                </div>
            </section>
        </main>

        <!-- 底部导航栏 -->
        <nav class=""w-full bg-white border-t border-gray-200 flex justify-around items-center py-2 main-footer
            fixed bottom-0 left-0 right-0 z-20 md:static md:bottom-auto md:left-auto md:right-auto"">
            <a href=""/"" class=""flex flex-col items-center text-gray-500 bottom-nav-btn noselect"">
                <i class=""ri-home-5-line ri-lg w-6 h-6 flex items-center justify-center""></i>
                <span class=""text-xs mt-1"">首页</span>
            </a>
            <a href=""/bbs/book_list_hot.aspx?days=1"" class=""flex flex-col items-center text-gray-500 bottom-nav-btn noselect"">
                <i class=""ri-fire-line ri-lg w-6 h-6 flex items-center justify-center""></i>
                <span class=""text-xs mt-1"">热门</span>
            </a>
            <a href=""/bbs/book_list.aspx?gettotal=2025&action=new"" class=""flex flex-col items-center text-gray-500 bottom-nav-btn noselect"">
                <i class=""ri-file-list-3-line ri-lg w-6 h-6 flex items-center justify-center""></i>
                <span class=""text-xs mt-1"">新帖</span>
            </a>
            <a href=""/myfile.aspx"" class=""flex flex-col items-center text-gray-500 bottom-nav-btn noselect"">
                <i class=""ri-user-3-line ri-lg w-6 h-6 flex items-center justify-center""></i>
                <span class=""text-xs mt-1"">我的</span>
            </a>
        </nav>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchType = document.getElementById('searchType');
            const searchOptions = document.getElementById('searchOptions');
            const currentSearchType = document.getElementById('currentSearchType');
            const optionItems = document.querySelectorAll('#searchOptions div');
            const searchInput = document.getElementById('searchInput');
            const form = document.getElementById('scbar_form');

            // 选项切换
            searchType.addEventListener('click', function(event) {
                event.stopPropagation();
                searchOptions.classList.toggle('active');
                searchType.classList.toggle('active'); // 控制三角翻转
            });
            optionItems.forEach(item => {
                item.addEventListener('click', function() {
                    currentSearchType.textContent = this.getAttribute('data-type');
                    searchOptions.classList.remove('active');
                    searchType.classList.remove('active'); // 选项确定后恢复
                });
            });
            // 输入框聚焦时关闭下拉菜单
            searchInput.addEventListener('focus', function() {
                searchOptions.classList.remove('active');
                searchType.classList.remove('active'); // 输入框聚焦时恢复
            });
            // 点击其他地方关闭下拉菜单
            document.addEventListener('click', function(event) {
                if (!searchType.contains(event.target) && !searchOptions.contains(event.target)) {
                    searchOptions.classList.remove('active');
                    searchType.classList.remove('active'); // 点击其他地方恢复
                }
            });
            // 表单提交逻辑
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                const type = currentSearchType.textContent;
                const key = searchInput.value.trim();
                let url = '';
                if (!key) return;
                if (type === '标题') {
                    url = '/bbs/book_list_search.aspx?action=search&siteid=1000&classid=0&type=title&key=' + encodeURIComponent(key);
                } else if (type === '内容') {
                    url = '/bbs/book_list_search.aspx?action=search&siteid=1000&classid=0&type=content&key=' + encodeURIComponent(key);
                } else if (type === '昵称') {
                    url = '/search/book_list.aspx?action=search&siteid=1000&classid=0&type=title&key=' + encodeURIComponent(key);
                } else if (type === 'ID号') {
                    url = '/search/book_list.aspx?touserid=' + encodeURIComponent(key);
                }
                window.location.href = url;
            });
        });
    </script>
</body>
</html>");
%>