/**
 * @description 妖火艾特脚本 - 完全重写版本
 * <AUTHOR> Agent
 * @date 2025-06-15
 * @version 1.2.2 - 修复中文昵称UBB链接转换失效问题
*/
(function () {
    // 全局变量
    let atUserData = {}; // 存储@用户信息 {userId: nickname}
    let originalContent = ''; // 存储原始内容，防止提交时丢失



    /**
     * 获取输入框元素
     */
    function getContentInput() {
        return document.getElementsByName('content')[0] ||
               document.querySelector('textarea[name="content"]');
    }

    /**
     * 获取回复表单
     */
    function getReplyForm() {
        return document.querySelector('form[name="f"]') ||
               document.querySelector('form[action*="book_re.aspx"]');
    }

    /**
     * 从用户空间页面获取昵称
     */
    function fetchNickname(userId) {
        const url = `/bbs/userinfo.aspx?touserid=${userId}`;

        return fetch(url)
            .then(response => response.text())
            .then(html => {
                // 尝试多种标题格式
                const patterns = [
                    /<title>(.*?)的空间<\/title>/i,
                    /<title>(.*?)的空间\s*-\s*.*?<\/title>/i,
                    /<title>(.*?)\s*-\s*用户空间<\/title>/i,
                    /<title>(.*?)\s*\|\s*用户空间<\/title>/i
                ];

                for (const pattern of patterns) {
                    const match = html.match(pattern);
                    if (match && match[1]) {
                        return match[1].trim();
                    }
                }

                // 如果标题匹配失败，尝试从页面内容中提取
                const contentPatterns = [
                    /<h2[^>]*>(.*?)<\/h2>/i,
                    /昵称[：:]\s*<\/span><\/b>(.*?)<br/i,
                    /昵称[：:]\s*(.*?)(?:<|$)/i,
                    /ID:\s*\d+.*?昵称[：:]\s*(.*?)(?:<|$)/i
                ];

                for (const pattern of contentPatterns) {
                    const match = html.match(pattern);
                    if (match && match[1]) {
                        const nickname = match[1].replace(/<[^>]*>/g, '').trim();
                        if (nickname && nickname !== '') {
                            return nickname;
                        }
                    }
                }

                return null;
            })
            .catch(error => {
                return null;
            });
    }

    /**
     * 发送@通知私信
     */
    function sendAtNotification(userId, nickname) {
        // 获取当前页面信息
        const urlParams = new URLSearchParams(window.location.search);
        let postId = urlParams.get('id');
        const pageTitle = document.title.split('-')[0].trim() || '帖子';

        // 如果URL参数中没有id，尝试从当前URL路径中提取
        if (!postId) {
            const pathMatch = window.location.pathname.match(/\/bbs-(\d+)\.html/);
            if (pathMatch) {
                postId = pathMatch[1];
            }
        }

        // 构建帖子链接
        const postUrl = postId ? `/bbs-${postId}.html` : window.location.pathname;

        // 获取实际的回复内容（去掉@用户部分）
        let replyContent = originalContent;
        if (!replyContent) {
            const textObj = getContentInput();
            if (textObj) {
                replyContent = textObj.value;
            }
        }

        // 去掉@用户部分，只保留实际回复内容
        const cleanContent = replyContent.replace(/@[^\s]+\s*/, '').trim();

        // 构建消息内容
        const messageContent = `有人在帖子中@了你：[url=${postUrl}]${pageTitle}[/url]///回复内容：${cleanContent}`;

        // 准备表单数据
        const formData = new FormData();
        formData.append('action', 'gomod');
        formData.append('touseridlist', userId);
        formData.append('content', messageContent);
        formData.append('title', '在帖子中@了你');
        formData.append('siteid', '1000');
        formData.append('classid', '0');
        formData.append('types', '0');
        formData.append('issystem', '');
        formData.append('backurl', 'myfile.aspx');

        // 发送私信
        fetch('/bbs/messagelist_add.aspx', {
            method: 'POST',
            body: formData
        });
    }

    /**
     * 处理@用户输入
     */
    function handleAtUserInput() {
        const textObj = getContentInput();
        if (!textObj) {
            return;
        }

        textObj.addEventListener('input', function(e) {
            const content = textObj.value;

            // 实时保存用户输入内容（防止其他脚本清空）
            originalContent = content;

            // 检测@用户ID格式：@数字 空格
            const atMatch = content.match(/@(\d+)\s/);
            if (atMatch) {
                const userId = atMatch[1];

                // 显示加载状态
                const beforeAt = content.substring(0, atMatch.index);
                const afterAt = content.substring(atMatch.index + atMatch[0].length);
                textObj.value = beforeAt + '昵称检索中...' + afterAt;

                // 获取昵称
                fetchNickname(userId).then(nickname => {
                    if (nickname) {
                        atUserData[userId] = nickname;
                        const newContent = beforeAt + `@${nickname} ` + afterAt;
                        textObj.value = newContent;
                        // 更新保存的内容
                        originalContent = newContent;
                    } else {
                        textObj.value = beforeAt + '@ID号错误 ' + afterAt;
                    }
                });
            }
        });
    }

    /**
     * 处理表单提交
     */
    function handleFormSubmit() {
        // 尝试多种表单选择器
        const selectors = [
            'form[name="f"]',
            'form[action*="book_re.aspx"]',
            'form[method="post"]',
            'form'
        ];

        let form = null;
        for (const selector of selectors) {
            const foundForm = document.querySelector(selector);
            if (foundForm) {
                form = foundForm;
                break;
            }
        }

        if (!form) {
            return;
        }

        // 监听提交按钮点击事件
        const submitButton = form.querySelector('input[type="submit"], button[type="submit"], input[name="g"]');
        if (submitButton) {
            submitButton.addEventListener('click', function(e) {
                // 最后一次保存当前输入框内容
                const textObj = getContentInput();
                if (textObj) {
                    const currentContent = textObj.value;
                    // 如果originalContent为空，使用当前内容
                    if (!originalContent && currentContent) {
                        originalContent = currentContent;
                    }
                }

                // 检查是否有@用户需要发送通知和转换UBB链接
                if (Object.keys(atUserData).length > 0) {
                    // 转换@昵称为UBB链接格式
                    if (textObj) {
                        let content = textObj.value;

                        for (const [userId, nickname] of Object.entries(atUserData)) {
                            // 将 @昵称 转换为 @[url=/bbs/userinfo.aspx?touserid=用户ID]昵称[/url]
                            // 转义特殊字符并使用空格或结尾作为边界
                            const escapedNickname = nickname.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                            const atPattern = new RegExp(`@${escapedNickname}(?=\\s|$)`, 'g');
                            const ubbLink = `@[url=/bbs/userinfo.aspx?touserid=${userId}]${nickname}[/url]`;
                            content = content.replace(atPattern, ubbLink);
                        }

                        textObj.value = content;
                    }

                    // 为每个@用户发送通知
                    for (const [userId, nickname] of Object.entries(atUserData)) {
                        setTimeout(() => {
                            sendAtNotification(userId, nickname);
                        }, 100);
                    }
                }
            });
        }
    }

    /**
     * 初始化脚本
     */
    function init() {
        handleAtUserInput();
        handleFormSubmit();
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }



})();