﻿/* 通用组件样式 */
.save-btn {
    background-color: #1abc9c;
    border: none;
    color: white;
    padding: 12px 20px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 16px;
    margin: 10px 0 0;
    cursor: pointer;
    border-radius: 6px;
    width: 100%;
    min-width: 100px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

    .save-btn:hover {
        background-color: #20ab8e;
        box-shadow: 0 4px 10px rgba(26, 188, 156, 0.5);
    }

    /* 按钮点击时的波纹效果 */
    .save-btn:active:after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(255, 255, 255, 0.7);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%);
        transform-origin: 50% 50%;
        animation: ripple 0.6s ease-out;
    }

.status-message {
    margin-top: 10px;
    padding: 8px;
    border-radius: 4px;
    display: none;
}

.success {
    background-color: #dff0d8;
    color: #3c763d;
}

.error {
    background-color: #f2dede;
    color: #a94442;
}

/* 调试信息 */
#debug-info {
    margin-top: 20px;
    padding: 10px;
    border: 1px solid #ccc;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
    background-color: #f9f9f9;
    display: block;
}

li, ul {
    list-style: none;
    margin: 0;
    padding: 0
}

.toggle-container {
    width: 250px;
    min-width: 200px;
    background: #fff;
    padding: 20px;
    border-radius: 10px;
    border: none;
    user-select: none;
    -webkit-user-select: none;
    -ms-user-select: none;
}

.tg-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0
}

    .tg-list-item h4 {
        margin: 0;
        color: #333;
        flex-grow: 1;
        font-size: 17px;
        text-align: left
    }

.font-choice {
    display: flex;
    border-radius: 5px;
    overflow: hidden;
    max-width: 145px;
}

    .font-choice input[type="radio"] {
        display: none;
    }

    .font-choice label {
        cursor: pointer;
        padding: 5.55px 12px;
        background: #f0f0f0;
        margin: 0;
        transition: background-color 0.3s, color 0.3s;
        line-height: normal;
    }

    .font-choice input[type="radio"]:checked + label {
        background: #0000FF;
        color: white;
    }

    .font-choice input[type="radio"]#song:checked + label,
    .font-choice input[type="radio"]#other:checked + label {
        background: #333;
        color: #ddd;
    }

    .font-choice.Medal-Images-choice {
        display: flex;
        align-items: center;
        /* margin-top: 10px; */
    }

        .font-choice.Medal-Images-choice input[type="radio"]#medalPartial:checked + label,
        .font-choice.Medal-Images-choice input[type="radio"]#medalAll:checked + label {
            background: #333;
            color: #ddd;
        }

/* 开关按钮样式 */
.tgl {
    display: none
}

    .tgl, .tgl *, .tgl :after, .tgl :before, .tgl + .tgl-btn, .tgl:after, .tgl:before {
        box-sizing: border-box
    }

        .tgl ::-moz-selection, .tgl :after::-moz-selection, .tgl :before::-moz-selection, .tgl + .tgl-btn::-moz-selection, .tgl::-moz-selection, .tgl:after::-moz-selection, .tgl:before::-moz-selection {
            background: 0 0
        }

        .tgl ::selection, .tgl :after::selection, .tgl :before::selection, .tgl + .tgl-btn::selection, .tgl::selection, .tgl:after::selection, .tgl:before::selection {
            background: 0 0
        }

        .tgl + .tgl-btn {
            outline: 0;
            display: block;
            width: 4em;
            height: 2em;
            position: relative;
            cursor: pointer;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none
        }

            .tgl + .tgl-btn:after, .tgl + .tgl-btn:before {
                position: relative;
                display: block;
                content: "";
                width: 50%;
                height: 100%
            }

            .tgl + .tgl-btn:after {
                left: 0
            }

            .tgl + .tgl-btn:before {
                display: none
            }

        .tgl:checked + .tgl-btn:after {
            left: 50%
        }

.tgl-ios + .tgl-btn {
    background: #fbfbfb;
    border-radius: 2em;
    padding: 2px;
    transition: all .4s ease;
    border: 1px solid #e8eae9
}

    .tgl-ios + .tgl-btn:after {
        border-radius: 2em;
        background: #fbfbfb;
        transition: left .3s cubic-bezier(.175,.885,.32,1.275), padding .3s ease, margin .3s ease;
        box-shadow: 0 0 0 1px rgba(0,0,0,.1), 0 4px 0 rgba(0,0,0,.08)
    }

    .tgl-ios + .tgl-btn:hover:after {
        will-change: padding
    }

    .tgl-ios + .tgl-btn:active {
        box-shadow: inset 0 0 0 2em #e8eae9
    }

        .tgl-ios + .tgl-btn:active:after {
            padding-right: .8em
        }

.tgl-ios:checked + .tgl-btn {
    background: #1abc9c;
}

    .tgl-ios:checked + .tgl-btn:active {
        box-shadow: none
    }

        .tgl-ios:checked + .tgl-btn:active:after {
            margin-left: -.8em
        }

/* iframe 模式专用样式 */
/* 重置 iframe 内部的 html 和 body 样式，以解决顶部空白问题 */
html, /* 应用于 iframe 内的 html 标签 */
body.iframe-mode {
    height: 100%;
    width: 100%;
    margin: 0;
    padding: 0;
    display: block; /* 覆盖 html 的内联 display:flex */
    background-color: transparent; /* 确保 html 和 body 背景透明 */
    overflow: hidden; /* 防止意外滚动条 */
}

body.iframe-mode {
}

.iframe-mode {
    overflow: hidden;
}

    .iframe-mode .toggle-container {
        padding: 0;
        border-radius: 0;
        transform: translateY(0);
        opacity: 1;
        transition: transform 0.3s ease, opacity 0.3s ease;
        width: auto;
    }

    .iframe-mode .title {
        display: none !important;
    }

/* 设置项目的动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translate3d(0, 20px, 0);
    }

    to {
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}

.settings-popup-styled-container {
    padding: 8px 15px 15px;
}
/* 优化按钮状态样式 */
.save-btn.btn-success {
    background-color: #16a085;
    position: relative;
    overflow: hidden;
}

    /* 移除原有的淡入淡出动画，使背景色直接更改 */
    .save-btn.btn-success:before {
        display: none;
    }

    /* SVG图标缩放动画 */
    .save-btn.btn-success svg {
        animation: iconPop 0.8s cubic-bezier(0.22, 0.61, 0.36, 1) forwards;
        transform-origin: center;
        transition: transform 0.3s ease;
    }

@keyframes iconPop {
    0% {
        transform: scale(0);
        opacity: 0;
    }

    50% {
        transform: scale(1.3);
        opacity: 1;
    }

    70% {
        transform: scale(0.9);
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.save-btn.btn-error {
    background-color: #f44336;
    border-color: #D32F2F;
    color: white;
    animation: shake 0.5s linear 1;
    box-shadow: 0 2px 8px rgba(244, 67, 54, 0.5);
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }

    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }

    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* 合并的按钮禁用状态基础样式 */
.save-btn[disabled],
.save-btn.saving {
    background-color: #9e9e9e !important;
    box-shadow: none !important;
    pointer-events: none;
    cursor: not-allowed;
}

/* 保存中状态特有样式 */
.save-btn.saving {
    position: relative;
    animation-name: none; /* 重置任何其他动画 */
}

    .save-btn.saving:before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 60%;
        background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.7), transparent);
        animation: saving-pulse 0.4s infinite linear;
        animation-play-state: running; /* 确保动画运行 */
    }

/* 禁用状态特有样式 */
.save-btn[disabled] {
    color: rgba(255, 255, 255, 0.7);
}

/* 确保每次添加saving类时动画都会重启 */
@keyframes saving-pulse {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(500%);
    }
}

/* 按钮焦点效果 */
.save-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(26, 188, 156, 0.4);
}


.iframe-mode .tg-list-item {
    padding: 12px 0;
    margin-bottom: 5px;
    border-bottom: 1px solid #f5f5f5;
}

    .iframe-mode .tg-list-item:last-child {
        margin-bottom: 0;
    }

    .iframe-mode .tg-list-item h4 {
        font-size: 16px;
        font-weight: 500;
        color: #333;
    }

.iframe-mode .font-choice {
    max-width: 140px;
}

    .iframe-mode .font-choice label {
        padding: 6px 12px;
        font-size: 14px;
    }

        .iframe-mode .font-choice label:hover {
            background-color: #e9e9e9;
        }

.iframe-mode .save-btn {
    margin-top: 15px;
    width: 94%;
    font-size: 16px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

    .iframe-mode .save-btn:hover {
        box-shadow: 0 4px 8px rgba(26, 188, 156, 0.5);
    }

    /* 为iframe模式添加特有的透明度效果 */
    .iframe-mode .save-btn[disabled],
    .iframe-mode .save-btn.saving {
        opacity: 0.7;
    }

.iframe-mode .status-message {
    width: 100%;
    padding: 10px;
    margin-top: 15px;
    border-radius: 6px;
    font-size: 14px;
    text-align: center;
}

    .iframe-mode .status-message.success {
        background-color: #e8f5e9;
        border: 1px solid #c8e6c9;
    }

    .iframe-mode .status-message.error {
        background-color: #ffebee;
        border: 1px solid #ffcdd2;
    }