// 将 countdownInterval 声明为全局变量
let countdownInterval;

function closeDialog() {
    document.getElementById('betting-dialog').style.display = 'none';
    // 重置选中的金额
    selectedAmount = null;
    // 恢复原始显示
    updateBettingFooter(currentUserPoints, true);
}

function updateGuessData() {
    location.reload();
}

function updateCountdown(deadline) {
    var now = new Date();
    var diff = deadline - now;
    var countdownElement = document.querySelector('.Betting-countdown');

    if (!countdownElement) {
        // 如果倒计时元素不存在，清除定时器
        clearInterval(countdownInterval);
        return;
    }

    if (diff > 0) {
        var days = Math.floor(diff / (1000 * 60 * 60 * 24));
        var hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        var minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        var seconds = Math.floor((diff % (1000 * 60)) / 1000);

        countdownElement.querySelector('.countdown-days').textContent = days.toString().padStart(2, '0');
        countdownElement.querySelector('.countdown-hours').textContent = hours.toString().padStart(2, '0');
        countdownElement.querySelector('.countdown-minutes').textContent = minutes.toString().padStart(2, '0');
        countdownElement.querySelector('.countdown-seconds').textContent = seconds.toString().padStart(2, '0');
    } else {
        const tipElement = document.createElement('div');
        tipElement.className = 'Betting-TipText';
        tipElement.textContent = '已停止下注，等待开奖';
        countdownElement.parentNode.replaceChild(tipElement, countdownElement);
        disableBettingButtons();
        // 清除定时器
        clearInterval(countdownInterval);
    }
}

function disableBettingButtons() {
    const buttons = document.querySelectorAll('.Betting-prediction-button');
    buttons.forEach(button => {
        const link = button.querySelector('a');
        if (link) {
            // 移除超链接，保留文本
            button.textContent = link.textContent;
            // 移除原有的点击事件
            button.onclick = null;
            // 添加新的点击事件
            button.addEventListener('click', function (event) {
                event.preventDefault();
                const isGreenOption = this.classList.contains('fall');
                showCustomAlert('已停止下注，等待开奖', isGreenOption);
            });
        }
    });
}

function adjustPoolDigits() {
    const poolDigits = document.querySelectorAll('.Betting-pool-digit');
    const screenWidth = window.innerWidth;
    let digitsToRemove = 0;

    if (screenWidth < 500) {
        digitsToRemove = 2;
    } else if (screenWidth < 633) {
        digitsToRemove = 1;
    }

    let foundNonZero = false;
    poolDigits.forEach((digit, index) => {
        if (index < digitsToRemove && digit.textContent === '0' && !foundNonZero) {
            digit.classList.add('hidden');
        } else {
            digit.classList.remove('hidden');
            foundNonZero = true;
        }
    });
}

function showCustomAlert(message, isGreen = false, autoClose = false) {
    const alertOverlay = document.getElementById('custom-alert');
    const alertMessage = document.getElementById('alert-message');
    const alertClose = document.getElementById('alert-close');

    alertMessage.innerHTML = message;  // 使用 innerHTML 而不是 textContent
    alertOverlay.style.display = 'flex';

    if (isGreen) {
        alertClose.classList.add('green');
        alertClose.classList.remove('red');
    } else {
        alertClose.classList.add('red');
        alertClose.classList.remove('green');
    }

    alertClose.onclick = function () {
        alertOverlay.style.display = 'none';
    };

    alertOverlay.onclick = function (event) {
        if (event.target === alertOverlay) {
            alertOverlay.style.display = 'none';
        }
    };

    if (autoClose) {
        setTimeout(() => {
            alertOverlay.style.display = 'none';
        }, 500);
    }
}

// 在文件开头添加这个新函数
function getPointValues(userBalance) {
    const thresholds = [
        { min: 100001, values: [200, 500, 3000, 5000, 10000, 20000, 30000, 50000, 100000] },
        { min: 80001, values: [200, 500, 5000, 8000, 10000, 20000, 30000, 50000, 80000] },
        { min: 50001, values: [200, 500, 3000, 5000, 8000, 10000, 20000, 30000, 50000] },
        { min: 30001, values: [200, 500, 2000, 3000, 5000, 8000, 10000, 20000, 30000] },
        { min: 20001, values: [200, 500, 1000, 2000, 3000, 5000, 8000, 10000, 20000] }
    ];

    for (let threshold of thresholds) {
        if (userBalance >= threshold.min) {
            return threshold.values;
        }
    }

    // 返回默认值，适用于用户余额小于20001或为负数的情况
    return [200, 300, 500, 1000, 2000, 3000, 5000, 8000, 10000];
}

// 在 initBetting 函数中添加这个新函数
function updatePointButtons() {
    const pointValues = getPointValues(currentUserPoints);
    const pointsButtons = document.querySelectorAll('.Betting-points-button');
    pointsButtons.forEach((button, index) => {
        button.textContent = pointValues[index];
    });
}

// 导出初始化函数
function initBetting(config) {
    console.log('initBetting called with config:', config);
    handleUserBet(config.userBet);
    if (config.isClosed) {
        disableVotingFunctions();
    }

    // 添加这行来初始化奖池动画
    animatePoolValue(0, config.totalAmount, 2000); // 2000ms = 2秒

    let prediction = null;
    let selectedAmount = null;
    currentUserPoints = parseInt(document.querySelector('.Betting-points-value').textContent);
    updatePointButtons(); // 初始化时调用

    function handlePrediction(option, event) {
        if (new Date() >= config.deadline) {
            event.preventDefault();
            const isGreenOption = event.currentTarget.classList.contains('fall');
            showCustomAlert('已停止下注，等待开奖', isGreenOption);
            return;
        }

        event.preventDefault();
        prediction = option;
        const dialog = document.getElementById('betting-dialog');
        const dialogTitle = dialog.querySelector('.Betting-dialog-title');
        const confirmButton = dialog.querySelector('.Betting-confirm-button');
        const pointsButtons = dialog.querySelectorAll('.Betting-points-button');

        document.querySelectorAll('.Betting-prediction-button').forEach(btn => {
            btn.classList.remove('active');
        });

        event.currentTarget.classList.add('active');

        dialogTitle.textContent = `竞猜${config.guessingTitle}`;
        confirmButton.textContent = `我选${option}`;
        confirmButton.className = `Betting-confirm-button ${option === config.option1Text ? 'rise' : 'fall'}`;

        pointsButtons.forEach(button => {
            button.classList.remove('rise', 'fall', 'active');
        });

        dialog.style.display = 'flex';
        // 重置选中的金额并更新显示
        selectedAmount = null;
        updateBettingFooter(currentUserPoints, true);
    }

    function confirmBet() {
        if (!selectedAmount) {
            showCustomAlert('请选择投入的妖晶数量');
            return;
        }

        // 添加余额检查
        if (parseInt(selectedAmount) > currentUserPoints) {
            showCustomAlert('您的余额不足，请更换下注金额');
            return;
        }

        const selectedButton = document.querySelector('.Betting-prediction-button.active');
        if (!selectedButton) {
            showCustomAlert('请选择一个预测选项');
            return;
        }
        prediction = selectedButton.textContent.trim();

        const voteUrl = selectedButton.getAttribute('data-vote-url');

        if (!voteUrl) {
            showCustomAlert('无法获取下注链接');
            return;
        }

        fetch(voteUrl)
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');

                const viewState = doc.querySelector('#__VIEWSTATE').value;
                const viewStateGenerator = doc.querySelector('#__VIEWSTATEGENERATOR').value;
                const eventValidation = doc.querySelector('#__EVENTVALIDATION').value;

                const formData = new URLSearchParams();
                formData.append('__VIEWSTATE', viewState);
                formData.append('__VIEWSTATEGENERATOR', viewStateGenerator);
                formData.append('__EVENTVALIDATION', eventValidation);
                formData.append('ddlBetAmount', selectedAmount);
                formData.append('btnConfirm', '确认下注');

                return fetch(voteUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: formData
                });
            })
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const tipInfo = doc.querySelector('.tip');
                if (tipInfo) {
                    const message = tipInfo.textContent.trim();
                    const isGreenOption = prediction === config.option2Text;
                    if (message.includes('成功')) {
                        showCustomAlert('下注成功', isGreenOption, true);
                        updatePoolValue(parseInt(selectedAmount));
                        updateOptionAmounts(parseInt(selectedAmount));
                        updateUserPoints(parseInt(selectedAmount));
                        // 调用庆祝函数
                        celebrate();
                        // 恢复原始显示
                        updateBettingFooter(currentUserPoints);

                        // 新增：更新页面为已下注状态
                        displayUserBetInfo(selectedAmount, prediction, prediction === config.option1Text ? 1 : 2);
                    } else {
                        showCustomAlert(message || '下注失败，请稍后重试', isGreenOption);
                    }
                } else {
                    showCustomAlert('下注失败，请稍后重试', false);
                }
                closeDialog();
            })
            .catch(error => {
                showCustomAlert('请求失败，请稍后重试');
                closeDialog();
            });
    }

    function animatePoolValue(startValue, endValue, duration) {
        const poolDigits = document.querySelectorAll('.Betting-pool-digit');
        const startTime = performance.now();

        function updateValue(currentTime) {
            const elapsedTime = currentTime - startTime;
            if (elapsedTime < duration) {
                const progress = elapsedTime / duration;
                const currentValue = Math.floor(startValue + (endValue - startValue) * progress);
                updatePoolDigits(currentValue);
                requestAnimationFrame(updateValue);
            } else {
                updatePoolDigits(endValue);
                // 动画结束后再调整数字显示
                adjustPoolDigits();
            }
        }

        function updatePoolDigits(value) {
            const paddedValue = value.toString().padStart(8, '0');
            poolDigits.forEach((digit, index) => {
                digit.textContent = paddedValue[index];
                // 在动画过程中，确保所有数字都可见
                digit.classList.remove('hidden');
            });
        }

        requestAnimationFrame(updateValue);
    }

    function updatePoolValue(newAmount) {
        const poolDigits = document.querySelectorAll('.Betting-pool-digit');
        const currentValue = parseInt(Array.from(poolDigits).map(digit => digit.textContent).join(''), 10);
        animatePoolValue(currentValue, currentValue + newAmount, 2000); // 动画持续时间
    }

    function updateOptionAmounts(newBetAmount) {
        const option1Bar = document.querySelector('.bg-gradient-to-r.from-red-500');
        const option2Bar = document.querySelector('.bg-gradient-to-r.from-green-500');
        const totalAmount = parseInt(Array.from(document.querySelectorAll('.Betting-pool-digit')).map(digit => digit.textContent).join('')) || 0;

        const newTotalAmount = totalAmount + newBetAmount;

        let option1Amount = Math.round(totalAmount * (parseInt(option1Bar.style.width) || 0) / 100);
        let option2Amount = totalAmount - option1Amount;

        if (prediction === config.option1Text) {
            option1Amount += newBetAmount;
        } else {
            option2Amount += newBetAmount;
        }

        // 使用与后端一致的计算方法
        const option1Percentage = newTotalAmount > 0 ? Math.floor((option1Amount / newTotalAmount) * 100) : 50;
        const option2Percentage = 100 - option1Percentage;

        const startOption1Percentage = parseInt(option1Bar.style.width) || 0;
        const startOption2Percentage = parseInt(option2Bar.style.width) || 0;

        animatePercentageBar(option1Bar, startOption1Percentage, option1Percentage);
        animatePercentageBar(option2Bar, startOption2Percentage, option2Percentage);

        animatePercentageDisplay(startOption1Percentage, option1Percentage, startOption2Percentage, option2Percentage);
    }

    function animatePercentageBar(element, startPercentage, endPercentage) {
        const duration = 2000; // 动画持续时间
        const startTime = performance.now();

        function updateBar(currentTime) {
            const elapsedTime = currentTime - startTime;
            if (elapsedTime < duration) {
                const progress = elapsedTime / duration;
                const currentPercentage = Math.round(startPercentage + (endPercentage - startPercentage) * progress);
                element.style.width = `${currentPercentage}%`;
                requestAnimationFrame(updateBar);
            } else {
                element.style.width = `${endPercentage}%`;
            }
        }

        requestAnimationFrame(updateBar);
    }

    function animatePercentageDisplay(startOption1, endOption1, startOption2, endOption2) {
        const duration = 2000; // 动画持续时间
        const startTime = performance.now();

        function updateDisplay(currentTime) {
            const elapsedTime = currentTime - startTime;
            if (elapsedTime < duration) {
                const progress = elapsedTime / duration;
                const currentOption1 = Math.round(startOption1 + (endOption1 - startOption1) * progress);
                const currentOption2 = Math.round(startOption2 + (endOption2 - startOption2) * progress);
                updatePercentageDisplay(currentOption1, currentOption2);
                requestAnimationFrame(updateDisplay);
            } else {
                updatePercentageDisplay(endOption1, endOption2);
            }
        }

        requestAnimationFrame(updateDisplay);
    }

    function getOptionAmounts() {
        const option1Amount = parseInt(document.querySelector('.bg-gradient-to-r.from-red-500').style.width) || 0;
        const option2Amount = parseInt(document.querySelector('.bg-gradient-to-r.from-green-500').style.width) || 0;
        const totalAmount = parseInt(Array.from(document.querySelectorAll('.Betting-pool-digit')).map(digit => digit.textContent).join('')) || 0;

        const option1Total = Math.round(totalAmount * option1Amount / 100);
        const option2Total = Math.round(totalAmount * option2Amount / 100);
    }

    document.querySelectorAll('.Betting-points-button').forEach(button => {
        button.addEventListener('click', function () {
            document.querySelectorAll('.Betting-points-button').forEach(btn => {
                btn.classList.remove('active', 'rise', 'fall');
            });

            const amount = parseInt(this.textContent);
            if (amount > currentUserPoints) {
                const isGreenOption = prediction === config.option2Text;
                showCustomAlert('您的余额不足，无法选择此金额', isGreenOption);
                return;
            }

            this.classList.add('active', prediction === config.option1Text ? 'rise' : 'fall');
            selectedAmount = this.textContent;

            // 更新显示的下注金额
            updateBettingFooter(amount);
        });
    });

    document.getElementById('confirmButton').addEventListener('click', confirmBet);

    document.getElementById('betting-dialog').addEventListener('click', function (event) {
        if (event.target === this) {
            closeDialog();
        }
    });

    document.querySelectorAll('.Betting-prediction-button').forEach(button => {
        button.addEventListener('click', function (event) {
            handlePrediction(this.textContent.trim(), event);
        });
    });

    window.addEventListener('resize', adjustPoolDigits);

    // 如果已经存在定时器，先清除它
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }

    // 设置新的定时器
    countdownInterval = setInterval(() => updateCountdown(config.deadline), 1000);
    updateCountdown(config.deadline);

    getOptionAmounts();

    // 在 initBetting 函数末尾添加以下代码
    if (config.userBetOption && config.winningOption) {
        console.log('Checking win condition:', {
            userBetOption: config.userBetOption,
            winningOption: config.winningOption
        });
        if (config.userBetOption === config.winningOption) {
            console.log('User won! Calling rainbow()');
            rainbow();
        } else {
            console.log('User did not win');
        }
    } else {
        console.log('Missing userBetOption or winningOption', {
            userBetOption: config.userBetOption,
            winningOption: config.winningOption
        });
    }

    // 初始化已存在的下注信息
    initExistingBet();
}

function updatePercentageDisplay(option1Percentage, option2Percentage) {
    const option1PercentageElement = document.querySelector('.text-xs.font-semibold.bg-red-500');
    const option2PercentageElement = document.querySelector('.text-xs.font-semibold.bg-green-500');

    if (option1PercentageElement) {
        option1PercentageElement.textContent = option1Percentage + '%';
    }
    if (option2PercentageElement) {
        option2PercentageElement.textContent = option2Percentage + '%';
    }
}

function updateUserPoints(betAmount) {
    currentUserPoints -= betAmount;
    const pointsElement = document.querySelector('.Betting-points-value');
    const availablePointsElement = document.getElementById('available-points');

    if (pointsElement) {
        pointsElement.textContent = currentUserPoints;
    }
    if (availablePointsElement) {
        availablePointsElement.textContent = currentUserPoints;
    }

    updatePointButtons(); // 更新按钮显示
}

function disableVotingFunctions() {
    // 禁用所有下注按钮
    var buttons = document.querySelectorAll('.Betting-prediction-button');
    buttons.forEach(function (button) {
        button.disabled = true;
        button.style.opacity = '0.5';
    });

    // 隐藏倒计时
    var countdownElement = document.querySelector('.Betting-countdown');
    if (countdownElement) {
        countdownElement.style.display = 'none';
    }

}

// 添加新函数来更新下注信息
function updateBettingFooter(amount, isAvailable = false) {
    const footerElement = document.querySelector('.Betting-dialog-footer');
    if (footerElement) {
        if (isAvailable) {
            footerElement.innerHTML = `可用 <span id="available-points">${amount}</span> 妖晶`;
        } else {
            footerElement.innerHTML = `下注 <span id="betting-amount">${amount}</span> 妖晶`;
        }
    }
}

function handleUserBet(userBet) {
    if (userBet) {
        const predictionButtons = document.querySelectorAll('.Betting-prediction-button');
        predictionButtons.forEach(button => button.style.display = 'none');

        const userBetInfo = document.querySelector('.user-bet-info');
        if (userBetInfo) {
            userBetInfo.style.display = 'block';
        }
    }
}

// 新增函数：显示用户下注信息
function displayUserBetInfo(betAmount, optionText, optionId) {
    const bettingContainer = document.querySelector('.Betting-widget .Betting-container');
    if (!bettingContainer) return;

    // 移除竞猜按钮容器
    const predictionButtonsContainer = bettingContainer.querySelector('.flex.justify-between.mb-8');
    if (predictionButtonsContainer) {
        predictionButtonsContainer.remove();
    }

    // 创建用户下注信息的HTML元素
    const userBetInfoDiv = document.createElement('div');
    userBetInfoDiv.className = 'user-bet-info mb-4';

    const betButton = document.createElement('button');
    betButton.className = `Betting-user-bet-button ${optionId === 1 ? 'rise' : 'fall'}`;
    betButton.innerHTML = `您已竞猜<span class="mybet-amount">${betAmount}</span>${optionText}`;

    // 添加点击事件
    betButton.addEventListener('click', () => showExpectedEarnings(betAmount, optionId));

    userBetInfoDiv.appendChild(betButton);

    // 插入用户下注信息到竞猜容器中，位于倒计时之前
    const countdownElement = bettingContainer.querySelector('.Betting-countdown');
    if (countdownElement) {
        bettingContainer.insertBefore(userBetInfoDiv, countdownElement);
    } else {
        bettingContainer.appendChild(userBetInfoDiv);
    }
}

// 新增函数：计算和显示预计收益
function showExpectedEarnings(betAmount, optionId) {
    const option1PercentageElement = document.querySelector('.text-xs.font-semibold.bg-red-500');
    const option2PercentageElement = document.querySelector('.text-xs.font-semibold.bg-green-500');

    if (!option1PercentageElement || !option2PercentageElement) {
        console.error('无法找到百分比元素');
        return;
    }

    const option1Percentage = parseInt(option1PercentageElement.textContent);
    const option2Percentage = parseInt(option2PercentageElement.textContent);

    if (isNaN(option1Percentage) || isNaN(option2Percentage)) {
        console.error('无效的百分比值');
        return;
    }

    let odds;
    if (optionId === 1) {
        odds = (100 / option1Percentage) * 0.9;
    } else {
        odds = (100 / option2Percentage) * 0.9;
    }

    const expectedEarnings = Math.floor(betAmount * odds);

    showCustomAlert(`根据当前赔率，预计获胜后可得<br><span class="mybet-amount">${expectedEarnings}</span>妖晶`, optionId === 2);
}

// 在文件末尾添加此函数
function checkUserWin(userBetOption, winningOption) {
    console.log('checkUserWin called with:', { userBetOption, winningOption });
    if (userBetOption && winningOption && userBetOption === winningOption) {
        console.log('User won in checkUserWin! Calling rainbow()');
        rainbow();
    } else {
        console.log('User did not win in checkUserWin');
    }
}

// 新增函数，用于初始化已存在的下注信息
function initExistingBet() {
    const existingBetButton = document.querySelector('.Betting-user-bet-button');
    if (existingBetButton) {
        const amountElement = existingBetButton.querySelector('.mybet-amount');
        if (amountElement) {
            const betAmount = parseInt(amountElement.textContent);
            const optionId = existingBetButton.classList.contains('rise') ? 1 : 2;

            // 移除 disabled 属性（如果存在）
            existingBetButton.removeAttribute('disabled');

            // 添加点击事件
            existingBetButton.addEventListener('click', () => showExpectedEarnings(betAmount, optionId));
        } else {
            console.warn('找到 Betting-user-bet-button，但未找到 .mybet-amount 元素');
        }
    }
}