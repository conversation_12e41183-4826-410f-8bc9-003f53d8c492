﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_List_rank.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Book_List_Rank" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<style>
    .rank-card {
        background-color: white;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        max-width: 720px;
        margin: 0 auto;
    }
    .rank-header {
        padding: 1rem 0.5rem 0;
    }
    .breadcrumb {
        display: flex;
        align-items: center;
        padding-left: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
        flex-wrap: nowrap;
        overflow-x: auto;
        scrollbar-width: none;
        white-space: nowrap;
    }
    .breadcrumb::-webkit-scrollbar {
        display: none;
    }
    .breadcrumb-item {
        display: flex;
        align-items: center;
        gap: 0.375rem;
        color: #6b7280;
        text-decoration: none;
        transition: color 0.2s;
        font-size: 16px;
    }
    .breadcrumb-item:hover {
        color: #374151;
    }
    .breadcrumb-item.active {
        font-size: 16px;
        cursor: pointer;
    }
    .breadcrumb-item svg {
        width: 16px;
        height: 16px;
    }
    .breadcrumb-separator {
        position: relative;
        width: 14px;
        height: 14px;
        margin: 0 0.5rem;
    }
    .breadcrumb-separator::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        border-right: 1.5px solid #d1d5db;
        border-top: 1.5px solid #d1d5db;
        transform: translate(-50%, -50%) rotate(45deg);
    }
    .rank-tabs {
        display: flex;
        border-bottom: 1px solid #e2e8f0;
        overflow-x: auto;
        scrollbar-width: none;
    }
    .rank-tabs::-webkit-scrollbar {
        display: none;
    }
    .rank-tabs a:hover {
        color: #378d8d;
        text-decoration: none;
    }
    .rank-tab {
        padding: 0.75rem 1rem;
        cursor: pointer;
        text-decoration: none;
        color: #64748b;
        border-bottom: 2px solid transparent;
        white-space: nowrap;
        flex-shrink: 0;
    }
    .rank-tab.active {
        border-bottom: 2px solid #378d8d;
        color: #378d8d;
        font-weight: bold;
    }
    .rank-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: background-color 0.2s;
        padding: 0 0.5rem;
    }
    .rank-item:nth-child(even) {
        background-color: #fbfbfb;
    }
    .rank-item:nth-child(odd) {
        background-color: #ffffff;
    }
    .rank-item:hover {
        background-color: #f1f5f9;
    }
    .rank-number {
        min-width: 3rem;
        text-align: center;
        font-weight: bold;
    }
	.rank-number,.rank-value-number{font-family: initial;}
    .rank-name {
        flex: 1;
    }
    .rank-name a {
        text-decoration: none;
        color: #0f172a;
    }
    .rank-value {
        text-align: right;
    }
    .rank-value-label {
        font-size: 0.875rem;
        color: #64748b;
    }
    .rank-value-number {
        font-weight: 600;
    }
    .btBox {
        padding: 1rem;
        border-top: 1px solid #e2e8f0;
        text-align: center;
    }
    .bt2 a {
        display: inline-block;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        background: white;
        border: 1px solid #e2e8f0;
        color: #0f172a;
        text-decoration: none;
        margin: 0 0.5rem;
    }
    .bt2 a:hover {
        background-color: #f1f5f9;
    }
    .card-footer {
        padding: 1rem;
        border-top: 1px solid #e2e8f0;
    }
    .pagination {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }
    .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        padding: 0.5rem 1rem;
        background-color: white;
        border: 1px solid #e2e8f0;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    .btn:hover {
        background-color: #f1f5f9;
    }
    .page-input {
        width: 1.5rem;
        text-align: center;
        padding: 0.25rem;
        border: 1px solid #e2e8f0;
        border-radius: 0.25rem;
    }
    .btn svg {
        width: 16px;
        height: 16px;
        stroke: #378d8d;
    }
    @media (max-width: 640px) {
        .rank-card {
            border-radius: 0;
            box-shadow: none;
        }
        .rank-header {
            padding: 0.5rem 0.25rem 0;
        }
        .breadcrumb {
            padding: 0.25rem;
        }
        .breadcrumb-item svg {
            width: 14px;
            height: 14px;
        }
        .breadcrumb-separator {
            width: 10px;
            height: 10px;
            margin: 0 0.25rem;
        }
        .rank-tab {
            padding: 0.5rem 0.75rem;
        }
        .rank-item {
            padding: 0 0.5rem;
        }
        .rank-number {
            min-width: 2.5rem;
        }
        .rank-value-label {
            font-size: 0.75rem;
        }
        .rank-value-number {
            font-size: 0.875rem;
        }
        .card-footer {
            padding: 0.5rem;
        }
        .pagination {
            gap: 0.5rem;
        }
        .btn {
            padding: 0.375rem 0.75rem;
        }
        .btn svg {
            width: 14px;
            height: 14px;
        }
    }
    .rank-item.active {
        background-color: #fef3c7;
    }
    .rank-item.active .rank-name a {
        font-weight: bold;
    }
</style>

<%
    if (this.stype == "0") { classVo.classname = "发帖排行"; }
    else if (this.stype == "1") { classVo.classname = "回复排行"; }
    else if (this.stype == "2") { classVo.classname = "妖晶排行"; }
    else if (this.stype == "3") { classVo.classname = "经验排行"; }
    else if (this.stype == "4") { classVo.classname = "人气排行"; }
    else if (this.stype == "5") { classVo.classname = "推荐排行"; }
    
    Response.Write(WapTool.showTop(classVo.classname, wmlVo));
    
    if (this.IsCheckManagerLvl("|00|01|02|03|04|", "") == true)
    {
        string isWebHtml = this.ShowWEB_list(this.classid);
        StringBuilder strhtml_list = new StringBuilder();
        
        // 开始卡片布局
        strhtml_list.Append("<div class=\"rank-card\">");
        
        // 头部
        strhtml_list.Append("<div class=\"rank-header\">");
        strhtml_list.Append("<div class=\"breadcrumb\">");
        strhtml_list.Append("<a href=\"/\" class=\"breadcrumb-item\">");
        strhtml_list.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-home\"><path d=\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"></path><polyline points=\"9 22 9 12 15 12 15 22\"></polyline></svg>");
        strhtml_list.Append("<span>首页</span>");
        strhtml_list.Append("</a>");
        strhtml_list.Append("<span class=\"breadcrumb-separator\"></span>");
        strhtml_list.Append("<span class=\"breadcrumb-item active\">风云榜</span>");
        strhtml_list.Append("<span class=\"breadcrumb-separator\"></span>");
        strhtml_list.Append("<span class=\"breadcrumb-item active\">" + classVo.classname + "</span>");
        strhtml_list.Append("</div>");
        
        // 选项卡
        strhtml_list.Append("<div class=\"rank-tabs\">");
        string[] tabNames = new[] { "帖子", "回复", siteVo.sitemoneyname, "经验", "人气" };
        for(int i = 0; i < 5; i++) {
            string activeClass = (this.stype == i.ToString()) ? " active" : "";
            strhtml_list.Append("<a href=\"" + this.http_start + "bbs/book_list_rank.aspx?stype=" + i + "\" class=\"rank-tab" + activeClass + "\">" + tabNames[i] + "</a>");
        }
        strhtml_list.Append("</div>");
        strhtml_list.Append("</div>");
        
        // 内容区
        strhtml_list.Append("<div class=\"rank-content\">");
        bool currentUserShown = false;
        
        // 计算当前页面的排名范围
        long pageStartRank = (CurrentPage - 1) * pageSize + 1;
        long pageEndRank = CurrentPage * pageSize;
        
        // 如果当前用户未在当前页显示且有排名，且排名小于当前页起始排名(即排名更好)，则在顶部显示
        if (!currentUserShown && CurrentUserRank > 0 && userVo != null && CurrentUserRank < pageStartRank)
        {
            strhtml_list.Append("<div class=\"rank-item active\" >");
            
            // 排名
            strhtml_list.Append("<div class=\"rank-number\">#" + CurrentUserRank.ToString() + "</div>");
            
            // 用户名
            strhtml_list.Append("<div class=\"rank-name\"><a href=\"" + this.http_start + "bbs/userinfo.aspx?siteid=" + this.siteid + "&classid=" + this.classid + "&touserid=" + userVo.userid + "\">" + WapTool.GetColorNickName(userVo.idname, userVo.nickname, lang, ver) + "</a></div>");
            
            // 数值
            strhtml_list.Append("<div class=\"rank-value\">");
            string label = "";
            string value = "";
            
            if(this.stype == "0") { 
                label = "帖子";
                value = userVo.bbsCount.ToString();
            }
            else if(this.stype == "1") {
                label = "回复";
                value = userVo.bbsReCount.ToString();
            }
            else if(this.stype == "2") {
                label = siteVo.sitemoneyname;
                value = userVo.money.ToString();
            }
            else if(this.stype == "3") {
                label = "经验";
                value = userVo.expr.ToString();
            }
            else if(this.stype == "4") {
                label = "人气";
                value = userVo.zoneCount.ToString();
            }
            
            strhtml_list.Append("<div class=\"rank-value-label\">" + label + "</div>");
            strhtml_list.Append("<div class=\"rank-value-number\">" + value + "</div>");
            strhtml_list.Append("</div>");
            
            strhtml_list.Append("</div>");
        }
        
        // 显示当前页的排名列表
        for (int i = 0; (listVo != null && i < listVo.Count); i++)
        {
            index = index + kk;
            
            // 判断是否是当前用户
            bool isCurrentUser = (userVo != null && listVo[i].userid == userVo.userid);
            string activeClass = isCurrentUser ? " active" : "";
            currentUserShown = currentUserShown || isCurrentUser;
            
            strhtml_list.Append("<div class=\"rank-item" + activeClass + "\">");
            
            // 排名
            if(index <= 3) {
                string crownColor = "";
                if(index == 1) crownColor = "#f59e0b";
                else if(index == 2) crownColor = "#94a3b8";
                else crownColor = "#b45309";
                
                strhtml_list.Append("<div class=\"rank-number\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"" + crownColor + "\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14\"/></svg></div>");
            } else {
                strhtml_list.Append("<div class=\"rank-number\">#" + index.ToString() + "</div>");
            }
            
            // 用户名
            strhtml_list.Append("<div class=\"rank-name\"><a href=\"" + this.http_start + "bbs/userinfo.aspx?siteid=" + this.siteid + "&classid=" + this.classid + "&touserid=" + listVo[i].userid + "\">" + WapTool.GetColorNickName(listVo[i].idname, listVo[i].nickname, lang, ver) + "</a></div>");
            
            // 数值
            strhtml_list.Append("<div class=\"rank-value\">");
            string label = "";
            string value = "";
            
            if(this.stype == "0") { 
                label = "帖子";
                value = listVo[i].bbsCount.ToString();
            }
            else if(this.stype == "1") {
                label = "回复";
                value = listVo[i].bbsReCount.ToString();
            }
            else if(this.stype == "2") {
                label = siteVo.sitemoneyname;
                value = listVo[i].money.ToString();
            }
            else if(this.stype == "3") {
                label = "经验";
                value = listVo[i].expr.ToString();
            }
            else if(this.stype == "4") {
                label = "人气";
                value = listVo[i].zoneCount.ToString();
            }
            
            strhtml_list.Append("<div class=\"rank-value-label\">" + label + "</div>");
            strhtml_list.Append("<div class=\"rank-value-number\">" + value + "</div>");
            strhtml_list.Append("</div>");
            
            strhtml_list.Append("</div>");
        }
        
        // 如果当前用户未在当前页显示且有排名，且排名大于当前页结束排名(即排名更差)，则在底部显示
        if (!currentUserShown && CurrentUserRank > 0 && userVo != null && CurrentUserRank > pageEndRank)
        {
            strhtml_list.Append("<div class=\"rank-item active\">");
            
            // 排名
            strhtml_list.Append("<div class=\"rank-number\">#" + CurrentUserRank.ToString() + "</div>");
            
            // 用户名
            strhtml_list.Append("<div class=\"rank-name\"><a href=\"" + this.http_start + "bbs/userinfo.aspx?siteid=" + this.siteid + "&classid=" + this.classid + "&touserid=" + userVo.userid + "\">" + WapTool.GetColorNickName(userVo.idname, userVo.nickname, lang, ver) + "</a></div>");
            
            // 数值
            strhtml_list.Append("<div class=\"rank-value\">");
            string label = "";
            string value = "";
            
            if(this.stype == "0") { 
                label = "帖子";
                value = userVo.bbsCount.ToString();
            }
            else if(this.stype == "1") {
                label = "回复";
                value = userVo.bbsReCount.ToString();
            }
            else if(this.stype == "2") {
                label = siteVo.sitemoneyname;
                value = userVo.money.ToString();
            }
            else if(this.stype == "3") {
                label = "经验";
                value = userVo.expr.ToString();
            }
            else if(this.stype == "4") {
                label = "人气";
                value = userVo.zoneCount.ToString();
            }
            
            strhtml_list.Append("<div class=\"rank-value-label\">" + label + "</div>");
            strhtml_list.Append("<div class=\"rank-value-number\">" + value + "</div>");
            strhtml_list.Append("</div>");
            
            strhtml_list.Append("</div>");
        }
        
        strhtml_list.Append("</div>");
        
        // 修改分页部分
        if(!string.IsNullOrEmpty(linkURL)) {
            strhtml_list.Append("<div class=\"card-footer\">");
            strhtml_list.Append("<div class=\"pagination\">");
            
            // 计算总页数
            int totalPages = (int)(total / pageSize + (total % pageSize > 0 ? 1 : 0));
            bool isFirstPage = CurrentPage == 1;
            bool isLastPage = CurrentPage >= totalPages;
            
            // 上一页按钮
            string prevPageUrl = isFirstPage ? "javascript:void(0)" : 
                this.http_start + "bbs/book_list_rank.aspx?getTotal=" + total + "&stype=" + stype + "&page=" + (CurrentPage - 1);
            string prevBtnStyle = isFirstPage ? " style=\"opacity:0.5;cursor:not-allowed;\"" : "";
            
            strhtml_list.Append("<button class=\"btn\"" + prevBtnStyle + " onclick=\"" + (isFirstPage ? "return false" : "window.location.href='" + prevPageUrl + "'") + "\">");
            strhtml_list.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#378d8d\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"15 18 9 12 15 6\"/></svg>");
            strhtml_list.Append("</button>");

            // 页码输入框
            strhtml_list.Append("<div>第 <input type=\"text\" class=\"page-input\" value=\"" + CurrentPage + "\"> / " + totalPages + " 页</div>");

            // 下一页按钮
            string nextPageUrl = isLastPage ? "javascript:void(0)" :
                this.http_start + "bbs/book_list_rank.aspx?getTotal=" + total + "&stype=" + stype + "&page=" + (CurrentPage + 1);
            string nextBtnStyle = isLastPage ? " style=\"opacity:0.5;cursor:not-allowed;\"" : "";
            
            strhtml_list.Append("<button class=\"btn\"" + nextBtnStyle + " onclick=\"" + (isLastPage ? "return false" : "window.location.href='" + nextPageUrl + "'") + "\">");
            strhtml_list.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"#378d8d\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><polyline points=\"9 18 15 12 9 6\"/></svg>");
            strhtml_list.Append("</button>");

            strhtml_list.Append("</div>");
            strhtml_list.Append("</div>");

            // 添加页码跳转的表单
            strhtml_list.Append("<form name=\"go\" method=\"get\" action=\"" + this.http_start + "bbs/Book_List_Rank.aspx\" style=\"display:none;\">");
            strhtml_list.Append("<input type=\"hidden\" name=\"action\" value=\"class\">");
            strhtml_list.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\">");
            strhtml_list.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\">");
            strhtml_list.Append("<input type=\"hidden\" name=\"getTotal\" value=\"" + total + "\">");
            strhtml_list.Append("<input type=\"hidden\" name=\"stype\" value=\"" + stype + "\">");
            strhtml_list.Append("<input type=\"hidden\" name=\"page\" id=\"pageInputHidden\">");
            strhtml_list.Append("</form>");

            // 添加页码跳转的JavaScript
            strhtml_list.Append("<script>");
            strhtml_list.Append("document.querySelector('.page-input').addEventListener('keypress', function(e) {");
            strhtml_list.Append("    if(e.key === 'Enter') {");
            strhtml_list.Append("        e.preventDefault();");
            strhtml_list.Append("        var page = parseInt(this.value);");
            strhtml_list.Append("        if(page > 0) {");
            strhtml_list.Append("            document.getElementById('pageInputHidden').value = page;");
            strhtml_list.Append("            document.forms['go'].submit();");
            strhtml_list.Append("        }");
            strhtml_list.Append("    }");
            strhtml_list.Append("});");
            strhtml_list.Append("</script>");
        }
        
        strhtml_list.Append("</div>");
        
        if (isWebHtml == "")
        {
            strhtml.Append(strhtml_list);
        }
        else
        {
            Response.Clear();
            Response.Write(WapTool.ToWML(isWebHtml.Replace("[view]", strhtml_list.ToString()), wmlVo));
            Response.End();
        }

        if (!string.IsNullOrEmpty(downLink))
        {
            strhtml.Append(downLink);
        }
        else
        {
        }
        
        Response.Write(WapTool.ToWML(strhtml.ToString(), wmlVo));
    }
    
    Response.Write(ERROR);
    Response.Write(WapTool.showDown(wmlVo));
%>