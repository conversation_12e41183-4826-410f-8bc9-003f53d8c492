body {
    min-height: 100vh;
    background-color: #f9fafb;
    padding-bottom: 1rem;
    max-width: 720px;
    margin: auto;
    box-shadow: 0 2px 1px -1px rgba(0, 0, 0, .2), 0 1px 1px 0 rgba(0, 0, 0, .14), 0 1px 3px 0 rgba(0, 0, 0, .12);
}
/* 重置和基础样式 */
.modern-identity-purchase * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

    /* 头部样式 */
    .modern-identity-purchase .header {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 0.875rem 1rem;
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .modern-identity-purchase .back-button {
        padding: 0.375rem;
        border-radius: 9999px;
        background: transparent;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #4d5562;
    }

        .modern-identity-purchase .back-button:hover {
            background-color: #f3f4f6;
        }

    .modern-identity-purchase .header-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        margin-left: 0.75rem;
    }

    /* 选项卡样式 */
    .modern-identity-purchase .tab-bar {
        display: flex;
        margin: 1.25rem 1rem;
        background-color: #f3f4f6;
        border-radius: 0.5rem;
        padding: 0.25rem;
    }

    .modern-identity-purchase .tab-button {
        flex: 1;
        padding: 0.625rem 0;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        text-align: center;
        border: none;
        background: transparent;
        cursor: pointer;
        transition: all 0.2s;
        color: #4b5563;
        text-decoration: none;
    }

        .modern-identity-purchase .tab-button:hover {
            color: #1f2937;
        }

        .modern-identity-purchase .tab-button.active {
            background-color: white;
            color: #059669;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

    /* 内容区域样式 */
    .modern-identity-purchase .content {
        padding: 0 1rem;
        margin-top: 1.25rem;
    }

    .modern-identity-purchase .card {
        background-color: white;
        border-radius: 0.75rem;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        padding: 1.25rem;
        margin-bottom: 1.25rem;
    }

    .modern-identity-purchase .card-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

        .modern-identity-purchase .card-title .icon {
            margin-right: 0.5rem;
            color: #059669;
        }

    .modern-identity-purchase .card-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.875rem 0;
        border-bottom: 1px solid #f3f4f6;
    }

        .modern-identity-purchase .card-row:last-child {
            border-bottom: none;
        }

    .modern-identity-purchase .card-label {
        font-size: 0.875rem;
        color: #4b5563;
        display: flex;
        align-items: center;
    }

    .modern-identity-purchase .card-value {
        font-weight: 500;
        color: #1f2937;
        display: flex;
        align-items: center;
    }

    .modern-identity-purchase .green-text, .modal-overlay .green-text {
        color: #059669;
    }

    .modern-identity-purchase .red-text, .modal-overlay .red-text {
        color: #ef4444;
    }

    .modern-identity-purchase .blue-text, .modal-overlay .blue-text {
        color: #228aff;
    }

    .modern-identity-purchase .purple-text, .modal-overlay .purple-text {
        color: #c000ff;
    }

    .modern-identity-purchase .pink-text, .modal-overlay .pink-text {
        color: #ff6363;
    }

    .modern-identity-purchase .gold-text, .modal-overlay .gold-text {
        color: #fa6700;
    }

    .modern-identity-purchase .large-text {
        font-size: 1.125rem;
        font-weight: 600;
    }

    /* 按钮样式 */
    .modern-identity-purchase .button {
        padding: 0 .5rem;
        border-radius: 0.375rem;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .modern-identity-purchase .button-outline {
        border: 1px solid #059669;
        color: #059669;
        background-color: transparent;
        margin-left: 0.75rem;
        text-decoration: none;
    }

        .modern-identity-purchase .button-outline:hover {
            background-color: #ecfdf5;
        }

    .modern-identity-purchase .button-primary {
        background-color: #059669;
        color: white;
        border: none;
        width: 100%;
        padding: 0.875rem;
        border-radius: 0.5rem;
        font-weight: 500;
        margin-top: 2rem;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

        .modern-identity-purchase .button-primary:hover {
            background-color: #047857;
        }

        .modern-identity-purchase .button-primary:active {
            transform: scale(0.99);
            box-shadow: none;
        }

    /* 计数器样式 */
    .modern-identity-purchase .counter {
        display: flex;
        align-items: center;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        overflow: hidden;
    }

    .modern-identity-purchase .counter-button {
        padding: 0.375rem 0.875rem;
        color: #4b5563;
        background: transparent;
        border: none;
        cursor: pointer;
    }

        .modern-identity-purchase .counter-button:hover {
            background-color: #f9fafb;
        }

    .modern-identity-purchase .counter-value {
        padding: 0.375rem 1rem;
        color: #1f2937;
        font-weight: 500;
        min-width: 40px;
        text-align: center;
        border: none !important;
    }

    /* 输入框样式 */
    .modern-identity-purchase .input-wrapper {
        position: relative;
    }

    .modern-identity-purchase .input {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        font-size: 1rem;
        transition: all 0.2s;
    }

        .modern-identity-purchase .input#password:focus {
            outline: none;
            border-color: transparent;
            box-shadow: 0 0 0 2px #059669;
        }

    .modern-identity-purchase .input-icon {
        position: absolute;
        right: 0.875rem;
        top: 50%;
        transform: translateY(-50%);
        color: #9ca3af;
        cursor: pointer;
    }

        .modern-identity-purchase .input-icon:hover {
            color: #6b7280;
        }

    /* 信息图标和工具提示样式 */
    .modern-identity-purchase .info-icon {
        width: 1rem;
        height: 1rem;
        border-radius: 9999px;
        background-color: #e5e7eb;
        color: #4b5563;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        font-weight: 700;
        margin-left: 0.375rem;
        cursor: pointer;
        position: relative;
    }

    .modern-identity-purchase .tooltip {
        position: absolute;
        bottom: 100%;
        margin-bottom: 0.5rem;
        background-color: #1f2937;
        color: white;
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
        border-radius: 0.5rem;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s;
        white-space: nowrap;
        z-index: 20;
        font-weight: normal;
    }

    .modern-identity-purchase .tooltip-left {
        left: 0;
    }

    .modern-identity-purchase .tooltip-right {
        right: 0;
    }

    .modern-identity-purchase .info-icon:hover .tooltip,
    .modern-identity-purchase .calendar-icon:hover .tooltip {
        opacity: 1;
    }

    /* 日历图标样式 */
    .modern-identity-purchase .calendar-icon {
        margin-right: 0.375rem;
        color: #6b7280;
        cursor: pointer;
        position: relative;
    }

    /* 工具类 */
    .modern-identity-purchase .flex {
        display: flex;
    }

    .modern-identity-purchase .items-center {
        align-items: center;
    }

    .modern-identity-purchase .ml-1 {
        margin-left: 0.25rem;
    }

    /* ASP.NET Web Forms 特定样式 */
    .modern-identity-purchase input[type="number"],
    .modern-identity-purchase input[type="text"],
    .modern-identity-purchase input[type="password"] {
        width: 100%;
        padding: 0.875rem 1rem;
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        font-size: 1rem;
        transition: all 0.2s;
    }

    /* 适配 WAP 和 Web 视图 */
    .modern-identity-purchase .btBox {
        margin-top: 2rem;
        padding: 0 1rem;
    }

    .modern-identity-purchase .bt2 {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1rem;
    }

        .modern-identity-purchase .bt2 a {
            flex: 1;
            text-align: center;
            padding: 0.75rem 0;
            background-color: #f3f4f6;
            color: #4b5563;
            text-decoration: none;
            border-radius: 0.5rem;
            margin: 0 0.5rem;
            font-weight: 500;
        }

            .modern-identity-purchase .bt2 a:first-child {
                margin-left: 0;
            }

            .modern-identity-purchase .bt2 a:last-child {
                margin-right: 0;
            }

            .modern-identity-purchase .bt2 a:hover {
                background-color: #e5e7eb;
            }

    /* 错误提示样式 */
    .modern-identity-purchase .tip {
        padding: 1rem;
        margin: 1rem 0;
        background-color: #fef2f2;
        border-radius: 0.5rem;
        color: #991b1b;
    }

        .modern-identity-purchase .tip b {
            font-weight: 600;
        }

    /* 成功提示样式 */
    .modern-identity-purchase .success {
        padding: 1rem;
        margin: 1rem 0;
        background-color: #ecfdf5;
        border-radius: 0.5rem;
        color: #065f46;
    }

        .modern-identity-purchase .success b {
            font-weight: 600;
        }

/* 适配小屏幕设备 */
@media (max-width: 640px) {
    .modern-identity-purchase .card {
        padding: 1rem;
    }

    .modern-identity-purchase .header {
        padding: 0.75rem;
    }

    .modern-identity-purchase .tab-bar {
        margin: 1rem 0.75rem;
    }

    .modern-identity-purchase .content {
        padding: 0 0.75rem;
    }
}

/* 模态弹窗样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
}

    .modal-overlay.active {
        opacity: 1;
        visibility: visible;
    }

.modal {
    background-color: white;
    border-radius: 0.75rem;
    width: 90%;
    max-width: 400px;
    padding: 1.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(20px);
    transition: transform 0.3s;
}

.modal-overlay.active .modal {
    transform: translateY(0);
}

.modal-icon-wrapper {
    width: 4rem;
    height: 4rem;
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.success-icon-wrapper {
    background-color: #ecfdf5;
    color: #059669;
}

.error-icon-wrapper {
    background-color: #fee2e2;
    color: #ef4444;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 1rem;
    color: #1f2937;
}

.modal-message {
    text-align: center;
    color: #6b7280;
    margin-bottom: 1.5rem;
    font-size: 0.94rem;
}

.modal-details {
    background-color: #f9fafb;
    border-radius: 0.5rem;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
}

.modal-detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

    .modal-detail-row:last-child {
        margin-bottom: 0;
    }

.modal-detail-label {
    color: #6b7280;
    font-size: 0.875rem;
}

.modal-detail-value {
    font-weight: 600;
    color: #1f2937;
}

.modal-actions {
    display: flex;
    gap: 0.75rem;
}

    .modal-actions .button {
        flex: 1;
        font-size: 0.94rem;
        padding: 0.75rem 0;
        cursor: pointer;
    }

.button-secondary {
    background-color: #f3f4f6;
    color: #4b5563;
    border: none;
    padding: 0.875rem;
    border-radius: 0.5rem;
    font-weight: 500;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

    .button-secondary:hover {
        background-color: #e5e7eb;
    }

.button-danger {
    background-color: #ef4444;
    color: white;
    border: none;
    padding: 0.875rem;
    border-radius: 0.5rem;
    font-weight: 500;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

    .button-danger:hover {
        background-color: #dc2626;
    }

#success-action-button {
    background-color: #059669;
    color: white;
    border: none;
    font-weight: 500;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border-radius: 0.5rem;
}

    #success-action-button:hover {
        background-color: #047857;
    }

/* 隐藏数字输入框的上下箭头 */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox 兼容性 */
input[type="number"] {
    -moz-appearance: textfield;
}

/* 现代浏览器通用方案 */
input[type="number"] {
    appearance: textfield;
}

/* 隐藏浏览器原生的密码显示/隐藏图标 */
input[type="password"]::-ms-reveal {
    display: none;
}

/* BuyGroup.html 特有样式 - 开始 */

/* 基础重置和通用样式（不在.modern-identity-purchase内时使用）*/
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

html {
    background-color: #E8E8E8;
}


/* 通用头部样式 */
.header {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 0.875rem 1rem;
    display: flex;
    align-items: center;
}

.back-button {
    padding: 0.375rem;
    color: #4d5562;
    border-radius: 9999px;
    background: transparent;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    -webkit-user-select: none;
    user-select: none;
}

.back-button:hover {
    background-color: #f3f4f6;
}

.header-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-left: 0.75rem;
}

/* 面包屑样式 */
.breadcrumb {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: white;
    border-bottom: 1px solid #f3f4f6;
    font-size: 0.875rem;
    color: #6b7280;
}

.breadcrumb a {
    color: #6b7280;
    text-decoration: none;
}

.breadcrumb a:hover {
    color: #059669;
}

.breadcrumb-separator {
    margin: 0 0.5rem;
    color: #d1d5db;
}

/* 身份网格和卡片样式 */
.identity-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 1rem;
    margin-top: 1rem;
}

@media (min-width: 768px) {
    .identity-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.identity-card {
    background-color: white;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
    position: relative;
    display: flex;
    flex-direction: column;
}

.identity-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.identity-card.active {
    border: 2px solid #059669;
}

.identity-card-header {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.identity-name {
    font-weight: 600;
    font-size: 1.125rem;
}

.identity-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    background-color: #f3f4f6;
    color: #4b5563;
}

.identity-badge .icon {
    margin-right: 0.25rem;
}

.identity-card-body {
    padding: 1rem;
    flex-grow: 1;
}

.identity-price {
    font-size: 1.25rem;
    font-weight: 600;
    color: #059669;
    margin-bottom: 0.75rem;
}

.identity-price-alt {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1rem;
}

.identity-privileges {
    margin-top: 1rem;
}

.identity-privileges-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: #4b5563;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.identity-privileges-title .icon {
    margin-right: 0.375rem;
    color: #059669;
}

.identity-privilege {
    display: flex;
    align-items: center;
    margin-bottom: 0.375rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.identity-privilege .icon {
    margin-right: 0.375rem;
    color: #059669;
    flex-shrink: 0;
}

.identity-card-footer {
    padding: 1rem;
    border-top: 1px solid #f3f4f6;
}

.purchase-button {
    width: 100%;
    padding: 0.75rem;
    border-radius: 0.5rem;
    background-color: #059669;
    color: white;
    font-weight: 500;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
    -webkit-user-select: none;
    user-select: none;
}

.purchase-button:hover {
    background-color: #047857;
}

.purchase-button:active {
    transform: scale(0.98);
}

.purchase-button .icon {
    margin-right: 0.375rem;
}

/* 确保颜色相关样式能够被页面全局访问 */
.green-text { color: #25a444; }
.red-text { color: #FF0000; }
.blue-text { color: #228aff; }
.purple-text { color: #c000ff; }
.pink-text { color: #ff6363; }
.pink-purple-text { color: #ff00c0; }
.gold-text { color: #fa6700; }
.gold-premium-text { color: #FF7F00; }
.basic-purple-text { color: #9c63ce; }

/* 当前标签样式 */
.current-tag {
    display: inline-flex;
    align-items: center;
    margin-left: 0.5rem;
    background-color: #ef4444;
    color: white;
    padding: 0.125rem 0.375rem;
    font-size: 0.625rem;
    font-weight: 500;
    border-radius: 0.25rem;
    vertical-align: middle;
}

/* 颜色选择器样式 */
.color-selector {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin: 1rem 0;
}

.color-option {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 9999px;
    cursor: pointer;
    position: relative;
    transition: transform 0.2s, box-shadow 0.2s;
    border: 2px solid transparent;
    -webkit-user-select: none;
    user-select: none;
}

.color-option:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.color-option.selected {
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(31, 41, 55, 0.3);
}

.color-option.selected::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1rem;
    height: 1rem;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.color-green { background-color: #25a444; }
.color-red { background-color: #FF0000; }
.color-blue { background-color: #228aff; }
.color-purple { background-color: #9c63ce; }
.color-pink { background-color: #ff6363; }
.color-pink-purple { background-color: #ff00c0; }

.color-name-display {
    font-size: 1rem;
    font-weight: 500;
    margin: 0.5rem 0;
    transition: color 0.3s;
    display: flex;
    align-items: center;
}

/* 常用工具类 - 确保即使不在.modern-identity-purchase内也能正常工作 */
.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

/* 响应式调整 */
@media (max-width: 640px) {
    .header {
        padding: 0.75rem;
    }
}

/* BuyGroup.html 特有样式 - 结束 */