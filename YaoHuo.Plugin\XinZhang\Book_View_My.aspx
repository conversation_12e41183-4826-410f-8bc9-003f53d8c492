﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_View_My.aspx.cs" Inherits="YaoHuo.Plugin.XinZhang.Book_View_My" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    Response.Write(WapTool.showTop("管理我的勋章", wmlVo));
    bool isexit = false;
    //显示广告
    //if (adVo.threeShowTop != "")
    //{
    //    strhtml.Append(adVo.threeShowTop );
    //}
    strhtml.Append("<div class=\"title\">管理我的勋章</div>");

    if (this.INFO == "OK")
    {
        strhtml.Append("<div class=\"tip\"><b>操作成功！</b></div>");
    }
    else if (this.INFO == "NO")
    {
        strhtml.Append("<div class=\"tip\"><b>操作失败！</b></div>");
    }
    else if (this.INFO == "NOPASS")
    {
        strhtml.Append("<div class=\"tip\"><b>密码错误，请重新输入</b></div>");
    }
    if (1 == 1)
    {
        strhtml.Append("<div class=\"content\">");
        strhtml.Append("<form name=\"f\" action=\"" + http_start + "xinzhang/book_view_my.aspx\" method=\"post\">");
        strhtml.Append("<span style='font-size:90%;'>操作存在风险，丢失无法找回</span>");
        strhtml.Append("<br/>");
        strhtml.Append("<input type=\"password\" name=\"pw\" style=\"width:66%;\" placeholder=\"确认操作请输入密码\" value=\"" + pw + "\" size=\"15\"/>");
        strhtml.Append("<br/>");
        strhtml.Append("<input class=\"btn\" type=\"submit\" name=\"g\" value=\"隐藏全部\"/>");
        strhtml.Append("<input class=\"btn\" type=\"submit\" name=\"g\" value=\"显示全部\"/>");
        strhtml.Append("<br/><hr/><br/>");
        //显示的勋章
        string[] arry = userVo.moneyname.Split('|');
        for (int i = 0; i < arry.Length; i++)
        {
            if (arry[i].Trim() != "")
            {
                if (arry[i].Trim().IndexOf("XinZhang") >= 0)
                {
                    strhtml.Append("<img src=\"" + this.http_start + arry[i] + "\" alt=\".\"/><br/>");
                }
                else if (arry[i].Trim().StartsWith("/") || arry[i].Trim().StartsWith("http://"))
                {
                    strhtml.Append("<img src=\"" + arry[i].Trim() + "\" alt=\".\"/><br/>");
                }
                else
                {
                    strhtml.Append("<img src=\"" + this.http_start + "bbs/medal/" + arry[i] + "\" alt=\".\"/><br/>");
                }
                isexit = true;
                //strhtml.Append("<input type=\"hidden\" name=\"id\" value=\"" + arry[i] + "\" />");
                //strhtml.Append("<input class=\"btn\" type=\"submit\" name=\"g\" value=\"删除_" + arry[i] + "\"/>");
                strhtml.Append("<input class=\"btn\" type=\"submit\" name=\"g\" value=\"隐藏_" + arry[i] + "\"/>");
                strhtml.Append("<hr/><br/>");
            }
        }
        //隐藏的勋章
        string[] hideArry = this.HideMoneyName.Split('|');
        for (int i = 0; i < hideArry.Length; i++)
        {
            if (hideArry[i].Trim() != "")
            {
                if (hideArry[i].Trim().IndexOf("XinZhang") >= 0)
                {
                    strhtml.Append("<img src=\"" + this.http_start + hideArry[i] + "\" alt=\".\"/><br/>");
                }
                else if (hideArry[i].Trim().StartsWith("/") || hideArry[i].Trim().StartsWith("http://"))
                {
                    strhtml.Append("<img src=\"" + hideArry[i].Trim() + "\" alt=\".\"/><br/>");
                }
                else
                {
                    strhtml.Append("<img src=\"" + this.http_start + "bbs/medal/" + hideArry[i] + "\" alt=\".\"/><br/>");
                }
                isexit = true;
                strhtml.Append("<input class=\"btn\" type=\"submit\" name=\"g\" value=\"显示_" + hideArry[i] + "\"/>");
                strhtml.Append("<br/><br/>");
            }
        }
        strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\" />");
        strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\" />");
        strhtml.Append("<input type=\"hidden\" name=\"lpage\" value=\"" + (this.lpage) + "\" />");
        strhtml.Append("<input type=\"hidden\" name=\"ordertype\" value=\"" + ordertype + "\" />");
        strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"godel\" />");
        strhtml.Append("</form>");
        strhtml.Append("</div>");
    }
    if (isexit == false)
    {
        strhtml.Append("<div class=\"tip\">暂时没有我的勋章！</div>");
    }
    string isWebHtml = this.ShowWEB_view(this.classid);
    strhtml.Append("<div class=\"btBox\"><div class=\"bt2\">");
    strhtml.Append("<a href=\"" + this.http_start + "XinZhang/book_list.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + this.lpage + "&amp;ordertype=" + this.ordertype + "" + "\">返回上级</a> ");
    strhtml.Append("<a href=\"" + this.http_start + "\">返回首页</a>");
    strhtml.Append("</div></div>");
    if (isWebHtml != "")
    {
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml, wmlVo).Replace("[view]", strhtml.ToString()));
        Response.End();
    }
    strhtml.Append(ERROR);
    Response.Write(WapTool.ToWML(strhtml.ToString(), wmlVo));
    Response.Write(WapTool.showDown(wmlVo));
%>