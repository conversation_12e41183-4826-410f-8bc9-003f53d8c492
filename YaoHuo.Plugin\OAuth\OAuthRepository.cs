using System;
using System.Collections.Generic;
using System.Linq;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.OAuth
{
    /// <summary>
    /// OAuth 数据访问实现（使用 DapperHelper）
    /// </summary>
    public class OAuthRepository : IOAuthRepository
    {
        private readonly string _connectionString;

        /// <summary>
        /// 数据库连接字符串（供OAuthService访问）
        /// </summary>
        public string ConnectionString => _connectionString;

        public OAuthRepository(string connectionString)
        {
            _connectionString = connectionString;
        }

        #region 客户端管理

        public OAuthClient GetClient(string clientId)
        {
            var sql = @"
SELECT app_id as AppId, app_name as AppName, app_description as AppDescription, 
       app_key_hash as AppKeyHash, app_key_salt as AppKeySalt, 
       redirect_uris as RedirectUris, allowed_scopes as AllowedScopes, 
       is_valid as IsValid, created_at as CreatedAt, updated_at as UpdatedAt
FROM oauth_clients 
WHERE app_id = @ClientId AND is_valid = 1";

            var parameters = new { ClientId = clientId };
            return DapperHelper.QueryFirstOrDefault<OAuthClient>(_connectionString, sql, parameters);
        }

        public List<OAuthClient> GetAllClients()
        {
            var sql = @"
SELECT app_id as AppId, app_name as AppName, app_description as AppDescription, 
       app_key_hash as AppKeyHash, app_key_salt as AppKeySalt, 
       redirect_uris as RedirectUris, allowed_scopes as AllowedScopes, 
       is_valid as IsValid, created_at as CreatedAt, updated_at as UpdatedAt
FROM oauth_clients 
ORDER BY created_at DESC";

            var result = DapperHelper.Query<OAuthClient>(_connectionString, sql);
            return result?.ToList() ?? new List<OAuthClient>();
        }

        public void CreateClient(OAuthClient client)
        {
            var sql = @"
INSERT INTO oauth_clients 
(app_id, app_name, app_description, app_key_hash, app_key_salt, redirect_uris, allowed_scopes, is_valid, created_at, updated_at)
VALUES 
(@AppId, @AppName, @AppDescription, @AppKeyHash, @AppKeySalt, @RedirectUris, @AllowedScopes, @IsValid, @CreatedAt, @UpdatedAt)";

            DapperHelper.Execute(_connectionString, sql, client);
        }

        public void UpdateClient(OAuthClient client)
        {
            var sql = @"
UPDATE oauth_clients 
SET app_name = @AppName, app_description = @AppDescription, 
    redirect_uris = @RedirectUris, allowed_scopes = @AllowedScopes, 
    is_valid = @IsValid, updated_at = @UpdatedAt
WHERE app_id = @AppId";

            DapperHelper.Execute(_connectionString, sql, client);
        }

        public bool DeleteClient(string clientId)
        {
            var sql = "DELETE FROM oauth_clients WHERE app_id = @ClientId";
            var parameters = new { ClientId = clientId };
            var result = DapperHelper.Execute(_connectionString, sql, parameters);
            return result > 0;
        }

        #endregion

        #region 授权码管理

        public void CreateAuthorizationCode(OAuthAuthorizationCode code)
        {
            var sql = @"
INSERT INTO oauth_authorization_codes 
(code, client_id, user_id, redirect_uri, scope, code_challenge, code_challenge_method, expires_at, created_at)
VALUES 
(@Code, @ClientId, @UserId, @RedirectUri, @Scope, @CodeChallenge, @CodeChallengeMethod, @ExpiresAt, @CreatedAt)";

            DapperHelper.Execute(_connectionString, sql, code);
        }

        public OAuthAuthorizationCode GetAuthorizationCode(string code, string clientId, string redirectUri)
        {
            var sql = @"
SELECT code as Code, client_id as ClientId, user_id as UserId, redirect_uri as RedirectUri, 
       scope as Scope, code_challenge as CodeChallenge, code_challenge_method as CodeChallengeMethod,
       expires_at as ExpiresAt, created_at as CreatedAt, used_at as UsedAt
FROM oauth_authorization_codes 
WHERE code = @Code AND client_id = @ClientId AND redirect_uri = @RedirectUri AND used_at IS NULL";

            var parameters = new { Code = code, ClientId = clientId, RedirectUri = redirectUri };
            return DapperHelper.QueryFirstOrDefault<OAuthAuthorizationCode>(_connectionString, sql, parameters);
        }

        public bool MarkAuthorizationCodeAsUsed(string code)
        {
            var sql = "UPDATE oauth_authorization_codes SET used_at = @UsedAt WHERE code = @Code";
            var parameters = new { Code = code, UsedAt = DateTime.UtcNow };
            var result = DapperHelper.Execute(_connectionString, sql, parameters);
            return result > 0;
        }

        #endregion

        #region 访问令牌管理

        public void CreateAccessToken(OAuthAccessToken token)
        {
            var sql = @"
INSERT INTO oauth_access_tokens 
(token_id, client_id, user_id, scope, expires_at, created_at, last_used_at, revoked_at, refresh_token, refresh_expires_at)
VALUES 
(@TokenId, @ClientId, @UserId, @Scope, @ExpiresAt, @CreatedAt, @LastUsedAt, @RevokedAt, @RefreshToken, @RefreshExpiresAt)";

            DapperHelper.Execute(_connectionString, sql, token);
        }

        /// <summary>
        /// 创建访问令牌（智能模式：自动撤销该用户在同一应用的现有令牌）
        /// </summary>
        /// <param name="token">新的访问令牌</param>
        /// <param name="revokeExisting">是否撤销现有令牌（默认true）</param>
        /// <returns>撤销的现有令牌数量</returns>
        public int CreateAccessTokenSmart(OAuthAccessToken token, bool revokeExisting = true)
        {
            int revokedCount = 0;
            
            if (revokeExisting)
            {
                // 先撤销该用户在同一应用的所有现有活跃令牌
                revokedCount = RevokeUserTokensForClient(token.UserId, token.ClientId);
            }
            
            // 创建新令牌
            CreateAccessToken(token);
            
            return revokedCount;
        }

        /// <summary>
        /// 创建访问令牌（激进模式：物理删除该用户在同一应用的现有令牌）
        /// </summary>
        /// <param name="token">新的访问令牌</param>
        /// <param name="deleteExisting">是否物理删除现有令牌（默认true）</param>
        /// <returns>删除的现有令牌数量</returns>
        public int CreateAccessTokenAggressive(OAuthAccessToken token, bool deleteExisting = true)
        {
            int deletedCount = 0;
            
            if (deleteExisting)
            {
                // 物理删除该用户在同一应用的所有现有令牌（不保留审计记录）
                var sql = @"
DELETE FROM oauth_access_tokens
WHERE user_id = @UserId
  AND client_id = @ClientId";

                var parameters = new {
                    UserId = token.UserId,
                    ClientId = token.ClientId
                };
                deletedCount = DapperHelper.Execute(_connectionString, sql, parameters);
            }
            
            // 创建新令牌
            CreateAccessToken(token);
            
            return deletedCount;
        }

        public OAuthAccessToken GetAccessToken(string tokenId)
        {
            var sql = @"
SELECT token_id as TokenId, client_id as ClientId, user_id as UserId, scope as Scope,
       expires_at as ExpiresAt, created_at as CreatedAt, last_used_at as LastUsedAt, 
       revoked_at as RevokedAt, refresh_token as RefreshToken, refresh_expires_at as RefreshExpiresAt
FROM oauth_access_tokens 
WHERE token_id = @TokenId";

            var parameters = new { TokenId = tokenId };
            return DapperHelper.QueryFirstOrDefault<OAuthAccessToken>(_connectionString, sql, parameters);
        }

        public bool RevokeAccessToken(string tokenId)
        {
            var sql = "UPDATE oauth_access_tokens SET revoked_at = @RevokedAt WHERE token_id = @TokenId";
            var parameters = new { TokenId = tokenId, RevokedAt = DateTime.UtcNow };
            var result = DapperHelper.Execute(_connectionString, sql, parameters);
            return result > 0;
        }

        public bool UpdateTokenLastUsed(string tokenId)
        {
            var sql = "UPDATE oauth_access_tokens SET last_used_at = @LastUsedAt WHERE token_id = @TokenId";
            var parameters = new { TokenId = tokenId, LastUsedAt = DateTime.UtcNow };
            var result = DapperHelper.Execute(_connectionString, sql, parameters);
            return result > 0;
        }

        public int RevokeUserTokensForClient(long userId, string clientId)
        {
            var sql = @"
UPDATE oauth_access_tokens
SET revoked_at = @RevokedAt
WHERE user_id = @UserId
  AND client_id = @ClientId
  AND revoked_at IS NULL";

            var parameters = new {
                UserId = userId,
                ClientId = clientId,
                RevokedAt = DateTime.UtcNow
            };
            return DapperHelper.Execute(_connectionString, sql, parameters);
        }

        public int RevokeAllUserTokens(long userId)
        {
            var sql = @"
UPDATE oauth_access_tokens
SET revoked_at = @RevokedAt
WHERE user_id = @UserId
  AND revoked_at IS NULL";

            var parameters = new {
                UserId = userId,
                RevokedAt = DateTime.UtcNow
            };
            return DapperHelper.Execute(_connectionString, sql, parameters);
        }

        #endregion

        #region 用户授权历史

        public List<dynamic> GetUserAuthorizations(long userId)
        {
            // 优化版：使用高效的窗口函数和聚合查询
            var sql = @"
SELECT 
    c.app_id           AS AppID,
    c.app_name         AS AppName,
    c.app_description  AS Remarks,
    t.user_id,
    MAX(t.created_at)                                        AS CreateTime,
    MAX(t.created_at)                                        AS last_authorized,
    SUM(CASE WHEN t.revoked_at IS NULL 
                  AND t.expires_at > GETDATE() THEN 1 ELSE 0 END) AS active_tokens,
    COUNT(*)                                                AS total_tokens,
    ROW_NUMBER() OVER (ORDER BY MAX(t.created_at) DESC)     AS AuthID
FROM oauth_clients c
INNER JOIN oauth_access_tokens t ON c.app_id = t.client_id
WHERE t.user_id = @UserId
GROUP BY c.app_id, c.app_name, c.app_description, t.user_id
HAVING SUM(CASE WHEN t.revoked_at IS NULL 
                    AND t.expires_at > GETDATE() THEN 1 ELSE 0 END) > 0
ORDER BY MAX(t.created_at) DESC";

            var parameters = new { UserId = userId };
            var result = DapperHelper.Query<dynamic>(_connectionString, sql, parameters);
            return result?.ToList() ?? new List<dynamic>();
        }

        /// <summary>
        /// 获取令牌和用户信息（优化版 - 单次JOIN查询）
        /// </summary>
        /// <param name="tokenId">令牌ID</param>
        /// <returns>令牌和用户信息的组合对象</returns>
        public dynamic GetTokenWithUserInfo(string tokenId)
        {
            var sql = @"
SELECT 
    t.scope, 
    t.expires_at,
    u.userid, 
    u.nickname, 
    u.expR,
    -- 在SQL中计算等级，减少应用层负担
    CASE 
        WHEN u.expR < 1 THEN 0
        WHEN u.expR < 1001 THEN 1
        WHEN u.expR < 2001 THEN 2
        WHEN u.expR < 5001 THEN 3
        WHEN u.expR < 10001 THEN 4
        WHEN u.expR < 50001 THEN 5
        WHEN u.expR < 100001 THEN 6
        WHEN u.expR < 200001 THEN 7
        WHEN u.expR < 300001 THEN 8
        WHEN u.expR < 500001 THEN 9
        WHEN u.expR < 800001 THEN 10
        WHEN u.expR < 1000001 THEN 11
        ELSE 12
    END as user_level
FROM oauth_access_tokens t
INNER JOIN [user] u ON t.user_id = u.userid AND u.siteid = 1000
WHERE t.token_id = @TokenId 
  AND t.revoked_at IS NULL 
  AND t.expires_at > GETUTCDATE()";

            var parameters = new { TokenId = tokenId };
            return DapperHelper.QueryFirstOrDefault<dynamic>(_connectionString, sql, parameters);
        }

        #endregion


    }
}