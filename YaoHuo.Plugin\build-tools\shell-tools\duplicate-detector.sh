#!/bin/bash
# duplicate-detector.sh - CSS重复代码自动检测工具
# 用于检测CSS中的重复定义、未使用类名和优化机会

echo "=== CSS重复代码检测工具 ==="
echo "检测时间: $(date)"
echo "检测范围: tailwind.config.js, style.css, Template目录"
echo ""

# 1. 检测重复的颜色值
echo "📊 1. 检测重复颜色值..."
echo "----------------------------------------"

echo "在 tailwind.config.js 中的颜色定义:"
if [ -f "tailwind.config.js" ]; then
    # 提取颜色定义并检查重复值
    grep -o "'[^']*':\s*'#[0-9a-fA-F]\{6\}'" tailwind.config.js | while read line; do
        color_name=$(echo "$line" | cut -d: -f1 | tr -d "'")
        color_value=$(echo "$line" | cut -d: -f2 | tr -d "' ")
        echo "$color_value $color_name"
    done | sort | awk '{
        if ($1 in colors) {
            colors[$1] = colors[$1] ", " $2
        } else {
            colors[$1] = $2
        }
    } END {
        for (color in colors) {
            count = gsub(/,/, "&", colors[color])
            if (count > 0) {
                print "🔴 重复颜色值 " color ": " colors[color]
            }
        }
    }'
else
    echo "❌ tailwind.config.js 文件不存在"
fi

echo ""
echo "在 style.css 中的硬编码颜色:"
if [ -f "style.css" ]; then
    grep -o "#[0-9a-fA-F]\{6\}" style.css | sort | uniq -c | awk '$1>1{print "🟡 颜色 " $2 " 出现 " $1 " 次"}'
else
    echo "❌ style.css 文件不存在"
fi

echo ""

# 2. 检测重复的类名定义
echo "📊 2. 检测重复类名定义..."
echo "----------------------------------------"

if [ -f "style.css" ]; then
    # 提取所有类名定义
    grep -n "^\s*\.[a-zA-Z][a-zA-Z0-9_-]*\s*{" style.css | while read line; do
        line_num=$(echo "$line" | cut -d: -f1)
        class_def=$(echo "$line" | cut -d: -f2- | sed 's/^\s*//' | sed 's/\s*{.*//')
        echo "$class_def $line_num"
    done | sort | awk '{
        if ($1 in classes) {
            classes[$1] = classes[$1] ", 第" $2 "行"
        } else {
            classes[$1] = "第" $2 "行"
        }
    } END {
        for (class in classes) {
            count = gsub(/,/, "&", classes[class])
            if (count > 0) {
                print "🔴 重复类名定义 " class ": " classes[class]
            }
        }
    }'
else
    echo "❌ style.css 文件不存在"
fi

echo ""

# 3. 检测疑似未使用的自定义类名
echo "📊 3. 检测疑似未使用的自定义类名..."
echo "----------------------------------------"

if [ -f "style.css" ] && [ -d "../Template" ]; then
    # 提取自定义类名（排除Tailwind标准类名）
    CUSTOM_CLASSES=$(grep -o "\.[a-zA-Z][a-zA-Z0-9_-]*" style.css | \
        grep -v "^\.bg-\|^\.text-\|^\.border-\|^\.p-\|^\.m-\|^\.w-\|^\.h-\|^\.flex\|^\.grid\|^\.rounded\|^\.shadow" | \
        sort | uniq)
    
    echo "检查自定义类名使用情况:"
    unused_count=0
    used_count=0
    
    for class in $CUSTOM_CLASSES; do
        class_name=${class#.}
        
        # 在模板文件中搜索
        template_usage=$(find ../Template -name "*.hbs" -exec grep -l "$class_name" {} \; 2>/dev/null | wc -l)
        
        # 在ASPX文件中搜索
        aspx_usage=$(find .. -name "*.aspx" -exec grep -l "$class_name" {} \; 2>/dev/null | wc -l)
        
        # 在CS文件中搜索
        cs_usage=$(find .. -name "*.cs" -exec grep -l "$class_name" {} \; 2>/dev/null | wc -l)
        
        total_usage=$((template_usage + aspx_usage + cs_usage))
        
        if [ $total_usage -eq 0 ]; then
            echo "⚠️  $class - 未发现使用"
            unused_count=$((unused_count + 1))
        else
            echo "✅ $class - 在 $total_usage 个文件中使用 (模板:$template_usage, ASPX:$aspx_usage, CS:$cs_usage)"
            used_count=$((used_count + 1))
        fi
    done
    
    echo ""
    echo "📈 统计结果:"
    echo "   已使用的自定义类名: $used_count"
    echo "   疑似未使用的类名: $unused_count"
    
    if [ $unused_count -gt 0 ]; then
        echo "   💡 建议: 进一步验证疑似未使用的类名，确认后可以安全删除"
    fi
else
    echo "❌ 缺少必要文件或目录"
fi

echo ""

# 4. 检测功能相同但名称不同的定义
echo "📊 4. 检测语义重复的颜色定义..."
echo "----------------------------------------"

if [ -f "tailwind.config.js" ]; then
    echo "检查值相同但名称不同的颜色定义:"
    grep -o "'[^']*':\s*'#[0-9a-fA-F]\{6\}'" tailwind.config.js | \
    awk -F: '{
        name = $1
        gsub(/['"'"']/, "", name)
        value = $2
        gsub(/['"'"' ]/, "", value)
        if (value in colors) {
            colors[value] = colors[value] ", " name
        } else {
            colors[value] = name
        }
    } END {
        for (color in colors) {
            count = gsub(/,/, "&", colors[color])
            if (count > 0) {
                print "🟡 颜色值 " color " 被多个名称使用: " colors[color]
                print "   💡 建议: 考虑统一为语义化变量或确认是否真的需要不同名称"
            }
        }
    }'
else
    echo "❌ tailwind.config.js 文件不存在"
fi

echo ""

# 5. 生成优化建议
echo "📊 5. 优化建议总结..."
echo "----------------------------------------"

# 计算当前CSS文件大小
if [ -f "../Template/CSS/output.css" ]; then
    css_size=$(stat -c%s "../Template/CSS/output.css" 2>/dev/null || stat -f%z "../Template/CSS/output.css" 2>/dev/null || echo "未知")
    echo "📏 当前CSS输出文件大小: $css_size bytes"
fi

echo ""
echo "🎯 优化建议:"
echo "1. 优先处理重复的类名定义（🔴 标记的项目）"
echo "2. 统一值相同但名称不同的颜色定义（🟡 标记的项目）"
echo "3. 验证并清理未使用的自定义类名（⚠️ 标记的项目）"
echo "4. 考虑建立设计令牌体系，减少硬编码颜色值"
echo "5. 定期运行此检测工具，防止新的重复代码产生"

echo ""
echo "✅ 检测完成！"
echo "💡 提示: 在进行任何清理操作前，请确保有完整的备份"
