﻿using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using System;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.Games.ChuiNiu
{
    public class Doit : MyPageWap
    {
        private string string_0 = PubConstant.GetAppString("InstanceName");

        public string KL_CheckIPTime = PubConstant.GetAppString("KL_CheckIPTime");

        public string action = "";

        public string page = "";

        public string INFO = "";

        public string ERROR = "";

        public string mymoney = "";

        public string myanswer = "";

        public wap2_games_config_Model configVo = new wap2_games_config_Model();

        public wap2_games_chuiniu_Model bookVo = new wap2_games_chuiniu_Model();

        public long min = 0L;

        public long max = 0L;

        public long per = 0L;

        public string id = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            IsLogin(userid, GetUrlQueryString());
            NeedPassWordToAdminNew();
            wap2_games_config_BLL wap2_games_config_BLL = new wap2_games_config_BLL(string_0);
            configVo = wap2_games_config_BLL.GetModel("gameen='chuiniu' and siteid=" + siteid);
            if (configVo == null)
            {
                configVo = new wap2_games_config_Model();
                configVo.siteid = siteVo.siteid;
                configVo.gameEn = "chuiniu";
                configVo.gameCn = "吹牛";
                configVo.config = "100|20000|95|10|5";
                configVo.todayTimes = 0L;
                configVo.todayMoney = 0L;
                configVo.updateTime = DateTime.Now;
                configVo.addtime = DateTime.Now;
                wap2_games_config_BLL.Add(configVo);
            }
            try
            {
                min = long.Parse(WapTool.GetArryString(configVo.config, '|', 0));
                max = long.Parse(WapTool.GetArryString(configVo.config, '|', 1));
                per = long.Parse(WapTool.GetArryString(configVo.config, '|', 2));
            }
            catch (Exception)
            {
                ShowTipInfo("此游戏还没有配置，请联系站长配置！", "games/gamesindex.aspx?siteid=" + siteid + "&amp;classid=" + classid);
            }
            if (id.IsNull())
            {
                INFO = "NULL";
                return;
            }
            wap2_games_chuiniu_BLL wap2_games_chuiniu_BLL = new wap2_games_chuiniu_BLL(string_0);
            bookVo = wap2_games_chuiniu_BLL.GetModel(long.Parse(id));
            if (bookVo == null)
            {
                ShowTipInfo("不存在此挑战！", "games/chuiniu/index.aspx?siteid=" + siteid + "&amp;classid=" + classid);
            }
            else if (bookVo.state != 0L)
            {
                ShowTipInfo("此挑战已被" + bookVo.winNickname + "(ID:" + bookVo.winUserid + ")抢去！", "games/chuiniu/index.aspx?siteid=" + siteid + "&amp;classid=" + classid);
            }
            else if (bookVo.userid == userVo.userid)
            {
                ShowTipInfo("自己挑战的只能由其它友友应战！", "games/chuiniu/index.aspx?siteid=" + siteid + "&amp;classid=" + classid);
            }
            if (!(action == "gomod"))
            {
                return;
            }
            try
            {
                myanswer = GetRequestValue("myanswer");
                if (myanswer != "1" && myanswer != "2")
                {
                    myanswer = "1";
                }

                if (bookVo.userid == userVo.userid)
                {
                    INFO = "ISME";
                    return;
                }
                if (userVo.money < bookVo.myMoney)
                {
                    INFO = "NOMONEY";
                    return;
                }

                long newState = myanswer == bookVo.myAnswer.ToString() ? 1L : 2L;

                // ✅ 使用DapperHelper进行安全的游戏状态更新
                string connectionString = PubConstant.GetConnectionString(string_0);
                string updateGameSql = @"UPDATE [wap2_games_chuiniu] SET
                                        state = @State,
                                        winUserid = @WinUserId,
                                        winNickname = @WinNickname,
                                        winAnswer = @WinAnswer,
                                        winTime = GETDATE()
                                        WHERE id = @Id AND state = 0";

                long updateResult = DapperHelper.Execute(connectionString, updateGameSql, new {
                    State = newState,
                    WinUserId = userVo.userid,
                    WinNickname = DapperHelper.LimitLength(userVo.nickname, 50),
                    WinAnswer = DapperHelper.SafeParseLong(myanswer, "答案"),
                    Id = DapperHelper.SafeParseLong(id, "游戏ID")
                });

                if (updateResult == 0)
                {
                    ShowTipInfo("抱歉，该挑战已经被其他人抢先应战了！",
                        "games/chuiniu/index.aspx?siteid=" + siteid + "&amp;classid=" + classid);
                    return;
                }

                bookVo = wap2_games_chuiniu_BLL.GetModel(long.Parse(id));

                if (bookVo.state == 1L)
                {
                    // ✅ 先计算新余额，避免SaveBankLog中的SELECT操作导致死锁
                    long winAmount = bookVo.myMoney * per / 100L;
                    long newBalance = userVo.money + winAmount;

                    // ✅ 使用DapperHelper进行安全的赢币操作
                    string updateWinnerMoneySql = "UPDATE [user] SET money = money + @WinAmount WHERE siteid = @SiteId AND userid = @UserId";
                    DapperHelper.Execute(connectionString, updateWinnerMoneySql, new {
                        WinAmount = winAmount,
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = userVo.userid
                    });

                    // ✅ 使用SaveBankLogWithBalance替换SaveBankLog，避免死锁
                    SaveBankLogWithBalance(userid.ToString(), "游戏赢币", winAmount.ToString(),
                        siteid, "系统", "吹牛打赢了", newBalance);
                    if (WapTool.GetArryString(siteVo.Version, '|', 46) == "1")
                    {
                        SaveMessage(siteid, "系统消息", "吹牛赢了", "吹牛赢:" + winAmount + "个" + siteVo.sitemoneyname, userid.ToString(), "", 1);
                    }
                    GamesClassManager.Tool.SaveGamesRank(siteVo.siteid, userVo.userid, userVo.nickname, "chuiniu", "1", winAmount);
                }
                else if (bookVo.state == 2L)
                {
                    // ✅ 先计算新余额，避免SaveBankLog中的SELECT操作导致死锁
                    long loseAmount = bookVo.myMoney;
                    long newBalance = userVo.money - loseAmount;

                    // ✅ 使用DapperHelper进行安全的输币操作
                    string updateLoserMoneySql = "UPDATE [user] SET money = money - @LoseAmount WHERE siteid = @SiteId AND userid = @UserId";
                    DapperHelper.Execute(connectionString, updateLoserMoneySql, new {
                        LoseAmount = loseAmount,
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = userVo.userid
                    });

                    // ✅ 使用SaveBankLogWithBalance替换SaveBankLog，避免死锁
                    SaveBankLogWithBalance(userid.ToString(), "游戏输币", "-" + bookVo.myMoney,
                        siteid, "系统", "吹牛输了", newBalance);

                    // ✅ 先获取原挑战者信息并计算新余额，避免SaveBankLog中的SELECT操作导致死锁
                    long originalWinAmount = bookVo.myMoney + bookVo.myMoney * per / 100L;

                    // 获取原挑战者的当前余额
                    string getOriginalUserMoneySql = "SELECT money FROM [user] WHERE siteid = @SiteId AND userid = @UserId";
                    long originalUserMoney = DapperHelper.ExecuteScalar<long>(connectionString, getOriginalUserMoneySql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = bookVo.userid
                    });
                    long originalUserNewBalance = originalUserMoney + originalWinAmount;

                    // ✅ 使用DapperHelper进行安全的原挑战者赢币操作
                    string updateOriginalWinnerMoneySql = "UPDATE [user] SET money = money + @WinAmount WHERE siteid = @SiteId AND userid = @UserId";
                    DapperHelper.Execute(connectionString, updateOriginalWinnerMoneySql, new {
                        WinAmount = originalWinAmount,
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = bookVo.userid
                    });

                    // ✅ 使用SaveBankLogWithBalance替换SaveBankLog，避免死锁
                    SaveBankLogWithBalance(bookVo.userid.ToString(), "游戏赢币", originalWinAmount.ToString(), siteid, "系统", "吹牛打赢了", originalUserNewBalance);
                    GamesClassManager.Tool.SaveGamesRank(siteVo.siteid, userVo.userid, userVo.nickname, "chuiniu", "0", bookVo.myMoney);
                }

                INFO = "OK";
                Action_user_doit(21);
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
        }
    }
}