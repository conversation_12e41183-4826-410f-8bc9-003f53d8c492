﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="SendMoney_FreeMain.aspx.cs" Inherits="YaoHuo.Plugin.BBS.SendMoney_FreeMain" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
Response.Write("<html><head><meta name='format-detection' content='telephone=no'><title>打赏楼主</title><meta name='viewport' content='width=device-width; initial-scale=1.0; minimum-scale=1.0; maximum-scale=1.0'>");
Response.Write("<link href='/NetCSS/CSS/BBS/SendMoney_freeMain.css' rel='stylesheet' type='text/css'/>");
Response.Write("</head>");
Response.Write(this.ERROR);
Response.Write("<body>");
Response.Write("<section class='aui-flexView'><header class='aui-navBar'>打赏妖晶 </header><div class='aui-recharge-box'><div class='aui-cell-box'>");
Response.Write("<form name=\"f\" action=\"" + this.http_start + "bbs/sendmoney_freeMain.aspx\" method=\"post\">");
Response.Write("<div class=\"tip\">");
if (this.INFO == "OK")
{
    Response.Write("<b>" + this.GetLang("打赏送币成功！|打赏送币成功！|Modify successfully!") + "</b> ");
    Response.Write("<a href=\"" + this.http_start + "bbs-" + this.id + ".html\">返回</a><br/>");
}
else if (this.INFO == "NULL")
{
    Response.Write("<b>" + this.GetLang("需要数字！|需要数字！|需要数字!") + "</b><br/>");
}
else if (this.INFO == "ERR")
{
    Response.Write("<b>" + this.GetLang("打赏送币失败，可能原因:<br>1.妖晶不足；2.打赏数值低于下限或超过上限；3.不能打赏自己|ERR！|ERR!") + "</b><br/>");
}
Response.Write("</div>");
Response.Write("<p class=\"info-text\">余额<span class=\"space\"></span>" + userVo.money + "<span class=\"space\"></span>妖晶</p>");
Response.Write("<p class=\"info-text\">楼主<span class=\"space\"></span>ID<span class=\"space\"></span>号<span class=\"space\"></span>" + touserid + "</p>");
Response.Write("<div class=\"info-text\" id=\"type-amount\">请选择打赏数量</div>");
Response.Write("<div class=\"aui-grids\">");
Response.Write("<button type=\"button\" class=\"aui-grids-item this-card\" value=\"101\">");
Response.Write("<span>101</span>");
Response.Write("</button>");
Response.Write("<button type=\"button\" class=\"aui-grids-item\" value=\"300\">");
Response.Write("<span>300</span>");
Response.Write("</button>");
Response.Write("<button type=\"button\" class=\"aui-grids-item\" value=\"520\">");
Response.Write("<span>520</span>");
Response.Write("</button>");
Response.Write("<button type=\"button\" class=\"aui-grids-item\" value=\"666\">");
Response.Write("<span>666</span>");
Response.Write("</button>");
Response.Write("<button type=\"button\" class=\"aui-grids-item\" value=\"2333\">");
Response.Write("<span>2333</span>");
Response.Write("</button>");
Response.Write("<button type=\"button\" class=\"aui-grids-item\" value=\"5000\">");
Response.Write("<span>5000</span>");
Response.Write("</button>");
Response.Write("<button type=\"button\" class=\"aui-grids-item\" value=\"6666\">");
Response.Write("<span>6666</span>");
Response.Write("</button>");
Response.Write("<button type=\"button\" class=\"aui-grids-item\" value=\"8888\">");
Response.Write("<span>8888</span>");
Response.Write("</button>");
Response.Write("<button type=\"button\" class=\"aui-grids-item\" value=\"10000\">");
Response.Write("<span>10000</span>");
Response.Write("</button>");
Response.Write("</div>");
Response.Write("<script type=\"text/javascript\">");
Response.Write("document.addEventListener('DOMContentLoaded', function() {");
Response.Write("    var buttons = document.querySelectorAll('.aui-grids-item');");
Response.Write("    var typeAmount = document.getElementById('type-amount');");
Response.Write("    var sendMoneyInput = document.querySelector('input[name=\"sendmoney\"]');");
Response.Write("    buttons.forEach(function(button) {");
Response.Write("        button.addEventListener('click', function() {");
Response.Write("            buttons.forEach(function(b) { b.classList.remove('this-card'); });");
Response.Write("            this.classList.add('this-card');");
Response.Write("            typeAmount.innerHTML = '打赏<span class=\"space\"></span>' + this.getAttribute('value') + '<span class=\"space\"></span>妖晶';");
Response.Write("            sendMoneyInput.value = this.getAttribute('value');");
Response.Write("        });");
Response.Write("    });");
Response.Write("});");
Response.Write("</script>");
Response.Write("<input type=\"hidden\" name=\"sendmoney\" value=\"101\" />");
Response.Write("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
Response.Write("<input type=\"hidden\" name=\"id\" value=\"" + id + "\"/>");
Response.Write("<input type=\"hidden\" name=\"reid\" value=\"" + reid + "\"/>");
Response.Write("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
Response.Write("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
Response.Write("<input type=\"hidden\" name=\"page\" value=\"" + page + "\"/>");
Response.Write("<input type=\"hidden\" name=\"lpage\" value=\"" + lpage + "\"/>");
Response.Write("<input type=\"hidden\" name=\"touserid\" value=\"" + touserid + "\"/>");
//Response.Write("<input type=\"hidden\" name=\"sid\" value=\"" + sid + "\"/>");
Response.Write("<button type=\"submit\" name=\"g\" class=\"btn\">" + this.GetLang("确定打赏|确定打赏|submit") + "</button><br/>");
Response.Write("</form></div></div></section></section>");
Response.Write("</body>");
Response.Write("</html>");
%>