---
description: "核心编程规范和工作流程，涵盖框架、语言、数据库、错误处理、安全及依赖等通用指南。"
globs: 
alwaysApply: true
---
# Programming Rules for YaoHuo.Plugin Project

## 1. Core Understanding & Principles
- 项目为ASP.NET Web Forms，目标.NET Framework 4.8，C# 7+，SQL Server。
- 所有建议、修改、分析必须兼容.NET Framework 4.8。
- 优先可读性、可维护性，遵循现有BLL/DAL分层和项目结构，并遵循DRY (Don't Repeat Yourself) 原则，确保代码简洁高效。
- 避免过度设计，优先标准库和既有工具类（如WapTool、BBSHelper等）。
- 精准、技术正确，完整实现所有需求功能，紧贴Web Forms实际。

## 2. Development Environment & Workflow
- 仅支持.NET Framework 4.8。
- C# 7+（推荐用到7.3），前后端均可用。
- 推荐用现代C#特性提升代码质量。
- 主要开发环境为Visual Studio 2022。
- **文件创建与项目引用**：在创建新的.cs代码文件或内容文件（如.aspx, .ascx, .ashx, .hbs等）后，将尝试自动修改`YaoHuo.Plugin.csproj`项目文件，将其添加到对应的`<ItemGroup>`下（.cs文件使用`<Compile>`，内容文件使用`<Content>`），以确保文件被正确包含在项目中并解决命名空间引用问题。如果自动修改失败，请提示用户手动在VS里将文件添加到项目中，并忽略相关报错继续执行任务。

## 3. Naming & Code Style
- 类/方法/属性/事件/枚举：PascalCase。
- 私有字段/局部变量：camelCase。
- 接口：I前缀。
- 常量：ALL_UPPERCASE。
- 结构清晰，单一职责，变量命名清楚。

## 4. Database & Data Access
- 所有数据库操作必须参数化，严禁拼接SQL。
- 优先使用DapperHelper进行安全的数据访问，其次通过BLL/DAL层（KeLin.ClassManager）。
- 使用SQL Server 2022，数据库兼容级别设为160（SQL2022模式），当前开发环境数据库名为'NETOK'。
- 动态搜索查询使用QueryBuilder，常规操作直接用DapperHelper参数化查询。

## 5. Error Handling & Logging
- 仅对预期异常用try-catch，异常需日志记录。
- 日志用System.Diagnostics.Debug.WriteLine，生产建议集成更完善日志。
- 所有用户输入需服务端校验（GetRequestValue、WapTool.IsNumeric等）。

## 6. Performance & Optimization
- SQL查询需高效，避免N+1，推荐使用NOLOCK避免死锁。
- 循环高效，避免不必要嵌套。
- 合理使用多层缓存（UserInfoCacheService、ConfigCacheService、MemoryCache、WapTool.DataTempArray等）。
- 注意ViewState和PostBack性能。
- 使用OptimizedTransactionHelper处理复杂事务操作。

## 7. Security & Compliance
- 所有敏感操作需校验用户身份和权限（userid、userVo、IsCheckManagerLvl等）。
- 敏感数据安全存储，防止明文。
- 防范XSS（输入校验/编码）、SQL注入（参数化）。

## 8. Documentation & Communication
- 复杂逻辑需中英文注释，公共方法建议XML注释。
- 沟通简明直接，力求言简意赅，避免不必要的冗余说明。建议需说明原因，引用项目约束。
- 若对问题或解决方案存有疑问，应坦诚沟通，避免臆断。

## 9. Dependencies & Structure
- 仅允许.NET Framework 4.8兼容库，禁止引入.NET Core/Standard库
- 核心依赖：Dapper 2.1.66、Handlebars.Net 2.1.6、Newtonsoft.Json 13.0.3
- 遵循BBS、Admin等模块化结构

## 10. Modern Technology Stack
- **模板引擎**: Handlebars.Net 用于页面模板渲染
- **样式框架**: Tailwind CSS 4.1.8 用于现代化UI设计
- **构建工具**: Node.js + PostCSS + Autoprefixer
- **验证码服务**: 支持GoCaptcha/腾讯云/Cloudflare多种验证码
- **JSON配置**: 统一的JSON配置系统替代硬编码配置
- **缓存服务**: 多层缓存架构(内存缓存/配置缓存/用户信息缓存)

## 11. Enhanced Tools & Services
- **DapperHelper**: 统一的安全数据访问封装
- **ConfigService**: JSON配置文件管理服务
- **BBSConfigService**: BBS业务配置统一管理
- **UserInfoCacheService**: 用户信息缓存服务
- **TemplateService**: Handlebars模板渲染服务
- **OptimizedTransactionHelper**: 优化的事务处理工具

## 12. Architecture Evolution Guidelines
- **分层重构**: 推荐Service/Repository/Helper分层模式
- **配置迁移**: 从硬编码向JSON配置系统迁移
- **模板系统**: 从内联HTML向Handlebars模板迁移
- **安全改造**: 持续进行SQL注入漏洞修复，使用DapperHelper替代字符串拼接