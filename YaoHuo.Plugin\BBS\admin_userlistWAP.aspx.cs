﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using Newtonsoft.Json;

namespace YaoHuo.Plugin.BBS
{
    public class admin_userlistWAP : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string linkURL = "";

        public string linkTOP = "";

        public string condition = "";

        public string ERROR = "";

        public string key = "";

        public List<wap_bbs_Model> listVo = null;

        public StringBuilder strhtml = new StringBuilder();

        public long kk = 1L;

        public long index = 0L;

        public long total = 0L;

        public long pageSize = 10L;

        public long CurrentPage = 1L;

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            CheckManagerLvl("03", classVo.adminusername, GetUrlQueryString());
            switch (action)
            {
                case "class":
                    showclass();
                    break;
                case "gotop":
                    gotop();
                    break;
                case "gogood":
                    gogood();
                    break;
                case "gocheck":
                    gocheck();
                    break;
                case "gocheckall":
                    gocheckall();
                    break;
                default:
                    showclass();
                    break;
                case "godel":
                    break;
            }
        }

        public void showclass()
        {
            key = GetRequestValue("key");

            // ✅ 使用QueryBuilder构建安全的查询条件，避免SQL注入，使用表别名
            var queryBuilder = new QueryBuilder()
                .WhereRaw("b.userid = @ParamN", DapperHelper.SafeParseLong(siteid, "站点ID"));

            if (classid == "0")
            {
                classVo.classid = 0L;
                classVo.position = "left";
                classVo.classname = "管理 所有论坛内容:" + key;
                classVo.siteimg = "NetImages/no.gif";
            }
            else
            {
                classVo.classname = "管理 " + classVo.classname + ":" + key;
                queryBuilder.WhereRaw("b.book_classid = @ParamN", DapperHelper.SafeParseLong(classid, "版块ID"));
            }

            if (!string.IsNullOrEmpty(key?.Trim()))
            {
                queryBuilder.WhereRaw("b.book_title LIKE @ParamN", "%" + DapperHelper.LimitLength(key, 100) + "%");
            }
            try
            {
                pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);

                // ✅ 在方法开始就定义连接字符串
                string adminConnectionString = PubConstant.GetConnectionString(a);

                if (GetRequestValue("getTotal") != "")
                {
                    total = long.Parse(GetRequestValue("getTotal"));
                }
                else
                {
                    // ✅ 使用安全的分页查询获取总数，JOIN class表（简化JOIN条件）
                    var fromClause = "wap_bbs b LEFT JOIN class c ON b.book_classid = c.classid";
                    var (countSql, _, parameters) = queryBuilder.BuildWithCount("SELECT COUNT(*)", fromClause);

                    total = DapperHelper.ExecuteScalar<long>(adminConnectionString, countSql, parameters);
                }

                if (GetRequestValue("page") != "")
                {
                    CurrentPage = long.Parse(GetRequestValue("page"));
                }
                CheckFunction("bbs", CurrentPage);
                CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
                index = pageSize * (CurrentPage - 1L);
                linkURL = http_start + "bbs/admin_userlistWAP.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;key=" + HttpUtility.UrlEncode(key) + "&amp;getTotal=" + total;
                linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, total, pageSize, CurrentPage, linkURL);
                linkURL = WapTool.GetPageLink(ver, lang, total, pageSize, CurrentPage, linkURL);
                if (CurrentPage == 1L && classVo.total != total)
                {
                    WapTool.SetTotal(siteid, classid, total);
                }

                // ✅ 使用安全的分页查询获取数据，JOIN class表获取版块名称
                var (dataSql, dataParameters) = queryBuilder.Build(
                    "SELECT TOP " + pageSize + " b.*, c.classname FROM wap_bbs b LEFT JOIN class c ON b.book_classid = c.classid");
                dataSql += " ORDER BY b.id DESC";

                listVo = DapperHelper.Query<wap_bbs_Model>(adminConnectionString, dataSql, dataParameters)?.ToList() ?? new List<wap_bbs_Model>();
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }

        public void gotop()
        {
            string requestValue = GetRequestValue("id");
            string requestValue2 = GetRequestValue("state");
            try
            {
                // ✅ 使用DapperHelper安全更新置顶状态，避免SQL注入
                UpdateTopStatusSafely(requestValue, requestValue2);
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
            showclass();
        }

        public void gogood()
        {
            string requestValue = GetRequestValue("id");
            string requestValue2 = GetRequestValue("state");
            try
            {
                // ✅ 使用DapperHelper安全更新精华状态，避免SQL注入
                UpdateGoodStatusSafely(requestValue, requestValue2);
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
            showclass();
        }

        public void gocheck()
        {
            CheckManagerLvl("01", "", GetUrlQueryString());
            if (userVo.managerlvl != "00" && siteVo.isCheckSite == 1L)
            {
                ShowTipInfo("超级管理员设置您网站内容需要审核，请联系超级管理员审核！", GetUrlQueryString().Replace("gocheck", "class"));
                return;
            }
            string requestValue = GetRequestValue("id");
            string requestValue2 = GetRequestValue("state");
            try
            {
                // ✅ 使用DapperHelper安全更新审核状态，避免SQL注入
                UpdateCheckStatusSafely(requestValue, requestValue2);
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
            showclass();
        }

        public void gocheckall()
        {
            CheckManagerLvl("01", "", GetUrlQueryString());
            if (userVo.managerlvl != "00" && siteVo.isCheckSite == 1L)
            {
                ShowTipInfo("超级管理员设置您网站内容需要审核，请联系超级管理员审核！", GetUrlQueryString().Replace("gocheckall", "class"));
                return;
            }
            string requestValue = GetRequestValue("state");
            try
            {
                // ✅ 使用DapperHelper安全批量更新审核状态，避免SQL注入
                UpdateAllCheckStatusSafely(requestValue);
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
            showclass();
        }

        /// <summary>
        /// 使用DapperHelper安全更新帖子置顶状态，避免SQL注入
        /// </summary>
        /// <param name="id">帖子ID</param>
        /// <param name="state">置顶状态</param>
        private void UpdateTopStatusSafely(string id, string state)
        {
            string adminConnectionString = PubConstant.GetConnectionString(a);
            string sql = "UPDATE wap_bbs SET book_top = @State WHERE userid = @UserId AND id = @Id";

            DapperHelper.Execute(adminConnectionString, sql, new {
                State = DapperHelper.SafeParseLong(state, "置顶状态"),
                UserId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                Id = DapperHelper.SafeParseLong(id, "帖子ID")
            });
        }

        /// <summary>
        /// 使用DapperHelper安全更新帖子精华状态，避免SQL注入
        /// </summary>
        /// <param name="id">帖子ID</param>
        /// <param name="state">精华状态</param>
        private void UpdateGoodStatusSafely(string id, string state)
        {
            string adminConnectionString = PubConstant.GetConnectionString(a);
            string sql = "UPDATE wap_bbs SET book_good = @State WHERE userid = @UserId AND id = @Id";

            DapperHelper.Execute(adminConnectionString, sql, new {
                State = DapperHelper.SafeParseLong(state, "精华状态"),
                UserId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                Id = DapperHelper.SafeParseLong(id, "帖子ID")
            });
        }

        /// <summary>
        /// 使用DapperHelper安全更新帖子审核状态，避免SQL注入
        /// </summary>
        /// <param name="id">帖子ID</param>
        /// <param name="state">审核状态</param>
        private void UpdateCheckStatusSafely(string id, string state)
        {
            string adminConnectionString = PubConstant.GetConnectionString(a);
            string sql = "UPDATE wap_bbs SET ischeck = @State WHERE id = @Id AND userid = @UserId";

            DapperHelper.Execute(adminConnectionString, sql, new {
                State = DapperHelper.SafeParseLong(state, "审核状态"),
                Id = DapperHelper.SafeParseLong(id, "帖子ID"),
                UserId = DapperHelper.SafeParseLong(siteid, "站点ID")
            });
        }

        /// <summary>
        /// 使用DapperHelper安全批量更新审核状态，避免SQL注入
        /// </summary>
        /// <param name="state">审核状态</param>
        private void UpdateAllCheckStatusSafely(string state)
        {
            string adminConnectionString = PubConstant.GetConnectionString(a);
            string sql = "UPDATE wap_bbs SET ischeck = @State WHERE ischeck <> @State AND userid = @UserId";

            DapperHelper.Execute(adminConnectionString, sql, new {
                State = DapperHelper.SafeParseLong(state, "审核状态"),
                UserId = DapperHelper.SafeParseLong(siteid, "站点ID")
            });
        }
    }
}