img,iframe{max-width:100%}
video{border-radius:10px;}
input[type=file i]{border:0}
form{display:inline;margin:0}
a:visited{text-decoration:none}
div#lastOne~div{display:none;opacity:0}
.text{font:18px/1.8 Sans-serif}
.noborderadius img{border-radius:0px!important;}
.number{font:18px/1.8 Sans-serif;word-break:normal}
.h4{text-align:center;font:20px/2.2 'Microsoft Yahei',Helvetica,Arial,Sans-serif;font-weight:700}
.centered-container { display: flex; justify-content: center; align-items: center; }
video {max-width: 100%;}
.ui__alert {
	z-index: 100;
	position: relative
}
.ui__alert_border {
	border: 1px solid #d9d9d9!important
}
.ui__alert * {
    padding: 0;
    margin-top: 0;
    font-size: 16px;
}
.ui__alert .ui__alert_bg {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    position: fixed;
    background: rgba(0, 0, 0, 0.2);
    animation-duration: 0.1s;
}
.ui__alert .ui__alert_bg.in {
    animation-name: bgFadeIn;
}

.ui__alert .ui__alert_bg.out {
    animation-name: bgFadeOut;
}
.ui__alert .ui__alert_content {
    text-align: center;
    position: fixed;
    min-width: 250px;
    max-width: 280px;
    background: rgb(241, 241, 241);
    border-radius: 10px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    animation-duration: 0.1s;
}
.ui__alert .ui__alert_content.in {
    animation-name: contentZoomIn;
}
.ui__alert .ui__alert_content.out {
    animation-name: contentZoomOut;
}
.ui__alert .ui__alert_content .ui__content_body {
    font-size: 14px;
    padding: 18px;
}
.ui__alert .ui__alert_content .ui__content_body .ui__title {
    margin-bottom: 5px;
    font-size: 17px;
}
@keyframes bgFadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
@keyframes bgFadeOut {
    0% {
        opacity: 1;
    }
    100% {
        opacity: 0;
    }
}
@keyframes contentZoomIn {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}
@keyframes contentZoomOut {
    0% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
}