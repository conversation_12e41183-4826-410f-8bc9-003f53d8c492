using System;
using YaoHuo.Plugin.Tool;

namespace YaoHuo.Plugin.WebSite.BBS.Service
{
    /// <summary>
    /// 派币状态缓存管理服务
    /// 注意：当前图标渲染已改为直接判断 freeLeftMoney > 0，不再使用变灰逻辑
    /// 此服务保留用于管理工具和未来可能的需求
    /// </summary>
    public static class FreeMoneyStatusCache
    {
        /// <summary>
        /// 缓存键前缀 - 使用命名空间化的键名避免冲突
        /// 格式：YaoHuo.Plugin.BBS:FreeMoneyStatus:Completed:{postId}
        /// </summary>
        private const string CACHE_PREFIX = "YaoHuo.Plugin.BBS:FreeMoneyStatus:Completed:";

        /// <summary>
        /// 检查派币是否完成（带缓存和数据一致性验证）
        /// </summary>
        /// <param name="postId">帖子ID</param>
        /// <param name="freeMoney">总派币金额</param>
        /// <param name="freeleftMoney">剩余派币金额</param>
        /// <returns>true=已完成，false=未完成</returns>
        public static bool IsFreeMoneyCompleted(long postId, long freeMoney, long freeleftMoney)
        {
            try
            {
                if (freeMoney <= 0)
                {
                    return false;
                }

                // 检查缓存
                string cacheKey = CACHE_PREFIX + postId;
                if (WapTool.DataTempArray.TryGetValue(cacheKey, out string cached))
                {
                    bool cachedStatus = cached == "1";
                    bool actualStatus = freeleftMoney <= 0;

                    // ✅ 数据一致性验证：如果缓存状态与实际状态不符，清除缓存重新计算
                    if (cachedStatus != actualStatus)
                    {
                        ClearPostCache(postId);
                        // 重新计算并缓存正确状态
                        if (actualStatus)
                        {
                            CacheCompletedStatus(postId);
                        }
                        return actualStatus;
                    }

                    return cachedStatus;
                }

                // 检查实际状态
                bool isCompleted = freeleftMoney <= 0;

                // 如果完成，缓存状态
                if (isCompleted)
                {
                    CacheCompletedStatus(postId);
                }

                return isCompleted;
            }
            catch (Exception)
            {
                // 降级处理：直接根据 freeleftMoney 判断
                return freeMoney > 0 && freeleftMoney <= 0;
            }
        }

        /// <summary>
        /// 缓存派币完成状态
        /// </summary>
        /// <param name="postId">帖子ID</param>
        public static void CacheCompletedStatus(long postId)
        {
            try
            {
                string cacheKey = CACHE_PREFIX + postId;
                WapTool.DataTempArray.Add(cacheKey, "1");
            }
            catch (Exception)
            {
                // 忽略重复添加异常
            }
        }

        /// <summary>
        /// 派币发放时调用，检查并缓存完成状态
        /// </summary>
        /// <param name="postId">帖子ID</param>
        /// <param name="remainingMoney">剩余金额</param>
        public static void OnFreeMoneyDistributed(long postId, long remainingMoney)
        {
            try
            {
                if (remainingMoney <= 0)
                {
                    CacheCompletedStatus(postId);
                }
            }
            catch (Exception)
            {
                // 忽略处理失败
            }
        }

        /// <summary>
        /// 批量检查派币状态
        /// </summary>
        /// <param name="posts">帖子列表</param>
        /// <returns>帖子ID到完成状态的映射</returns>
        public static System.Collections.Generic.Dictionary<long, bool> BatchCheckFreeMoneyStatus(
            System.Collections.Generic.IEnumerable<dynamic> posts)
        {
            var result = new System.Collections.Generic.Dictionary<long, bool>();

            try
            {
                foreach (var post in posts)
                {
                    long postId = post.id;
                    long freeMoney = post.freeMoney;
                    long freeleftMoney = post.freeleftMoney;

                    bool isCompleted = IsFreeMoneyCompleted(postId, freeMoney, freeleftMoney);
                    result[postId] = isCompleted;
                }
            }
            catch (Exception)
            {
                // 忽略批量处理失败
            }

            return result;
        }

        /// <summary>
        /// 清除指定帖子的缓存
        /// </summary>
        /// <param name="postId">帖子ID</param>
        public static void ClearPostCache(long postId)
        {
            try
            {
                string cacheKey = CACHE_PREFIX + postId;
                WapTool.DataTempArray.Remove(cacheKey);
            }
            catch (Exception)
            {
                // 忽略清除失败
            }
        }

        /// <summary>
        /// 强制重新评估派币状态（用于数据修正后的缓存更新）
        /// </summary>
        /// <param name="postId">帖子ID</param>
        /// <param name="freeMoney">总派币金额</param>
        /// <param name="freeleftMoney">当前剩余派币金额</param>
        /// <returns>true=已完成，false=未完成</returns>
        public static bool RefreshFreeMoneyStatus(long postId, long freeMoney, long freeleftMoney)
        {
            try
            {
                if (freeMoney <= 0)
                {
                    return false;
                }

                // 先清除旧缓存
                ClearPostCache(postId);

                // 重新评估状态
                bool isCompleted = freeleftMoney <= 0;

                // 如果完成，缓存新状态
                if (isCompleted)
                {
                    CacheCompletedStatus(postId);
                }

                return isCompleted;
            }
            catch (Exception)
            {
                // 降级处理：直接根据 freeleftMoney 判断
                return freeMoney > 0 && freeleftMoney <= 0;
            }
        }

        /// <summary>
        /// 获取缓存统计信息（监控功能）
        /// </summary>
        /// <returns>缓存统计信息</returns>
        public static string GetCacheStats()
        {
            try
            {
                int totalCacheCount = WapTool.DataTempArray.Count;
                int freeMoneyCompletedCount = 0;

                foreach (var key in WapTool.DataTempArray.Keys)
                {
                    if (key.StartsWith(CACHE_PREFIX))
                    {
                        freeMoneyCompletedCount++;
                    }
                }

                return $"派币状态缓存：{freeMoneyCompletedCount}个已完成帖子，总缓存项：{totalCacheCount}";
            }
            catch (Exception ex)
            {
                return $"获取缓存统计失败：{ex.Message}";
            }
        }
    }
}
