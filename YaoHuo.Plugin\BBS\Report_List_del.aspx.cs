﻿using System;
using KeLin.ClassManager;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Report_List_del : MyPageWap
    {
        private readonly string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string page = "";

        public string INFO = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            page = GetRequestValue("page");
            CheckManagerLvl("04", classVo.adminusername, "bbs/showadmin.aspx?siteid=" + siteid + "&amp;classid=" + classid);
            if (classid == "0")
            {
                CheckManagerLvl("04", "", "bbs/showadmin.aspx?siteid=" + siteid + "&amp;classid=" + classid);
            }
            if (action == "godel")
            {
                try
                {
                    // ✅ 使用DapperHelper安全删除举报记录，避免SQL注入
                    if (id == "0")
                    {
                        // 删除所有举报记录
                        DeleteAllReportsSafely();
                    }
                    else
                    {
                        // 删除单条举报记录
                        DeleteReportSafely();
                    }
                    INFO = "OK";
                }
                catch (Exception ex)
                {
                    INFO = ex.ToString();
                }
            }
        }

        /// <summary>
        /// 使用DapperHelper安全删除举报记录，避免SQL注入
        /// </summary>
        private void DeleteReportSafely()
        {
            string reportConnectionString = PubConstant.GetConnectionString(string_10);
            string deleteSql = "DELETE FROM wap2_bbs_report WHERE siteid = @SiteId AND classid = @ClassId AND id = @Id AND types = 0";

            DapperHelper.Execute(reportConnectionString, deleteSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                ClassId = DapperHelper.SafeParseLong(classid, "版块ID"),
                Id = DapperHelper.SafeParseLong(id, "举报ID")
            });
        }

        /// <summary>
        /// 删除所有举报记录
        /// </summary>
        private void DeleteAllReportsSafely()
        {
            string reportConnectionString = PubConstant.GetConnectionString(string_10);

            if (classid == "0")
            {
                // 清空所有举报记录 - 使用TRUNCATE获得最佳性能
                string truncateSql = "TRUNCATE TABLE wap2_bbs_report";
                DapperHelper.Execute(reportConnectionString, truncateSql);
            }
            else
            {
                // 删除指定版块的举报记录
                string deleteSql = "DELETE FROM wap2_bbs_report WHERE siteid = @SiteId AND classid = @ClassId AND types = 0";
                DapperHelper.Execute(reportConnectionString, deleteSql, new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    ClassId = DapperHelper.SafeParseLong(classid, "版块ID")
                });
            }
        }
    }
}