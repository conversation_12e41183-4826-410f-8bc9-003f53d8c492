export class FilterService {
    constructor() {
        this.activeFilters = new Map();
        this.currentFilters = new Map();
    }
    static getInstance() {
        if (!FilterService.instance) {
            FilterService.instance = new FilterService();
        }
        return FilterService.instance;
    }
    static initFilter(config) {
        FilterService.getInstance().initializeFilter(config);
    }
    static applyFilter(filterId, filterValue) {
        FilterService.getInstance().performFilter(filterId, filterValue);
    }
    static getCurrentFilter(filterId) {
        return FilterService.getInstance().getCurrentFilterValue(filterId);
    }
    static resetFilter(filterId) {
        FilterService.getInstance().resetFilterToDefault(filterId);
    }
    initializeFilter(config) {
        this.activeFilters.set(config.filterId, config);
        this.bindFilterEvents(config);
        this.setInitialFilter(config);
    }
    performFilter(filterId, filterValue) {
        const config = this.activeFilters.get(filterId);
        if (!config)
            return;
        this.updateFilterTabStyles(config, filterValue);
        const visibleCount = this.filterItems(config, filterValue);
        this.currentFilters.set(filterId, filterValue);
        if (config.onFilterChange) {
            config.onFilterChange(filterValue, visibleCount);
        }
    }
    getCurrentFilterValue(filterId) {
        return this.currentFilters.get(filterId);
    }
    resetFilterToDefault(filterId) {
        const config = this.activeFilters.get(filterId);
        if (!config)
            return;
        const defaultValue = config.defaultFilter || 'all';
        this.performFilter(filterId, defaultValue);
    }
    bindFilterEvents(config) {
        const filterTabs = document.querySelectorAll(config.filterTabsSelector);
        filterTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const target = e.currentTarget;
                const filterValue = this.getFilterValueFromElement(target);
                if (filterValue) {
                    this.performFilter(config.filterId, filterValue);
                }
            });
        });
    }
    getFilterValueFromElement(element) {
        const dataAttributes = ['data-category', 'data-filter', 'data-value', 'data-type'];
        for (const attr of dataAttributes) {
            const value = element.getAttribute(attr);
            if (value) {
                return value;
            }
        }
        const textContent = element.textContent?.trim().toLowerCase();
        if (textContent) {
            const filterMap = {
                '全部': 'all',
                '等级': 'level',
                '活跃': 'active',
                '特殊': 'special',
                '成就': 'achievement',
                '节日': 'festival'
            };
            return filterMap[textContent] || textContent;
        }
        return undefined;
    }
    setInitialFilter(config) {
        const defaultValue = config.defaultFilter || 'all';
        this.performFilter(config.filterId, defaultValue);
    }
    updateFilterTabStyles(config, activeFilter) {
        const filterTabs = document.querySelectorAll(config.filterTabsSelector);
        filterTabs.forEach(tab => {
            const element = tab;
            element.classList.remove(config.activeClass);
            const tabValue = this.getFilterValueFromElement(element);
            if (tabValue === activeFilter) {
                element.classList.add(config.activeClass);
            }
        });
    }
    filterItems(config, filterValue) {
        const items = document.querySelectorAll(config.itemsConfig.selector);
        let visibleCount = 0;
        items.forEach(item => {
            const element = item;
            const shouldShow = this.shouldShowItem(element, config.itemsConfig, filterValue);
            if (shouldShow) {
                this.showItem(element, config.itemsConfig);
                visibleCount++;
            }
            else {
                this.hideItem(element, config.itemsConfig);
            }
        });
        return visibleCount;
    }
    shouldShowItem(element, itemConfig, filterValue) {
        if (filterValue === 'all') {
            return true;
        }
        const itemCategories = element.getAttribute(itemConfig.dataAttribute);
        if (!itemCategories) {
            return false;
        }
        const categories = itemCategories.split(' ').map(cat => cat.trim().toLowerCase());
        return categories.includes(filterValue.toLowerCase());
    }
    showItem(element, itemConfig) {
        if (itemConfig.hideClass) {
            element.classList.remove(itemConfig.hideClass);
        }
        if (itemConfig.showClass) {
            element.classList.add(itemConfig.showClass);
        }
        else {
            element.style.display = 'flex';
        }
    }
    hideItem(element, itemConfig) {
        if (itemConfig.showClass) {
            element.classList.remove(itemConfig.showClass);
        }
        if (itemConfig.hideClass) {
            element.classList.add(itemConfig.hideClass);
        }
        else {
            element.style.display = 'none';
        }
    }
    getFilterStats(filterId) {
        const config = this.activeFilters.get(filterId);
        if (!config) {
            return { total: 0, visible: 0, hidden: 0 };
        }
        const items = document.querySelectorAll(config.itemsConfig.selector);
        let visible = 0;
        let hidden = 0;
        items.forEach(item => {
            const element = item;
            const isVisible = element.style.display !== 'none' &&
                !element.classList.contains(config.itemsConfig.hideClass || '');
            if (isVisible) {
                visible++;
            }
            else {
                hidden++;
            }
        });
        return {
            total: items.length,
            visible,
            hidden
        };
    }
}
export function initFilter(config) {
    FilterService.initFilter(config);
}
export function applyFilter(filterId, filterValue) {
    FilterService.applyFilter(filterId, filterValue);
}
export function resetFilter(filterId) {
    FilterService.resetFilter(filterId);
}
export default FilterService;
