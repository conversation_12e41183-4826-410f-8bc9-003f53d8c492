﻿using System;
using System.Web;
using System.Collections.Generic;
using System.Web.Script.Serialization;
using KeLin.ClassManager;
using System.Web.Caching;

namespace YaoHuo.Plugin.BBS
{
    public class GetReplyInfo : IHttpHandler
    {
        // 限流相关常量
        private const int DEFAULT_RATE_LIMIT_SECONDS = 5;
        private const int DEFAULT_MAX_REQUESTS_PER_WINDOW = 5;
        private const int CACHED_RATE_LIMIT_SECONDS = 1;
        private const int CACHED_MAX_REQUESTS_PER_WINDOW = 5;
        // 缓存时长
        private const int CACHE_DURATION_MINUTES = 60;
        private const int NOT_FOUND_CACHE_DURATION_MINUTES = 5;

        private class RateLimitEntry
        {
            public int Count { get; set; }
            public DateTime FirstRequestTime { get; set; }
        }

        public void ProcessRequest(HttpContext context)
        {
            context.Response.ContentType = "application/json";
            var result = new Dictionary<string, object>();
            JavaScriptSerializer serializer = new JavaScriptSerializer();

            // 1. 登录校验（通过cookie）
            long? loggedInUserId = GetLoggedInUserIdFromCookie(context);
            if (loggedInUserId == null || loggedInUserId.Value == 0)
            {
                result["error"] = "Access denied. User not logged in or invalid session.";
                result["status"] = 401;
                context.Response.StatusCode = 401;
                context.Response.Write(serializer.Serialize(result));
                return;
            }
            string useridForRateLimit = loggedInUserId.Value.ToString();

            // 参数校验
            string bookIdParam = context.Request.QueryString["id"];
            string originalFloorParam = context.Request.QueryString["tofloor"];
            string siteIdParam = context.Request.QueryString["siteid"];

            if (string.IsNullOrEmpty(bookIdParam) || !long.TryParse(bookIdParam, out long bookId) ||
                string.IsNullOrEmpty(originalFloorParam) || !int.TryParse(originalFloorParam, out int originalFloor) ||
                string.IsNullOrEmpty(siteIdParam) || !KeLin.ClassManager.Tool.WapTool.IsNumeric(siteIdParam) || originalFloor <= 0)
            {
                result["error"] = "Invalid parameters. 'id' (long), 'tofloor' (int > 0), and 'siteid' (string) are required.";
                result["status"] = 400; // Bad Request
                context.Response.StatusCode = 400;
                context.Response.Write(serializer.Serialize(result));
                return;
            }

            // 数据缓存key
            string dataCacheKey = $"ReplyIdCache_{siteIdParam}_{bookId}_{originalFloor}";
            object cachedData = HttpRuntime.Cache.Get(dataCacheKey);

            int currentRateLimitSeconds = DEFAULT_RATE_LIMIT_SECONDS;
            int currentMaxRequests = DEFAULT_MAX_REQUESTS_PER_WINDOW;

            if (cachedData != null) // 命中缓存，限流更宽松
            {
                currentRateLimitSeconds = CACHED_RATE_LIMIT_SECONDS;
                currentMaxRequests = CACHED_MAX_REQUESTS_PER_WINDOW;
            }

            // 限流逻辑
            string rateLimitKey = $"RateLimit_GetReplyInfo_{useridForRateLimit}_{siteIdParam}_{bookId}_{originalFloor}";
            RateLimitEntry rateLimitEntry = HttpRuntime.Cache.Get(rateLimitKey) as RateLimitEntry;

            DateTime now = DateTime.UtcNow;

            if (rateLimitEntry == null || (now - rateLimitEntry.FirstRequestTime).TotalSeconds > currentRateLimitSeconds)
            {
                // 新窗口或过期
                rateLimitEntry = new RateLimitEntry { Count = 1, FirstRequestTime = now };
                HttpRuntime.Cache.Insert(rateLimitKey, rateLimitEntry, null, now.AddSeconds(currentRateLimitSeconds), Cache.NoSlidingExpiration);
            }
            else
            {
                // 窗口内
                if (rateLimitEntry.Count >= currentMaxRequests)
                {
                    result["error"] = $"请求过于频繁。请在 {Math.Max(1, (int)(currentRateLimitSeconds - (now - rateLimitEntry.FirstRequestTime).TotalSeconds))} 秒后重试。";
                    result["status"] = 429;
                    context.Response.StatusCode = 429;
                    context.Response.Headers["Retry-After"] = Math.Max(1, (int)(currentRateLimitSeconds - (now - rateLimitEntry.FirstRequestTime).TotalSeconds)).ToString();
                    context.Response.Write(serializer.Serialize(result));
                    return;
                }
                rateLimitEntry.Count++;
            }

            if (cachedData != null)
            {
                result["replyid"] = cachedData.ToString();
                result["source"] = "cache";
                context.Response.Write(serializer.Serialize(result));
                return;
            }

            // 未命中缓存，查数据库
            string instanceName = PubConstant.GetAppString("InstanceName");
            long replyId = GetReplyIdByOriginalFloor(bookId, originalFloor, siteIdParam, instanceName);

            if (replyId > 0)
            {
                result["replyid"] = replyId.ToString();
                HttpRuntime.Cache.Insert(dataCacheKey, replyId.ToString(), null,
                    DateTime.UtcNow.AddMinutes(CACHE_DURATION_MINUTES), Cache.NoSlidingExpiration);
            }
            else
            {
                result["error"] = "Reply not found for the given book ID and floor number.";
                result["status"] = 404;
                context.Response.StatusCode = 404;
                HttpRuntime.Cache.Insert(dataCacheKey, "NOT_FOUND", null,
                    DateTime.UtcNow.AddMinutes(NOT_FOUND_CACHE_DURATION_MINUTES), Cache.NoSlidingExpiration);
            }

            context.Response.Write(serializer.Serialize(result));
        }

        /// <summary>
        /// 从cookie中获取当前登录用户ID
        /// </summary>
        private long? GetLoggedInUserIdFromCookie(HttpContext context)
        {
            var cookies = context.Request.Cookies;
            string sidValue = null;
            foreach (string key in cookies.AllKeys)
            {
                if (!string.IsNullOrEmpty(key) && key.StartsWith("sid", StringComparison.OrdinalIgnoreCase))
                {
                    sidValue = cookies[key].Value;
                    break;
                }
            }

            if (string.IsNullOrEmpty(sidValue))
                return null;

            string sidToDecode = sidValue;
            int dashIndex = sidValue.IndexOf('-');
            if (dashIndex > 0)
            {
                sidToDecode = sidValue.Substring(0, dashIndex);
            }

            try
            {
                string decoded = KeLin.ClassManager.Tool.WapTool.Decode_KL(sidToDecode);
                string[] parts = decoded.Split('_');
                if (parts.Length > 2)
                {
                    if (long.TryParse(parts[2], out long userId) && userId > 0)
                    {
                        return userId;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解码SID出错: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 根据主贴ID和原始楼层号查找回帖ID
        /// </summary>
        private long GetReplyIdByOriginalFloor(long bookId, int originalFloor, string siteId, string instanceName)
        {
            long foundReplyId = 0;
            try
            {
                using (var conn = new System.Data.SqlClient.SqlConnection(PubConstant.GetConnectionString(instanceName)))
                {
                    conn.Open();
                    string sql = @"
                        WITH NumberedReplies AS (
                            SELECT
                                id,
                                ROW_NUMBER() OVER (ORDER BY id ASC) AS OriginalFloorNum
                            FROM
                                wap_bbsre WITH (NOLOCK)
                            WHERE
                                bookid = @BookId
                                AND devid = @DevId
                                AND isCheck IN (0, 1, 2, 3) 
                        )
                        SELECT TOP 1
                            id
                        FROM
                            NumberedReplies
                        WHERE
                            OriginalFloorNum = @OriginalFloor;";

                    using (var cmd = new System.Data.SqlClient.SqlCommand(sql, conn))
                    {
                        cmd.Parameters.AddWithValue("@BookId", bookId);
                        cmd.Parameters.AddWithValue("@OriginalFloor", originalFloor);
                        cmd.Parameters.AddWithValue("@DevId", siteId);

                        object queryResult = cmd.ExecuteScalar();
                        if (queryResult != null && queryResult != DBNull.Value)
                        {
                            foundReplyId = Convert.ToInt64(queryResult);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"GetReplyIdByOriginalFloor出错: {ex.ToString()}");
            }
            return foundReplyId;
        }

        public bool IsReusable
        {
            get
            {
                return false;
            }
        }
    }
}