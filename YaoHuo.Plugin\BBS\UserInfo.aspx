﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="UserInfo.aspx.cs" Inherits="YaoHuo.Plugin.BBS.UserInfo" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
Response.Write (WapTool.showTop(this.GetLang(toUserVo.nickname+"的空间|"+toUserVo.nickname+"的空间|"+toUserVo.nickname+" Zome"), wmlVo));
string TA = "TA";
if (this.userid == touserid)
{
    TA = "我";
}
else
{
    if (toUserVo.sex.ToString() == "1")
    {
        TA = "他";
    }
    else if(toUserVo.sex.ToString () == "0")
    {
        TA = "她";
    }
}
strhtml.Append("<script type=\"text/javascript\" defer src=\"/NetCSS/JS/HyperLink.js\"></script>");
strhtml.Append("<script src=\"/NetCSS/JS/BBS/ui-switcher.js\"></script>");
//会员可见
if (this.IsCheckManagerLvl("|00|01|02|03|04|","")==true)
{
    strhtml.Append("<div class=\"title\">"+this.GetLang(toUserVo.nickname + "的空间|" + toUserVo.nickname + "的空间|" + toUserVo.nickname + " Zome")+"<span style=\"float: right; cursor: pointer; font-size: 14px; color: #000;\" onclick=\"switchToNewUI()\" title=\"切换到新版\">[新版]</span></div>");
    strhtml.Append("<div class=\"content\">");
    strhtml.Append("<span class=\"qianming\">");
    strhtml.Append(WapTool.ToWML(toUserVo.remark, wmlVo) + "<br/></span>");
    strhtml.Append("<span class=\"touxiang\">");
    strhtml.Append(WapTool.GetHeadImgHTML(http_start, toUserVo.headimg) + "</span>");
    strhtml.Append("<form name=\"f6\" action=\"" + http_start + "bbs/messagelist_add.aspx\" method=\"post\">");
    strhtml.Append("<div style=\"display: flex; align-items: center;\">");
    strhtml.Append("<textarea class=\"txt\" oninput=\"adjustTextareaHeight(this)\" name=\"content\" style=\"width:60%; height:19px; margin:5px;overflow: hidden;\"></textarea>");
    strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
    strhtml.Append("<input type=\"hidden\" name=\"touseridlist\" value=\"" + touserid + "\"/>");
    strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
    strhtml.Append("<input type=\"hidden\" name=\"backurl\" value=\"" + backurl + "\"/>");
    strhtml.Append("<input type=\"submit\" class=\"btn\" value=\"发送私信\" style=\"margin:0 0 0px;\"/>");
    strhtml.Append("</div>");
    strhtml.Append("</form>");
    strhtml.Append("<div style=\"display:none\">");
    strhtml.Append("<form name=\"f2\" action=\"" + http_start + "bbs/FriendList.aspx\" method=\"post\">");
    strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"addfriend\"/>");
    strhtml.Append("<input type=\"hidden\" name=\"backurl\" value=\"" + backurl + "\"/>");
    strhtml.Append("<input type=\"hidden\" name=\"friendtype\" value=\"0\"/>");
    strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
    strhtml.Append("<input type=\"hidden\" name=\"touserid\" value=\"" + touserid + "\"/>");
    strhtml.Append("<input type=\"submit\" class=\"btn\" value=\"加为好友\" />");
    strhtml.Append("</form> ");
    strhtml.Append("<form name=\"f4\" action=\"" + http_start + "bbs/FriendList.aspx\" method=\"post\">");
    strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"addfriend\"/>");
    strhtml.Append("<input type=\"hidden\" name=\"backurl\" value=\"" + backurl + "\"/>");
    strhtml.Append("<input type=\"hidden\" name=\"friendtype\" value=\"1\"/>");
    strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
    strhtml.Append("<input type=\"hidden\" name=\"touserid\" value=\"" + touserid + "\"/>");
    strhtml.Append("<input type=\"submit\" class=\"btn\" value=\"加入黑单\" />");
    strhtml.Append("</form> ");
    strhtml.Append("</div>");
    strhtml.Append("<div class=\"btBox\"><div style=\"width:99%;\" class=\"bt2\">");
    strhtml.Append("<a href=\"javascript:;\" onclick=\"f2.submit();\">加为好友</a> ");
    //strhtml.Append("<a href=\"javascript:;\" onclick=\"f3.submit();\">加入追求</a> ");
    strhtml.Append("<a href=\"javascript:;f4.submit();\" onclick=\"return confirm('确定要拉黑吗？');\">加黑名单</a> ");
    strhtml.Append("</div></div>");
    strhtml.Append("<b>ID号<span class=\"recolon\">:</span></b>" + toUserVo.userid + WapTool.GetOLtimePic(this.http_start, siteVo.lvlTimeImg, toUserVo.LoginTimes) + "<br/>");
    strhtml.Append("<b>昵称<span class=\"recolon\">:</span></b>" + WapTool.GetColorNickName(toUserVo.idname, toUserVo.nickname, lang, ver) + "<br/>");
    strhtml.Append("<b>" + WapTool.GetSiteMoneyName(siteVo.sitemoneyname, lang) + "<span class=\"recolon\">:</span></b><a href=\"" + http_start + "bbs/toMoneyInfo.aspx?siteid=" + siteid + "&amp;backurl=" + HttpUtility.UrlEncode("bbs/userinfo.aspx?siteid=" + this.siteid + "&amp;touserid=" + this.touserid) + "\">" + toUserVo.money + "</a>");
    if (this.isAdmin == true)
    {
        strhtml.Append("[<a href=\"" + this.http_start + "bbs/banklist.aspx?siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;key=" + touserid + "\">明细</a>.<a href=\"" + this.http_start + "chinabank_wap/banklist.aspx?siteid=" + this.siteid + "&amp;tositeid=" + this.siteid + "&amp;touserid=" + this.touserid + "\">RMB</a>]");
    }
    strhtml.Append("<br/>");
    strhtml.Append("<b>经验<span class=\"recolon\">:</span></b><a href=\"" + http_start + "bbs/tolvlInfo.aspx?siteid=" + siteid + "&amp;backurl=" + HttpUtility.UrlEncode("bbs/userinfo.aspx?siteid=" + this.siteid + "&amp;touserid=" + this.touserid) + "\">" + toUserVo.expr + "</a><br/>");
    strhtml.Append("<b>等级<span class=\"recolon\">:</span></b>" + WapTool.GetLevl(siteVo.lvlNumer, toUserVo.expr,toUserVo.money,type) + " ");
    strhtml.Append("<b>头衔<span class=\"recolon\">:</span></b>" + WapTool.GetHandle(siteVo.lvlNumer, toUserVo.expr,toUserVo.money,type) + "<br/>");
    strhtml.Append("<b>勋章<span class=\"recolon\">:</span></b>" + WapTool.GetMedal(toUserVo.userid.ToString(), toUserVo.moneyname, WapTool.GetSiteDefault(siteVo.Version, 47), wmlVo) + "<br/>");
    strhtml.Append("<a href=\"/wapindex.aspx?classid=171\">" + WapTool.GetMyID(toUserVo.idname, this.lang) + "</a>/");
    if (toUserVo.sex == 1) { strhtml.Append(this.GetLang("男|男|Male")); } else { strhtml.Append(this.GetLang("女|女|Female")); }
    strhtml.Append("/" + toUserVo.age + "岁");
    strhtml.Append("/" + toUserVo.city + "<br/>");
    strhtml.Append("<a name=\"right\"></a><b>权限<span class=\"recolon\">:</span></b>" + idtype + "<br/>");
    strhtml.Append(this.AdminClass);
    if (toUserVo.isonline == "1")
    {
        strhtml.Append("<b>在线</b>");
    }
    else
    {
        strhtml.Append("<b>离线</b>");
    }
    strhtml.Append(WapTool.GetOnline(http_start, toUserVo.isonline, toUserVo.sex.ToString()));
    strhtml.Append("[<a href=\"" + http_start + "bbs/messagelist_add.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;types=0&amp;touserid=" + this.touserid + "&amp;backurl=" + HttpUtility.UrlEncode("bbs/userinfo.aspx?siteid=" + this.siteid + "&amp;touserid=" + this.touserid) + "\">对话</a>]<br/>");
    strhtml.Append("<b>" + TA + "的</b><a href=\"" + this.http_start + "bbs/userinfomore.aspx?touserid=" + this.touserid + "&amp;backurl=" + HttpUtility.UrlEncode(backurl) + "\">详细资料&gt;&gt;</a><br/>");
    strhtml.Append("<b>" + TA + "的</b><span style=\"padding-right:0.2em;\"><a href=\"" + this.http_start + "bbs/book_list.aspx?action=search&amp;key=" + this.touserid + "&amp;type=pub\">帖子(" + toUserVo.bbsCount + ")</a></span>");
    strhtml.Append("<a href=\"" + this.http_start + "bbs/book_re_my.aspx?touserid=" + this.touserid + "\">回复(" + toUserVo.bbsReCount + ")</a>");
    //strhtml.Append("<b>" + TA + "的</b><a href=\"" + this.http_start + "bbs/userinfomore.aspx?siteid=" + this.siteid + "&amp;touserid=" + this.touserid + "&amp;backurl=" + HttpUtility.UrlEncode(backurl) + "\">详细资料&gt;&gt;</a><br/>");
    //strhtml.Append("<b>" + TA + "的</b><span style=\"padding-right:0.2em;\"><a href=\"" + this.http_start + "bbs/book_list.aspx?action=search&amp;siteid=" + this.siteid + "&amp;classid=0&amp;key=" + this.touserid + "&amp;type=pub\">帖子(" + toUserVo.bbsCount + ")</a></span>");
    //strhtml.Append("<a href=\"" + this.http_start + "bbs/book_re_my.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=0&amp;touserid=" + this.touserid + "\">回复(" + toUserVo.bbsReCount + ")</a>");
    //strhtml.Append("<br/><b>" + TA + "的家族<span class=\"recolon\">:</span></b>" + this.showClan + "");
    //strhtml.Append("<b>" + TA + "的粉丝</b>(" + this.fans + ")<a href=\"" + this.http_start + "bbs/FriendList.aspx?action=addfriend&amp;friendtype=0&amp;siteid=" + this.siteid + "&amp;touserid=" + touserid + "&amp;backurl=" + HttpUtility.UrlEncode(backurl) + "\">喜欢</a>");
    //strhtml.Append("<br/><a href=\"" + this.http_start + "bbs/ZoneVistList.aspx?touserid=" + touserid + "&amp;type=1&amp;siteid=" + this.siteid + "&amp;backurl=" + HttpUtility.UrlEncode(backurl) + "\">谁看过我</a>|<a href=\"" + this.http_start + "bbs/ZoneVistList.aspx?touserid=" + touserid + "&amp;type=0&amp;siteid=" + this.siteid + "&amp;backurl=" + HttpUtility.UrlEncode(backurl) + "\">我看过谁</a>");
    strhtml.Append("<br/><span style=\"padding-right:0.2em;\">空间人气</span>"+toUserVo.zoneCount+"<span style=\"padding:0.1em;\">/</span><span style=\"padding-right:0.15em;\">今日</span>"+this.todayZoneCount);
    strhtml.Append("</div>");
    strhtml.Append("<div class=\"title\">");
    strhtml.Append("<b>=" + TA + "的动态=</b><span class=\"right\"><a class=\"urlbtn\" href=\"" + http_start + "bbs/book_list_log.aspx?action=my&amp;siteid=" + siteid + "&amp;classid=0&amp;touserid=" + this.touserid + "" + "\">更多&gt;&gt;</a></span>");
    strhtml.Append("</div>");
    strhtml.Append("<div class=\"content\">");
    for (int i = 0; (this.loglistVo != null && i < loglistVo.Count); i++)
    {
        strhtml.Append(WapTool.DateToString(loglistVo[i].oper_time,lang,1) + "前" +loglistVo[i].log_info.Replace("[sid]",this.sid) + "<br/>");
    }
    if (loglistVo == null)
    {
        strhtml.Append("(暂无动态)");
    }
    strhtml.Append("</div>");
    //strhtml.Append("<div class=\"title\">");
    //strhtml.Append("<b>=" + TA + "的相册= </b><span class=\"right\"><a class=\"urlbtn\" href=\"" + this.http_start + "album/myalbum.aspx?siteid=" + this.siteid + "&amp;classid=0&amp;touserid=" + this.touserid + "\">更多&gt;&gt;</a></span>");
    strhtml.Append("</div>");
    //strhtml.Append("<div class=\"content\">");
    //for (int i = 0; (this.albumlistVo != null && i < albumlistVo.Count); i++)
    {
        //strhtml.Append("<a href=\"" + http_start + "album/book_view.aspx?siteid=" + siteid + "&amp;classid=" + albumlistVo[i].book_classid + "&amp;id=" + albumlistVo[i].id + "" + "\"><img src=\"" + this.http_start + "album/" + albumlistVo[i].book_img + "\" alt=\"load...\"/></a> ");
        //if (i == (albumlistVo.Count - 1)) strhtml.Append("<br/>");
    }
    //if (albumlistVo == null)
    {
        //strhtml.Append("(暂无相片)<br/>");
    }
    //strhtml.Append("</div>");
            strhtml.Append("<script> function adjustTextareaHeight(textarea) { if (textarea.scrollHeight > textarea.offsetHeight) { textarea.style.height = textarea.scrollHeight + 'px'; } } </script>");
            strhtml.Append("<div class=\"title\">");
            strhtml.Append("<b>=" + TA + "的留言板=</b><br/>");
            strhtml.Append("</div>");
            strhtml.Append("<div id=\"guessbook\" class=\"content\">");
            strhtml.Append("<form name=\"f5\" action=\"" + http_start + "bbs/userguessbook.aspx\" method=\"post\">");
            strhtml.Append("<div style=\"display: flex; align-items: center;\">");
            strhtml.Append("<textarea class=\"txt\" oninput=\"adjustTextareaHeight(this)\" name=\"content\" required=\"required\" style=\"width:80%;max-width:350px;font-size:0.75em;height:19px;line-height:19px;\">我来踩踩，记得回哦[img]/bbs/face/放电.gif[/img]</textarea>");
            strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"add\"/>");
            strhtml.Append("<input type=\"hidden\" name=\"touserid\" value=\"" + touserid + "\"/>");
            strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
            strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
            //strhtml.Append("<input type=\"hidden\" name=\"sid\" value=\"" + sid + "\"/>");
            strhtml.Append("<input type=\"submit\" class=\"btn\" value=\"留 言\" id='Submitcomment' style=\"margin:0 3px 0px;\"/>");
            strhtml.Append("</div>");
            strhtml.Append("</form>");
            strhtml.Append("<div class=\"Comment \">");
    StringBuilder gbstr = new StringBuilder();
    for (int i = 0; (gblistVo != null && i < gblistVo.Count); i++)
    {
        if (i % 2 == 0)
        {
            gbstr.Append("<div class=\"line1\">");
        }
        else
        {
            gbstr.Append("<div class=\"line2\">");
        }
        gbstr.Append("<span class=\"retext\">" + gblistVo[i].content + "</span></div>");
    }
    if (gblistVo == null)
    {
        strhtml.Append("（暂时木有留言，快抢沙发哦！）<br/>");
    }
    else
    {
        strhtml.Append(WapTool.ToWML(gbstr.ToString(), wmlVo));
        strhtml.Append("</div><div class=\"more\"><a class=\"noafter\" href=\"" + this.http_start + "bbs/userguessbook.aspx?action=search&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;touserid=" + this.touserid + "\">更多留言内容</a></div>");
    }
    strhtml.Append("</div><script> document.addEventListener(\"DOMContentLoaded\", function() { var images = document.querySelectorAll(\"img\"); for (var i = 0; i < images.length; i++) { var img = images[i]; if (img.alt === \"头像\" && img.src.includes(\"/bbs/head/\")) { img.style.maxHeight = \"150px\"; } } var signatures = document.querySelectorAll(\"span.qianming\"); for (var j = 0; j < signatures.length; j++) { var signature = signatures[j]; if (signature.textContent.trim() === \"\") { signature.remove(); } } }); </script>");
    string isWebHtml = this.ShowWEB_view(this.classid);
    if (isWebHtml != "")
    {
        string strhtml_list = strhtml.ToString();
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml.Replace("[view]", strhtml_list), wmlVo));
        Response.End();
    }
    //会员可见结束
}
strhtml.Append("<div class=\"btBox\"><div class=\"bt2\">");
strhtml.Append("<a href=\"/myfile.aspx\">返回上级</a> ");
strhtml.Append("<a href=\"/\">返回首页</a>	");
strhtml.Append("</div></div>");
Response.Write(strhtml.ToString());
//显示底部
Response.Write(WapTool.showDown(wmlVo));
 %>
<script>
    // UI切换器已通过ui-switcher.js加载，使用全局函数即可
    // 页面加载时会自动检测并修复Cookie问题
</script>