﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_View_addvote.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Book_View_addvote" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    wmlVo.mycss += "\r\n<link href=\"/netcss/css/upload-resource.css?X13\" rel=\"stylesheet\" type=\"text/css\"/>";
	if (this.INFO == "OK")
    {
        wmlVo.timer = "2";
        wmlVo.strUrl = "bbs-" + this.getid + ".html";
    }
    StringBuilder strhtml = new StringBuilder();
    Response.Write(WapTool.showTop(this.GetLang("发表投票帖|發表主題|add subject"), wmlVo));
    if (num > 9) num = 9;
    if (num < 2) num = 2;
    strhtml.Append("<div class=\"upload-container\">");
    strhtml.Append("<div class=\"breadcrumb\">");
    strhtml.Append("<a href=\"/\" class=\"breadcrumb-item\">");
    strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-home h-4 w-4\" data-id=\"7\"><path d=\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"></path><polyline points=\"9 22 9 12 15 12 15 22\"></polyline></svg>");
    strhtml.Append("<span>首页</span>");
    strhtml.Append("</a>");
    strhtml.Append("<span class=\"breadcrumb-separator\"></span>");
    strhtml.Append("<a href=\"/bbslist-" + this.classid + ".html\" class=\"breadcrumb-item\">" + classVo.classname + "</a>");
    strhtml.Append("<span class=\"breadcrumb-separator\"></span>");
    strhtml.Append("<span class=\"breadcrumb-item active\">发表投票帖</span>");
    strhtml.Append("</div>");

    // 通知容器
    strhtml.Append("<div class=\"notification-container\" style=\"display:none\"><div class=\"custom-notification\"> <div class=\"custom-notification-container\"> <p class=\"custom-notification-content\">草稿保存成功!</p> </div> </div></div>");
    strhtml.Append(this.ERROR);
    if (this.INFO == "NULL")
    {
        strhtml.Append("<div class=\"tip\"><b>标题最少" + this.titlemax + "字，内容最少" + this.contentmax + "字！</b></div>");
    }
    else if (this.INFO == "TITLEMAX")
    {
        if (title_max != "0")
        {
            strhtml.Append("<div class=\"tip\"><b>标题最大" + this.title_max + "字。</b></div>");
        }
        if (content_max != "0")
        {
            strhtml.Append("<div class=\"tip\"><b>内容最大" + this.content_max + "字。</b></div>");
        }
    }
    else if (this.INFO == "ERR_FORMAT")
    {
        strhtml.Append("<div class=\"tip\"><b>取到非法值:\"$$\"请更换手机浏览器或重新编辑！</b></div>");
    }
    else if (this.INFO == "REPEAT")
    {
        strhtml.Append("<div class=\"tip\"><b>请不要发表重复内容</b></div>");
    }
    else if (this.INFO == "PWERROR")
    {
        strhtml.Append("<div class=\"tip\"><b>密码错误，请重新录入我的密码！</b></div>");
    }
    else if (this.INFO == "ERROR_Secret")
    {
        strhtml.Append("<div class=\"tip\"><b>暗号错误，如果忘记联系站长索取！</b></div>");
    }
    else if (this.INFO == "MAX")
    {
        strhtml.Append("<div class=\"tip\"><b>今天你已超过回帖限制：" + this.KL_CheckBBSCount + " 个帖子了，请明天再来！</b></div>");
    }
    else if (this.INFO == "SENDMONEY")
    {
        strhtml.Append("<div class=\"tip\"><b>你当前的只有:" + userVo.money + "个，所以你悬赏值只能小于或等于" + userVo.money + "个</b></div>");
    }
    else if (this.INFO == "NOMONEY")
    {
        strhtml.Append("<div class=\"tip\"><b>你当前的只有:" + userVo.money + "个，发帖需要扣掉：" + getmoney2 + "个</b></div>");
    }
    else if (this.INFO == "LOCK")
    {
        strhtml.Append("<div class=\"tip\"><b>抱歉，您已经被加入黑名单，请注意发帖规则！</b></div>");
    }
    // 成功提示
    if (this.INFO == "OK")
    {
        strhtml.Append("<div class=\"upload-success\">");
        strhtml.Append("<div class=\"upload-success-header\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"></path><polyline points=\"22 4 12 14.01 9 11.01\"></polyline></svg>");
        strhtml.Append("<div class=\"upload-success-title\">发表投票主题成功！</div>");
        strhtml.Append("</div>");
        if (siteVo.isCheck == 1)
        {
            strhtml.Append("<div class=\"upload-success-subtitle\">审核后显示！</div>");
        }
        strhtml.Append("<div class=\"upload-success-subtitle\">获得" + WapTool.GetSiteMoneyName(siteVo.sitemoneyname, this.lang) + ":" + getmoney + "、经验:" + getexpr + "</div>");
        strhtml.Append("</div>");
        strhtml.Append("<script type=\"text/javascript\" src=\"/netcss/js/ClearDraft.js?X1\"></script>");

        // 成功后显示新的两个按钮
        strhtml.Append("<div class=\"nav-buttons\">");
        strhtml.Append("<a class=\"nav-btn\" href=\"/bbs-" + this.getid + ".html\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m12 19-7-7 7-7\"/><path d=\"M19 12H5\"/></svg>");
        strhtml.Append("进入主题</a>");

        strhtml.Append("<a class=\"nav-btn\" href=\"/\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-house\"><path d=\"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\"/><path d=\"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"/></svg>");
        strhtml.Append("返回首页</a>");
        strhtml.Append("</div>");
    }
    else
    {
        strhtml.Append("<div class=\"content\">");
        // 主表单
        strhtml.Append("<form name=\"f\" action=\"" + http_start + "bbs/book_view_addvote.aspx\" method=\"post\">");

        // 标题
        strhtml.Append("<div class=\"form-group\">");
        strhtml.Append("<label>" + this.GetLang("标题|標題|Title") + "</label>");
        strhtml.Append("<input type=\"text\" minlength=\"5\" maxlength=\"25\" required=\"required\" name=\"book_title\" class=\"form-control\" value=\"" + book_title + "\"/>");
        strhtml.Append("</div>");

        // 内容
        strhtml.Append("<div class=\"form-group\">");
        strhtml.Append("<div class=\"content-header\">");
        strhtml.Append("<label>" + this.GetLang("内容|內容|Content") + "</label>");
        strhtml.Append("<div class=\"textarea-actions\">");
        strhtml.Append("<button type=\"button\" class=\"action-btn-small\" id=\"saveDraftButton\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z\"></path><path d=\"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7\"></path><path d=\"M7 3v4a1 1 0 0 0 1 1h7\"></path></svg>");
        strhtml.Append("<span>保存草稿</span></button>");
        strhtml.Append("<button type=\"button\" class=\"action-btn-small\" style=\"display:none\" id=\"clearDraftButton\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M3 6h18\"></path><path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"></path><path d=\"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\"></path><line x1=\"10\" x2=\"10\" y1=\"11\" y2=\"17\"></line><line x1=\"14\" x2=\"14\" y1=\"11\" y2=\"17\"></line></svg>");
        strhtml.Append("<span style=\"margin-left:2px;\">清除草稿</span></button>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("<textarea name=\"book_content\" class=\"form-control\" oninput=\"adjustTextareaHeight(this)\" minlength=\"15\" required=\"required\" rows=\"3\" style=\"min-height:25vh;\">" + book_content + "</textarea>");
        strhtml.Append("</div>");

        // 投票选项控件
        strhtml.Append("<div class=\"num-selector\">");
        strhtml.Append("<label>投票选项</label>");
        strhtml.Append("<div class=\"number-control\">");
        strhtml.Append("<button type=\"button\" class=\"num-btn\" onclick=\"updateVoteNum(-1)\" id=\"decreaseBtn\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"></line></svg>");
        strhtml.Append("</button>");
        strhtml.Append("<input type=\"number\" id=\"numInput\" name=\"displayNum\" readonly value=\"" + this.num + "\" min=\"2\" max=\"9\"/>");
        strhtml.Append("<button type=\"button\" class=\"num-btn\" onclick=\"updateVoteNum(1)\" id=\"increaseBtn\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><line x1=\"12\" y1=\"5\" x2=\"12\" y2=\"19\"></line><line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"></line></svg>");
        strhtml.Append("</button>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");

        // 投票选项列表
        for (int i = 0; i < this.num; i++)
        {
            strhtml.Append("<div class=\"form-row\" style=\"margin-bottom:8px;\">");
            strhtml.Append("<div class=\"form-group half\" style=\"display:flex;align-items:center;gap:8px;margin:0;\">");
            strhtml.Append("<div class=\"file-number\">" + (i + 1) + "</div>");
            strhtml.Append("<input type=\"text\" minlength=\"1\" maxlength=\"15\" required=\"required\" name=\"vote\" class=\"form-control\" style=\"margin:0\"/>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
        }

        // 隐藏字段和提交按钮
        strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"page\" value=\"" + page + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"num\" value=\"" + num + "\"/>");

        strhtml.Append("<button type=\"submit\" name=\"g\" id=\"submitBtn\" class=\"submit-btn\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M22 11.08V12a10 10 0 1 1-5.93-9.14\"></path><polyline points=\"22 4 12 14.01 9 11.01\"></polyline></svg>");
        strhtml.Append("<span>发表投票帖</span></button>");
        strhtml.Append("</form>");
        strhtml.Append("</div>");

        // 未成功时显示原来的4个按钮
        strhtml.Append("<div class=\"nav-buttons grid-2\">");
        strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/book_view_add.aspx?classid=" + this.classid + "\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7\"></path><path d=\"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z\"></path></svg>");
        strhtml.Append("发表普通帖</a>");

        strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/book_view_sendmoney.aspx?classid=" + this.classid + "\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-database w-4 h-4 mr-2\" data-id=\"40\"><ellipse cx=\"12\" cy=\"5\" rx=\"9\" ry=\"3\"></ellipse><path d=\"M3 5V19A9 3 0 0 0 21 19V5\"></path><path d=\"M3 12A9 3 0 0 0 21 12\"></path></svg>");
        strhtml.Append("发表派币帖</a>");

        strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/book_view_addfile.aspx?classid=" + this.classid + "\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"></path><polyline points=\"14 2 14 8 20 8\"></polyline></svg>");
        strhtml.Append("发表文件帖</a>");

        strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/book_view_ubb.aspx?classid=" + this.classid + "&amp;backurl=" + HttpUtility.UrlEncode("bbs/book_view_add.aspx?classid=" + this.classid) + "\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12 20h9\"></path><path d=\"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z\"></path></svg>");
        strhtml.Append("UBB方法</a>");
        strhtml.Append("</div>");
    }

    strhtml.Append("</div>");

    strhtml.Append("<script type=\"text/javascript\" src=\"/netcss/js/SaveDraft.js?001\"></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/netcss/js/fileupload/vote-editor.js?v=001\"></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/netcss/js/fileupload/post-editor.js?8\" defer></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/netcss/js/fileupload/image-uploader.js\" defer></script>");
    Response.Write(strhtml);
    Response.Write(WapTool.showDown(wmlVo));
%>