body.iframe-mode {
    margin: 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.container {
    background-color: #fff;
    padding: 15px;
    border-radius: 0;
    box-shadow: none;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

body.iframe-mode h1 {
    display: none;
}

h1 {
    color: #333;
    font-size: 1.5em;
    text-align: center;
    margin-bottom: 15px;
    font-weight: 600;
}

.search-tabs {
    display: flex;
    margin-bottom: 15px;
}

.search-tab-button {
    background: none;
    border: none;
    padding: 10px 2px;
    cursor: pointer;
    font-size: 1em;
    color: #555;
    position: relative;
    outline: none;
    font-weight: normal;
    flex-grow: 1;
    text-align: center;
    min-width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

    .search-tab-button.active {
        color: #333;
        font-weight: bold;
    }

    .search-tab-button::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 25%;
        right: 25%;
        height: 2px;
        background-color: #1abc9c;
        border-radius: 1px;
        transform: scaleX(0);
        transform-origin: bottom center;
        transition: transform 0.2s ease-out;
    }

    .search-tab-button.active::after {
        transform: scaleX(1);
    }

/* 响应式设计 - 适配小屏幕 */
@media (max-width: 320px) {
    .search-tab-button {
        font-size: 0.8em;
        padding: 8px 1px;
    }

    .container {
        padding: 12px;
    }
}

@media (max-width: 280px) {
    .search-tab-button {
        font-size: 0.75em;
        padding: 6px 0;
    }
}

.search-form {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 0;
}

#search {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 1em;
    box-sizing: border-box;
    width: 100%;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

    #search:focus {
        border-color: #1abc9c;
        box-shadow: 0 0 0 2px rgba(26, 188, 156, 0.2);
        outline: none;
    }

#searchButton {
    background-color: #1abc9c;
    border: none;
    color: white;
    padding: 10px 15px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 1em;
    font-weight: 500;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.3s ease, box-shadow 0.3s ease;
    width: 100%;
}

    #searchButton:hover {
        background-color: #16a085;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }



.keyword-info-message {
    color: #888;
    font-size: 0.85em;
    text-align: center;
    margin-top: 8px;
    margin-bottom: 5px;
    min-height: 1.1em;
    display: none;
}

.search-form input[type="text"],
.search-form input[type="number"],
.search-form button {
    width: 100%;
}

@font-face {
    font-family: FONTD;
    src: url(FONTD.eot);
    src: url(FONTD.eot?#iefix) format("embedded-opentype"),url(data:application/x-font-ttf;charset=utf-8;base64,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) format("truetype"),url(FONTD.svg#FONTD) format("svg");
    font-style: normal;
    font-weight: 400
}