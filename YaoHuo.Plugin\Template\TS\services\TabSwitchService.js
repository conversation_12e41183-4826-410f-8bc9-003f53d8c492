export class TabSwitchService {
    constructor() {
        this.activeGroups = new Map();
        this.currentTabs = new Map();
    }
    static getInstance() {
        if (!TabSwitchService.instance) {
            TabSwitchService.instance = new TabSwitchService();
        }
        return TabSwitchService.instance;
    }
    static initTabGroup(groupId, config) {
        TabSwitchService.getInstance().initializeTabGroup(groupId, config);
    }
    static switchTab(groupId, tabId) {
        TabSwitchService.getInstance().switchToTab(groupId, tabId);
    }
    static getCurrentTab(groupId) {
        return TabSwitchService.getInstance().getCurrentActiveTab(groupId);
    }
    initializeTabGroup(groupId, config) {
        this.activeGroups.set(groupId, config);
        this.bindTabEvents(groupId, config);
        this.setInitialTab(groupId, config);
    }
    switchToTab(groupId, tabId) {
        const config = this.activeGroups.get(groupId);
        if (!config)
            return;
        const targetTab = config.tabs.find(tab => tab.id === tabId);
        if (!targetTab)
            return;
        const currentTabId = this.currentTabs.get(groupId);
        this.performTabSwitch(groupId, config, targetTab, currentTabId);
    }
    getCurrentActiveTab(groupId) {
        return this.currentTabs.get(groupId);
    }
    bindTabEvents(groupId, config) {
        const tabElements = document.querySelectorAll(config.groupSelector);
        tabElements.forEach(tabElement => {
            tabElement.addEventListener('click', (e) => {
                e.preventDefault();
                const target = e.currentTarget;
                const tabId = this.getTabIdFromElement(target, config);
                if (tabId) {
                    this.switchToTab(groupId, tabId);
                }
            });
        });
    }
    getTabIdFromElement(element, config) {
        const dataAttributes = ['data-function', 'data-tab', 'data-id', 'data-category'];
        for (const attr of dataAttributes) {
            const value = element.getAttribute(attr);
            if (value && config.tabs.some(tab => tab.id === value)) {
                return value;
            }
        }
        for (const tab of config.tabs) {
            if (element.matches(tab.selector)) {
                return tab.id;
            }
        }
        return undefined;
    }
    setInitialTab(groupId, config) {
        const urlParams = new URLSearchParams(window.location.search);
        let initialTabId;
        for (const tab of config.tabs) {
            if (tab.urlParam && tab.urlValue) {
                const paramValue = urlParams.get(tab.urlParam);
                if (paramValue === tab.urlValue) {
                    initialTabId = tab.id;
                    break;
                }
            }
        }
        if (!initialTabId && config.tabs.length > 0) {
            initialTabId = config.tabs[0].id;
        }
        if (initialTabId) {
            this.switchToTab(groupId, initialTabId);
        }
    }
    performTabSwitch(groupId, config, targetTab, currentTabId) {
        if (currentTabId) {
            const currentTab = config.tabs.find(tab => tab.id === currentTabId);
            if (currentTab && currentTab.onDeactivate) {
                currentTab.onDeactivate();
            }
        }
        this.updateTabStyles(config, targetTab);
        this.updateContentDisplay(config, targetTab);
        if (config.updateUrl) {
            this.updateUrl(config, targetTab);
        }
        this.currentTabs.set(groupId, targetTab.id);
        if (targetTab.onActivate) {
            targetTab.onActivate();
        }
        if (config.onChange) {
            config.onChange(targetTab);
        }
    }
    updateTabStyles(config, activeTab) {
        const tabElements = document.querySelectorAll(config.groupSelector);
        tabElements.forEach(element => {
            element.classList.remove(config.activeClass);
            if (element.matches(activeTab.selector) ||
                this.getTabIdFromElement(element, config) === activeTab.id) {
                element.classList.add(config.activeClass);
            }
        });
    }
    updateContentDisplay(config, activeTab) {
        config.tabs.forEach(tab => {
            if (tab.contentSelector) {
                const contentElement = document.querySelector(tab.contentSelector);
                if (contentElement) {
                    contentElement.style.display = 'none';
                }
            }
        });
        if (activeTab.contentSelector) {
            const activeContent = document.querySelector(activeTab.contentSelector);
            if (activeContent) {
                activeContent.style.display = 'block';
            }
        }
    }
    updateUrl(config, activeTab) {
        if (!activeTab.urlParam || !activeTab.urlValue)
            return;
        const url = new URL(window.location.href);
        url.searchParams.set(activeTab.urlParam, activeTab.urlValue);
        if (config.preserveParams) {
            const currentParams = new URLSearchParams(window.location.search);
            config.preserveParams.forEach(param => {
                const value = currentParams.get(param);
                if (value) {
                    url.searchParams.set(param, value);
                }
            });
        }
        history.replaceState(null, '', url.toString());
    }
}
export function initTabGroup(groupId, config) {
    TabSwitchService.initTabGroup(groupId, config);
}
export function switchTab(groupId, tabId) {
    TabSwitchService.switchTab(groupId, tabId);
}
export default TabSwitchService;
