using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using HandlebarsDotNet;
using System.Text;
using YaoHuo.Plugin.BBS.Models;

namespace YaoHuo.Plugin.WebSite.Tool
{
    /// <summary>
    /// Handlebars模板渲染服务
    /// </summary>
    public static class TemplateService
    {
        // 显式管理的、全局唯一的 Handlebars 环境实例
        private static readonly IHandlebars _handlebarsEnvironment;

        // 模板缓存
        private static readonly Dictionary<string, HandlebarsTemplate<object, object>> _templateCache =
            new Dictionary<string, HandlebarsTemplate<object, object>>();
        private static readonly object _cacheLock = new object();

        // 性能监控
        private static readonly Dictionary<string, (int Count, TimeSpan TotalTime)> _performanceMetrics =
            new Dictionary<string, (int, TimeSpan)>();

        /// <summary>
        /// 静态构造函数，在类首次被访问时执行一次。
        /// 用于创建 IHandlebars 实例并注册全局 Helpers 和 Partials。
        /// </summary>
        static TemplateService()
        {
            try
            {
                var config = new HandlebarsConfiguration();
                // 可在此处对 config进行自定义配置，例如：
                // config.ThrowOnUnresolvedBindingExpression = true; // 更严格的绑定检查
                // config.BlockHelpersMissing = (writer, options, context, arguments) => { /* 自定义处理 */ };

                _handlebarsEnvironment = Handlebars.Create(config);
                RegisterHelpersInternal(_handlebarsEnvironment);
                RegisterPartialsInternal(_handlebarsEnvironment);
            }
            catch (Exception ex)
            {
                // 抛出异常可能会阻止应用程序正常启动，但这有助于暴露初始化问题。
                // 在生产环境中，可能需要更优雅的错误处理或回退机制。
                throw new InvalidOperationException("TemplateService 初始化失败", ex);
            }
        }

        /// <summary>
        /// 内部方法：注册全局 Handlebars Helpers 到指定的 IHandlebars 实例。
        /// </summary>
        private static void RegisterHelpersInternal(IHandlebars handlebarsInstance)
        {
            // eq Helper - 相等比较
            handlebarsInstance.RegisterHelper("eq", (writer, options, context, parameters) =>
            {
                if (parameters.Length >= 2)
                {
                    string arg1 = parameters[0]?.ToString();
                    string arg2 = parameters[1]?.ToString();
                    if (string.Equals(arg1, arg2, StringComparison.OrdinalIgnoreCase))
                    {
                        options.Template(writer, context);
                    }
                    else
                    {
                        options.Inverse(writer, context);
                    }
                }
            });

            // ne Helper - 不等比较
            handlebarsInstance.RegisterHelper("ne", (writer, options, context, parameters) =>
            {
                if (parameters.Length >= 2)
                {
                    string arg1 = parameters[0]?.ToString();
                    string arg2 = parameters[1]?.ToString();
                    if (!string.Equals(arg1, arg2, StringComparison.OrdinalIgnoreCase))
                    {
                        options.Template(writer, context);
                    }
                    else
                    {
                        options.Inverse(writer, context);
                    }
                }
            });
            // formatNumber Helper - 数字格式化
            handlebarsInstance.RegisterHelper("formatNumber", (writer, context, parameters) =>
            {
                if (parameters.Length > 0 && parameters[0] != null)
                {
                    if (long.TryParse(parameters[0].ToString(), out long number))
                    {
                        writer.WriteSafeString(number.ToString("N0"));
                    }
                    else
                    {
                        writer.WriteSafeString(parameters[0].ToString());
                    }
                }
            });

            // hasPermission Helper - 权限检查
            handlebarsInstance.RegisterHelper("hasPermission", (writer, options, context, parameters) =>
            {
                bool conditionMet = false;
                if (parameters.Length > 0 && parameters[0] != null)
                {
                    string permission = parameters[0].ToString();
                    conditionMet = (permission != "普通");
                }

                if (conditionMet)
                {
                    options.Template(writer, context);
                }
                else
                {
                    options.Inverse(writer, context);
                }
            });

            // url Helper - URL生成
            handlebarsInstance.RegisterHelper("url", (writer, context, parameters) =>
            {
                if (parameters.Length > 0 && parameters[0] != null)
                {
                    string path = parameters[0].ToString();
                    // 这里可以添加基础URL处理逻辑
                    writer.WriteSafeString(path);
                }
            });

            // firstChar Helper - 获取字符串的第一个字符（大写）
            handlebarsInstance.RegisterHelper("firstChar", (writer, context, parameters) =>
            {
                if (parameters.Length > 0 && parameters[0] != null)
                {
                    string str = parameters[0].ToString();
                    string result = (str != null && str.Length > 0) ? str.Substring(0, 1).ToUpper() : "?";
                    writer.WriteSafeString(result);
                }
                else
                {
                    writer.WriteSafeString("?");
                }
            });

            // formatDate Helper - 格式化日期
            handlebarsInstance.RegisterHelper("formatDate", (writer, context, parameters) =>
            {
                if (parameters.Length > 0 && parameters[0] != null)
                {
                    if (DateTime.TryParse(parameters[0].ToString(), out DateTime date))
                    {
                        writer.WriteSafeString(date.ToString("yyyy-MM-dd"));
                    }
                    else
                    {
                        writer.WriteSafeString(parameters[0].ToString());
                    }
                }
                else
                {
                    writer.WriteSafeString("");
                }
            });

            // htmlEncode Helper - HTML编码防XSS
            handlebarsInstance.RegisterHelper("htmlEncode", (writer, context, parameters) =>
            {
                if (parameters.Length > 0 && parameters[0] != null)
                {
                    string encoded = HttpUtility.HtmlEncode(parameters[0].ToString());
                    writer.WriteSafeString(encoded);
                }
            });

            // ifAny Helper - 检查是否有任何非空值
            handlebarsInstance.RegisterHelper("ifAny", (writer, options, context, parameters) =>
            {
                bool hasAnyValue = parameters.Any(p => p != null && !string.IsNullOrEmpty(p.ToString()));
                if (hasAnyValue)
                {
                    options.Template(writer, context);
                }
                else
                {
                    options.Inverse(writer, context);
                }
            });

            // truncate Helper - 截断字符串
            handlebarsInstance.RegisterHelper("truncate", (writer, context, parameters) =>
            {
                if (parameters.Length >= 2 && parameters[0] != null)
                {
                    string text = parameters[0].ToString();
                    if (int.TryParse(parameters[1].ToString(), out int maxLength) && maxLength > 0)
                    {
                        string result = text.Length > maxLength ? text.Substring(0, maxLength) + "..." : text;
                        writer.WriteSafeString(result);
                    }
                    else
                    {
                        writer.WriteSafeString(text);
                    }
                }
            });

            // json Helper - 将对象序列化为JSON字符串
            handlebarsInstance.RegisterHelper("json", (writer, context, parameters) =>
            {
                if (parameters.Length > 0 && parameters[0] != null)
                {
                    try
                    {
                        string jsonString = Newtonsoft.Json.JsonConvert.SerializeObject(parameters[0]);
                        writer.WriteSafeString(jsonString);
                    }
                    catch (Exception)
                    {
                        // 如果序列化失败，输出空对象
                        writer.WriteSafeString("{}");
                    }
                }
                else
                {
                    writer.WriteSafeString("{}");
                }
            });

            // raw Helper - 输出原始HTML内容，不进行HTML转义
            handlebarsInstance.RegisterHelper("raw", (writer, context, parameters) =>
            {
                if (parameters.Length > 0 && parameters[0] != null)
                {
                    // 使用WriteSafeString输出原始HTML，不进行转义
                    writer.WriteSafeString(parameters[0].ToString());
                }
            });
        }

        /// <summary>
        /// 内部方法：注册全局 Handlebars Partials 到指定的 IHandlebars 实例。
        /// </summary>
        private static void RegisterPartialsInternal(IHandlebars handlebarsInstance)
        {
            try
            {
                string partialsPath = HttpContext.Current.Server.MapPath("~/Template/Partials");
                if (Directory.Exists(partialsPath))
                {
                    foreach (string file in Directory.GetFiles(partialsPath, "*.hbs"))
                    {
                        string partialName = Path.GetFileNameWithoutExtension(file);
                        string content = File.ReadAllText(file);
                        handlebarsInstance.RegisterTemplate(partialName, content);
                    }
                }
            }
            catch (Exception ex)
            {
                // 静默处理 Partials 注册错误，避免影响主要功能
                // 可以考虑记录到应用程序日志中
                throw new InvalidOperationException("Partials 注册失败", ex);
            }
        }

        /// <summary>
        /// 编译 Handlebars 模板并缓存。
        /// 所有编译操作都使用内部的 _handlebarsEnvironment 实例。
        /// </summary>
        /// <param name="templatePath">模板的应用程序相对路径 (例如 "~/Template/Pages/MyPage.hbs")</param>
        /// <returns>编译后的模板委托</returns>
        public static HandlebarsTemplate<object, object> CompileTemplate(string templatePath)
        {
            // 验证模板路径安全性
            ValidateTemplatePath(templatePath);

            string 物理路径 = HttpContext.Current.Server.MapPath(templatePath);

            lock (_cacheLock)
            {
                if (_templateCache.TryGetValue(物理路径, out var cachedTemplate))
                {
                    return cachedTemplate;
                }

                if (!File.Exists(物理路径))
                {
                    throw new FileNotFoundException("模板文件未找到。", 物理路径);
                }

                string templateContent = File.ReadAllText(物理路径);
                var compiledTemplate = _handlebarsEnvironment.Compile(templateContent);
                _templateCache[物理路径] = compiledTemplate;

                return compiledTemplate;
            }
        }

        /// <summary>
        /// 渲染指定的 Handlebars 模板（通常是页面级模板或布局模板）。
        /// </summary>
        /// <param name="templatePath">模板的应用程序相对路径</param>
        /// <param name="model">传递给模板的数据模型</param>
        /// <returns>渲染后的 HTML 字符串</returns>
        public static string RenderTemplate(string templatePath, object model)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                var compiledTemplate = CompileTemplate(templatePath);
                string result = compiledTemplate(model);

                stopwatch.Stop();
                RecordPerformanceMetric(templatePath, stopwatch.Elapsed);

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                // 返回一个清晰的错误信息，便于调试。生产环境可能需要更通用的错误提示。
                return $"<div style='border:2px solid red; padding:10px; background-color:#ffe0e0; color:red;'>模板渲染错误 ({Path.GetFileName(templatePath)}): {HttpUtility.HtmlEncode(ex.Message)}<br><pre>{HttpUtility.HtmlEncode(ex.StackTrace)}</pre></div>";
            }
        }

        /// <summary>
        /// 记录性能指标
        /// </summary>
        private static void RecordPerformanceMetric(string templatePath, TimeSpan duration)
        {
            lock (_cacheLock)
            {
                string key = Path.GetFileName(templatePath);
                if (_performanceMetrics.TryGetValue(key, out var existing))
                {
                    _performanceMetrics[key] = (existing.Count + 1, existing.TotalTime + duration);
                }
                else
                {
                    _performanceMetrics[key] = (1, duration);
                }
            }
        }

        /// <summary>
        /// 获取用户当前的UI偏好设置 ("new" 或 "old")。
        /// </summary>
        public static string GetViewMode()
        {
            var request = HttpContext.Current?.Request;
            if (request?.Cookies["ui_preference"] != null)
            {
                string preference = request.Cookies["ui_preference"].Value;
                // 验证值的有效性
                if (preference == "new" || preference == "old")
                {
                    return preference;
                }
            }
            return "new"; // 默认新版，或根据项目配置调整
        }

        /// <summary>
        /// 验证模板路径的安全性
        /// </summary>
        private static void ValidateTemplatePath(string templatePath)
        {
            if (string.IsNullOrWhiteSpace(templatePath))
                throw new ArgumentException("模板路径不能为空", nameof(templatePath));

            if (!templatePath.StartsWith("~/Template/"))
                throw new ArgumentException("模板路径必须在 ~/Template/ 目录下", nameof(templatePath));

            if (templatePath.Contains(".."))
                throw new ArgumentException("模板路径不能包含相对路径", nameof(templatePath));
        }

        /// <summary>
        /// 清除模板缓存（主要用于开发调试）。
        /// </summary>
        public static void ClearCache()
        {
            lock (_cacheLock)
            {
                _templateCache.Clear();
            }
        }

        /// <summary>
        /// 调试方法：检查TemplateService的初始化状态
        /// </summary>
        public static string GetDebugInfo()
        {
            var info = new StringBuilder();
            info.AppendLine("=== TemplateService Debug Info ===");
            info.AppendLine($"_handlebarsEnvironment is null: {_handlebarsEnvironment == null}");
            info.AppendLine($"Template cache count: {_templateCache.Count}");
            
            // 尝试编译一个简单的测试模板来验证Helper
            try
            {
                var testTemplate = _handlebarsEnvironment.Compile("{{#eq 'test' 'test'}}SUCCESS{{else}}FAIL{{/eq}}");
                var result = testTemplate(new { });
                info.AppendLine($"EQ Helper test result: {result}");
            }
            catch (Exception ex)
            {
                info.AppendLine($"EQ Helper test FAILED: {ex.Message}");
            }
            
            info.AppendLine("=== End Debug Info ===");
            return info.ToString();
        }

        /// <summary>
        /// 获取性能指标
        /// </summary>
        public static Dictionary<string, object> GetPerformanceMetrics()
        {
            lock (_cacheLock)
            {
                return _performanceMetrics.ToDictionary(
                    kvp => kvp.Key,
                    kvp => (object)new {
                        Count = kvp.Value.Count,
                        AverageMs = Math.Round(kvp.Value.TotalTime.TotalMilliseconds / kvp.Value.Count, 2),
                        TotalMs = Math.Round(kvp.Value.TotalTime.TotalMilliseconds, 2)
                    }
                );
            }
        }

        /// <summary>
        /// 清除性能指标
        /// </summary>
        public static void ClearPerformanceMetrics()
        {
            lock (_cacheLock)
            {
                _performanceMetrics.Clear();
            }
        }

        /// <summary>
        /// 渲染页面
        /// </summary>
        /// <param name="templatePath">模板路径</param>
        /// <param name="model">数据模型</param>
        /// <returns>渲染后的HTML</returns>
        public static string RenderPage(string templatePath, object model)
        {
            // 保持向后兼容的方法
            return RenderTemplate(templatePath, model);
        }

        /// <summary>
        /// 渲染完整的页面，包括页面主体和主布局。
        /// 这是推荐的页面渲染入口点。
        /// </summary>
        /// <param name="pageTemplatePath">页面主体模板的路径 (例如 "~/Template/Pages/MyFile.hbs")</param>
        /// <param name="pageModel">传递给页面主体模板的数据模型</param>
        /// <param name="pageTitle">页面标题，将用于布局</param>
        /// <param name="headerOptions">头部选项模型，用于布局</param>
        /// <param name="pageSpecificCss">页面专属CSS文件路径 (可选)</param>
        /// <param name="mainLayoutPath">主布局模板的路径 (默认为 "~/Template/Layouts/MainLayout.hbs")</param>
        /// <returns>渲染后的完整 HTML 页面</returns>
        public static string RenderPageWithLayout(
            string pageTemplatePath,
            object pageModel,
            string pageTitle,
            HeaderOptionsModel headerOptions,
            string pageSpecificCss = null,
            string mainLayoutPath = "~/Template/Layouts/MainLayout.hbs")
        {
            // 1. 兜底处理 HeaderOptions —— 未提供则自动构造，并补充铃铛信息（最小侵入）
            if (headerOptions == null)
            {
                headerOptions = new HeaderOptionsModel();
            }

            // 只有在默认状态（显示铃铛但未设置未读数量）时，才自动填充未读数量
            if (headerOptions.ShowMessageNotification && headerOptions.InitialUnreadCount == 0)
            {
                try
                {
                    var pageCtx = HttpContext.Current?.Handler as MyPageWap;
                    if (pageCtx != null && pageCtx.userid != "0")
                    {
                        if (long.TryParse(pageCtx.siteid, out var _sid) && long.TryParse(pageCtx.userid, out var _uid))
                        {
                            // 不再强制设置ShowMessageNotification，只自动获取未读数量
                            headerOptions.InitialUnreadCount = YaoHuo.Plugin.WebSite.Services.MessageService.GetUnreadCountCached(_sid, _uid);
                        }
                    }
                }
                catch { /* 忽略兜底异常，保持页面可用 */ }
            }

            // 2. 渲染页面主体内容
            string pageContentHtml = RenderTemplate(pageTemplatePath, pageModel);

            // 3. 构建布局模型
            var layoutModel = new
            {
                PageTitle = pageTitle,
                Content = pageContentHtml, // 将渲染好的页面主体作为 Content 传递给布局
                HeaderOptions = headerOptions,
                PageSpecificCss = pageSpecificCss
                // 可以根据需要添加其他布局所需的数据，如 SiteInfo, FooterInfo 等
            };

            // 4. 渲染主布局
            return RenderTemplate(mainLayoutPath, layoutModel);
        }


    }
} 