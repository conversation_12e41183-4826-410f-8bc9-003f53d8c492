<!-- Font Awesome 图标库 -->
<link href="//lf6-cdn-tos.bytecdntp.com/cdn/expire-1-y/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

<!-- 页面特定样式 -->
<style>.main-content{overflow-x: hidden;}</style>

<div class="card">
    <div class="card-header">
        <h2 class="card-title">
            <i class="fas fa-comments card-icon"></i>
            空间留言
        </h2>
    </div>
    <div class="card-body">

        <div class="mb-6 pb-4 border-b border-gray-100">
            <form id="messageForm">
                <input type="hidden" name="action" value="add_ajax" />
                <input type="hidden" name="siteid" value="{{SiteInfo.SiteId}}" />
                <input type="hidden" name="classid" value="{{ClassId}}" />
                <input type="hidden" name="touserid" value="{{TargetUserId}}" />
                <input type="hidden" name="backurl" value="{{FormData.BackUrl}}" />

                <div class="flex space-x-3">
                    <div class="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center overflow-hidden">
                        {{#if CurrentUserAvatarUrl}}
                        {{#unless CurrentUserIsDefaultAvatar}}
                        <img src="{{CurrentUserAvatarUrl}}"
                             alt="我的头像"
                             class="w-full h-full object-fill rounded-lg"
                             onerror="this.style.display='none'; this.parentElement.innerHTML='<i class=&quot;fas fa-user text-gray-500&quot;></i>';">
                        {{else}}
                        <i class="fas fa-user text-gray-500"></i>
                        {{/unless}}
                        {{else}}
                        <i class="fas fa-user text-gray-500"></i>
                        {{/if}}
                    </div>
                    <div class="flex-1">
                        <textarea name="content" id="messageInput" placeholder="写下你的留言..."
                                class="w-full p-3 border border-gray-200 bg-gray-50/60 rounded-lg resize-none focus:outline-none focus:border-primary focus:bg-white transition-colors focus:shadow-[0_0_0_2px_rgba(88,180,176,0.2)]"
                                rows="3" maxlength="{{FormData.MaxLength}}" required></textarea>
                        <div class="flex justify-end items-center mt-2">
                            <button type="submit" id="sendButton" class="bg-gradient-to-r from-primary to-secondary text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:scale-105 hover:shadow-[0_4px_15px_rgba(88,180,176,0.3)]">
                                <i class="fas fa-paper-plane mr-2"></i>发送留言
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>

         <div class="flex items-center justify-between mb-4 gap-4">
             <div class="bg-gray-100 p-1.5 rounded-xl inline-flex space-x-0.5">
                  <button data-tab="latest" class="rounded-lg px-4 py-1.5 text-sm transition-all duration-200 {{#if SortOrder}}{{#eq SortOrder '0'}}bg-white text-primary font-semibold shadow-[0_1px_4px_rgba(0,0,0,0.09)]{{else}}text-text-secondary hover:bg-white/70{{/eq}}{{else}}bg-white text-primary font-semibold shadow-[0_1px_4px_rgba(0,0,0,0.09)]{{/if}}">
                       最新
                 </button>
                  <button data-tab="earliest" class="rounded-lg px-4 py-1.5 text-sm transition-all duration-200 {{#eq SortOrder '1'}}bg-white text-primary font-semibold shadow-[0_1px_4px_rgba(0,0,0,0.09)]{{else}}text-text-secondary hover:bg-white/70{{/eq}}">
                       最早
                 </button>
             </div>
             <h4 id="listTitle" class="text-sm text-gray-400 text-right whitespace-nowrap">共 {{TotalMessages}} 条留言</h4>
         </div>

        <div class="space-y-0" id="messageList">
            {{#if MessagesList}}
                {{#each MessagesList}}
                <div class="message-card py-4 border-b border-gray-100 relative min-h-[60px]">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center overflow-hidden relative">
                            <!-- 调试信息 -->
                            <!-- DEBUG: AuthorAvatarUrl={{AuthorAvatarUrl}}, IsDefaultAvatar={{IsDefaultAvatar}}, FirstChar={{FirstChar}} -->

                            <!-- 首字母fallback -->
                            <span class="avatar-fallback-small" data-fallback="true">{{firstChar AuthorNickname}}</span>
                            <!-- 头像图片 -->
                            {{#if AuthorAvatarUrl}}
                            {{#unless IsDefaultAvatar}}
                            <img src="{{AuthorAvatarUrl}}"
                                 alt="{{AuthorNickname}}"
                                 class="w-8 h-8 object-fill absolute top-0 left-0 z-[1] hidden rounded-lg"
                                 data-avatar-src="{{AuthorAvatarUrl}}"
                                 onload="handleSmallAvatarLoad(this)"
                                 onerror="handleSmallAvatarError(this)">
                            {{/unless}}
                            {{/if}}
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-0.5">
                                <a href="{{AuthorSpaceUrl}}" class="font-medium text-sm text-primary hover:text-primary/80 transition-colors">{{AuthorNickname}}</a>
                                <div class="text-gray-400 text-xs">#{{MessageIndex}}</div>
                            </div>
                            <div class="mb-2 -mt-1">
                                <span class="text-xs text-gray-500 cursor-pointer inline-block"
                                     data-detail-time="{{DetailTime}}"
                                     onmouseenter="showTimeTooltip(this)"
                                     onmouseleave="hideTimeTooltip(this)"
                                     title="点击查看详细时间">{{FriendlyTime}}</span>
                            </div>
                            <div class="retext text-sm text-gray-700 leading-relaxed break-words break-all overflow-hidden mb-2 [&_img]:max-w-full [&_iframe]:max-w-full">{{{Content}}}</div>
                            {{#if CanDelete}}
                            <div class="flex justify-between items-center">
                                <div></div>
                                <div class="text-gray-400">
                                    <button data-delete-id="{{Id}}" class="delete-btn inline-flex items-center justify-center w-8 h-8 rounded-full hover:bg-red-50 hover:text-red-500 transition-all duration-200" title="删除留言">
                                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                                    </button>
                                </div>
                            </div>
                            {{/if}}
                         </div>
                    </div>
                </div>
                {{/each}}
            {{else}}
                <div class="text-center py-12">
                    <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
                        <i class="fas fa-comment-slash text-2xl text-gray-400"></i>
                    </div>
                    <h4 class="text-lg font-medium text-gray-600 mb-2">暂无留言</h4>
                    <p class="text-gray-500">成为第一个留言的人吧！</p>
                </div>
            {{/if}}
        </div>

        {{!-- 分页区域 --}}
        {{#if Pagination.HasPages}}
        <div class="flex items-center justify-center gap-4 pt-4 border-gray-200" id="paginationContainer">
            <!-- Left Button -->
            <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light"
                     {{#eq Pagination.CurrentPage 1}}disabled{{/eq}}
                     data-page="{{Pagination.PrevPage}}"
                     title="上一页">
                <i data-lucide="chevron-left" class="w-5 h-5"></i>
            </button>

            <!-- Page Info -->
            <div class="flex-1 text-center text-sm text-text-secondary px-2">
                第 {{Pagination.CurrentPage}} / {{Pagination.TotalPages}} 页
            </div>

            <!-- Right Button -->
            <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light"
                     {{#eq Pagination.CurrentPage Pagination.TotalPages}}disabled{{/eq}}
                     data-page="{{Pagination.NextPage}}"
                     title="下一页">
                <i data-lucide="chevron-right" class="w-5 h-5"></i>
            </button>
        </div>
        {{/if}}

    </div>
</div>

{{!-- fade-out 样式已迁移到 style.css --}}

<script>
document.addEventListener('DOMContentLoaded', function() {

    // 调试：检查头像数据
    console.log('[UserGuessBook] 页面加载完成，开始检查头像数据');
    document.querySelectorAll('.message-card').forEach((card, index) => {
        const avatarContainer = card.querySelector('.w-8.h-8');
        const img = avatarContainer?.querySelector('img');
        const fallback = avatarContainer?.querySelector('.avatar-fallback-small');

        console.log(`[UserGuessBook] 留言 ${index + 1}:`, {
            hasImg: !!img,
            imgSrc: img?.src || 'N/A',
            imgHidden: img?.classList.contains('hidden'),
            fallbackText: fallback?.textContent || 'N/A',
            fallbackDisplay: fallback?.style.display || 'default'
        });
    });

    // 初始化已经加载完成的头像
    document.querySelectorAll('img[data-avatar-src]').forEach(img => {
        if (img.complete) {
            if (img.naturalWidth > 0) {
                console.log('[UserGuessBook] 图片已加载完成，调用handleSmallAvatarLoad:', img.src);
                handleSmallAvatarLoad(img);
            } else {
                console.log('[UserGuessBook] 图片加载失败，调用handleSmallAvatarError:', img.src);
                handleSmallAvatarError(img);
            }
        } else {
            console.log('[UserGuessBook] 图片尚未加载完成:', img.src);
            // 设置超时检查
            setTimeout(() => {
                if (img.classList.contains('hidden') && !img.complete) {
                    console.log('[UserGuessBook] 图片加载超时，调用handleSmallAvatarError:', img.src);
                    handleSmallAvatarError(img);
                }
            }, 3000);
        }
    });

    // 字符计数功能已移除，保持界面简洁

    // Tab 切换逻辑
    const tabButtons = document.querySelectorAll('[data-tab]');
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const sortOrder = button.dataset.tab === 'latest' ? '0' : '1';

            // 构建完整的Tab切换URL
            const baseUrl = '/bbs/userGuessBook.aspx';
            const params = new URLSearchParams();
            params.set('touserid', '{{TargetUserId}}');
            params.set('ot', sortOrder);
            params.set('page', '1'); // 重置到第一页

            const fullUrl = baseUrl + '?' + params.toString();
            window.location.href = fullUrl;
        });
    });

    // 分页按钮功能
    document.querySelectorAll('#paginationContainer button[data-page]').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            if (!this.disabled) {
                const page = this.dataset.page;

                // 构建完整的分页URL，确保包含所有必要参数
                const baseUrl = '/bbs/userGuessBook.aspx';
                const params = new URLSearchParams();
                params.set('touserid', '{{TargetUserId}}');
                params.set('ot', '{{SortOrder}}' || '0');
                params.set('page', page);

                const fullUrl = baseUrl + '?' + params.toString();
                window.location.href = fullUrl;
            }
        });
    });

    // 删除按钮功能 - AJAX删除（使用事件委托支持动态添加的元素）
    document.addEventListener('click', function(e) {
        const deleteButton = e.target.closest('.delete-btn');
        if (!deleteButton) return;

        e.preventDefault();
        const messageId = deleteButton.dataset.deleteId;
        const messageCard = deleteButton.closest('.message-card');

        showCustomConfirm('确定要删除这条留言吗？', function() {
            // 显示删除中状态
            const originalText = deleteButton.innerHTML;
            deleteButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            deleteButton.disabled = true;

            // 检查是否是新添加的留言（临时ID）
            if (messageId.startsWith('new-')) {
                // 新添加的留言，直接从DOM中移除
                messageCard.classList.add('message-delete-fade');

                setTimeout(() => {
                    messageCard.remove();
                    showToast('success', '留言删除成功');
                    updateMessageCount(-1);

                    // 检查是否还有留言，如果没有则显示空状态
                    const remainingMessages = document.querySelectorAll('.message-card');
                    if (remainingMessages.length === 0) {
                        location.reload(); // 重新加载页面显示空状态
                    }
                }, 300);
                return;
            }

            // 构建AJAX删除URL
            const deleteUrl = `/bbs/userGuessBook.aspx?action=delete&id=${messageId}`;

            // 发送AJAX删除请求
            fetch(deleteUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            })
            .then(response => {
                return response.text();
            })
            .then(responseText => {
                try {
                    const data = JSON.parse(responseText);

                    if (data.success) {
                        // 删除成功，添加淡出动画并移除元素
                        messageCard.classList.add('message-delete-fade');

                        setTimeout(() => {
                            messageCard.remove();
                            showToast('success', data.message || '删除成功');
                            updateMessageCount(-1);

                            // 检查是否还有留言，如果没有则显示空状态
                            const remainingMessages = document.querySelectorAll('.message-card');
                            if (remainingMessages.length === 0) {
                                location.reload(); // 重新加载页面显示空状态
                            }
                        }, 300);
                    } else {
                        // 删除失败，恢复按钮状态
                        deleteButton.innerHTML = originalText;
                        deleteButton.disabled = false;
                        showToast('error', data.message || '删除失败');
                    }
                } catch (jsonError) {
                    // 恢复按钮状态
                    deleteButton.innerHTML = originalText;
                    deleteButton.disabled = false;
                    showToast('error', '删除失败，请重试');
                }
            })
            .catch(error => {
                // 恢复按钮状态
                deleteButton.innerHTML = originalText;
                deleteButton.disabled = false;
                showToast('error', '网络错误，删除失败');
            });
        });
    });

    // AJAX表单提交
    const form = document.getElementById('messageForm');
    const messageInput = document.getElementById('messageInput');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault(); // 阻止默认表单提交
            submitMessageAjax();
        });
    }
});

// 防重复提交标志
let isSubmitting = false;

// AJAX提交留言函数
function submitMessageAjax() {
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');
    const content = messageInput.value.trim();

    // 防重复提交检查
    if (isSubmitting) {
        console.log('[UserGuessBook] 正在提交中，忽略重复请求');
        return;
    }

    // 前端验证
    if (content.length < 2) {
        showToast('warning', '留言内容至少需要2个字符！');
        messageInput.focus();
        return;
    }

    // 检查提交频率限制（客户端10秒限制）
    const lastSubmitTime = localStorage.getItem('lastMessageSubmitTime');
    const currentTime = Date.now();
    if (lastSubmitTime && (currentTime - parseInt(lastSubmitTime)) < 10000) {
        const remainingSeconds = Math.ceil((10000 - (currentTime - parseInt(lastSubmitTime))) / 1000);
        showToast('warning', `操作过快，请${remainingSeconds}秒后再试`);
        return;
    }

    // 设置提交状态
    isSubmitting = true;

    // 显示提交中状态
    const originalText = sendButton.innerHTML;
    sendButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>提交中...';
    sendButton.disabled = true;

    // 构建表单数据
    const formData = new FormData();
    formData.append('action', 'add_ajax');
    formData.append('content', content);
    formData.append('siteid', '{{SiteInfo.SiteId}}');
    formData.append('classid', '{{ClassId}}');
    formData.append('touserid', '{{TargetUserId}}');

    // 发送AJAX请求
    fetch('/bbs/userGuessBook.aspx', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        return response.text();
    })
    .then(responseText => {
        try {
            const data = JSON.parse(responseText);

            if (data.success) {
                // 成功处理
                showToast('success', data.message);
                messageInput.value = ''; // 清空输入框

                // 更新最后提交时间
                localStorage.setItem('lastMessageSubmitTime', currentTime.toString());

                // 动态插入新留言
                if (data.newMessage) {
                    addNewMessageToList(data.newMessage);
                } else {
                    // 如果没有新留言数据，fallback到刷新页面
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                }
            } else {
                // 失败处理
                const toastType = data.type || 'error';
                showToast(toastType, data.message);

                // 特殊错误处理
                handleSubmitError(data.type, data.message);
            }
        } catch (jsonError) {
            console.error('JSON解析失败:', jsonError);
            console.log('原始响应:', responseText);
            showToast('error', '服务器返回了意外的响应格式');
        }
    })
    .catch(error => {
        console.error('提交请求失败:', error);
        showToast('error', '网络错误，请重试');
    })
    .finally(() => {
        // 恢复按钮状态
        sendButton.innerHTML = originalText;
        sendButton.disabled = false;
        // 重置提交状态
        isSubmitting = false;
        console.log('[UserGuessBook] 提交完成，重置提交状态');
    });
}

// 动态添加新留言到列表顶部
function addNewMessageToList(messageData) {
    const messageList = document.getElementById('messageList');

    // 检查是否有空状态显示
    const emptyState = messageList.querySelector('.text-center.py-12');
    if (emptyState) {
        emptyState.remove();
    }

    // 构建新留言HTML
    const newMessageHtml = buildMessageHtml(messageData);

    // 创建新留言元素
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = newMessageHtml;
    const newMessage = tempDiv.firstElementChild;

    // 添加滑入动画类
    newMessage.classList.add('animate-slide-in-from-top');

    // 插入到列表顶部
    messageList.insertBefore(newMessage, messageList.firstChild);

    // 初始化头像处理
    const avatarImg = newMessage.querySelector('img[data-avatar-src]');
    if (avatarImg) {
        if (avatarImg.complete && avatarImg.naturalWidth > 0) {
            handleSmallAvatarLoad(avatarImg);
        } else {
            avatarImg.onload = () => handleSmallAvatarLoad(avatarImg);
            avatarImg.onerror = () => handleSmallAvatarError(avatarImg);
        }
    }

    // 初始化Lucide图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // 更新留言总数
    updateMessageCount(1);
}

// 构建留言HTML结构
function buildMessageHtml(messageData) {
    // 处理头像显示逻辑
    let avatarHtml = '';
    if (messageData.authorAvatarUrl && !messageData.isDefaultAvatar) {
        avatarHtml = `
            <span class="avatar-fallback-small" data-fallback="true">${messageData.authorNickname.charAt(0)}</span>
            <img src="${messageData.authorAvatarUrl}"
                 alt="${messageData.authorNickname}"
                 class="w-8 h-8 object-fill absolute top-0 left-0 z-[1] hidden rounded-lg"
                 data-avatar-src="${messageData.authorAvatarUrl}"
                 onload="handleSmallAvatarLoad(this)"
                 onerror="handleSmallAvatarError(this)">
        `;
    } else {
        avatarHtml = `<span class="avatar-fallback-small" data-fallback="true">${messageData.authorNickname.charAt(0)}</span>`;
    }

    // 构建删除按钮HTML
    let deleteButtonHtml = '';
    if (messageData.canDelete) {
        deleteButtonHtml = `
            <div class="flex justify-between items-center">
                <div></div>
                <div class="text-gray-400">
                    <button data-delete-id="${messageData.id}" class="delete-btn inline-flex items-center justify-center w-8 h-8 rounded-full hover:bg-red-50 hover:text-red-500 transition-all duration-200" title="删除留言">
                        <i data-lucide="trash-2" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        `;
    }

    return `
        <div class="message-card py-4 border-b border-gray-100 relative min-h-[60px]">
            <div class="flex items-start space-x-3">
                <div class="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center overflow-hidden relative">
                    ${avatarHtml}
                </div>
                <div class="flex-1">
                    <div class="flex items-center justify-between mb-0.5">
                        <a href="${messageData.authorSpaceUrl}" class="font-medium text-sm text-primary hover:text-primary/80 transition-colors">${messageData.authorNickname}</a>
                        <div class="text-gray-400 text-xs">#${messageData.messageIndex}</div>
                    </div>
                    <div class="mb-2 -mt-1">
                        <span class="text-xs text-gray-500 cursor-pointer inline-block"
                             data-detail-time="${messageData.detailTime}"
                             onmouseenter="showTimeTooltip(this)"
                             onmouseleave="hideTimeTooltip(this)"
                             title="点击查看详细时间">${messageData.friendlyTime}</span>
                    </div>
                    <div class="retext text-sm text-gray-700 leading-relaxed break-words break-all overflow-hidden mb-2 [&_img]:max-w-full [&_iframe]:max-w-full">${messageData.content}</div>
                    ${deleteButtonHtml}
                </div>
            </div>
        </div>
    `;
}

// 更新留言总数显示
function updateMessageCount(increment) {
    const listTitle = document.getElementById('listTitle');
    if (listTitle) {
        const currentText = listTitle.textContent;
        const match = currentText.match(/共 (\d+) 条留言/);
        if (match) {
            const currentCount = parseInt(match[1]);
            const newCount = currentCount + increment;
            listTitle.textContent = `共 ${newCount} 条留言`;
        }
    }
}

// 处理特殊错误类型
function handleSubmitError(errorType, message) {
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendButton');

    switch (errorType) {
        case 'warning':
            // 警告类型错误，焦点回到输入框
            setTimeout(() => {
                messageInput.focus();
            }, 100);
            break;
        case 'error':
            // 严重错误，可能需要禁用提交功能
            if (message.includes('黑名单')) {
                // 被拉黑，禁用提交功能
                sendButton.disabled = true;
                sendButton.innerHTML = '<i class="fas fa-ban mr-2"></i>已被禁止';
                messageInput.disabled = true;
                messageInput.placeholder = '您已被加入黑名单，无法发表留言';
            }
            break;
    }
}

// Toast 通知处理函数
function closeToast(toastId) {
    const toast = document.getElementById(toastId);
    if (toast) {
        toast.classList.add('fade-out');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }
}

function autoCloseToast(toastId, delay = 3000) {
    setTimeout(() => {
        closeToast(toastId);
    }, delay);
}

// 增强的Toast通知函数，使用 Tailwind CSS 类
function showToast(type, message) {
    // 创建toast容器
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container';
        document.body.appendChild(toastContainer);
    }

    // 创建toast元素
    const toast = document.createElement('div');
    const toastId = 'toast-' + Date.now();
    toast.id = toastId;

    // 根据类型设置样式和图标
    let toastClass, icon;
    switch (type) {
        case 'success':
            toastClass = 'toast-dynamic-success';
            icon = 'check-circle';
            break;
        case 'error':
            toastClass = 'toast-dynamic-error';
            icon = 'x-circle';
            break;
        case 'warning':
            toastClass = 'toast-dynamic-warning';
            icon = 'alert-triangle';
            break;
        case 'info':
            toastClass = 'toast-dynamic-info';
            icon = 'info';
            break;
        default:
            toastClass = 'toast-dynamic-info';
            icon = 'info';
    }

    toast.className = toastClass;

    toast.innerHTML = `
        <i data-lucide="${icon}" class="w-4 h-4 flex-shrink-0"></i>
        <span class="flex-1">${message}</span>
        <button onclick="closeToast('${toastId}')" class="bg-transparent border-none text-white cursor-pointer p-0 ml-2 hover:opacity-80">
            <i data-lucide="x" class="w-3.5 h-3.5"></i>
        </button>
    `;

    toastContainer.appendChild(toast);

    // 初始化图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // 显示动画
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);

    // 根据类型设置不同的自动关闭时间
    const autoCloseDelay = type === 'error' ? 6000 : 4000; // 错误消息显示更久
    setTimeout(() => {
        closeToast(toastId);
    }, autoCloseDelay);
}

// 自定义确认对话框函数，使用 Tailwind CSS 类
function showCustomConfirm(message, onConfirm) {
    const confirmDialogOverlay = document.createElement('div');
    confirmDialogOverlay.className = 'confirm-overlay';

    const confirmDialogContent = document.createElement('div');
    confirmDialogContent.className = 'confirm-content';

    confirmDialogContent.innerHTML = `
        <h3 class="confirm-title">确认操作</h3>
        <p class="confirm-message">${message}</p>
        <div class="confirm-actions">
            <button class="custom-confirm-btn custom-confirm-delete" id="confirmCustomConfirm">确定</button>
            <button class="custom-confirm-btn custom-confirm-cancel" id="cancelCustomConfirm">取消</button>
        </div>
    `;

    confirmDialogOverlay.appendChild(confirmDialogContent);
    document.body.appendChild(confirmDialogOverlay);

    // 关闭对话框函数
    function closeDialog() {
        if (document.body.contains(confirmDialogOverlay)) {
            document.body.removeChild(confirmDialogOverlay);
        }
    }

    const cancelBtn = document.getElementById('cancelCustomConfirm');
    if(cancelBtn) cancelBtn.onclick = closeDialog;

    const confirmBtn = document.getElementById('confirmCustomConfirm');
    if(confirmBtn) confirmBtn.onclick = () => {
        onConfirm();
        closeDialog();
    };

    // 点击遮罩关闭
    confirmDialogOverlay.addEventListener('click', function(e) {
        if (e.target === confirmDialogOverlay) {
            closeDialog();
        }
    });
}

// 头像加载处理已移至 AvatarHandler.js 统一管理
</script>

<!-- 引入新的组件系统 -->
<script src="/NetCSS/JS/HyperLink.js" defer></script>
<script src="/Template/JS/Components/AvatarHandler.js?v=1"></script>
<script src="/Template/JS/Components/TimeTooltip.js"></script>