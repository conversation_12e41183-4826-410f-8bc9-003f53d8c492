﻿using System;
using System.Text;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Book_View_SendMoney : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string KL_CheckBBSCount = PubConstant.GetAppString("KL_CheckBBSCount");

        public wap_bbs_Model bbsVo = new wap_bbs_Model();

        public string action = "";

        public string page = "";

        public string INFO = "";

        public string ERROR = "";

        public string book_title = "";

        public string book_content = "";

        public string sendmoney = "";

        public bool isadmin = false;

        public long getid;

        public string getmoney = "0";

        public string getexpr = "0";

        public string needpwFlag = "";

        public string needpw = "";

        public string titlemax = "0";

        public string contentmax = "0";

        public string title_max = "0";

        public string content_max = "0";

        public StringBuilder strhtml = new StringBuilder();

        public string freemoney = "";

        public string freerule1 = "";

        public string freerule2 = "";

        public string maxs = "";

        public long allMoney = 0L;

        public bool isNeedSecret = false;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID对应非论坛模块。", "");
            }
            action = GetRequestValue("action");
            page = GetRequestValue("page");
            needpwFlag = WapTool.GetArryString(siteVo.Version, '|', 31);
            if ("1".Equals(WapTool.GetArryString(classVo.smallimg, '|', 2)) && !CheckManagerLvl("04", classVo.adminusername))
            {
                ShowTipInfo("发帖功能已关闭！", "wapindex.aspx?siteid=" + siteid + "&amp;classid=" + classVo.childid);
            }
            if (classVo.topicID != "" && classVo.topicID != "0" && IsCheckManagerLvl("|00|01|03|04|", ""))
            {
                isNeedSecret = true;
            }
            if (!"1".Equals(WapTool.GetArryString(classVo.smallimg, '|', 11)))
            {
                IsLogin(userid, "bbs/book_view_sendmoney.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + page);
            }
            string arryString = WapTool.GetArryString(classVo.smallimg, '|', 27);
            if (arryString.Trim() != "")
            {
                arryString = "|" + arryString + "|";
                if (!IsCheckManagerLvl("|00|01|03|04|", classVo.adminusername) && arryString.IndexOf("|" + userVo.SessionTimeout + "|") < 0)
                {
                    ShowTipInfo("我当前的用户级别：" + WapTool.GetMyID(userVo.idname, lang) + " 不允许发帖。<br/>允许发帖用户级别为：" + WapTool.GetCardIDNameFormID_multiple(siteid, arryString, lang), "bbs/book_list.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + page);
                }
            }
            string text = WapTool.GetSiteDefault(siteVo.Version, 14);
            if (!WapTool.IsNumeric(text))
            {
                text = "0";
            }
            long num = Convert.ToInt64(text);
            if (num > 0L)
            {
                long num2 = WapTool.DateDiff(DateTime.Now, userVo.RegTime, "MM");
                if (num2 < num)
                {
                    ShowTipInfo("请再过:" + (num - num2) + "分钟才能发帖！", "bbs/book_list.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + page);
                }
            }
            if (classid == "0")
            {
                ShowTipInfo("无此栏目ID", "");
            }
            isadmin = IsUserManager(userid, userVo.managerlvl, classVo.adminusername);
            maxs = WapTool.GetArryString(siteVo.Version, '|', 22);
            if (!WapTool.IsNumeric(maxs))
            {
                maxs = "0";
            }
            if (long.Parse(maxs) < 2L)
            {
                maxs = "10000";
            }
            if (action == "gomod")
            {
                try
                {
                    book_title = GetRequestValue("book_title").Trim(); // 移除标题前后的空格
                    book_content = GetRequestValue("book_content");
                    book_title = book_title.Replace("/", "／").Replace("[", "［").Replace("]", "］");
                    titlemax = WapTool.GetArryString(classVo.smallimg, '|', 24);
                    contentmax = WapTool.GetArryString(classVo.smallimg, '|', 25);
                    if (!WapTool.IsNumeric(titlemax) || titlemax == "0")
                    {
                        titlemax = "2";
                    }
                    if (!WapTool.IsNumeric(contentmax) || contentmax == "0")
                    {
                        contentmax = "2";
                    }
                    title_max = WapTool.GetArryString(classVo.smallimg, '|', 30);
                    content_max = WapTool.GetArryString(classVo.smallimg, '|', 31);
                    if (!WapTool.IsNumeric(title_max))
                    {
                        title_max = "0";
                    }
                    if (!WapTool.IsNumeric(content_max))
                    {
                        content_max = "0";
                    }
                    needpw = GetRequestValue("needpw");
                    sendmoney = GetRequestValue("sendmoney");
                    freemoney = GetRequestValue("freemoney").TrimStart('0');
                    freerule1 = GetRequestValue("freerule1");
                    freerule2 = GetRequestValue("freerule2").TrimStart('0');
                    if (!WapTool.IsNumeric(freemoney))
                    {
                        freemoney = "0";
                    }
                    if (long.Parse(freemoney) < 2000)
                    {
                        INFO = "MINFREEMONEY";
                        return;
                    }
                    if (long.Parse(freemoney) > long.Parse(maxs))
                    {
                        freemoney = maxs;
                    }
                    bool flag = false;
                    bool flag2 = false;
                    string[] array = freerule2.Split('|');
                    for (int i = 0; i < array.Length; i++)
                    {
                        if (WapTool.IsNumeric(array[i]))
                        {
                            if (long.Parse(array[i]) < 200)
                            {
                                INFO = "MINMONEY";
                                return;
                            }
                            allMoney += long.Parse(array[i]);
                            continue;
                        }
                        flag = true;
                        break;
                    }
                    string[] array2 = freerule1.Split('|');
                    for (int i = 0; i < array2.Length; i++)
                    {
                        if (!WapTool.IsNumeric(array2[i]))
                        {
                            flag2 = true;
                            break;
                        }
                    }
                    string arryString2 = WapTool.GetArryString(classVo.smallimg, '|', 21);
                    if (arryString2.Trim() != "")
                    {
                        arryString2 = "|" + arryString2 + "|";
                        bool flag3 = false;
                        if (book_content.IndexOf("[/reply]") > 0 || book_content.IndexOf("[/buy]") > 0 || book_content.IndexOf("[/coin]") > 0 || book_content.IndexOf("[/grade]") > 0)
                        {
                            flag3 = true;
                        }
                        if (flag3 && !IsCheckManagerLvl("|00|01|03|04|", classVo.adminusername) && arryString2.IndexOf("|" + userVo.SessionTimeout + "|") < 0)
                        {
                            ShowTipInfo("我当前的用户级别：" + WapTool.GetMyID(userVo.idname, lang) + " 不允许发特殊帖。<br/>允许发特殊帖用户级别为：" + WapTool.GetCardIDNameFormID_multiple(siteid, arryString2, lang), "bbs/book_list.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + page);
                        }
                    }
                    if (flag || flag2)
                    {
                        INFO = "FORMATERR";
                    }
                    else if (needpwFlag == "1" && PubConstant.md5(needpw).ToLower() != userVo.password.ToLower())
                    {
                        INFO = "PWERROR";
                    }
                    else if (isNeedSecret && base.Request.Form.Get("secret").ToString() != classVo.topicID)
                    {
                        INFO = "ERROR_Secret";
                    }
                    else if (book_title.Trim().Length < long.Parse(titlemax) || book_content.Trim().Length < long.Parse(contentmax))
                    {
                        INFO = "NULL";
                    }
                    else if ((title_max != "0" && book_title.Trim().Length > long.Parse(title_max)) || (content_max != "0" && book_content.Trim().Length > long.Parse(content_max)))
                    {
                        INFO = "TITLEMAX";
                    }
                    //else if (book_title.IndexOf("$(") >= 0 || book_content.IndexOf("$(") >= 0)
                    //{
                    //    INFO = "ERR_FORMAT";
                    //}
                    else if (book_title.Equals(Session["content"]))
                    {
                        INFO = "REPEAT";
                    }
                    else
                    {
                        if (!WapTool.CheckUserBBSCount(siteid, userid, KL_CheckBBSCount, "bbs"))
                        {
                            INFO = "MAX";
                        }
                        else if (WapTool.IsLockuser(siteid, userid, classid) > -1L)
                        {
                            INFO = "LOCK";
                        }
                        else if (WapTool.CheckStrCount(freerule1, "|") != WapTool.CheckStrCount(freerule2, "|"))
                        {
                            INFO = "FORMATERR";
                        }
                        else if (long.Parse(freemoney) > userVo.money || long.Parse(freemoney) < 1L)
                        {
                            INFO = "NOMONEY";
                        }
                        else if (allMoney > long.Parse(freemoney))
                        {
                            INFO = "MAXMONEY";
                        }
                        else if (array2.Length > 1 && allMoney != long.Parse(freemoney))
                        {
                            INFO = "NOEQUALMONEY";
                        }
                        else
                        {
                            Session["content"] = book_title;
                            if (book_title.Length > 200)
                            {
                                book_title = book_title.Substring(0, 200);
                            }
                            // ✅ 使用DapperHelper进行安全的参数化插入操作
                            string connectionString = PubConstant.GetConnectionString(string_10);

                            string insertPostSql = @"INSERT INTO wap_bbs (ischeck, userid, book_classid, book_title, book_author, book_pub,
                                                    book_content, book_date, reShow, sendMoney, viewmoney, viewtype, reDate, freeMoney, freeLeftMoney, freeRule)
                                                    VALUES (@IsCheck, @UserId, @BookClassId, @BookTitle, @BookAuthor, @BookPub,
                                                    @BookContent, @BookDate, @ReShow, @SendMoney, @ViewMoney, @ViewType, @ReDate, @FreeMoney, @FreeLeftMoney, @FreeRule);
                                                    SELECT CAST(SCOPE_IDENTITY() AS BIGINT);";

                            getid = DapperHelper.ExecuteScalar<long>(connectionString, insertPostSql, new {
                                IsCheck = siteVo.isCheck,
                                UserId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                                BookClassId = DapperHelper.SafeParseLong(classid, "栏目ID"),
                                BookTitle = DapperHelper.LimitLength(book_title, 200),
                                BookAuthor = DapperHelper.LimitLength(userVo.nickname, 50),
                                BookPub = DapperHelper.SafeParseLong(userid, "用户ID"),
                                BookContent = book_content,
                                BookDate = DateTime.Now,
                                ReShow = 0L,
                                SendMoney = 0L,
                                ViewMoney = 0L,
                                ViewType = 0L,
                                ReDate = DateTime.Now,
                                FreeMoney = DapperHelper.SafeParseLong(freemoney, "派币金额"),
                                FreeLeftMoney = DapperHelper.SafeParseLong(freemoney, "剩余派币金额"),
                                FreeRule = DapperHelper.LimitLength(freerule1 + "_" + freerule2, 500)
                            });
                            getmoney = WapTool.GetSiteDefault(siteVo.moneyregular, 0);
                            if (!WapTool.IsNumeric(getmoney))
                            {
                                getmoney = "0";
                            }
                            getexpr = WapTool.GetSiteDefault(siteVo.lvlRegular, 0);
                            if (!WapTool.IsNumeric(getexpr))
                            {
                                getexpr = "0";
                            }
                            // ✅ 先计算新余额，避免SaveBankLog中的SELECT操作导致死锁
                            long earnedMoney = DapperHelper.SafeParseLong(getmoney, "获得金币");
                            long spentMoney = DapperHelper.SafeParseLong(freemoney, "派币金额");
                            long netMoneyChange = earnedMoney - spentMoney;
                            long newBalance = userVo.money + netMoneyChange;

                            // ✅ 使用DapperHelper进行安全的参数化更新用户信息
                            string updateUserSql = @"UPDATE [user] SET money = money + @GetMoney - @FreeMoney,
                                                    expR = expR + @GetExp, bbscount = @BbsCount
                                                    WHERE siteid = @SiteId AND userid = @UserId";

                            DapperHelper.Execute(connectionString, updateUserSql, new {
                                GetMoney = earnedMoney,
                                FreeMoney = spentMoney,
                                GetExp = DapperHelper.SafeParseLong(getexpr, "获得经验"),
                                BbsCount = userVo.bbsCount + 1L,
                                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                                UserId = DapperHelper.SafeParseLong(userid, "用户ID")
                            });

                            // ✅ 使用SaveBankLogWithBalance替换SaveBankLog，避免死锁
                            SaveBankLogWithBalance(userid, "论坛发帖", getmoney.ToString(), userid, nickname, "发派币帖[" + getid + "]", newBalance);
                            if (spentMoney > 0L)
                            {
                                // 派币帖扣费的余额（发帖奖励后再扣除派币）
                                long balanceAfterReward = newBalance - spentMoney;
                                SaveBankLogWithBalance(userid, "发派币帖", "-" + freemoney.ToString(), userid, nickname, "发布派币帖[" + getid + "]", balanceAfterReward);
                            }
                            VisiteCount("发表新帖:<a href=\"" + http_start + "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + getid + "\">" + WapTool.GetShowImg(book_title, "200", "bbs") + "</a>");
                            INFO = "OK";
                            WapTool.ClearDataBBS("bbs" + siteid + classid);
                            WapTool.ClearDataTemp("bbsTotal" + siteid + classid);
                            Action_user_doit(1);
                        }
                    }
                }
                catch (Exception ex)
                {
                    ERROR = WapTool.ErrorToString(ex.ToString());
                }
            }
            if (INFO == "WAITING")
            {
                VisiteCount("发表新帖。");
            }
        }
    }
}