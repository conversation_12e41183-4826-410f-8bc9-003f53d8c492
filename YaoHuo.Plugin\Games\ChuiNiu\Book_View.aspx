﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_View.aspx.cs" Inherits="YaoHuo.Plugin.Games.ChuiNiu.Book_View" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    string title = "吹牛详情 (ID: " + bookVo.id + ")";
    
    string headHtml = "<meta charset=\"UTF-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\">\n<link rel=\"stylesheet\" href=\"/games/chuiniu/tailwind.min.css?2\"/> <link rel=\"stylesheet\" href=\"/games/chuiniu/styles.css?25\"/>\n<link rel=\"stylesheet\" href=\"//lf6-cdn-tos.bytecdntp.com/cdn/expire-1-y/font-awesome/6.0.0/css/all.min.css\"/>";
    Response.Write(WapTool.showTop(title, wmlVo, false, headHtml));
    
    // 显示错误信息（如果有）
    if (!string.IsNullOrEmpty(this.ERROR))
    {
        Response.Write("<div class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4\">");
    Response.Write(this.ERROR);
        Response.Write("</div>");
    }
    
    // 顶部导航栏
    Response.Write("<header class=\"bg-gradient-to-r from-teal-500 to-teal-700 shadow-md p-4 flex items-center sticky top-0 z-10 text-white\">");
    Response.Write("<a href=\"" + this.http_start + "games/chuiniu/book_list.aspx?type=" + type + "&amp;touserid=" + touserid + "\" class=\"text-white mr-4\">");
    Response.Write("<i class=\"fas fa-arrow-left\"></i>");
    Response.Write("</a>");
    Response.Write("<h1 class=\"text-xl font-bold\">" + title + "</h1>");
    Response.Write("</header>");
    
    // 主要内容
    Response.Write("<main class=\"p-4 pb-20 max-w-lg mx-auto\">");
    Response.Write("<div class=\"bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden\">");
    
    // 状态横幅 - 根据 bookVo.state 决定样式
    if (bookVo.state == 0) // 进行中
    {
        Response.Write("<div class=\"bg-gradient-to-r from-teal-500 to-teal-600 p-4 text-white text-center\">");
        Response.Write("<div class=\"flex items-center justify-center\">");
        Response.Write("<i class=\"fas fa-hourglass-half text-gray-200 text-xl mr-2 animate-spin slow-spin\"></i>");
        Response.Write("<span class=\"font-bold text-lg\">挑战进行中</span>");
        Response.Write("</div>");
        Response.Write("</div>");
    }
    else if (bookVo.state == 1) // 应战者获胜
    {
        Response.Write("<div class=\"bg-gradient-to-r from-green-500 to-green-600 p-4 text-white text-center\">");
        Response.Write("<div class=\"flex items-center justify-center\">");
        Response.Write("<i class=\"fas fa-trophy text-yellow-300 text-xl mr-2\"></i>");
        Response.Write("<span class=\"font-bold text-lg\">应战者获胜！</span>");
        Response.Write("</div>");
        Response.Write("</div>");
    }
    else // 应战者失败 (bookVo.state == 2)
    {
        Response.Write("<div class=\"bg-gradient-to-r from-red-500 to-red-600 p-4 text-white text-center\">");
        Response.Write("<div class=\"flex items-center justify-center\">");
        Response.Write("<i class=\"fas fa-times-circle text-gray-200 text-xl mr-2\"></i>");
        Response.Write("<span class=\"font-bold text-lg\">应战者失败！</span>");
        Response.Write("</div>");
        Response.Write("</div>");
    }
    
    // 挑战详情
    Response.Write("<div class=\"p-6\">");
    
    // 挑战者信息
    Response.Write("<div class=\"detail-item\">");
    Response.Write("<p class=\"detail-label\">发起者</p>");
    Response.Write("<div class=\"flex items-center detail-value\">");
    Response.Write("<div class=\"w-10 h-10 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-3\">");
    Response.Write("<i class=\"fas fa-user\"></i>");
    Response.Write("</div>");
    Response.Write("<div>");
    Response.Write("<p class=\"font-medium\"><a href=\"" + this.http_start + "bbs/userinfo.aspx?touserid=" + bookVo.userid + "\">" + bookVo.nickName + "</a><span class=\"text-xs text-gray-500 ml-1\">(" + bookVo.userid + ")</span>");
    Response.Write("</p>");
    Response.Write("</div>");
    Response.Write("</div>");
    Response.Write("</div>");
    
    // 应战者信息 - 只在有应战者时显示（状态不为0）
    if (bookVo.state > 0)
    {
        Response.Write("<div class=\"detail-item\">");
        Response.Write("<p class=\"detail-label\">应战者</p>");
        Response.Write("<div class=\"flex items-center detail-value\">");
        Response.Write("<div class=\"w-10 h-10 rounded-full bg-red-100 text-red-600 flex items-center justify-center mr-3\">");
        Response.Write("<i class=\"fas fa-user\"></i>");
        Response.Write("</div>");
        Response.Write("<p class=\"font-medium\"><a href=\"" + this.http_start + "bbs/userinfo.aspx?touserid=" + bookVo.winUserid + "\">" + bookVo.winNickname + "</a><span class=\"text-xs text-gray-500 ml-1\">(" + bookVo.winUserid + ")</span>");
        Response.Write("</p>");
        Response.Write("</div>");
        Response.Write("</div>");
    }
    
    // 赌注信息
    Response.Write("<div class=\"detail-item\">");
    Response.Write("<p class=\"detail-label\">赌注</p>");
    Response.Write("<div class=\"bg-amber-50 p-3 rounded-lg flex items-center\">");
    Response.Write("<p class=\"font-bold text-lg text-amber-600 mr-1\">" + bookVo.myMoney + "</p>");
    Response.Write("<span class=\"coin-icon\">");
    Response.Write("<i class=\"fas fa-coins text-xs\"></i>");
    Response.Write("</span>");
    Response.Write("</div>");
    Response.Write("</div>");
    
    // 问题信息
    Response.Write("<div class=\"detail-item\">");
    Response.Write("<p class=\"detail-label\">挑战问题</p>");
    Response.Write("<div class=\"bg-gray-50 p-4 rounded-lg\">");
    Response.Write("<p class=\"text-gray-800 font-medium\">\"" + bookVo.Question + "\"</p>");
    Response.Write("</div>");
    Response.Write("</div>");
    
    // 选项信息
    Response.Write("<div class=\"detail-item\">");
    Response.Write("<p class=\"detail-label\">选项</p>");
    Response.Write("<div class=\"space-y-2 mt-2\">");
    Response.Write("<div class=\"flex bg-gray-50 p-3 rounded-lg\">");
    Response.Write("<span class=\"text-gray-600 mr-2\">答案一:</span>");
    Response.Write("<span class=\"text-gray-800 font-medium\">" + bookVo.Answer1 + "</span>");
    Response.Write("</div>");
    Response.Write("<div class=\"flex bg-gray-50 p-3 rounded-lg\">");
    Response.Write("<span class=\"text-gray-600 mr-2\">答案二:</span>");
    Response.Write("<span class=\"text-gray-800 font-medium\">" + bookVo.Answer2 + "</span>");
    Response.Write("</div>");
    Response.Write("</div>");
    Response.Write("</div>");
    
    // 双方选择 - 只在比赛结束时显示
    if (bookVo.state > 0)
    {
        Response.Write("<div class=\"detail-item\">");
        Response.Write("<p class=\"detail-label\">双方选择</p>");
        Response.Write("<div class=\"space-y-3 mt-2\">");
        
        // 发起者选择 - 始终使用绿色背景
        Response.Write("<div class=\"bg-green-50 border border-green-200 p-3 rounded-lg flex justify-between items-center\">");
        Response.Write("<div class=\"flex items-center\">");
        Response.Write("<div class=\"w-8 h-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-2\">");
        Response.Write("<i class=\"fas fa-user text-sm\"></i>");
        Response.Write("</div>");
        Response.Write("<div>");
        Response.Write("<p class=\"text-sm text-gray-600\">发起者选择:</p>");
        Response.Write("<p class=\"font-medium text-gray-800\">答案" + bookVo.myAnswer + " (" + (bookVo.myAnswer == 1 ? bookVo.Answer1 : bookVo.Answer2) + ")</p>");
        Response.Write("</div>");
        Response.Write("</div>");
        Response.Write("</div>");
        
        // 应战者选择 - 根据 bookVo.state 决定样式
        if (bookVo.state == 1) // 应战者获胜
        {
            Response.Write("<div class=\"bg-green-50 border border-green-200 p-3 rounded-lg flex justify-between items-center\">");
            Response.Write("<div class=\"flex items-center\">");
            Response.Write("<div class=\"w-8 h-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center mr-2\">");
            Response.Write("<i class=\"fas fa-user text-sm\"></i>");
            Response.Write("</div>");
            Response.Write("<div>");
            Response.Write("<p class=\"text-sm text-gray-600\">应战者选择:</p>");
            Response.Write("<p class=\"font-medium text-gray-800\">答案" + bookVo.winAnswer + " (" + (bookVo.winAnswer == 1 ? bookVo.Answer1 : bookVo.Answer2) + ")</p>");
            Response.Write("</div>");
            Response.Write("</div>");
            Response.Write("<div class=\"bg-green-100 text-green-700 rounded-full w-8 h-8 flex items-center justify-center\">");
            Response.Write("<i class=\"fas fa-check\"></i>");
            Response.Write("</div>");
            Response.Write("</div>");
        }
        else // 应战者失败 (bookVo.state == 2)
        {
            Response.Write("<div class=\"bg-red-50 border border-red-200 p-3 rounded-lg flex justify-between items-center\">");
            Response.Write("<div class=\"flex items-center\">");
            Response.Write("<div class=\"w-8 h-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center mr-2\">");
            Response.Write("<i class=\"fas fa-user text-sm\"></i>");
            Response.Write("</div>");
            Response.Write("<div>");
            Response.Write("<p class=\"text-sm text-gray-600\">应战者选择:</p>");
            Response.Write("<p class=\"font-medium text-gray-800\">答案" + bookVo.winAnswer + " (" + (bookVo.winAnswer == 1 ? bookVo.Answer1 : bookVo.Answer2) + ")</p>");
            Response.Write("</div>");
            Response.Write("</div>");
            Response.Write("<div class=\"bg-red-100 text-red-700 rounded-full w-8 h-8 flex items-center justify-center\">");
            Response.Write("<i class=\"fas fa-times\"></i>");
            Response.Write("</div>");
            Response.Write("</div>");
        }
        
        Response.Write("</div>");
        Response.Write("</div>");
        
        // 结果展示 - 根据 bookVo.state 决定样式和内容
        Response.Write("<div class=\"detail-item\">");
        Response.Write("<p class=\"detail-label\">结果</p>");
        
        // 假设 winPercent 是百分比，如果没有这个变量，则设置一个默认值
        int winPercent = 95; // 默认赢取95%
        if (bookVo.state == 1) // 应战者获胜
        {
            long winAmount = (long)((double)bookVo.myMoney * winPercent / 100.0);
            Response.Write("<div class=\"bg-gradient-to-r from-teal-500 to-teal-600 p-4 rounded-lg text-white text-center\">");
            Response.Write("<p class=\"font-medium\">应战者获胜</p>");
            Response.Write("<div class=\"flex items-center justify-center mt-2\">");
            Response.Write("<p class=\"font-bold text-lg mr-1\">+" + winAmount + "</p>");
            Response.Write("<span class=\"coin-icon\">");
            Response.Write("<i class=\"fas fa-coins text-xs\"></i>");
            Response.Write("</span>");
            Response.Write("</div>");
            Response.Write("</div>");
        }
        else // 应战者失败 (bookVo.state == 2)
        {
            Response.Write("<div class=\"bg-gradient-to-r from-red-500 to-red-600 p-4 rounded-lg text-white text-center\">");
            Response.Write("<p class=\"font-medium\">应战者失败</p>");
            Response.Write("<div class=\"flex items-center justify-center mt-2\">");
            Response.Write("<p class=\"font-bold text-lg mr-1\">-" + bookVo.myMoney + "</p>");
            Response.Write("<span class=\"coin-icon\">");
            Response.Write("<i class=\"fas fa-coins text-xs\"></i>");
            Response.Write("</span>");
            Response.Write("</div>");
            Response.Write("</div>");
        }
        
        Response.Write("</div>");
    }
    else // 比赛进行中，显示状态卡片和应战按钮
    {
        Response.Write("<div class=\"detail-item\">");
        Response.Write("<p class=\"detail-label\">等待应战</p>");
        Response.Write("<div class=\"mt-2\">");
        
        // 替换 URL 中的 book_view 为 doit
        string doitUrl = Request.RawUrl.Replace("book_view", "doit");
        
        Response.Write("<a href=\"" + doitUrl + "\" class=\"w-full bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white font-bold py-3 px-4 rounded-lg shadow-md transition duration-150 ease-in-out flex items-center justify-center\">");
        Response.Write("<i class=\"fas fa-flag-checkered mr-2\"></i>");
        Response.Write("去应战");
        Response.Write("</a>");
        Response.Write("</div>");
        Response.Write("</div>");
    }
    // 时间信息
    Response.Write("<div class=\"detail-item\">");
    Response.Write("<p class=\"detail-label\">时间</p>");
    Response.Write("<div class=\"bg-gray-50 p-3 rounded-lg space-y-2\">");
    Response.Write("<div class=\"flex justify-between text-sm\">");
    Response.Write("<p class=\"text-gray-600\">发起时间:</p>");
    Response.Write("<p class=\"text-gray-800\">" + bookVo.addtime + "</p>");
    Response.Write("</div>");
    // 如果有结束时间，则显示（这里检查 bookVo.state 而不是 endtime 属性）
    if (bookVo.state > 0)
    {
        Response.Write("<div class=\"flex justify-between text-sm\">");
        Response.Write("<p class=\"text-gray-600\">结束时间:</p>");
        Response.Write("<p class=\"text-gray-800\">" + bookVo.winTime + "</p>");
        Response.Write("</div>");
    }
    Response.Write("</div>");
    Response.Write("</div>");
    Response.Write("</div>");
    Response.Write("</div>");
    Response.Write("</main>");
    Response.Write("</body>");
    Response.Write("</html>");
%>