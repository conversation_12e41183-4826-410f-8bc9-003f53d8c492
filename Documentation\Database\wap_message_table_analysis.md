# wap_message 消息表字段分析

## 📊 表结构概览

`wap_message` 表是YaoHuo系统中存储用户消息的核心表，包含私信、系统消息等各类消息数据。

## 🔍 核心字段分析

### 基础字段

| 字段名         | 数据类型      | 默认值 | 说明                     |
| -------------- | ------------- | ------ | ------------------------ |
| `id`         | bigint        | 自增   | 消息唯一标识             |
| `siteid`     | bigint        | -      | 站点ID，值全部为1000     |
| `userid`     | bigint        | 0      | **用户ID**（含义因isnew值而异，见下文详述）  |
| `nickname`   | nvarchar(50)  | NULL   | **用户昵称**（通常对应userid的昵称）     |
| `touserid`   | bigint        | 0      | **目标用户ID**（含义因isnew值而异，见下文详述）   |
| `tonickname` | nvarchar(50)  | NULL   | 目标用户昵称（通常为NULL） |
| `title`      | ntext         | NULL   | 消息标题                 |
| `content`    | ntext         | NULL   | 消息内容                 |
| `addtime`    | smalldatetime | NULL   | 发送时间                 |

### 状态字段

#### isnew 字段（消息状态）

| 值    | 含义                 | 说明                         |
| ----- | -------------------- | ---------------------------- |
| `0` | **已读消息**   | 收件箱中的已读消息           |
| `1` | **未读消息**   | 收件箱中的未读消息（默认值） |
| `2` | **发件箱消息** | 发送者的发件箱记录           |

#### issystem 字段（消息类型）

| 值    | 含义                   | 说明                                    |
| ----- | ---------------------- | --------------------------------------- |
| `0` | **普通用户消息** | 用户之间的私信（默认值）                |
| `1` | **系统消息**     | 系统发送的通知消息，如@提醒、空间留言等 |
| `2` | **特殊状态**     | 通常用于"收藏"功能，在常规查询中被排除  |

## 🔄 消息存储机制

### 普通消息 (`issystem=0`)

系统采用**双副本机制**存储用户间的普通消息。每当一个用户发送一条消息，数据库会同时创建两条记录：

1. **收件箱副本**: 一条记录存储在收件人的"收件箱"中，其 `isnew` 状态为 `0` (已读) 或 `1` (未读)。
2. **发件箱副本**: 另一条记录存储在发送者的"发件箱"中，其 `isnew` 状态固定为 `2`。

**关键设计特点**：这两条副本记录中的 `userid` 和 `touserid` 字段含义**故意设计为不同**：

*   **收件箱副本 (`isnew=0` or `1`)**: `userid` = 发送者ID, `touserid` = 接收者ID（符合直觉）
*   **发件箱副本 (`isnew=2`)**: `userid` = 接收者ID, `touserid` = 发送者ID（字段含义颠倒）

这种设计在 `Messagelist_add.aspx.cs` 第254-272行明确体现：

```csharp
// 插入收件箱消息 (isnew=1)
DapperHelper.Execute(connectionString, insertInboxSql, new {
    UserId = userIdLong,        // 发送者ID
    ToUserId = toUserIdLong,    // 接收者ID
    // ... 其他参数
});

// 插入发件箱消息 (isnew=2) - 故意交换参数
DapperHelper.Execute(connectionString, insertOutboxSql, new {
    UserId = toUserIdLong,      // 接收者ID (故意颠倒)
    ToUserId = userIdLong,      // 发送者ID (故意颠倒)
    // ... 其他参数
});
```

#### 数据示例

```sql
-- 用户 1000 (Clover) 发送消息给用户 16325 (zeg)

-- 记录1: 收件箱副本 (isnew=1, 未读), 存储在用户 16325 的收件箱中
-- userid=1000 (发送者), touserid=16325 (接收者)
INSERT INTO wap_message (userid, touserid, isnew) VALUES (1000, 16325, 1);

-- 记录2: 发件箱副本 (isnew=2), 存储在用户 1000 的发件箱中
-- userid=16325 (接收者), touserid=1000 (发送者) - 字段含义颠倒
INSERT INTO wap_message (userid, touserid, isnew) VALUES (16325, 1000, 2);
```

### 系统消息 (`issystem=1`)

系统消息（如@提醒、空间留言等）采用**单条记录**存储。

#### 数据示例

```sql
-- 用户 16325 回复帖子，@了用户 1000
id=12288726, userid=16325, touserid=1000, issystem=1
```

### 发件箱查询

```sql
-- 获取用户的发件箱
-- 注意：由于字段设计，发件箱记录的当前用户ID存储在 touserid 字段
SELECT * FROM wap_message 
WHERE siteid = @SiteId 
  AND touserid = @UserId -- 当前用户ID在touserid字段
  AND isnew = 2  -- 发件箱
  AND issystem <> 2
ORDER BY addtime DESC;
```

## 📋 查询模式

### 获取用户对话

```sql
-- 获取用户A和用户B之间的完整对话（包含收发件箱和系统消息）
SELECT * FROM wap_message 
WHERE siteid = @SiteId 
  AND issystem <> 2
  AND ((userid = @UserA AND touserid = @UserB) 
       OR (userid = @UserB AND touserid = @UserA))
ORDER BY addtime DESC;
```

### 收件箱查询

```sql
-- 获取用户的收件箱
SELECT * FROM wap_message 
WHERE siteid = @SiteId 
  AND touserid = @UserId 
  AND isnew IN (0,1)  -- 已读和未读
  AND issystem <> 2
ORDER BY addtime DESC;
```

## ⚠️ 注意事项

### 数据类型限制

- `content` 字段为 `ntext` 类型，不能直接使用 `LEFT()` 函数
- 需要使用 `SUBSTRING(content, 1, n)` 或 `CAST(content AS NVARCHAR(n))` 进行截取

### 删除操作风险

- 消息删除可能影响对话双方的数据完整性
- 建议采用软删除或标记删除的方式
- 当前系统通过查询双方的记录，确保即使一方删除，另一方仍能看到对话历史。
- 删除收件箱消息**不会**影响发送者的发件箱记录。
- 对话详情页通过查询双方的所有记录，并进行去重，可以实现单向删除后对话历史依然完整的效果。
- 修复后的删除对话功能，只会删除当前用户侧的记录，不会影响对方。

## 🔬 已确认问题与修复方案

### 核心问题：消息方向显示错误

**问题描述**：
- 当对方删除收件箱记录后，数据库中仅剩发件箱副本 (`isnew=2`)
- 由于发件箱副本的 `userid`/`touserid` 字段含义颠倒，导致页面判断消息归属错误
- 表现为消息气泡显示在错误的一侧，昵称显示混乱

**根本原因**：
- 发件箱副本的字段设计：`userid` 存储接收者ID，`touserid` 存储发送者ID
- 页面显示逻辑依赖 `userid` 字段判断消息归属：`if (msg.userid == currentUserId)`
- 当仅剩发件箱副本时，此判断逻辑失效

**修复方案**：
在 `MessageList_view.aspx.cs` 中实现了增强版的 `FixSingleCopyMessageOrientation` 方法：

```csharp
/// <summary>
/// 修复消息字段方向异常问题
/// 检测并修复userid/touserid字段与nickname不匹配的消息记录
/// </summary>
private void FixSingleCopyMessageOrientation(List<wap_message_Model> messages, long currentUserId)
{
    foreach (var msg in messages)
    {
        bool needsFix = false;

        // 情况1：标准发件箱副本 (isnew = 2)
        if (msg.isnew == 2)
        {
            needsFix = true;
        }
        // 情况2：检测nickname与userid不匹配的异常数据
        else if (!string.IsNullOrEmpty(msg.nickname))
        {
            try
            {
                user_BLL userBll = new user_BLL(a);
                user_Model actualUser = userBll.getUserInfo(msg.userid.ToString(), siteid);

                // 如果nickname与userid不匹配，但与touserid匹配，说明字段颠倒
                if (actualUser != null && actualUser.nickname != msg.nickname)
                {
                    user_Model toUser = userBll.getUserInfo(msg.touserid.ToString(), siteid);
                    if (toUser != null && toUser.nickname == msg.nickname)
                    {
                        needsFix = true;
                    }
                }
            }
            catch { }
        }

        if (needsFix)
        {
            // 交换字段，恢复正确的发送关系
            long temp = msg.userid;
            msg.userid = msg.touserid;
            msg.touserid = temp;
        }
    }
}
```

### 🔍 深层问题发现：数据异常检测

**新发现的问题**：
除了标准的发件箱副本（`isnew=2`）外，数据库中还存在**字段方向异常但状态标记错误**的消息记录：
- 这些消息的 `isnew` 可能是 `0` 或 `1`（看似正常的收件箱消息）
- 但其 `userid`/`touserid` 字段与 `nickname` 字段不匹配
- 表现为：`nickname` 显示用户A的昵称，但 `userid` 指向用户B

**检测方法**：
通过比较消息记录的 `nickname` 字段与 `userid` 对应用户的实际昵称：
- 如果不匹配，再检查 `nickname` 是否与 `touserid` 对应用户的昵称匹配
- 如果匹配，说明 `userid` 和 `touserid` 字段颠倒了

**修复策略**：
1. **标准发件箱副本**：直接交换 `userid` 和 `touserid` 字段
2. **数据异常检测**：通过昵称匹配检测字段颠倒，然后交换字段
3. **关键时机**：在锚点消息加载后立即修复，确保后续的昵称获取和对话伙伴判断基于正确的数据

该修复方法已应用于：
1. **锚点消息修复**：在 `bookVo` 加载后立即修复，确保昵称获取基于正确数据
2. **页面初始加载**：修复消息列表的字段方向异常
3. **新版UI渲染**：在 `BuildPageModelForNewUI` 中修复上下文消息
4. **AJAX动态加载**：在动态加载更多消息时修复新加载的消息

### 衍生问题：双向删除对话

**问题描述**：
- `MessageList_Del.aspx.cs` 和 `MessageList_Clear.aspx.cs` 的"删除与对方所有对话"功能
- 原先的SQL会同时删除自己和对方的记录

**修复方案**：
已修改删除SQL，仅删除当前用户侧的副本：

```sql
DELETE FROM wap_message
WHERE siteid = @SiteId
  AND issystem <> 2
  AND touserid = @UserId
  AND userid = @ModelUserId
```

## 📅 更新记录

- **2025-01-10**: 发现并修复数据异常检测问题，增强了 `FixSingleCopyMessageOrientation` 方法，能够检测和修复所有字段方向异常的消息记录，不仅限于标准发件箱副本。
- **2024-12-31**: 基于代码深入分析，修正了对存储机制的描述，确认字段颠倒是故意的设计而非编程错误，并详细记录了修复方案的实现细节。
- **2024-12-30**: 根据最新的代码分析和功能修复，添加了查询条件问题、去重逻辑和页面功能修复的相关内容。
- **2024-12-29**: 根据最新的代码分析，修正了消息存储机制的描述，并确认了待验证的问题。
- **2024-12-28**: 初始文档创建，基于实际数据分析和代码调试结果。

---

> **说明**: 本文档基于实际开发过程中的深入代码分析和数据验证，准确反映了系统的设计特点和已实施的修复方案。
