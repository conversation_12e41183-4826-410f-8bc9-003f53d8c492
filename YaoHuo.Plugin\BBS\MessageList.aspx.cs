﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.Template.Models;
using YaoHuo.Plugin.BBS.Models;

namespace YaoHuo.Plugin.BBS
{
    public class MessageList : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string linkURL = "";

        public string condition = "";

        public string ERROR = "";

        public string INFO = "";

        public string key = "";

        public string types = "";

        public string backurl = "";

        public string linkTOP = "";

        public string issystem = "";

        public List<wap_message_Model> listVo = null;

        public long kk = 1L;

        public long index = 0L;

        public long total = 0L;

        public long pageSize = 10L;

        public long CurrentPage = 1L;

        protected void Page_Load(object sender, EventArgs e)
        {
            // 检查UI偏好并尝试渲染新版UI
            bool newVersionRendered = CheckAndHandleUIPreference();
            if (newVersionRendered)
            {
                return; // 阻止旧版代码执行
            }

            action = GetRequestValue("action");
            issystem = GetRequestValue("issystem");
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            IsLogin(userid, backurl);
            if (WapTool.GetArryString(siteVo.Version, '|', 53) == "1")
            {
                needPassWordToAdmin();
            }
            switch (action)
            {
                case "class":
                    showclass();
                    break;
                default:
                    showclass();
                    break;
                case "godel":
                    break;
            }
        }

        public void showclass()
        {
            key = GetRequestValue("key");
            types = GetRequestValue("types");
            if (types == "")
            {
                types = "0";
            }
            if (action == "save")
            {
                string requestValue = GetRequestValue("id");
                if (WapTool.IsNumeric(requestValue))
                {
                    // ✅ 使用DapperHelper进行安全的参数化更新操作
                    string connectionString = PubConstant.GetConnectionString(string_10);
                    string updateSql = "UPDATE wap_message SET issystem = 2 WHERE siteid = @SiteId AND touserid = @UserId AND id = @MessageId";
                    DapperHelper.Execute(connectionString, updateSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                        MessageId = DapperHelper.SafeParseLong(requestValue, "消息ID")
                    });
                }
            }

            // ✅ 使用QueryBuilder构建安全的查询条件
            var queryBuilder = new QueryBuilder()
                .Where("siteid = @ParamN", DapperHelper.SafeParseLong(siteid, "站点ID"));

            // 根据 types 构建基础查询条件
            if (types == "2") // 发件箱
            {
                queryBuilder.Where("touserid = @ParamN", DapperHelper.SafeParseLong(userid, "用户ID"))
                           .Where("isnew = @ParamN", 2);
            }
            else // 收件箱 (types == "0" 或其他默认情况)
            {
                queryBuilder.Where("touserid = @ParamN", DapperHelper.SafeParseLong(userid, "用户ID"))
                           .Where("isnew < @ParamN", 2);

                // 收件箱的 issystem 筛选逻辑
                if (WapTool.IsNumeric(issystem))
                {
                    queryBuilder.Where("issystem = @ParamN", DapperHelper.SafeParseLong(issystem, "系统消息类型"));
                }
                else
                {
                    queryBuilder.Where("issystem <> @ParamN", 2);
                }
            }

            // 追加多字段搜索条件 (搜索标题、内容、联系人昵称)
            if (!string.IsNullOrEmpty(key))
            {
                string searchKey = "%" + DapperHelper.LimitLength(key, 100) + "%";
                
                if (types == "2") // 发件箱：搜索收件人昵称
                {
                    // 发件箱中需要通过userid字段（收件人ID）关联user表查找昵称
                    queryBuilder.Where(@"(title LIKE @ParamN OR content LIKE @ParamN 
                                         OR EXISTS (SELECT 1 FROM [user] u WITH(NOLOCK) 
                                                   WHERE u.userid = wap_message.userid 
                                                   AND u.siteid = wap_message.siteid 
                                                   AND u.nickname LIKE @ParamN))", searchKey);
                }
                else // 收件箱：搜索发送者昵称
                {
                    queryBuilder.Where("(title LIKE @ParamN OR content LIKE @ParamN OR nickname LIKE @ParamN)", searchKey);
                }
            }

            try
            {
                pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);

                // ✅ 使用安全的分页查询
                string connectionString = PubConstant.GetConnectionString(string_10);

                if (GetRequestValue("page") != "")
                {
                    CurrentPage = long.Parse(GetRequestValue("page"));
                }

                // ✅ 使用安全的分页数据查询（包含总数计算），启用NOLOCK提升性能
                var result = PaginationHelper.GetPagedDataWithBuilder<wap_message_Model>(
                    connectionString,
                    "SELECT *",
                    "wap_message",
                    queryBuilder,
                    (int)CurrentPage,
                    (int)pageSize,
                    "ORDER BY id DESC",
                    useNoLock: true
                );

                // 从分页结果获取总数和数据
                total = result.Total;
                listVo = result.Data;

                // 重新检查当前页码
                CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
                index = pageSize * (CurrentPage - 1L);

                // 构建简化版的分页链接
                string linkKeyParam = string.IsNullOrEmpty(key) ? "" : $"&key={HttpUtility.UrlEncode(key)}";
                string linkIssystemParam = string.IsNullOrEmpty(issystem) ? "" : $"&issystem={issystem}";
                linkURL = $"{http_start}bbs/messagelist.aspx?types={types}{linkIssystemParam}{linkKeyParam}";
                linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, total, pageSize, CurrentPage, linkURL);
                linkURL = WapTool.GetPageLink(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL);

                if (types == "2" && listVo != null)
                {
                    user_BLL userBll = new user_BLL(string_10);
                    foreach (var msg in listVo)
                    {
                        var touser = userBll.getUserInfo(msg.userid.ToString(), siteid); // userid 是收件人ID
                        if (touser != null && !string.IsNullOrEmpty(touser.nickname))
                        {
                            msg.tonickname = touser.nickname;
                        }
                        else
                        {
                            msg.tonickname = msg.userid.ToString();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }

        /// <summary>
        /// 检查UI偏好并处理新版UI渲染
        /// </summary>
        /// <returns>是否成功渲染新版UI</returns>
        private bool CheckAndHandleUIPreference()
        {
            string uiPreference = Request.Cookies["ui_preference"]?.Value ?? "old";

            if (uiPreference == "new")
            {
                try
                {
                    RenderWithHandlebars();
                    return true;
                }
                catch (System.Threading.ThreadAbortException)
                {
                    return true; // 成功渲染
                }
                catch (Exception ex)
                {
                    ERROR = "新版模板加载失败: " + ex.Message;
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// 使用Handlebars渲染新版UI
        /// </summary>
        private void RenderWithHandlebars()
        {
            try
            {
                // 先执行数据加载逻辑
                LoadPageData();

                // 构建页面数据模型
                var pageModel = BuildMessageListPageModel();

                // 配置Header选项
                var headerOptions = new HeaderOptionsModel
                {
                    ShowViewModeToggle = false,
                    ShowMessageNotification = false, // 消息页面不显示铃铛图标
                    CustomButtons = new List<HeaderButtonModel>
                    {
                        new HeaderButtonModel
                        {
                            Id = "write-message-btn",
                            Icon = "edit",
                            Tooltip = "写信",
                            Link = "/bbs/messagelist_add.aspx"
                        }
                    }
                };

                // 使用TemplateService渲染页面
                string finalHtml = TemplateService.RenderPageWithLayout(
                    "~/Template/Pages/MessageList.hbs",
                    pageModel,
                    pageModel.PageTitle,
                    headerOptions
                );

                // 输出渲染结果
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write(finalHtml);
                Response.End();
            }
            catch (System.Threading.ThreadAbortException)
            {
                throw; // Response.End()的正常行为
            }
            catch (Exception ex)
            {
                // 错误处理
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}</div>");
                HttpContext.Current.ApplicationInstance.CompleteRequest();
            }
        }

        /// <summary>
        /// 加载页面数据（复用现有逻辑）
        /// </summary>
        private void LoadPageData()
        {
            action = GetRequestValue("action");
            issystem = GetRequestValue("issystem");
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            IsLogin(userid, backurl);
            if (WapTool.GetArryString(siteVo.Version, '|', 53) == "1")
            {
                needPassWordToAdmin();
            }

            // 执行相应的操作
            switch (action)
            {
                case "class":
                    showclass();
                    break;
                case "save":
                    showclass(); // save操作在showclass中处理
                    break;
                default:
                    showclass();
                    break;
            }
        }

        /// <summary>
        /// 构建MessageList页面数据模型
        /// </summary>
        /// <returns>页面数据模型</returns>
        private MessageListPageModel BuildMessageListPageModel()
        {
            var model = new MessageListPageModel();

            // 基础信息设置
            model.SiteId = siteid;
            model.ClassId = classid;
            model.BackUrl = HttpUtility.UrlEncode(backurl);
            model.MessageType = types ?? "0";
            model.FilterType = issystem ?? "";
            model.SearchKey = key ?? "";

            // 设置页面标题
            if (model.MessageType == "2")
            {
                model.PageTitle = "发件箱";
                model.ShowFilterActions = false; // 发件箱不显示筛选操作
            }
            else
            {
                model.PageTitle = "收件箱";
                model.ShowFilterActions = true;
            }

            // 处理消息和错误信息
            if (!string.IsNullOrEmpty(ERROR))
            {
                model.Message.SetError(ERROR);
            }
            if (!string.IsNullOrEmpty(INFO))
            {
                model.Message.SetSuccess(INFO);
            }

            // 构建消息列表数据
            if (listVo != null && listVo.Count > 0)
            {
                foreach (var msg in listVo)
                {
                    var messageItem = new MessageItemModel
                    {
                        Id = msg.id,
                        Title = msg.title ?? "",
                        TruncatedTitle = TruncateTitle(msg.title ?? "", 20),
                        IsNew = msg.isnew == 1,
                        IsSystem = msg.issystem == 1,
                        AddTime = msg.addtime,
                        FormattedTime = msg.addtime.ToString("yyyy/M/d HH:mm"),
                        FriendlyTime = GetFriendlyTime(msg.addtime),
                        MessageTypeClass = msg.issystem == 1 ? "system-message" : "chat-message"
                    };

                    // 根据消息类型设置显示信息
                    if (model.MessageType == "2") // 发件箱
                    {
                        // 发件箱中：msg.userid = 收信人ID, msg.touserid = 发送者ID(当前用户)
                        // 应该显示收信人的信息
                        var receiverAvatar = AvatarHelper.GetUserAvatar(msg.userid.ToString(), siteid, string_10, http_start);

                        messageItem.SenderName = msg.tonickname ?? msg.userid.ToString(); // 显示收信人昵称
                        messageItem.SenderId = msg.userid; // 收信人ID
                        messageItem.SenderAvatarUrl = receiverAvatar.AvatarUrl;
                        messageItem.SenderIsDefaultAvatar = receiverAvatar.IsDefaultAvatar;
                        messageItem.SenderFirstChar = receiverAvatar.FirstChar;
                    }
                    else // 收件箱
                    {
                        // 收件箱中：msg.userid = 发送者ID, msg.touserid = 收信人ID(当前用户)
                        // 应该显示发送者的信息
                        var senderAvatar = AvatarHelper.GetUserAvatar(msg.userid.ToString(), siteid, string_10, http_start);

                        messageItem.SenderName = msg.nickname ?? "";
                        messageItem.SenderId = msg.userid;
                        messageItem.SenderAvatarUrl = senderAvatar.AvatarUrl;
                        messageItem.SenderIsDefaultAvatar = senderAvatar.IsDefaultAvatar;
                        messageItem.SenderFirstChar = senderAvatar.FirstChar;
                    }

                    // 构建简化版的链接
                    string keyParam = string.IsNullOrEmpty(model.SearchKey) ? "" : $"&key={HttpUtility.UrlEncode(model.SearchKey)}";
                    string issystemParam = string.IsNullOrEmpty(model.FilterType) ? "" : $"&issystem={model.FilterType}";
                    messageItem.ViewUrl = $"{http_start}bbs/messagelist_view.aspx?types={model.MessageType}&id={msg.id}&page={CurrentPage}{issystemParam}{keyParam}";
                    messageItem.DeleteUrl = $"{http_start}bbs/messagelist_del.aspx?action=del&types={model.MessageType}&id={msg.id}&page={CurrentPage}{keyParam}";

                    // 收件箱且非收藏消息时可以收藏
                    if (model.MessageType == "0" && model.FilterType != "2")
                    {
                        messageItem.CanFavorite = true;
                        messageItem.FavoriteUrl = $"{http_start}bbs/messagelist.aspx?action=save&types={model.MessageType}&id={msg.id}&page={CurrentPage}{issystemParam}{keyParam}";
                    }

                    model.MessagesList.Add(messageItem);
                }

                // 计算未读消息数量
                if (model.MessageType == "0")
                {
                    model.UnreadCount = model.MessagesList.Count(m => m.IsNew);
                }
            }

            // 构建分页信息
            model.Pagination.CurrentPage = (int)CurrentPage;
            model.Pagination.Total = (int)total;
            model.Pagination.PageSize = (int)pageSize;
            model.Pagination.TotalPages = (int)Math.Ceiling((double)total / pageSize);
            model.Pagination.IsFirstPage = CurrentPage <= 1;
            model.Pagination.IsLastPage = CurrentPage >= model.Pagination.TotalPages;
            model.Pagination.ShowPagination = model.Pagination.TotalPages > 1;

            // 构建站点信息
            model.SiteInfo.SiteName = siteVo.sitename;
            model.SiteInfo.SiteUrl = http_start;

            // 设置收件箱完全为空状态（收件箱 + 无筛选 + 总数为0）
            model.IsInboxCompletelyEmpty = (model.MessageType == "0" &&
                                          string.IsNullOrEmpty(model.FilterType) &&
                                          model.Pagination.Total == 0);

            return model;
        }

        /// <summary>
        /// 截断标题
        /// </summary>
        private string TruncateTitle(string title, int maxLength)
        {
            if (string.IsNullOrEmpty(title) || title.Length <= maxLength)
                return title;
            return title.Substring(0, maxLength) + "...";
        }

        /// <summary>
        /// 获取友好时间显示
        /// </summary>
        private string GetFriendlyTime(DateTime dateTime)
        {
            var now = DateTime.Now;
            var timeSpan = now - dateTime;

            if (timeSpan.TotalMinutes < 1)
                return "刚刚";
            if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes}分钟前";
            if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours}小时前";
            if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays}天前";
            if (dateTime.Year == now.Year)
                return dateTime.ToString("M月d日");
            return dateTime.ToString("yyyy年M月d日");
        }

        /// <summary>
        /// 获取昵称首字符
        /// </summary>
        private string GetFirstChar(string nickname)
        {
            if (string.IsNullOrEmpty(nickname))
                return "?";
            return nickname.Substring(0, 1).ToUpper();
        }
    }
}