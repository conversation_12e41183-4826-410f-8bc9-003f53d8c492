using System;
using System.Web.UI;
using YaoHuo.Plugin.WebSite.Services.Config;

namespace YaoHuo.Plugin.Pages.IMAX
{
    /// <summary>
    /// IMAX影院分布页面
    /// 展示中国各地IMAX影院的详细信息
    /// </summary>
    public partial class IMAX : Page
    {
        /// <summary>
        /// 页面加载事件
        /// </summary>
        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                try
                {
                    // 设置页面基本信息
                    SetPageMetadata();
                }
                catch (Exception)
                {
                    // 静默处理错误
                }
            }
        }



        /// <summary>
        /// 设置页面元数据
        /// </summary>
        private void SetPageMetadata()
        {
            // 由于页面包含代码块，无法动态修改控件集合
            // 页面标题和meta标签已在ASPX中静态设置
        }



        /// <summary>
        /// 获取配置统计信息（用于调试）
        /// </summary>
        /// <returns>配置统计信息</returns>
        public string GetConfigStats()
        {
            try
            {
                return ConfigService.GetCacheStats();
            }
            catch (Exception ex)
            {
                return $"获取统计信息失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 刷新IMAX配置缓存（用于管理）
        /// </summary>
        public void RefreshImaxConfig()
        {
            try
            {
                ConfigService.RefreshConfig("ImaxCinemas");
            }
            catch (Exception)
            {
                // 静默处理错误
            }
        }

        /// <summary>
        /// 检查IMAX配置文件状态
        /// </summary>
        /// <returns>配置文件状态信息</returns>
        public string CheckImaxConfigStatus()
        {
            try
            {
                bool exists = ConfigService.ConfigExists("ImaxCinemas", "IMAX");
                var lastModified = ConfigService.GetConfigLastModified("ImaxCinemas", "IMAX");
                
                string status = $"配置文件存在: {(exists ? "是" : "否")}";
                if (lastModified.HasValue)
                {
                    status += $", 最后修改: {lastModified.Value:yyyy-MM-dd HH:mm:ss}";
                }
                
                return status;
            }
            catch (Exception ex)
            {
                return $"检查配置状态失败: {ex.Message}";
            }
        }
    }
}