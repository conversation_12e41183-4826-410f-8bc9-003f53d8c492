@font-face {
    font-family: Arvo;
    font-style: normal;
    font-weight: regular;
    src: url(//lib.baomitu.com/fonts/arvo/arvo-regular.eot);
    src: local('Arvo'),local('Arvo-Normal'),url(//lib.baomitu.com/fonts/arvo/arvo-regular.eot?#iefix) format('embedded-opentype'),url(//lib.baomitu.com/fonts/arvo/arvo-regular.woff2) format('woff2'),url(//lib.baomitu.com/fonts/arvo/arvo-regular.woff) format('woff'),url(//lib.baomitu.com/fonts/arvo/arvo-regular.ttf) format('truetype'),url(//lib.baomitu.com/fonts/arvo/arvo-regular.svg#Arvo) format('svg')
}

body {
    background-color: #e8e8e8
}

body,button,div,form,input,p {
    margin: 0;
    padding: 0
}

.aui-grids {
    position: relative;
    overflow: hidden;
    margin-top: 20px;
    margin-left: 3%
}

.aui-grids-item {
    font-family: Arvo,serif;
    width: 30.33333%;
    float: left;
    position: relative;
    z-index: 0;
    padding: .7rem 0;
    font-size: .28rem;
    border: 1px solid #1abc9c;
    border-radius: 5px;
    color: #1abc9c;
    text-align: center;
    margin-right: 3%;
    margin-bottom: 10px;
    height: 5rem;
    display: flex;
    background: rgba(255,255,255,.9);
    outline: none;
}

.aui-grids-item span {
    font-size: 1.55rem;
    width: 100%;
    display: block;
    align-self: center
}

.this-card {
    background: #1abc9c;
    border: 1px solid #1abc9c;
    color: #fff
}

.aui-cell-box {
    margin: 0 auto;
    background: rgba(255,255,255,.9);
    padding: 2%;
    box-shadow: 0 0 20px rgba(0,0,0,.3);
    text-align: center
}

.info-text {
    font-size: 1.3rem;
    color: #666;
    margin-top: .5rem
}

.btn {
    display: block;
    margin: 0 auto;
    margin-top: 2.5%;
    background: #1abc9c;
    color: #fff;
    padding: 3% 5%;
    font-size: 1.6rem;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    outline: none;
}

.aui-flexView {
    width: 100%;
    height: 100%;
    margin: 0 auto;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    flex-direction: column
}

.tip {
    color: red;
    font-size: 1.35rem
}

.tip a {
    color: #000
}

.space {
    margin-left: 2px;
    margin-right: 2px
}

.aui-navBar {
    color: #efeff2;
    font-size: 1.4rem;
    justify-content: center;
    align-items: center;
    height: 44px;
    position: relative;
    display: -ms-flexbox;
    display: flex;
    background-color: none;
    z-index: 1002;
    background: #303030;
    font-weight: 700
}

.aui-recharge-box {
    position: relative;
    overflow: hidden;
    background: #fff
}

body {
    color: #333;
    margin: 0;
    height: 100%;
    font-family: Arvo,serif,'Myriad Set Pro','Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;
    -webkit-font-smoothing: antialiased;
    font-weight: 400;
    background-repeat: no-repeat;
    background-attachment: fixed
}

* {
    box-sizing: border-box
}

button,input {
    -webkit-tap-highlight-color: transparent
}

@media screen and (min-width: 1441px) {
    .aui-flexView {
        box-shadow:0 2px 1px -1px rgba(0,0,0,.2),0 1px 1px 0 rgba(0,0,0,.14),0 1px 3px 0 rgba(0,0,0,.12);
        max-width: 720px;
        background-color: #fff
    }
}
.btn:hover {
    background: #20ab8e;
}