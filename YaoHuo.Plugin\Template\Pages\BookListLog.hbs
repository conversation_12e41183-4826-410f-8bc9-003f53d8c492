<!-- Font Awesome CSS -->
<link href="//lf6-cdn-tos.bytecdntp.com/cdn/expire-1-y/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">



<!-- 动态列表区域 -->
<div class="card">
    <div class="card-body">
        {{#if HasDynamics}}
        <!-- 动态列表 -->
        <div class="flex flex-col gap-3">
            {{#each DynamicsList}}
            <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <i class="dynamic-icon" data-content="{{LogInfo}}"></i>
                <div class="flex-1">
                    <p class="text-sm text-gray-700">{{raw LogInfo}}</p>
                    <p class="text-xs text-gray-500 mt-1">{{FriendlyTime}}</p>
                </div>
            </div>
            {{/each}}
        </div>
        
        <!-- 分页导航 -->
        {{#if Pagination.ShowPagination}}
        <div class="flex items-center justify-center gap-4 mt-4 pt-4 border-t border-border-light">
            <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed"
                    id="prevPageBtn"
                    {{#if Pagination.IsFirstPage}}disabled{{/if}}>
                <i class="fas fa-chevron-left"></i>
            </button>
            <div class="flex-1 text-center text-sm text-text-secondary px-2">
                第 {{Pagination.CurrentPage}} / {{Pagination.TotalPages}} 页
            </div>
            <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed"
                    id="nextPageBtn"
                    {{#if Pagination.IsLastPage}}disabled{{/if}}>
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
        {{/if}}
        
        {{else}}
        <!-- 空状态显示 -->
        <div class="text-center py-12 px-4 flex flex-col items-center justify-center">
            <div class="mb-4 w-20 h-20">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 500 500" fill="none" class="w-full h-full">
                    <circle cx="250" cy="250" r="200" fill="#F5F5F5"></circle>
                    <path d="M165 220C165 198.402 182.402 181 204 181H296C317.598 181 335 198.402 335 220V280C335 301.598 317.598 319 296 319H204C182.402 319 165 301.598 165 280V220Z" fill="white" stroke="#5EBCB0" stroke-width="4"></path>
                    <circle cx="190" cy="240" r="20" fill="#5EBCB0" opacity="0.7"></circle>
                    <circle cx="250" cy="240" r="20" fill="#5EBCB0" opacity="0.5"></circle>
                    <circle cx="310" cy="240" r="20" fill="#5EBCB0" opacity="0.3"></circle>
                    <path d="M170 340C170 329.507 178.507 321 189 321H311C321.493 321 330 329.507 330 340V340C330 350.493 321.493 359 311 359H189C178.507 359 170 350.493 170 340V340Z" fill="white" stroke="#5EBCB0" stroke-width="4"></path>
                    <path d="M190 160C190 149.507 198.507 141 209 141H291C301.493 141 310 149.507 310 160V160C310 170.493 301.493 179 291 179H209C198.507 179 190 170.493 190 160V160Z" fill="white" stroke="#5EBCB0" stroke-width="4"></path>
                </svg>
            </div>
            <div class="text-lg font-medium text-text-primary mb-1">
                {{#if Navigation.IsMyDynamics}}暂无{{WhoDisplay}}动态{{else}}暂无好友动态{{/if}}
            </div>
            <div class="text-sm text-text-secondary">
                {{#if Navigation.IsMyDynamics}}快去论坛发帖互动吧{{else}}快去添加好友吧{{/if}}
            </div>
        </div>
        {{/if}}
    </div>
</div>



<!-- 引入统一的动态图标匹配系统 -->
<script src="/Template/JS/Components/DynamicIcons.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 使用统一的动态图标匹配系统
    DynamicIconMatcher.init('.dynamic-icon').then(count => {
        console.log(`BookListLog页面: 初始化了 ${count} 个动态图标`);
    });

    // 初始化动态链接样式
    initDynamicLinks();

    // 初始化分页功能
    initPagination();

    // 初始化 Lucide 图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }
});

// 初始化动态链接样式
function initDynamicLinks() {
    // 为动态内容中的所有链接添加 dynamic-link 类
    document.querySelectorAll('.flex.flex-col.gap-3 .text-gray-700 a').forEach(link => {
        // 排除已经有特定样式的链接
        if (!link.classList.contains('enhanced-link') &&
            !link.classList.contains('btn-primary') &&
            !link.classList.contains('text-primary')) {
            link.classList.add('dynamic-link');
        }
    });
}

// 分页功能
function initPagination() {
    const prevBtn = document.getElementById('prevPageBtn');
    const nextBtn = document.getElementById('nextPageBtn');
    
    if (prevBtn && !prevBtn.disabled) {
        prevBtn.addEventListener('click', function() {
            const currentPage = {{Pagination.CurrentPage}};
            if (currentPage > 1) {
                navigateToPage(currentPage - 1);
            }
        });
    }
    
    if (nextBtn && !nextBtn.disabled) {
        nextBtn.addEventListener('click', function() {
            const currentPage = {{Pagination.CurrentPage}};
            const totalPages = {{Pagination.TotalPages}};
            if (currentPage < totalPages) {
                navigateToPage(currentPage + 1);
            }
        });
    }
}

// 导航到指定页面
function navigateToPage(page) {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('page', page);
    window.location.href = currentUrl.toString();
}


</script>