using System;
using System.Collections.Generic;

namespace YaoHuo.Plugin.WebSite.Services.Config.Models
{
    /// <summary>
    /// 动态图标配置根对象
    /// </summary>
    public class DynamicIconConfigRoot
    {
        /// <summary>
        /// 配置元数据
        /// </summary>
        public ConfigMetadata Config { get; set; } = new ConfigMetadata();

        /// <summary>
        /// 图标匹配规则列表
        /// </summary>
        public List<DynamicIconRule> IconRules { get; set; } = new List<DynamicIconRule>();

        /// <summary>
        /// 默认图标配置
        /// </summary>
        public DefaultIconConfig DefaultIcon { get; set; } = new DefaultIconConfig();

        /// <summary>
        /// 系统设置
        /// </summary>
        public DynamicIconSettings Settings { get; set; } = new DynamicIconSettings();
    }

    /// <summary>
    /// 动态图标匹配规则
    /// </summary>
    public class DynamicIconRule
    {
        /// <summary>
        /// 规则唯一标识
        /// </summary>
        public string Id { get; set; } = "";

        /// <summary>
        /// 匹配类型：startsWith, includes, equals, regex
        /// </summary>
        public string MatchType { get; set; } = "startsWith";

        /// <summary>
        /// 匹配模式/关键词
        /// </summary>
        public string Pattern { get; set; } = "";

        /// <summary>
        /// Font Awesome 图标类名
        /// </summary>
        public string Icon { get; set; } = "";

        /// <summary>
        /// Tailwind CSS 颜色类名
        /// </summary>
        public string Color { get; set; } = "";

        /// <summary>
        /// 规则描述
        /// </summary>
        public string Description { get; set; } = "";

        /// <summary>
        /// 优先级（数字越小优先级越高）
        /// </summary>
        public int Priority { get; set; } = 999;

        /// <summary>
        /// 是否启用此规则
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 获取完整的CSS类名
        /// </summary>
        public string FullClassName => $"{Icon} {Color} mt-1 dynamic-icon";

        /// <summary>
        /// 检查内容是否匹配此规则
        /// </summary>
        /// <param name="content">要检查的内容</param>
        /// <returns>是否匹配</returns>
        public bool IsMatch(string content)
        {
            if (string.IsNullOrEmpty(content) || string.IsNullOrEmpty(Pattern) || !Enabled)
                return false;

            switch (MatchType.ToLower())
            {
                case "startswith":
                    return content.StartsWith(Pattern, StringComparison.OrdinalIgnoreCase);
                case "includes":
                case "contains":
                    return content.Contains(Pattern);
                case "equals":
                    return content.Equals(Pattern, StringComparison.OrdinalIgnoreCase);
                case "regex":
                    try
                    {
                        return System.Text.RegularExpressions.Regex.IsMatch(content, Pattern);
                    }
                    catch
                    {
                        return false;
                    }
                default:
                    return content.StartsWith(Pattern, StringComparison.OrdinalIgnoreCase);
            }
        }
    }

    /// <summary>
    /// 默认图标配置
    /// </summary>
    public class DefaultIconConfig
    {
        /// <summary>
        /// 默认图标类名
        /// </summary>
        public string Icon { get; set; } = "fas fa-circle-notch";

        /// <summary>
        /// 默认颜色类名
        /// </summary>
        public string Color { get; set; } = "text-gray-400";

        /// <summary>
        /// 描述
        /// </summary>
        public string Description { get; set; } = "默认动态图标";

        /// <summary>
        /// 获取完整的CSS类名
        /// </summary>
        public string FullClassName => $"{Icon} {Color} mt-1 dynamic-icon";
    }

    /// <summary>
    /// 动态图标系统设置
    /// </summary>
    public class DynamicIconSettings
    {
        /// <summary>
        /// 是否启用缓存
        /// </summary>
        public bool EnableCache { get; set; } = true;

        /// <summary>
        /// 缓存超时时间（秒）
        /// </summary>
        public int CacheTimeout { get; set; } = 3600;

        /// <summary>
        /// 匹配失败时是否回退到默认图标
        /// </summary>
        public bool FallbackToDefault { get; set; } = true;

        /// <summary>
        /// 是否启用调试模式
        /// </summary>
        public bool DebugMode { get; set; } = false;
    }

    /// <summary>
    /// 动态图标匹配结果
    /// </summary>
    public class DynamicIconMatchResult
    {
        /// <summary>
        /// 是否匹配成功
        /// </summary>
        public bool IsMatched { get; set; }

        /// <summary>
        /// 匹配的规则
        /// </summary>
        public DynamicIconRule MatchedRule { get; set; }

        /// <summary>
        /// 最终的CSS类名
        /// </summary>
        public string ClassName { get; set; } = "";

        /// <summary>
        /// 图标类名
        /// </summary>
        public string Icon { get; set; } = "";

        /// <summary>
        /// 颜色类名
        /// </summary>
        public string Color { get; set; } = "";

        /// <summary>
        /// 匹配描述
        /// </summary>
        public string Description { get; set; } = "";

        /// <summary>
        /// 创建匹配成功的结果
        /// </summary>
        public static DynamicIconMatchResult Success(DynamicIconRule rule)
        {
            return new DynamicIconMatchResult
            {
                IsMatched = true,
                MatchedRule = rule,
                ClassName = rule.FullClassName,
                Icon = rule.Icon,
                Color = rule.Color,
                Description = rule.Description
            };
        }

        /// <summary>
        /// 创建使用默认图标的结果
        /// </summary>
        public static DynamicIconMatchResult Default(DefaultIconConfig defaultIcon)
        {
            return new DynamicIconMatchResult
            {
                IsMatched = false,
                MatchedRule = null,
                ClassName = defaultIcon.FullClassName,
                Icon = defaultIcon.Icon,
                Color = defaultIcon.Color,
                Description = defaultIcon.Description
            };
        }
    }
}
