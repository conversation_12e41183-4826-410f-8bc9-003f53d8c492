using YaoHuo.Plugin.Template.Models;

namespace YaoHuo.Plugin.BBS.Models
{
    /// <summary>
    /// ModifyPassword 页面数据模型
    /// </summary>
    public class ModifyPasswordPageModel : BasePageModel
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public ModifyPasswordPageModel()
        {
            PageTitle = "修改密码";
        }

        /// <summary>
        /// 表单数据
        /// </summary>
        public ModifyPasswordFormModel FormData { get; set; } = new ModifyPasswordFormModel();
    }

    /// <summary>
    /// 表单数据模型
    /// </summary>
    public class ModifyPasswordFormModel
    {
        /// <summary>
        /// 表单操作URL
        /// </summary>
        public string ActionUrl { get; set; }

        /// <summary>
        /// 隐藏字段
        /// </summary>
        public HiddenFieldsModel HiddenFields { get; set; } = new HiddenFieldsModel();
    }
}