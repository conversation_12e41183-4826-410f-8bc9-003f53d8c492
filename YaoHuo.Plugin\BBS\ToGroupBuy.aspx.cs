using System;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.WebSite.BBS.Service;

namespace YaoHuo.Plugin.BBS
{
    public class toGroupBuy : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string KL_CheckIPTime = PubConstant.GetAppString("KL_CheckIPTime");

        public string action = "";
        public string backurl = "";
        public string INFO = "";
        public string ERROR = "";
        public string toid = "";
        public long toNeedMoney = 0L;
        public long STATE = 0L;
        public long changeMoney = 0L;
        public string changePW = "";
        public string num = "";
        public wap2_smallType_Model idVo = null;

        // 新增公共属性，用于传递数据到前端
        public int CurrentRank { get; private set; } = 0;
        public int RemainingDays { get; private set; } = 0;
        public int TargetRank { get; private set; } = 0;
        public long CurrentId { get; private set; } = 0;
        public long ToidNum { get; private set; } = 0;

        protected void Page_Load(object sender, EventArgs e)
        {
            string infoFromQueryString = Request.QueryString.Get("info");
            if (!string.IsNullOrEmpty(infoFromQueryString))
            {
                INFO = infoFromQueryString;
            }
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            IsLogin(userid, backurl);
            action = GetRequestValue("action");
            changePW = GetRequestValue("changePW");
            toid = GetRequestValue("toid");

            // Validate and parse toid before using it
            long parsedToid;
            if (!long.TryParse(toid, out parsedToid))
            {
                ERROR = "参数错误：身份ID无效或缺失！"; // 设置错误信息
                // 为前端使用的公共属性分配默认值
                CurrentRank = 0;
                RemainingDays = 0;
                TargetRank = 0;
                CurrentId = 0;
                ToidNum = 0;
                return; // 如果toid无效，则停止处理
            }

            // 现在可以使用 parsedToid 进行后续操作，而不是直接使用 toid 字符串
            // 例如：idVo = wap2_smallType_BLL.GetModel(parsedToid);
            // 但是为了兼容您现有代码中多处使用 toid 字符串的情况，
            // 并且 toid 已经被验证是有效数字，您可以继续使用 toid 字符串，
            // 但更严谨的做法是在验证后统一使用 parsedToid。
            // 这里为了最小化改动，我们先保留使用 toid 字符串，因为已经验证过它的有效性。

            num = GetRequestValue("num");
            if (!WapTool.IsNumeric(num))
            {
                num = "1";
            }
            if (int.Parse(num) > 100)
            {
                num = "100";
            }
            if (toid == "105" && int.Parse(num) < 12)
            {
                num = "12";
            }
            wap2_smallType_BLL wap2_smallType_BLL = new wap2_smallType_BLL(string_10);
            idVo = wap2_smallType_BLL.GetModel(long.Parse(toid));
            if (idVo == null)
            {
                ShowTipInfo("不存在此身份级别！", "");
            }
            toNeedMoney = idVo.rank * long.Parse(num);
            STATE = toNeedMoney;
            if (STATE < 1L)
            {
                INFO = "CLOSE";
            }
            if (userVo.userid.ToString() == userVo.siteid.ToString())
            {
                // 移除站长购买限制
                // INFO = "MASTERNO";
            }

            // 获取当前用户的身份信息
            wap2_smallType_BLL currentTypeBLL = new wap2_smallType_BLL(string_10);
            var currentIdVo = currentTypeBLL.GetModel(userVo.SessionTimeout);
            if (currentIdVo != null)
            {
                CurrentRank = (int)currentIdVo.rank;
                CurrentId = currentIdVo.id;
                TimeSpan remainingTime = userVo.endTime - DateTime.Now;
                RemainingDays = remainingTime.Days > 0 ? remainingTime.Days : 0;
            }

            // 获取目标身份的等级和ID
            TargetRank = (int)idVo.rank;

            // 使用临时变量来解析 toid
            long tempToidNum = 0;
            long.TryParse(toid, out tempToidNum);
            ToidNum = tempToidNum;

            if (action == "add") // 直接使用 action 变量
            {
                addMoney();
            }
        }

        public void addMoney()
        {
            if (string.IsNullOrEmpty(INFO)) // 简化条件：仅当INFO为空或null时执行
            {
                if (STATE < 1L)
                {
                    INFO = "CLOSE";
                }
                else if (PubConstant.md5(changePW).ToLower() != userVo.password.ToLower())
                {
                    INFO = "PWERR";
                }
                else if (isCheckIPTime(long.Parse(KL_CheckIPTime)))
                {
                    INFO = "WAITING";
                }
                else if (userVo.RMB < 1m || userVo.RMB < (decimal)toNeedMoney)
                {
                    INFO = "NOTMONEY";
                }
                else
                {
                    string text = "";
                    DateTime newEndTime;
                    // 获取当前身份的rank（RMB价格）
                    wap2_smallType_BLL wap2_smallType_BLL = new wap2_smallType_BLL(string_10);
                    var currentIdVo = wap2_smallType_BLL.GetModel(userVo.SessionTimeout);
                    if (currentIdVo != null)
                    {
                        if (long.Parse(toid) == currentIdVo.id)
                        {
                            // 如果身份ID相同，直接延长有效期
                            newEndTime = userVo.endTime.AddDays(int.Parse(num) * 30);
                        }
                        else
                        {
                            // 不同价格身份之间的转换逻辑
                            if (currentIdVo.rank > 0 && idVo.rank > 0)
                            {
                                // 计算当前身份剩余价值
                                TimeSpan remainingTime = userVo.endTime - DateTime.Now;
                                int remainingDaysCalc = remainingTime.Days > 0 ? remainingTime.Days : 0;
                                decimal currentValue = (currentIdVo.rank / 30m) * remainingDaysCalc;

                                // 计算新身份对应的天数
                                decimal newIdentityDaysDecimal = (currentValue / idVo.rank) * 30m;
                                int newIdentityDays = (int)Math.Floor(newIdentityDaysDecimal);

                                // 计算购买的天数
                                int purchasedDays = int.Parse(num) * 30;

                                // 新有效期
                                newEndTime = DateTime.Now.AddDays(newIdentityDays + purchasedDays);
                            }
                            else
                            {
                                // 如果新身份不能购买或当前身份不能转换
                                INFO = "CLOSE";
                                return;
                            }
                        }
                    }
                    else
                    {
                        // 新身份的有效期从购买日开始计算
                        newEndTime = DateTime.Now.AddDays(int.Parse(num) * 30);
                    }

                    // 更新用户的身份和有效期
                    text = string.Concat(",endtime='", newEndTime, "',sessiontimeout=", idVo.id, " ");
                    string orderID = DateTime.Now.ToString("yyyyMMddHHmmss") + "-" + userid;
                    try
                    {
                        // ✅ 先计算新的RMB余额，避免SaveRMBLog中的SELECT操作导致死锁
                        decimal newRMBBalance = userVo.RMB - toNeedMoney;

                        // ✅ 使用Dapper修复SQL注入漏洞
                        UpdateUserIdentitySafely(toNeedMoney, idVo.id, newEndTime, siteid, userid);

                        // ✅ 记录RMB日志
                        SaveRMBLog(userid, "-2", "-" + toNeedMoney, userid, nickname, "购买身份级别[" + idVo.id + "]", orderID);

                        // ✅ 购买成功后清除用户缓存，确保身份信息立即更新
                        long userIdLong = long.Parse(userid);
                        UserInfoCacheService.ClearUserCache(userIdLong);

                        INFO = "OK";
                    }
                    catch
                    {
                        ERROR = "购买失败，请稍后重试";
                        INFO = "ERROR";
                    }
                }

                // 记录动态（必须在重定向之前）
                VisiteCount("使用RMB购买了身份");

                // POST后重定向
                string redirectUrl = string.Format("{0}bbs/togroupbuy.aspx?toid={1}&num={2}&info={3}",
                                                   http_start, toid, num, INFO);
                Response.Redirect(redirectUrl);
            }
        }

        /// <summary>
        /// 使用Dapper安全地更新用户身份信息，避免SQL注入
        /// </summary>
        /// <param name="needMoney">需要扣除的RMB金额</param>
        /// <param name="identityId">身份ID</param>
        /// <param name="endTime">有效期结束时间</param>
        /// <param name="siteId">站点ID</param>
        /// <param name="userId">用户ID</param>
        private void UpdateUserIdentitySafely(long needMoney, long identityId, DateTime endTime, string siteId, string userId)
        {
            try
            {
                string connectionString = PubConstant.GetConnectionString(string_10);
                string sql = "UPDATE [user] SET RMB = RMB - @NeedMoney, sessiontimeout = @IdentityId, endtime = @EndTime WHERE siteid = @SiteId AND userid = @UserId";

                var siteIdLong = DapperHelper.SafeParseLong(siteId, "站点ID");
                var userIdLong = DapperHelper.SafeParseLong(userId, "用户ID");

                int affectedRows = DapperHelper.Execute(connectionString, sql, new {
                    NeedMoney = needMoney,
                    IdentityId = identityId,
                    EndTime = endTime,
                    SiteId = siteIdLong,
                    UserId = userIdLong
                });

                if (affectedRows == 0)
                {
                    throw new InvalidOperationException("更新用户身份失败，可能用户不存在或条件不匹配");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"更新用户身份失败: {ex.Message}", ex);
            }
        }
    }
}