using Newtonsoft.Json;
using System;

namespace YaoHuo.Plugin.OAuth
{
    /// <summary>
    /// OAuth 2.0 令牌撤销端点
    /// 实现 RFC 7009 OAuth 2.0 Token Revocation
    /// </summary>
    public partial class Revoke : OAuthBasePage
    {

        /// <summary>
        /// JSON 响应内容
        /// </summary>
        public string jsonResponse { get; private set; }

        /// <summary>
        /// 页面加载事件
        /// </summary>
        protected void Page_Load(object sender, EventArgs e)
        {
            // 使用基类方法设置JSON响应格式和CORS头
            SetJsonResponse();
            SetCorsHeaders();

            try
            {
                if (Request.HttpMethod.ToUpper() != OAuthConstants.HTTP_METHOD_POST)
                {
                    throw new OAuth2Exception(OAuthConstants.INVALID_REQUEST, "仅支持 POST 请求");
                }

                ProcessRevokeRequest();
            }
            catch (OAuth2Exception ex)
            {
                // 记录OAuth错误
                var clientId = GetRequestValue("client_id");
                OAuthLogger.LogError(ex.Error, clientId, ex.ErrorDescription, Request.UserHostAddress, Request.UserAgent);

                HandleOAuth2Error(ex);
            }
            catch (Exception ex)
            {
                // 记录系统错误
                var clientId = GetRequestValue("client_id");
                OAuthLogger.LogError("server_error", clientId, ex.Message, Request.UserHostAddress, Request.UserAgent);

                LogSecurityEvent($"令牌撤销端点异常: {ex.Message}");
                HandleOAuth2Error(new OAuth2Exception(OAuthConstants.SERVER_ERROR, "服务器内部错误"));
            }

            // 输出JSON响应并结束请求
            Response.Write(jsonResponse);
            Response.Flush();
            Context.ApplicationInstance.CompleteRequest();
        }

        /// <summary>
        /// 处理令牌撤销请求
        /// </summary>
        private void ProcessRevokeRequest()
        {
            // 获取参数
            var token = GetRequestValue("token");
            var tokenTypeHint = GetRequestValue("token_type_hint");
            var clientId = GetRequestValue("client_id");
            var clientSecret = GetRequestValue("client_secret");

            // 验证必需参数
            if (string.IsNullOrEmpty(token))
                throw new OAuth2Exception(OAuthConstants.INVALID_REQUEST, "缺少 token 参数");

            // 撤销令牌（使用新的业务流程）
            bool revoked = false;
            long userId = 0;

            if (!string.IsNullOrEmpty(clientId) && !string.IsNullOrEmpty(clientSecret))
            {
                // 如果提供了客户端凭据，使用完整的业务流程验证
                revoked = OAuthService.RevokeToken(token, clientId, clientSecret);

                // 尝试获取用户ID用于日志记录
                try
                {
                    var userInfo = OAuthService.GetUserInfo(token);
                    if (userInfo != null)
                    {
                        // 使用反射获取userid属性
                        var userIdProperty = userInfo.GetType().GetProperty("userid");
                        if (userIdProperty != null)
                        {
                            userId = (long)userIdProperty.GetValue(userInfo);
                        }
                    }
                }
                catch
                {
                    // 忽略获取用户ID失败的情况
                }
            }
            else
            {
                // 如果没有提供客户端凭据，直接撤销（兼容性考虑）
                revoked = OAuthService.RevokeTokenDirect(token);
            }

            // 记录Token撤销日志
            OAuthLogger.LogTokenRevoke(clientId, userId, Request.UserHostAddress, Request.UserAgent);

            // 记录撤销日志
            LogSecurityEvent($"令牌撤销: Token={token.Substring(0, Math.Min(8, token.Length))}..., ClientId={clientId}, Success={revoked}");

            // 返回成功响应（根据 RFC 7009，无论令牌是否存在都返回成功）
            jsonResponse = JsonConvert.SerializeObject(new { success = true }, Formatting.Indented);
        }

        /// <summary>
        /// 处理 OAuth 2.0 错误
        /// </summary>
        private void HandleOAuth2Error(OAuth2Exception ex)
        {
            Response.StatusCode = OAuthConstants.HTTP_BAD_REQUEST;

            var errorResponse = new
            {
                error = ex.Error,
                error_description = ex.ErrorDescription
            };

            jsonResponse = JsonConvert.SerializeObject(errorResponse, Formatting.Indented);
            LogSecurityEvent($"令牌撤销端点错误: {ex.Error} - {ex.ErrorDescription}");
        }

    }
}