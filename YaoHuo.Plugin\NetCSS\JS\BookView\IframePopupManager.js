'use strict';

/**
 * IframePopupManager - 通用iframe弹窗管理器
 * 用于创建、显示和管理弹出的iframe窗口，如设置、搜索等功能
 */
var IframePopupManager = (function () {
    // 私有配置常量
    var DEFAULT_CONFIG = {
        TRANSITION_DELAY: 300,     // 过渡动画时间(ms)，与CSS一致
        MIN_HEIGHT: 100,           // iframe最小高度
        DEFAULT_HEIGHT: 210,       // iframe默认高度
        CLOSE_ON_OVERLAY_CLICK: true // 点击遮罩层是否关闭弹窗
    };

    // 创建管理器实例的工厂方法
    function createPopupManager(options) {
        // 合并默认配置和自定义配置
        var config = Object.assign({}, DEFAULT_CONFIG, options.config || {});
        var popupId = options.id || ('popup_' + Math.random().toString(36).substr(2, 9));
        
        // 私有状态变量
        var _state = {
            shouldRefreshOnClose: false,
            isInitialized: false,
            isOpen: false
        };

        // 缓存DOM元素
        var _elements = {
            popupContainer: null,
            overlay: null,
            triggerButton: options.triggerSelector ? document.querySelector(options.triggerSelector) : null,
            iframe: null,
            closeBtn: null
        };

        // 私有HTML模板
        var _templates = {
            containerHTML: function () {
                return `
                <div id="${popupId}_overlay" class="popup-overlay"></div>
                <div id="${popupId}_container" class="popup-container">
                    <header class="popup-header">
                        ${options.title || '弹窗'}
                        <button id="${popupId}_closeBtn" class="popup-close-btn">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" width="18" height="18" fill="currentColor" stroke="currentColor" stroke-width="20">
                                <path d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z"/>
                            </svg>
                        </button>
                    </header>
                    <div class="popup-content-area" style="${options.contentStyle || 'display:flex; flex-direction:column; padding:0;'}">
                        <iframe id="${popupId}_iframe" src="" frameborder="0" scrolling="${options.scrolling || 'no'}" style="border:none; width:100%; height:${options.defaultHeight || config.DEFAULT_HEIGHT}px; ${options.iframeStyle || 'flex-grow:1;'}"></iframe>
                    </div>
                </div>
                `;
            },
            errorMessage: `<div style="padding: 20px; text-align: center;">${options.errorMessage || '页面加载失败，请重试'}</div>`
        };

        // 错误处理函数
        function _handleIframeError() {
            if (_elements.popupContainer) {
                var contentArea = _elements.popupContainer.querySelector('.popup-content-area');
                if (contentArea) {
                    contentArea.innerHTML = _templates.errorMessage;
                } else {
                    _elements.popupContainer.innerHTML = _templates.errorMessage; // Fallback
                }
            }
        }

        // iframe消息处理函数
        function _handleIframeMessage(event) {
            if (!event.data) return;

            if (event.data.type === 'settingsChanged' && options.onSettingsChanged) {
                _state.shouldRefreshOnClose = !event.data.doNotRefresh;
                options.onSettingsChanged(event.data);
            }

            if (event.data.type === 'resize') {
                var iframeContentHeight = event.data.height;
                if (_elements.iframe) {
                    if (iframeContentHeight > config.MIN_HEIGHT) {
                        _elements.iframe.style.height = iframeContentHeight + 'px';
                    } else {
                        _elements.iframe.style.height = config.MIN_HEIGHT + 'px';
                    }
                }
            }

            // 调用自定义的iframe消息处理函数
            if (options.onIframeMessage) {
                options.onIframeMessage(event);
            }
        }

        // iframe加载处理函数
        function _handleIframeLoad() {
            try {
                if (!_elements.iframe) return;
                
                if (options.onIframeLoad) {
                    options.onIframeLoad(_elements.iframe);
                }
                
                // 处理iframe背景色
                if (options.setIframeBackground) {
                    try {
                        const iframeDocument = _elements.iframe.contentDocument || _elements.iframe.contentWindow.document;
                        if (iframeDocument && iframeDocument.body) {
                            iframeDocument.body.style.backgroundColor = options.backgroundColor || 'white';
                        }
                    } catch (e) {
                        console.error("Error accessing iframe content:", e);
                    }
                }
            } catch (e) {
                console.error("Error in iframe load handler:", e);
            }
        }

        // 创建和缓存DOM元素
        function _createAndCacheElements() {
            // 如果元素不存在，则创建
            if (!document.getElementById(`${popupId}_overlay`)) {
                document.body.insertAdjacentHTML('beforeend', _templates.containerHTML());
            }
            
            // 缓存元素引用
            _elements.popupContainer = document.getElementById(`${popupId}_container`);
            _elements.overlay = document.getElementById(`${popupId}_overlay`);
            _elements.iframe = document.getElementById(`${popupId}_iframe`);
            _elements.closeBtn = document.getElementById(`${popupId}_closeBtn`);
            
            // 绑定弹窗事件
            _bindPopupEvents();
        }

        // 移除DOM元素
        function _removeElements() {
            if (_elements.overlay) {
                _elements.overlay.remove();
                _elements.overlay = null;
            }
            if (_elements.popupContainer) {
                _elements.popupContainer.remove();
                _elements.popupContainer = null;
            }
            _elements.iframe = null;
            _elements.closeBtn = null;
        }

        // 绑定触发按钮事件
        function _bindTriggerEvents() {
            if (_elements.triggerButton) {
                _elements.triggerButton.addEventListener('click', function (event) {
                    event.preventDefault();
                    openPopup();
                });
            }
            // 监听iframe消息
            window.addEventListener('message', _handleIframeMessage);
        }

        // 绑定弹窗事件
        function _bindPopupEvents() {
            if (_elements.closeBtn) {
                _elements.closeBtn.addEventListener('click', closePopup);
            }
            
            if (_elements.overlay && config.CLOSE_ON_OVERLAY_CLICK) {
                _elements.overlay.addEventListener('click', function (event) {
                    if (event.target === _elements.overlay) {
                        closePopup();
                    }
                });
            }
            
            if (_elements.iframe) {
                _elements.iframe.addEventListener('load', _handleIframeLoad);
                _elements.iframe.addEventListener('error', _handleIframeError);
            }
        }

        // 打开弹窗
        function openPopup() {
            if (_state.isOpen) return;
            
            _state.shouldRefreshOnClose = false;
            _createAndCacheElements();
            
            if (_elements.iframe) {
                // 设置iframe高度
                _elements.iframe.style.height = (options.defaultHeight || config.DEFAULT_HEIGHT) + 'px';
                
                // 设置iframe src并添加时间戳防止缓存
                var timestamp = new Date().getTime();
                var iframeUrl = options.iframeUrl + (options.iframeUrl.includes('?') ? '&' : '?') + 't=' + timestamp;
                _elements.iframe.src = iframeUrl;
            }

            // 使用requestAnimationFrame使视觉更新更平滑
            requestAnimationFrame(function () {
                if (_elements.overlay) _elements.overlay.classList.add('active');
                if (_elements.popupContainer) _elements.popupContainer.classList.add('active');
                _state.isOpen = true;
                
                // 调用自定义打开回调
                if (options.onOpen) {
                    options.onOpen();
                }
            });
        }

        // 关闭弹窗
        function closePopup(e) {
            if (e) e.preventDefault();
            if (!_state.isOpen) return;
            
            if (_elements.popupContainer) _elements.popupContainer.classList.remove('active');
            if (_elements.overlay) _elements.overlay.classList.remove('active');
            
            setTimeout(function () {
                // 检查弹窗是否仍然需要关闭（没有被快速重新打开）
                if (_elements.popupContainer && !_elements.popupContainer.classList.contains('active')) {
                    if (_elements.iframe) {
                        _elements.iframe.src = 'about:blank'; // 在移除前清空iframe src
                    }
                    _removeElements();
                    _state.isOpen = false;
                    
                    // 页面刷新逻辑
                    if (_state.shouldRefreshOnClose && options.refreshOnClose !== false) {
                        location.reload();
                    }
                    
                    // 调用自定义关闭回调
                    if (options.onClose) {
                        options.onClose();
                    }
                }
            }, config.TRANSITION_DELAY);
        }

        // 初始化管理器
        function init() {
            if (_state.isInitialized) return;
            _bindTriggerEvents();
            _state.isInitialized = true;
            
            // 调用自定义初始化回调
            if (options.onInit) {
                options.onInit();
            }
        }

        // 返回公共API
        return {
            init: init,
            open: openPopup,
            close: closePopup
        };
    }

    // 返回工厂方法
    return {
        create: createPopupManager
    };
})();

// 初始化设置弹窗
document.addEventListener('DOMContentLoaded', function() {
    // 创建设置弹窗管理器
    var settingsPopup = IframePopupManager.create({
        id: 'miniSettings',
        title: '论坛设置',
        triggerSelector: '.BBSettings',
        iframeUrl: '/bbs/settings.aspx?iframe=true',
        contentStyle: 'display:flex; flex-direction:column; padding:0;',
        errorMessage: '设置页面加载失败，请<a href="/bbs/settings.aspx">单独打开设置</a>',
        onSettingsChanged: function(data) {
            // 特定于设置页面的处理
        }
    });
    
    // 创建搜索弹窗管理器
    var searchPopup = IframePopupManager.create({
        id: 'searchMain',
        title: '论坛搜索',
        triggerSelector: '.showSearch',
        iframeUrl: '/bbs/book_search_new.aspx?iframe=true',
        defaultHeight: 175,
        setIframeBackground: true,
        backgroundColor: 'white'
    });
    
    // 初始化弹窗管理器
    settingsPopup.init();
    searchPopup.init();
}); 