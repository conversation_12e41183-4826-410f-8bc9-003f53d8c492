{{#if Message.HasMessage}}
<div class="message {{Message.Type}}">
    <div>
        {{Message.Content}}
    </div>
</div>
{{/if}}

<!-- 统计面板（占位符） -->
<div class="card hidden" id="stats-panel">
    <div class="bg-gradient-to-br from-primary to-primary-dark p-4">
        <div class="flex items-center justify-between mb-2">
            <div class="text-white">
                <h3 class="text-lg font-medium">月度统计</h3>
                <p class="text-sm opacity-80">当前：{{Filter.ToYear}}年{{Filter.ToMonth}}月</p>
            </div>
        </div>
    </div>
    
    <div class="stats-grid" id="statsPanel">
        <div class="stats-item">
            <span class="text-text-primary font-medium transition-colors">+{{Statistics.FormattedMonthlyIncome}}</span>
            <span class="text-xs text-text-secondary transition-colors">本月收入</span>
        </div>
        <div class="stats-item">
            <span class="text-text-primary font-medium transition-colors">-{{Statistics.FormattedMonthlyExpense}}</span>
            <span class="text-xs text-text-secondary transition-colors">本月支出</span>
        </div>
        <div class="stats-item">
            <span class="text-text-primary font-medium transition-colors">{{Statistics.FormattedMonthlyNet}}</span>
            <span class="text-xs text-text-secondary transition-colors">本月净收入</span>
        </div>
        <div class="stats-item">
            <span class="text-text-primary font-medium transition-colors">功能开发中</span>
            <span class="text-xs text-text-secondary transition-colors">敬请期待</span>
        </div>
    </div>
</div>

<!-- 筛选卡片 -->
<div class="card">
    <div class="card-header">
        <div class="card-title">
            <i data-lucide="filter" class="card-icon"></i>
            筛选条件
        </div>
    </div>
    <div class="card-body">
                    <form action="{{SiteInfo.HttpStart}}bbs/banklist.aspx" method="get" id="filter-form" onsubmit="handleSearchSubmit(event)">
            
            <!-- 年份和月份 -->
            <div class="grid-2">
                <div class="form-group">
                    <label class="form-label">年份</label>
                    <select class="form-select" name="toyear" id="toyear">
                        {{#each Filter.YearOptions}}
                        <option value="{{Value}}" {{#if Selected}}selected{{/if}}>{{Text}}</option>
                        {{/each}}
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">月份</label>
                    <select class="form-select" name="tomonth" id="tomonth">
                        {{#each Filter.MonthOptions}}
                        <option value="{{Value}}" {{#if Selected}}selected{{/if}}>{{Text}}</option>
                        {{/each}}
                    </select>
                </div>
            </div>
            
            <!-- 管理员专用：ID号输入 -->
            {{#if IsAdmin}}
            <div class="form-group mb-4">
                <label class="form-label">用户ID</label>
                <input type="text" class="form-input" name="key" value="{{Filter.Key}}" placeholder="输入用户ID（留空查看所有用户）">
            </div>
            {{/if}}
            
            <!-- 搜索类型和关键字 -->
            <div class="grid-2">
                <div class="form-group">
                    <label class="form-label">搜索类型</label>
                    <select class="form-select" name="typeid" id="typeid">
                        {{#each Filter.SearchTypeOptions}}
                        <option value="{{Value}}" {{#if Selected}}selected{{/if}}>{{Text}}</option>
                        {{/each}}
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">关键字</label>
                    <input type="text" class="form-input" name="typekey" value="{{Filter.TypeKey}}" placeholder="输入搜索关键字">
                </div>
            </div>
            
            <!-- 按钮组 -->
            <div class="grid-2">
                <button type="submit" class="btn btn-primary">
                    <i data-lucide="search" class="w-4 h-4 mr-1"></i>
                    查询
                </button>
                <button type="button" class="btn btn-outline" onclick="resetFilter()">
                    <i data-lucide="rotate-ccw" class="w-4 h-4 mr-1"></i>
                    重置
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 交易记录表格 -->
<div class="card">
    <div class="card-header">
        <div class="card-title">
            <i data-lucide="receipt" class="card-icon"></i>
            交易记录
            {{#if Pagination.TotalItems}}
            <span class="text-sm text-text-secondary font-normal ml-1">(共{{Pagination.TotalItems}}条)</span>
            {{/if}}
        </div>
    </div>
    <div class="card-body p-0">
        {{#if BankLogList}}
        <div class="table-responsive">
            <table class="w-full">
                <thead class="bg-bg-gray-50 border-b border-border-light">
                    <tr>
                        <th class="px-4 py-3 text-center text-xs font-medium text-text-secondary uppercase tracking-wider">项目</th>
                        <th class="px-4 py-3 text-center text-xs font-medium text-text-secondary uppercase tracking-wider">交易金额</th>
                        <th class="px-4 py-3 text-center text-xs font-medium text-text-secondary uppercase tracking-wider">账户余额</th>
                        <th class="px-4 py-3 text-center text-xs font-medium text-text-secondary uppercase tracking-wider">操作人</th>
                        <th class="px-4 py-3 text-center text-xs font-medium text-text-secondary uppercase tracking-wider">时间</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-border-light">
                    {{#each BankLogList}}
                    <tr class="hover:bg-bg-gray-50 transition-colors cursor-pointer" onclick="showTransactionDetail(this)"
                        data-action-name="{{ActionName}}"
                        data-remark="{{Remark}}"
                        data-money="{{FormattedMoney}}"
                        data-is-positive="{{IsPositiveAmount}}"
                        data-left-money="{{LeftMoney}}"
                        data-opera-nickname="{{OperaNickname}}"
                        data-opera-userid="{{OperaUserId}}"
                        data-add-time="{{FormattedAddTime}}">
                        <td class="px-4 py-3 text-center align-middle">
                            <div class="text-sm text-text-primary break-words">{{ActionName}}</div>
                        </td>
                        <td class="px-4 py-3 text-center align-middle">
                            {{#if IsPositiveAmount}}
                            <span class="text-success break-words">+{{FormattedMoney}}</span>
                            {{else}}
                            <span class="text-danger break-words">{{FormattedMoney}}</span>
                            {{/if}}
                        </td>
                        <td class="px-4 py-3 text-center align-middle">
                            <span class="text-text-secondary break-words">{{LeftMoney}}</span>
                        </td>
                        <td class="px-4 py-3 text-center align-middle">
                            <div class="text-sm text-text-secondary break-words">{{OperaNickname}}</div>
                        </td>
                        <td class="px-4 py-3 text-center align-middle">
                            <span class="text-sm text-text-secondary break-words">{{ShortFormattedAddTime}}</span>
                        </td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
        {{else}}
        <div class="text-center py-8 text-text-secondary">
            <i data-lucide="inbox" class="w-12 h-12 mx-auto mb-3 opacity-50"></i>
            <p>暂无交易记录</p>
        </div>
        {{/if}}
        
        {{! 分页导航 - 内嵌在表格卡片内，用分割线分开 }}
        {{#if Pagination.ShowPagination}}
        <div class="border-t border-border-light py-4 px-4">
            <div class="flex items-center justify-center gap-4">
                <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light" onclick="navigatePage({{Pagination.CurrentPage}} - 1)" {{#if Pagination.IsFirstPage}}disabled{{/if}}>
                    <i data-lucide="chevron-left" class="w-5 h-5"></i>
                </button>
                <div class="flex-1 text-center text-sm text-text-secondary px-2">
                    第 {{Pagination.CurrentPage}} / {{Pagination.TotalPages}} 页
                </div>
                <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light" onclick="navigatePage({{Pagination.CurrentPage}} + 1)" {{#if Pagination.IsLastPage}}disabled{{/if}}>
                    <i data-lucide="chevron-right" class="w-5 h-5"></i>
                </button>
            </div>
        </div>
        {{/if}}
    </div>
</div>

<!-- 管理员操作 -->
{{#if IsAdmin}}
<div class="mx-4 mt-4">
    <button class="w-full bg-danger text-white border border-danger rounded-md px-4 py-3 font-medium text-sm transition-all duration-200 hover:bg-danger-dark hover:border-danger-dark flex items-center justify-center" id="clear-records-btn">
        <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
        删除所有会员记录(保留本月)
    </button>
</div>
{{/if}}



<!-- TypeScript模块加载 -->
<script type="module">
    // 加载BankList页面的TypeScript模块
    import '/Template/TS/pages/BankList.js?v=*************';

    console.log('BankList页面TypeScript模块已加载');
</script>





