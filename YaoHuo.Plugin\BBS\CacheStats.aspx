﻿<%@ Page Language="C#" Inherits="YaoHuo.Plugin.WebSite.MyPageWap" %>
<%@ Import Namespace="System" %>
<%@ Import Namespace="System.Text" %>
<%@ Import Namespace="YaoHuo.Plugin.WebSite" %>
<%@ Import Namespace="YaoHuo.Plugin.BBS" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%@ Import Namespace="YaoHuo.Plugin.WebSite.Tool" %>

<script runat="server">
    protected void Page_Load(object sender, EventArgs e)
    {
        // 设置响应类型
        Response.ContentType = "text/html; charset=utf-8";

        string action = Request.QueryString["action"];
        string siteid = Request.QueryString["siteid"] ?? "1000";

        // ✅ 权限检查：只允许管理员访问
        if (!IsAdminUser())
        {
            if (action != null)
            {
                Response.Write("您没有权限访问此功能！");
                return;
            }
            else
            {
                // 显示无权限页面
                ShowNoPermissionPage();
                return;
            }
        }

        if (action != null)
        {
            try
            {
                switch (action)
                {
                    case "stats":
                        ShowStats();
                        return;
                    case "clear_user":
                        Response.Write("用户缓存已清除！");
                        UserInfoCacheService.ClearAllUserCache();
                        return;
                    case "clear_block":
                        Response.Write("黑名单缓存已清除！");
                        UserBlockingService.ClearAllBlockCache();
                        return;
                    case "reset_stats":
                        Response.Write("统计数据已重置！");
                        UserInfoCacheService.ResetCacheStats();
                        return;
                }
            }
            catch (Exception ex)
            {
                Response.Write("操作失败: " + ex.Message);
                return;
            }
        }
    }

    private void ShowStats()
    {
        var sb = new StringBuilder();

        try
        {
            // 用户信息缓存统计
            string userCacheStats = UserInfoCacheService.GetCacheStats();
            sb.Append("<div class=\"stats\">");
            sb.Append("<h3>用户信息缓存</h3>");
            sb.Append("<p>" + userCacheStats + "</p>");
            sb.Append("</div>");

            // 黑名单缓存统计
            string blockCacheStats = UserBlockingService.GetCacheStats();
            sb.Append("<div class=\"stats\">");
            sb.Append("<h3>黑名单缓存</h3>");
            sb.Append("<p>" + blockCacheStats + "</p>");
            sb.Append("</div>");

            // 数据库身份配置缓存统计
            string dbIdentityCacheStats = DatabaseIdentityService.GetCacheStats();
            sb.Append("<div class=\"stats\">");
            sb.Append("<h3>数据库身份配置缓存</h3>");
            sb.Append("<p>" + dbIdentityCacheStats + "</p>");
            sb.Append("</div>");
        }
        catch (Exception ex)
        {
            sb.Append("<div class=\"error\">获取缓存统计失败: " + ex.Message + "</div>");
        }

        Response.Write(sb.ToString());
    }

    /// <summary>
    /// 检查是否为管理员用户
    /// </summary>
    /// <returns>是否为管理员</returns>
    private bool IsAdminUser()
    {
        try
        {
            // 检查用户是否已登录
            if (base.userid == "0" || base.userVo == null)
            {
                return false;
            }

            // 获取管理员级别
            string managerlvl = base.userVo.managerlvl ?? "02";

            // 只允许超级管理员(00)和管理员(01)访问
            return managerlvl == "00" || managerlvl == "01";
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 显示无权限页面
    /// </summary>
    private void ShowNoPermissionPage()
    {
        string html = @"
<!DOCTYPE html>
<html>
<head>
    <title>访问被拒绝</title>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 50px; background: #f5f5f5; text-align: center; }
        .container { max-width: 500px; margin: 0 auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .icon { font-size: 64px; color: #dc3545; margin-bottom: 20px; }
        .title { font-size: 24px; font-weight: bold; color: #333; margin-bottom: 15px; }
        .message { color: #666; margin-bottom: 30px; line-height: 1.5; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='icon'>🚫</div>
        <div class='title'>访问被拒绝</div>
        <div class='message'>
            抱歉，您没有权限访问缓存管理页面。<br>
            此功能仅限管理员使用。
        </div>
        <a href='javascript:history.back()' class='btn'>返回上一页</a>
    </div>
</body>
</html>";
        Response.Write(html);
    }
</script>

<!DOCTYPE html>
<html>
<head>
    <title>缓存统计</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .title { font-size: 24px; font-weight: bold; margin-bottom: 20px; color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .info { background: #d4edda; color: #155724; padding: 10px; border-radius: 4px; margin: 10px 0; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px; margin: 10px 0; border: 1px solid #f5c6cb; }
        .stats { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 15px 0; border: 1px solid #dee2e6; }
        .btn { display: inline-block; padding: 8px 16px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; border: none; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-warning:hover { background: #e0a800; }
        .loading { text-align: center; padding: 20px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">缓存统计信息</div>

        <div id="message"></div>
        <div id="stats-content" class="loading">正在加载统计信息...</div>

        <div style="margin-top: 20px;">
            <button class="btn btn-danger" onclick="clearCache('user')">清除用户缓存</button>
            <button class="btn btn-danger" onclick="clearCache('block')">清除黑名单缓存</button>
            <button class="btn btn-warning" onclick="resetStats()">重置统计</button>
            <button class="btn" onclick="loadStats()">刷新统计</button>
        </div>

        <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; font-size: 14px;">
            <p>说明：</p>
            <ul>
                <li>用户信息缓存：缓存用户基本信息，减少数据库查询</li>
                <li>黑名单缓存：缓存用户黑名单关系，提升过滤性能</li>
                <li>缓存时间：用户信息10分钟，黑名单5分钟</li>
                <li><strong>权限要求：仅限管理员访问</strong></li>
            </ul>
        </div>
    </div>

    <script>
        // 获取URL参数
        function getUrlParam(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name) || '1000';
        }

        const siteid = getUrlParam('siteid');

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="${type}">${message}</div>`;
            setTimeout(() => {
                messageDiv.innerHTML = '';
            }, 3000);
        }

        // 加载统计信息
        function loadStats() {
            document.getElementById('stats-content').innerHTML = '<div class="loading">正在加载统计信息...</div>';

            fetch(`?action=stats&siteid=${siteid}`)
                .then(response => response.text())
                .then(data => {
                    document.getElementById('stats-content').innerHTML = data;
                })
                .catch(error => {
                    document.getElementById('stats-content').innerHTML = '<div class="error">加载失败: ' + error.message + '</div>';
                });
        }

        // 清除缓存
        function clearCache(type) {
            if (!confirm(`确定要清除${type === 'user' ? '用户' : '黑名单'}缓存吗？`)) {
                return;
            }

            fetch(`?action=clear_${type}&siteid=${siteid}`)
                .then(response => response.text())
                .then(data => {
                    showMessage(data, 'info');
                    loadStats();
                })
                .catch(error => {
                    showMessage('操作失败: ' + error.message, 'error');
                });
        }

        // 重置统计
        function resetStats() {
            fetch(`?action=reset_stats&siteid=${siteid}`)
                .then(response => response.text())
                .then(data => {
                    showMessage(data, 'info');
                    loadStats();
                })
                .catch(error => {
                    showMessage('操作失败: ' + error.message, 'error');
                });
        }

        // 页面加载时获取统计信息
        window.onload = function() {
            loadStats();
        };
    </script>
</body>
</html>