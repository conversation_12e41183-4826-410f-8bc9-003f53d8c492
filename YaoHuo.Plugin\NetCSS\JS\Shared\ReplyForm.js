/**
 * ReplyForm.js - 回复表单及回复链接交互逻辑
 * 提供表单置顶、回复操作和相关UI交互通用功能
 */
(function () {
    'use strict';

    /**
     * 表单置顶状态
     * @type {Boolean}
     */
    var sticked = false;

    /**
     * 回复事件是否已绑定
     * @type {Boolean}
     */
    var replyAnyBound = false;

    /**
     * 保存对象引用以便后续清除
     */
    var currentRecontentElement = null;
    var currentClickHandler = null;

    /**
     * 内部辅助函数：精确清理回复特定功能添加的DOM元素
     * @param {HTMLFormElement} form 表单元素
     */
    function cleanReplySpecificElements(form) {
        if (!form) return;
        try {
            // 移除置顶时在表单开头添加的 <b> 和 <select>
            let firstChild = form.firstChild;
            if (firstChild && firstChild.nodeType === Node.ELEMENT_NODE && firstChild.tagName === 'B' &&
                firstChild.textContent && firstChild.textContent.startsWith('回复') && firstChild.textContent.endsWith('楼')) {
                let secondChild = firstChild.nextSibling; // 应该是由 insertAdjacentHTML 添加的 <select>
                form.removeChild(firstChild); // 移除 <b>
                if (secondChild && secondChild.nodeType === Node.ELEMENT_NODE && secondChild.tagName === 'SELECT' && secondChild.name === 'sendmsg2') {
                    form.removeChild(secondChild); // 移除 <select>
                }
            }

            // 移除置顶时在表单末尾添加的隐藏输入框
            var hiddenReply = form.querySelector('input[type="hidden"][name="reply"]');
            if (hiddenReply && hiddenReply.parentElement === form) form.removeChild(hiddenReply);
            var hiddenTouserid = form.querySelector('input[type="hidden"][name="touserid"]');
            if (hiddenTouserid && hiddenTouserid.parentElement === form) form.removeChild(hiddenTouserid);
        } catch (e) {
            console.warn("ReplyForm.js: Error during specific element cleanup:", e);
        }
    }

    /**
     * 将回复表单置顶
     * @param {Object} options 配置选项
     * @param {HTMLElement} options.container 容器元素
     * @param {HTMLFormElement} options.form 表单元素
     */
    function sticky(options) {
        var content = options.container || document.querySelector(".viewContent");
        var form = options.form || document.querySelector("form[name='f']");

        if (content && form) {
            // 先尝试移除，避免异常
            try {
                content.removeChild(form);
            } catch (e) {
                // 忽略可能的错误，例如form已经不在content中
            }

            var newDiv = document.createElement("div");
            var clazz = document.createAttribute("class");
            clazz.value = "sticky";
            newDiv.setAttributeNode(clazz);
            newDiv.appendChild(form);
            content.insertBefore(newDiv, content.firstChild);
        }
    }

    /**
     * 处理回复链接点击事件，实现回复特定用户的功能
     * @param {Object} options 配置选项
     * @param {HTMLElement} options.stickyElement 置顶元素
     * @param {HTMLElement} options.recontentElement 回复内容容器
     * @param {String} options.viewContentSelector 备用视图内容选择器(Book_Re.aspx页面专用)
     */
    function replyAny(options) {
        // 先重置现有绑定，确保不会重复
        resetReplyAnyBinding();

        var sticky = options.stickyElement || document.getElementsByClassName("sticky")[0];
        if (!sticky) {
            return;
        }

        // 找到内容容器元素
        var recontent = null;

        // 先尝试直接使用传入的元素
        if (options.recontentElement) {
            recontent = options.recontentElement;
        }
        // 然后尝试使用类选择器
        else if (document.getElementsByClassName("recontent").length > 0) {
            recontent = document.getElementsByClassName("recontent")[0];
        }
        // 最后尝试使用自定义选择器
        else if (options.viewContentSelector) {
            recontent = document.querySelector(options.viewContentSelector);
        }

        if (!recontent) {
            return;
        }

        // 保存引用以便后续清除
        currentRecontentElement = recontent;

        // 创建点击处理函数
        var clickHandler = function (event) {
            // 通过DomHelpers模块查找回复链接元素
            var replyLinkElement = window.DomHelpers ?
                window.DomHelpers.findParentReplyLink(event.target) :
                findParentReplyLinkFallback(event.target);

            // 如果找到了回复链接元素
            if (replyLinkElement) {
                event.preventDefault();

                var form = document.querySelector("form[name='f']");
                if (!form) {
                    return;
                }

                var replyLink = replyLinkElement.href;
                var replyMatch = /reply=(\d+)/.exec(replyLink);
                var touseridMatch = /touserid=(\d+)/.exec(replyLink);

                if (!replyMatch || !touseridMatch) {
                    return;
                }

                var reply = replyMatch[1];
                var touserid = touseridMatch[1];

                // 判断是否点击了当前已经激活的回复链接（用于取消）
                var isCurrentlyReplyingThis = false;
                if (sticked) {
                    var existingReplyInput = form.querySelector('input[type="hidden"][name="reply"]');
                    if (existingReplyInput && existingReplyInput.value === reply) {
                        isCurrentlyReplyingThis = true;
                    }
                }

                // 统一清理：在进行任何操作前，先清理掉之前可能添加的元素
                cleanReplySpecificElements(form);

                if (isCurrentlyReplyingThis) {
                    // 如果是点击当前已激活的回复链接，则取消置顶状态
                    sticky.style.cssText = "";
                    sticked = false;
                } else {
                    // 否则，为新的回复对象设置置顶状态
                    // 添加回复提示和隐藏字段
                    form.insertAdjacentHTML(
                        "afterbegin",
                        "<b>回复" + reply + '楼</b><select style="display: none;" name="sendmsg2"><option value="1">通知对方</option><option value="0">不予通知</option></select>'
                    );
                    form.insertAdjacentHTML(
                        "beforeend",
                        '<input type="hidden" name="reply" value="' + reply + '">'
                    );
                    form.insertAdjacentHTML(
                        "beforeend",
                        '<input type="hidden" name="touserid" value="' + touserid + '">'
                    );

                    // 置顶表单
                    sticky.style.cssText = "position: sticky; top: 0; ";
                    // try { sticky.click(); } catch (e) { } // 移除了对div.sticky的click调用，行为不明确
                    sticked = true;
                }
            }
        };

        // 保存处理函数引用以便后续移除
        currentClickHandler = clickHandler;

        // 绑定点击事件
        recontent.addEventListener('click', clickHandler);

        replyAnyBound = true;
    }

    /**
     * 重置回复表单绑定状态，允许重新绑定事件（用于加载更多后）
     */
    function resetReplyAnyBinding() {
        // 先移除现有的事件监听器
        if (replyAnyBound && currentRecontentElement && currentClickHandler) {
            currentRecontentElement.removeEventListener('click', currentClickHandler);
            currentRecontentElement = null;
            currentClickHandler = null;
        }

        replyAnyBound = false;
    }

    /**
     * 获取表单置顶状态
     * @returns {Boolean} 是否置顶
     */
    function getStickedStatus() {
        return sticked;
    }

    /**
     * 设置表单置顶状态
     * @param {Boolean} status 置顶状态
     */
    function setStickedStatus(status) {
        sticked = !!status;
    }

    /**
     * 新增：重置回复表单UI到初始状态
     * 用于异步回复成功后调用。
     */
    function resetFormUI() {
        var form = document.querySelector("form[name='f']");
        var stickyElement = document.querySelector(".sticky");

        if (form) {
            cleanReplySpecificElements(form);
        }

        if (stickyElement) {
            stickyElement.style.cssText = ""; // 移除置顶样式

            // 更精确地处理Book_View.aspx中可能存在的用户控件FaceAndReply生成的元素
            // 查找并移除回复特定楼层的提示文本（可能在form外部的其他容器中）
            var replyInstructions = stickyElement.querySelectorAll("b");
            for (var i = 0; i < replyInstructions.length; i++) {
                var el = replyInstructions[i];
                if (el.textContent && el.textContent.indexOf("回复") !== -1 && el.textContent.indexOf("楼") !== -1) {
                    try {
                        // 如果这个b标签包含在某个span中（如replyother），可能需要移除整个span
                        var parentSpan = el.closest("span.replyother");
                        if (parentSpan) {
                            parentSpan.remove();
                        } else {
                            el.remove();
                        }
                    } catch (e) {
                        console.warn("ReplyForm.js: Error removing reply instruction:", e);
                    }
                }
            }
        }

        // 移除可能存在于表单中但未被cleanReplySpecificElements捕获的隐藏字段
        var hiddenInputs = document.querySelectorAll('input[type="hidden"][name="reply"], input[type="hidden"][name="touserid"]');
        for (var j = 0; j < hiddenInputs.length; j++) {
            try {
                hiddenInputs[j].parentNode.removeChild(hiddenInputs[j]);
            } catch (e) {
                console.warn("ReplyForm.js: Error removing hidden field:", e);
            }
        }

        sticked = false; // 重置内部置顶状态
    }

    /**
     * 内部辅助函数：查找回复链接元素（DomHelpers不可用时的后备）
     * @param {HTMLElement} element 起始元素
     * @returns {HTMLElement|null} 回复链接元素或null
     */
    function findParentReplyLinkFallback(element) {
        while (element) {
            if (element.classList && (element.classList.contains('replyicon') || element.classList.contains('replyme'))) {
                return element;
            }
            element = element.parentElement;
        }
        return null;
    }

    /**
     * 初始化表单提交拦截（用于派币帖验证码）
     */
    function initFormSubmitInterception() {
        var form = document.querySelector("form[name='f']");
        if (!form) return;

        // 避免重复绑定
        if (form.hasAttribute('data-captcha-intercepted')) {
            return;
        }
        form.setAttribute('data-captcha-intercepted', 'true');

        form.addEventListener('submit', function(e) {
            if (window.FreeMoneyPostCaptcha && window.REQUIRES_CAPTCHA) {
                e.preventDefault();

                // 防抖处理
                if (window.FreeMoneyPostCaptcha.isProcessing) {
                    return;
                }

                window.FreeMoneyPostCaptcha.ensureCaptchaToken(function() {
                    // 验证通过后重新提交表单
                    // 移除拦截标记，避免无限循环
                    form.removeAttribute('data-captcha-intercepted');
                    form.submit();
                });
            }
        });
    }

    // 页面加载完成后初始化表单拦截
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(initFormSubmitInterception, 100);
    });

    // 导出公共函数
    window.ReplyForm = {
        sticky: sticky,
        replyAny: replyAny,
        resetReplyAnyBinding: resetReplyAnyBinding,
        getStickedStatus: getStickedStatus,
        setStickedStatus: setStickedStatus,
        resetFormUI: resetFormUI, // 导出新的UI重置函数
        initFormSubmitInterception: initFormSubmitInterception // 导出表单拦截初始化函数
    };
})();