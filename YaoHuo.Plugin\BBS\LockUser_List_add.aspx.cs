﻿using System;
using System.Linq;
using Dapper;
using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.WebSite.BBS.Service;

namespace YaoHuo.Plugin.BBS
{
    public class LockUser_List_add : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";
        public string id = "";
        public string toclassid = "";
        public string touserid = "";
        public string backurlid = "";
        public string lockdate = "";
        public string INFO = "";
        public string ERROR = "";

        public wap_bbsre_Model bbsReVo = null;

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            toclassid = GetRequestValue("toclassid");
            touserid = GetRequestValue("touserid");
            backurlid = GetRequestValue("backurlid");
            lockdate = GetRequestValue("lockdate");
            if (lockdate == "")
            {
                lockdate = "0";
            }
            CheckManagerLvl("04", classVo.adminusername, "bbs/book_list.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=" + classid);
            needPassWordToAdmin();
            if (!(action == "gomod"))
            {
                return;
            }
            try
            {
                bool flag = true;
                user_Model user_Model = null;
                if (!WapTool.IsNumeric(touserid))
                {
                    touserid = "0";
                }
                // ✅ 使用UserService获取用户信息
                string connectionString = PubConstant.GetConnectionString(string_10);
                user_Model = UserService.GetUserInfoSafely(touserid, siteid, connectionString);
                if (user_Model == null)
                {
                    flag = false;
                }
                else if (IsUserManager(touserid, user_Model.managerlvl, classVo.adminusername))
                {
                    flag = false;
                }
                if (!WapTool.IsNumeric(lockdate) || !WapTool.IsNumeric(touserid) || !WapTool.IsNumeric(toclassid))
                {
                    INFO = "NOTNUM";
                    return;
                }
                if (!flag)
                {
                    INFO = "NOTALLOW";
                    return;
                }

                // ✅ 使用TransactionHelper进行安全的事务性封禁操作
                // connectionString 已在上面定义
                long siteIdLong = DapperHelper.SafeParseLong(siteid, "站点ID");
                long userIdLong = DapperHelper.SafeParseLong(userid, "用户ID");
                long toUserIdLong = DapperHelper.SafeParseLong(touserid, "目标用户ID");
                long toClassIdLong = DapperHelper.SafeParseLong(toclassid, "版块ID");
                long lockDateLong = DapperHelper.SafeParseLong(lockdate, "封禁天数");

                TransactionHelper.ExecuteMoneyTransaction(connectionString, (connection, transaction) =>
                {
                    // 1. 删除该用户的所有旧加黑记录
                    string deleteSql = "DELETE FROM user_lock WHERE siteid = @SiteId AND lockuserid = @LockUserId";
                    int rowsAffected = connection.Execute(deleteSql, new {
                        SiteId = siteIdLong,
                        LockUserId = toUserIdLong
                    }, transaction);

                    // 2. 如果删除了旧记录，记录解除加黑的日志
                    if (rowsAffected > 0)
                    {
                        string clearLogSql = @"INSERT INTO wap_log(siteid, oper_userid, oper_nickname, oper_type, log_info, oper_ip)
                                              VALUES (@SiteId, @UserId, @Nickname, 0, @LogInfo, @IP)";
                        connection.Execute(clearLogSql, new {
                            SiteId = siteIdLong,
                            UserId = userIdLong,
                            Nickname = DapperHelper.LimitLength(nickname, 50),
                            LogInfo = $"清除用户ID{touserid}的所有加黑记录",
                            IP = DapperHelper.LimitLength(IP, 50)
                        }, transaction);
                    }

                    // 3. 添加新的加黑记录
                    string insertLockSql = @"INSERT INTO user_lock (siteid, lockuserid, lockdate, operdate, operuserid, classid)
                                            VALUES (@SiteId, @LockUserId, @LockDate, @OperDate, @OperUserId, @ClassId)";
                    connection.Execute(insertLockSql, new {
                        SiteId = siteIdLong,
                        LockUserId = toUserIdLong,
                        LockDate = lockDateLong,
                        OperDate = DateTime.Now,
                        OperUserId = userIdLong,
                        ClassId = toClassIdLong
                    }, transaction);

                    // 4. 记录加黑操作日志
                    string lockLogSql = @"INSERT INTO wap_log(siteid, oper_userid, oper_nickname, oper_type, log_info, oper_ip)
                                         VALUES (@SiteId, @UserId, @Nickname, 0, @LogInfo, @IP)";
                    connection.Execute(lockLogSql, new {
                        SiteId = siteIdLong,
                        UserId = userIdLong,
                        Nickname = DapperHelper.LimitLength(nickname, 50),
                        LogInfo = $"加黑用户ID{touserid}",
                        IP = DapperHelper.LimitLength(IP, 50)
                    }, transaction);
                });

                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }


    }
}