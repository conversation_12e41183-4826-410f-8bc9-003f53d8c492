import { DEFAULT_CONFIG } from '../types/CommonTypes.js';
import { ToastService } from './ToastService.js';
export class AjaxService {
    constructor() {
        this.activeRequests = new Map();
        this.loadingStates = new Map();
    }
    static getInstance() {
        if (!AjaxService.instance) {
            AjaxService.instance = new AjaxService();
        }
        return AjaxService.instance;
    }
    static get(url, config) {
        return AjaxService.getInstance().request({ ...config, url, method: 'GET' });
    }
    static post(url, data, config) {
        return AjaxService.getInstance().request({ ...config, url, method: 'POST', data });
    }
    static delete(url, config) {
        return AjaxService.getInstance().request({ ...config, url, method: 'DELETE' });
    }
    static cancelAll() {
        AjaxService.getInstance().cancelAllRequests();
    }
    async request(config) {
        const requestId = this.generateRequestId();
        const controller = new AbortController();
        this.activeRequests.set(requestId, controller);
        try {
            if (config.showLoading && config.loadingElement) {
                this.showLoading(config.loadingElement, config.loadingText);
            }
            const requestOptions = {
                method: config.method || 'GET',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    ...config.headers
                },
                signal: controller.signal,
                credentials: 'same-origin'
            };
            if (config.data && (config.method === 'POST' || config.method === 'PUT')) {
                if (typeof config.data === 'string') {
                    requestOptions.body = config.data;
                }
                else {
                    requestOptions.body = new URLSearchParams(config.data).toString();
                }
            }
            const timeout = config.timeout || DEFAULT_CONFIG.AJAX_TIMEOUT;
            const timeoutId = setTimeout(() => {
                controller.abort();
            }, timeout);
            const response = await fetch(config.url, requestOptions);
            clearTimeout(timeoutId);
            const result = await this.handleResponse(response, config);
            if (config.onSuccess) {
                config.onSuccess(result);
            }
            if (config.showToast && config.successMessage) {
                ToastService.showSuccess(config.successMessage);
            }
            return result;
        }
        catch (error) {
            return this.handleError(error, config);
        }
        finally {
            this.activeRequests.delete(requestId);
            if (config.showLoading && config.loadingElement) {
                this.hideLoading(config.loadingElement);
            }
            if (config.onComplete) {
                config.onComplete();
            }
        }
    }
    async handleResponse(response, _config) {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        }
        else {
            const text = await response.text();
            try {
                return JSON.parse(text);
            }
            catch {
                return this.parseTextResponse(text);
            }
        }
    }
    parseTextResponse(text) {
        const successKeywords = ['删除成功', '清空成功', '操作成功', 'success', 'deleted successfully'];
        const errorKeywords = ['删除失败', '清空失败', '操作失败', 'error', 'failed'];
        const lowerText = text.toLowerCase();
        for (const keyword of successKeywords) {
            if (lowerText.includes(keyword.toLowerCase())) {
                return { success: true, message: '操作成功', data: text };
            }
        }
        for (const keyword of errorKeywords) {
            if (lowerText.includes(keyword.toLowerCase())) {
                return { success: false, message: '操作失败', data: text };
            }
        }
        return { success: true, message: '操作完成', data: text };
    }
    handleError(error, config) {
        console.error('AJAX请求错误:', error);
        let errorMessage = config.errorMessage || '请求失败，请重试';
        if (error.name === 'AbortError') {
            errorMessage = '请求已取消';
        }
        else if (error.message) {
            errorMessage = error.message;
        }
        if (config.onError) {
            config.onError(error);
        }
        if (config.showToast !== false) {
            ToastService.showError(errorMessage);
        }
        return Promise.reject(error);
    }
    showLoading(element, loadingText) {
        if (this.loadingStates.has(element)) {
            return;
        }
        const originalContent = element.innerHTML;
        const originalDisabled = element.disabled || false;
        this.loadingStates.set(element, {
            element,
            originalContent,
            originalDisabled
        });
        const text = loadingText || '加载中...';
        element.innerHTML = `<i data-lucide="loader-2" class="w-4 h-4 animate-spin mr-2"></i>${text}`;
        element.disabled = true;
        if (typeof window.lucide !== 'undefined') {
            window.lucide.createIcons();
        }
    }
    hideLoading(element) {
        const loadingState = this.loadingStates.get(element);
        if (!loadingState) {
            return;
        }
        element.innerHTML = loadingState.originalContent;
        element.disabled = loadingState.originalDisabled;
        this.loadingStates.delete(element);
        if (typeof window.lucide !== 'undefined') {
            window.lucide.createIcons();
        }
    }
    cancelAllRequests() {
        for (const controller of this.activeRequests.values()) {
            controller.abort();
        }
        this.activeRequests.clear();
    }
    generateRequestId() {
        return `request-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    getActiveRequestCount() {
        return this.activeRequests.size;
    }
}
export function ajaxGet(url, config) {
    return AjaxService.get(url, config);
}
export function ajaxPost(url, data, config) {
    return AjaxService.post(url, data, config);
}
export function ajaxDelete(url, config) {
    return AjaxService.delete(url, config);
}
export default AjaxService;
