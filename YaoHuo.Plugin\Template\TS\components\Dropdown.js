export class DropdownComponent {
    constructor() {
        this.activeDropdowns = new Map();
        this.dropdownCounter = 0;
    }
    static getInstance() {
        if (!DropdownComponent.instance) {
            DropdownComponent.instance = new DropdownComponent();
        }
        return DropdownComponent.instance;
    }
    static create(triggerId, config) {
        return DropdownComponent.getInstance().createDropdown(triggerId, config);
    }
    static show(dropdownId) {
        DropdownComponent.getInstance().showDropdown(dropdownId);
    }
    static hide(dropdownId) {
        DropdownComponent.getInstance().hideDropdown(dropdownId);
    }
    static hideAll() {
        DropdownComponent.getInstance().hideAllDropdowns();
    }
    createDropdown(triggerId, config) {
        const dropdownId = this.generateDropdownId();
        const triggerElement = document.getElementById(triggerId);
        if (!triggerElement) {
            console.error(`Dropdown trigger element not found: ${triggerId}`);
            return '';
        }
        const dropdownElement = this.createDropdownElement(dropdownId, config);
        const container = triggerElement.parentElement;
        if (container) {
            if (getComputedStyle(container).position === 'static') {
                container.style.position = 'relative';
            }
            container.appendChild(dropdownElement);
        }
        this.bindTriggerEvents(triggerElement, dropdownId, config);
        this.activeDropdowns.set(dropdownId, dropdownElement);
        return dropdownId;
    }
    showDropdown(dropdownId) {
        const dropdown = this.activeDropdowns.get(dropdownId);
        if (!dropdown)
            return;
        this.hideAllDropdowns();
        dropdown.classList.add('show');
        this.adjustDropdownPosition(dropdown);
        this.bindOutsideClick(dropdownId);
    }
    hideDropdown(dropdownId) {
        const dropdown = this.activeDropdowns.get(dropdownId);
        if (!dropdown)
            return;
        dropdown.classList.remove('show');
        this.unbindOutsideClick(dropdownId);
    }
    hideAllDropdowns() {
        for (const dropdownId of this.activeDropdowns.keys()) {
            this.hideDropdown(dropdownId);
        }
    }
    createDropdownElement(dropdownId, config) {
        const dropdown = document.createElement('div');
        dropdown.id = dropdownId;
        dropdown.className = 'dropdown-menu';
        if (config.position) {
            this.applyPositionStyles(dropdown, config.position);
        }
        config.items.forEach(item => {
            if (item.divider) {
                const divider = document.createElement('div');
                divider.className = 'dropdown-divider';
                dropdown.appendChild(divider);
            }
            else {
                const menuItem = this.createMenuItem(item, dropdownId, config);
                dropdown.appendChild(menuItem);
            }
        });
        return dropdown;
    }
    createMenuItem(item, dropdownId, config) {
        const menuItem = document.createElement('a');
        menuItem.href = '#';
        menuItem.className = `dropdown-item ${item.disabled ? 'opacity-50 cursor-not-allowed' : ''}`;
        menuItem.setAttribute('data-action', item.action);
        if (item.icon) {
            const icon = document.createElement('i');
            icon.className = `w-4 h-4 mr-2`;
            icon.setAttribute('data-lucide', item.icon);
            menuItem.appendChild(icon);
        }
        const text = document.createElement('span');
        text.textContent = item.text;
        menuItem.appendChild(text);
        if (!item.disabled) {
            menuItem.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const customEvent = new CustomEvent('dropdown-item-click', {
                    detail: {
                        action: item.action,
                        item: item,
                        dropdownId: dropdownId
                    }
                });
                document.dispatchEvent(customEvent);
                if (config.closeOnClick !== false) {
                    this.hideDropdown(dropdownId);
                }
            });
        }
        return menuItem;
    }
    applyPositionStyles(dropdown, position) {
        dropdown.classList.remove('left-1/2', '-translate-x-1/2');
        switch (position) {
            case 'bottom-left':
                dropdown.classList.add('left-0');
                break;
            case 'bottom-right':
                dropdown.classList.add('right-0');
                break;
            case 'top-left':
                dropdown.classList.add('left-0', 'bottom-full', 'top-auto', 'mb-2', 'mt-0');
                break;
            case 'top-right':
                dropdown.classList.add('right-0', 'bottom-full', 'top-auto', 'mb-2', 'mt-0');
                break;
        }
    }
    bindTriggerEvents(trigger, dropdownId, config) {
        const triggerType = config.trigger || 'click';
        if (triggerType === 'click') {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                const dropdown = this.activeDropdowns.get(dropdownId);
                if (dropdown && dropdown.classList.contains('show')) {
                    this.hideDropdown(dropdownId);
                }
                else {
                    this.showDropdown(dropdownId);
                }
            });
        }
        else if (triggerType === 'hover') {
            trigger.addEventListener('mouseenter', () => {
                this.showDropdown(dropdownId);
            });
            trigger.addEventListener('mouseleave', () => {
                setTimeout(() => {
                    const dropdown = this.activeDropdowns.get(dropdownId);
                    if (dropdown && !dropdown.matches(':hover')) {
                        this.hideDropdown(dropdownId);
                    }
                }, 100);
            });
        }
    }
    adjustDropdownPosition(dropdown) {
        const rect = dropdown.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;
        if (rect.bottom > viewportHeight) {
            dropdown.style.bottom = '100%';
            dropdown.style.top = 'auto';
        }
        if (rect.right > viewportWidth) {
            dropdown.style.right = '0';
            dropdown.style.left = 'auto';
        }
    }
    bindOutsideClick(dropdownId) {
        const handler = (e) => {
            const dropdown = this.activeDropdowns.get(dropdownId);
            if (dropdown && !dropdown.contains(e.target)) {
                this.hideDropdown(dropdownId);
            }
        };
        document.addEventListener('click', handler);
        this.activeDropdowns.get(dropdownId).__outsideClickHandler = handler;
    }
    unbindOutsideClick(dropdownId) {
        const dropdown = this.activeDropdowns.get(dropdownId);
        if (dropdown && dropdown.__outsideClickHandler) {
            document.removeEventListener('click', dropdown.__outsideClickHandler);
            delete dropdown.__outsideClickHandler;
        }
    }
    generateDropdownId() {
        return `dropdown-${++this.dropdownCounter}-${Date.now()}`;
    }
}
export function createDropdown(triggerId, items) {
    return DropdownComponent.create(triggerId, {
        items,
        position: 'bottom-right',
        trigger: 'click',
        closeOnClick: true
    });
}
export function showDropdown(dropdownId) {
    DropdownComponent.show(dropdownId);
}
export function hideDropdown(dropdownId) {
    DropdownComponent.hide(dropdownId);
}
export default DropdownComponent;
