/**
 * BookReScript.js - Book_Re.aspx页面特有的脚本逻辑
 * 使用KL_common.js提供的异步加载功能，并初始化页面特有组件
 */
(function () {
    'use strict';

    /**
     * 初始化异步加载更多回复功能
     */
    function asyncReply() {
        let more = document.querySelector(".more");
        if (more) {
            let moreLink = more.querySelector("a");
            // 注意：Book_Re页面每页加载15条回复，而Book_View页面是30条
            let totalpage = Math.ceil(
                /getTotal=\d+/.exec(moreLink.href)[0].slice(9) / 15
            );
            let currpage = 1;
            let topage = 2;
            let tourl = moreLink.href.replace("&page=" + topage, "");
            let newMoreLink = document.createElement("a");
            let span1 = document.createElement("span");
            let span1_id = document.createAttribute("id");
            span1_id.value = "YH_show_loadimg";
            span1.setAttributeNode(span1_id);
            newMoreLink.appendChild(span1);
            let span2 = document.createElement("span");
            let span2_id = document.createAttribute("id");
            span2_id.value = "YH_show_tip";
            span2.setAttributeNode(span2_id);
            span2.appendChild(
                document.createTextNode("加载更多(" + currpage + "/" + totalpage + ")")
            );
            newMoreLink.appendChild(span2);
            newMoreLink.style.width = "50%";
            more.appendChild(newMoreLink);
            moreLink.style.width = "50%";
            more.appendChild(moreLink);

            // 使用KL_common.js中的KL_show_next函数加载更多
            newMoreLink.onclick = () => {
                KL_show_next(totalpage, 15, currpage, tourl, "page");
            }

            newMoreLink.addEventListener("click", function () {
                const textContentElements = document.querySelectorAll(".retext");
                textContentElements.forEach((element) => {
                    if (typeof processTextContent === 'function') {
                        processTextContent(element);
                    }
                });
            });
        }
    }

    /**
     * 异步提交回复
     */
    function asyncComment() {
        let form = document.getElementsByName('f')[0];
        if (!form) {
            return;
        }

        // 使用QuickReplyAjax模块的功能
        if (window.QuickReplyAjax) {
            window.QuickReplyAjax.initAsyncComment({
                form: form,
                onSuccess: function (result, html) {
                    let tip = '';
                    if (result.isSuccess) {
                        tip = '<div class="ui__alert"><div class="ui__alert_bg in"></div> <div class="ui__alert_content in"> <div class="ui__content_body"><h4 class="ui__title">回复成功</h4><div>获得妖晶' + result.yaogem + '，经验' + result.exp + '</div> </div></div></div>';
                    } else if (result.message) {
                        tip = '<div class="ui__alert"><div class="ui__alert_bg in"></div> <div class="ui__alert_content in"> <div class="ui__content_body"><h4 class="ui__title">' + (result.message.split('，')[0]) + '</h4><div>' + (result.message.split('，')[1] || '') + '</div> </div></div></div>';
                    }

                    // 清空文本框内容
                    form.querySelector('[name="content"]').value = '';

                    let recontent = document.getElementsByClassName('recontent')[0];

                    // 获取当前URL中的classid、id参数
                    const urlParams = new URLSearchParams(window.location.search);
                    const classidParam = urlParams.get('classid');
                    const idParam = urlParams.get('id');

                    fetch(`/bbs/book_re.aspx?classid=${classidParam}&id=${idParam}`)
                        .then(res => res.text())
                        .then(html => {
                            let newcontentMatch = /recontent">([\s\S]*?)<div class="btBox"/.exec(html);
                            let newcontent = newcontentMatch ? newcontentMatch[1] : '';
                            recontent.innerHTML = '<div id="retip">' + tip + '</div>' + newcontent;

                            // 首先重置ReplyForm的UI状态，清除"回复X楼"和置顶样式
                            if (window.ReplyForm && typeof window.ReplyForm.resetFormUI === 'function') {
                                window.ReplyForm.resetFormUI();
                            }

                            // 如果启用了自定义布局，应用新布局
                            if (typeof isCustomLayoutEnabled !== 'undefined' && isCustomLayoutEnabled) {
                                if (typeof applyNewLayoutToNewContent === 'function') {
                                    applyNewLayoutToNewContent();
                                }
                            }

                            // 重置sticky状态
                            let sticky = document.getElementsByClassName("sticky")[0];
                            if (sticky) sticky.style.cssText = "";

                            // 重新初始化回复功能
                            if (window.ReplyForm) {
                                initReplyForm(); // 使用新的初始化方法
                            }

                            // 🔧 修复：清空验证码令牌，确保下次回复时重新验证
                            const tokenInput = document.getElementById('gocaptcha-token');
                            if (tokenInput) {
                                tokenInput.value = '';
                            }

                            // 🔧 修复：重置验证码组件状态
                            if (window.FreeMoneyPostCaptcha && typeof window.FreeMoneyPostCaptcha.reset === 'function') {
                                window.FreeMoneyPostCaptcha.reset();
                            }

                            // 处理文本内容
                            const textContentElements = document.querySelectorAll(".retext");
                            textContentElements.forEach((element) => {
                                if (typeof processTextContent === 'function') {
                                    processTextContent(element);
                                }
                            });

                            // 设置提示自动关闭
                            const retip = document.getElementById('retip');
                            const alertBg = retip.querySelector('.ui__alert_bg');
                            const clearTip = () => {
                                if (hideTipTimeout) {
                                    retip.style.display = 'none';
                                    clearTimeout(hideTipTimeout);
                                }
                            };

                            let hideTipTimeout = setTimeout(clearTip, result.timeoutDuration);

                            // 🔧 修复：添加空值检查，防止空指针异常
                            if (alertBg) {
                                alertBg.addEventListener('click', clearTip);
                            }

                            const titleElement = retip.querySelector('.ui__title');
                            if (titleElement) {
                                titleElement.addEventListener('click', clearTip);
                            }
                        });
                },
                onError: function (error) {
                    // 用户提示
                    let recontent = document.getElementsByClassName('recontent')[0];
                    if (recontent) {
                        let tip = '<div class="ui__alert"><div class="ui__alert_bg in"></div> <div class="ui__alert_content in"> <div class="ui__content_body"><h4 class="ui__title">请求失败</h4><div>请检查网络连接或稍后重试</div> </div></div></div>';
                        recontent.insertAdjacentHTML('afterbegin', '<div id="retip">' + tip + '</div>');
                        const retip = document.getElementById('retip');
                        const alertBg = retip.querySelector('.ui__alert_bg');
                        const clearTip = () => {
                            if (hideTipTimeout) {
                                retip.style.display = 'none';
                                clearTimeout(hideTipTimeout);
                            }
                        };
                        let hideTipTimeout = setTimeout(clearTip, 3000);

                        // 🔧 修复：添加空值检查，防止空指针异常
                        if (alertBg) {
                            alertBg.addEventListener('click', clearTip);
                        }

                        const titleElement = retip.querySelector('.ui__title');
                        if (titleElement) {
                            titleElement.addEventListener('click', clearTip);
                        }
                    }
                }
            });
        } else {
            // 回退到原有的asyncComment实现（若需要）
        }
    }

    /**
     * 页面初始化
     */
    function init() {
        // 使用共享模块的DOM助手设置自适应文本框
        if (window.DomHelpers) {
            window.DomHelpers.setupAutoResizeTextareas();
        }

        // 初始化异步加载更多回复
        asyncReply();

        // 初始化异步评论
        asyncComment();

        // 稍微延迟初始化回复功能，确保DOM完全就绪
        setTimeout(function () {
            // 先初始化表单置顶
            if (window.ReplyForm) {
                var viewContent = document.querySelector(".viewContent");
                var form = document.querySelector("form[name='f']");

                if (viewContent && form) {
                    window.ReplyForm.sticky({
                        container: viewContent,
                        form: form
                    });

                    // 完成表单置顶后，初始化回复功能
                    initReplyForm();
                }
            } else {
                setTimeout(initReplyForm, 50);
            }
        }, 10);
    }

    // 兼容性方法：尝试检测ReplyForm模块是否已加载
    function ensureReplyFormAvailable() {
        // 检查模块是否加载
        if (!window.ReplyForm) {
            // 在10ms后重试
            setTimeout(function () {
                initReplyForm();
            }, 10);
            return false;
        }
        return true;
    }

    // 初始化回复功能
    function initReplyForm() {
        if (!ensureReplyFormAvailable()) {
            return; // 等待模块加载
        }

        // Book_Re.aspx页面结构分析：
        // 1. 有一个外层的.viewContent容器
        // 2. 回复列表在.recontent容器中
        var viewContent = document.querySelector(".viewContent");
        var stickyElement = document.querySelector(".sticky");
        var recontentElement = document.querySelector(".recontent");

        // 如果.sticky元素不存在，尝试自动创建
        if (!stickyElement && viewContent) {
            // 仅在元素不存在时尝试初始化置顶表单
            if (window.ReplyForm) {
                window.ReplyForm.sticky({
                    container: viewContent,
                    form: document.querySelector("form[name='f']")
                });
                // 重新获取sticky元素
                stickyElement = document.querySelector(".sticky");
            }
        }

        if (!recontentElement) {
            return; // 如果找不到recontent元素，无法初始化回复功能
        }

        // 使用更明确的参数调用ReplyForm
        window.ReplyForm.resetReplyAnyBinding();
        window.ReplyForm.replyAny({
            stickyElement: stickyElement,
            recontentElement: recontentElement
        });
    }

    // 导出必要的函数
    window.BookReScript = {
        asyncReply: asyncReply,
        asyncComment: asyncComment,
        init: init,
        initReplyForm: initReplyForm
    };

    // 在DOM加载完成后进行初始化
    document.addEventListener("DOMContentLoaded", init);
})(); 