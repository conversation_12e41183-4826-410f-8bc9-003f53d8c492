﻿/**
 * 帖子搜索功能模块
 */
const BookSearch = {
    /**
     * 提交搜索请求
     */
    submitSearch() {
        console.log('开始搜索...');
        const searchInput = document.getElementById('searchInput');
        const key = searchInput.value.trim();  // 添加 trim() 确保去除空格
        const type = document.querySelector('.search-type-text').textContent === '内容' ? 'content' : 'title';
        
        // 使用更现代的验证方式
        if (!key) {
            searchInput.focus();  // 将焦点放在输入框上
            return;
        }

        console.log('搜索类型:', type);
        console.log('搜索关键词:', key);

        const urlParams = new URLSearchParams(window.location.search);
        let pubValue = urlParams.get('pub');
        if (!pubValue && urlParams.get('type') === 'pub') {
            pubValue = urlParams.get('key');
            console.log('从type=pub中获取用户ID:', pubValue);
        }
        console.log('当前用户 ID:', pubValue);

        let searchUrl = `${window.baseUrl}bbs/book_list_search.aspx?action=search&siteid=${window.siteid}&classid=${window.classid}&type=${type}&key=${encodeURIComponent(key)}`;
        if (pubValue) {
            searchUrl += `&pub=${pubValue}`;
            console.log('添加pub参数到URL');
        }
        console.log('最终跳转 URL:', searchUrl);
        window.location.href = searchUrl;
    },

    /**
     * 更新搜索框占位符文本
     * @param {string} selectedType - 选中的搜索类型
     */
    updatePlaceholder(selectedType) {
        const searchInput = document.getElementById('searchInput');
        const urlParams = new URLSearchParams(window.location.search);
        let userId = urlParams.get('pub');
        if (!userId && urlParams.get('type') === 'pub') {
            userId = urlParams.get('key');
        }
        const placeholderText = userId ? `搜索${userId}的帖子${selectedType}` : `搜索${selectedType}`;
        searchInput.placeholder = placeholderText;
    },

    /**
     * 初始化事件监听器
     */
    initEventListeners() {
        const searchType = document.getElementById('searchType');
        const searchTypeText = searchType.querySelector('.search-type-text');
        const searchTypeArrow = searchType.querySelector('.search-type-arrow');
        const searchTypeOptions = searchType.querySelector('.search-type-options');
        const searchInput = document.getElementById('searchInput');

        // 回车键搜索
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.submitSearch();
            }
        });

        // 搜索类型下拉框点击
        searchType.addEventListener('click', () => {
            searchTypeOptions.style.display = searchTypeOptions.style.display === 'block' ? 'none' : 'block';
            searchTypeArrow.classList.toggle('open');
        });

        // 搜索类型选项点击
        searchTypeOptions.addEventListener('click', (e) => {
            if (e.target.classList.contains('search-type-option')) {
                const options = searchTypeOptions.querySelectorAll('.search-type-option');
                options.forEach(option => option.classList.remove('active'));
                e.target.classList.add('active');
                
                const selectedType = e.target.dataset.type;
                searchTypeText.textContent = selectedType;
                
                this.updatePlaceholder(selectedType);
                
                searchTypeOptions.style.display = 'none';
                searchTypeArrow.classList.remove('open');
                e.stopPropagation();
            }
        });

        // 点击外部关闭下拉框
        document.addEventListener('click', (e) => {
            if (!searchType.contains(e.target)) {
                searchTypeOptions.style.display = 'none';
                searchTypeArrow.classList.remove('open');
            }
        });
    },

    /**
     * 初始化搜索模块
     */
    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.initEventListeners();
        });
    }
};

// 初始化搜索功能
BookSearch.init();
