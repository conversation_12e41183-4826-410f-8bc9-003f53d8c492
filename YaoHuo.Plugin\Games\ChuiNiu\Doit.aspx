﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Doit.aspx.cs" Inherits="YaoHuo.Plugin.Games.ChuiNiu.Doit" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    StringBuilder strhtml = new StringBuilder();
    
    // 添加必要的CSS和JS
    string headHtml = "<meta charset=\"UTF-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\">\n<link rel=\"stylesheet\" href=\"/games/chuiniu/tailwind.min.css?2\"/> <link rel=\"stylesheet\" href=\"/games/chuiniu/styles.css?25\"/>\n<link rel=\"stylesheet\" href=\"//lf6-cdn-tos.bytecdntp.com/cdn/expire-1-y/font-awesome/6.0.0/css/all.min.css\"/>";
    Response.Write(WapTool.showTop("接受挑战", wmlVo, false, headHtml));
    
    // 头部
    strhtml.Append("<header class=\"bg-gradient-to-r from-teal-500 to-teal-700 shadow-md p-4 flex items-center sticky top-0 z-10 text-white\">");
    strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/index.aspx\" class=\"text-white mr-4\">");
    strhtml.Append("<i class=\"fas fa-arrow-left\"></i>");
    strhtml.Append("</a>");
    strhtml.Append("<h1 class=\"text-xl font-bold\">接受挑战</h1>");
    strhtml.Append("</header>");
    
    strhtml.Append("<main class=\"p-4 pb-20 max-w-lg mx-auto\">");
    
    // 显示错误消息
    if (!string.IsNullOrEmpty(this.ERROR))
    {
        strhtml.Append("<div class=\"mb-4 p-4 bg-red-100 text-red-700 rounded-lg border border-red-200\">");
        strhtml.Append(this.ERROR);
        strhtml.Append("</div>");
    }
    
    // 显示INFO状态消息
    if (this.INFO == "NULL")
    {
        strhtml.Append("<div class=\"mb-4 p-4 bg-amber-100 text-amber-700 rounded-lg border border-amber-200\">");
        strhtml.Append("只能数值且不能为0！");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "WAITING")
    {
        strhtml.Append("<div class=\"mb-4 p-4 bg-amber-100 text-amber-700 rounded-lg border border-amber-200\">");
        strhtml.Append("<b>请再过" + this.KL_CheckIPTime + "秒后操作！</b>");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "NUMBER")
    {
        strhtml.Append("<div class=\"mb-4 p-4 bg-amber-100 text-amber-700 rounded-lg border border-amber-200\">");
        strhtml.Append("<b>只能录入数值！或不在范围内</b>");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "NOMONEY")
    {
        strhtml.Append("<div class=\"mb-4 p-4 bg-amber-100 text-amber-700 rounded-lg border border-amber-200\">");
        strhtml.Append("<b>抱歉，你的" + siteVo.sitemoneyname + "不够了！</b>");
        strhtml.Append("</div>");
    }
    
    // 基于INFO状态显示不同的内容
    if (this.INFO == "OK")
    {
        // 首先显示结果卡片（原来在第二位的卡片移到前面）
        strhtml.Append("<section class=\"bg-white rounded-xl shadow-sm p-3 mb-4 border border-gray-100 overflow-hidden\">");
        
        // 详情内容，进一步减小内边距
        strhtml.Append("<div class=\"p-1\">");
        
        // 结果展示 - 先显示结果，后显示双方选择
        strhtml.Append("<div class=\"detail-item\">");
        
        if (bookVo.state == 1) // 应战者获胜
        {
            strhtml.Append("<div class=\"bg-gradient-to-r from-teal-500 to-teal-600 p-4 rounded-lg text-white text-center\">");
            // 将原本在顶部的图标和文字移到这里
            strhtml.Append("<div class=\"flex items-center justify-center\">");
            strhtml.Append("<i class=\"fas fa-trophy text-yellow-300 text-xl mr-2\"></i>");
            strhtml.Append("<span class=\"font-bold text-lg\">恭喜您，打赢了！</span>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"flex items-center justify-center mt-2\">");
            strhtml.Append("<p class=\"font-bold text-lg mr-1\">+" + (bookVo.myMoney * per) / 100 + "</p>");
            strhtml.Append("<span class=\"coin-icon\">");
            strhtml.Append("<i class=\"fas fa-coins text-xs\"></i>");
            strhtml.Append("</span>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
        }
        else // 应战者失败 (bookVo.state == 2)
        {
            strhtml.Append("<div class=\"bg-gradient-to-r from-red-500 to-red-600 p-4 rounded-lg text-white text-center\">");
            // 将原本在顶部的图标和文字移到这里
            strhtml.Append("<div class=\"flex items-center justify-center\">");
            strhtml.Append("<i class=\"fas fa-times-circle text-gray-200 text-xl mr-2\"></i>");
            strhtml.Append("<span class=\"font-bold text-lg\">很遗憾，您输了！</span>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"flex items-center justify-center mt-2\">");
            strhtml.Append("<p class=\"font-bold text-lg mr-1\">-" + bookVo.myMoney + "</p>");
            strhtml.Append("<span class=\"coin-icon\">");
            strhtml.Append("<i class=\"fas fa-coins text-xs\"></i>");
            strhtml.Append("</span>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
        }
        
        strhtml.Append("</div>");
        
        // 双方选择 - 移到结果后面，减少上边距
        strhtml.Append("<div class=\"detail-item mt-1 mb-1\">");
        strhtml.Append("<div class=\"space-y-2 mt-1\">");
        
        // 发起者选择 - 绿色背景
        strhtml.Append("<div class=\"bg-green-50 border border-green-200 p-3 rounded-lg flex justify-between items-center\">");
        strhtml.Append("<div class=\"flex items-center\">");
        strhtml.Append("<div class=\"w-8 h-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-2\">");
        strhtml.Append("<i class=\"fas fa-user text-sm\"></i>");
        strhtml.Append("</div>");
        strhtml.Append("<div>");
        strhtml.Append("<p class=\"text-sm text-gray-600\">发起者选择:</p>");
        strhtml.Append("<p class=\"font-medium text-gray-800\">答案" + bookVo.myAnswer + " (" + (bookVo.myAnswer == 1 ? bookVo.Answer1 : bookVo.Answer2) + ")</p>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        // 应战者选择 - 根据 bookVo.state 决定样式
        if (bookVo.state == 1) // 应战者获胜
        {
            strhtml.Append("<div class=\"bg-green-50 border border-green-200 p-3 rounded-lg flex justify-between items-center\">");
            strhtml.Append("<div class=\"flex items-center\">");
            strhtml.Append("<div class=\"w-8 h-8 rounded-full bg-green-100 text-green-600 flex items-center justify-center mr-2\">");
            strhtml.Append("<i class=\"fas fa-user text-sm\"></i>");
            strhtml.Append("</div>");
            strhtml.Append("<div>");
            strhtml.Append("<p class=\"text-sm text-gray-600\">您的选择:</p>");
            strhtml.Append("<p class=\"font-medium text-gray-800\">答案" + bookVo.winAnswer + " (" + (bookVo.winAnswer == 1 ? bookVo.Answer1 : bookVo.Answer2) + ")</p>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"bg-green-100 text-green-700 rounded-full w-8 h-8 flex items-center justify-center\">");
            strhtml.Append("<i class=\"fas fa-check\"></i>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
        }
        else // 应战者失败 (bookVo.state == 2)
        {
            strhtml.Append("<div class=\"bg-red-50 border border-red-200 p-3 rounded-lg flex justify-between items-center\">");
            strhtml.Append("<div class=\"flex items-center\">");
            strhtml.Append("<div class=\"w-8 h-8 rounded-full bg-red-100 text-red-600 flex items-center justify-center mr-2\">");
            strhtml.Append("<i class=\"fas fa-user text-sm\"></i>");
            strhtml.Append("</div>");
            strhtml.Append("<div>");
            strhtml.Append("<p class=\"text-sm text-gray-600\">您的选择:</p>");
            strhtml.Append("<p class=\"font-medium text-gray-800\">答案" + bookVo.winAnswer + " (" + (bookVo.winAnswer == 1 ? bookVo.Answer1 : bookVo.Answer2) + ")</p>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"bg-red-100 text-red-700 rounded-full w-8 h-8 flex items-center justify-center\">");
            strhtml.Append("<i class=\"fas fa-times\"></i>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
        }
        
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        strhtml.Append("</div>");
        strhtml.Append("</section>");
        
        // 然后显示挑战详情卡片（原来在第一位的卡片移到后面）
        strhtml.Append("<section class=\"bg-white rounded-xl shadow-sm p-6 mb-3 border border-gray-100\">");
        strhtml.Append("<div class=\"mb-4 pb-4 border-b border-gray-100\">");
        strhtml.Append("<p class=\"text-sm text-gray-500 mb-1\">发起者</p>");
        strhtml.Append("<div class=\"flex items-center\">");
        strhtml.Append("<div class=\"bg-gradient-to-r from-green-400 to-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 shadow-sm\">");
        strhtml.Append("<i class=\"fas fa-user text-sm\"></i>");
        strhtml.Append("</div>");
        strhtml.Append("<a href=\"" + this.http_start + "bbs/userinfo.aspx?touserid=" + bookVo.userid + "\" class=\"font-semibold text-gray-800\">" + bookVo.nickName + "</a>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        strhtml.Append("<div class=\"mb-4 pb-4 border-b border-gray-100\">");
        strhtml.Append("<p class=\"text-sm text-gray-500 mb-1\">赌注</p>");
        strhtml.Append("<div class=\"flex items-center\">");
        strhtml.Append("<p class=\"font-bold text-lg text-amber-600\">" + bookVo.myMoney + "</p>");
        strhtml.Append("<span class=\"coin-icon ml-1\">");
        strhtml.Append("<i class=\"fas fa-coins text-xs\"></i>");
        strhtml.Append("</span>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        strhtml.Append("<div class=\"mb-5\">");
        strhtml.Append("<p class=\"text-sm text-gray-500 mb-2\">挑战问题</p>");
        strhtml.Append("<div class=\"bg-gray-50 p-4 rounded-lg\">");
        strhtml.Append("<p class=\"text-gray-800 text-lg font-medium\">\"" + bookVo.Question + "\"</p>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        // 选项部分 - 仅显示，不可交互
        strhtml.Append("<div class=\"space-y-3\">");
        strhtml.Append("<p class=\"text-sm text-gray-500 mb-1\">选项：</p>");
        
        // 已提交答案，简单显示选项
        strhtml.Append("<div class=\"border rounded-lg p-4 bg-gray-50\">");
        strhtml.Append("<div class=\"flex items-center\">");
        strhtml.Append("<div>");
        strhtml.Append("<span class=\"font-semibold text-gray-700 mr-2\">答案一:</span>");
        strhtml.Append("<span class=\"text-gray-800\">" + bookVo.Answer1 + "</span>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        strhtml.Append("<div class=\"border rounded-lg p-4 bg-gray-50\">");
        strhtml.Append("<div class=\"flex items-center\">");
        strhtml.Append("<div>");
        strhtml.Append("<span class=\"font-semibold text-gray-700 mr-2\">答案二:</span>");
        strhtml.Append("<span class=\"text-gray-800\">" + bookVo.Answer2 + "</span>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        strhtml.Append("</div>");
        strhtml.Append("</section>");
    }
    else
    {
        // 默认情况下，显示挑战详情卡片和表单
        strhtml.Append("<section class=\"bg-white rounded-xl shadow-sm p-6 mb-6 border border-gray-100\">");
        strhtml.Append("<div class=\"mb-4 pb-4 border-b border-gray-100\">");
        strhtml.Append("<p class=\"text-sm text-gray-500 mb-1\">发起者</p>");
        strhtml.Append("<div class=\"flex items-center\">");
        strhtml.Append("<div class=\"bg-gradient-to-r from-green-400 to-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-3 shadow-sm\">");
        strhtml.Append("<i class=\"fas fa-user text-sm\"></i>");
        strhtml.Append("</div>");
        strhtml.Append("<a href=\"" + this.http_start + "bbs/userinfo.aspx?touserid=" + bookVo.userid + "\" class=\"font-semibold text-gray-800\">" + bookVo.nickName + "</a>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        strhtml.Append("<div class=\"mb-4 pb-4 border-b border-gray-100\">");
        strhtml.Append("<p class=\"text-sm text-gray-500 mb-1\">赌注</p>");
        strhtml.Append("<div class=\"flex items-center\">");
        strhtml.Append("<p class=\"font-bold text-lg text-amber-600\">" + bookVo.myMoney + "</p>");
        strhtml.Append("<span class=\"coin-icon ml-1\">");
        strhtml.Append("<i class=\"fas fa-coins text-xs\"></i>");
        strhtml.Append("</span>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        strhtml.Append("<div class=\"mb-5\">");
        strhtml.Append("<p class=\"text-sm text-gray-500 mb-2\">挑战问题</p>");
        strhtml.Append("<div class=\"bg-gray-50 p-4 rounded-lg\">");
        strhtml.Append("<p class=\"text-gray-800 text-lg font-medium\">\"" + bookVo.Question + "\"</p>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        // 选项部分
        strhtml.Append("<div class=\"space-y-3\">");
        strhtml.Append("<p class=\"text-sm text-gray-500 mb-1\">选项：</p>");
        
        // 直接创建表单部分
        strhtml.Append("<form action=\"" + http_start + "games/chuiniu/doit.aspx\" method=\"post\" class=\"space-y-4\">");
        strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"id\" value=\"" + id + "\"/>");
        
        // 使用传统单选按钮组
        strhtml.Append("<div class=\"space-y-3\">");
        
        // 选项1
        strhtml.Append("<label for=\"answer1\" class=\"block cursor-pointer\">");
        strhtml.Append("<div class=\"border rounded-lg p-4 bg-gray-50 hover:bg-gray-100 transition-all\">");
        strhtml.Append("<div class=\"flex items-center\">");
        strhtml.Append("<input type=\"radio\" id=\"answer1\" name=\"myanswer\" value=\"1\" class=\"w-5 h-5 text-teal-600 rounded-full mr-3\" required>");
        strhtml.Append("<div>");
        strhtml.Append("<span class=\"font-semibold text-gray-700 mr-2\">答案一:</span>");
        strhtml.Append("<span class=\"text-gray-800\">" + bookVo.Answer1 + "</span>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("</label>");
        
        // 选项2
        strhtml.Append("<label for=\"answer2\" class=\"block cursor-pointer\">");
        strhtml.Append("<div class=\"border rounded-lg p-4 bg-gray-50 hover:bg-gray-100 transition-all\">");
        strhtml.Append("<div class=\"flex items-center\">");
        strhtml.Append("<input type=\"radio\" id=\"answer2\" name=\"myanswer\" value=\"2\" class=\"w-5 h-5 text-teal-600 rounded-full mr-3\" required>");
        strhtml.Append("<div>");
        strhtml.Append("<span class=\"font-semibold text-gray-700 mr-2\">答案二:</span>");
        strhtml.Append("<span class=\"text-gray-800\">" + bookVo.Answer2 + "</span>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("</label>");
        
        strhtml.Append("</div>");
        
        // 提交按钮
        strhtml.Append("<button type=\"submit\" class=\"w-full py-4 px-4 rounded-lg gradient-btn font-bold shadow-sm text-white transition-all flex items-center justify-center\">");
        strhtml.Append("<i class=\"fas fa-check-circle mr-2\"></i>确认应战");
        strhtml.Append("</button>");
        
        strhtml.Append("</form>");
        
        strhtml.Append("</div>");
        strhtml.Append("</section>");
        
        // 显示表单卡片
        strhtml.Append("<section class=\"bg-white rounded-xl shadow-sm p-6 border border-gray-100\">");
        strhtml.Append("<div class=\"flex justify-between items-center pb-4 border-b border-gray-100\">");
        strhtml.Append("<div class=\"flex items-center\">");
        strhtml.Append("<p class=\"text-gray-600 mr-2\">您的余额:</p>");
        strhtml.Append("<p class=\"font-bold text-amber-600\">" + userVo.money + "</p>");
        strhtml.Append("<span class=\"coin-icon ml-1\">");
        strhtml.Append("<i class=\"fas fa-coins text-xs\"></i>");
        strhtml.Append("</span>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        // 游戏规则/说明
        strhtml.Append("<div class=\"pt-4\">");
        strhtml.Append("<div class=\"flex items-center text-gray-800 font-medium mb-3\">");
        strhtml.Append("<i class=\"fas fa-info-circle text-teal-500 mr-2\"></i>");
        strhtml.Append("规则说明");
        strhtml.Append("</div>");
        strhtml.Append("<ul class=\"text-sm text-gray-600 space-y-2\">");
        strhtml.Append("<li class=\"flex items-start\">");
        strhtml.Append("<i class=\"fas fa-circle text-teal-500 text-xs mt-1 mr-2\"></i>");
        strhtml.Append("<span>应战需要账户余额不低于赌注金额。</span>");
        strhtml.Append("</li>");
        strhtml.Append("<li class=\"flex items-start\">");
        strhtml.Append("<i class=\"fas fa-circle text-teal-500 text-xs mt-1 mr-2\"></i>");
        strhtml.Append("<span>若您获胜，将赢取对方赌注的<span class=\"font-semibold text-teal-600\">" + per + "%</span>。</span>");
        strhtml.Append("</li>");
        strhtml.Append("<li class=\"flex items-start\">");
        strhtml.Append("<i class=\"fas fa-circle text-teal-500 text-xs mt-1 mr-2\"></i>");
        strhtml.Append("<span>若您失败，将失去相应赌注金额。</span>");
        strhtml.Append("</li>");
        strhtml.Append("</ul>");
        strhtml.Append("</div>");
        
        strhtml.Append("</section>");
    }
    
    strhtml.Append("</main>");
    
    strhtml.Append("<script src='main.js'></script>");
    Response.Write(strhtml);
    // 添加main.js引用
    Response.Write(WapTool.showDown(wmlVo));
%>