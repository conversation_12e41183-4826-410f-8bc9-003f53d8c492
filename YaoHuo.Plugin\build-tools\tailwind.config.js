/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "../Template/**/*.{html,hbs,aspx,ascx,cshtml,js}", // 扫描 Template 目录下的模板文件和 JS 文件
    "../**/*.{aspx,ascx,cshtml}", // 扫描项目根目录下的 ASP.NET 文件
    "../BBS/**/*.{aspx,ascx,cshtml}", // 扫描 BBS 目录
    "../Admin/**/*.{aspx,ascx,cshtml}", // 扫描 Admin 目录
    "../Pages/**/*.{aspx,ascx,cshtml}", // 扫描 Pages 目录
    "../WebSite/**/*.{aspx,ascx,cshtml}", // 扫描 WebSite 目录
    // 排除 node_modules, bin, obj 等目录
    "!../node_modules/**/*",
    "!../bin/**/*",
    "!../obj/**/*",
    "!../.git/**/*",
    "!../.vs/**/*"
  ],
  safelist: [
    // 动态图标颜色类 - 确保这些类被包含在最终的CSS中
    'text-primary',
    'text-emerald-500',
    'text-orange-400',
    'text-purple-400',
    'text-pink-400',
    'text-sky-500',
    'text-yellow-500',
    'text-purple-500',
    'text-rose-500',
    'text-gray-400',
    'text-gray-500',

    // JavaScript 动态使用的类 - UserGuessBook.hbs 和 MessageNotification.js
    'hidden',
    'fade-out',
    // 'new-message-slide-in' - 已被 animate-slide-in-from-top 替代，移除

    // 头像相关类
    'object-fill', // 头像拉伸填充模式
    'bg-gray-150', // 自定义中间灰色背景

    // FontAwesome 图标类通过 content 扫描自动包含，移除冗余项

    // Toast 和对话框相关的 Tailwind 类
    'fixed',
    'top-20',
    'bottom-5',
    'right-20',
    'z-toast',
    'z-modal',
    'bg-white',
    'bg-black',
    'bg-opacity-70',
    'rounded-xl',
    'rounded-lg',
    'shadow-dialog',
    'shadow-lg',
    'flex',
    'items-center',
    'justify-center',
    'p-4',
    'px-5',
    'py-4',
    'py-2.5',
    'text-sm',
    'text-white',
    'font-medium',
    'transition-all',
    'duration-300',
    'opacity-0',
    'opacity-100',
    'transform',
    'translate-x-1/2',
    '-translate-x-1/2',
    'translate-y-0',
    '-translate-y-2.5',

    // 渐变背景类
    'bg-gradient-to-r',
    'from-success',
    'to-green-600',
    'from-error',
    'to-danger',
    'from-warning',
    'to-orange-600',
    'from-info',
    'to-blue-600',

    // 响应式padding类 - UserInfo页面
    'xs-400:pt-4',
    'xs-350:pt-4',
    'xs-400:px-4',
    'xs-350:px-3',
    'xs-400:pb-4',
    'xs-350:pb-3',

    // Header图标相关类
    'header-icon-standard',
    'header-icon-size',
    'w-[18px]',
    'h-[18px]',
    'w-[19px]',
    'h-[19px]',
    'w-[22px]',
    'h-[22px]',

    // Header绝对居中布局相关类
    'relative',
    'absolute',
    'left-1/2',
    'top-1/2',
    'transform',
    '-translate-x-1/2',
    '-translate-y-1/2',
    'z-10',
    'z-20',
    'pointer-events-none',
    'ml-auto',

    // 响应式尺寸类 - 头像优化
    'xs-400:w-18',
    'xs-400:h-18',
    'xs-350:w-16',
    'xs-350:h-16',

    // 响应式字体类 - 昵称和按钮优化
    'xs-400:text-lg',
    'xs-350:text-base',
    'xs-400:text-xs',
    'xs-350:text-xs',

    // 响应式按钮类 - 私信按钮优化
    'xs-400:px-2',
    'xs-400:py-1',
    'xs-350:px-1.5',
    'xs-350:py-0.5',
    'xs-400:mr-0.5',
    'xs-400:hidden',

    // 响应式padding类 - 帖子回复统计卡片优化
    'xs-350:p-3.5',

    // 响应式margin类 - 个人资料卡片间距优化
    'xs-400:mb-0.5',
    'xs-350:mb-0',
    'xs-400:mb-1',
    'xs-350:mb-0.5',

    // 负边距类 - 文字与头像对齐优化
    '-mt-0.5',

    // 昵称截断类 - 任何屏幕都可能截断
    'truncate',
    'max-w-[calc(100%-4.5rem)]',
    'xs-400:max-w-[calc(100%-3rem)]',
    'xs-350:max-w-[calc(100%-2.5rem)]',

    // 昵称滚动相关类
    'cursor-pointer',
    'relative',
    'overflow-hidden',

    // 消息通知相关类 - JavaScript 动态使用
    'show', // notification-dropdown 状态类
    'unread', // notification-item 状态类
    'bg-gradient-notification-badge',
    '-top-1.5',
    '-right-1.5',
    'min-w-4',
    'border-2',
    'border-white',
    'rounded-full',
    'w-80',
    'max-w-[90vw]',
    'shadow-xl',
    'z-[90]',
    'opacity-0',
    'invisible',
    'pointer-events-none',
    'opacity-100',
    'visible',
    'pointer-events-auto',
    '-translate-y-2.5',
    'translate-y-0',
    'scale-95',
    'scale-100'
  ],
  theme: {
    extend: {
      screens: {
        'max-768': {'max': '768px'},
        'xs-480': {'max': '480px'},
        'xs-400': {'max': '400px'},
        'xs-390': {'max': '390px'},
        'xs-350': {'max': '350px'},
        'xs-310': {'max': '310px'},
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
      },
      colors: {
        // ... 复制过来的颜色配置 ...
        'primary': '#58b4b0',
        'primary-dark': '#4a9c98',
        'primary-light': '#7cd0cb',
        'primary-alpha-10': 'rgba(88, 180, 176, 0.1)',
        'primary-alpha-30': 'rgba(88, 180, 176, 0.3)',
        'primary-alpha-05': 'rgba(88, 180, 176, 0.05)',
        'primary-alpha-20': 'rgba(88, 180, 176, 0.2)',

        // 次要颜色
        'secondary': '#9CCCC8',
        'secondary-dark': '#6b7280', // 保留：与 text-secondary 值相同但语义不同
        'secondary-light': '#d1d5db',

        // 强调色
        'accent': '#f97316',

        // 文本颜色（简化版本，与text-primary等保持一致）
        // 'text': '#1f2937', // 已删除：与 text-primary 重复，使用 text-primary 替代
        // 'muted': '#6b7280', // 已删除：与 text-secondary 重复，使用 text-secondary 替代

        // 文本颜色
        'text-primary': '#1f2937',
        'text-secondary': '#6b7280',
        'text-tertiary': '#4b5563',
        'text-light': '#9ca3af',

        // 状态颜色
        'success': '#10b981',
        'danger': '#dc2626',
        'danger-dark': '#b91c1c',
        'warning': '#d97706',
        'warning-light': '#eab308',
        'warning-alpha-20': 'rgba(217, 119, 6, 0.2)',
        'error': '#ef4444',
        'info': '#3b82f6',

        // 绿色系列
        'green-50': '#f0fdf4',
        'green-500': '#22c55e',
        'green-600': '#16a34a',
        'green-700': '#15803d',
        'green-800': '#166534',

        // 蓝色系列
        'blue-50': '#eff6ff',
        'blue-100': '#dbeafe',
        // 'blue-500': '#3b82f6', // 已删除：与 info 重复，使用 info 替代
        'blue-600': '#2563eb',
        'blue-700': '#1d4ed8',
        'blue-800': '#1e40af',

        // 紫色系列
        'purple-400': '#a855f7',
        'purple-500': '#8b5cf6',
        'purple-600': '#7c3aed',

        // 翠绿色系列
        'emerald-400': '#34d399',
        'emerald-500': '#10b981', // 恢复：动态图标需要使用
        'emerald-600': '#059669',

        // 粉色系列
        'pink-400': '#f472b6',
        'pink-500': '#ec4899',
        'pink-600': '#db2777',

        // 橙色系列
        'orange-50': '#fff7ed',
        'orange-400': '#fb923c',
        'orange-500': '#f97316', // 恢复：动态图标需要使用
        'orange-600': '#ea580c',
        'orange-700': '#c2410c',

        // 青色系列
        'teal-50': '#f0fdfa',
        'teal-700': '#0f766e',

        // 天空蓝系列
        'sky-50': '#f0f9ff',
        'sky-400': '#38bdf8',
        'sky-500': '#0ea5e9',
        'sky-600': '#0284c7',
        'sky-700': '#0369a1',

        // 黄色系列
        'yellow-500': '#eab308',

        // 玫瑰色系列
        'rose-500': '#f43f5e',

        // 琥珀色系列
        // 'amber-100': '#fef3c7', // 已删除：与 bg-admin 重复，使用 bg-admin 替代

        // 灰色系列
        'gray-50': '#f9fafb',
        'gray-100': '#f3f4f6',
        'gray-150': '#eff0f3', // 自定义中间灰色，更接近 gray-100 的淡灰色
        'gray-200': '#e5e7eb',
        'gray-300': '#d1d5db',
        'gray-400': '#9ca3af',
        'gray-500': '#6b7280',
        'gray-600': '#4b5563',
        'gray-700': '#374151',
        'gray-800': '#1f2937',
        'gray-900': '#111827',

        // 背景颜色
        'bg-primary': '#f9fafb',
        // 'bg-gray-50': '#f9fafb', // 已删除：与 bg-primary 重复
        'bg-gray-100': '#f3f4f6',

        // 边框颜色
        'border-light': '#f3f4f6', // 与 bg-gray-100 值相同但语义不同，保持独立
        'border-normal': '#e5e7eb',
        'border-dark': '#d1d5db',

        // 特殊背景色
        'bg-vip': '#fef2f2',
        'bg-admin': '#fef3c7', // 管理员身份背景色
        'bg-medal': '#fef3c7', // 勋章相关背景色 - 与 bg-admin 值相同但语义不同
        'bg-error': '#fee2e2',
        'bg-info': '#e0f2fe',
      },
      spacing: {
        // ... 复制过来的间距配置 ...
        '0': '0',
        '1': '0.25rem', // 4px
        '2': '0.5rem',  // 8px
        '3': '0.75rem', // 12px
        '3.5': '0.875rem', // 14px - 用于帖子回复卡片中间档位
        '4': '1rem',    // 16px
        '5': '1.25rem', // 20px
        '6': '1.5rem',  // 24px
        '8': '2rem',    // 32px
        '10': '2.5rem', // 40px
        '12': '3rem',   // 48px
        '16': '4rem',   // 64px
        '20': '5rem',   // 80px
      },
      fontSize: {
        // ... 复制过来的字体大小配置 ...
        'xs': '0.75rem',   // 12px
        'sm': '0.875rem',  // 14px
        'base': '1rem',    // 16px
        'lg': '1.125rem',  // 18px
        'xl': '1.25rem',   // 20px
        '2xl': '1.5rem',   // 24px
        '3xl': '1.875rem', // 30px
      },
      fontWeight: {
        // ... 复制过来的字体粗细配置 ...
        'normal': '400',
        'medium': '500',
        'semibold': '600',
        'bold': '700',
      },
      borderRadius: {
        // 标准Tailwind圆角配置
        'none': '0',
        'sm': '0.125rem',     // 2px
        'DEFAULT': '0.25rem', // 4px
        'md': '0.375rem',     // 6px
        'lg': '0.5rem',       // 8px
        'xl': '0.75rem',      // 12px
        '2xl': '1rem',        // 16px
        '3xl': '1.5rem',      // 24px
        'full': '9999px',     // 胶囊形
        'circle': '50%',      // 圆形
      },
      boxShadow: {
        // ... 复制过来的阴影配置 ...
        'sm': '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        'DEFAULT': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'lg': '0 4px 6px rgba(0, 0, 0, 0.1)',
        'dialog': '0 10px 25px rgba(0,0,0,0.15)', // 自定义对话框阴影
      },
      zIndex: {
        // ... 复制过来的 zIndex 配置 ...
        'dropdown': '10',
        'modal': '1000',
        'toast': '1001',
      },
      transitionProperty: {
        // ... 复制过来的过渡属性配置 ...
        'DEFAULT': 'all',
      },
      transitionDuration: {
        // ... 复制过来的过渡时间配置 ...
        'fast': '0.15s',
        'DEFAULT': '0.2s',
        'slow': '0.3s',
        'extra-slow': '1.2s',
      },
      transitionTimingFunction: {
        // ... 复制过来的过渡 timing function 配置 ...
        'ease-out': 'cubic-bezier(0, 0, 0.2, 1)',
        'ease-in': 'cubic-bezier(0.4, 0, 1, 1)',
        'ease-in-out': 'cubic-bezier(0.4, 0, 0.2, 1)',
      },
      keyframes: {
        // ... 复制过来的 keyframes 配置 ...
        expandFields: {
          'from': { opacity: '0', transform: 'translateY(-10px)' },
          'to': { opacity: '1', transform: 'translateY(0)' }
        },
        collapseFields: {
          'from': { opacity: '1', transform: 'translateY(0)' },
          'to': { opacity: '0', transform: 'translateY(-10px)' }
        },
        // UserGuessBook 页面动画
        'slide-in-from-top': {
          '0%': { opacity: '0', transform: 'translateY(-20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' }
        },
        // 消息通知相关动画
        'breathe': {
          '0%, 100%': { transform: 'scale(1)', opacity: '1' },
          '50%': { transform: 'scale(1.15)', opacity: '0.8' }
        },
      },
      animation: {
        // ... 复制过来的 animation 配置 ...
        expandFields: 'expandFields 0.2s ease-in-out',
        collapseFields: 'collapseFields 0.2s ease-in-out',
        // UserGuessBook 页面动画
        'slide-in-from-top': 'slide-in-from-top 0.5s ease-out forwards',
        // 消息通知相关动画
        'breathe': 'breathe 2s ease-in-out infinite',
      },
      width: {
        // ... 复制过来的 width 配置 ...
        '18': '4.5rem', // 72px - 用于头像响应式尺寸
        'icon-xs': '0.875rem', // 14px
        'icon-sm': '1rem', // 16px
        'icon-base': '1.25rem', // 20px
        'icon-lg': '1.5rem', // 24px
        'icon-xl': '2rem', // 32px
        'icon-2xl': '2.5rem', // 40px
      },
      height: {
        // ... 复制过来的 height 配置 ...
        '18': '4.5rem', // 72px - 用于头像响应式尺寸
        'icon-xs': '0.875rem', // 14px
        'icon-sm': '1rem', // 16px
        'icon-base': '1.25rem', // 20px
        'icon-lg': '1.5rem', // 24px
        'icon-xl': '2rem', // 32px
        'icon-2xl': '2.5rem', // 40px
      },
      backgroundImage: {
        // 配色方案二的自定义渐变
        'gradient-posts-s2': 'linear-gradient(to bottom right, #f0f9ff, rgba(219, 234, 254, 0.8))',
        'gradient-replies-s2': 'linear-gradient(to bottom right, #f0fdfa, rgba(156, 204, 200, 0.2))',

        // 配色方案一的自定义渐变（备用）
        'gradient-posts-s1': 'linear-gradient(to bottom right, #f0fdfa, rgba(88, 180, 176, 0.1), rgba(156, 204, 200, 0.15))',
        'gradient-replies-s1': 'linear-gradient(to bottom right, #fff7ed, rgba(249, 115, 22, 0.1), rgba(254, 243, 199, 0.8))',

        // 消息通知相关渐变
        'gradient-notification-badge': 'linear-gradient(135deg, #dc2626, #ef4444)',
      },
    },
  },
  plugins: [
    function({ addBase, addComponents }) {
      addBase({
        // 修复UBB标签中图片显示问题，使其与文本保持在同一行
        '.text-gray-900 img': {
          'display': 'inline',
          'vertical-align': 'middle'
        }
      });

      addComponents({
        // 重复的组件样式已移至 style.css 中统一管理

        // 勋章容器样式
        '.medal-container': {
          'line-height': '1.2'
        },
        '.medal-container img': {
          'display': 'inline-block',
          'margin': '2px',
          'vertical-align': 'middle'
        },

        // 昵称显示样式已移至 style.css 中统一管理

        // 动态图标样式
        '.dynamic-icon': {
          'width': '20px !important',
          'height': '20px !important',
          'display': 'flex !important',
          'align-items': 'center !important',
          'justify-content': 'center !important',
          'flex-shrink': '0 !important',
          'font-size': '14px !important'
        },

        // 下拉菜单样式已移至 style.css 中统一管理

        // 配色方案hover效果
        '.card-hover-s1-posts:hover': {
          'border-color': 'rgba(88, 180, 176, 0.3)',
          'box-shadow': '0 8px 25px rgba(88, 180, 176, 0.15)'
        },
        '.card-hover-s1-replies:hover': {
          'border-color': 'rgba(249, 115, 22, 0.3)',
          'box-shadow': '0 8px 25px rgba(249, 115, 22, 0.15)'
        },
        '.card-hover-s2-posts:hover': {
          'border-color': 'rgba(88, 180, 176, 0.3)',
          'box-shadow': '0 8px 25px rgba(88, 180, 176, 0.15)'
        },
        '.card-hover-s2-replies:hover': {
          'border-color': 'rgba(14, 165, 233, 0.3)',
          'box-shadow': '0 8px 25px rgba(14, 165, 233, 0.15)'
        },

        // 详细资料页面组件样式
        '.info-item': {
          'display': 'flex',
          'align-items': 'flex-start',
          'padding': '16px 20px',
          'border-bottom': '1px solid #f3f4f6',
          'transition': 'background-color 0.3s ease'
        },
        '.info-item:hover': {
          'background-color': '#f8fafc'
        },
        '.info-item:last-child': {
          'border-bottom': 'none'
        },
        '.info-label': {
          'width': '90px',
          'flex-shrink': '0',
          'font-weight': '500',
          'color': '#64748b',
          'padding-top': '2px',
          'font-size': '14px'
        },
        '.info-value': {
          'flex': '1',
          'color': '#334155',
          'line-height': '1.5',
          'word-break': 'break-all',
          'font-size': '14px'
        },

        // 头像fallback样式
        '.avatar-fallback-main': {
          'font-size': '28px',
          'font-family': 'Inter, "Noto Sans SC", system-ui, sans-serif',
          'font-weight': '500',
          'color': 'rgba(88, 180, 176, 0.7)',
          'display': 'flex',
          'align-items': 'center',
          'justify-content': 'center',
          'width': '100%',
          'height': '100%'
        },
        '.avatar-fallback-small': {
          'font-size': '16px',
          'font-family': 'Inter, "Noto Sans SC", system-ui, sans-serif',
          'font-weight': '500',
          'color': 'rgba(88, 180, 176, 0.9)',
          'display': 'flex',
          'align-items': 'center',
          'justify-content': 'center',
          'width': '100%',
          'height': '100%'
        },

        // 身份图标间距优化已移至 style.css 中统一管理
      });


    }
  ],
}