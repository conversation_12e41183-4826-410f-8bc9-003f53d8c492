using System;
using System.Collections.Generic;

namespace YaoHuo.Plugin.WebSite.Services.Config.Models
{
    /// <summary>
    /// OAuth 2.0 配置根对象
    /// </summary>
    public class OAuthConfigRoot
    {
        /// <summary>
        /// 配置元信息
        /// </summary>
        public ConfigInfo config { get; set; }

        /// <summary>
        /// 令牌设置
        /// </summary>
        public TokenSettings tokenSettings { get; set; }

        /// <summary>
        /// 安全设置
        /// </summary>
        public SecuritySettings securitySettings { get; set; }

        /// <summary>
        /// 端点设置
        /// </summary>
        public EndpointSettings endpointSettings { get; set; }

        /// <summary>
        /// CORS 设置
        /// </summary>
        public CorsSettings corsSettings { get; set; }



        /// <summary>
        /// 令牌管理设置
        /// </summary>
        public TokenManagementSettings tokenManagementSettings { get; set; }

        /// <summary>
        /// 日志记录设置
        /// </summary>
        public LoggingSettings loggingSettings { get; set; }
    }

    /// <summary>
    /// 配置元信息
    /// </summary>
    public class ConfigInfo
    {
        public string version { get; set; }
        public DateTime lastUpdated { get; set; }
        public string description { get; set; }
        public string author { get; set; }
        public string environment { get; set; }
    }

    /// <summary>
    /// 令牌设置
    /// </summary>
    public class TokenSettings
    {
        /// <summary>
        /// 授权码有效期（分钟）
        /// </summary>
        public int authorizationCodeLifetimeMinutes { get; set; } = 10;

        /// <summary>
        /// 访问令牌有效期（小时）
        /// </summary>
        public int accessTokenLifetimeHours { get; set; } = 1;

        /// <summary>
        /// 刷新令牌有效期（天）
        /// </summary>
        public int refreshTokenLifetimeDays { get; set; } = 30;

        /// <summary>
        /// 默认权限范围
        /// </summary>
        public string defaultScope { get; set; } = "profile";

        /// <summary>
        /// 支持的权限范围
        /// </summary>
        public List<string> supportedScopes { get; set; } = new List<string> { "profile" };

        /// <summary>
        /// 支持的授权类型
        /// </summary>
        public List<string> supportedGrantTypes { get; set; } = new List<string> { "authorization_code" };

        /// <summary>
        /// 是否要求 PKCE
        /// </summary>
        public bool requirePKCE { get; set; } = true;
    }

    /// <summary>
    /// 安全设置
    /// </summary>
    public class SecuritySettings
    {
        /// <summary>
        /// 是否要求 HTTPS
        /// </summary>
        public bool requireHttps { get; set; } = true;

        /// <summary>
        /// 是否允许 localhost 使用 HTTP
        /// </summary>
        public bool allowLocalhostHttp { get; set; } = true;

        /// <summary>
        /// AppKey 哈希盐值
        /// </summary>
        public string appKeySalt { get; set; } = "DefaultSalt_ChangeInProduction";

        /// <summary>
        /// 是否启用令牌指纹绑定
        /// </summary>
        public bool enableTokenFingerprinting { get; set; } = false;

        /// <summary>
        /// 最大重定向 URI 数量
        /// </summary>
        public int maxRedirectUris { get; set; } = 10;
    }

    /// <summary>
    /// 端点设置
    /// </summary>
    public class EndpointSettings
    {
        /// <summary>
        /// 授权端点
        /// </summary>
        public string authorizationEndpoint { get; set; } = "/OAuth/Authorize.aspx";

        /// <summary>
        /// 令牌端点
        /// </summary>
        public string tokenEndpoint { get; set; } = "/OAuth/Token.aspx";

        /// <summary>
        /// 撤销端点
        /// </summary>
        public string revokeEndpoint { get; set; } = "/OAuth/Revoke.aspx";
    }

    /// <summary>
    /// CORS 设置
    /// </summary>
    public class CorsSettings
    {
        /// <summary>
        /// 允许的来源
        /// </summary>
        public List<string> allowedOrigins { get; set; } = new List<string> { "*" };

        /// <summary>
        /// 允许的方法
        /// </summary>
        public List<string> allowedMethods { get; set; } = new List<string> { "GET", "POST" };

        /// <summary>
        /// 允许的头部
        /// </summary>
        public List<string> allowedHeaders { get; set; } = new List<string> { "Content-Type", "Authorization" };
    }



    /// <summary>
    /// 令牌管理设置
    /// </summary>
    public class TokenManagementSettings
    {
        /// <summary>
        /// 令牌管理模式
        /// normal: 普通模式，不处理现有令牌
        /// smart: 智能模式，撤销现有令牌（保留审计记录）
        /// aggressive: 激进模式，物理删除现有令牌（无审计记录）
        /// </summary>
        public string mode { get; set; } = "smart";

        /// <summary>
        /// 是否记录令牌管理活动日志
        /// </summary>
        public bool enableActivityLog { get; set; } = true;
    }

    /// <summary>
    /// 日志记录设置
    /// </summary>
    public class LoggingSettings
    {
        /// <summary>
        /// 是否启用日志记录
        /// </summary>
        public bool enabled { get; set; } = true;

        /// <summary>
        /// 日志路径（相对于网站根目录）
        /// </summary>
        public string logPath { get; set; } = "../Logs/OAuth";

        /// <summary>
        /// 日志文件名模式
        /// </summary>
        public string logFileNamePattern { get; set; } = "oauth-.log";

        /// <summary>
        /// 日志轮转间隔（Day, Hour, Minute）
        /// </summary>
        public string rollingInterval { get; set; } = "Day";

        /// <summary>
        /// 保留的日志文件数量限制
        /// </summary>
        public int retainedFileCountLimit { get; set; } = 30;

        /// <summary>
        /// 是否使用本地时间（否则使用UTC）
        /// </summary>
        public bool useLocalTime { get; set; } = true;

        /// <summary>
        /// 日志级别（Verbose, Debug, Information, Warning, Error, Fatal）
        /// </summary>
        public string logLevel { get; set; } = "Information";
    }
}