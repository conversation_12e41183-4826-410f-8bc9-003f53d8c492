﻿using System;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.Games.ChuiNiu
{
    public class ClassConfigAll : MyPageWap
    {
        private readonly string string_0 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string INFO = "";

        public string ERROR = "";

        public string par0 = "";

        public string par1 = "";

        public string par2 = "";

        public string par3 = "";

        public string par4 = "";

        public string backtype = "";

        public wap2_games_config_Model configVo = new wap2_games_config_Model();

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            backtype = GetRequestValue("backtype");
            IsCheckManagerLvl("|00|01|", "", "games/chuiniu/index.aspx?siteid=" + siteid + "&amp;classid=" + classid);
            wap2_games_config_BLL wap2_games_config_BLL = new wap2_games_config_BLL(string_0);
            configVo = wap2_games_config_BLL.GetModel("gameen='chuiniu' and siteid=" + siteid);
            if (configVo == null)
            {
                configVo = new wap2_games_config_Model
                {
                    siteid = siteVo.siteid,
                    gameEn = "chuiniu",
                    gameCn = "吹牛",
                    config = "100|20000|95|10|5",
                    todayTimes = 0L,
                    todayMoney = 0L,
                    updateTime = DateTime.Now,
                    addtime = DateTime.Now
                };
                wap2_games_config_BLL.Add(configVo);
            }
            par0 = WapTool.GetArryString(configVo.config, '|', 0);
            par1 = WapTool.GetArryString(configVo.config, '|', 1);
            par2 = WapTool.GetArryString(configVo.config, '|', 2);
            par3 = WapTool.GetArryString(configVo.config, '|', 3);
            par4 = WapTool.GetArryString(configVo.config, '|', 4);
            if (!(action == "gomod"))
            {
                return;
            }
            try
            {
                par0 = GetRequestValue("par0");
                par1 = GetRequestValue("par1");
                par2 = GetRequestValue("par2");
                par3 = GetRequestValue("par3");
                par4 = GetRequestValue("par4");
                if (!WapTool.IsNumeric(par0))
                {
                    par0 = "0";
                }
                if (!WapTool.IsNumeric(par1))
                {
                    par1 = "0";
                }
                if (!WapTool.IsNumeric(par2))
                {
                    par2 = "0";
                }
                if (!WapTool.IsNumeric(par3))
                {
                    par3 = "0";
                }
                if (!WapTool.IsNumeric(par4))
                {
                    par4 = "0";
                }
                if (long.Parse(par4) > 10L)
                {
                    par4 = "100";
                }
                configVo.config = par0 + "|" + par1 + "|" + par2 + "|" + par3 + "|" + par4;
                wap2_games_config_BLL.Update(configVo);
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
        }
    }
}