using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Transactions;
using Dapper;

namespace YaoHuo.Plugin.WebSite.Tool
{
    /// <summary>
    /// 🔧 优化的事务助手类
    /// 提供统一的锁顺序和事务管理，避免死锁
    /// </summary>
    public static class OptimizedTransactionHelper
    {
        /// <summary>
        /// 🔒 按固定顺序获取锁，避免死锁
        /// 锁获取顺序：user表(按ID升序) → wap_bbs表 → wap_bbsre表 → wap_bankLog表
        /// </summary>
        /// <param name="connection">数据库连接</param>
        /// <param name="transaction">数据库事务</param>
        /// <param name="userIds">需要锁定的用户ID列表</param>
        /// <param name="postIds">需要锁定的帖子ID列表（可选）</param>
        public static void AcquireLocksInOrder(IDbConnection connection, IDbTransaction transaction, 
            IEnumerable<long> userIds = null, IEnumerable<long> postIds = null)
        {
            // 1. 按用户ID升序锁定用户记录
            if (userIds != null)
            {
                var validUserIds = userIds.Where(id => id > 0).Distinct().OrderBy(id => id).ToList();
                if (validUserIds.Any())
                {
                    var parameters = new Dictionary<string, object>();
                    var paramNames = new List<string>();
                    
                    for (int i = 0; i < validUserIds.Count; i++)
                    {
                        string paramName = $"@UserId{i}";
                        paramNames.Add(paramName);
                        parameters.Add(paramName, validUserIds[i]);
                    }
                    
                    string lockUserSql = $"SELECT userid FROM [user] WITH (UPDLOCK, ROWLOCK) WHERE userid IN ({string.Join(",", paramNames)}) ORDER BY userid";
                    connection.Query<long>(lockUserSql, parameters, transaction);
                }
            }
            
            // 2. 按帖子ID升序锁定帖子记录
            if (postIds != null)
            {
                var validPostIds = postIds.Where(id => id > 0).Distinct().OrderBy(id => id).ToList();
                if (validPostIds.Any())
                {
                    var parameters = new Dictionary<string, object>();
                    var paramNames = new List<string>();
                    
                    for (int i = 0; i < validPostIds.Count; i++)
                    {
                        string paramName = $"@PostId{i}";
                        paramNames.Add(paramName);
                        parameters.Add(paramName, validPostIds[i]);
                    }
                    
                    string lockPostSql = $"SELECT id FROM [wap_bbs] WITH (UPDLOCK, ROWLOCK) WHERE id IN ({string.Join(",", paramNames)}) ORDER BY id";
                    connection.Query<long>(lockPostSql, parameters, transaction);
                }
            }
        }
        
        /// <summary>
        /// 🔧 执行优化的事务操作
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="operation">要执行的操作</param>
        /// <param name="userIds">需要锁定的用户ID</param>
        /// <param name="postIds">需要锁定的帖子ID</param>
        /// <param name="timeoutSeconds">事务超时时间（秒）</param>
        public static T ExecuteWithOptimizedTransaction<T>(string connectionString, 
            Func<IDbConnection, IDbTransaction, T> operation,
            IEnumerable<long> userIds = null, 
            IEnumerable<long> postIds = null,
            int timeoutSeconds = 30)
        {
            using (var scope = new TransactionScope(TransactionScopeOption.Required,
                new TransactionOptions {
                    IsolationLevel = System.Transactions.IsolationLevel.ReadCommitted,
                    Timeout = TimeSpan.FromSeconds(timeoutSeconds)
                }))
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // 按固定顺序获取锁
                            AcquireLocksInOrder(connection, transaction, userIds, postIds);
                            
                            // 执行业务操作
                            T result = operation(connection, transaction);
                            
                            // 提交事务
                            transaction.Commit();
                            scope.Complete();
                            
                            return result;
                        }
                        catch
                        {
                            // 事务自动回滚
                            throw;
                        }
                    }
                }
            }
        }
        
        /// <summary>
        /// 🔧 执行优化的事务操作（无返回值）
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="operation">要执行的操作</param>
        /// <param name="userIds">需要锁定的用户ID</param>
        /// <param name="postIds">需要锁定的帖子ID</param>
        /// <param name="timeoutSeconds">事务超时时间（秒）</param>
        public static void ExecuteWithOptimizedTransaction(string connectionString, 
            Action<IDbConnection, IDbTransaction> operation,
            IEnumerable<long> userIds = null, 
            IEnumerable<long> postIds = null,
            int timeoutSeconds = 30)
        {
            ExecuteWithOptimizedTransaction<object>(connectionString, (conn, trans) => {
                operation(conn, trans);
                return null;
            }, userIds, postIds, timeoutSeconds);
        }
        
        /// <summary>
        /// 📊 批量更新用户金币和经验
        /// </summary>
        /// <param name="connection">数据库连接</param>
        /// <param name="transaction">数据库事务</param>
        /// <param name="userUpdates">用户更新列表</param>
        /// <param name="siteId">站点ID</param>
        public static void BatchUpdateUserStats(IDbConnection connection, IDbTransaction transaction,
            IEnumerable<UserStatsUpdate> userUpdates, long siteId)
        {
            if (userUpdates == null || !userUpdates.Any()) return;
            
            foreach (var update in userUpdates)
            {
                string updateUserSql = @"UPDATE [user] SET 
                                        money = money + @MoneyChange,
                                        expR = expR + @ExpChange,
                                        bbsCount = bbsCount + @PostCountChange,
                                        bbsReCount = bbsReCount + @ReplyCountChange
                                        WHERE siteid = @SiteId AND userid = @UserId";
                
                connection.Execute(updateUserSql, new {
                    MoneyChange = update.MoneyChange,
                    ExpChange = update.ExpChange,
                    PostCountChange = update.PostCountChange,
                    ReplyCountChange = update.ReplyCountChange,
                    SiteId = siteId,
                    UserId = update.UserId
                }, transaction);
            }
        }
        
        /// <summary>
        /// 📝 批量插入银行日志
        /// </summary>
        /// <param name="connection">数据库连接</param>
        /// <param name="transaction">数据库事务</param>
        /// <param name="bankLogs">银行日志列表</param>
        /// <param name="siteId">站点ID</param>
        /// <param name="ip">IP地址</param>
        public static void BatchInsertBankLogs(IDbConnection connection, IDbTransaction transaction,
            IEnumerable<BankLogEntry> bankLogs, long siteId, string ip)
        {
            if (bankLogs == null || !bankLogs.Any()) return;
            
            string insertBankLogSql = @"INSERT INTO wap_bankLog (siteid, userid, actionName, money, leftMoney, opera_userid, opera_nickname, remark, ip, addtime)
                                       VALUES (@SiteId, @UserId, @ActionName, @Money, @LeftMoney, @OperaUserId, @OperaNickname, @Remark, @IP, @AddTime)";
            
            var logEntries = bankLogs.Select(log => new {
                SiteId = siteId,
                UserId = log.UserId,
                ActionName = log.ActionName,
                Money = log.Money.ToString(),
                LeftMoney = log.LeftMoney,
                OperaUserId = log.OperatorUserId,
                OperaNickname = log.OperatorNickname,
                Remark = log.Remark,
                IP = ip,
                AddTime = DateTime.Now
            });
            
            connection.Execute(insertBankLogSql, logEntries, transaction);
        }
    }
    
    /// <summary>
    /// 用户统计更新数据
    /// </summary>
    public class UserStatsUpdate
    {
        public long UserId { get; set; }
        public long MoneyChange { get; set; }
        public long ExpChange { get; set; }
        public int PostCountChange { get; set; }
        public int ReplyCountChange { get; set; }
    }
    
    /// <summary>
    /// 银行日志条目
    /// </summary>
    public class BankLogEntry
    {
        public long UserId { get; set; }
        public string ActionName { get; set; }
        public long Money { get; set; }
        public long LeftMoney { get; set; }
        public long OperatorUserId { get; set; }
        public string OperatorNickname { get; set; }
        public string Remark { get; set; }
    }
}
