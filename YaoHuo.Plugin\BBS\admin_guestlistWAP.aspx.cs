﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Admin_guestlistWAP : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string linkURL = "";

        public string linkTOP = "";

        public string condition = "";

        public string ERROR = "";

        public string key = "";

        public List<wap_bbsre_Model> listVo = null;

        public StringBuilder strhtml = new StringBuilder();

        public long kk = 1L;

        public long index = 0L;

        public long total = 0L;

        public long pageSize = 10L;

        public long CurrentPage = 1L;

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            CheckManagerLvl("03", classVo.adminusername, GetUrlQueryString());
            switch (action)
            {
                case "gocheckall":
                    gocheckall();
                    break;
                case "gocheck":
                    gocheck();
                    break;
                case "class":
                    showclass();
                    break;
                default:
                    showclass();
                    break;
                case "godel":
                    break;
            }
        }

        public void showclass()
        {
            key = GetRequestValue("key");

            // ✅ 使用QueryBuilder构建安全的查询条件，避免SQL注入
            var queryBuilder = new QueryBuilder()
                .Where("devid = @ParamN", DapperHelper.LimitLength(siteid, 50));

            if (classid == "0")
            {
                classVo.classid = 0L;
                classVo.position = "left";
                classVo.classname = "管理 所有留言板回帖:" + key;
                classVo.siteimg = "NetImages/no.gif";
            }
            else
            {
                classVo.classname = "管理 " + classVo.classname + "回帖:" + key;
                queryBuilder.Where("classid = @ParamN", DapperHelper.SafeParseLong(classid, "版块ID"));
            }

            if (!string.IsNullOrEmpty(key?.Trim()))
            {
                queryBuilder.Where("content LIKE @ParamN", "%" + DapperHelper.LimitLength(key, 100) + "%");
            }
            try
            {
                pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);

                // ✅ 在方法开始就定义连接字符串
                string adminConnectionString = PubConstant.GetConnectionString(string_10);

                if (GetRequestValue("getTotal") != "")
                {
                    total = long.Parse(GetRequestValue("getTotal"));
                }
                else
                {
                    // ✅ 使用安全的分页查询获取总数
                    var (countSql, _, parameters) = queryBuilder.BuildWithCount("SELECT COUNT(*)", "wap_bbsre");
                    total = DapperHelper.ExecuteScalar<long>(adminConnectionString, countSql, parameters);
                }

                if (GetRequestValue("page") != "")
                {
                    CurrentPage = long.Parse(GetRequestValue("page"));
                }
                CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
                CheckFunction("bbs", CurrentPage);
                index = pageSize * (CurrentPage - 1L);
                linkURL = http_start + "bbs/admin_guestlistWAP.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;key=" + HttpUtility.UrlEncode(key) + "&amp;getTotal=" + total;
                linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, total, pageSize, CurrentPage, linkURL);
                linkURL = WapTool.GetPageLink(ver, lang, total, pageSize, CurrentPage, linkURL);
                if (CurrentPage == 1L && classVo.total != total)
                {
                    WapTool.SetTotal(siteid, classid, total);
                }

                // ✅ 使用安全的分页查询获取数据
                var result = PaginationHelper.GetPagedDataWithBuilder<wap_bbsre_Model>(
                    adminConnectionString, "SELECT *", "wap_bbsre", queryBuilder,
                    (int)CurrentPage, (int)pageSize, "ORDER BY id DESC");
                listVo = result.Data;
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }

        public void gocheck()
        {
            CheckManagerLvl("01", "", GetUrlQueryString());
            if (userVo.managerlvl != "00" && siteVo.isCheckSite == 1L)
            {
                ShowTipInfo("超级管理员设置您网站内容需要审核，请联系超级管理员审核！", GetUrlQueryString().Replace("gocheck", "class"));
                return;
            }
            string requestValue = GetRequestValue("id");
            string requestValue2 = GetRequestValue("state");
            try
            {
                // ✅ 使用DapperHelper安全更新审核状态，避免SQL注入
                UpdateCheckStatusSafely(requestValue, requestValue2);
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
            showclass();
        }

        public void gocheckall()
        {
            CheckManagerLvl("01", "", GetUrlQueryString());
            if (userVo.managerlvl != "00" && siteVo.isCheckSite == 1L)
            {
                ShowTipInfo("超级管理员设置您网站内容需要审核，请联系超级管理员审核！", GetUrlQueryString().Replace("gocheckall", "class"));
                return;
            }
            string requestValue = GetRequestValue("state");
            try
            {
                // ✅ 使用DapperHelper安全批量更新审核状态，避免SQL注入
                UpdateAllCheckStatusSafely(requestValue);
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
            showclass();
        }

        /// <summary>
        /// 使用DapperHelper安全更新单个回帖的审核状态，避免SQL注入
        /// </summary>
        /// <param name="id">回帖ID</param>
        /// <param name="state">审核状态</param>
        private void UpdateCheckStatusSafely(string id, string state)
        {
            string adminConnectionString = PubConstant.GetConnectionString(string_10);
            string sql = "UPDATE wap_bbsre SET ischeck = @State WHERE id = @Id AND devid = @DevId";

            DapperHelper.Execute(adminConnectionString, sql, new {
                State = DapperHelper.SafeParseLong(state, "审核状态"),
                Id = DapperHelper.SafeParseLong(id, "回帖ID"),
                DevId = DapperHelper.LimitLength(siteid, 50)
            });
        }

        /// <summary>
        /// 使用DapperHelper安全批量更新审核状态，避免SQL注入
        /// </summary>
        /// <param name="state">审核状态</param>
        private void UpdateAllCheckStatusSafely(string state)
        {
            string adminConnectionString = PubConstant.GetConnectionString(string_10);
            string sql = "UPDATE wap_bbsre SET ischeck = @State WHERE ischeck <> @State AND devid = @DevId";

            DapperHelper.Execute(adminConnectionString, sql, new {
                State = DapperHelper.SafeParseLong(state, "审核状态"),
                DevId = DapperHelper.LimitLength(siteid, 50)
            });
        }
    }
}