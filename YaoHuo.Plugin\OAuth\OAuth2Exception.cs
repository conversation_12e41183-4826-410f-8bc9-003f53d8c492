using System;

namespace YaoHuo.Plugin.OAuth
{
    /// <summary>
    /// OAuth 2.0 标准异常类
    /// 用于处理 OAuth 2.0 流程中的错误
    /// </summary>
    public class OAuth2Exception : Exception
    {
        /// <summary>
        /// OAuth 2.0 错误代码
        /// </summary>
        public string Error { get; }

        /// <summary>
        /// 错误描述
        /// </summary>
        public string ErrorDescription { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="error">OAuth 2.0 错误代码</param>
        /// <param name="errorDescription">错误描述</param>
        public OAuth2Exception(string error, string errorDescription) 
            : base($"{error}: {errorDescription}")
        {
            Error = error;
            ErrorDescription = errorDescription;
        }

        /// <summary>
        /// 构造函数（带内部异常）
        /// </summary>
        /// <param name="error">OAuth 2.0 错误代码</param>
        /// <param name="errorDescription">错误描述</param>
        /// <param name="innerException">内部异常</param>
        public OAuth2Exception(string error, string errorDescription, Exception innerException) 
            : base($"{error}: {errorDescription}", innerException)
        {
            Error = error;
            ErrorDescription = errorDescription;
        }
    }
}