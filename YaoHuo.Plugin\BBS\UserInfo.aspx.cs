﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.BBS.Models;
using YaoHuo.Plugin.BBS.Base.Helper;
using YaoHuo.Plugin.Template.Models;

namespace YaoHuo.Plugin.BBS
{
    public class UserInfo : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string touserid = "";

        public string INFO = "";

        public string ERROR = "";

        public string backurl = "";

        public string idtype = "";

        public string myMobile = "";

        public long fans = 0L;

        public long todayZoneCount = 0L;

        public user_Model toUserVo = new user_Model();

        public string AdminClass = "";

        public string type = "";

        public string showClan = "";

        public StringBuilder strhtml = new StringBuilder();

        public List<wap_log_Model> loglistVo = null;

        public List<wap2_userGuessBook_Model> gblistVo = null;

        public List<wap_album_Model> albumlistVo = null;

        public wap_friends_Model RemarkVo = null;

        public string touseridRemark = "";

        public bool isAdmin = false;

        protected void Page_Load(object sender, EventArgs e)
        {
            // 检查UI偏好并处理新版渲染
            if (CheckAndHandleUIPreference())
            {
                return; // 新版UI已渲染，停止执行旧版代码
            }

            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            type = WapTool.GetSiteDefault(siteVo.Version, 27);
            touserid = GetRequestValue("touserid");
            if (!WapTool.IsNumeric(touserid))
            {
                touserid = "0";
            }
            isAdmin = IsCheckManagerLvl("|00|01|", "");
            user_BLL user_BLL = new user_BLL(string_10);
            toUserVo = user_BLL.getUserInfo(touserid, siteid);
            if (toUserVo == null)
            {
                ShowTipInfo(GetLang("无资料记录，当前您查看的是匿名或游客！|無資料記錄，當前您查看的是匿名或游客！|No data records, may be anonymous, may be deleted!"), backurl);
            }
            idtype = WapTool.GetIDName(siteid, touserid, toUserVo.managerlvl, lang);

            // ✅ 使用DapperHelper替换BLL调用，修复SQL注入漏洞
            string connectionString = PubConstant.GetConnectionString(string_10);

            // 修复动态列表查询
            string logSql = "SELECT TOP 3 * FROM wap_log WHERE siteid = @SiteId AND oper_userid = @UserId AND oper_type = 1 ORDER BY id DESC";
            var logList = DapperHelper.Query<wap_log_Model>(connectionString, logSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                UserId = DapperHelper.SafeParseLong(touserid, "用户ID")
            });
            loglistVo = logList?.ToList() ?? new List<wap_log_Model>();

            // 修复相册列表查询
            string albumSql = "SELECT TOP 4 * FROM wap_album WHERE ischeck = 0 AND ishidden = 0 AND userid = @SiteId AND makerid = @MakerId ORDER BY id DESC";
            var albumList = DapperHelper.Query<wap_album_Model>(connectionString, albumSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                MakerId = DapperHelper.SafeParseLong(touserid, "制作者ID")
            });
            albumlistVo = albumList?.ToList() ?? new List<wap_album_Model>();

            // 修复留言本查询
            string gbSql = "SELECT TOP 3 * FROM wap2_userGuessBook WHERE ischeck = 0 AND siteid = @SiteId AND userid = @UserId ORDER BY id DESC";
            var gbList = DapperHelper.Query<wap2_userGuessBook_Model>(connectionString, gbSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                UserId = DapperHelper.SafeParseLong(touserid, "用户ID")
            });
            gblistVo = gbList?.ToList() ?? new List<wap2_userGuessBook_Model>();

            AdminClass = WapTool.GetClassAdmin(http_start, sid, siteid, touserid);

            // 修复粉丝数查询
            string fansSql = "SELECT COUNT(*) FROM wap_friends WHERE siteid = @SiteId AND friendtype = 0 AND frienduserid = @UserId";
            fans = DapperHelper.ExecuteScalar<long>(connectionString, fansSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                UserId = DapperHelper.SafeParseLong(touserid, "用户ID")
            });
            // connectionString已在上面定义，无需重复声明

            if (userid != touserid)
            {
                // ✅ 完全修复SQL注入：使用DapperHelper替换BLL的GetListCount调用
                string sqlCount = "SELECT COUNT(*) FROM wap2_visitZone WHERE userid = @UserId AND touserid = @ToUserId";
                int listCount = DapperHelper.ExecuteScalar<int>(connectionString, sqlCount, new {
                    UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                    ToUserId = DapperHelper.SafeParseLong(touserid, "目标用户ID")
                });

                if (listCount > 0)
                {
                    // ✅ 使用Dapper修复SQL注入漏洞
                    UpdateVisitZoneSafely(userid, touserid);
                }
                else
                {
                    // ✅ 使用Dapper修复SQL注入漏洞
                    InsertVisitZoneSafely(siteid, userid, nickname, touserid, toUserVo.nickname);
                }
            }
            // ✅ 修复遗漏的SQL注入：使用DapperHelper替换BLL调用
            string sqlTodayCount = "SELECT COUNT(*) FROM wap2_visitZone WHERE touserid = @ToUserId AND DATEDIFF(dd, addtime, GETDATE()) < 1";
            todayZoneCount = DapperHelper.ExecuteScalar<int>(connectionString, sqlTodayCount, new {
                ToUserId = DapperHelper.SafeParseLong(touserid, "目标用户ID")
            });
            string fcountSubMoneyFlag = WapTool.GetFcountSubMoneyFlag(siteid, userid, IP);
            if (fcountSubMoneyFlag.IndexOf("ZONE" + touserid) < 0)
            {
                // ✅ 使用Dapper修复SQL注入漏洞
                UpdateUserZoneCountSafely(touserid);
                UpdateFcountSafely(fcountSubMoneyFlag, touserid, IP, siteid, userid);
            }
            // 家族信息暂时注释掉，新版UI不显示家族信息
            /*
            DataSet dataSet = DbHelperSQL.ExecuteDataset(WapTool._ConnStr, CommandType.Text, "select   a.id,a.clan_name from [wap_clan_list] a ,[wap_clan_user] b where a.id=b.clan_id  and b.userid=" + touserid);
            if (dataSet != null)
            {
                foreach (DataRow row in dataSet.Tables[0].Rows)
                {
                    showClan = showClan + "<a href=\"" + http_start + "clan/my.aspx?siteid=" + siteid + "&amp;classid=0&amp;action=clanInfo&amp;id=" + row["id"].ToString() + "\">" + row["clan_name"].ToString() + "</a><br/>";
                }
            }
            else
            {
                showClan = "<a href=\"" + http_start + "clan/main.aspx?siteid=" + siteid + "&amp;classid=0\">还没有(去看看)</a><br/>";
            }
            */
            if (userid != "0")
            {
                // ✅ 使用DapperHelper修复好友备注查询的SQL注入漏洞
                string friendSql = "SELECT * FROM wap_friends WHERE siteid = @SiteId AND userid = @UserId AND friendtype = 0 AND frienduserid = @FriendUserId";
                var friendList = DapperHelper.Query<wap_friends_Model>(connectionString, friendSql, new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                    FriendUserId = DapperHelper.SafeParseLong(touserid, "好友用户ID")
                });
                RemarkVo = friendList?.FirstOrDefault();
                if (RemarkVo != null)
                {
                    touseridRemark = RemarkVo.friendusername;
                }
            }
            VisiteCount("浏览<a href=\"" + http_start + "bbs/userinfo.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;touserid=" + touserid + "\">" + WapTool.Left(toUserVo.nickname, 20) + "空间</a>");
            Action_user_doit(4);
        }

        /// <summary>
        /// 检查UI偏好并处理新版渲染
        /// </summary>
        /// <returns>是否已渲染新版UI</returns>
        private bool CheckAndHandleUIPreference()
        {
            string uiPreference = Request.Cookies["ui_preference"]?.Value ?? "old";

            if (uiPreference == "new")
            {
                try
                {
                    RenderWithHandlebars();
                    return true;
                }
                catch (System.Threading.ThreadAbortException)
                {
                    return true; // 成功渲染
                }
                catch (Exception ex)
                {
                    ERROR = "新版模板加载失败: " + ex.Message;
                    return false;
                }
            }
            return false;
        }

        /// <summary>
        /// 使用Handlebars渲染新版UI
        /// </summary>
        private void RenderWithHandlebars()
        {
            try
            {
                // 首先获取基础数据（复用现有逻辑）
                LoadBasicData();

                // 构建页面数据模型
                var pageModel = BuildUserInfoPageModel();

                // 配置头部选项
                var headerOptions = new HeaderOptionsModel
                {
                    ShowViewModeToggle = pageModel.IsOwnSpace  // 只有查看自己空间时才显示视图切换按钮
                };

                // 如果不是查看自己的空间，添加操作按钮
                if (!pageModel.IsOwnSpace)
                {
                    headerOptions.CustomButtons.Add(new HeaderButtonModel
                    {
                        Id = "userActions",
                        Icon = "more-vertical",
                        Tooltip = "更多操作",
                        HasDropdown = true,
                        DropdownItems = new List<HeaderDropdownItemModel>
                        {
                            new HeaderDropdownItemModel
                            {
                                Text = "加为好友",
                                Icon = "user-plus",
                                Link = pageModel.ActionButtons.AddFriendUrl
                            },
                            new HeaderDropdownItemModel
                            {
                                IsDivider = true
                            },
                            new HeaderDropdownItemModel
                            {
                                Text = "加黑名单",
                                Icon = "user-x",
                                Link = pageModel.ActionButtons.AddBlacklistUrl
                            }
                        }
                    });
                }

                // 使用TemplateService渲染页面
                string finalHtml = TemplateService.RenderPageWithLayout(
                    "~/Template/Pages/UserInfo.hbs",
                    pageModel,
                    pageModel.PageTitle,
                    headerOptions
                );

                // 输出渲染结果
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write(finalHtml);
                Response.End();
            }
            catch (System.Threading.ThreadAbortException)
            {
                throw; // Response.End()的正常行为
            }
            catch (Exception ex)
            {
                // 错误处理
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}</div>");
                HttpContext.Current.ApplicationInstance.CompleteRequest();
            }
        }

        /// <summary>
        /// 加载基础数据（从原Page_Load方法中提取）
        /// </summary>
        private void LoadBasicData()
        {
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            type = WapTool.GetSiteDefault(siteVo.Version, 27);
            touserid = GetRequestValue("touserid");
            if (!WapTool.IsNumeric(touserid))
            {
                touserid = "0";
            }
            isAdmin = IsCheckManagerLvl("|00|01|", "");
            user_BLL user_BLL = new user_BLL(string_10);
            toUserVo = user_BLL.getUserInfo(touserid, siteid);
            if (toUserVo == null)
            {
                ShowTipInfo(GetLang("无资料记录，当前您查看的是匿名或游客！|無資料記錄，當前您查看的是匿名或游客！|No data records, may be anonymous, may be deleted!"), backurl);
            }
            idtype = WapTool.GetIDName(siteid, touserid, toUserVo.managerlvl, lang);

            // ✅ 使用DapperHelper替换BLL调用，修复SQL注入漏洞（LoadBasicData方法）
            string loadDataConnectionString = PubConstant.GetConnectionString(string_10);

            // 修复动态列表查询
            string logSql = "SELECT TOP 3 * FROM wap_log WHERE siteid = @SiteId AND oper_userid = @UserId AND oper_type = 1 ORDER BY id DESC";
            var logList = DapperHelper.Query<wap_log_Model>(loadDataConnectionString, logSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                UserId = DapperHelper.SafeParseLong(touserid, "用户ID")
            });
            loglistVo = logList?.ToList() ?? new List<wap_log_Model>();

            // 修复相册列表查询
            string albumSql = "SELECT TOP 4 * FROM wap_album WHERE ischeck = 0 AND ishidden = 0 AND userid = @SiteId AND makerid = @MakerId ORDER BY id DESC";
            var albumList = DapperHelper.Query<wap_album_Model>(loadDataConnectionString, albumSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                MakerId = DapperHelper.SafeParseLong(touserid, "制作者ID")
            });
            albumlistVo = albumList?.ToList() ?? new List<wap_album_Model>();

            // 修复留言本查询
            string gbSql = "SELECT TOP 3 * FROM wap2_userGuessBook WHERE ischeck = 0 AND siteid = @SiteId AND userid = @UserId ORDER BY id DESC";
            var gbList = DapperHelper.Query<wap2_userGuessBook_Model>(loadDataConnectionString, gbSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                UserId = DapperHelper.SafeParseLong(touserid, "用户ID")
            });
            gblistVo = gbList?.ToList() ?? new List<wap2_userGuessBook_Model>();

            AdminClass = WapTool.GetClassAdmin(http_start, sid, siteid, touserid);

            // 修复粉丝数查询
            string fansSql = "SELECT COUNT(*) FROM wap_friends WHERE siteid = @SiteId AND friendtype = 0 AND frienduserid = @UserId";
            fans = DapperHelper.ExecuteScalar<long>(loadDataConnectionString, fansSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                UserId = DapperHelper.SafeParseLong(touserid, "用户ID")
            });

            if (userid != touserid)
            {
                // ✅ 完全修复SQL注入：使用DapperHelper替换BLL的GetListCount调用
                string sqlCount = "SELECT COUNT(*) FROM wap2_visitZone WHERE userid = @UserId AND touserid = @ToUserId";
                int listCount = DapperHelper.ExecuteScalar<int>(loadDataConnectionString, sqlCount, new {
                    UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                    ToUserId = DapperHelper.SafeParseLong(touserid, "目标用户ID")
                });

                if (listCount > 0)
                {
                    // ✅ 使用Dapper修复SQL注入漏洞
                    UpdateVisitZoneSafely(userid, touserid);
                }
                else
                {
                    // ✅ 使用Dapper修复SQL注入漏洞
                    InsertVisitZoneSafely(siteid, userid, nickname, touserid, toUserVo.nickname);
                }
            }
            // ✅ 修复遗漏的SQL注入：使用DapperHelper替换BLL调用
            string sqlTodayCount = "SELECT COUNT(*) FROM wap2_visitZone WHERE touserid = @ToUserId AND DATEDIFF(dd, addtime, GETDATE()) < 1";
            todayZoneCount = DapperHelper.ExecuteScalar<int>(loadDataConnectionString, sqlTodayCount, new {
                ToUserId = DapperHelper.SafeParseLong(touserid, "目标用户ID")
            });
            string fcountSubMoneyFlag = WapTool.GetFcountSubMoneyFlag(siteid, userid, IP);
            if (fcountSubMoneyFlag.IndexOf("ZONE" + touserid) < 0)
            {
                // ✅ 使用Dapper修复SQL注入漏洞
                UpdateUserZoneCountSafely(touserid);
                UpdateFcountSafely(fcountSubMoneyFlag, touserid, IP, siteid, userid);
            }
            // 家族信息暂时注释掉，新版UI不显示家族信息
            /*
            DataSet dataSet = DbHelperSQL.ExecuteDataset(WapTool._ConnStr, CommandType.Text, "select   a.id,a.clan_name from [wap_clan_list] a ,[wap_clan_user] b where a.id=b.clan_id  and b.userid=" + touserid);
            if (dataSet != null)
            {
                foreach (DataRow row in dataSet.Tables[0].Rows)
                {
                    showClan = showClan + "<a href=\"" + http_start + "clan/my.aspx?siteid=" + siteid + "&amp;classid=0&amp;action=clanInfo&amp;id=" + row["id"].ToString() + "\">" + row["clan_name"].ToString() + "</a><br/>";
                }
            }
            else
            {
                showClan = "<a href=\"" + http_start + "clan/main.aspx?siteid=" + siteid + "&amp;classid=0\">还没有(去看看)</a><br/>";
            }
            */
            if (userid != "0")
            {
                // ✅ 使用DapperHelper修复好友备注查询的SQL注入漏洞（LoadBasicData方法）
                string friendSql = "SELECT * FROM wap_friends WHERE siteid = @SiteId AND userid = @UserId AND friendtype = 0 AND frienduserid = @FriendUserId";
                var friendList = DapperHelper.Query<wap_friends_Model>(loadDataConnectionString, friendSql, new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                    FriendUserId = DapperHelper.SafeParseLong(touserid, "好友用户ID")
                });
                RemarkVo = friendList?.FirstOrDefault();
                if (RemarkVo != null)
                {
                    touseridRemark = RemarkVo.friendusername;
                }
            }
            VisiteCount("浏览<a href=\"" + http_start + "bbs/userinfo.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;touserid=" + touserid + "\">" + WapTool.Left(toUserVo.nickname, 20) + "空间</a>");
            Action_user_doit(4);
        }

        /// <summary>
        /// 构建用户空间页面数据模型
        /// </summary>
        /// <returns>用户空间页面模型</returns>
        private UserInfoPageModel BuildUserInfoPageModel()
        {
            var model = new UserInfoPageModel();

            // 设置页面标题
            model.PageTitle = toUserVo.nickname + "的空间";

            // 设置管理员权限
            model.IsAdmin = isAdmin;

            // 设置基础信息
            model.SiteInfo.SiteId = siteid;
            model.SiteInfo.ClassId = classid;
            model.SiteInfo.HttpStart = http_start;
            model.SiteInfo.BackUrl = backurl;

            // 设置隐藏字段
            model.HiddenFields.SiteId = siteid;
            model.HiddenFields.ClassId = classid;
            model.HiddenFields.BackUrl = backurl;

            // 判断是否是查看自己的空间
            model.IsOwnSpace = (userid == touserid);
            model.TargetUserId = touserid;

            // ✅ 使用统一的AvatarHelper获取当前用户头像信息
            var currentUserAvatar = AvatarHelper.GetUserAvatar(userid, siteid, string_10, http_start);
            model.CurrentUserAvatarUrl = currentUserAvatar.AvatarUrl;
            model.CurrentUserIsDefaultAvatar = currentUserAvatar.IsDefaultAvatar;

            // 构建用户基本信息
            BuildUserBasicInfo(model);

            // 构建用户统计数据
            BuildUserStats(model);

            // 构建用户动态列表
            BuildUserDynamics(model);

            // 构建留言列表
            BuildUserMessages(model);

            // 构建勋章信息
            BuildUserMedals(model);

            // 构建操作按钮
            BuildActionButtons(model);

            // 构建空间访问统计
            BuildSpaceStats(model);

            return model;
        }

        /// <summary>
        /// 构建用户基本信息
        /// </summary>
        private void BuildUserBasicInfo(UserInfoPageModel model)
        {
            model.UserInfo.UserId = toUserVo.userid.ToString();
            model.UserInfo.Nickname = toUserVo.nickname ?? "未知用户";
            model.UserInfo.DisplayNickname = WapTool.GetColorNickName(toUserVo.idname, toUserVo.nickname, lang, ver);

            // ✅ 使用统一的AvatarHelper获取目标用户头像信息
            var targetUserAvatar = AvatarHelper.GetUserAvatar(toUserVo.userid.ToString(), siteid, string_10, http_start);
            model.UserInfo.AvatarUrl = targetUserAvatar.AvatarUrl;
            model.UserInfo.IsDefaultAvatar = targetUserAvatar.IsDefaultAvatar;

            model.UserInfo.Sex = (int)toUserVo.sex;
            model.UserInfo.SexDisplay = toUserVo.sex == 1 ? "男" : "女";
            model.UserInfo.Age = (int)toUserVo.age;
            model.UserInfo.City = toUserVo.city ?? "";
            model.UserInfo.IsOnline = (toUserVo.isonline == "1");
            model.UserInfo.OnlineStatusDisplay = model.UserInfo.IsOnline ? "在线" : "离线";
            model.UserInfo.IdentityType = idtype;
            // 处理个性签名的UBB转换
            string rawRemark = toUserVo.remark ?? "";
            model.UserInfo.Remark = string.IsNullOrEmpty(rawRemark) ? "" : WapTool.ToWML(rawRemark, wmlVo);

            // 注册时间和时长
            model.UserInfo.RegisterTime = string.Format("{0:yyyy/MM/dd}", toUserVo.RegTime);
            model.UserInfo.RegisterDuration = TimeHelper.FormatRegisterDuration(toUserVo.RegTime);
        }

        /// <summary>
        /// 构建用户统计数据
        /// </summary>
        private void BuildUserStats(UserInfoPageModel model)
        {
            model.Stats.Money = toUserVo.money;
            model.Stats.MoneyDisplay = FormatMoney(toUserVo.money);
            model.Stats.Experience = toUserVo.expr;
            model.Stats.ExperienceDisplay = toUserVo.expr.ToString();
            model.Stats.Level = WapTool.GetLevl(siteVo.lvlNumer, toUserVo.expr, toUserVo.money, type);
            model.Stats.Title = WapTool.GetHandle(siteVo.lvlNumer, toUserVo.expr, toUserVo.money, type);
            model.Stats.PostCount = toUserVo.bbsCount;
            model.Stats.ReplyCount = toUserVo.bbsReCount;

            // 构建链接
            model.Stats.PostsUrl = http_start + "bbs/book_list_search.aspx?action=search&key=" + touserid + "&type=pub";
            model.Stats.RepliesUrl = http_start + "bbs/book_re_my.aspx?touserid=" + touserid;
        }

        /// <summary>
        /// 构建用户动态列表
        /// </summary>
        private void BuildUserDynamics(UserInfoPageModel model)
        {
            if (loglistVo != null && loglistVo.Count > 0)
            {
                foreach (var log in loglistVo)
                {
                    var dynamic = new UserDynamicModel
                    {
                        Id = log.id,
                        Content = log.log_info.Replace("[sid]", sid),
                        OperTime = log.oper_time,
                        FriendlyTime = WapTool.DateToString(log.oper_time, lang, 1) + "前",
                        TypeIcon = "activity" // 默认图标
                    };
                    model.DynamicsList.Add(dynamic);
                }
            }
        }

        /// <summary>
        /// 构建留言列表
        /// </summary>
        private void BuildUserMessages(UserInfoPageModel model)
        {
            if (gblistVo != null && gblistVo.Count > 0)
            {
                foreach (var gb in gblistVo)
                {
                    // 提取原始内容并使用WapTool.ToWML处理UBB
                    string processedContent = ProcessUBBContent(gb.content);

                    // ✅ 使用统一的AvatarHelper获取留言者头像信息
                    var messageUserAvatar = AvatarHelper.GetUserAvatar(gb.fromuserid.ToString(), siteid, string_10, http_start);
                    string avatarUrl = messageUserAvatar.AvatarUrl;
                    bool isDefaultAvatar = messageUserAvatar.IsDefaultAvatar;

                    // 构建用户空间链接
                    string authorSpaceUrl = !string.IsNullOrEmpty(gb.fromuserid.ToString())
                        ? $"{http_start}bbs/userinfo.aspx?siteid={siteid}&touserid={gb.fromuserid}"
                        : "";

                    var message = new UserMessageModel
                    {
                        Id = gb.id,
                        Content = processedContent,
                        AuthorNickname = gb.fromnickname ?? "匿名用户",
                        AuthorUserId = gb.fromuserid.ToString(),
                        AuthorSpaceUrl = authorSpaceUrl,
                        AuthorAvatarUrl = avatarUrl,
                        IsDefaultAvatar = isDefaultAvatar,
                        AddTime = gb.addtime,
                        FriendlyTime = TimeHelper.ToFriendlyTime(gb.addtime),
                        DetailTime = TimeHelper.ToDetailTime(gb.addtime)
                    };
                    model.MessagesList.Add(message);
                }
            }
        }

        /// <summary>
        /// 构建勋章信息
        /// </summary>
        private void BuildUserMedals(UserInfoPageModel model)
        {
            string medalHtml = WapTool.GetMedal(toUserVo.userid.ToString(), toUserVo.moneyname, WapTool.GetSiteDefault(siteVo.Version, 47), wmlVo);

            model.Medals.HasMedals = !string.IsNullOrEmpty(medalHtml) && medalHtml.Trim() != "";
            model.Medals.MedalHtml = medalHtml ?? "";

            // 简单计算勋章数量（通过img标签数量）
            if (model.Medals.HasMedals)
            {
                model.Medals.MedalCount = medalHtml.Split(new string[] { "<img" }, StringSplitOptions.None).Length - 1;
            }
            else
            {
                model.Medals.MedalCount = 0;
            }
        }

        /// <summary>
        /// 构建操作按钮
        /// </summary>
        private void BuildActionButtons(UserInfoPageModel model)
        {
            model.ActionButtons.SendMessageUrl = http_start + "bbs/messagelist_add.aspx?touserid=" + touserid;
            model.ActionButtons.AddFriendUrl = http_start + "bbs/FriendList.aspx?action=addfriend&friendtype=0&touserid=" + touserid;
            model.ActionButtons.AddBlacklistUrl = http_start + "bbs/FriendList.aspx?action=addfriend&friendtype=1&touserid=" + touserid;
            model.ActionButtons.ViewDetailUrl = http_start + "bbs/userinfomore.aspx?touserid=" + touserid;
            model.ActionButtons.SubmitMessageUrl = http_start + "bbs/userguessbook.aspx";
            model.ActionButtons.ViewMoreDynamicsUrl = http_start + "bbs/book_list_log.aspx?touserid=" + touserid;
            model.ActionButtons.ViewMoreMessagesUrl = http_start + "bbs/userguessbook.aspx?touserid=" + touserid;
        }

        /// <summary>
        /// 构建空间访问统计
        /// </summary>
        private void BuildSpaceStats(UserInfoPageModel model)
        {
            model.SpaceStats.TotalVisits = toUserVo.zoneCount;
            model.SpaceStats.TodayVisits = todayZoneCount;
            model.SpaceStats.VisitStatsDisplay = $"空间人气: {toUserVo.zoneCount} / 今天: {todayZoneCount}";
        }

        /// <summary>
        /// 解析fromnickname字段，提取昵称和用户ID
        /// </summary>
        /// <param name="fromnickname">原始fromnickname字段内容</param>
        /// <returns>昵称和用户ID的元组</returns>
        private (string nickname, string userId) ParseFromNickname(string fromnickname)
        {
            if (string.IsNullOrEmpty(fromnickname))
            {
                return ("匿名用户", "");
            }

            try
            {
                // 解析HTML：<a href="/bbs/userinfo.aspx?siteid=1000&amp;touserid=28985">ヘ繌呱</a>
                var match = System.Text.RegularExpressions.Regex.Match(
                    fromnickname,
                    @"<a href=""/bbs/userinfo\.aspx\?siteid=\d+&amp;touserid=(\d+)"">([^<]+)</a>"
                );

                if (match.Success)
                {
                    string userId = match.Groups[1].Value;
                    string nickname = match.Groups[2].Value;
                    return (nickname, userId);
                }
                else
                {
                    // 如果解析失败，返回原始内容（去除HTML标签）
                    string cleanNickname = System.Text.RegularExpressions.Regex.Replace(fromnickname, @"<[^>]+>", "");
                    return (cleanNickname.Trim(), "");
                }
            }
            catch (Exception)
            {
                return ("匿名用户", "");
            }
        }

        /// <summary>
        /// 提取留言内容（去除昵称和时间信息）
        /// </summary>
        /// <param name="content">原始content字段内容</param>
        /// <returns>纯留言内容</returns>
        private string ExtractMessageContent(string content)
        {
            if (string.IsNullOrEmpty(content))
            {
                return "";
            }

            try
            {
                // 移除开头的昵称链接和时间信息
                // 格式：<a href="...">昵称</a> <span class="right">时间</span><br/>实际内容
                var cleanContent = System.Text.RegularExpressions.Regex.Replace(
                    content,
                    @"^<a href=""[^""]*"">[^<]*</a>\s*<span class=""right"">[^<]*</span><br/>",
                    "",
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase
                );

                return cleanContent.Trim();
            }
            catch
            {
                return content;
            }
        }

        /// <summary>
        /// 处理UBB内容转换为HTML
        /// </summary>
        /// <param name="content">原始UBB内容</param>
        /// <returns>转换后的HTML内容</returns>
        private string ProcessUBBContent(string content)
        {
            if (string.IsNullOrEmpty(content))
            {
                return "";
            }

            try
            {
                // 使用WapTool的ToWML方法进行转换
                string processedContent = WapTool.ToWML(content, wmlVo);

                // -------- 清理前置的昵称和时间信息（与UserGuessBook保持一致） --------
                if (!string.IsNullOrEmpty(processedContent))
                {
                    // 1. 移除格式：<a href="...">昵称</a> <span class="right">MM-dd HH:mm</span><br/>
                    var linkTimeRegex = new System.Text.RegularExpressions.Regex(
                        "^<a\\s+href=\"[^\"]*\"[^>]*>[^<]*</a>\\s*<span[^>]*>[^<]*</span><br\\s*/?>",
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                    processedContent = linkTimeRegex.Replace(processedContent, "").Trim();

                    // 2. 备用匹配：简单的 "昵称 MM-dd HH:mm<br/>" 格式
                    var simpleTimeRegex = new System.Text.RegularExpressions.Regex(
                        "^[^<\n]*\\s+\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}(<br\\s*/?>|\n|$)",
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                    processedContent = simpleTimeRegex.Replace(processedContent, "").Trim();

                    // 3. 移除多余的 <br/> 开头
                    while (processedContent.StartsWith("<br/>") || processedContent.StartsWith("<br>"))
                    {
                        processedContent = System.Text.RegularExpressions.Regex.Replace(
                            processedContent, "^<br\\s*/?>", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase).Trim();
                    }
                }

                return processedContent;
            }
            catch
            {
                // 如果UBB转换失败，返回原始内容
                return content;
            }
        }

        /// <summary>
        /// 格式化妖晶数量显示
        /// </summary>
        /// <param name="money">妖晶数量</param>
        /// <returns>格式化后的显示文本</returns>
        private string FormatMoney(long money)
        {
            if (money >= 100000)
            {
                // 超过10万，显示为X万（不显示小数点）
                return (money / 10000) + "万";
            }
            else
            {
                // 小于10万，显示完整数字并用逗号分隔
                return money.ToString("N0");
            }
        }

        /// <summary>
        /// 使用Dapper安全地更新访问时间
        /// </summary>
        private void UpdateVisitZoneSafely(string userId, string toUserId)
        {
            string connectionString = PubConstant.GetConnectionString(string_10);
            string sql = "UPDATE wap2_visitZone SET addtime = GETDATE() WHERE userid = @UserId AND touserid = @ToUserId";

            // SafeParseLong内部已包含数字验证，无需重复调用ValidateNumeric
            DapperHelper.Execute(connectionString, sql, new {
                UserId = DapperHelper.SafeParseLong(userId, "用户ID"),
                ToUserId = DapperHelper.SafeParseLong(toUserId, "目标用户ID")
            });
        }

        /// <summary>
        /// 使用Dapper安全地插入访问记录
        /// </summary>
        private void InsertVisitZoneSafely(string siteId, string userId, string nickname, string toUserId, string toNickname)
        {
            string connectionString = PubConstant.GetConnectionString(string_10);
            string sql = "INSERT INTO wap2_visitZone(siteid,userid,nickname,touserid,tonickname,addtime) VALUES(@SiteId,@UserId,@Nickname,@ToUserId,@ToNickname,GETDATE())";

            // SafeParseLong内部已包含数字验证，无需重复调用ValidateNumeric
            DapperHelper.Execute(connectionString, sql, new {
                SiteId = DapperHelper.SafeParseLong(siteId, "站点ID"),
                UserId = DapperHelper.SafeParseLong(userId, "用户ID"),
                Nickname = DapperHelper.LimitLength(nickname ?? "", 16),
                ToUserId = DapperHelper.SafeParseLong(toUserId, "目标用户ID"),
                ToNickname = DapperHelper.LimitLength(toNickname ?? "", 16)
            });
        }

        /// <summary>
        /// 使用Dapper安全地更新用户空间访问计数
        /// </summary>
        private void UpdateUserZoneCountSafely(string toUserId)
        {
            string connectionString = PubConstant.GetConnectionString(string_10);
            string sql = "UPDATE [user] SET ZoneCount = ZoneCount + 1 WHERE userid = @ToUserId";

            // SafeParseLong内部已包含数字验证，无需重复调用ValidateNumeric
            DapperHelper.Execute(connectionString, sql, new {
                ToUserId = DapperHelper.SafeParseLong(toUserId, "目标用户ID")
            });
        }

        /// <summary>
        /// 使用Dapper安全地更新fcount标记
        /// </summary>
        private void UpdateFcountSafely(string fcountSubMoneyFlag, string toUserId, string ip, string siteId, string userId)
        {
            string connectionString = PubConstant.GetConnectionString(string_10);
            string sql = "UPDATE [fcount] SET SubMoneyFlag = @SubMoneyFlag WHERE fip = @IP AND fuserid = @SiteId AND userid = @UserId";

            // SafeParseLong内部已包含数字验证，无需重复调用ValidateNumeric
            DapperHelper.Execute(connectionString, sql, new {
                SubMoneyFlag = DapperHelper.LimitLength(fcountSubMoneyFlag + "ZONE" + toUserId + ",", 4000),
                IP = DapperHelper.LimitLength(ip ?? "", 50),
                SiteId = DapperHelper.SafeParseLong(siteId, "站点ID"),
                UserId = DapperHelper.SafeParseLong(userId, "用户ID")
            });
        }
    }
}