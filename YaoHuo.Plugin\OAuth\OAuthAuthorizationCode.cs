using System;

namespace YaoHuo.Plugin.OAuth
{
    /// <summary>
    /// OAuth 授权码实体（对应 oauth_authorization_codes 表）
    /// </summary>
    public class OAuthAuthorizationCode
    {
        /// <summary>
        /// 授权码（主键）
        /// </summary>
        public string Code { get; set; }

        /// <summary>
        /// 客户端ID
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        /// 重定向URI
        /// </summary>
        public string RedirectUri { get; set; }

        /// <summary>
        /// 权限范围
        /// </summary>
        public string Scope { get; set; }

        /// <summary>
        /// PKCE代码挑战
        /// </summary>
        public string CodeChallenge { get; set; }

        /// <summary>
        /// PKCE方法
        /// </summary>
        public string CodeChallengeMethod { get; set; }

        /// <summary>
        /// 过期时间
        /// </summary>
        public DateTime ExpiresAt { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 使用时间（标记是否已使用）
        /// </summary>
        public DateTime? UsedAt { get; set; }

        #region 轻量业务方法

        /// <summary>
        /// 检查授权码是否已过期
        /// </summary>
        /// <returns>是否过期</returns>
        public bool IsExpired()
        {
            return DateTime.UtcNow > ExpiresAt;
        }

        /// <summary>
        /// 检查授权码是否已使用
        /// </summary>
        /// <returns>是否已使用</returns>
        public bool IsUsed()
        {
            return UsedAt.HasValue;
        }

        /// <summary>
        /// 检查授权码是否有效（未过期且未使用）
        /// </summary>
        /// <returns>是否有效</returns>
        public bool IsValid()
        {
            return !IsExpired() && !IsUsed();
        }

        /// <summary>
        /// 验证PKCE代码验证器（轻量业务方法）
        /// </summary>
        /// <param name="codeVerifier">代码验证器</param>
        /// <returns>是否匹配</returns>
        public bool VerifyCodeChallenge(string codeVerifier)
        {
            if (string.IsNullOrEmpty(CodeChallenge) || string.IsNullOrEmpty(codeVerifier))
                return false;

            if (CodeChallengeMethod == "plain")
            {
                return CodeChallenge == codeVerifier;
            }
            else if (CodeChallengeMethod == "S256")
            {
                using (var sha256 = System.Security.Cryptography.SHA256.Create())
                {
                    var challengeBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(codeVerifier));
                    var computedChallenge = Convert.ToBase64String(challengeBytes)
                        .TrimEnd('=')
                        .Replace('+', '-')
                        .Replace('/', '_');
                    return CodeChallenge == computedChallenge;
                }
            }

            return false;
        }

        /// <summary>
        /// 标记授权码为已使用（轻量业务方法）
        /// </summary>
        public void MarkAsUsed()
        {
            UsedAt = DateTime.UtcNow;
        }

        #endregion
    }
}