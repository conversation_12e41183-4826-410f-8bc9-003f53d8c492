﻿.custom-notification {font-size: 14px;position: absolute; top: 4px; right: 8px; width: 111px; border: 0; text-align: left; z-index: 99; box-sizing: border-box; font-weight: 400; border-radius: 6px; box-shadow: 2px 2px 10px 2px rgba(11, 10, 10, 0.2); background-color: #fff; cursor: pointer; } .custom-notification .custom-notification-container { display: flex; align-items: center; height: 30px; } .custom-notification .custom-notification-container .custom-notification-content-wrapper { margin: 0; height: 100%; color: gray; padding: 0 20px; border-radius: 0 6px 6px 0; flex: 1; display: flex; flex-direction: column; justify-content: center; } .custom-notification .custom-notification-container .custom-notification-content-wrapper .custom-notification-content { margin: 0; padding: 0; font-size: 14px; line-height: 16px; } .custom-notification-content{color: gray;padding-left: 12px;} .notification-container { position: relative; }