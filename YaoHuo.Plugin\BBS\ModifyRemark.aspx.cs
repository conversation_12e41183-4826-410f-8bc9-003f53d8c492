﻿using KeLin.ClassManager;
using System;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class ModifyRemark : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public string INFO = "";

        public string ERROR = "";

        public string num = "0";

        protected void Page_Load(object sender, EventArgs e)
        {
            IsLogin(userid, "bbs/modifyuserinfo.aspx?siteid=" + siteid);
            needPassWordToAdmin();
            string text = base.Request.Form.Get("action");
            num = WapTool.GetSiteDefault(siteVo.Version, 49);
            if (!WapTool.IsNumeric(num) || num == "0")
            {
                num = "50";
            }
            if (!(text == "gomod"))
            {
                return;
            }
            try
            {
                string remark = GetRequestValue("remark");
                if (num != "0")
                {
                    remark = WapTool.Left(remark, int.Parse(num));
                }

                // ✅ 使用DapperHelper安全更新用户签名，避免SQL注入
                UpdateUserRemarkSafely(remark);
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
        }

        /// <summary>
        /// 使用DapperHelper安全地更新用户签名，避免SQL注入
        /// </summary>
        /// <param name="remark">用户签名</param>
        private void UpdateUserRemarkSafely(string remark)
        {
            string remarkConnectionString = PubConstant.GetConnectionString(a);
            string sql = "UPDATE [user] SET remark = @Remark WHERE siteid = @SiteId AND userid = @UserId";

            DapperHelper.Execute(remarkConnectionString, sql, new {
                Remark = DapperHelper.LimitLength(remark ?? "", 50),
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                UserId = DapperHelper.SafeParseLong(userid, "用户ID")
            });
        }
    }
}