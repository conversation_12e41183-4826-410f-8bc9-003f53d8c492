-- OAuth 增强版数据清理机制
-- 版本: v2.0
-- 创建日期: 2025-06-20
-- 功能: 全面的OAuth数据清理，包括授权码、令牌和审计记录管理

USE [NETOK]
GO

PRINT '========================================';
PRINT '创建 OAuth 增强版数据清理机制';
PRINT '========================================';
PRINT '';

-- 1. 删除现有存储过程（如果存在）
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_cleanup_expired_oauth_data')
BEGIN
    DROP PROCEDURE [dbo].[sp_cleanup_expired_oauth_data];
    PRINT '删除旧版本清理存储过程';
END

-- 2. 创建增强版清理存储过程
EXEC('
CREATE PROCEDURE [dbo].[sp_cleanup_expired_oauth_data]
    @DryRun BIT = 1,                    -- 默认为预览模式，不实际删除
    @CleanupMode NVARCHAR(20) = ''ALL'', -- 清理模式: ALL, CODES, TOKENS, AUDIT
    @RetentionDays INT = 30,            -- 审计记录保留天数
    @BatchSize INT = 1000               -- 批量删除大小
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @DeletedCodes INT = 0;
    DECLARE @DeletedTokens INT = 0;
    DECLARE @DeletedUsedCodes INT = 0;
    DECLARE @DeletedRevokedTokens INT = 0;
    DECLARE @CurrentTime DATETIME2 = GETUTCDATE();
    DECLARE @RetentionCutoff DATETIME2 = DATEADD(DAY, -@RetentionDays, @CurrentTime);
    
    PRINT ''=== OAuth 数据清理开始 ==='';
    PRINT ''清理模式: '' + @CleanupMode;
    PRINT ''当前时间: '' + CONVERT(NVARCHAR(30), @CurrentTime, 120);
    PRINT ''审计保留期: '' + CAST(@RetentionDays AS NVARCHAR(10)) + '' 天'';
    PRINT ''批量大小: '' + CAST(@BatchSize AS NVARCHAR(10));
    PRINT '''';

    -- 预览模式：统计待清理数据
    IF @DryRun = 1
    BEGIN
        PRINT ''=== 预览模式 - 统计待清理数据 ==='';

        -- 统计过期授权码
        IF @CleanupMode IN (''ALL'', ''CODES'')
        BEGIN
            SELECT @DeletedCodes = COUNT(*)
            FROM oauth_authorization_codes
            WHERE expires_at < @CurrentTime;

            SELECT @DeletedUsedCodes = COUNT(*)
            FROM oauth_authorization_codes
            WHERE used_at IS NOT NULL AND used_at < @RetentionCutoff;

            PRINT ''过期授权码数量: '' + CAST(@DeletedCodes AS NVARCHAR(10));
            PRINT ''超期已使用授权码数量: '' + CAST(@DeletedUsedCodes AS NVARCHAR(10));
        END

        -- 统计过期令牌
        IF @CleanupMode IN (''ALL'', ''TOKENS'')
        BEGIN
            SELECT @DeletedTokens = COUNT(*)
            FROM oauth_access_tokens
            WHERE expires_at < @CurrentTime AND revoked_at IS NULL;

            PRINT ''过期访问令牌数量: '' + CAST(@DeletedTokens AS NVARCHAR(10));
        END

        -- 统计超期审计记录
        IF @CleanupMode IN (''ALL'', ''AUDIT'')
        BEGIN
            SELECT @DeletedRevokedTokens = COUNT(*)
            FROM oauth_access_tokens
            WHERE revoked_at IS NOT NULL AND revoked_at < @RetentionCutoff;

            PRINT ''超期已撤销令牌数量: '' + CAST(@DeletedRevokedTokens AS NVARCHAR(10));
        END

        PRINT '''';
        PRINT ''注意: 执行清理请设置 @DryRun = 0'';
    END
    ELSE
    BEGIN
        PRINT ''=== 执行模式 - 开始清理数据 ==='';
        
        -- 清理过期授权码
        IF @CleanupMode IN (''ALL'', ''CODES'')
        BEGIN
            PRINT ''正在清理过期授权码...'';
            DELETE FROM oauth_authorization_codes 
            WHERE expires_at < @CurrentTime;
            SET @DeletedCodes = @@ROWCOUNT;
            
            PRINT ''正在清理超期已使用授权码...'';
            DELETE FROM oauth_authorization_codes 
            WHERE used_at IS NOT NULL AND used_at < @RetentionCutoff;
            SET @DeletedUsedCodes = @@ROWCOUNT;
        END
        
        -- 清理过期访问令牌（保留已撤销的用于审计）
        IF @CleanupMode IN (''ALL'', ''TOKENS'')
        BEGIN
            PRINT ''正在清理过期访问令牌...'';
            DELETE FROM oauth_access_tokens
            WHERE expires_at < @CurrentTime AND revoked_at IS NULL;
            SET @DeletedTokens = @@ROWCOUNT;
        END
        
        -- 清理超期审计记录（已撤销的令牌）
        IF @CleanupMode IN (''ALL'', ''AUDIT'')
        BEGIN
            PRINT ''正在清理超期审计记录...'';
            DELETE FROM oauth_access_tokens
            WHERE revoked_at IS NOT NULL AND revoked_at < @RetentionCutoff;
            SET @DeletedRevokedTokens = @@ROWCOUNT;
        END
        
        PRINT ''=== 清理执行完成 ==='';
        PRINT ''已删除过期授权码: '' + CAST(@DeletedCodes AS NVARCHAR(10));
        PRINT ''已删除超期已使用授权码: '' + CAST(@DeletedUsedCodes AS NVARCHAR(10));
        PRINT ''已删除过期访问令牌: '' + CAST(@DeletedTokens AS NVARCHAR(10));
        PRINT ''已删除超期审计记录: '' + CAST(@DeletedRevokedTokens AS NVARCHAR(10));
    END
    
    -- 返回清理统计
    SELECT
        @CurrentTime as cleanup_time,
        @CleanupMode as cleanup_mode,
        @RetentionDays as retention_days,
        @DeletedCodes as deleted_expired_codes,
        @DeletedUsedCodes as deleted_used_codes,
        @DeletedTokens as deleted_expired_tokens,
        @DeletedRevokedTokens as deleted_audit_records,
        (@DeletedCodes + @DeletedUsedCodes + @DeletedTokens + @DeletedRevokedTokens) as total_deleted,
        @DryRun as dry_run_mode;
        
    PRINT '''';
    PRINT ''=== OAuth 数据清理完成 ==='';
END
');

PRINT '增强版清理存储过程 sp_cleanup_expired_oauth_data 创建成功';
PRINT '';

-- 3. 创建清理统计视图
IF EXISTS (SELECT * FROM sys.views WHERE name = 'v_oauth_cleanup_stats')
BEGIN
    DROP VIEW [dbo].[v_oauth_cleanup_stats];
END

EXEC('
CREATE VIEW [dbo].[v_oauth_cleanup_stats] AS
SELECT
    ''Authorization Codes'' as data_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN expires_at < GETUTCDATE() THEN 1 END) as expired_records,
    COUNT(CASE WHEN used_at IS NULL THEN 1 END) as unused_records,
    COUNT(CASE WHEN used_at IS NOT NULL AND used_at < DATEADD(DAY, -30, GETUTCDATE()) THEN 1 END) as old_used_records
FROM oauth_authorization_codes

UNION ALL

SELECT
    ''Access Tokens'' as data_type,
    COUNT(*) as total_records,
    COUNT(CASE WHEN expires_at < GETUTCDATE() AND revoked_at IS NULL THEN 1 END) as expired_records,
    COUNT(CASE WHEN revoked_at IS NOT NULL THEN 1 END) as revoked_records,
    COUNT(CASE WHEN revoked_at IS NOT NULL AND revoked_at < DATEADD(DAY, -30, GETUTCDATE()) THEN 1 END) as old_revoked_records
FROM oauth_access_tokens
');

PRINT '清理统计视图 v_oauth_cleanup_stats 创建成功';
PRINT '';

-- 4. 创建自动清理作业（可选，需要SQL Server Agent）
PRINT '自动清理作业创建指南:';
PRINT '1. 在SQL Server Management Studio中创建新作业';
PRINT '2. 作业名称: OAuth_Daily_Cleanup';
PRINT '3. 作业步骤命令:';
PRINT '   EXEC sp_cleanup_expired_oauth_data @DryRun = 0, @CleanupMode = ''ALL'', @RetentionDays = 30';
PRINT '4. 设置每日执行计划（建议凌晨2点）';
PRINT '';

PRINT '========================================';
PRINT 'OAuth 增强版数据清理机制创建完成！';
PRINT '';
PRINT '可用命令:';
PRINT '* 预览清理: EXEC sp_cleanup_expired_oauth_data @DryRun = 1';
PRINT '* 执行清理: EXEC sp_cleanup_expired_oauth_data @DryRun = 0';
PRINT '* 只清理授权码: EXEC sp_cleanup_expired_oauth_data @DryRun = 0, @CleanupMode = ''CODES''';
PRINT '* 查看统计: SELECT * FROM v_oauth_cleanup_stats';
PRINT '';
PRINT '清理模式说明:';
PRINT '* ALL: 清理所有过期数据（默认）';
PRINT '* CODES: 只清理授权码';
PRINT '* TOKENS: 只清理访问令牌';
PRINT '* AUDIT: 只清理审计记录';
PRINT '';
PRINT '建议清理频率:';
PRINT '* 过期授权码: 每日清理';
PRINT '* 过期令牌: 每日清理';
PRINT '* 审计记录: 每月清理（保留30天）';
