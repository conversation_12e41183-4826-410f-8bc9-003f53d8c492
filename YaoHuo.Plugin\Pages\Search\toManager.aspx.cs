﻿using System;
using System.Collections.Generic;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.Search
{
    public class toManager : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string touserid = "";

        public string page = "";

        public string INFO = "";

        public string ERROR = "";

        public string backurl = "";

        public user_Model touserVo = null;

        public string tobankmoney = "";

        public string topassword = "";

        public string toexpR = "";

        public string tosessiontimeout = "";

        public string tomanagerlvl = "";

        public string tolockuser = "";

        public string tochangedate = "";

        public string needpw = "";

        public List<wap2_smallType_Model> idlistVo = null;

        protected void Page_Load(object sender, EventArgs e)
        {
            INFO = "";
            ERROR = "";
            touserVo = null;

            action = GetRequestValue("action");
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);

            touserid = GetRequestValue("touserid");
            if (string.IsNullOrEmpty(touserid))
            {
                ERROR = "参数错误：未指定用户ID";
                return;
            }

            try
            {
                user_BLL user_BLL = new user_BLL(string_10);
                touserVo = user_BLL.getUserInfo(touserid, siteid);
                if (touserVo == null)
                {
                    ERROR = "错误：指定的用户不存在";
                    return;
                }
            }
            catch (Exception ex)
            {
                ERROR = "系统错误：" + ex.Message;
                return;
            }

            IsCheckManagerLvl("|00|01", "", GetUrlQueryString());
            tomanagerlvl = GetRequestValue("tomanagerlvl");
            if (userVo.managerlvl == "01")
            {
                if (userVo.userid != userVo.siteid)
                {
                    if (userVo.userid == touserVo.userid)
                    {
                        if (tomanagerlvl != "" && tomanagerlvl != userVo.managerlvl)
                        {
                            ShowTipInfo("抱歉，你权限选错了！", "");
                        }
                    }
                    else
                    {
                        if (touserVo.managerlvl != "02")
                        {
                            ShowTipInfo("抱歉，你没有权限管理此用户！", "");
                        }
                        if (tomanagerlvl != "" && tomanagerlvl != "02")
                        {
                            ShowTipInfo("抱歉，你权限选错了！", "");
                        }
                    }
                }
                else if (userVo.userid == touserVo.userid)
                {
                    if (tomanagerlvl != "" && "|01|".IndexOf(tomanagerlvl) < 0)
                    {
                        ShowTipInfo("抱歉，你权限选错了！", "");
                    }
                }
                else if (tomanagerlvl != "" && "|01|02|03|04|".IndexOf(tomanagerlvl) < 0)
                {
                    ShowTipInfo("抱歉，你权限选错了！", "");
                }
            }
            if (userVo.managerlvl == "00")
            {
                if (userVo.userid != userVo.siteid)
                {
                    if (userVo.userid == touserVo.userid)
                    {
                        if (tomanagerlvl != "" && tomanagerlvl != userVo.managerlvl)
                        {
                            ShowTipInfo("抱歉，你权限选错了！", "");
                        }
                    }
                    else
                    {
                        if ("|01|02|03|04|".IndexOf(touserVo.managerlvl) < 0)
                        {
                            ShowTipInfo("抱歉，你没有权限管理此用户！", "");
                        }
                        if (tomanagerlvl != "" && "|01|02|03|04|".IndexOf(tomanagerlvl) < 0)
                        {
                            ShowTipInfo("抱歉，你权限选错了！", "");
                        }
                    }
                }
                else if (userVo.userid == touserVo.userid && tomanagerlvl != "" && "|00|".IndexOf(tomanagerlvl) < 0)
                {
                    ShowTipInfo("抱歉，你权限选错了！", "");
                }
            }
            if (action == "gomod")
            {
                tobankmoney = GetRequestValue("tobankmoney");
                topassword = GetRequestValue("topassword");
                toexpR = GetRequestValue("toexpR");
                tosessiontimeout = GetRequestValue("tosessiontimeout");
                tolockuser = GetRequestValue("tolockuser");
                tomanagerlvl = GetRequestValue("tomanagerlvl");
                tochangedate = GetRequestValue("tochangedate");
                needpw = GetRequestValue("needpw");
                try
                {
                    if (PubConstant.md5(needpw).ToLower() != userVo.password.ToLower())
                    {
                        INFO = "PWERROR";
                    }
                    else if (!WapTool.IsNumeric(tobankmoney) || !WapTool.IsNumeric(toexpR))
                    {
                        INFO = "NUM";
                    }
                    else
                    {
                        // ✅ 使用DapperHelper进行安全的用户管理操作
                        string connectionString = PubConstant.GetConnectionString(string_10);

                        // 构建参数对象
                        var parameters = new
                        {
                            ManagerLvl = DapperHelper.LimitLength(tomanagerlvl, 10),
                            ExpR = DapperHelper.SafeParseLong(toexpR, "经验值"),
                            LockUser = DapperHelper.SafeParseLong(tolockuser, "锁定状态"),
                            BankMoney = DapperHelper.SafeParseLong(tobankmoney, "银行金币"),
                            SessionTimeout = DapperHelper.SafeParseLong(tosessiontimeout, "会话超时"),
                            Password = string.IsNullOrEmpty(topassword) ? null : PubConstant.md5(topassword),
                            EndTime = (DateTime?)null,
                            SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                            UserId = DapperHelper.SafeParseLong(touserid, "用户ID")
                        };

                        // 处理结束时间
                        if (siteid.ToString() != touserid.ToString() && !string.IsNullOrEmpty(tochangedate.Trim()))
                        {
                            try
                            {
                                DateTime endTime = DateTime.Parse(tochangedate.Trim());
                                parameters = new
                                {
                                    parameters.ManagerLvl,
                                    parameters.ExpR,
                                    parameters.LockUser,
                                    parameters.BankMoney,
                                    parameters.SessionTimeout,
                                    parameters.Password,
                                    EndTime = (DateTime?)endTime,
                                    parameters.SiteId,
                                    parameters.UserId
                                };
                                touserVo.endTime = endTime;
                            }
                            catch (Exception)
                            {
                                // 忽略日期解析错误
                            }
                        }

                        // 构建SQL语句
                        string updateSql = @"UPDATE [user] SET
                                           managerlvl = @ManagerLvl,
                                           expR = @ExpR,
                                           lockuser = @LockUser,
                                           myBankMoney = @BankMoney,
                                           sessiontimeout = @SessionTimeout";

                        if (!string.IsNullOrEmpty(topassword))
                        {
                            updateSql += ", password = @Password";
                        }

                        if (parameters.EndTime.HasValue)
                        {
                            updateSql += ", endtime = @EndTime";
                        }

                        updateSql += " WHERE siteid = @SiteId AND userid = @UserId";

                        DapperHelper.Execute(connectionString, updateSql, parameters);
                        INFO = "OK";
                    }
                }
                catch (Exception ex)
                {
                    ERROR = ex.ToString();
                }
            }
            tobankmoney = touserVo.myBankMoney.ToString();
            toexpR = touserVo.expr.ToString();
            tosessiontimeout = touserVo.SessionTimeout.ToString();
            tolockuser = touserVo.LockUser.ToString();
            string strWhere = "siteid=" + siteid + " and systype='card'";
            wap2_smallType_BLL wap2_smallType_BLL = new wap2_smallType_BLL(string_10);
            idlistVo = wap2_smallType_BLL.GetListVo(100L, 1L, strWhere, "*", "id", 100L, 1);
        }
    }
}