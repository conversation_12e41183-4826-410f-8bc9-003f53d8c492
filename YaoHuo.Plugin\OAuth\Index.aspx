﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Index.aspx.cs" Inherits="YaoHuo.Plugin.OAuth.Index" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权管理</title>

    <!-- 本地 Tailwind CSS -->
    <link href="/Template/CSS/output.css" rel="stylesheet">

    <!-- 本地 Lucide Icons -->
    <script src="/NetCSS/JS/BBS/Lucide.0.511.0.min.js"></script>

    <!-- 自定义样式 -->
    <style>
        .btn-revoke {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            border: 1px solid #f59e0b;
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            transition: background 0.2s ease;
            cursor: pointer;
        }

        .btn-revoke:hover {
            background: linear-gradient(135deg, #fde68a 0%, #fcd34d 100%);
        }

        .btn-revoke i {
            margin-right: 0.25rem;
        }

        .btn-danger-outline {
            border-color: #dc2626;
            color: #dc2626;
            background-color: transparent;
        }

        .btn-danger-outline:hover {
            border-color: #b91c1c;
            background-color: #fef2f2;
            color: #b91c1c;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 固定顶部导航栏 -->
        <header class="header">
            <div class="header-content">
                <div class="header-icon" onclick="window.location.href='/'">
                    <i data-lucide="arrow-left" class="w-6 h-6"></i>
                </div>
                <div class="header-title">授权管理</div>
                <div class="header-actions-right">
                    <div class="header-icon" onclick="showOAuthInfo()" title="关于OAuth授权">
                        <i data-lucide="info" class="w-5 h-5"></i>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <div class="main-content">
            <%
            // 显示后端处理的消息
            string htmlContent = strhtml.ToString();
            if (!string.IsNullOrEmpty(htmlContent))
            {
                if (htmlContent.Contains("成功"))
                {
                    %>
                    <div class="message success mb-4" style="background-color: #e9fbf0; border: 1px solid #bbf7d0; color: #166534;">
                        <i data-lucide="check-circle" class="w-4 h-4 inline mr-1"></i>
                        <%=htmlContent.Replace("<div class='tip'>", "").Replace("</div>", "").Replace("<div class='title'>", "")%>
                    </div>
                    <%
                }
                else if (htmlContent.Contains("失败") || htmlContent.Contains("错误"))
                {
                    %>
                    <div class="message error mb-4">
                        <i data-lucide="x-circle" class="w-4 h-4 inline mr-1"></i>
                        <%=htmlContent.Replace("<div class='tip'>", "").Replace("</div>", "").Replace("<div class='title'>", "")%>
                    </div>
                    <%
                }
            }
            %>



            <!-- 已授权应用列表 -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title justify-between">
                        <div class="flex items-center">
                            <i class="card-icon" data-lucide="shield-check"></i>
                            <span>已授权应用</span>
                        </div>
                        <% if (listVo != null && listVo.Count > 0) { %>
                        <span class="text-sm text-text-secondary font-normal inline">共 <span><%=listVo.Count%></span> 个应用</span>
                        <% } %>
                    </div>
                </div>
                <div class="card-body">
                    <%
                    if (listVo != null && listVo.Count > 0)
                    {
                        for (int i = 0; i < listVo.Count; i++)
                        {
                            index = index + kk;
                            %>
                            <div class="friend-item mb-4">
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-2">
                                        <h3 class="text-lg font-medium text-text-primary">
                                            <i data-lucide="app-window" class="w-4 h-4 inline mr-2"></i><%= string.IsNullOrEmpty(Convert.ToString(listVo[i].AppName)) ? "未知应用" : Server.HtmlEncode(Convert.ToString(listVo[i].AppName)) %>
                                        </h3>
                                        <span class="text-xs text-text-secondary">
                                            <%= Convert.ToDateTime(listVo[i].CreateTime).ToLocalTime().ToString("yyyy-MM-dd HH:mm") %>
                                            <% if (Convert.ToInt32(listVo[i].active_tokens) > 0) { %>
                                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">活跃</span>
                                            <% } else { %>
                                                <span class="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">过期</span>
                                            <% } %>
                                        </span>
                                    </div>

                                    <% if (!string.IsNullOrEmpty(Convert.ToString(listVo[i].Remarks)) && Convert.ToString(listVo[i].Remarks) != "无说明") { %>
                                    <p class="text-sm text-text-secondary mb-3">
                                        <i data-lucide="file-text" class="w-4 h-4 inline mr-1"></i>
                                        <%= Server.HtmlEncode(Convert.ToString(listVo[i].Remarks)) %>
                                    </p>
                                    <% } %>

                                    <!-- 操作按钮 -->
                                    <div class="flex justify-end space-x-2">
                                        <%
                                        string deleteToken = GenerateFormToken("OAuth_Index_Delete");
                                        string revokeToken = GenerateFormToken("OAuth_Index_Revoke");
                                        %>
                                        <%
                                            var appIdStr = Convert.ToString(listVo[i].AppID);
                                            var authIdStr = Convert.ToString(listVo[i].AuthID);
                                            var rawAppName = string.IsNullOrEmpty(Convert.ToString(listVo[i].AppName)) ? "未知应用" : Convert.ToString(listVo[i].AppName);
                                            var appNameJs = System.Web.HttpUtility.JavaScriptStringEncode(rawAppName);
                                        %>
                                        <%--
                                        <button type="button" class="btn-revoke"
                                                onclick="confirmRevoke('<%=Server.HtmlEncode(appIdStr)%>', '<%=appNameJs%>', '<%=Server.HtmlEncode(revokeToken)%>')">
                                            <i data-lucide="key" class="w-4 h-4 mr-1"></i>
                                            撤销令牌
                                        </button>
                                        --%>
                                        <button type="button" class="btn btn-outline btn-danger-outline"
                                                onclick="confirmDelete('<%=Server.HtmlEncode(appIdStr)%>', '<%=appNameJs%>', '<%=Server.HtmlEncode(deleteToken)%>')">
                                            <i data-lucide="trash-2" class="w-4 h-4 mr-1"></i>
                                            取消授权
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <%
                        }

                        // 撤销所有令牌按钮
                        string revokeAllToken = GenerateFormToken("OAuth_Index_RevokeAll");
                        %>
                        <%--
                        <div class="border-t pt-4 mt-4">
                            <div class="text-center">
                                <button type="button" class="btn btn-destructive"
                                        onclick="confirmRevokeAll('<%=revokeAllToken%>')">
                                    <i data-lucide="shield-x" class="w-4 h-4 mr-1"></i>
                                    撤销所有令牌
                                </button>
                                <p class="text-xs text-text-tertiary mt-2">
                                    此操作将撤销所有应用的访问令牌，但不会删除授权记录
                                </p>
                            </div>
                        </div>
                        --%>
                        <%
                    }
                    else
                    {
                        %>
                        <div class="text-center py-8">
                            <i data-lucide="shield-off" class="w-12 h-12 text-text-light mx-auto mb-3"></i>
                            <p class="text-text-secondary mb-3">暂无已授权的应用</p>
                        </div>
                        <%
                    }
                    %>
                </div>
            </div>
        </div>
    </div>

    <!-- 确认对话框 -->
    <div id="confirmModal" class="confirm-overlay" style="display: none;">
        <div class="confirm-content">
            <div class="confirm-title" id="confirmTitle">
                确认操作
            </div>
            <div class="confirm-message" id="confirmMessage">
                确定要执行此操作吗？
            </div>
            <div class="confirm-actions">
                <button type="button" class="custom-confirm-btn custom-confirm-cancel" onclick="closeConfirmModal()">
                    取消
                </button>
                <button type="button" class="custom-confirm-btn custom-confirm-delete" id="confirmBtn">
                    确认
                </button>
            </div>
        </div>
    </div>

    <!-- OAuth信息弹窗 -->
    <div id="oauthInfoModal" class="confirm-overlay" style="display: none;">
        <div class="confirm-content" style="max-width: 480px;width:90%;">
            <div class="confirm-title">
                <i data-lucide="shield-check" class="w-5 h-5 inline mr-2 text-blue-600"></i>
                关于 OAuth 授权
            </div>
            <div class="confirm-message text-left">
                <div class="mb-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">什么是 OAuth 2.0？</h4>
                    <p class="text-sm text-gray-700 leading-relaxed mb-3">
                        OAuth 2.0 是一个开放标准，允许第三方应用在用户授权下访问您的账户信息，而无需暴露您的密码。
                    </p>
                </div>
                
                <div class="grid grid-cols-2 gap-4 mb-4">
                    <div class="text-center p-3 bg-green-50 rounded-lg">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i data-lucide="shield" class="w-4 h-4 text-green-600"></i>
                        </div>
                        <h5 class="text-xs font-medium text-green-900 mb-1">安全授权</h5>
                        <p class="text-xs text-green-700">基于标准协议</p>
                    </div>
                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i data-lucide="key" class="w-4 h-4 text-blue-600"></i>
                        </div>
                        <h5 class="text-xs font-medium text-blue-900 mb-1">令牌管理</h5>
                        <p class="text-xs text-blue-700">随时撤销访问</p>
                    </div>
                </div>

                <div class="bg-amber-50 border border-amber-200 rounded-lg p-3">
                    <div class="flex items-start space-x-2">
                        <i data-lucide="info" class="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0"></i>
                        <div>
                            <p class="text-xs text-amber-800 leading-relaxed">
                                您可以随时查看和撤销已授权的应用。建议定期检查授权列表，撤销不再使用的应用访问权限。
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="confirm-actions">
                <button type="button" class="btn btn-outline" onclick="closeOAuthInfoModal()">
                    <i data-lucide="x" class="w-4 h-4 mr-1"></i>
                    关闭
                </button>
            </div>
        </div>
    </div>

    <script>
        let confirmAction = null;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });

        // 显示OAuth信息弹窗
        function showOAuthInfo() {
            document.getElementById('oauthInfoModal').style.display = 'flex';
            // 确保在弹窗显示后立即重新渲染图标
            setTimeout(function() {
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            }, 10);
        }

        // 关闭OAuth信息弹窗
        function closeOAuthInfoModal() {
            document.getElementById('oauthInfoModal').style.display = 'none';
        }

        // 确认删除授权
        function confirmDelete(authId, appName, token) {
            document.getElementById('confirmTitle').textContent = '取消授权';
            document.getElementById('confirmMessage').innerHTML =
                '确认取消 “<strong>' + appName + '</strong>” 的访问权限？';

            confirmAction = function() {
                window.location.href = '/OAuth/Index.aspx?action=del&aid=' + authId + '&token=' + token;
            };

            document.getElementById('confirmModal').style.display = 'flex';
        }

        // 确认撤销令牌
        function confirmRevoke(appId, appName, token) {
            document.getElementById('confirmTitle').textContent = '撤销令牌';
            document.getElementById('confirmMessage').innerHTML =
                '确定要撤销应用 "<strong>' + appName + '</strong>" 的所有访问令牌吗？<br/><br/>' +
                '<span class="text-warning text-xs">⚠️ 撤销后该应用需要重新获得授权才能访问您的账户。</span>';

            confirmAction = function() {
                window.location.href = '/OAuth/Index.aspx?action=revoke&aid=' + appId + '&token=' + token;
            };

            document.getElementById('confirmModal').style.display = 'flex';
        }

        // 确认撤销所有令牌
        function confirmRevokeAll(token) {
            document.getElementById('confirmTitle').textContent = '撤销所有令牌';
            document.getElementById('confirmMessage').innerHTML =
                '确定要撤销所有应用的访问令牌吗？<br/><br/>' +
                '<span class="text-danger text-xs">⚠️ 此操作不可恢复！所有已授权的应用都需要重新获得授权。</span>';

            confirmAction = function() {
                window.location.href = '/OAuth/Index.aspx?action=revokeall&token=' + token;
            };

            document.getElementById('confirmModal').style.display = 'flex';
        }

        // 关闭确认对话框
        function closeConfirmModal() {
            document.getElementById('confirmModal').style.display = 'none';
            confirmAction = null;
        }

        // 执行确认操作
        document.getElementById('confirmBtn').addEventListener('click', function() {
            if (confirmAction) {
                confirmAction();
            }
        });

        // 点击对话框外部关闭
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('confirm-overlay')) {
                if (e.target.id === 'confirmModal') {
                    closeConfirmModal();
                } else if (e.target.id === 'oauthInfoModal') {
                    closeOAuthInfoModal();
                }
            }
        });
    </script>
</body>
</html>