﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="FriendList_Mod.aspx.cs" Inherits="YaoHuo.Plugin.BBS.FriendList_Mod" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    StringBuilder strhtml = new StringBuilder();
    Response.Write(WapTool.showTop(this.GetLang("修改备注|修改备注|delete"), wmlVo));
    strhtml.Append("<div class=\"subtitle\">" + this.GetLang("修改备注|修改备注|delete") + "</div>");
    strhtml.Append(this.ERROR);
    strhtml.Append("<div class=\"content\">");
    if (this.INFO == "")
    {
        strhtml.Append("<form name=\"gt\" action=\"" + http_start + "bbs/friendlist_Mod.aspx\" method=\"post\">");
        strhtml.Append("<input maxlength=\"8\" type=\"text\" class=\"txt\" name=\"remark\" value=\"" + bookVo.friendusername + "\"/><br/>");
        strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"id\" value=\"" + id + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"page\" value=\"" + page + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"backurl\" value=\"" + backurl + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"friendtype\" value=\"" + friendtype + "\"/>");
        strhtml.Append("<input type=\"submit\" class=\"btn\" name=\"bt\" value=\" 保 存 \"/>");
        strhtml.Append("</form>");
    }
    else if (this.INFO == "OK")
    {
        strhtml.Append("<b>" + this.GetLang("修改备注成功！|刪除成功！|Deleted successfully!") + "</b><br/>");
    }
    strhtml.Append("</div>");
    strhtml.Append("<div class=\"btBox\"><div class=\"bt1\">");
    strhtml.Append("<a href=\"" + http_start + "bbs/friendlist.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;friendtype=" + this.friendtype + "&amp;backurl=" + HttpUtility.UrlEncode(backurl) + "&amp;page=" + this.page + "" + "\">返回列表</a>");
    //strhtml.Append(WapTool.GetVS(wmlVo));
    strhtml.Append("</div></div>");
    string isWebHtml = this.ShowWEB_view(this.classid);
    if (isWebHtml != "")
    {
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml, wmlVo).Replace("[view]", strhtml.ToString()));
        Response.End();
    }
    Response.Write(strhtml);
    Response.Write(WapTool.showDown(wmlVo));
%>