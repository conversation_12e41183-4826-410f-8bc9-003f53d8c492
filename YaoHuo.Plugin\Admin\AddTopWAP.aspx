﻿<%@ Page Language="C#" AutoEventWireup="true" ValidateRequest="false" CodeBehind="addTopWAP.aspx.cs" Inherits="YaoHuo.Plugin.Admin.AddTopWAP" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    Response.Write(WapTool.showTop(this.GetLang("顶部排版内容|顶部排版内容|Top Content"), wmlVo));
    Response.Write("<div class=\"title\">" + this.GetLang("顶部排版内容|顶部排版内容|Top Content") + "</div>");
    Response.Write(this.ERROR);
    if (this.INFO == "OK")
    {
        Response.Write("<div class=\"tip\">");
        Response.Write("<b>");
        Response.Write(this.GetLang("更新成功！|更新成功！|Successfully Update"));
        Response.Write("</b><br/>");
        Response.Write("</div>");
    }
    else
    {
        Response.Write("<div class=\"content\">");
        Response.Write("<form name=\"go\" action=\"" + this.http_start + "admin/addtopwap.aspx\" method=\"post\">");
        //Response.Write("输入顶部排版内容:<br/>");
        Response.Write("<textarea name=\"path\" rows=\"12\" style=\"min-height:80vh;width:98.5%\">" + classVo.introduce + "</textarea><br/>");
        //Response.Write("<a href=\"/admin/ubb2.aspx\">点击此查看UBB方法</a><br/>");
        Response.Write("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
        Response.Write("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
        Response.Write("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
        Response.Write("<input type=\"hidden\" name=\"sid\" value=\"" + sid + "\"/>");
        Response.Write("<input type=\"submit\" name=\"bt\" value=\"" + this.GetLang("保 存|保 存|Update") + "\"/>");
        Response.Write("</form></div>");
    }
    Response.Write("<div class=\"btBox\"><div class=\"bt1\">");
    Response.Write("<a href=\"" + this.http_start + "admin/wapindexeditwap.aspx?siteid=" + this.siteid + "&amp;classid=" + this.classid + "\">" + this.GetLang("返回上级|返回上级|Back to set") + "</a> ");
    Response.Write("</div></div>");
    Response.Write(WapTool.showDown(wmlVo));
%>