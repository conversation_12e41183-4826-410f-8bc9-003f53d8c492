﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_View_modfile_del.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Book_View_modfile_del" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    StringBuilder strhtml = new StringBuilder();
    Response.Write(WapTool.showTop(this.GetLang("删除附件|刪除附件|Delete attachment"), wmlVo));
    strhtml.Append("<div class=\"subtitle\">" + this.GetLang("删除操作|刪除操作|delete") + "</div>");
    
    if (!string.IsNullOrEmpty(this.ERROR))
    {
        strhtml.Append("<div class=\"tip\">" + this.ERROR + "</div>");
    }
    else if (this.INFO == "OK")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append("<b>" + this.GetLang("删除成功！|刪除成功！|Deleted successfully!") + "</b> ");
        strhtml.Append("<a href=\"" + this.http_start + "bbs/book_view_modfile.aspx?id=" + this.id + 
            "&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;page=" + this.lpage + 
            "\">" + this.GetLang("返回上级|返回上级|Back to list") + "</a> ");
        strhtml.Append("</div>");
    }
    else if (this.hasPermission)  // 只有在有权限时才显示删除选项
    {
        strhtml.Append("<div class=\"content\">\n");
        // 保证formToken有值
        string tokenKey = "formTokenList_modfile_del_" + id + "_" + delid;
        string formToken = null;
        var tokenList = Session[tokenKey] as System.Collections.Generic.List<string>;
        if (tokenList != null && tokenList.Count > 0)
            formToken = tokenList[tokenList.Count - 1];
        else
            formToken = GenerateFormToken(tokenKey);
        strhtml.Append("<a href=\"" + this.http_start + "bbs/book_view_modfile_del.aspx?action=godel" +
            "&amp;id=" + this.id + "&amp;delid=" + this.delid + "&amp;siteid=" + this.siteid +
            "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage +
            "&amp;token=" + formToken + "\">" +
            this.GetLang("确定删除！|確定刪除！|Confirm Delete!") + "</a><br/>\n");
        strhtml.Append("</div>\n");
    }

    string isWebHtml = this.ShowWEB_view(this.classid);
    if (isWebHtml != "")
    {
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml, wmlVo).Replace("[view]", strhtml.ToString()));
        Response.End();
    }

    strhtml.Append("<div class=\"btBox\"><div class=\"bt2\">");
    strhtml.Append("<a href=\"" + this.http_start + "bbs/book_view_modfile.aspx?id=" + this.id + 
        "&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;page=" + this.lpage + 
        "\">" + this.GetLang("返回上级|返回上级|Back to list") + "</a> ");
    if (this.hasPermission)  // 只有在有权限时才显示管理链接
    {
        strhtml.Append("<a href=\"" + this.http_start + "bbs/book_view_admin.aspx?siteid=" + this.siteid + 
            "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;id=" + this.id + 
            "\">" + this.GetLang("返回管理|返回上級|Back to admin") + "</a> ");
    }
    strhtml.Append("</div></div>");
    
    Response.Write(strhtml);
    Response.Write(WapTool.showDown(wmlVo));
%>