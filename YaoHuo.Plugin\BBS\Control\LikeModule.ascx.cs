﻿using System;
using System.Web.UI;
using KeLin.ClassManager.Model;

namespace YaoHuo.Plugin.BBS.Control
{
    public partial class LikeModule : System.Web.UI.UserControl
    {
        public bool HasLiked { get; set; }
        public long LikeCount { get; set; }  // 改为 long 类型
        public string PostId { get; set; }
        public string ClassId { get; set; }
        public string SiteId { get; set; }
        public string HttpStart { get; set; }
        public string CurrentPage { get; set; }  // 新增
        public string LPage { get; set; }        // 新增
        public wap_bbs_Model BookVo { get; set; }
        public user_Model UserVo { get; set; }

        protected override void Render(HtmlTextWriter writer)
        {
            using (var sw = new System.IO.StringWriter(new System.Text.StringBuilder(1024)))
            using (var hw = new HtmlTextWriter(sw))
            {
                base.Render(hw);
                ((Book_View)Page).strhtml.Append(sw.ToString());
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                LikeCount = BookVo?.suport ?? 0;
            }
        }
    }
}