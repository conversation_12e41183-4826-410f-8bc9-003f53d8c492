-- TempDB 使用情况简化分析
-- 兼容SQL Server 2022，兼容级别160

-- 1. TempDB 基本空间使用情况
SELECT 
    f.name AS file_name,
    f.type_desc,
    f.size * 8 / 1024 AS allocated_size_mb,
    FILEPROPERTY(f.name, 'SpaceUsed') * 8 / 1024 AS used_space_mb,
    (f.size - FILEPROPERTY(f.name, 'SpaceUsed')) * 8 / 1024 AS free_space_mb,
    CAST(FILEPROPERTY(f.name, 'SpaceUsed') * 100.0 / f.size AS DECIMAL(5,2)) AS used_percentage
FROM sys.master_files f
WHERE f.database_id = DB_ID('tempdb')
ORDER BY f.type, f.file_id;

-- 2. TempDB 性能计数器
SELECT 
    counter_name,
    cntr_value,
    CASE counter_name
        WHEN 'Data File(s) Size (KB)' THEN CAST(cntr_value / 1024 AS VARCHAR(20)) + ' MB'
        WHEN 'Log File(s) Size (KB)' THEN CAST(cntr_value / 1024 AS VARCHAR(20)) + ' MB'
        WHEN 'Log File(s) Used Size (KB)' THEN CAST(cntr_value / 1024 AS VARCHAR(20)) + ' MB'
        WHEN 'Percent Log Used' THEN CAST(cntr_value AS VARCHAR(20)) + '%'
        ELSE CAST(cntr_value AS VARCHAR(20))
    END AS formatted_value
FROM sys.dm_os_performance_counters
WHERE object_name LIKE '%Databases%'
    AND instance_name = 'tempdb'
    AND counter_name IN (
        'Data File(s) Size (KB)',
        'Log File(s) Size (KB)', 
        'Log File(s) Used Size (KB)',
        'Percent Log Used'
    );

-- 3. 检查 TempDB 相关等待
SELECT 
    wait_type,
    waiting_tasks_count,
    wait_time_ms,
    max_wait_time_ms,
    signal_wait_time_ms,
    wait_time_ms / NULLIF(waiting_tasks_count, 0) AS avg_wait_time_ms
FROM sys.dm_os_wait_stats
WHERE wait_type IN (
    'PAGELATCH_SH', 'PAGELATCH_EX', 'PAGELATCH_UP',
    'PAGEIOLATCH_SH', 'PAGEIOLATCH_EX', 'PAGEIOLATCH_UP',
    'WRITELOG', 'LOGBUFFER'
)
    AND waiting_tasks_count > 0
ORDER BY wait_time_ms DESC;

-- 4. 当前活动会话的 TempDB 使用
SELECT 
    s.session_id,
    s.login_name,
    s.program_name,
    r.command,
    r.status,
    ISNULL(r.total_elapsed_time, 0) AS elapsed_time_ms,
    SUBSTRING(st.text, (r.statement_start_offset/2)+1, 
        ((CASE r.statement_end_offset 
            WHEN -1 THEN DATALENGTH(st.text)
            ELSE r.statement_end_offset 
        END - r.statement_start_offset)/2) + 1) AS current_statement
FROM sys.dm_exec_sessions s
LEFT JOIN sys.dm_exec_requests r ON s.session_id = r.session_id
OUTER APPLY sys.dm_exec_sql_text(r.sql_handle) st
WHERE s.session_id > 50  -- 排除系统会话
    AND s.is_user_process = 1
    AND (r.command IS NOT NULL OR s.open_transaction_count > 0)
ORDER BY r.total_elapsed_time DESC;

-- 5. 简单的 TempDB 文件增长检查
-- 查看错误日志中的自动增长事件（最近7天）
CREATE TABLE #ErrorLog (
    LogDate DATETIME,
    ProcessInfo VARCHAR(50),
    LogText NVARCHAR(MAX)
);

INSERT INTO #ErrorLog
EXEC xp_readerrorlog 0, 1, N'autogrow', N'tempdb';

SELECT 
    LogDate,
    LogText,
    CASE 
        WHEN LogText LIKE '%grew%' THEN '文件增长'
        WHEN LogText LIKE '%shrunk%' THEN '文件收缩'
        ELSE '其他'
    END AS event_type
FROM #ErrorLog
WHERE LogDate > DATEADD(day, -7, GETDATE())
ORDER BY LogDate DESC;

DROP TABLE #ErrorLog;

-- 6. TempDB 使用建议分析
WITH TempDBStats AS (
    SELECT 
        SUM(CASE WHEN type_desc = 'ROWS' THEN size * 8 / 1024 END) AS total_data_mb,
        SUM(CASE WHEN type_desc = 'ROWS' THEN FILEPROPERTY(name, 'SpaceUsed') * 8 / 1024 END) AS used_data_mb,
        SUM(CASE WHEN type_desc = 'LOG' THEN size * 8 / 1024 END) AS total_log_mb,
        SUM(CASE WHEN type_desc = 'LOG' THEN FILEPROPERTY(name, 'SpaceUsed') * 8 / 1024 END) AS used_log_mb,
        COUNT(CASE WHEN type_desc = 'ROWS' THEN 1 END) AS data_file_count
    FROM sys.master_files 
    WHERE database_id = DB_ID('tempdb')
)
SELECT 
    total_data_mb,
    used_data_mb,
    CAST(used_data_mb * 100.0 / total_data_mb AS DECIMAL(5,2)) AS data_usage_percent,
    total_log_mb,
    used_log_mb,
    CAST(used_log_mb * 100.0 / total_log_mb AS DECIMAL(5,2)) AS log_usage_percent,
    data_file_count,
    CASE 
        WHEN used_data_mb * 100.0 / total_data_mb < 50 THEN '数据文件大小充足'
        WHEN used_data_mb * 100.0 / total_data_mb < 80 THEN '数据文件使用正常'
        ELSE '数据文件可能需要增加大小'
    END AS data_recommendation,
    CASE 
        WHEN used_log_mb * 100.0 / total_log_mb < 50 THEN '日志文件大小充足'
        WHEN used_log_mb * 100.0 / total_log_mb < 80 THEN '日志文件使用正常'
        ELSE '日志文件可能需要增加大小'
    END AS log_recommendation
FROM TempDBStats;
