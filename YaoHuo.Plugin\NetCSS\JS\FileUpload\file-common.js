// 公共变量
var selectedFiles = [];
var maxFiles = 9;

// 拖拽相关函数
function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById('fileSelectArea').classList.add('drag-over');
}

function handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById('fileSelectArea').classList.remove('drag-over');
}

function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById('fileSelectArea').classList.remove('drag-over');
}

// 粘贴处理
document.addEventListener('paste', function(e) {
    var items = (e.clipboardData || e.originalEvent.clipboardData).items;
    var files = [];
    for (var i = 0; i < items.length; i++) {
        if (items[i].kind === 'file') {
            files.push(items[i].getAsFile());
        }
    }
    if (files.length > 0) {
        handleFiles(files);
    }
});

// 文件处理核心函数
async function handleFiles(files) {
    var totalFiles = selectedFiles.length + files.length;
    if(totalFiles > maxFiles) {
        showErrorDialog('文件数量超限', `最多只能选择${maxFiles}个文件，当前已选择${selectedFiles.length}个文件`);
        return;
    }

    // 逐个验证并添加文件
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        // 如果文件不存在且验证通过，则添加
        if (!isFileAlreadySelected(file)) {
            const isValid = await validateFileSelection(file);
            if (isValid) {
                selectedFiles.push(file);
            }
        }
    }

    // 更新显示
    updateFileDisplay();
}

// 检查文件是否已存在
function isFileAlreadySelected(newFile) {
    return selectedFiles.some(existingFile => 
        existingFile.name === newFile.name && 
        existingFile.size === newFile.size &&
        existingFile.lastModified === newFile.lastModified
    );
}

// 移除文件
function removeFile(index) {
    selectedFiles.splice(index, 1);
    updateFileDisplay();
}

// 统一的文件显示更新函数
function updateFileDisplay(options = {}) {
    const {
        isNewPost = false,    // 是否是发新帖
        isReply = false,      // 是否是回复
        isAppend = false      // 是否是续传
    } = options;

    var container = document.getElementById('fileUploadContainer');
    var fileSelectArea = document.getElementById('fileSelectArea');
    var submitBtn = document.getElementById('submitBtn');
    container.innerHTML = '';
    
    // 处理文件区域显示
    if(selectedFiles.length > 0) {
        fileSelectArea.style.display = 'none';
        submitBtn.style.display = 'flex';
        
        // 添加操作按钮
        var btnContainer = document.createElement('div');
        btnContainer.className = 'button-container';
        
        var appendBtn = document.createElement('button');
        appendBtn.type = 'button';
        appendBtn.className = 'action-btn';
        appendBtn.innerText = '追加文件';
        appendBtn.onclick = function() {
            document.getElementById('multiFileSelect').click();
        };
        
        var removeAllBtn = document.createElement('button');
        removeAllBtn.type = 'button';
        removeAllBtn.className = 'action-btn';
        removeAllBtn.innerText = '移除所有';
        removeAllBtn.onclick = function() {
            selectedFiles = [];
            document.getElementById('multiFileSelect').value = '';
            updateFileDisplay(options);
            document.getElementById('submitBtn').style.display = 'none';
            fileSelectArea.style.display = 'flex';
        };
        
        btnContainer.appendChild(appendBtn);
        btnContainer.appendChild(removeAllBtn);
        container.appendChild(btnContainer);
    } else {
        fileSelectArea.style.display = 'flex';
        submitBtn.style.display = 'none';
        fileSelectArea.style.flexDirection = 'column';
        fileSelectArea.style.alignItems = 'center';
        fileSelectArea.style.justifyContent = 'center';
    }
    
    // 显示每个文件
    selectedFiles.forEach(function(file, i) {
        var fileArea = document.createElement('div');
        fileArea.className = 'fileUploadArea';
        fileArea.style.display = 'block';
        
        // 修复 HTML 字符串的闭合标签
        var fileContent = `
            <div class="file-item">
                <div class="file-header">
                    <div class="file-title">
                        <div class="file-number">${i + 1}</div>
                        <div class="file-name">${file.name}</div>
                    </div>
                    <button type="button" class="removeBtn" onclick="removeFile(${i})">移除</button>
                </div>
                <input type="file" style="display:none" name="book_file" accept=".txt,.zip,.rar,.7z,.apk,.jpg,.jpeg,.png,.gif,.webp,.torrent,.mp3,.wma,.wav,.pdf,.xls,.doc,.docx" />
                <textarea name="book_file_info" class="book_file_info" placeholder="选填文件说明，可以留空" oninput="adjustTextareaHeight(this)" rows="2"></textarea>
            </div>
        `;
        
        fileArea.innerHTML = fileContent;
        container.appendChild(fileArea);
        
        // 设置文件
        var dt = new DataTransfer();
        dt.items.add(file);
        fileArea.querySelector('input[type="file"]').files = dt.files;
        
        // 初始化新添加的文件说明文本框
        const newTextarea = fileArea.querySelector('textarea.book_file_info');
        if (newTextarea) {
            adjustTextareaHeight(newTextarea);
            newTextarea.addEventListener('input', function() {
                adjustTextareaHeight(this);
            });
        }
    });
    
    // 更新文件数量
    document.getElementById('numInput').value = selectedFiles.length || 1;
    
    // 根据不同页面类型处理提交按钮显示
    if(isNewPost) {
        submitBtn.style.display = selectedFiles.length > 0 ? 'flex' : 'none';
    } else if(isReply || isAppend) {
        submitBtn.style.display = selectedFiles.length > 0 ? 'flex' : 'none';
    }
}

// 表单验证
document.addEventListener('DOMContentLoaded', function() {
    var form = document.querySelector('form[name="f"]');
    if(form) {
        form.addEventListener('submit', function(e) {
            // 文件验证
            var fileInputs = document.querySelectorAll('input[name="book_file"]');
            var hasFiles = false;
            var fileCount = 0;
            
            fileInputs.forEach(input => {
                if(input.files && input.files.length > 0) {
                    hasFiles = true;
                    fileCount++;
                }
            });
            
            if(!hasFiles) {
                e.preventDefault();
                alert('请至少选择一个文件');
                return false;
            }
            
            // 发新帖时的标题和内容验证
            var titleInput = document.querySelector('input[name="book_title"]');
            var contentTextarea = document.querySelector('textarea[name="book_content"]');
            
            if(titleInput && contentTextarea) {  // 发新帖页面
                if(titleInput.value.length < 5) {
                    e.preventDefault();
                    alert('标题最少5个字符');
                    return false;
                }
                
                if(contentTextarea.value.length < 15) {
                    e.preventDefault();
                    alert('内容最少15个字符');
                    return false;
                }
            }
            
            document.getElementById('numInput').value = fileCount;
        });
    }
});

// 文件选择验证函数
function validateFileSelection(file) {
    const imageTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    const maxSize = 1024 * 1024; // 1MB in bytes
    
    // 检查是否是图片文件
    const isImage = imageTypes.includes(file.type.toLowerCase());
    
    // 如果声称是图片文件，进行进一步验证
    if (isImage) {
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    // 图片加载成功，说明是真实图片
                    resolve(true);
                };
                img.onerror = function() {
                    // 图片加载失败，可能是伪装的文件
                    showErrorDialog('无效的图片文件', `文件 "${file.name}" 不是有效的图片文件。`);
                    resolve(false);
                };
                img.src = e.target.result;
            };
            reader.onerror = function() {
                showErrorDialog('文件读取错误', `文件 "${file.name}" 读取失败。`);
                resolve(false);
            };
            reader.readAsDataURL(file);
        });
    }
    
    // 非图片文件检查大小
    if (!isImage && file.size > maxSize) {
        showErrorDialog('文件大小超限', `文件 "${file.name}" 大小超过限制。当前文件大小: ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
        return Promise.resolve(false);
    }
    
    return Promise.resolve(true);
}

// 显示错误对话框
function showErrorDialog(title, message) {
    const dialog = document.createElement('dialog');
    dialog.className = 'dialog-url';
    
    dialog.innerHTML = `
        <div class="dialog-url-header">
            <svg class="dialog-url-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <h2 class="dialog-url-title">${title}</h2>
        </div>
        <p class="dialog-url-description">${message}</p>
        <div class="dialog-url-footer">
            <button class="dialog-url-button" onclick="this.closest('dialog').close()">确定</button>
        </div>
    `;
    
    document.body.appendChild(dialog);
    dialog.showModal();
    
    // 点击背景关闭
    dialog.addEventListener('click', (e) => {
        if (e.target === dialog) dialog.close();
    });
}

// 修改文件选择处理函数
function handleFileSelect(event) {
    event.preventDefault();
    const files = event.target.files || event.dataTransfer.files;
    handleFiles(files);
    
    // 清空文件输入框的值，允许重复选择相同文件
    if (event.target && event.target.value) {
        event.target.value = '';
    }
}

// DOM加载完成后绑定事件
document.addEventListener('DOMContentLoaded', function() {
    // 文件选择事件
    const fileInput = document.getElementById('multiFileSelect');
    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelect);
    }
    
    // 拖放事件
    const dropZone = document.querySelector('.file-select-area');
    if (dropZone) {
        dropZone.addEventListener('drop', handleFileSelect);
        dropZone.addEventListener('dragover', handleDragOver);
        dropZone.addEventListener('dragleave', handleDragLeave);
    }
});