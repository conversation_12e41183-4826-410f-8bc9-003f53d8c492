// KenLin_ClassManager, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// KeLin.ClassManager.DAL.user_DAL
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using KeLin.ClassManager;
using KeLin.ClassManager.ExUtility;
using KeLin.ClassManager.Model;
using KeLin.ClassManager.Tool;

public class user_DAL
{
	private string string_0 = "";

	private string string_1 = "";

	public user_DAL(string InstanceName)
	{
		string_0 = InstanceName;
		string_1 = PubConstant.GetConnectionString(string_0);
	}

	public long GetHangBiaoShi()
	{
		return 0L;
	}

	public int Add(user_Model model)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("insert into [user](");
		stringBuilder.Append("siteid,username,nickname,password,managerlvl,sex,age,shenggao,tizhong,xing<PERSON>o,aihao,fenfuo,zhiye,city,mobile,email,money,moneyname,moneyregular,RegTime,LastLoginIP,LoginTimes,LockUser,headimg,remark,sitename,siteimg,siteuptip,sitedowntip,siteposition,siterowremark,sitelistflag,sitelist,sitetype,MaxPerPage_Default,MaxPerPage_Content,MaxFileSize,SaveUpFilesPath,UpFileType,CharFilter,UAFilter,SessionTimeout,MailServer,MailServerUserName,MailServerPassWord,sitemoneyname,sitespace,myspace,siteRight,isCheck,endTime,SidTimeOut,lvlNumer,lvlTimeImg,lvlRegular,myBankMoney,chuiNiu,version)");
		stringBuilder.Append(" values (");
		stringBuilder.Append("@siteid,@username,@nickname,@password,@managerlvl,@sex,@age,@shenggao,@tizhong,@xingzuo,@aihao,@fenfuo,@zhiye,@city,@mobile,@email,@money,@moneyname,@moneyregular,@RegTime,@LastLoginIP,@LoginTimes,@LockUser,@headimg,@remark,@sitename,@siteimg,@siteuptip,@sitedowntip,@siteposition,@siterowremark,@sitelistflag,@sitelist,@sitetype,@MaxPerPage_Default,@MaxPerPage_Content,@MaxFileSize,@SaveUpFilesPath,@UpFileType,@CharFilter,@UAFilter,@SessionTimeout,@MailServer,@MailServerUserName,@MailServerPassWord,@sitemoneyname,@sitespace,@myspace,@siteRight,@isCheck,@endTime,@SidTimeOut,@lvlNumer,@lvlTimeImg,@lvlRegular,@myBankMoney,@chuiNiu,@version)");
		stringBuilder.Append(";select @@IDENTITY");
		SqlParameter[] array = new SqlParameter[58]
		{
			new SqlParameter("@siteid", SqlDbType.BigInt),
			new SqlParameter("@username", SqlDbType.NVarChar),
			new SqlParameter("@nickname", SqlDbType.NVarChar),
			new SqlParameter("@password", SqlDbType.NVarChar),
			new SqlParameter("@managerlvl", SqlDbType.NVarChar),
			new SqlParameter("@sex", SqlDbType.SmallInt),
			new SqlParameter("@age", SqlDbType.SmallInt),
			new SqlParameter("@shenggao", SqlDbType.NVarChar),
			new SqlParameter("@tizhong", SqlDbType.NVarChar),
			new SqlParameter("@xingzuo", SqlDbType.NVarChar),
			new SqlParameter("@aihao", SqlDbType.NVarChar),
			new SqlParameter("@fenfuo", SqlDbType.NVarChar),
			new SqlParameter("@zhiye", SqlDbType.NVarChar),
			new SqlParameter("@city", SqlDbType.NVarChar),
			new SqlParameter("@mobile", SqlDbType.NVarChar),
			new SqlParameter("@email", SqlDbType.NVarChar),
			new SqlParameter("@money", SqlDbType.BigInt),
			new SqlParameter("@moneyname", SqlDbType.NText),
			new SqlParameter("@moneyregular", SqlDbType.NVarChar),
			new SqlParameter("@RegTime", SqlDbType.DateTime),
			new SqlParameter("@LastLoginIP", SqlDbType.NVarChar),
			new SqlParameter("@LoginTimes", SqlDbType.BigInt),
			new SqlParameter("@LockUser", SqlDbType.SmallInt),
			new SqlParameter("@headimg", SqlDbType.NVarChar),
			new SqlParameter("@remark", SqlDbType.NVarChar),
			new SqlParameter("@sitename", SqlDbType.NVarChar),
			new SqlParameter("@siteimg", SqlDbType.NVarChar),
			new SqlParameter("@siteuptip", SqlDbType.NText),
			new SqlParameter("@sitedowntip", SqlDbType.NText),
			new SqlParameter("@siteposition", SqlDbType.NVarChar),
			new SqlParameter("@siterowremark", SqlDbType.NText),
			new SqlParameter("@sitelistflag", SqlDbType.SmallInt),
			new SqlParameter("@sitelist", SqlDbType.SmallInt),
			new SqlParameter("@sitetype", SqlDbType.Int),
			new SqlParameter("@MaxPerPage_Default", SqlDbType.SmallInt),
			new SqlParameter("@MaxPerPage_Content", SqlDbType.SmallInt),
			new SqlParameter("@MaxFileSize", SqlDbType.Int),
			new SqlParameter("@SaveUpFilesPath", SqlDbType.NVarChar),
			new SqlParameter("@UpFileType", SqlDbType.NText),
			new SqlParameter("@CharFilter", SqlDbType.NText),
			new SqlParameter("@UAFilter", SqlDbType.NText),
			new SqlParameter("@SessionTimeout", SqlDbType.SmallInt),
			new SqlParameter("@MailServer", SqlDbType.NVarChar),
			new SqlParameter("@MailServerUserName", SqlDbType.NVarChar),
			new SqlParameter("@MailServerPassWord", SqlDbType.NVarChar),
			new SqlParameter("@sitemoneyname", SqlDbType.NVarChar),
			new SqlParameter("@sitespace", SqlDbType.BigInt),
			new SqlParameter("@myspace", SqlDbType.BigInt),
			new SqlParameter("@siteRight", SqlDbType.SmallInt),
			new SqlParameter("@isCheck", SqlDbType.SmallInt),
			new SqlParameter("@endTime", SqlDbType.DateTime),
			new SqlParameter("@SidTimeOut", SqlDbType.NVarChar),
			new SqlParameter("@lvlNumer", SqlDbType.NText),
			new SqlParameter("@lvlTimeImg", SqlDbType.NText),
			new SqlParameter("@lvlRegular", SqlDbType.NText),
			new SqlParameter("@myBankMoney", SqlDbType.BigInt),
			new SqlParameter("@chuiNiu", SqlDbType.NVarChar),
			new SqlParameter("@version", SqlDbType.NVarChar)
		};
		array[0].Value = model.siteid;
		array[1].Value = model.username;
		array[2].Value = model.nickname;
		array[3].Value = model.password;
		array[4].Value = model.managerlvl;
		array[5].Value = model.Int64_0;
		array[6].Value = model.Int64_1;
		array[7].Value = model.shenggao;
		array[8].Value = model.tizhong;
		array[9].Value = model.xingzuo;
		array[10].Value = model.aihao;
		array[11].Value = model.fenfuo;
		array[12].Value = model.zhiye;
		array[13].Value = model.city;
		array[14].Value = model.mobile;
		array[15].Value = model.email;
		array[16].Value = model.money;
		array[17].Value = model.moneyname;
		array[18].Value = model.moneyregular;
		array[19].Value = model.RegTime;
		array[20].Value = model.LastLoginIP;
		array[21].Value = model.LoginTimes;
		array[22].Value = model.LockUser;
		array[23].Value = model.headimg;
		array[24].Value = model.remark;
		array[25].Value = model.sitename;
		array[26].Value = model.siteimg;
		array[27].Value = model.siteuptip;
		array[28].Value = model.sitedowntip;
		array[29].Value = model.siteposition;
		array[30].Value = model.siterowremark;
		array[31].Value = model.sitelistflag;
		array[32].Value = model.sitelist;
		array[33].Value = model.sitetype;
		array[34].Value = model.MaxPerPage_Default;
		array[35].Value = model.MaxPerPage_Content;
		array[36].Value = model.MaxFileSize;
		array[37].Value = model.SaveUpFilesPath;
		array[38].Value = model.UpFileType;
		array[39].Value = model.CharFilter;
		array[40].Value = model.UAFilter;
		array[41].Value = model.SessionTimeout;
		array[42].Value = model.MailServer;
		array[43].Value = model.MailServerUserName;
		array[44].Value = model.MailServerPassWord;
		array[45].Value = model.sitemoneyname;
		array[46].Value = model.sitespace;
		array[47].Value = model.myspace;
		array[48].Value = model.siteRight;
		array[49].Value = model.isCheck;
		array[50].Value = model.endTime;
		array[51].Value = model.SidTimeOut;
		array[52].Value = model.lvlNumer;
		array[53].Value = model.lvlTimeImg;
		array[54].Value = model.lvlRegular;
		array[55].Value = model.myBankMoney;
		array[56].Value = model.chuiNiu;
		array[57].Value = model.Version;
		object obj = DbHelperSQL.ExecuteScalar(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (obj == null)
		{
			return 0;
		}
		return Convert.ToInt32(obj);
	}

	public void Update(user_Model model)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("update [user] set ");
		stringBuilder.Append("siteid=@siteid,");
		stringBuilder.Append("username=@username,");
		stringBuilder.Append("nickname=@nickname,");
		stringBuilder.Append("password=@password,");
		stringBuilder.Append("managerlvl=@managerlvl,");
		stringBuilder.Append("sex=@sex,");
		stringBuilder.Append("age=@age,");
		stringBuilder.Append("shenggao=@shenggao,");
		stringBuilder.Append("tizhong=@tizhong,");
		stringBuilder.Append("xingzuo=@xingzuo,");
		stringBuilder.Append("aihao=@aihao,");
		stringBuilder.Append("fenfuo=@fenfuo,");
		stringBuilder.Append("zhiye=@zhiye,");
		stringBuilder.Append("city=@city,");
		stringBuilder.Append("mobile=@mobile,");
		stringBuilder.Append("email=@email,");
		stringBuilder.Append("money=@money,");
		stringBuilder.Append("moneyname=@moneyname,");
		stringBuilder.Append("moneyregular=@moneyregular,");
		stringBuilder.Append("RegTime=@RegTime,");
		stringBuilder.Append("LastLoginIP=@LastLoginIP,");
		stringBuilder.Append("LastLoginTime=@LastLoginTime,");
		stringBuilder.Append("LoginTimes=@LoginTimes,");
		stringBuilder.Append("LockUser=@LockUser,");
		stringBuilder.Append("headimg=@headimg,");
		stringBuilder.Append("remark=@remark,");
		stringBuilder.Append("sitename=@sitename,");
		stringBuilder.Append("siteimg=@siteimg,");
		stringBuilder.Append("siteuptip=@siteuptip,");
		stringBuilder.Append("sitedowntip=@sitedowntip,");
		stringBuilder.Append("siteposition=@siteposition,");
		stringBuilder.Append("siterowremark=@siterowremark,");
		stringBuilder.Append("sitelistflag=@sitelistflag,");
		stringBuilder.Append("sitelist=@sitelist,");
		stringBuilder.Append("sitetype=@sitetype,");
		stringBuilder.Append("MaxPerPage_Default=@MaxPerPage_Default,");
		stringBuilder.Append("MaxPerPage_Content=@MaxPerPage_Content,");
		stringBuilder.Append("MaxFileSize=@MaxFileSize,");
		stringBuilder.Append("SaveUpFilesPath=@SaveUpFilesPath,");
		stringBuilder.Append("UpFileType=@UpFileType,");
		stringBuilder.Append("CharFilter=@CharFilter,");
		stringBuilder.Append("UAFilter=@UAFilter,");
		stringBuilder.Append("SessionTimeout=@SessionTimeout,");
		stringBuilder.Append("MailServer=@MailServer,");
		stringBuilder.Append("MailServerUserName=@MailServerUserName,");
		stringBuilder.Append("MailServerPassWord=@MailServerPassWord,");
		stringBuilder.Append("sitemoneyname=@sitemoneyname,");
		stringBuilder.Append("sitespace=@sitespace,");
		stringBuilder.Append("myspace=@myspace,");
		stringBuilder.Append("siteRight=@siteRight,");
		stringBuilder.Append("isCheck=@isCheck,");
		stringBuilder.Append("endTime=@endTime,");
		stringBuilder.Append("SidTimeOut=@SidTimeOut,");
		stringBuilder.Append("lvlNumer=@lvlNumer,");
		stringBuilder.Append("lvlTimeImg=@lvlTimeImg,");
		stringBuilder.Append("lvlRegular=@lvlRegular,");
		stringBuilder.Append("myBankMoney=@myBankMoney,");
		stringBuilder.Append("myBankTime=@myBankTime,");
		stringBuilder.Append("chuiNiu=@chuiNiu,");
		stringBuilder.Append("version=@version");
		stringBuilder.Append(" where userid=@userid ");
		SqlParameter[] array = new SqlParameter[61]
		{
			new SqlParameter("@userid", SqlDbType.BigInt),
			new SqlParameter("@siteid", SqlDbType.BigInt),
			new SqlParameter("@username", SqlDbType.NVarChar),
			new SqlParameter("@nickname", SqlDbType.NVarChar),
			new SqlParameter("@password", SqlDbType.NVarChar),
			new SqlParameter("@managerlvl", SqlDbType.NVarChar),
			new SqlParameter("@sex", SqlDbType.SmallInt),
			new SqlParameter("@age", SqlDbType.SmallInt),
			new SqlParameter("@shenggao", SqlDbType.NVarChar),
			new SqlParameter("@tizhong", SqlDbType.NVarChar),
			new SqlParameter("@xingzuo", SqlDbType.NVarChar),
			new SqlParameter("@aihao", SqlDbType.NVarChar),
			new SqlParameter("@fenfuo", SqlDbType.NVarChar),
			new SqlParameter("@zhiye", SqlDbType.NVarChar),
			new SqlParameter("@city", SqlDbType.NVarChar),
			new SqlParameter("@mobile", SqlDbType.NVarChar),
			new SqlParameter("@email", SqlDbType.NVarChar),
			new SqlParameter("@money", SqlDbType.BigInt),
			new SqlParameter("@moneyname", SqlDbType.NText),
			new SqlParameter("@moneyregular", SqlDbType.NVarChar),
			new SqlParameter("@RegTime", SqlDbType.DateTime),
			new SqlParameter("@LastLoginIP", SqlDbType.NVarChar),
			new SqlParameter("@LastLoginTime", SqlDbType.DateTime),
			new SqlParameter("@LoginTimes", SqlDbType.BigInt),
			new SqlParameter("@LockUser", SqlDbType.SmallInt),
			new SqlParameter("@headimg", SqlDbType.NVarChar),
			new SqlParameter("@remark", SqlDbType.NVarChar),
			new SqlParameter("@sitename", SqlDbType.NVarChar),
			new SqlParameter("@siteimg", SqlDbType.NVarChar),
			new SqlParameter("@siteuptip", SqlDbType.NText),
			new SqlParameter("@sitedowntip", SqlDbType.NText),
			new SqlParameter("@siteposition", SqlDbType.NVarChar),
			new SqlParameter("@siterowremark", SqlDbType.NText),
			new SqlParameter("@sitelistflag", SqlDbType.SmallInt),
			new SqlParameter("@sitelist", SqlDbType.SmallInt),
			new SqlParameter("@sitetype", SqlDbType.Int),
			new SqlParameter("@MaxPerPage_Default", SqlDbType.SmallInt),
			new SqlParameter("@MaxPerPage_Content", SqlDbType.SmallInt),
			new SqlParameter("@MaxFileSize", SqlDbType.Int),
			new SqlParameter("@SaveUpFilesPath", SqlDbType.NVarChar),
			new SqlParameter("@UpFileType", SqlDbType.NText),
			new SqlParameter("@CharFilter", SqlDbType.NText),
			new SqlParameter("@UAFilter", SqlDbType.NText),
			new SqlParameter("@SessionTimeout", SqlDbType.SmallInt),
			new SqlParameter("@MailServer", SqlDbType.NVarChar),
			new SqlParameter("@MailServerUserName", SqlDbType.NVarChar),
			new SqlParameter("@MailServerPassWord", SqlDbType.NVarChar),
			new SqlParameter("@sitemoneyname", SqlDbType.NVarChar),
			new SqlParameter("@sitespace", SqlDbType.BigInt),
			new SqlParameter("@myspace", SqlDbType.BigInt),
			new SqlParameter("@siteRight", SqlDbType.SmallInt),
			new SqlParameter("@isCheck", SqlDbType.SmallInt),
			new SqlParameter("@endTime", SqlDbType.DateTime),
			new SqlParameter("@SidTimeOut", SqlDbType.NVarChar),
			new SqlParameter("@lvlNumer", SqlDbType.NText),
			new SqlParameter("@lvlTimeImg", SqlDbType.NText),
			new SqlParameter("@lvlRegular", SqlDbType.NText),
			new SqlParameter("@myBankMoney", SqlDbType.BigInt),
			new SqlParameter("@myBankTime", SqlDbType.DateTime),
			new SqlParameter("@chuiNiu", SqlDbType.NVarChar),
			new SqlParameter("@version", SqlDbType.BigInt)
		};
		array[0].Value = model.userid;
		array[1].Value = model.siteid;
		array[2].Value = model.username;
		array[3].Value = model.nickname;
		array[4].Value = model.password;
		array[5].Value = model.managerlvl;
		array[6].Value = model.Int64_0;
		array[7].Value = model.Int64_1;
		array[8].Value = model.shenggao;
		array[9].Value = model.tizhong;
		array[10].Value = model.xingzuo;
		array[11].Value = model.aihao;
		array[12].Value = model.fenfuo;
		array[13].Value = model.zhiye;
		array[14].Value = model.city;
		array[15].Value = model.mobile;
		array[16].Value = model.email;
		array[17].Value = model.money;
		array[18].Value = model.moneyname;
		array[19].Value = model.moneyregular;
		array[20].Value = model.RegTime;
		array[21].Value = model.LastLoginIP;
		array[22].Value = model.LastLoginTime;
		array[23].Value = model.LoginTimes;
		array[24].Value = model.LockUser;
		array[25].Value = model.headimg;
		array[26].Value = model.remark;
		array[27].Value = model.sitename;
		array[28].Value = model.siteimg;
		array[29].Value = model.siteuptip;
		array[30].Value = model.sitedowntip;
		array[31].Value = model.siteposition;
		array[32].Value = model.siterowremark;
		array[33].Value = model.sitelistflag;
		array[34].Value = model.sitelist;
		array[35].Value = model.sitetype;
		array[36].Value = model.MaxPerPage_Default;
		array[37].Value = model.MaxPerPage_Content;
		array[38].Value = model.MaxFileSize;
		array[39].Value = model.SaveUpFilesPath;
		array[40].Value = model.UpFileType;
		array[41].Value = model.CharFilter;
		array[42].Value = model.UAFilter;
		array[43].Value = model.SessionTimeout;
		array[44].Value = model.MailServer;
		array[45].Value = model.MailServerUserName;
		array[46].Value = model.MailServerPassWord;
		array[47].Value = model.sitemoneyname;
		array[48].Value = model.sitespace;
		array[49].Value = model.myspace;
		array[50].Value = model.siteRight;
		array[51].Value = model.isCheck;
		if (model.endTime != DateTime.MinValue)
		{
			array[52].Value = model.endTime;
		}
		else
		{
			array[52].Value = null;
		}
		array[53].Value = model.SidTimeOut;
		array[54].Value = model.lvlNumer;
		array[55].Value = model.lvlTimeImg;
		array[56].Value = model.lvlRegular;
		array[57].Value = model.myBankMoney;
		array[58].Value = model.myBankTime;
		array[59].Value = model.chuiNiu;
		array[60].Value = model.Version;
		DbHelperSQL.ExecuteNonQuery(string_1, CommandType.Text, stringBuilder.ToString(), array);
	}

	public void UpdateUser(user_Model model)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("update [user] set ");
		stringBuilder.Append("nickname=@nickname,");
		stringBuilder.Append("password=@password,");
		stringBuilder.Append("managerlvl=@managerlvl,");
		stringBuilder.Append("sex=@sex,");
		stringBuilder.Append("age=@age,");
		stringBuilder.Append("shenggao=@shenggao,");
		stringBuilder.Append("tizhong=@tizhong,");
		stringBuilder.Append("xingzuo=@xingzuo,");
		stringBuilder.Append("aihao=@aihao,");
		stringBuilder.Append("fenfuo=@fenfuo,");
		stringBuilder.Append("zhiye=@zhiye,");
		stringBuilder.Append("city=@city,");
		stringBuilder.Append("mobile=@mobile,");
		stringBuilder.Append("email=@email,");
		stringBuilder.Append("money=@money,");
		stringBuilder.Append("moneyname=@moneyname,");
		stringBuilder.Append("LockUser=@LockUser,");
		stringBuilder.Append("remark=@remark,");
		stringBuilder.Append("MailServerUserName=@MailServerUserName,");
		stringBuilder.Append("myBankMoney=@myBankMoney,");
		stringBuilder.Append("LoginTimes=@LoginTimes,");
		stringBuilder.Append("expr=@expr,");
		stringBuilder.Append("SessionTimeout=@SessionTimeout");
		stringBuilder.Append(" where userid=@userid ");
		SqlParameter[] array = new SqlParameter[24]
		{
			new SqlParameter("@userid", SqlDbType.BigInt),
			new SqlParameter("@nickname", SqlDbType.NVarChar),
			new SqlParameter("@password", SqlDbType.NVarChar),
			new SqlParameter("@managerlvl", SqlDbType.NVarChar),
			new SqlParameter("@sex", SqlDbType.SmallInt),
			new SqlParameter("@age", SqlDbType.SmallInt),
			new SqlParameter("@shenggao", SqlDbType.NVarChar),
			new SqlParameter("@tizhong", SqlDbType.NVarChar),
			new SqlParameter("@xingzuo", SqlDbType.NVarChar),
			new SqlParameter("@aihao", SqlDbType.NVarChar),
			new SqlParameter("@fenfuo", SqlDbType.NVarChar),
			new SqlParameter("@zhiye", SqlDbType.NVarChar),
			new SqlParameter("@city", SqlDbType.NVarChar),
			new SqlParameter("@mobile", SqlDbType.NVarChar),
			new SqlParameter("@email", SqlDbType.NVarChar),
			new SqlParameter("@money", SqlDbType.BigInt),
			new SqlParameter("@moneyname", SqlDbType.NText),
			new SqlParameter("@LockUser", SqlDbType.SmallInt),
			new SqlParameter("@remark", SqlDbType.NVarChar),
			new SqlParameter("@MailServerUserName", SqlDbType.NVarChar),
			new SqlParameter("@myBankMoney", SqlDbType.BigInt),
			new SqlParameter("@LoginTimes", SqlDbType.BigInt),
			new SqlParameter("@expr", SqlDbType.BigInt),
			new SqlParameter("@SessionTimeout", SqlDbType.BigInt)
		};
		array[0].Value = model.userid;
		array[1].Value = model.nickname;
		array[2].Value = model.password;
		array[3].Value = model.managerlvl;
		array[4].Value = model.Int64_0;
		array[5].Value = model.Int64_1;
		array[6].Value = model.shenggao;
		array[7].Value = model.tizhong;
		array[8].Value = model.xingzuo;
		array[9].Value = model.aihao;
		array[10].Value = model.fenfuo;
		array[11].Value = model.zhiye;
		array[12].Value = model.city;
		array[13].Value = model.mobile;
		array[14].Value = model.email;
		array[15].Value = model.money;
		array[16].Value = model.moneyname;
		array[17].Value = model.LockUser;
		array[18].Value = model.remark;
		array[19].Value = model.MailServerUserName;
		array[20].Value = model.myBankMoney;
		array[21].Value = model.LoginTimes;
		array[22].Value = model.expr;
		array[23].Value = model.SessionTimeout;
		DbHelperSQL.ExecuteNonQuery(string_1, CommandType.Text, stringBuilder.ToString(), array);
	}

	public void UpdateUserEndTime(long siteid, long userid, DateTime dateTime_0)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("update [user] set ");
		stringBuilder.Append("endTime=@endTime");
		stringBuilder.Append(" where userid=@userid and siteid=@siteid ");
		SqlParameter[] array = new SqlParameter[3]
		{
			new SqlParameter("@endTime", SqlDbType.DateTime),
			new SqlParameter("@userid", SqlDbType.BigInt),
			new SqlParameter("@siteid", SqlDbType.BigInt)
		};
		array[0].Value = dateTime_0;
		array[1].Value = userid;
		array[2].Value = siteid;
		DbHelperSQL.ExecuteNonQuery(string_1, CommandType.Text, stringBuilder.ToString(), array);
	}

	public void UpdateUser_WAP(user_Model model)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("update [user] set ");
		stringBuilder.Append("sex=@sex,");
		stringBuilder.Append("age=@age,");
		stringBuilder.Append("shenggao=@shenggao,");
		stringBuilder.Append("tizhong=@tizhong,");
		stringBuilder.Append("xingzuo=@xingzuo,");
		stringBuilder.Append("aihao=@aihao,");
		stringBuilder.Append("fenfuo=@fenfuo,");
		stringBuilder.Append("zhiye=@zhiye,");
		stringBuilder.Append("city=@city,");
		stringBuilder.Append("mobile=@mobile,");
		stringBuilder.Append("email=@email,");
		stringBuilder.Append("remark=@remark ");
		stringBuilder.Append(" where userid=@userid ");
		SqlParameter[] array = new SqlParameter[13]
		{
			new SqlParameter("@sex", SqlDbType.SmallInt),
			new SqlParameter("@age", SqlDbType.SmallInt),
			new SqlParameter("@shenggao", SqlDbType.NVarChar),
			new SqlParameter("@tizhong", SqlDbType.NVarChar),
			new SqlParameter("@xingzuo", SqlDbType.NVarChar),
			new SqlParameter("@aihao", SqlDbType.NVarChar),
			new SqlParameter("@fenfuo", SqlDbType.NVarChar),
			new SqlParameter("@zhiye", SqlDbType.NVarChar),
			new SqlParameter("@city", SqlDbType.NVarChar),
			new SqlParameter("@mobile", SqlDbType.NVarChar),
			new SqlParameter("@email", SqlDbType.NVarChar),
			new SqlParameter("@remark", SqlDbType.NVarChar),
			new SqlParameter("@userid", SqlDbType.BigInt)
		};
		array[0].Value = model.Int64_0;
		array[1].Value = model.Int64_1;
		array[2].Value = model.shenggao;
		array[3].Value = model.tizhong;
		array[4].Value = model.xingzuo;
		array[5].Value = model.aihao;
		array[6].Value = model.fenfuo;
		array[7].Value = model.zhiye;
		array[8].Value = model.city;
		array[9].Value = model.mobile;
		array[10].Value = model.email;
		array[11].Value = model.remark;
		array[12].Value = model.userid;
		DbHelperSQL.ExecuteNonQuery(string_1, CommandType.Text, stringBuilder.ToString(), array);
	}

	public void Delete(long HangBiaoShi)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("delete from [user] ");
		stringBuilder.Append(" where userid=@HangBiaoShi ");
		SqlParameter[] array = new SqlParameter[1]
		{
			new SqlParameter("@HangBiaoShi", SqlDbType.BigInt)
		};
		array[0].Value = HangBiaoShi;
		DbHelperSQL.ExecuteNonQuery(string_1, CommandType.Text, stringBuilder.ToString(), array);
	}

	public user_Model GetModel(string UserName, string PassWord)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 userid,siteid,username,nickname,password,managerlvl,sex,age,shenggao,tizhong,xingzuo,aihao,fenfuo,zhiye,city,mobile,email,money,moneyname,moneyregular,RegTime,LastLoginIP,LastLoginTime,LoginTimes,LockUser,headimg,remark,sitename,siteimg,siteuptip,sitedowntip,siteposition,siterowremark,sitelistflag,sitelist,sitetype,MaxPerPage_Default,MaxPerPage_Content,MaxFileSize,SaveUpFilesPath,UpFileType,CharFilter,UAFilter,SessionTimeout,MailServer,MailServerUserName,MailServerPassWord,sitemoneyname,sitespace,myspace,siteRight,isCheck,endTime,SidTimeOut,lvlNumer,lvlTimeImg,lvlRegular,myBankMoney,myBankTime,chuiNiu,HangBiaoShi,version,rmb,expr,sitevip from [user] ");
		stringBuilder.Append(" where username=@username and managerlvl<>'02'");
		SqlParameter[] array = new SqlParameter[1]
		{
			new SqlParameter("@username", SqlDbType.NVarChar)
		};
		array[0].Value = UserName;
		user_Model user_Model = new user_Model();
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			if (dataSet.Tables[0].Rows[0]["userid"].ToString() != "")
			{
				user_Model.userid = long.Parse(dataSet.Tables[0].Rows[0]["userid"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["siteid"].ToString() != "")
			{
				user_Model.siteid = long.Parse(dataSet.Tables[0].Rows[0]["siteid"].ToString());
			}
			user_Model.username = dataSet.Tables[0].Rows[0]["username"].ToString();
			user_Model.nickname = dataSet.Tables[0].Rows[0]["nickname"].ToString();
			user_Model.nickname = user_Model.nickname.Replace("[", "").Replace("]", "");
			user_Model.password = dataSet.Tables[0].Rows[0]["password"].ToString();
			user_Model.managerlvl = dataSet.Tables[0].Rows[0]["managerlvl"].ToString();
			if (dataSet.Tables[0].Rows[0]["sex"].ToString() != "")
			{
				user_Model.Int64_0 = long.Parse(dataSet.Tables[0].Rows[0]["sex"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["age"].ToString() != "")
			{
				user_Model.Int64_1 = long.Parse(dataSet.Tables[0].Rows[0]["age"].ToString());
			}
			user_Model.shenggao = dataSet.Tables[0].Rows[0]["shenggao"].ToString();
			user_Model.tizhong = dataSet.Tables[0].Rows[0]["tizhong"].ToString();
			user_Model.xingzuo = dataSet.Tables[0].Rows[0]["xingzuo"].ToString();
			user_Model.aihao = dataSet.Tables[0].Rows[0]["aihao"].ToString();
			user_Model.fenfuo = dataSet.Tables[0].Rows[0]["fenfuo"].ToString();
			user_Model.zhiye = dataSet.Tables[0].Rows[0]["zhiye"].ToString();
			user_Model.city = dataSet.Tables[0].Rows[0]["city"].ToString();
			user_Model.city = user_Model.city.Replace("[", "").Replace("]", "");
			user_Model.mobile = dataSet.Tables[0].Rows[0]["mobile"].ToString();
			user_Model.email = dataSet.Tables[0].Rows[0]["email"].ToString();
			if (dataSet.Tables[0].Rows[0]["money"].ToString() != "")
			{
				user_Model.money = long.Parse(dataSet.Tables[0].Rows[0]["money"].ToString());
			}
			user_Model.moneyname = dataSet.Tables[0].Rows[0]["moneyname"].ToString();
			user_Model.moneyregular = dataSet.Tables[0].Rows[0]["moneyregular"].ToString();
			if (dataSet.Tables[0].Rows[0]["RegTime"].ToString() != "")
			{
				user_Model.RegTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["RegTime"].ToString());
			}
			user_Model.LastLoginIP = dataSet.Tables[0].Rows[0]["LastLoginIP"].ToString();
			if (dataSet.Tables[0].Rows[0]["LastLoginTime"].ToString() != "")
			{
				user_Model.LastLoginTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["LastLoginTime"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["LoginTimes"].ToString() != "")
			{
				user_Model.LoginTimes = long.Parse(dataSet.Tables[0].Rows[0]["LoginTimes"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["LockUser"].ToString() != "")
			{
				user_Model.LockUser = long.Parse(dataSet.Tables[0].Rows[0]["LockUser"].ToString());
			}
			user_Model.headimg = dataSet.Tables[0].Rows[0]["headimg"].ToString();
			user_Model.remark = dataSet.Tables[0].Rows[0]["remark"].ToString();
			user_Model.sitename = dataSet.Tables[0].Rows[0]["sitename"].ToString();
			user_Model.siteimg = dataSet.Tables[0].Rows[0]["siteimg"].ToString();
			user_Model.siteuptip = dataSet.Tables[0].Rows[0]["siteuptip"].ToString();
			user_Model.sitedowntip = dataSet.Tables[0].Rows[0]["sitedowntip"].ToString();
			user_Model.siteposition = dataSet.Tables[0].Rows[0]["siteposition"].ToString();
			user_Model.siterowremark = dataSet.Tables[0].Rows[0]["siterowremark"].ToString();
			if (dataSet.Tables[0].Rows[0]["sitelistflag"].ToString() != "")
			{
				user_Model.sitelistflag = long.Parse(dataSet.Tables[0].Rows[0]["sitelistflag"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["sitelist"].ToString() != "")
			{
				user_Model.sitelist = long.Parse(dataSet.Tables[0].Rows[0]["sitelist"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["sitetype"].ToString() != "")
			{
				user_Model.sitetype = long.Parse(dataSet.Tables[0].Rows[0]["sitetype"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["MaxPerPage_Default"].ToString() != "")
			{
				user_Model.MaxPerPage_Default = long.Parse(dataSet.Tables[0].Rows[0]["MaxPerPage_Default"].ToString());
			}
			if (user_Model.MaxPerPage_Content < 100L)
			{
				user_Model.MaxPerPage_Content = 100L;
			}
			if (dataSet.Tables[0].Rows[0]["MaxPerPage_Content"].ToString() != "")
			{
				user_Model.MaxPerPage_Content = long.Parse(dataSet.Tables[0].Rows[0]["MaxPerPage_Content"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["MaxFileSize"].ToString() != "")
			{
				user_Model.MaxFileSize = long.Parse(dataSet.Tables[0].Rows[0]["MaxFileSize"].ToString());
			}
			user_Model.SaveUpFilesPath = dataSet.Tables[0].Rows[0]["SaveUpFilesPath"].ToString();
			user_Model.UpFileType = dataSet.Tables[0].Rows[0]["UpFileType"].ToString();
			user_Model.CharFilter = dataSet.Tables[0].Rows[0]["CharFilter"].ToString();
			user_Model.UAFilter = dataSet.Tables[0].Rows[0]["UAFilter"].ToString();
			if (dataSet.Tables[0].Rows[0]["SessionTimeout"].ToString() != "")
			{
				user_Model.SessionTimeout = long.Parse(dataSet.Tables[0].Rows[0]["SessionTimeout"].ToString());
			}
			user_Model.MailServer = dataSet.Tables[0].Rows[0]["MailServer"].ToString();
			user_Model.MailServerUserName = dataSet.Tables[0].Rows[0]["MailServerUserName"].ToString();
			user_Model.MailServerPassWord = dataSet.Tables[0].Rows[0]["MailServerPassWord"].ToString();
			user_Model.sitemoneyname = dataSet.Tables[0].Rows[0]["sitemoneyname"].ToString();
			if (dataSet.Tables[0].Rows[0]["sitespace"].ToString() != "")
			{
				user_Model.sitespace = long.Parse(dataSet.Tables[0].Rows[0]["sitespace"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["myspace"].ToString() != "")
			{
				user_Model.myspace = long.Parse(dataSet.Tables[0].Rows[0]["myspace"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["siteRight"].ToString() != "")
			{
				user_Model.siteRight = long.Parse(dataSet.Tables[0].Rows[0]["siteRight"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["isCheck"].ToString() != "")
			{
				user_Model.isCheck = long.Parse(dataSet.Tables[0].Rows[0]["isCheck"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["endTime"].ToString() != "")
			{
				user_Model.endTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["endTime"].ToString());
			}
			user_Model.SidTimeOut = dataSet.Tables[0].Rows[0]["SidTimeOut"].ToString();
			user_Model.lvlNumer = dataSet.Tables[0].Rows[0]["lvlNumer"].ToString();
			user_Model.lvlTimeImg = dataSet.Tables[0].Rows[0]["lvlTimeImg"].ToString();
			user_Model.lvlRegular = dataSet.Tables[0].Rows[0]["lvlRegular"].ToString();
			if (dataSet.Tables[0].Rows[0]["myBankMoney"].ToString() != "")
			{
				user_Model.myBankMoney = long.Parse(dataSet.Tables[0].Rows[0]["myBankMoney"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["myBankTime"].ToString() != "")
			{
				user_Model.myBankTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["myBankTime"].ToString());
			}
			user_Model.chuiNiu = dataSet.Tables[0].Rows[0]["chuiNiu"].ToString();
			if (dataSet.Tables[0].Rows[0]["HangBiaoShi"].ToString() != "")
			{
				user_Model.HangBiaoShi = long.Parse(dataSet.Tables[0].Rows[0]["HangBiaoShi"].ToString());
			}
			user_Model.Version = dataSet.Tables[0].Rows[0]["version"].ToString();
			user_Model.RMB = decimal.Parse(dataSet.Tables[0].Rows[0]["rmb"].ToString());
			user_Model.expr = long.Parse(dataSet.Tables[0].Rows[0]["expr"].ToString());
			user_Model.siteVIP = dataSet.Tables[0].Rows[0]["siteVIP"].ToString();
			user_Model.isCheckSite = user_Model.isCheck;
			user_Model.isCheck = WapTool.GetIsCheck(user_Model.isCheck, user_Model.Version);
			return user_Model;
		}
		return null;
	}

	public user_Model GetUserModel(string strWhere)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 userid,siteid,username,nickname,password,managerlvl,sex,age,shenggao,tizhong,xingzuo,aihao,fenfuo,zhiye,city,mobile,email,money,moneyname,moneyregular,RegTime,LastLoginIP,LastLoginTime,LoginTimes,LockUser,headimg,remark,sitename,siteimg,siteuptip,sitedowntip,siteposition,siterowremark,sitelistflag,sitelist,sitetype,MaxPerPage_Default,MaxPerPage_Content,MaxFileSize,SaveUpFilesPath,UpFileType,CharFilter,UAFilter,SessionTimeout,MailServer,MailServerUserName,MailServerPassWord,sitemoneyname,sitespace,myspace,siteRight,isCheck,endTime,SidTimeOut,lvlNumer,lvlTimeImg,lvlRegular,myBankMoney,myBankTime,chuiNiu,HangBiaoShi,rmb,expr,sitevip from [user] ");
		stringBuilder.Append(" where ");
		stringBuilder.Append(strWhere);
		user_Model user_Model = new user_Model();
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString());
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			if (dataSet.Tables[0].Rows[0]["userid"].ToString() != "")
			{
				user_Model.userid = long.Parse(dataSet.Tables[0].Rows[0]["userid"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["siteid"].ToString() != "")
			{
				user_Model.siteid = long.Parse(dataSet.Tables[0].Rows[0]["siteid"].ToString());
			}
			user_Model.username = dataSet.Tables[0].Rows[0]["username"].ToString();
			user_Model.nickname = dataSet.Tables[0].Rows[0]["nickname"].ToString();
			user_Model.nickname = user_Model.nickname.Replace("[", "").Replace("]", "");
			user_Model.password = dataSet.Tables[0].Rows[0]["password"].ToString();
			user_Model.managerlvl = dataSet.Tables[0].Rows[0]["managerlvl"].ToString();
			if (dataSet.Tables[0].Rows[0]["sex"].ToString() != "")
			{
				user_Model.Int64_0 = long.Parse(dataSet.Tables[0].Rows[0]["sex"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["age"].ToString() != "")
			{
				user_Model.Int64_1 = long.Parse(dataSet.Tables[0].Rows[0]["age"].ToString());
			}
			user_Model.shenggao = dataSet.Tables[0].Rows[0]["shenggao"].ToString();
			user_Model.tizhong = dataSet.Tables[0].Rows[0]["tizhong"].ToString();
			user_Model.xingzuo = dataSet.Tables[0].Rows[0]["xingzuo"].ToString();
			user_Model.aihao = dataSet.Tables[0].Rows[0]["aihao"].ToString();
			user_Model.fenfuo = dataSet.Tables[0].Rows[0]["fenfuo"].ToString();
			user_Model.zhiye = dataSet.Tables[0].Rows[0]["zhiye"].ToString();
			user_Model.city = dataSet.Tables[0].Rows[0]["city"].ToString();
			user_Model.city = user_Model.city.Replace("[", "").Replace("]", "");
			user_Model.mobile = dataSet.Tables[0].Rows[0]["mobile"].ToString();
			user_Model.email = dataSet.Tables[0].Rows[0]["email"].ToString();
			if (dataSet.Tables[0].Rows[0]["money"].ToString() != "")
			{
				user_Model.money = long.Parse(dataSet.Tables[0].Rows[0]["money"].ToString());
			}
			user_Model.moneyname = dataSet.Tables[0].Rows[0]["moneyname"].ToString();
			user_Model.moneyregular = dataSet.Tables[0].Rows[0]["moneyregular"].ToString();
			if (dataSet.Tables[0].Rows[0]["RegTime"].ToString() != "")
			{
				user_Model.RegTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["RegTime"].ToString());
			}
			user_Model.LastLoginIP = dataSet.Tables[0].Rows[0]["LastLoginIP"].ToString();
			if (dataSet.Tables[0].Rows[0]["LastLoginTime"].ToString() != "")
			{
				user_Model.LastLoginTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["LastLoginTime"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["LoginTimes"].ToString() != "")
			{
				user_Model.LoginTimes = long.Parse(dataSet.Tables[0].Rows[0]["LoginTimes"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["LockUser"].ToString() != "")
			{
				user_Model.LockUser = long.Parse(dataSet.Tables[0].Rows[0]["LockUser"].ToString());
			}
			user_Model.headimg = dataSet.Tables[0].Rows[0]["headimg"].ToString();
			user_Model.remark = dataSet.Tables[0].Rows[0]["remark"].ToString();
			user_Model.sitename = dataSet.Tables[0].Rows[0]["sitename"].ToString();
			user_Model.siteimg = dataSet.Tables[0].Rows[0]["siteimg"].ToString();
			user_Model.siteuptip = dataSet.Tables[0].Rows[0]["siteuptip"].ToString();
			user_Model.sitedowntip = dataSet.Tables[0].Rows[0]["sitedowntip"].ToString();
			user_Model.siteposition = dataSet.Tables[0].Rows[0]["siteposition"].ToString();
			user_Model.siterowremark = dataSet.Tables[0].Rows[0]["siterowremark"].ToString();
			if (dataSet.Tables[0].Rows[0]["sitelistflag"].ToString() != "")
			{
				user_Model.sitelistflag = long.Parse(dataSet.Tables[0].Rows[0]["sitelistflag"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["sitelist"].ToString() != "")
			{
				user_Model.sitelist = long.Parse(dataSet.Tables[0].Rows[0]["sitelist"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["sitetype"].ToString() != "")
			{
				user_Model.sitetype = long.Parse(dataSet.Tables[0].Rows[0]["sitetype"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["MaxPerPage_Default"].ToString() != "")
			{
				user_Model.MaxPerPage_Default = long.Parse(dataSet.Tables[0].Rows[0]["MaxPerPage_Default"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["MaxPerPage_Content"].ToString() != "")
			{
				user_Model.MaxPerPage_Content = long.Parse(dataSet.Tables[0].Rows[0]["MaxPerPage_Content"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["MaxFileSize"].ToString() != "")
			{
				user_Model.MaxFileSize = long.Parse(dataSet.Tables[0].Rows[0]["MaxFileSize"].ToString());
			}
			user_Model.SaveUpFilesPath = dataSet.Tables[0].Rows[0]["SaveUpFilesPath"].ToString();
			user_Model.UpFileType = dataSet.Tables[0].Rows[0]["UpFileType"].ToString();
			user_Model.CharFilter = dataSet.Tables[0].Rows[0]["CharFilter"].ToString();
			user_Model.UAFilter = dataSet.Tables[0].Rows[0]["UAFilter"].ToString();
			if (dataSet.Tables[0].Rows[0]["SessionTimeout"].ToString() != "")
			{
				user_Model.SessionTimeout = long.Parse(dataSet.Tables[0].Rows[0]["SessionTimeout"].ToString());
			}
			user_Model.MailServer = dataSet.Tables[0].Rows[0]["MailServer"].ToString();
			user_Model.MailServerUserName = dataSet.Tables[0].Rows[0]["MailServerUserName"].ToString();
			user_Model.MailServerPassWord = dataSet.Tables[0].Rows[0]["MailServerPassWord"].ToString();
			user_Model.sitemoneyname = dataSet.Tables[0].Rows[0]["sitemoneyname"].ToString();
			if (dataSet.Tables[0].Rows[0]["sitespace"].ToString() != "")
			{
				user_Model.sitespace = long.Parse(dataSet.Tables[0].Rows[0]["sitespace"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["myspace"].ToString() != "")
			{
				user_Model.myspace = long.Parse(dataSet.Tables[0].Rows[0]["myspace"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["siteRight"].ToString() != "")
			{
				user_Model.siteRight = long.Parse(dataSet.Tables[0].Rows[0]["siteRight"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["isCheck"].ToString() != "")
			{
				user_Model.isCheck = long.Parse(dataSet.Tables[0].Rows[0]["isCheck"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["endtime"].ToString() != "")
			{
				user_Model.endTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["endtime"].ToString());
			}
			else
			{
				user_Model.endTime = DateTime.MinValue;
			}
			user_Model.SidTimeOut = dataSet.Tables[0].Rows[0]["SidTimeOut"].ToString();
			user_Model.lvlNumer = dataSet.Tables[0].Rows[0]["lvlNumer"].ToString();
			user_Model.lvlTimeImg = dataSet.Tables[0].Rows[0]["lvlTimeImg"].ToString();
			user_Model.lvlRegular = dataSet.Tables[0].Rows[0]["lvlRegular"].ToString();
			if (dataSet.Tables[0].Rows[0]["myBankMoney"].ToString() != "")
			{
				user_Model.myBankMoney = long.Parse(dataSet.Tables[0].Rows[0]["myBankMoney"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["myBankTime"].ToString() != "")
			{
				user_Model.myBankTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["myBankTime"].ToString());
			}
			user_Model.chuiNiu = dataSet.Tables[0].Rows[0]["chuiNiu"].ToString();
			if (dataSet.Tables[0].Rows[0]["HangBiaoShi"].ToString() != "")
			{
				user_Model.HangBiaoShi = long.Parse(dataSet.Tables[0].Rows[0]["HangBiaoShi"].ToString());
			}
			user_Model.RMB = decimal.Parse(dataSet.Tables[0].Rows[0]["rmb"].ToString());
			user_Model.expr = long.Parse(dataSet.Tables[0].Rows[0]["expr"].ToString());
			user_Model.siteVIP = dataSet.Tables[0].Rows[0]["siteVIP"].ToString();
			return user_Model;
		}
		return null;
	}

	public user_Model GetModel(long HangBiaoShi)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 userid,siteid,username,nickname,password,managerlvl,sex,age,shenggao,tizhong,xingzuo,aihao,fenfuo,zhiye,city,mobile,email,money,moneyname,moneyregular,RegTime,LastLoginIP,LastLoginTime,LoginTimes,LockUser,headimg,remark,sitename,siteimg,siteuptip,sitedowntip,siteposition,siterowremark,sitelistflag,sitelist,sitetype,MaxPerPage_Default,MaxPerPage_Content,MaxFileSize,SaveUpFilesPath,UpFileType,CharFilter,UAFilter,SessionTimeout,MailServer,MailServerUserName,MailServerPassWord,sitemoneyname,sitespace,myspace,siteRight,isCheck,endTime,SidTimeOut,lvlNumer,lvlTimeImg,lvlRegular,myBankMoney,myBankTime,chuiNiu,HangBiaoShi,version,rmb,expr,sitevip from [user] ");
		stringBuilder.Append(" where userid=@HangBiaoShi");
		SqlParameter[] array = new SqlParameter[1]
		{
			new SqlParameter("@HangBiaoShi", SqlDbType.BigInt)
		};
		array[0].Value = HangBiaoShi;
		user_Model user_Model = new user_Model();
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			if (dataSet.Tables[0].Rows[0]["userid"].ToString() != "")
			{
				user_Model.userid = long.Parse(dataSet.Tables[0].Rows[0]["userid"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["siteid"].ToString() != "")
			{
				user_Model.siteid = long.Parse(dataSet.Tables[0].Rows[0]["siteid"].ToString());
			}
			user_Model.username = dataSet.Tables[0].Rows[0]["username"].ToString();
			user_Model.nickname = dataSet.Tables[0].Rows[0]["nickname"].ToString();
			user_Model.nickname = user_Model.nickname.Replace("[", "").Replace("]", "");
			user_Model.password = dataSet.Tables[0].Rows[0]["password"].ToString();
			user_Model.managerlvl = dataSet.Tables[0].Rows[0]["managerlvl"].ToString();
			if (dataSet.Tables[0].Rows[0]["sex"].ToString() != "")
			{
				user_Model.Int64_0 = long.Parse(dataSet.Tables[0].Rows[0]["sex"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["age"].ToString() != "")
			{
				user_Model.Int64_1 = long.Parse(dataSet.Tables[0].Rows[0]["age"].ToString());
			}
			user_Model.shenggao = dataSet.Tables[0].Rows[0]["shenggao"].ToString();
			user_Model.tizhong = dataSet.Tables[0].Rows[0]["tizhong"].ToString();
			user_Model.xingzuo = dataSet.Tables[0].Rows[0]["xingzuo"].ToString();
			user_Model.aihao = dataSet.Tables[0].Rows[0]["aihao"].ToString();
			user_Model.fenfuo = dataSet.Tables[0].Rows[0]["fenfuo"].ToString();
			user_Model.zhiye = dataSet.Tables[0].Rows[0]["zhiye"].ToString();
			user_Model.city = dataSet.Tables[0].Rows[0]["city"].ToString();
			user_Model.city = user_Model.city.Replace("[", "").Replace("]", "");
			user_Model.mobile = dataSet.Tables[0].Rows[0]["mobile"].ToString();
			user_Model.email = dataSet.Tables[0].Rows[0]["email"].ToString();
			if (dataSet.Tables[0].Rows[0]["money"].ToString() != "")
			{
				user_Model.money = long.Parse(dataSet.Tables[0].Rows[0]["money"].ToString());
			}
			user_Model.moneyname = dataSet.Tables[0].Rows[0]["moneyname"].ToString();
			user_Model.moneyregular = dataSet.Tables[0].Rows[0]["moneyregular"].ToString();
			if (dataSet.Tables[0].Rows[0]["RegTime"].ToString() != "")
			{
				user_Model.RegTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["RegTime"].ToString());
			}
			user_Model.LastLoginIP = dataSet.Tables[0].Rows[0]["LastLoginIP"].ToString();
			if (dataSet.Tables[0].Rows[0]["LastLoginTime"].ToString() != "")
			{
				user_Model.LastLoginTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["LastLoginTime"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["LoginTimes"].ToString() != "")
			{
				user_Model.LoginTimes = long.Parse(dataSet.Tables[0].Rows[0]["LoginTimes"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["LockUser"].ToString() != "")
			{
				user_Model.LockUser = long.Parse(dataSet.Tables[0].Rows[0]["LockUser"].ToString());
			}
			user_Model.headimg = dataSet.Tables[0].Rows[0]["headimg"].ToString();
			user_Model.remark = dataSet.Tables[0].Rows[0]["remark"].ToString();
			user_Model.sitename = dataSet.Tables[0].Rows[0]["sitename"].ToString();
			user_Model.siteimg = dataSet.Tables[0].Rows[0]["siteimg"].ToString();
			user_Model.siteuptip = dataSet.Tables[0].Rows[0]["siteuptip"].ToString();
			user_Model.sitedowntip = dataSet.Tables[0].Rows[0]["sitedowntip"].ToString();
			user_Model.siteposition = dataSet.Tables[0].Rows[0]["siteposition"].ToString();
			user_Model.siterowremark = dataSet.Tables[0].Rows[0]["siterowremark"].ToString();
			if (dataSet.Tables[0].Rows[0]["sitelistflag"].ToString() != "")
			{
				user_Model.sitelistflag = long.Parse(dataSet.Tables[0].Rows[0]["sitelistflag"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["sitelist"].ToString() != "")
			{
				user_Model.sitelist = long.Parse(dataSet.Tables[0].Rows[0]["sitelist"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["sitetype"].ToString() != "")
			{
				user_Model.sitetype = long.Parse(dataSet.Tables[0].Rows[0]["sitetype"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["MaxPerPage_Default"].ToString() != "")
			{
				user_Model.MaxPerPage_Default = long.Parse(dataSet.Tables[0].Rows[0]["MaxPerPage_Default"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["MaxPerPage_Content"].ToString() != "")
			{
				user_Model.MaxPerPage_Content = long.Parse(dataSet.Tables[0].Rows[0]["MaxPerPage_Content"].ToString());
			}
			if (user_Model.MaxPerPage_Content < 100L)
			{
				user_Model.MaxPerPage_Content = 100L;
			}
			if (dataSet.Tables[0].Rows[0]["MaxFileSize"].ToString() != "")
			{
				user_Model.MaxFileSize = long.Parse(dataSet.Tables[0].Rows[0]["MaxFileSize"].ToString());
			}
			user_Model.SaveUpFilesPath = dataSet.Tables[0].Rows[0]["SaveUpFilesPath"].ToString();
			user_Model.UpFileType = dataSet.Tables[0].Rows[0]["UpFileType"].ToString();
			user_Model.CharFilter = dataSet.Tables[0].Rows[0]["CharFilter"].ToString();
			user_Model.UAFilter = dataSet.Tables[0].Rows[0]["UAFilter"].ToString();
			if (dataSet.Tables[0].Rows[0]["SessionTimeout"].ToString() != "")
			{
				user_Model.SessionTimeout = long.Parse(dataSet.Tables[0].Rows[0]["SessionTimeout"].ToString());
			}
			user_Model.MailServer = dataSet.Tables[0].Rows[0]["MailServer"].ToString();
			user_Model.MailServerUserName = dataSet.Tables[0].Rows[0]["MailServerUserName"].ToString();
			user_Model.MailServerPassWord = dataSet.Tables[0].Rows[0]["MailServerPassWord"].ToString();
			user_Model.sitemoneyname = dataSet.Tables[0].Rows[0]["sitemoneyname"].ToString();
			if (dataSet.Tables[0].Rows[0]["sitespace"].ToString() != "")
			{
				user_Model.sitespace = long.Parse(dataSet.Tables[0].Rows[0]["sitespace"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["myspace"].ToString() != "")
			{
				user_Model.myspace = long.Parse(dataSet.Tables[0].Rows[0]["myspace"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["siteRight"].ToString() != "")
			{
				user_Model.siteRight = long.Parse(dataSet.Tables[0].Rows[0]["siteRight"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["isCheck"].ToString() != "")
			{
				user_Model.isCheck = long.Parse(dataSet.Tables[0].Rows[0]["isCheck"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["endTime"].ToString() != "")
			{
				user_Model.endTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["endTime"].ToString());
			}
			user_Model.SidTimeOut = dataSet.Tables[0].Rows[0]["SidTimeOut"].ToString();
			user_Model.lvlNumer = dataSet.Tables[0].Rows[0]["lvlNumer"].ToString();
			user_Model.lvlTimeImg = dataSet.Tables[0].Rows[0]["lvlTimeImg"].ToString();
			user_Model.lvlRegular = dataSet.Tables[0].Rows[0]["lvlRegular"].ToString();
			if (dataSet.Tables[0].Rows[0]["myBankMoney"].ToString() != "")
			{
				user_Model.myBankMoney = long.Parse(dataSet.Tables[0].Rows[0]["myBankMoney"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["myBankTime"].ToString() != "")
			{
				user_Model.myBankTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["myBankTime"].ToString());
			}
			user_Model.chuiNiu = dataSet.Tables[0].Rows[0]["chuiNiu"].ToString();
			if (dataSet.Tables[0].Rows[0]["HangBiaoShi"].ToString() != "")
			{
				user_Model.HangBiaoShi = long.Parse(dataSet.Tables[0].Rows[0]["HangBiaoShi"].ToString());
			}
			user_Model.Version = dataSet.Tables[0].Rows[0]["version"].ToString();
			user_Model.RMB = decimal.Parse(dataSet.Tables[0].Rows[0]["rmb"].ToString());
			user_Model.expr = long.Parse(dataSet.Tables[0].Rows[0]["expr"].ToString());
			user_Model.siteVIP = dataSet.Tables[0].Rows[0]["siteVIP"].ToString();
			user_Model.isCheckSite = user_Model.isCheck;
			user_Model.isCheck = WapTool.GetIsCheck(user_Model.isCheck, user_Model.Version);
			return user_Model;
		}
		return null;
	}

	public user_Model GetPassFormID(long siteid, long userid)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 password,lockuser,userid,username,MailServerUserName from [user] ");
		stringBuilder.Append(" where userid=@userid and siteid=@siteid");
		SqlParameter[] array = new SqlParameter[2]
		{
			new SqlParameter("@userid", SqlDbType.BigInt),
			new SqlParameter("@siteid", SqlDbType.BigInt)
		};
		array[0].Value = userid;
		array[1].Value = siteid;
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			user_Model user_Model = new user_Model();
			user_Model.password = dataSet.Tables[0].Rows[0]["password"].ToString();
			user_Model.LockUser = long.Parse(dataSet.Tables[0].Rows[0]["LockUser"].ToString());
			user_Model.userid = long.Parse(dataSet.Tables[0].Rows[0]["userid"].ToString());
			user_Model.username = dataSet.Tables[0].Rows[0]["username"].ToString();
			user_Model.MailServerUserName = dataSet.Tables[0].Rows[0]["MailServerUserName"].ToString();
			return user_Model;
		}
		return null;
	}

	public user_Model GetPassFormUsername(long siteid, string username)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 password,lockuser,userid,username,MailServerUserName from [user] ");
		stringBuilder.Append(" where (username=@username1 or username=@username2) and siteid=@siteid order by userid desc");
		SqlParameter[] array = new SqlParameter[3]
		{
			new SqlParameter("@username1", SqlDbType.VarChar),
			new SqlParameter("@username2", SqlDbType.VarChar),
			new SqlParameter("@siteid", SqlDbType.BigInt)
		};
		array[0].Value = username;
		array[1].Value = siteid + username;
		array[2].Value = siteid;
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			user_Model user_Model = new user_Model();
			user_Model.password = dataSet.Tables[0].Rows[0]["password"].ToString();
			user_Model.LockUser = long.Parse(dataSet.Tables[0].Rows[0]["LockUser"].ToString());
			user_Model.userid = long.Parse(dataSet.Tables[0].Rows[0]["userid"].ToString());
			user_Model.username = dataSet.Tables[0].Rows[0]["username"].ToString();
			user_Model.MailServerUserName = dataSet.Tables[0].Rows[0]["MailServerUserName"].ToString();
			return user_Model;
		}
		return null;
	}

	public void SetUserSID(long siteid, long userid, string string_2)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("update [user] set SidTimeOut=@sid ");
		stringBuilder.Append(" where siteid=@siteid and userid=@userid");
		SqlParameter[] array = new SqlParameter[3]
		{
			new SqlParameter("@sid", SqlDbType.VarChar),
			new SqlParameter("@siteid", SqlDbType.BigInt),
			new SqlParameter("@userid", SqlDbType.BigInt)
		};
		array[0].Value = string_2;
		array[1].Value = siteid;
		array[2].Value = userid;
		DbHelperSQL.ExecuteNonQuery(string_1, CommandType.Text, stringBuilder.ToString(), array);
	}

	public int GetListCount(string strWhere)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select count(userid)");
		stringBuilder.Append(" FROM [user] ");
		if (strWhere.Trim() != "")
		{
			stringBuilder.Append(" where " + strWhere);
		}
		return int.Parse(DbHelperSQL.ExecuteScalar(string_1, CommandType.Text, stringBuilder.ToString()).ToString());
	}

	public long UpdateSQL(string strSQL)
	{
		return DbHelperSQL.ExecuteQuery(string_1, strSQL);
	}

	public DataSet GetList(string strWhere)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select userid,siteid,username,nickname,password,managerlvl,sex,age,shenggao,tizhong,xingzuo,aihao,fenfuo,zhiye,city,mobile,email,money,moneyname,moneyregular,RegTime,LastLoginIP,LastLoginTime,LoginTimes,LockUser,headimg,remark,sitename,siteimg,siteuptip,sitedowntip,siteposition,siterowremark,sitelistflag,sitelist,sitetype,MaxPerPage_Default,MaxPerPage_Content,MaxFileSize,SaveUpFilesPath,UpFileType,CharFilter,UAFilter,SessionTimeout,MailServer,MailServerUserName,MailServerPassWord,sitemoneyname,sitespace,myspace,siteRight,isCheck,endTime,SidTimeOut,lvlNumer,lvlTimeImg,lvlRegular,myBankMoney,myBankTime,chuiNiu,version ");
		stringBuilder.Append(" FROM [user] ");
		if (strWhere.Trim() != "")
		{
			stringBuilder.Append(" where " + strWhere);
		}
		return DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString());
	}

	public DataSet GetList(int Top, string strWhere, string filedOrder)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select ");
		if (Top > 0)
		{
			stringBuilder.Append(" top " + Top);
		}
		stringBuilder.Append(" userid,siteid,username,nickname,password,managerlvl,sex,age,shenggao,tizhong,xingzuo,aihao,fenfuo,zhiye,city,mobile,email,money,moneyname,moneyregular,RegTime,LastLoginIP,LastLoginTime,LoginTimes,LockUser,headimg,remark,sitename,siteimg,siteuptip,sitedowntip,siteposition,siterowremark,sitelistflag,sitelist,sitetype,MaxPerPage_Default,MaxPerPage_Content,MaxFileSize,SaveUpFilesPath,UpFileType,CharFilter,UAFilter,SessionTimeout,MailServer,MailServerUserName,MailServerPassWord,sitemoneyname,sitespace,myspace,siteRight,isCheck,endTime,SidTimeOut,lvlNumer,lvlTimeImg,lvlRegular,myBankMoney,myBankTime,chuiNiu,version ");
		stringBuilder.Append(" FROM [user] ");
		if (strWhere.Trim() != "")
		{
			stringBuilder.Append(" where " + strWhere);
		}
		if (filedOrder.Trim() != "")
		{
			stringBuilder.Append(" order by " + filedOrder);
		}
		return DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString());
	}

	public DataSet GetList(int PageSize, int PageIndex, int OrderType, string strWhere)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("exec UP_GetRecordByPage @tblName,@fldName,@PageSize,@PageIndex,@IsReCount,@OrderType,@strWhere");
		SqlParameter[] array = new SqlParameter[7]
		{
			new SqlParameter("@tblName", SqlDbType.VarChar),
			new SqlParameter("@fldName", SqlDbType.VarChar),
			new SqlParameter("@PageSize", SqlDbType.Int),
			new SqlParameter("@PageIndex", SqlDbType.Int),
			new SqlParameter("@IsReCount", SqlDbType.Int),
			new SqlParameter("@OrderType", SqlDbType.Bit),
			new SqlParameter("@strWhere", SqlDbType.VarChar)
		};
		array[0].Value = "[user]";
		array[1].Value = "userid";
		array[2].Value = PageSize;
		array[3].Value = PageIndex;
		array[4].Value = 0;
		array[5].Value = OrderType;
		array[6].Value = strWhere;
		return DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
	}

	public DataSet GetList(int PageSize, int PageIndex, string strWhere, string ShowFldName, string OrderfldName, int TotalCount, int OrderType)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("exec UP_GetRecordByPageOrder @tblName,@fldName,@OrderfldName,@StatfldName,@TotalCount,@PageSize,@PageIndex,@IsReCount,@OrderType,@strWhere");
		SqlParameter[] array = new SqlParameter[10]
		{
			new SqlParameter("@tblName", SqlDbType.VarChar),
			new SqlParameter("@fldName", SqlDbType.VarChar),
			new SqlParameter("@OrderfldName", SqlDbType.VarChar),
			new SqlParameter("@StatfldName", SqlDbType.VarChar),
			new SqlParameter("@TotalCount", SqlDbType.Int),
			new SqlParameter("@PageSize", SqlDbType.Int),
			new SqlParameter("@PageIndex", SqlDbType.Int),
			new SqlParameter("@IsReCount", SqlDbType.Int),
			new SqlParameter("@OrderType", SqlDbType.Bit),
			new SqlParameter("@strWhere", SqlDbType.VarChar)
		};
		array[0].Value = "[user]";
		array[1].Value = ShowFldName;
		array[2].Value = OrderfldName;
		array[3].Value = "";
		array[4].Value = TotalCount;
		array[5].Value = PageSize;
		array[6].Value = PageIndex;
		array[7].Value = 0;
		array[8].Value = OrderType;
		array[9].Value = strWhere;
		return DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
	}

	public DataSet GetViewList(int PageSize, int PageIndex, string strWhere, string ShowFldName, string OrderfldName, int TotalCount, int OrderType)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("exec UP_GetRecordByPageOrder @tblName,@fldName,@OrderfldName,@StatfldName,@TotalCount,@PageSize,@PageIndex,@IsReCount,@OrderType,@strWhere");
		SqlParameter[] array = new SqlParameter[10]
		{
			new SqlParameter("@tblName", SqlDbType.VarChar),
			new SqlParameter("@fldName", SqlDbType.VarChar),
			new SqlParameter("@OrderfldName", SqlDbType.VarChar),
			new SqlParameter("@StatfldName", SqlDbType.VarChar),
			new SqlParameter("@TotalCount", SqlDbType.Int),
			new SqlParameter("@PageSize", SqlDbType.Int),
			new SqlParameter("@PageIndex", SqlDbType.Int),
			new SqlParameter("@IsReCount", SqlDbType.Int),
			new SqlParameter("@OrderType", SqlDbType.Bit),
			new SqlParameter("@strWhere", SqlDbType.VarChar)
		};
		array[0].Value = "[UserView]";
		array[1].Value = ShowFldName;
		array[2].Value = OrderfldName;
		array[3].Value = "";
		array[4].Value = TotalCount;
		array[5].Value = PageSize;
		array[6].Value = PageIndex;
		array[7].Value = 0;
		array[8].Value = OrderType;
		array[9].Value = strWhere;
		return DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
	}

	public int UserDelAll(string strUserID)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("exec UserDelAll @UserID");
		SqlParameter[] array = new SqlParameter[1]
		{
			new SqlParameter("@UserID", SqlDbType.VarChar)
		};
		array[0].Value = strUserID;
		return DbHelperSQL.ExecuteNonQuery(string_1, CommandType.Text, stringBuilder.ToString(), array);
	}

	public int UserDelAllPeople(string strUserID, string Managerlvl)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("exec UserDelAllPeople @UserID,@Managerlvl");
		SqlParameter[] array = new SqlParameter[2]
		{
			new SqlParameter("@UserID", SqlDbType.VarChar),
			new SqlParameter("@Managerlvl", SqlDbType.VarChar)
		};
		array[0].Value = strUserID;
		array[1].Value = Managerlvl;
		DbHelperSQL.ExecuteNonQuery(string_1, CommandType.Text, "delete from [wap_Oauth] where userid=" + strUserID);
		return DbHelperSQL.ExecuteNonQuery(string_1, CommandType.Text, stringBuilder.ToString(), array);
	}

	public void DelOnline(string siteid, string delTime)
	{
		if (!(delTime == "0"))
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("exec DelOnline @siteid,@delTime");
			SqlParameter[] array = new SqlParameter[2]
			{
				new SqlParameter("@siteid", SqlDbType.VarChar),
				new SqlParameter("@delTime", SqlDbType.VarChar)
			};
			array[0].Value = siteid;
			array[1].Value = delTime;
			DbHelperSQL.ExecuteNonQuery(string_1, CommandType.Text, stringBuilder.ToString(), array);
		}
	}

	public user_Model getSiteInfo(string siteid)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 userid,siteid,username,nickname,managerlvl,mobile,email,moneyregular,lockuser,sitename,siteimg,siteuptip,sitedowntip,siteposition,siterowremark,SiteListFlag,sitelist,sitetype,MaxPerpage_default,MaxPerpage_content,MaxFileSize,UpFileType,CharFilter,UAFilter,MailServer,MailServerPassWord,sitemoneyname,siteright,DATEDIFF(d, GETDATE(), endtime) AS isValid,endTime,lvlNumer,lvlTimeImg,lvlRegular,SaveupfilesPath,SidTimeOut,version,sitespace,myspace,sitevip,isCheck from [user] ");
		stringBuilder.Append(" where userid=siteid and userid=@siteid");
		SqlParameter[] array = new SqlParameter[1]
		{
			new SqlParameter("@siteid", SqlDbType.BigInt)
		};
		array[0].Value = siteid;
		user_Model user_Model = new user_Model();
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			user_Model.userid = long.Parse(dataSet.Tables[0].Rows[0]["userid"].ToString());
			user_Model.siteid = long.Parse(dataSet.Tables[0].Rows[0]["siteid"].ToString());
			user_Model.username = dataSet.Tables[0].Rows[0]["username"].ToString();
			user_Model.nickname = dataSet.Tables[0].Rows[0]["nickname"].ToString();
			user_Model.nickname = user_Model.nickname.Replace("[", "").Replace("]", "");
			user_Model.managerlvl = dataSet.Tables[0].Rows[0]["managerlvl"].ToString();
			user_Model.mobile = dataSet.Tables[0].Rows[0]["mobile"].ToString();
			user_Model.email = dataSet.Tables[0].Rows[0]["email"].ToString();
			user_Model.moneyregular = dataSet.Tables[0].Rows[0]["moneyregular"].ToString();
			user_Model.LockUser = long.Parse(dataSet.Tables[0].Rows[0]["LockUser"].ToString());
			user_Model.sitename = dataSet.Tables[0].Rows[0]["sitename"].ToString();
			user_Model.siteimg = dataSet.Tables[0].Rows[0]["siteimg"].ToString();
			user_Model.siteuptip = dataSet.Tables[0].Rows[0]["siteuptip"].ToString();
			user_Model.sitedowntip = dataSet.Tables[0].Rows[0]["sitedowntip"].ToString();
			user_Model.siteposition = dataSet.Tables[0].Rows[0]["siteposition"].ToString();
			user_Model.siterowremark = dataSet.Tables[0].Rows[0]["siterowremark"].ToString();
			user_Model.sitelistflag = long.Parse(dataSet.Tables[0].Rows[0]["SiteListFlag"].ToString());
			user_Model.sitelist = long.Parse(dataSet.Tables[0].Rows[0]["sitelist"].ToString());
			user_Model.sitetype = long.Parse(dataSet.Tables[0].Rows[0]["sitetype"].ToString());
			if (dataSet.Tables[0].Rows[0]["MaxPerpage_default"].ToString() != "")
			{
				user_Model.MaxPerPage_Default = long.Parse(dataSet.Tables[0].Rows[0]["MaxPerpage_default"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["MaxPerpage_content"].ToString() != "")
			{
				user_Model.MaxPerPage_Content = long.Parse(dataSet.Tables[0].Rows[0]["MaxPerpage_content"].ToString());
			}
			if (user_Model.MaxPerPage_Content < 100L)
			{
				user_Model.MaxPerPage_Content = 100L;
			}
			user_Model.MaxFileSize = long.Parse(dataSet.Tables[0].Rows[0]["MaxFileSize"].ToString());
			user_Model.UpFileType = dataSet.Tables[0].Rows[0]["UpFileType"].ToString();
			user_Model.CharFilter = dataSet.Tables[0].Rows[0]["CharFilter"].ToString();
			user_Model.UAFilter = dataSet.Tables[0].Rows[0]["UAFilter"].ToString();
			user_Model.MailServer = dataSet.Tables[0].Rows[0]["MailServer"].ToString();
			user_Model.MailServerPassWord = dataSet.Tables[0].Rows[0]["MailServerPassWord"].ToString();
			user_Model.sitemoneyname = dataSet.Tables[0].Rows[0]["sitemoneyname"].ToString();
			user_Model.siteRight = long.Parse(dataSet.Tables[0].Rows[0]["siteright"].ToString());
			if (dataSet.Tables[0].Rows[0]["isValid"].ToString() != "")
			{
				user_Model.isValid = long.Parse(dataSet.Tables[0].Rows[0]["isValid"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["endTime"].ToString() != "")
			{
				user_Model.endTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["endTime"].ToString());
			}
			user_Model.lvlNumer = dataSet.Tables[0].Rows[0]["lvlNumer"].ToString();
			user_Model.lvlTimeImg = dataSet.Tables[0].Rows[0]["lvlTimeImg"].ToString();
			user_Model.lvlRegular = dataSet.Tables[0].Rows[0]["lvlRegular"].ToString();
			user_Model.SaveUpFilesPath = dataSet.Tables[0].Rows[0]["SaveUpFilesPath"].ToString();
			user_Model.SidTimeOut = dataSet.Tables[0].Rows[0]["SidTimeOut"].ToString();
			user_Model.Version = dataSet.Tables[0].Rows[0]["Version"].ToString();
			user_Model.sitespace = long.Parse(dataSet.Tables[0].Rows[0]["sitespace"].ToString());
			user_Model.myspace = long.Parse(dataSet.Tables[0].Rows[0]["myspace"].ToString());
			user_Model.siteVIP = dataSet.Tables[0].Rows[0]["siteVIP"].ToString();
			if (dataSet.Tables[0].Rows[0]["isCheck"].ToString() != "")
			{
				user_Model.isCheck = long.Parse(dataSet.Tables[0].Rows[0]["isCheck"].ToString());
			}
			else
			{
				user_Model.isCheck = 0L;
			}
			user_Model.isCheckSite = user_Model.isCheck;
			user_Model.isCheck = WapTool.GetIsCheck(user_Model.isCheck, user_Model.Version);
			return user_Model;
		}
		return null;
	}

	public user_Model getUserInfo(string userid, string siteid)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 * from UserVO_View ");
		stringBuilder.Append(" where siteid=@siteid and userid=@userid");
		SqlParameter[] array = new SqlParameter[2]
		{
			new SqlParameter("@siteid", SqlDbType.BigInt),
			new SqlParameter("@userid", SqlDbType.BigInt)
		};
		array[0].Value = siteid;
		array[1].Value = userid;
		user_Model user_Model = new user_Model();
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			user_Model.userid = long.Parse(userid);
			user_Model.siteid = long.Parse(siteid);
			user_Model.username = dataSet.Tables[0].Rows[0]["username"].ToString();
			user_Model.nickname = dataSet.Tables[0].Rows[0]["nickname"].ToString();
			user_Model.nickname = user_Model.nickname.Replace("[", "").Replace("]", "").Replace("'", "’")
				.Replace("<", "《")
				.Replace(">", "》");
			user_Model.password = dataSet.Tables[0].Rows[0]["password"].ToString();
			user_Model.managerlvl = dataSet.Tables[0].Rows[0]["managerlvl"].ToString();
			if (dataSet.Tables[0].Rows[0]["sex"].ToString() != "")
			{
				user_Model.Int64_0 = long.Parse(dataSet.Tables[0].Rows[0]["sex"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["age"].ToString() != "")
			{
				user_Model.Int64_1 = long.Parse(dataSet.Tables[0].Rows[0]["age"].ToString());
			}
			user_Model.shenggao = dataSet.Tables[0].Rows[0]["shenggao"].ToString();
			user_Model.tizhong = dataSet.Tables[0].Rows[0]["tizhong"].ToString();
			user_Model.xingzuo = dataSet.Tables[0].Rows[0]["xingzuo"].ToString();
			user_Model.aihao = dataSet.Tables[0].Rows[0]["aihao"].ToString();
			user_Model.fenfuo = dataSet.Tables[0].Rows[0]["fenfuo"].ToString();
			user_Model.zhiye = dataSet.Tables[0].Rows[0]["zhiye"].ToString();
			user_Model.city = dataSet.Tables[0].Rows[0]["city"].ToString();
			user_Model.city = user_Model.city.Replace("[", "").Replace("]", "");
			user_Model.mobile = dataSet.Tables[0].Rows[0]["mobile"].ToString();
			user_Model.email = dataSet.Tables[0].Rows[0]["email"].ToString();
			user_Model.money = long.Parse(dataSet.Tables[0].Rows[0]["money"].ToString());
			user_Model.moneyname = dataSet.Tables[0].Rows[0]["moneyname"].ToString();
			try
			{
				if (dataSet.Tables[0].Rows[0]["regtime"].ToString() != "")
				{
					user_Model.RegTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["regtime"].ToString());
				}
				if (dataSet.Tables[0].Rows[0]["lastlogintime"].ToString() != "")
				{
					user_Model.LastLoginTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["lastlogintime"].ToString());
				}
			}
			catch (Exception)
			{
			}
			user_Model.LastLoginIP = dataSet.Tables[0].Rows[0]["lastloginip"].ToString();
			user_Model.LoginTimes = long.Parse(dataSet.Tables[0].Rows[0]["logintimes"].ToString());
			user_Model.LockUser = long.Parse(dataSet.Tables[0].Rows[0]["lockuser"].ToString());
			user_Model.headimg = dataSet.Tables[0].Rows[0]["headimg"].ToString();
			user_Model.remark = dataSet.Tables[0].Rows[0]["remark"].ToString();
			if (dataSet.Tables[0].Rows[0]["endtime"].ToString() != "")
			{
				user_Model.endTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["endtime"].ToString());
			}
			else
			{
				user_Model.endTime = DateTime.MinValue;
			}
			if (dataSet.Tables[0].Rows[0]["sessiontimeout"].ToString() != "")
			{
				user_Model.SessionTimeout = long.Parse(dataSet.Tables[0].Rows[0]["sessiontimeout"].ToString());
			}
			user_Model.SidTimeOut = dataSet.Tables[0].Rows[0]["SidTimeOut"].ToString();
			if (dataSet.Tables[0].Rows[0]["mybankmoney"].ToString() != "")
			{
				user_Model.myBankMoney = long.Parse(dataSet.Tables[0].Rows[0]["mybankmoney"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["mybanktime"].ToString() != "")
			{
				user_Model.myBankTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["mybanktime"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["dtimes"].ToString() != "")
			{
				user_Model.dtimes = long.Parse(dataSet.Tables[0].Rows[0]["dtimes"].ToString());
			}
			user_Model.expr = long.Parse(dataSet.Tables[0].Rows[0]["expR"].ToString());
			user_Model.MaxPerPage_Content = long.Parse(dataSet.Tables[0].Rows[0]["MaxPerPage_Content"].ToString());
			if (user_Model.MaxPerPage_Content < 100L)
			{
				user_Model.MaxPerPage_Content = 100L;
			}
			user_Model.MailServerUserName = dataSet.Tables[0].Rows[0]["MailServerUserName"].ToString();
			user_Model.idname = dataSet.Tables[0].Rows[0]["idname"].ToString();
			user_Model.isonline = dataSet.Tables[0].Rows[0]["isonline"].ToString();
			user_Model.RMB = decimal.Parse(dataSet.Tables[0].Rows[0]["RMB"].ToString());
			if (dataSet.Tables[0].Rows[0]["zoneCount"].ToString() != "")
			{
				user_Model.zoneCount = long.Parse(dataSet.Tables[0].Rows[0]["zoneCount"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["bbsCount"].ToString() != "")
			{
				user_Model.bbsCount = long.Parse(dataSet.Tables[0].Rows[0]["bbsCount"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["bbsReCount"].ToString() != "")
			{
				user_Model.bbsReCount = long.Parse(dataSet.Tables[0].Rows[0]["bbsReCount"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["actionTime"].ToString() != "")
			{
				user_Model.actionTime = DateTime.Parse(dataSet.Tables[0].Rows[0]["actionTime"].ToString());
			}
			if (dataSet.Tables[0].Rows[0]["actionState"].ToString() != "")
			{
				user_Model.actionState = dataSet.Tables[0].Rows[0]["actionState"].ToString();
			}
			else
			{
				user_Model.actionState = "0";
			}
			if (dataSet.Tables[0].Rows[0]["TJCount"].ToString() != "")
			{
				user_Model.TJCount = long.Parse(dataSet.Tables[0].Rows[0]["TJCount"].ToString());
			}
			return user_Model;
		}
		return null;
	}

	public StringBuilder getSiteCSS(string siteid, string string_2)
	{
		StringBuilder stringBuilder = new StringBuilder();
		StringBuilder stringBuilder2 = new StringBuilder();
		string_2 = string_2.Trim();
		DataSet dataSet;
		if (string_2 == "" || string_2 == "0")
		{
			stringBuilder.Append("select  top 1 style from [wap2_style] ");
			stringBuilder.Append(" where siteid=@siteid and style_type=1 and issystem<>1 ");
			SqlParameter[] array = new SqlParameter[1]
			{
				new SqlParameter("@siteid", SqlDbType.BigInt)
			};
			array[0].Value = siteid;
			dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		}
		else
		{
			stringBuilder.Append("select  top 1 style from [wap2_style] ");
			stringBuilder.Append(" where siteid=@siteid and id=@id  ");
			SqlParameter[] array = new SqlParameter[2]
			{
				new SqlParameter("@siteid", SqlDbType.BigInt),
				new SqlParameter("@id", SqlDbType.BigInt)
			};
			array[0].Value = siteid;
			array[1].Value = string_2;
			dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		}
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			stringBuilder2.Append(dataSet.Tables[0].Rows[0]["style"].ToString());
		}
		else
		{
			stringBuilder2.Append("");
		}
		return stringBuilder2;
	}

	public bool isHasExistUsername(string username)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 userid  from [user] where username=@username");
		SqlParameter[] array = new SqlParameter[1]
		{
			new SqlParameter("@username", SqlDbType.NVarChar)
		};
		array[0].Value = username;
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			return true;
		}
		return false;
	}

	public string isExistFromEmail(string siteid, string totype, string toname, string toemail)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 userid  from [user] where siteid=@siteid and email=@toemail");
		if (totype == "0")
		{
			stringBuilder.Append(" and userid=@toname ");
		}
		else
		{
			stringBuilder.Append(" and username=@toname ");
		}
		SqlParameter[] array = new SqlParameter[3]
		{
			new SqlParameter("@siteid", SqlDbType.BigInt),
			new SqlParameter("@toemail", SqlDbType.NVarChar),
			new SqlParameter("@toname", SqlDbType.NVarChar)
		};
		array[0].Value = siteid;
		array[1].Value = toemail;
		array[2].Value = toname;
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			Random random = new Random();
			string text = PubConstant.smethod_2(toname + random.Next(5000, 99999));
			string text2 = "";
			text2 = ((!(totype == "0")) ? (" username='" + toname + "'") : (" userid=" + long.Parse(toname)));
			UpdateSQL("update [user] set SidTimeOut='" + text + "' where " + text2);
			return text;
		}
		return "";
	}

	public bool isExistFromSid(string siteid, string string_2, string email)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 userid  from [user] where siteid=@siteid and SidTimeOut=@SidTimeOut and email=@email");
		SqlParameter[] array = new SqlParameter[3]
		{
			new SqlParameter("@siteid", SqlDbType.BigInt),
			new SqlParameter("@SidTimeOut", SqlDbType.NVarChar),
			new SqlParameter("@email", SqlDbType.NVarChar)
		};
		array[0].Value = siteid;
		array[1].Value = string_2;
		array[2].Value = email;
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			return true;
		}
		return false;
	}

	public bool isHasExistNickname(string siteid, string nickname)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 userid  from [user] where siteid=@siteid and nickname=@nickname");
		SqlParameter[] array = new SqlParameter[2]
		{
			new SqlParameter("@siteid", SqlDbType.BigInt),
			new SqlParameter("@nickname", SqlDbType.NVarChar)
		};
		array[0].Value = siteid;
		array[1].Value = nickname;
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			return true;
		}
		return false;
	}

	public bool isHasExistEmail(string siteid, string email)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 userid  from [user] where siteid=@siteid and email=@email");
		SqlParameter[] array = new SqlParameter[2]
		{
			new SqlParameter("@siteid", SqlDbType.BigInt),
			new SqlParameter("@email", SqlDbType.NVarChar)
		};
		array[0].Value = siteid;
		array[1].Value = email;
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			return true;
		}
		return false;
	}

	public string isHasExistUserName(string username)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 userid  from [user] where username=@username ");
		SqlParameter[] array = new SqlParameter[1]
		{
			new SqlParameter("@username", SqlDbType.NVarChar)
		};
		array[0].Value = username;
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			return dataSet.Tables[0].Rows[0]["userid"].ToString();
		}
		return "0";
	}

	public string GetNicknameFromID(string siteid, string touserid)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 nickname  from [user] where siteid=@siteid and userid=@userid");
		SqlParameter[] array = new SqlParameter[2]
		{
			new SqlParameter("@siteid", SqlDbType.BigInt),
			new SqlParameter("@userid", SqlDbType.BigInt)
		};
		array[0].Value = siteid;
		array[1].Value = touserid;
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			return dataSet.Tables[0].Rows[0]["nickname"].ToString().Replace("[", "").Replace("]", "");
		}
		return "";
	}

	public string GetNicknameFromUserName(string siteid, string tousername)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 nickname  from [user] where siteid=@siteid and username=@username");
		SqlParameter[] array = new SqlParameter[2]
		{
			new SqlParameter("@siteid", SqlDbType.BigInt),
			new SqlParameter("@username", SqlDbType.BigInt)
		};
		array[0].Value = siteid;
		array[1].Value = tousername;
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			return dataSet.Tables[0].Rows[0]["nickname"].ToString().Replace("[", "").Replace("]", "");
		}
		return "";
	}

	public bool isLogin(string siteid, string userid, string string_2)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select  top 1 fid  from fcount where fip=@ip and fuserid=@siteid and userid=@userid");
		SqlParameter[] array = new SqlParameter[3]
		{
			new SqlParameter("@ip", SqlDbType.NVarChar),
			new SqlParameter("@siteid", SqlDbType.BigInt),
			new SqlParameter("@userid", SqlDbType.BigInt)
		};
		array[0].Value = string_2;
		array[1].Value = siteid;
		array[2].Value = userid;
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet.Tables[0].Rows.Count > 0)
		{
			return true;
		}
		return false;
	}

	public long userActionTime(string siteid, string userid, string string_2, string timetype)
	{
		StringBuilder stringBuilder = new StringBuilder();
		if (timetype == "n")
		{
			stringBuilder.Append("select DATEDIFF(n,lastLogintime,GETDATE()) as n  from [user] where siteid=@siteid and userid=@userid");
			SqlParameter[] array = new SqlParameter[2]
			{
				new SqlParameter("@siteid", SqlDbType.BigInt),
				new SqlParameter("@userid", SqlDbType.BigInt)
			};
			array[0].Value = siteid;
			array[1].Value = userid;
			DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
			if (dataSet.Tables[0].Rows.Count > 0)
			{
				return long.Parse(dataSet.Tables[0].Rows[0]["n"].ToString());
			}
		}
		else if (timetype == "ss")
		{
			stringBuilder.Append("select top 1 DATEDIFF(ss,ftime,GETDATE()) as n  from [fcount] where fip=@fip and fuserid=@siteid and userid=@userid");
			SqlParameter[] array = new SqlParameter[3]
			{
				new SqlParameter("@siteid", SqlDbType.BigInt),
				new SqlParameter("@userid", SqlDbType.BigInt),
				new SqlParameter("@fip", SqlDbType.NVarChar)
			};
			array[0].Value = siteid;
			array[1].Value = userid;
			array[2].Value = string_2;
			DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
			if (dataSet.Tables[0].Rows.Count > 0)
			{
				return long.Parse(dataSet.Tables[0].Rows[0]["n"].ToString());
			}
		}
		return 0L;
	}

	public List<user_Model> GetBBSadmin(string string_2)
	{
		StringBuilder stringBuilder = new StringBuilder();
		List<user_Model> list = new List<user_Model>();
		stringBuilder.Append("select userid,username,nickname,managerlvl,sex,money,expr,city,headimg,remark from [user] where ");
		stringBuilder.Append(string_2);
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString());
		if (dataSet == null || dataSet.Tables.Count <= 0 || dataSet.Tables[0].Rows.Count <= 0)
		{
			return null;
		}
		foreach (DataRow row in dataSet.Tables[0].Rows)
		{
			user_Model user_Model = new user_Model();
			user_Model.userid = long.Parse(row["userid"].ToString());
			user_Model.username = row["username"].ToString();
			user_Model.nickname = row["nickname"].ToString();
			user_Model.nickname = user_Model.nickname.Replace("[", "").Replace("]", "");
			user_Model.managerlvl = row["managerlvl"].ToString();
			if (row["sex"].ToString() != "")
			{
				user_Model.Int64_0 = long.Parse(row["sex"].ToString());
			}
			user_Model.money = long.Parse(row["money"].ToString());
			user_Model.expr = long.Parse(row["expr"].ToString());
			user_Model.city = row["city"].ToString();
			user_Model.city = user_Model.city.Replace("[", "").Replace("]", "");
			user_Model.headimg = row["headimg"].ToString();
			user_Model.remark = row["remark"].ToString();
			list.Add(user_Model);
		}
		return list;
	}

	public List<user_Model> GetUserListVo(string strWhere)
	{
		StringBuilder stringBuilder = new StringBuilder();
		List<user_Model> list = new List<user_Model>();
		stringBuilder.Append("select * from userVO_view where ");
		stringBuilder.Append(strWhere);
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString());
		if (dataSet == null || dataSet.Tables.Count <= 0 || dataSet.Tables[0].Rows.Count <= 0)
		{
			return null;
		}
		foreach (DataRow row in dataSet.Tables[0].Rows)
		{
			user_Model user_Model = new user_Model();
			user_Model.userid = long.Parse(row["userid"].ToString());
			user_Model.username = row["username"].ToString();
			user_Model.nickname = row["nickname"].ToString();
			user_Model.nickname = user_Model.nickname.Replace("[", "").Replace("]", "");
			user_Model.managerlvl = row["managerlvl"].ToString();
			if (row["sex"].ToString() != "")
			{
				user_Model.Int64_0 = long.Parse(row["sex"].ToString());
			}
			user_Model.money = long.Parse(row["money"].ToString());
			user_Model.expr = long.Parse(row["expr"].ToString());
			user_Model.city = row["city"].ToString();
			user_Model.city = user_Model.city.Replace("[", "").Replace("]", "");
			user_Model.isonline = row["isonline"].ToString();
			user_Model.headimg = row["headimg"].ToString();
			user_Model.remark = row["remark"].ToString();
			user_Model.idname = row["idname"].ToString();
			if (row["bbsCount"].ToString() != "")
			{
				user_Model.bbsCount = long.Parse(row["bbsCount"].ToString());
			}
			if (row["bbsReCount"].ToString() != "")
			{
				user_Model.bbsReCount = long.Parse(row["bbsReCount"].ToString());
			}
			if (row["zoneCount"].ToString() != "")
			{
				user_Model.zoneCount = long.Parse(row["zoneCount"].ToString());
			}
			if (row["TJCount"].ToString() != "")
			{
				user_Model.TJCount = long.Parse(row["TJCount"].ToString());
			}
			list.Add(user_Model);
		}
		return list;
	}

	public List<user_Model> GetUserListVo(long PageSize, long PageIndex, string strWhere, string ShowFldName, string OrderfldName, long TotalCount, long OrderType)
	{
		StringBuilder stringBuilder = new StringBuilder();
		List<user_Model> list = new List<user_Model>();
		stringBuilder.Append("exec UP_GetRecordByPageOrder @tblName,@fldName,@OrderfldName,@StatfldName,@TotalCount,@PageSize,@PageIndex,@IsReCount,@OrderType,@strWhere");
		SqlParameter[] array = new SqlParameter[10]
		{
			new SqlParameter("@tblName", SqlDbType.VarChar),
			new SqlParameter("@fldName", SqlDbType.VarChar),
			new SqlParameter("@OrderfldName", SqlDbType.VarChar),
			new SqlParameter("@StatfldName", SqlDbType.VarChar),
			new SqlParameter("@TotalCount", SqlDbType.Int),
			new SqlParameter("@PageSize", SqlDbType.Int),
			new SqlParameter("@PageIndex", SqlDbType.Int),
			new SqlParameter("@IsReCount", SqlDbType.Int),
			new SqlParameter("@OrderType", SqlDbType.Bit),
			new SqlParameter("@strWhere", SqlDbType.VarChar)
		};
		array[0].Value = "userVO_view";
		array[1].Value = ShowFldName;
		array[2].Value = OrderfldName;
		array[3].Value = "";
		array[4].Value = TotalCount;
		array[5].Value = PageSize;
		array[6].Value = PageIndex;
		array[7].Value = 0;
		array[8].Value = OrderType;
		array[9].Value = strWhere;
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet == null || dataSet.Tables.Count <= 0 || dataSet.Tables[0].Rows.Count <= 0)
		{
			return null;
		}
		foreach (DataRow row in dataSet.Tables[0].Rows)
		{
			user_Model user_Model = new user_Model();
			user_Model.userid = long.Parse(row["userid"].ToString());
			user_Model.username = row["username"].ToString();
			user_Model.nickname = row["nickname"].ToString();
			user_Model.nickname = user_Model.nickname.Replace("[", "").Replace("]", "");
			user_Model.managerlvl = row["managerlvl"].ToString();
			if (row["sex"].ToString() != "")
			{
				user_Model.Int64_0 = long.Parse(row["sex"].ToString());
			}
			user_Model.money = long.Parse(row["money"].ToString());
			user_Model.expr = long.Parse(row["expr"].ToString());
			user_Model.city = row["city"].ToString();
			user_Model.city = user_Model.city.Replace("[", "").Replace("]", "");
			user_Model.isonline = row["isonline"].ToString();
			user_Model.headimg = row["headimg"].ToString();
			user_Model.remark = row["remark"].ToString();
			user_Model.idname = row["idname"].ToString();
			if (row["bbsCount"].ToString() != "")
			{
				user_Model.bbsCount = long.Parse(row["bbsCount"].ToString());
			}
			if (row["bbsReCount"].ToString() != "")
			{
				user_Model.bbsReCount = long.Parse(row["bbsReCount"].ToString());
			}
			if (row["zoneCount"].ToString() != "")
			{
				user_Model.zoneCount = long.Parse(row["zoneCount"].ToString());
			}
			if (row["TJCount"].ToString() != "")
			{
				user_Model.TJCount = long.Parse(row["TJCount"].ToString());
			}
			list.Add(user_Model);
		}
		return list;
	}

	public List<user_Model> GetSiteListVo(long PageSize, long PageIndex, string strWhere, string ShowFldName, string OrderfldName, long TotalCount, long OrderType)
	{
		StringBuilder stringBuilder = new StringBuilder();
		List<user_Model> list = new List<user_Model>();
		stringBuilder.Append("exec UP_GetRecordByPageOrder @tblName,@fldName,@OrderfldName,@StatfldName,@TotalCount,@PageSize,@PageIndex,@IsReCount,@OrderType,@strWhere");
		SqlParameter[] array = new SqlParameter[10]
		{
			new SqlParameter("@tblName", SqlDbType.VarChar),
			new SqlParameter("@fldName", SqlDbType.VarChar),
			new SqlParameter("@OrderfldName", SqlDbType.VarChar),
			new SqlParameter("@StatfldName", SqlDbType.VarChar),
			new SqlParameter("@TotalCount", SqlDbType.Int),
			new SqlParameter("@PageSize", SqlDbType.Int),
			new SqlParameter("@PageIndex", SqlDbType.Int),
			new SqlParameter("@IsReCount", SqlDbType.Int),
			new SqlParameter("@OrderType", SqlDbType.Bit),
			new SqlParameter("@strWhere", SqlDbType.VarChar)
		};
		array[0].Value = "[user]";
		array[1].Value = ShowFldName;
		array[2].Value = OrderfldName;
		array[3].Value = "";
		array[4].Value = TotalCount;
		array[5].Value = PageSize;
		array[6].Value = PageIndex;
		array[7].Value = 0;
		array[8].Value = OrderType;
		array[9].Value = strWhere;
		DataSet dataSet = DbHelperSQL.ExecuteDataset(string_1, CommandType.Text, stringBuilder.ToString(), array);
		if (dataSet == null || dataSet.Tables.Count <= 0 || dataSet.Tables[0].Rows.Count <= 0)
		{
			return null;
		}
		foreach (DataRow row in dataSet.Tables[0].Rows)
		{
			user_Model user_Model = new user_Model();
			user_Model.siteid = long.Parse(row["siteid"].ToString());
			user_Model.userid = long.Parse(row["userid"].ToString());
			user_Model.username = row["username"].ToString();
			user_Model.sitename = row["sitename"].ToString();
			user_Model.RegTime = DateTime.Parse(row["RegTime"].ToString());
			list.Add(user_Model);
		}
		return list;
	}

	public long GetUserVoListCount(string strWhere)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("select count(userid)");
		stringBuilder.Append(" FROM [userVO_view] ");
		if (strWhere.Trim() != "")
		{
			stringBuilder.Append(" where " + strWhere);
		}
		return long.Parse(DbHelperSQL.ExecuteScalar(string_1, CommandType.Text, stringBuilder.ToString()).ToString());
	}
}