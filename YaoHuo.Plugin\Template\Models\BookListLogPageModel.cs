using System;
using System.Collections.Generic;
using YaoHuo.Plugin.Template.Models;

namespace YaoHuo.Plugin.BBS.Models
{
    /// <summary>
    /// 更多动态页面数据模型
    /// </summary>
    public class BookListLogPageModel : BasePageModelWithPagination
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public BookListLogPageModel()
        {
            PageTitle = "更多动态"; // 默认标题，会根据 action 动态设置
        }

        /// <summary>
        /// 动态列表数据
        /// </summary>
        public List<DynamicItemModel> DynamicsList { get; set; } = new List<DynamicItemModel>();

        /// <summary>
        /// 动作类型（"my" 或 "friends"）
        /// </summary>
        public string ActionType { get; set; } = "my";

        /// <summary>
        /// 目标用户ID
        /// </summary>
        public string TargetUserId { get; set; } = "";

        /// <summary>
        /// 显示文本（"我的" 或 "TA"）
        /// </summary>
        public string WhoDisplay { get; set; } = "我的";

        /// <summary>
        /// 性别显示文本（"他的" 或 "她的"）
        /// </summary>
        public string GenderDisplay { get; set; } = "他的";

        /// <summary>
        /// 导航链接数据
        /// </summary>
        public NavigationModel Navigation { get; set; } = new NavigationModel();

        /// <summary>
        /// 是否有动态数据
        /// </summary>
        public bool HasDynamics => DynamicsList != null && DynamicsList.Count > 0;
    }

    /// <summary>
    /// 单个动态项的数据结构
    /// </summary>
    public class DynamicItemModel
    {
        /// <summary>
        /// 动态内容（已处理HTML）
        /// </summary>
        public string LogInfo { get; set; } = "";

        /// <summary>
        /// 友好时间显示（如：3小时前）
        /// </summary>
        public string FriendlyTime { get; set; } = "";

        /// <summary>
        /// 详细时间（用于tooltip显示）
        /// </summary>
        public string DetailTime { get; set; } = "";



        /// <summary>
        /// 作者昵称（仅好友动态显示）
        /// </summary>
        public string AuthorNickname { get; set; } = "";

        /// <summary>
        /// 作者用户ID（仅好友动态）
        /// </summary>
        public string AuthorUserId { get; set; } = "";

        /// <summary>
        /// 作者用户空间链接（仅好友动态）
        /// </summary>
        public string AuthorSpaceUrl { get; set; } = "";

        /// <summary>
        /// 是否显示作者信息（好友动态为true）
        /// </summary>
        public bool ShowAuthor { get; set; } = false;
    }

    /// <summary>
    /// 导航数据模型
    /// </summary>
    public class NavigationModel
    {
        /// <summary>
        /// 我的动态链接
        /// </summary>
        public string MyDynamicsUrl { get; set; } = "";

        /// <summary>
        /// 好友动态链接
        /// </summary>
        public string FriendsDynamicsUrl { get; set; } = "";

        /// <summary>
        /// 是否为我的动态页面
        /// </summary>
        public bool IsMyDynamics { get; set; } = true;

        /// <summary>
        /// 是否为好友动态页面
        /// </summary>
        public bool IsFriendsDynamics { get; set; } = false;
    }
}
