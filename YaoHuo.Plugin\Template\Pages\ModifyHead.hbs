{{#if Message.HasMessage}}
<div class="message {{Message.Type}}">
    <div>
        {{Message.Content}}
    </div>
</div>
{{/if}}

<!-- 头像预览 -->
<div class="card">
    <div class="card-body">
        <div class="flex flex-col items-center">
            <img src="{{Avatar.CurrentFullImageUrl}}" alt="当前头像" class="w-24 h-24 sm:w-28 sm:h-28 rounded-full object-fill border-3 border-primary-light shadow-md mb-4 transition-transform hover:scale-105" id="preview-avatar">
            <h3 class="text-lg font-medium text-text-primary mb-2" id="preview-title">当前头像</h3>
            <p class="text-sm text-text-secondary text-center max-w-[80%]" id="avatar-tip-text">选择一个能展示你个性的头像，让大家更容易记住你</p>
        </div>
    </div>
</div>

<!-- 系统头像选择 -->
<div class="card">
    <div class="card-header">
        <div class="card-title">
            <i data-lucide="image" class="card-icon"></i>
            选择系统头像
        </div>
    </div>
    <div class="card-body">
        <!-- 性别选择器 -->
        <div class="grid-2 mb-4">
            <div class="flex-1 p-2 text-center border border-border-normal rounded cursor-pointer transition-all flex items-center justify-center gap-2" id="male-option" onclick="selectGender('male')">
                <i data-lucide="mars"></i> 男
            </div>
            <div class="flex-1 p-2 text-center border border-border-normal rounded cursor-pointer transition-all flex items-center justify-center gap-2" id="female-option" onclick="selectGender('female')">
                <i data-lucide="venus"></i> 女
            </div>
        </div>

        <!-- 头像网格 -->
        <div class="grid-3 mb-4 sm:grid-cols-2 md:grid-cols-3" id="avatar-grid">
            <!-- 通过JavaScript动态加载 -->
        </div>

        <!-- 左右箭头翻页和设为头像按钮 -->
        <div class="flex justify-between items-center mt-3 gap-2 sm:gap-3">
            <div class="w-8 h-8 sm:w-9 sm:h-9 flex items-center justify-center rounded-full bg-white border border-border-normal text-text-secondary cursor-pointer transition-all duration-200 flex-shrink-0 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light" id="prev-arrow" onclick="navigateAvatars('prev')">
                <i data-lucide="chevron-left"></i>
            </div>
            <button class="btn btn-primary flex-1 min-w-0 sm:min-w-[120px] py-2 px-3 sm:px-4 text-sm gap-1 sm:gap-2 hover:transform hover:-translate-y-0.5 hover:shadow-md active:translate-y-0 hidden" id="set-avatar-btn" onclick="saveSystemAvatar()">
                <i data-lucide="check"></i>
                设为头像
            </button>
            <div class="w-8 h-8 sm:w-9 sm:h-9 flex items-center justify-center rounded-full bg-white border border-border-normal text-text-secondary cursor-pointer transition-all duration-200 flex-shrink-0 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light" id="next-arrow" onclick="navigateAvatars('next')">
                <i data-lucide="chevron-right"></i>
            </div>
        </div>
    </div>
</div>

<!-- 自定义头像 -->
<div class="card">
    <div class="card-header">
        <div class="card-title">
            <i data-lucide="link" class="card-icon"></i>
            自定义站内图片地址
        </div>
    </div>
    <div class="card-body">
        <form id="custom-avatar-form" action="{{Avatar.FormAction}}" method="post">
            <div class="form-group">
                <div class="flex items-center gap-1">
                    <input type="text" class="form-input flex-grow min-w-0" placeholder="输入站内图片地址" id="custom-avatar-url" name="toheadimg">
                    <button type="button" class="btn btn-outline flex-shrink-0 px-4 py-3 gap-1" onclick="previewCustomAvatar(event)">
                        <i data-lucide="refresh-cw" class="w-4 h-4"></i>
                        预览
                    </button>
                </div>
                <div class="form-hint">(示例: bbs/face/放电.gif)</div>
            </div>
            
            <!-- 自定义头像预览区域 -->
            <div id="custom-preview-container" class="hidden mt-4">
                <div class="flex flex-col items-center my-2 mb-4 bg-bg-gray-50 p-4 rounded-md border border-dashed border-border-normal w-full text-center">
                    <div class="w-full flex justify-center items-center mb-4 overflow-hidden">
                        <img src="" alt="头像预览" class="block w-auto max-w-full max-h-[50vh] rounded-md object-contain" id="custom-preview-img">
                    </div>
                    <div id="preview-size-tip" class="hidden mb-2 text-red-600 text-xs">
                        图片较大，已自动缩放
                    </div>
                </div>
                <!-- 设为头像按钮 -->
                <button type="button" class="btn btn-primary w-full py-2 px-3 sm:px-4 text-sm gap-1 sm:gap-2 hover:transform hover:-translate-y-0.5 hover:shadow-md active:translate-y-0 mt-4" id="set-custom-avatar-btn" onclick="setCustomAvatar()">
                    <i data-lucide="check"></i>
                    设为头像
                </button>
            </div>
            
            <!-- 隐藏字段 -->
            <input type="hidden" name="action" value="gomod">
            <input type="hidden" name="siteid" value="{{SiteInfo.SiteId}}">
            <input type="hidden" name="classid" value="{{SiteInfo.ClassId}}">
            <input type="hidden" name="sysimg" id="sysimg-input" value="{{Avatar.CurrentSystemImageNumber}}">
        </form>
    </div>
</div>

<!-- 上传头像 -->
<div class="card">
    <div class="card-header">
        <div class="card-title">
            <i data-lucide="upload" class="card-icon"></i>
            从相册上传
        </div>
    </div>
    <div class="card-body">
        <div class="upload-section">
            <a href="{{Avatar.AlbumUploadUrl}}" class="flex items-center justify-center gap-2 w-full p-3 bg-primary-alpha-05 border border-dashed border-primary rounded-md text-primary cursor-pointer transition-all hover:bg-primary-alpha-10">
                <i data-lucide="image-plus"></i>
                <span>点击上传相片</span>
            </a>
        </div>
    </div>
</div>

<script>
    // 初始化Lucide图标
    document.addEventListener('DOMContentLoaded', function() {
        lucide.createIcons();
        initializePage();
    });

    // 页面初始化
    function initializePage() {
        // 初始化性别选择器
        initializeGenderSelector();
        
        // 加载初始头像网格（默认加载男性头像第一页）
        loadAvatarGrid('male', 0);
    }

    // 初始化性别选择器
    function initializeGenderSelector() {
        const currentAvatarNumber = {{Avatar.CurrentSystemImageNumber}};
        
        // 根据当前头像编号自动选择性别
        if (currentAvatarNumber >= 31) {
            selectGender('female', false);
        } else {
            selectGender('male', false);
        }
    }

    // 全局变量
    let currentGender = 'male';
    let currentPage = 0;
    let selectedAvatarNumber = null;
    
    // 常量
    const maleAvatarCount = {{Avatar.MaleAvatarCount}};
    const femaleAvatarCount = {{Avatar.FemaleAvatarCount}};
    const avatarsPerPage = {{Avatar.PerPage}};
    const baseUrl = "{{Avatar.SystemImageBaseUrl}}";
    
    // 计算最大页数
    function getMaxPages(gender) {
        const count = gender === 'male' ? maleAvatarCount : femaleAvatarCount;
        return Math.ceil(count / avatarsPerPage) - 1;
    }

    // 选择性别
    function selectGender(gender, resetPage = true) {
        // 更新UI
        const maleOption = document.getElementById('male-option');
        const femaleOption = document.getElementById('female-option');
        
        maleOption.classList.remove('selected');
        femaleOption.classList.remove('selected');
        maleOption.classList.remove('bg-primary-alpha-10', 'border-primary', 'text-primary', 'font-medium');
        femaleOption.classList.remove('bg-primary-alpha-10', 'border-primary', 'text-primary', 'font-medium');
        
        if (gender === 'male') {
            maleOption.classList.add('selected');
            maleOption.classList.add('bg-primary-alpha-10', 'border-primary', 'text-primary', 'font-medium');
        } else {
            femaleOption.classList.add('selected');
            femaleOption.classList.add('bg-primary-alpha-10', 'border-primary', 'text-primary', 'font-medium');
        }
        
        // 更新全局状态
        currentGender = gender;
        
        // 重置页码
        if (resetPage) {
            currentPage = 0;
        }
        
        // 清除选中的头像
        selectedAvatarNumber = null;
        document.getElementById('set-avatar-btn').classList.add('hidden');
        
        // 加载新的头像网格
        loadAvatarGrid(gender, currentPage);
    }

    // 加载头像网格
    function loadAvatarGrid(gender, page) {
        const grid = document.getElementById('avatar-grid');
        grid.innerHTML = '';
        
        // 计算起始和结束头像编号
        let startNumber, endNumber;
        
        if (gender === 'male') {
            startNumber = page * avatarsPerPage + 1;
            endNumber = Math.min(startNumber + avatarsPerPage - 1, maleAvatarCount);
        } else {
            startNumber = page * avatarsPerPage + 31;
            endNumber = Math.min(startNumber + avatarsPerPage - 1, 30 + femaleAvatarCount);
        }
        
        // 创建头像选项
        for (let i = startNumber; i <= endNumber; i++) {
            const avatarUrl = `${baseUrl}${i}.gif`;
            
            const div = document.createElement('div');
            div.className = 'relative aspect-square rounded-md overflow-hidden cursor-pointer shadow-sm transition-all hover:-translate-y-0.5 hover:shadow-md';
            div.onclick = function() { selectAvatar(this, i); };
            
            const img = document.createElement('img');
            img.src = avatarUrl;
            img.alt = `系统头像${i}`;
            img.className = 'w-full h-full object-fill';
            
            div.appendChild(img);
            grid.appendChild(div);
        }
        
        // 更新导航箭头状态
        updateNavigationArrows();
    }

    // 更新导航箭头状态
    function updateNavigationArrows() {
        const prevArrow = document.getElementById('prev-arrow');
        const nextArrow = document.getElementById('next-arrow');
        const maxPages = getMaxPages(currentGender);
        
        // 第一页禁用上一页按钮
        if (currentPage === 0) {
            prevArrow.classList.add('opacity-50');
            prevArrow.classList.add('cursor-not-allowed');
        } else {
            prevArrow.classList.remove('opacity-50');
            prevArrow.classList.remove('cursor-not-allowed');
        }
        
        // 最后一页禁用下一页按钮
        if (currentPage >= maxPages) {
            nextArrow.classList.add('opacity-50');
            nextArrow.classList.add('cursor-not-allowed');
        } else {
            nextArrow.classList.remove('opacity-50');
            nextArrow.classList.remove('cursor-not-allowed');
        }
    }

    // 选择头像
    function selectAvatar(element, avatarNumber) {
        // 移除其他选中状态
        const options = document.querySelectorAll('#avatar-grid > div');
        options.forEach(option => {
            option.classList.remove('selected');
            option.classList.remove('border-3');
            option.classList.remove('border-primary');
            
            // 移除选中标记（如果存在）
            const checkmark = option.querySelector('.avatar-checkmark');
            if (checkmark) {
                option.removeChild(checkmark);
            }
        });
        
        // 添加选中状态
        element.classList.add('selected');
        element.classList.add('border-3');
        element.classList.add('border-primary');
        
        // 添加选中标记
        if (!element.querySelector('.avatar-checkmark')) {
            const checkmark = document.createElement('div');
            checkmark.className = 'avatar-checkmark absolute top-1 right-1 w-5 h-5 bg-primary rounded-full flex items-center justify-center text-white text-xs z-10';
            checkmark.innerHTML = '✓';
            element.appendChild(checkmark);
        }
        
        // 更新全局状态
        selectedAvatarNumber = avatarNumber;
        
        // 更新预览
        document.getElementById('preview-avatar').src = `${baseUrl}${avatarNumber}.gif`;
        document.getElementById('preview-title').textContent = '预览效果';
        
        // 更新提示文本
        document.getElementById('avatar-tip-text').textContent = '您正在预览系统头像';
        
        // 更新隐藏字段
        document.getElementById('sysimg-input').value = avatarNumber;
        
        // 显示设为头像按钮
        document.getElementById('set-avatar-btn').classList.remove('hidden');

        // 取消自定义头像预览效果
        const customPreviewContainer = document.getElementById('custom-preview-container');
        customPreviewContainer.classList.add('hidden');
        
        // 添加选择动画效果
        element.style.transform = 'scale(0.95)';
        setTimeout(() => {
            element.style.transform = '';
        }, 200);
    }

    // 左右翻页导航
    function navigateAvatars(direction) {
        const maxPages = getMaxPages(currentGender);
        
        if (direction === 'prev' && currentPage > 0) {
            currentPage--;
            loadAvatarGrid(currentGender, currentPage);
        } else if (direction === 'next' && currentPage < maxPages) {
            currentPage++;
            loadAvatarGrid(currentGender, currentPage);
        }
    }

    // 保存系统头像
    function saveSystemAvatar() {
        if (selectedAvatarNumber) {
            document.getElementById('custom-avatar-url').value = '';
            document.getElementById('custom-avatar-form').submit();
        }
    }

    // 预览自定义头像
    function previewCustomAvatar(event) {
        if (event) {
            event.preventDefault();
        }

        const urlInput = document.getElementById('custom-avatar-url');
        let url = urlInput.value.trim();
        const previewBtnIcon = document.querySelector('.preview-btn .lucide-refresh-cw');
        const previewContainer = document.getElementById('custom-preview-container');
        const previewImg = document.getElementById('custom-preview-img');
        const sizeTip = document.getElementById('preview-size-tip');

        if (url) {
            // 检查是否包含完整URL
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                // 拼接站点URL
                url = "{{SiteInfo.HttpStart}}" + url;
            }
            
            // 显示加载动画
            if (previewBtnIcon) {
                previewBtnIcon.classList.add('animate-spin');
            }
            
            // 注意：自定义头像预览不影响顶部的预览区域
            
            // 设置最小加载时间以确保动画可见
            const minLoadingTime = 300; // 300ms
            const startTime = Date.now();
            
            const tempImg = new Image();
            tempImg.onload = function() {
                const elapsedTime = Date.now() - startTime;
                const remainingTime = Math.max(0, minLoadingTime - elapsedTime);
                
                setTimeout(() => {
                    // 移除加载动画
                    if (previewBtnIcon) {
                        previewBtnIcon.classList.remove('animate-spin');
                    }

                    // 图像加载成功
                    previewContainer.classList.remove('hidden'); // 确保容器可见
                    previewImg.src = url; // 设置预览图片，替换旧图片
                    // 确保"设为头像"按钮可见
                    document.getElementById('set-custom-avatar-btn').classList.remove('hidden');

                    // 检查图片高度是否超过限制
                    if (tempImg.height > window.innerHeight * 0.5) {
                        sizeTip.classList.remove('hidden');
                    } else {
                        sizeTip.classList.add('hidden');
                    }
                    
                    // 清除系统头像的选中状态
                    const options = document.querySelectorAll('#avatar-grid > div');
                    options.forEach(option => {
                        option.classList.remove('selected');
                        option.classList.remove('border-3');
                        option.classList.remove('border-primary');
                        
                        // 移除选中标记（如果存在）
                        const checkmark = option.querySelector('.avatar-checkmark');
                        if (checkmark) {
                            option.removeChild(checkmark);
                        }
                    });
                    
                    // 隐藏系统头像的设为头像按钮
                    document.getElementById('set-avatar-btn').classList.add('hidden');
                    
                    // 清除选中的头像编号
                    selectedAvatarNumber = null;
                    
                    // 更新 lucide 图标
                    lucide.createIcons();
                    
                    // 确保预览区域可见（滚动到目标卡片，上方留一些边距）
                    const customAvatarCard = previewContainer.closest('.card');
                    if (customAvatarCard) {
                        const cardTop = customAvatarCard.getBoundingClientRect().top + window.pageYOffset;
                        const headerHeight = 78; // 增加顶部边距，调整滚动位置
                        window.scrollTo({
                            top: cardTop - headerHeight,
                            behavior: 'smooth'
                        });
                    }

                }, remainingTime);
            };
            
            tempImg.onerror = function() {
                const elapsedTime = Date.now() - startTime;
                const remainingTime = Math.max(0, minLoadingTime - elapsedTime);
                
                setTimeout(() => {
                    // 移除加载动画
                    if (previewBtnIcon) {
                        previewBtnIcon.classList.remove('animate-spin');
                    }

                    // 图像加载失败 - 显示错误提示并清空图片
                    showToast('加载失败，图片地址无效', 'error');
                    previewImg.src = ''; // 清空图片源
                    sizeTip.classList.add('hidden'); // 隐藏尺寸提示
                    previewContainer.classList.remove('hidden'); // 确保容器可见，但里面没图
                    // 同时隐藏"设为头像"按钮
                    document.getElementById('set-custom-avatar-btn').classList.add('hidden');

                }, remainingTime);
            };
            
            // 设置图像源
            tempImg.src = url;
        } else {
             // 移除加载动画 (如果之前有)
            if (previewBtnIcon) {
                previewBtnIcon.classList.remove('animate-spin');
            }
            // 显示警告提示并清空图片
            showToast('请输入有效的图片地址', 'warning');
            previewImg.src = ''; // 清空图片源
            sizeTip.classList.add('hidden'); // 隐藏尺寸提示
            previewContainer.classList.remove('hidden'); // 确保容器可见，但里面没图
            // 同时隐藏"设为头像"按钮
            document.getElementById('set-custom-avatar-btn').classList.add('hidden');
        }
    }

    // 在自定义头像卡片中心显示提示
    function showCustomAvatarCardToast(message, type = 'info') {
        // 显示普通弹窗提示
        showToast(message, type);
    }

    // 设置自定义头像
    function setCustomAvatar() {
        const urlInput = document.getElementById('custom-avatar-url');
        const url = urlInput.value.trim();
        
        if (url) {
            document.getElementById('custom-avatar-form').submit();
        } else {
            showToast('请输入有效的图片地址', 'warning');
        }
    }

    // 显示提示消息（改善定位，避免遮挡界面元素）
    function showToast(message, type = 'info') {
        // 移除所有现有的toast
        document.querySelectorAll('.toast').forEach(toast => toast.remove());

        // 找到自定义头像预览区域容器
        const previewContainer = document.getElementById('custom-preview-container');
        
        // 如果预览容器不可见，则显示在卡片中心
        if (!previewContainer || previewContainer.classList.contains('hidden')) {
            const customAvatarCard = document.querySelector('#custom-avatar-form').closest('.card');
            showToastInContainer(customAvatarCard, message, type);
        } else {
            // 显示在预览区域内
            const previewArea = previewContainer.querySelector('.bg-bg-gray-50');
            showToastInContainer(previewArea, message, type);
        }
    }

    // 在指定容器中显示Toast提示
    function showToastInContainer(container, message, type) {
        const toast = document.createElement('div');
        toast.className = `toast absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 px-6 py-3 rounded-md z-[1000] shadow-lg opacity-0 transition-opacity duration-300 text-sm font-medium max-w-[300px] text-center bg-[rgba(0,0,0,0.7)] text-white whitespace-nowrap`;
        
        // 添加类型样式
        if (type === 'success') {
            toast.classList.add('bg-[rgba(16,185,129,0.95)]');
        } else if (type === 'error') {
            toast.classList.add('bg-[rgba(239,68,68,0.95)]');
        } else if (type === 'warning') {
            toast.classList.add('bg-[rgba(217,119,6,0.95)]');
        } else if (type === 'info') {
            toast.classList.add('bg-[rgba(59,130,246,0.95)]');
        }
        
        toast.textContent = message;
        
        // 确保容器有相对定位
        const originalPosition = container.style.position;
        if (!originalPosition || originalPosition === 'static') {
            container.style.position = 'relative';
        }
        
        // 将toast添加到容器中
        container.appendChild(toast);
        
        // 淡入效果
        setTimeout(() => {
            toast.classList.remove('opacity-0');
            toast.classList.add('opacity-100');
        }, 10);
        
        // 2秒后移除提示
        setTimeout(() => {
            toast.classList.remove('opacity-100');
            toast.classList.add('opacity-0');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
                // 恢复原始定位
                if (!originalPosition || originalPosition === 'static') {
                    container.style.position = originalPosition || '';
                }
            }, 300);
        }, 2000);
    }
</script> 