using YaoHuo.Plugin.Template.Models;

namespace YaoHuo.Plugin.BBS.Models
{
    /// <summary>
    /// MyFile 个人中心页面数据模型
    /// </summary>
    public class MyFilePageModel : BasePageModel
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MyFilePageModel()
        {
            PageTitle = "个人中心";
        }

        /// <summary>
        /// 用户基本信息
        /// </summary>
        public MyFileUserInfoModel UserInfo { get; set; } = new MyFileUserInfoModel();

        /// <summary>
        /// 用户统计信息
        /// </summary>
        public MyFileStatisticsModel Statistics { get; set; } = new MyFileStatisticsModel();

        /// <summary>
        /// 用户资产信息
        /// </summary>
        public MyFileAssetsModel Assets { get; set; } = new MyFileAssetsModel();

        /// <summary>
        /// 用户权限信息
        /// </summary>
        public MyFilePermissionsModel Permissions { get; set; } = new MyFilePermissionsModel();

        /// <summary>
        /// 勋章信息
        /// </summary>
        public MyFileMedalsModel Medals { get; set; } = new MyFileMedalsModel();

        /// <summary>
        /// 功能链接
        /// </summary>
        public MyFileLinksModel Links { get; set; } = new MyFileLinksModel();

        /// <summary>
        /// 站点信息（MyFile页面专用，覆盖基类的SiteInfo）
        /// </summary>
        public new MyFileSiteInfoModel SiteInfo { get; set; } = new MyFileSiteInfoModel();
    }

    /// <summary>
    /// 用户基本信息模型
    /// </summary>
    public class MyFileUserInfoModel
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 用户昵称
        /// </summary>
        public string Nickname { get; set; }

        /// <summary>
        /// 显示名称（带颜色和格式）
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// 用户等级
        /// </summary>
        public string Level { get; set; }

        /// <summary>
        /// 身份信息（纯文本）
        /// </summary>
        public string Identity { get; set; }

        /// <summary>
        /// 身份HTML（包含颜色和图标）
        /// </summary>
        public string IdentityHtml { get; set; }

        /// <summary>
        /// 有效期时间
        /// </summary>
        public string EndTime { get; set; }

        /// <summary>
        /// 经验值
        /// </summary>
        public long Experience { get; set; }

        /// <summary>
        /// 是否为VIP
        /// </summary>
        public bool IsVip { get; set; }

        /// <summary>
        /// 是否有有效期
        /// </summary>
        public bool HasEndTime { get; set; }
    }

    /// <summary>
    /// 用户统计信息模型
    /// </summary>
    public class MyFileStatisticsModel
    {
        /// <summary>
        /// 未读消息数
        /// </summary>
        public string MessageCount { get; set; }

        /// <summary>
        /// 总消息数
        /// </summary>
        public string MessageAll { get; set; }

        /// <summary>
        /// 好友数
        /// </summary>
        public string FriendCount { get; set; }

        /// <summary>
        /// 发帖数
        /// </summary>
        public string PostCount { get; set; }

        /// <summary>
        /// 回复数
        /// </summary>
        public string ReplyCount { get; set; }

        /// <summary>
        /// 消息显示格式（未读/总数）
        /// </summary>
        public string MessageDisplay { get; set; }
    }

    /// <summary>
    /// 用户资产信息模型
    /// </summary>
    public class MyFileAssetsModel
    {
        /// <summary>
        /// 妖晶余额
        /// </summary>
        public long Money { get; set; }

        /// <summary>
        /// 银行妖晶
        /// </summary>
        public long BankMoney { get; set; }

        /// <summary>
        /// RMB余额
        /// </summary>
        public decimal RMB { get; set; }

        /// <summary>
        /// 妖晶名称
        /// </summary>
        public string MoneyName { get; set; }

        /// <summary>
        /// 妖晶显示格式
        /// </summary>
        public string MoneyDisplay { get; set; }

        /// <summary>
        /// 银行妖晶显示格式
        /// </summary>
        public string BankDisplay { get; set; }

        /// <summary>
        /// RMB显示格式
        /// </summary>
        public string RMBDisplay { get; set; }

        /// <summary>
        /// 是否有银行妖晶
        /// </summary>
        public bool HasBankMoney { get; set; }

        /// <summary>
        /// 是否有RMB
        /// </summary>
        public bool HasRMB { get; set; }
    }

    /// <summary>
    /// 用户权限信息模型
    /// </summary>
    public class MyFilePermissionsModel
    {
        /// <summary>
        /// 管理员等级
        /// </summary>
        public string ManagerLevel { get; set; }

        /// <summary>
        /// 管理员显示名称
        /// </summary>
        public string AdminDisplay { get; set; }

        /// <summary>
        /// 是否为管理员
        /// </summary>
        public bool IsAdmin { get; set; }

        /// <summary>
        /// 是否为超级管理员
        /// </summary>
        public bool IsSuperAdmin { get; set; }

        /// <summary>
        /// 是否有管理员权限
        /// </summary>
        public bool HasAdminPermission { get; set; }
    }

    /// <summary>
    /// 勋章信息模型
    /// </summary>
    public class MyFileMedalsModel
    {
        /// <summary>
        /// 勋章HTML内容
        /// </summary>
        public string MedalHtml { get; set; }

        /// <summary>
        /// 是否有勋章
        /// </summary>
        public bool HasMedals { get; set; }
    }

    /// <summary>
    /// 功能链接模型
    /// </summary>
    public class MyFileLinksModel
    {
        /// <summary>
        /// 邮箱链接
        /// </summary>
        public string MailboxUrl { get; set; }

        /// <summary>
        /// 好友列表链接
        /// </summary>
        public string FriendsUrl { get; set; }

        /// <summary>
        /// 我的帖子链接
        /// </summary>
        public string PostsUrl { get; set; }

        /// <summary>
        /// 我的回复链接
        /// </summary>
        public string RepliesUrl { get; set; }

        /// <summary>
        /// 编辑资料链接
        /// </summary>
        public string EditProfileUrl { get; set; }

        /// <summary>
        /// 充值链接
        /// </summary>
        public string RechargeUrl { get; set; }

        /// <summary>
        /// 账目明细链接
        /// </summary>
        public string AccountDetailUrl { get; set; }

        /// <summary>
        /// RMB充值链接
        /// </summary>
        public string RMBRechargeUrl { get; set; }

        /// <summary>
        /// RMB明细链接
        /// </summary>
        public string RMBDetailUrl { get; set; }

        /// <summary>
        /// 申请勋章链接
        /// </summary>
        public string ApplyMedalUrl { get; set; }

        /// <summary>
        /// 购买勋章链接
        /// </summary>
        public string BuyMedalUrl { get; set; }

        /// <summary>
        /// 收藏夹链接
        /// </summary>
        public string FavoritesUrl { get; set; }

        /// <summary>
        /// 相册链接
        /// </summary>
        public string AlbumUrl { get; set; }

        /// <summary>
        /// 家族链接
        /// </summary>
        public string ClanUrl { get; set; }

        /// <summary>
        /// 黑名单链接
        /// </summary>
        public string BlacklistUrl { get; set; }

        /// <summary>
        /// 管理员链接
        /// </summary>
        public string AdminUrl { get; set; }

        /// <summary>
        /// 超级管理员链接
        /// </summary>
        public string SuperAdminUrl { get; set; }

        /// <summary>
        /// 退出登录链接
        /// </summary>
        public string LogoutUrl { get; set; }

        /// <summary>
        /// VIP购买链接
        /// </summary>
        public string VipUrl { get; set; }

        /// <summary>
        /// 修改密码链接
        /// </summary>
        public string ChangePasswordUrl { get; set; }

        /// <summary>
        /// 更换头像链接
        /// </summary>
        public string ChangeAvatarUrl { get; set; }

        /// <summary>
        /// 我的空间链接
        /// </summary>
        public string MySpaceUrl { get; set; }
    }

    /// <summary>
    /// 站点信息模型（MyFile页面专用）
    /// </summary>
    public class MyFileSiteInfoModel
    {
        /// <summary>
        /// 站点ID
        /// </summary>
        public string SiteId { get; set; }

        /// <summary>
        /// HTTP起始路径
        /// </summary>
        public string HttpStart { get; set; }

        /// <summary>
        /// 妖晶名称
        /// </summary>
        public string MoneyName { get; set; }
    }
}
