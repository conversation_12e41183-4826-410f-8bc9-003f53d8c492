/**
 * 模态弹窗管理器
 * 适用于各种提示信息的弹窗展示
 */
class ModalManager {
    constructor() {
        this.modals = {};
        this.init();
    }

    init() {
        // 创建模态弹窗容器
        this.createModalContainer();

        // 初始化事件监听
        this.initEventListeners();
    }

    createModalContainer() {
        // 添加成功弹窗
        const successModal = document.createElement('div');
        successModal.id = 'success-modal';
        successModal.className = 'modal-overlay';
        successModal.innerHTML = `
            <div class="modal">
                <div class="modal-icon-wrapper success-icon-wrapper">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>
                </div>
                <h3 class="modal-title">购买成功</h3>
                <p class="modal-message">操作已成功完成</p>
                
                <div class="modal-details" id="success-details">
                    <!-- 动态填充内容 -->
                </div>
                
                <div class="modal-actions">
                    <button class="button button-secondary close-modal">返回</button>
                    <button class="button button-primary" id="success-action-button">查看我的身份</button>
                </div>
            </div>
        `;

        // 添加错误弹窗
        const errorModal = document.createElement('div');
        errorModal.id = 'error-modal';
        errorModal.className = 'modal-overlay';
        errorModal.innerHTML = `
            <div class="modal">
                <div class="modal-icon-wrapper error-icon-wrapper">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                </div>
                <h3 class="modal-title">购买失败</h3>
                <p class="modal-message">操作未能完成</p>
                
                <div class="modal-details" id="error-details">
                    <!-- 动态填充内容 -->
                </div>
                
                <div class="modal-actions">
                    <button class="button button-secondary close-modal">取消</button>
                    <button class="button button-danger" id="error-action-button">重新尝试</button>
                </div>
            </div>
        `;

        // 将模态弹窗添加到页面
        document.body.appendChild(successModal);
        document.body.appendChild(errorModal);

        // 保存引用
        this.modals.success = successModal;
        this.modals.error = errorModal;
    }

    initEventListeners() {
        // 关闭按钮事件
        const closeButtons = document.querySelectorAll('.close-modal');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.hideAllModals();
            });
        });

        // 点击模态框外部关闭
        window.addEventListener('click', (e) => {
            for (const key in this.modals) {
                if (e.target === this.modals[key]) {
                    this.hideModal(key);
                }
            }
        });

        // 成功操作按钮
        const successActionButton = document.getElementById('success-action-button');
        if (successActionButton) {
            successActionButton.addEventListener('click', () => {
                this.hideModal('success');
                // 执行回调(如果有)
                if (this.successCallback) {
                    this.successCallback();
                }
            });
        }

        // 错误操作按钮
        const errorActionButton = document.getElementById('error-action-button');
        if (errorActionButton) {
            errorActionButton.addEventListener('click', () => {
                this.hideModal('error');
                // 执行回调(如果有)
                if (this.errorCallback) {
                    this.errorCallback();
                }
            });
        }
    }

    // 显示成功弹窗
    showSuccess(options = {}) {
        const {
            title = '操作成功',
            message = '您的操作已成功完成',
            details = [],
            actionText = '确定',
            actionCallback = null
        } = options;

        // 更新内容
        const modal = this.modals.success;
        modal.querySelector('.modal-title').textContent = title;
        modal.querySelector('.modal-message').textContent = message;

        // 设置按钮文本
        const actionButton = modal.querySelector('#success-action-button');
        if (actionButton) {
            actionButton.textContent = actionText;
        }

        // 设置回调
        this.successCallback = actionCallback;

        // 填充详情
        const detailsContainer = modal.querySelector('#success-details');
        detailsContainer.innerHTML = '';

        if (details.length > 0) {
            details.forEach(detail => {
                const row = document.createElement('div');
                row.className = 'modal-detail-row';
                row.innerHTML = `
                    <span class="modal-detail-label">${detail.label}</span>
                    <span class="modal-detail-value ${detail.className || ''}">${detail.value}</span>
                `;
                detailsContainer.appendChild(row);
            });
            detailsContainer.style.display = 'block';
        } else {
            detailsContainer.style.display = 'none';
        }

        // 显示弹窗
        this.showModal('success');
    }

    // 显示错误弹窗
    showError(options = {}) {
        const {
            title = '操作失败',
            message = '您的操作未能完成',
            details = [],
            actionText = '重新尝试',
            actionCallback = null
        } = options;

        // 更新内容
        const modal = this.modals.error;
        modal.querySelector('.modal-title').textContent = title;
        modal.querySelector('.modal-message').textContent = message;

        // 设置按钮文本
        const actionButton = modal.querySelector('#error-action-button');
        if (actionButton) {
            actionButton.textContent = actionText;
        }

        // 设置回调
        this.errorCallback = actionCallback;

        // 填充详情
        const detailsContainer = modal.querySelector('#error-details');
        detailsContainer.innerHTML = '';

        if (details.length > 0) {
            details.forEach(detail => {
                const row = document.createElement('div');
                row.className = 'modal-detail-row';
                row.innerHTML = `
                    <span class="modal-detail-label">${detail.label}</span>
                    <span class="modal-detail-value ${detail.className || ''}">${detail.value}</span>
                `;
                detailsContainer.appendChild(row);
            });
            detailsContainer.style.display = 'block';
        } else {
            detailsContainer.style.display = 'none';
        }

        // 显示弹窗
        this.showModal('error');
    }

    // 显示指定弹窗
    showModal(type) {
        if (this.modals[type]) {
            this.modals[type].classList.add('active');
        }
    }

    // 隐藏指定弹窗
    hideModal(type) {
        if (this.modals[type]) {
            this.modals[type].classList.remove('active');
        }
    }

    // 隐藏所有弹窗
    hideAllModals() {
        for (const key in this.modals) {
            this.hideModal(key);
        }
    }
}

// 创建全局单例实例
const modalManager = new ModalManager();

// 在DOMContentLoaded事件中初始化
document.addEventListener('DOMContentLoaded', function () {
    // 立即处理页面中的提示信息
    processPageMessages();
});

/**
 * 处理页面中的消息提示并转换为弹窗
 */
function processPageMessages() {
    // 处理成功提示
    const successElements = document.querySelectorAll('.modern-identity-purchase .success');
    if (successElements.length > 0) {
        const successElem = successElements[0];
        const message = successElem.textContent || '操作成功';
        const boldText = successElem.querySelector('b')?.textContent || '购买身份成功！';
        const linkElem = successElem.querySelector('a');

        // 提取购买信息
        let actionLink = '';
        let actionText = '确定';

        if (linkElem) {
            actionLink = linkElem.href;
            actionText = '查看我的身份';
        }

        // 构建详情
        let details = [];

        // 首先检查页面中的隐藏购买信息元素
        const boughtIdentity = document.getElementById('boughtIdentity')?.textContent || '';
        const boughtMonths = document.getElementById('boughtMonths')?.textContent || '';
        const paidAmount = document.getElementById('paidAmount')?.textContent || '';

        if (boughtIdentity || boughtMonths || paidAmount) {
            // 使用页面中的隐藏元素信息
            if (boughtIdentity) {
                // 检查身份名称中是否包含颜色信息，并相应设置类名
                const identityText = boughtIdentity;
                let className = 'green-text'; // 默认绿色

                if (identityText.includes('绿色')) {
                    className = 'green-text';
                } else if (identityText.includes('红色') || identityText.includes('红名')) {
                    className = 'red-text';
                } else if (identityText.includes('蓝色') || identityText.includes('蓝名')) {
                    className = 'blue-text';
                } else if (identityText.includes('紫色') || identityText.includes('紫名')) {
                    className = 'purple-text';
                } else if (identityText.includes('粉色') || identityText.includes('粉名')) {
                    className = 'pink-text';
                } else if (identityText.includes('金名')) {
                    className = 'gold-text';
                }

                details.push({
                    label: '购买身份',
                    value: boughtIdentity,
                    className: className
                });
            }

            if (boughtMonths) {
                details.push({
                    label: '购买月数',
                    value: boughtMonths + '个月'
                });
            }

            if (paidAmount) {
                details.push({
                    label: '支付金额',
                    value: paidAmount
                });
            }
        }
        // 如果页面中没有隐藏元素，继续使用window.purchaseInfo
        else if (window.purchaseInfo) {
            // 购买身份
            if (window.purchaseInfo.targetIdentity) {
                // 检查身份名称中是否包含颜色信息，并相应设置类名
                const identityText = window.purchaseInfo.targetIdentity;
                let className = 'green-text'; // 默认绿色

                if (identityText.includes('绿色')) {
                    className = 'green-text';
                } else if (identityText.includes('红色') || identityText.includes('红名')) {
                    className = 'red-text';
                } else if (identityText.includes('蓝色') || identityText.includes('蓝名')) {
                    className = 'blue-text';
                } else if (identityText.includes('紫色') || identityText.includes('紫名')) {
                    className = 'purple-text';
                } else if (identityText.includes('粉色') || identityText.includes('粉名')) {
                    className = 'pink-text';
                } else if (identityText.includes('金名')) {
                    className = 'gold-text';
                }

                details.push({
                    label: '购买身份',
                    value: window.purchaseInfo.targetIdentity,
                    className: className
                });
            }

            // 购买月数
            if (window.purchaseInfo.monthCount) {
                details.push({
                    label: '购买月数',
                    value: window.purchaseInfo.monthCount + '个月'
                });
            }

            // 支付金额
            if (window.purchaseInfo.price) {
                details.push({
                    label: '支付金额',
                    value: window.purchaseInfo.price
                });
            }
        } else {
            // 如果上面两个方法都无法获取信息，尝试从页面直接获取
            const currentIdentity = document.getElementById('currentIdentity')?.textContent || '';
            const targetIdentity = document.getElementById('targetIdentity')?.textContent || '';
            const monthCount = document.getElementById('month-count')?.textContent || '1';
            const price = document.getElementById('price')?.textContent || '';
            const expiryDate = document.getElementById('expiryDate')?.textContent || '';

            // 添加基本信息
            if (targetIdentity) {
                // 检查身份名称中是否包含颜色信息，并相应设置类名
                const identityText = targetIdentity;
                let className = 'green-text'; // 默认绿色

                if (identityText.includes('绿色')) {
                    className = 'green-text';
                } else if (identityText.includes('红色') || identityText.includes('红名')) {
                    className = 'red-text';
                } else if (identityText.includes('蓝色') || identityText.includes('蓝名')) {
                    className = 'blue-text';
                } else if (identityText.includes('紫色') || identityText.includes('紫名')) {
                    className = 'purple-text';
                } else if (identityText.includes('粉色') || identityText.includes('粉名')) {
                    className = 'pink-text';
                } else if (identityText.includes('金名')) {
                    className = 'gold-text';
                }

                details.push({
                    label: '购买身份',
                    value: targetIdentity,
                    className: className
                });
            }

            if (monthCount) {
                details.push({
                    label: '购买月数',
                    value: monthCount + '个月'
                });
            }

            if (price) {
                details.push({
                    label: '支付金额',
                    value: price
                });
            }
        }

        // 确保至少有基本信息
        if (details.length === 0) {
            const targetIdentityText = document.querySelectorAll('.modern-identity-purchase .card-value')[1]?.textContent || '';
            if (targetIdentityText) {
                details.push({
                    label: '购买身份',
                    value: targetIdentityText,
                    className: 'green-text'
                });
            }
        }

        // 提取购买成功的信息
        let successMessage = '';

        // 更新为新的默认文本格式，不再使用boldText
        // 获取身份信息的优先级：隐藏元素 > window.purchaseInfo > DOM元素
        let identityName = boughtIdentity ||
            window.purchaseInfo?.targetIdentity ||
            document.getElementById('targetIdentity')?.textContent ||
            document.querySelectorAll('.modern-identity-purchase .card-value')[1]?.textContent || '';

        // 移除HTML标签
        identityName = identityName.replace(/<[^>]*>/g, '').trim();

        successMessage = identityName ?
            `您已成功购买${identityName}身份` :
            '您已成功购买身份';

        // 显示成功弹窗
        modalManager.showSuccess({
            title: '购买成功',
            message: successMessage,
            details: details,
            actionText: actionText,
            actionCallback: () => {
                if (actionLink) {
                    window.location.href = actionLink;
                }
            }
        });

        // 隐藏原始提示
        successElem.style.display = 'none';
    }

    // 处理错误提示
    const errorElements = document.querySelectorAll('.modern-identity-purchase .tip');
    if (errorElements.length > 0) {
        const errorElem = errorElements[0];
        const boldText = errorElem.querySelector('b')?.textContent || '操作失败';

        // 构建错误类型映射
        const errorTypeMap = {
            '密码错误': {
                title: '支付密码错误',
                message: '请重新输入正确的支付密码',
                details: [
                    { label: '错误原因', value: '支付密码不正确' },
                    { label: '解决方案', value: '请重新输入正确的支付密码' }
                ],
                actionText: '重新尝试'
            },
            '数量不足': {
                title: '余额不足',
                message: '您的账户余额不足',
                details: [
                    { label: '错误原因', value: '账户余额不足' },
                    { label: '解决方案', value: '请充值后再试' }
                ],
                actionText: '去充值'
            },
            '暂时无法购买': {
                title: '无法购买',
                message: '暂时无法购买此身份',
                details: [],
                actionText: '返回'
            },
            '一次只能购买大于': {
                title: '身份级别错误',
                message: '身份级别选择错误',
                details: [
                    { label: '错误原因', value: '身份级别选择错误' },
                    { label: '解决方案', value: '请选择高于当前级别的身份' }
                ],
                actionText: '返回'
            }
        };

        // 根据内容判断错误类型
        let errorType = {
            title: '操作失败',
            message: boldText,
            details: [],
            actionText: '返回'
        };

        for (const key in errorTypeMap) {
            if (boldText.includes(key)) {
                errorType = errorTypeMap[key];
                break;
            }
        }

        // 显示错误弹窗
        modalManager.showError({
            title: errorType.title,
            message: errorType.message,
            details: errorType.details,
            actionText: errorType.actionText,
            actionCallback: () => {
                if (errorType.actionText === '去充值') {
                    // 跳转到充值页面
                    const rechargeLink = document.querySelector('.modern-identity-purchase .button-outline');
                    if (rechargeLink) {
                        window.location.href = rechargeLink.href;
                    }
                } else if (errorType.actionText === '重新尝试') {
                    // 聚焦到密码输入框
                    const passwordInput = document.getElementById('password');
                    if (passwordInput) {
                        passwordInput.value = '';
                        passwordInput.focus();
                    }
                }
            }
        });

        // 隐藏原始提示
        errorElem.style.display = 'none';
    }
} 