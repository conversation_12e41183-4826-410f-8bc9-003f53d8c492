document.addEventListener('DOMContentLoaded', function () {
    // 附件优化脚本
    var attachmentTitles = document.querySelectorAll('.attachmentitle');
    attachmentTitles.forEach(function (title) {
        var text = title.textContent;
        if (text.startsWith(' ')) {
            title.textContent = text.trimStart();
        }
    });

    var attachmentNames = document.querySelectorAll('.attachmentname');
    attachmentNames.forEach(function (name) {
        var text = name.textContent;
        if (text.includes('..')) {
            name.textContent = text.replace(/\\.{2,}/g, '.');
        }
    });

    var attachments = document.querySelectorAll('.attachment');
    attachments.forEach(function (attachment) {
        var downloadLink = attachment.querySelector('.downloadlink');
        if (!downloadLink) {
            var attachmentSum = attachment.querySelector('.attachmenSum');
            if (attachmentSum) {
                attachmentSum.style.display = 'none';
            }
            attachment.style.border = 'none';
            attachment.style.padding = '0';
            attachment.style.backgroundColor = 'transparent';
        }
        var attachmentListNum = attachment.querySelector('.attachmentlistnum');
        if (attachmentListNum && attachmentListNum.textContent.trim() === '1') {
            var attachmentNumber = attachment.querySelector('.attachmentnumber');
            if (attachmentNumber) {
                attachmentNumber.style.display = 'none';
            }
        }
    });

    var attachmentImages = document.querySelectorAll('.attachmentimage');
    attachmentImages.forEach(function (image) {
        var downloadName = image.previousElementSibling;
        if (downloadName && downloadName.classList.contains('downloadname')) {
            downloadName.style.display = 'none';
        }
    });
}); 