﻿using System;
using KeLin.ClassManager;
using YaoHuo.Plugin.WebSite;
using System.Runtime.Caching;

namespace YaoHuo.Plugin.BBS
{
    public class BBSGuessService
    {
        private readonly MyPageWap _page;
        private readonly string _instanceName;

        // 使用 MemoryCache 作为缓存实现
        private static readonly MemoryCache _guessCache = new MemoryCache("BBSGuessCache");
        private static readonly CacheItemPolicy _cachePolicy = new CacheItemPolicy
        {
            // 非竞猜帖可以缓存更久，因为帖子创建后就无法添加竞猜了
            AbsoluteExpiration = DateTimeOffset.Now.AddDays(90)
        };

        // 缓存键格式：GUESS_{postid}
        private static string GetCacheKey(long postId) => $"GUESS_{postId}";

        // 添加一个静态只读对象表示非竞猜帖
        private static readonly object NOT_GUESS_POST = new object();

        public BBSGuessService(MyPageWap page)
        {
            _page = page;
            _instanceName = PubConstant.GetAppString("InstanceName");
        }

        public GuessData GetGuessingData(long bbsId)
        {
            string cacheKey = GetCacheKey(bbsId);
            bool isGuessPost = false;

            // 先检查缓存中是否已知道这个帖子是否为竞猜帖
            if (_guessCache.Contains(cacheKey))
            {
                var cachedData = _guessCache.Get(cacheKey);

                // 如果是非竞猜帖标记，直接返回null
                if (cachedData == NOT_GUESS_POST)
                {
                    return null;
                }

                // 是竞猜帖，需要获取最新数据
                isGuessPost = true;
            }

            try
            {
                if (string.IsNullOrEmpty(_instanceName))
                {
                    return null;
                }

                GuessManager guessManager = new GuessManager(_instanceName);

                // 如果不知道是否为竞猜帖，先查询一次
                if (!isGuessPost)
                {
                    var checkData = guessManager.GetGuessingByBbsId(bbsId);

                    // 缓存"是否为竞猜帖"的状态
                    _guessCache.Add(cacheKey, checkData == null ? NOT_GUESS_POST : new object(), _cachePolicy);

                    // 如果不是竞猜帖，直接返回null
                    if (checkData == null)
                    {
                        return null;
                    }
                }

                // 获取最新的竞猜数据
                return guessManager.GetGuessingByBbsId(bbsId);
            }
            catch (Exception ex)
            {
                // 记录详细错误日志
                System.Diagnostics.Debug.WriteLine($"GetGuessingData error: bbsId={bbsId}, error={ex.Message}");
                return null;
            }
        }

        public BetInfo GetUserBet(long guessingId, long userId)
        {
            try
            {
                GuessManager guessManager = new GuessManager(_instanceName);
                return guessManager.GetUserBet(guessingId, userId);
            }
            catch (Exception ex)
            {
                // 记录详细错误日志
                System.Diagnostics.Debug.WriteLine($"GetUserBet error: guessingId={guessingId}, userId={userId}, error={ex.Message}");
                return null;
            }
        }

        public BetInfo ProcessGuessing(string id, string userid, GuessData guessingData)
        {
            try
            {
                if (guessingData == null)
                {
                    return null;
                }

                if (guessingData.IsClosed)
                {
                    SetWinningInfo(guessingData);
                }

                long userIdLong;
                if (long.TryParse(userid, out userIdLong))
                {
                    return GetUserBet(guessingData.Id, userIdLong);
                }
            }
            catch (Exception ex)
            {
                // 记录详细错误日志
                System.Diagnostics.Debug.WriteLine($"ProcessGuessing error: id={id}, userid={userid}, error={ex.Message}");
            }

            return null;
        }

        private void SetWinningInfo(GuessData guessingData)
        {
            try
            {
                if (_page is Book_View bookView)
                {
                    bookView.WinningOptionId = guessingData.WinningOptionId;
                    bookView.WinningOptionText = guessingData.WinningOptionText;
                }
            }
            catch (Exception ex)
            {
                // 记录详细错误日志
                System.Diagnostics.Debug.WriteLine($"SetWinningInfo error: error={ex.Message}");
            }
        }
    }
}