﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Book_View_modfile : MyPageWap
    {
        private readonly string _instanceName = PubConstant.GetAppString("InstanceName");

        public readonly string KL_NotDownAndUpload = PubConstant.GetAppString("KL_NotDownAndUpload");

        public string formToken = "";

        public wap_bbs_Model bbsVo = new wap_bbs_Model();

        public List<wap2_attachment_Model> imgList = new List<wap2_attachment_Model>();

        public string action = "";

        public string page = "";

        public string INFO = "";

        public string ERROR = "";

        public string book_title = "";

        public string book_content = "";

        public string book_imgTrue = "";

        public string updateInfo = "";

        public string softMoney = "";

        public string softSafe = "";

        public string money = "";

        public string lpage = "";

        public string id = "";

        public long getid;

        public int num = 1;

        public StringBuilder book_img = new StringBuilder();

        public StringBuilder band = new StringBuilder();

        public StringBuilder platform = new StringBuilder();

        public StringBuilder screen = new StringBuilder();

        public StringBuilder serial = new StringBuilder();

        public string formTokenDelAll = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID��论坛模块。", "");
            }
            action = GetRequestValue("action");
            page = GetRequestValue("page");
            id = GetRequestValue("id");
            lpage = GetRequestValue("lpage");
            wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(_instanceName);
            bbsVo = wap_bbs_BLL.GetModel(long.Parse(id));
            if (bbsVo == null)
            {
                ShowTipInfo("已删除！或不存在！", "");
            }
            else if (bbsVo.ischeck == 1L)
            {
                ShowTipInfo("正在审核中！", "");
            }
            else if (bbsVo.book_classid.ToString() != classid)
            {
                ShowTipInfo("栏目ID对不上！可能没有传classid值！", "");
            }
            else if (bbsVo.islock == 1L)
            {
                ShowTipInfo("此帖已锁！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bbsVo.book_classid + "&amp;id=" + bbsVo.id + "&amp;lpage=" + lpage);
            }
            else if (bbsVo.islock == 2L)
            {
                ShowTipInfo("此帖已结！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bbsVo.book_classid + "&amp;id=" + bbsVo.id + "&amp;lpage=" + lpage);
            }
            bbsVo.book_title = WapTool.GetShowImg(bbsVo.book_title, "200", "bbs");
            if (bbsVo.userid.ToString() != siteid)
            {
                base.Response.End();
            }
            if (bbsVo.book_classid.ToString() != classid)
            {
                base.Response.End();
            }
            if (userid != bbsVo.book_pub.ToString())
            {
                CheckManagerLvl("04", classVo.adminusername, "bbs/book_view_admin.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;lpage=" + lpage + "&amp;id=" + id);
            }
            else
            {
                IsLogin(userid, "bbs/book_view_mod.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id + "&amp;lpage=" + lpage);
                //needPassWordToAdmin();
            }
            // ✅ 使用DapperHelper进行安全的参数化查询
            string connectionString = PubConstant.GetConnectionString(_instanceName);
            string selectSql = "SELECT * FROM wap2_attachment WHERE book_type = @BookType AND book_id = @BookId";
            imgList = DapperHelper.Query<wap2_attachment_Model>(connectionString, selectSql, new {
                BookType = "bbs",
                BookId = DapperHelper.SafeParseLong(id, "帖子ID")
            }).ToList();
            if (action != "gomod")
            {
                formToken = Guid.NewGuid().ToString();
                Session["formToken"] = formToken;

                string tokenKeyDelAll = "formTokenList_modfile_del_" + id;
                formTokenDelAll = Guid.NewGuid().ToString();
                if (Session[tokenKeyDelAll] == null)
                {
                     Session[tokenKeyDelAll] = new List<string>();
                }
                var tokenList = Session[tokenKeyDelAll] as List<string>;
                 if(tokenList != null)
                 {
                      if(tokenList.Count >= 5)
                     {
                          tokenList.RemoveAt(0);
                     }
                     tokenList.Add(formTokenDelAll);
                 }

                return;
            }
            try
            {
                string[] values = base.Request.Form.GetValues("book_id");
                string[] values2 = base.Request.Form.GetValues("book_file_title");
                string[] values3 = base.Request.Form.GetValues("book_ext");
                string[] values4 = base.Request.Form.GetValues("book_size");
                string[] values5 = base.Request.Form.GetValues("book_file_info");
                string[] values6 = base.Request.Form.GetValues("book_click");
                int num = 0;
                while (imgList != null && num < imgList.Count)
                {
                    for (int i = 0; i < values.Length; i++)
                    {
                        if (imgList[num].ID.ToString() == values[i])
                        {
                            imgList[num].book_content = ToHtm(values5[i]);
                            imgList[num].book_title = ToHtm(values2[i]);
                            imgList[num].book_ext = ToHtm(values3[i]);
                            imgList[num].book_size = values4[i];
                            try
                            {
                                imgList[num].book_click = long.Parse(values6[i]);
                            }
                            catch (Exception)
                            {
                            }
                            if (imgList[num].book_title.Trim() == "")
                            {
                                INFO = "NULL";
                            }
                            imgList[num].book_title = imgList[num].book_title.Replace("[", "［").Replace("］", "]");
                            CheckGo(bbsVo.viewtype.ToString(), imgList[num].book_content);
                        }
                    }
                    num++;
                }
                if (INFO != "")
                {
                    return;
                }
                if (WapTool.IsLockuser(siteid, userid, classid) > -1L)
                {
                    INFO = "LOCK";
                    return;
                }
                // ✅ 使用DapperHelper进行安全的参数化更新操作
                string lockReason = "{" + userVo.nickname + "(ID" + userVo.userid + ")修改附件" + $"{DateTime.Now:MM-dd HH:mm}" + "}<br/>";
                lockReason += bbsVo.whylock;

                string updateBbsSql = "UPDATE wap_bbs SET isdown = @IsDown, whylock = @WhyLock WHERE id = @PostId";
                DapperHelper.Execute(connectionString, updateBbsSql, new {
                    IsDown = 1L,
                    WhyLock = DapperHelper.LimitLength(lockReason, 1000),
                    PostId = DapperHelper.SafeParseLong(id, "帖子ID")
                });

                // ✅ 批量更新附件信息
                num = 0;
                while (imgList != null && num < imgList.Count)
                {
                    string updateAttachmentSql = @"UPDATE wap2_attachment SET
                        book_title = @BookTitle,
                        book_content = @BookContent,
                        book_ext = @BookExt,
                        book_size = @BookSize,
                        book_click = @BookClick
                        WHERE ID = @AttachmentId";

                    DapperHelper.Execute(connectionString, updateAttachmentSql, new {
                        BookTitle = DapperHelper.LimitLength(imgList[num].book_title, 100),
                        BookContent = imgList[num].book_content,
                        BookExt = DapperHelper.LimitLength(imgList[num].book_ext, 10),
                        BookSize = DapperHelper.LimitLength(imgList[num].book_size, 20),
                        BookClick = imgList[num].book_click,
                        AttachmentId = imgList[num].ID
                    });
                    num++;
                }
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
        }

        public void CheckGo(string viewtype, string content)
        {
            string arryString = WapTool.GetArryString(classVo.smallimg, '|', 21);
            if (arryString != "")
            {
                arryString = "_" + arryString + "_";
                bool flag = false;
                if (int.Parse(viewtype) > 2 || content.IndexOf("[/reply]") > 0 || content.IndexOf("[/buy]") > 0 || content.IndexOf("[/coin]") > 0 || content.IndexOf("[/grade]") > 0)
                {
                    flag = true;
                }
                if (flag && !IsCheckManagerLvl("|00|01|03|04|", classVo.adminusername) && arryString.IndexOf("_" + userVo.SessionTimeout + "_") < 0)
                {
                    ShowTipInfo("您当前的身份不允许发特殊帖。", "bbs/book_view_modfile.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id);
                }
            }
        }
    }
}