/**
 * 统一的头像处理模块
 * 处理所有页面的头像加载、错误处理和超时检测
 *
 * @version 1.0
 * <AUTHOR>
 * @date 2025-01-20
 * @location /Template/JS/Components/AvatarHandler.js
 */

class AvatarHandler {
    static config = {
        timeout: 3000,
        fallbackClass: 'avatar-fallback',
        sizes: {
            small: 'w-8 h-8',
            medium: 'w-12 h-12',
            large: 'w-20 h-20'
        }
    };

    /**
     * 处理头像加载成功
     * @param {HTMLImageElement} img - 图片元素
     * @param {string} size - 尺寸类型 ('auto', 'small', 'medium', 'large')
     */
    static handleLoad(img, size = 'auto') {
        const containerClass = this.getContainerClass(img, size);
        const container = img.closest(containerClass);
        const fallback = container?.querySelector('[data-fallback="true"]');

        // 图片加载成功，显示图片并隐藏首字母 fallback
        img.classList.remove('hidden');
        if (fallback) {
            fallback.classList.add('hidden');
        }
    }

    /**
     * 处理头像加载失败
     * @param {HTMLImageElement} img - 图片元素
     * @param {string} size - 尺寸类型 ('auto', 'small', 'medium', 'large')
     */
    static handleError(img, size = 'auto') {
        const containerClass = this.getContainerClass(img, size);
        const container = img.closest(containerClass);
        const fallback = container?.querySelector('[data-fallback="true"]');

        // 图片加载失败，隐藏图片并显示首字母 fallback
        img.classList.add('hidden');
        img.style.display = 'none';
        if (fallback) {
            fallback.classList.remove('hidden');
        }
    }

    /**
     * 自动检测或获取容器尺寸类名
     * @param {HTMLImageElement} img - 图片元素
     * @param {string} size - 尺寸类型
     * @returns {string} 容器选择器
     */
    static getContainerClass(img, size) {
        // 使用更具体的选择器，避免选中图片元素本身
        if (size !== 'auto') {
            const sizeClass = this.config.sizes[size].replace(' ', '.');
            return `div.${sizeClass}`;
        }

        // 自动检测图片尺寸
        if (img.classList.contains('w-8')) return 'div.w-8.h-8';
        if (img.classList.contains('w-12')) return 'div.w-12.h-12';
        if (img.classList.contains('w-20')) return 'div.w-20.h-20';
        if (img.classList.contains('w-24')) return 'div.w-24.h-24';
        if (img.classList.contains('w-28')) return 'div.w-28.h-28';

        // 默认返回小尺寸
        return 'div.w-8.h-8';
    }

    /**
     * 设置头像加载超时检查
     * @param {HTMLImageElement} img - 图片元素
     * @param {number} timeoutMs - 超时时间（毫秒）
     */
    static setLoadTimeout(img, timeoutMs = null) {
        const timeout = timeoutMs || this.config.timeout;
        
        setTimeout(() => {
            // 如果图片仍然隐藏且没有完成加载，强制显示首字母
            if (img.classList.contains('hidden') && !img.complete) {
                this.handleError(img);
            }
        }, timeout);
    }

    /**
     * 初始化页面中的所有头像
     * 处理已经加载完成的图片（来自缓存）
     */
    static initPageAvatars() {
        document.querySelectorAll('img[data-avatar-src]').forEach(img => {
            if (img.complete) {
                if (img.naturalWidth > 0) {
                    // 图片加载成功
                    this.handleLoad(img);
                } else {
                    // 图片加载失败
                    this.handleError(img);
                }
            } else {
                // 设置超时检查
                this.setLoadTimeout(img);
            }
        });
    }
}

// ==================== 全局函数，供模板调用 ====================

/**
 * 处理大头像加载成功
 * @param {HTMLImageElement} img - 图片元素
 */
function handleAvatarLoad(img) {
    AvatarHandler.handleLoad(img, 'large');
}

/**
 * 处理大头像加载失败
 * @param {HTMLImageElement} img - 图片元素
 */
function handleAvatarError(img) {
    AvatarHandler.handleError(img, 'large');
}

/**
 * 处理小头像加载成功
 * @param {HTMLImageElement} img - 图片元素
 */
function handleSmallAvatarLoad(img) {
    AvatarHandler.handleLoad(img, 'small');
}

/**
 * 处理小头像加载失败
 * @param {HTMLImageElement} img - 图片元素
 */
function handleSmallAvatarError(img) {
    AvatarHandler.handleError(img, 'small');
}

/**
 * 处理中等头像加载成功
 * @param {HTMLImageElement} img - 图片元素
 */
function handleMediumAvatarLoad(img) {
    AvatarHandler.handleLoad(img, 'medium');
}

/**
 * 处理中等头像加载失败
 * @param {HTMLImageElement} img - 图片元素
 */
function handleMediumAvatarError(img) {
    AvatarHandler.handleError(img, 'medium');
}

/**
 * 检查头像加载超时（向后兼容）
 * @param {HTMLImageElement} img - 图片元素
 * @param {number} timeoutMs - 超时时间
 */
function checkAvatarLoadTimeout(img, timeoutMs = 3000) {
    AvatarHandler.setLoadTimeout(img, timeoutMs);
}

// ==================== 自动初始化 ====================

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    AvatarHandler.initPageAvatars();
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AvatarHandler;
}
