﻿using KeLin.ClassManager.Model;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using YaoHuo.Plugin.WebSite.Tool;
using System.Linq;
using System.Runtime.Caching;
using System;

namespace YaoHuo.Plugin.Tool
{
    /// <summary>
    /// 黑名单工具（带缓存优化）
    /// </summary>
    public class UserBlockingService
    {
        // ✅ 黑名单缓存：30分钟缓存，避免永久缓存风险
        private static readonly MemoryCache _blockListCache = MemoryCache.Default;

        // ✅ 缓存过期时间：30分钟，避免永久缓存带来的风险
        private static readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(30);

        /// <summary>
        /// 创建缓存策略（每次调用都创建新的过期时间）
        /// </summary>
        private static CacheItemPolicy CreateCachePolicy()
        {
            return new CacheItemPolicy
            {
                Priority = CacheItemPriority.Default,
                SlidingExpiration = ObjectCache.NoSlidingExpiration,
                AbsoluteExpiration = DateTimeOffset.Now.Add(_cacheExpiry)
            };
        }
        /// <summary>
        /// 添加黑名单用户
        /// </summary>
        /// <param name="userInfo">用户信息</param>
        /// <param name="connStr">数据库连接字符串</param>
        /// <param name="addUserID">要拉黑的用户ID</param>
        /// <returns></returns>
        public static string AddBlackUser(user_Model userInfo, string connStr, string addUserID)
        {
            //黑名单上限
            var blackUp = 20;
            //会员用户增加黑名单上限
            switch (userInfo.SessionTimeout)
            {
                case 101:
                case 358:
                    blackUp = 30;
                    break;

                case 105:
                case 140:
                case 180:
                    blackUp = 40;
                    break;

                default:
                    break;
            }

            //获取拉取黑名单数量 - 使用DapperHelper安全查询（添加siteid条件）
            string countSql = "SELECT COUNT(*) FROM wap_friends WHERE friendtype = 1 AND siteid = @SiteId AND userid = @UserId";
            var friendsCount = DapperHelper.ExecuteScalar<int>(connStr, countSql, new {
                SiteId = userInfo.siteid,
                UserId = DapperHelper.SafeParseLong(userInfo.userid.ToString(), "用户ID")
            });

            //白名单用户
            var noBlockList = new List<string>()
            {
                "1000",
                "11637",
                "36787",
            };

            // 移除前导零并检查
            var processedUserID = Regex.Replace(addUserID, @"^0+", "");

            //白名单不能黑
            if (noBlockList.Contains(processedUserID))
            {
                return "NOTBLACK";
            }
            //经验未达到1000却拉了一个黑名单（准备拉第二个黑名单）
            else if (userInfo.expr <= 1000 && friendsCount >= blackUp - 10)
            {
                return "UPMAX";
            }
            else if (userInfo.expr <= 2000 && friendsCount >= blackUp - 9)
            {
                return "UPMAX";
            }
            else if (userInfo.expr <= 5000 && friendsCount >= blackUp - 8)
            {
                return "UPMAX";
            }
            else if (userInfo.expr <= 10000 && friendsCount >= blackUp - 7)
            {
                return "UPMAX";
            }
            else if (userInfo.expr <= 50000 && friendsCount >= blackUp - 6)
            {
                return "UPMAX";
            }
            else if (userInfo.expr <= 100000 && friendsCount >= blackUp - 5)
            {
                return "UPMAX";
            }
            else if (userInfo.expr <= 200000 && friendsCount >= blackUp - 4)
            {
                return "UPMAX";
            }
            else if (userInfo.expr <= 300000 && friendsCount >= blackUp - 3)
            {
                return "UPMAX";
            }
            else if (userInfo.expr <= 500000 && friendsCount >= blackUp - 2)
            {
                return "UPMAX";
            }
            else if (userInfo.expr <= 800000 && friendsCount >= blackUp - 1)
            {
                return "UPMAX";
            }
            else if (userInfo.expr <= 1000000 && friendsCount >= blackUp - 0)
            {
                return "UPMAX";
            }
            //限制黑名单上限
            else if (friendsCount >= blackUp)
            {
                return "UPMAX";
            }

            // ✅ 如果验证通过，执行添加黑名单操作
            try
            {
                // 先获取被拉黑用户的昵称
                string friendNickname = "";
                try
                {
                    string getUserSql = "SELECT nickname FROM [user] WHERE userid = @UserId AND siteid = @SiteId";
                    friendNickname = DapperHelper.ExecuteScalar<string>(connStr, getUserSql, new {
                        UserId = DapperHelper.SafeParseLong(addUserID, "好友用户ID"),
                        SiteId = userInfo.siteid
                    }) ?? "";
                }
                catch (Exception)
                {
                    // 如果获取失败，使用空字符串
                }

                // 执行数据库插入操作（包含完整字段，包括rank）
                string insertSql = "INSERT INTO wap_friends (siteid, userid, frienduserid, friendusername, friendnickname, rank, friendtype, addtime) VALUES (@SiteId, @UserId, @FriendUserId, @FriendUserName, @FriendNickname, @Rank, 1, GETDATE())";
                DapperHelper.Execute(connStr, insertSql, new {
                    SiteId = userInfo.siteid,
                    UserId = userInfo.userid,
                    FriendUserId = DapperHelper.SafeParseLong(addUserID, "好友用户ID"),
                    FriendUserName = "", // 通常为空
                    FriendNickname = DapperHelper.LimitLength(friendNickname, 50),
                    Rank = 0 // 默认rank为0
                });

                // ✅ 数据库操作成功后立即清除缓存，确保下次查询获取最新数据
                ClearUserBlockCache(userInfo.userid.ToString());

                return "OK";
            }
            catch (Exception)
            {
                return "ERROR";
            }
        }

        /// <summary>
        /// 是否是黑名单用户（带缓存优化）
        /// </summary>
        /// <param name="connStr">数据库连接字符串</param>
        /// <param name="muUserID">用户ID</param>
        /// <param name="isUserID">验证的用户ID</param>
        /// <returns></returns>
        public static bool IsBlackUser(string connStr, string muUserID, string isUserID)
        {
            if (muUserID.IsNullOrZero() || isUserID.IsNullOrZero())
            {
                return false;
            }

            // ✅ 使用缓存优化的版本
            var blockedUsers = GetBlockedUserIds(connStr, muUserID);
            if (long.TryParse(isUserID, out long targetUserId))
            {
                return blockedUsers.Contains(targetUserId);
            }
            
            return false;
        }

        /// <summary>
        /// 获取被当前用户拉黑的用户ID列表（带缓存优化）
        /// </summary>
        /// <param name="connStr">数据库连接字符串</param>
        /// <param name="userId">用户ID</param>
        /// <returns>被拉黑的用户ID列表</returns>
        public static List<long> GetBlockedUserIds(string connStr, string userId)
        {
            if (userId.IsNullOrZero())
            {
                return new List<long>();
            }

            string cacheKey = $"blocked_{userId}";

            // ✅ 先查缓存
            if (_blockListCache.Get(cacheKey) is List<long> cachedBlocked)
            {
                return cachedBlocked;
            }

            // ✅ 缓存未命中，查询数据库
            try
            {
                string sql = "SELECT frienduserid FROM wap_friends WHERE friendtype = 1 AND userid = @UserId";
                var blockedUsers = DapperHelper.Query<long>(connStr, sql, new {
                    UserId = DapperHelper.SafeParseLong(userId, "用户ID")
                });

                var result = blockedUsers?.ToList() ?? new List<long>();

                // ✅ 存入缓存（10分钟缓存）
                _blockListCache.Set(cacheKey, result, CreateCachePolicy());

                return result;
            }
            catch (Exception)
            {
                // ✅ 数据库查询失败时的降级处理
                return new List<long>(); // 返回空列表，不影响功能
            }
        }

        /// <summary>
        /// 获取拉黑当前用户的VIP用户ID列表（带缓存优化）
        /// </summary>
        /// <param name="connStr">数据库连接字符串</param>
        /// <param name="userId">用户ID</param>
        /// <returns>拉黑当前用户的VIP用户ID列表</returns>
        public static List<long> GetVipUsersWhoBlockedMe(string connStr, string userId)
        {
            if (userId.IsNullOrZero())
            {
                return new List<long>();
            }

            string cacheKey = $"vip_blocked_me_{userId}";

            // ✅ 先查缓存
            if (_blockListCache.Get(cacheKey) is List<long> cachedVipBlockers)
            {
                return cachedVipBlockers;
            }

            // ✅ 缓存未命中，查询数据库
            try
            {
                //会员角色ID
                var vipRoleIDs = new int[] { 101, 105, 140, 180, 358 };

                // 获取拉黑当前用户的VIP用户列表
                string sql = @"SELECT t1.userid
                              FROM wap_friends t1
                              INNER JOIN UserVO_View t2 ON t1.userid = t2.userid
                              WHERE t1.friendtype = 1
                              AND t1.frienduserid = @UserId
                              AND t2.SessionTimeout IN @VipRoleIds";

                var vipBlockers = DapperHelper.Query<long>(connStr, sql, new {
                    UserId = DapperHelper.SafeParseLong(userId, "用户ID"),
                    VipRoleIds = vipRoleIDs
                });

                var result = vipBlockers?.ToList() ?? new List<long>();

                // ✅ 存入缓存（10分钟缓存）
                _blockListCache.Set(cacheKey, result, CreateCachePolicy());

                return result;
            }
            catch (Exception)
            {
                // ✅ 数据库查询失败时的降级处理
                return new List<long>(); // 返回空列表，不影响功能
            }
        }

        /// <summary>
        /// 移除黑名单用户（带缓存清除）
        /// </summary>
        /// <param name="userId">当前用户ID</param>
        /// <param name="removeUserID">要移除的用户ID</param>
        /// <param name="connStr">数据库连接字符串</param>
        /// <param name="siteId">站点ID</param>
        /// <returns>操作结果</returns>
        public static string RemoveBlackUser(string userId, string removeUserID, string connStr, string siteId)
        {
            try
            {
                // 执行数据库删除操作（添加siteid条件）
                string deleteSql = "DELETE FROM wap_friends WHERE siteid = @SiteId AND userid = @UserId AND frienduserid = @FriendUserId AND friendtype = 1";
                int rowsAffected = DapperHelper.Execute(connStr, deleteSql, new {
                    SiteId = DapperHelper.SafeParseLong(siteId, "站点ID"),
                    UserId = DapperHelper.SafeParseLong(userId, "用户ID"),
                    FriendUserId = DapperHelper.SafeParseLong(removeUserID, "好友用户ID")
                });

                if (rowsAffected > 0)
                {
                    // ✅ 数据库操作成功后立即清除缓存
                    ClearUserBlockCache(userId);
                    return "OK";
                }
                else
                {
                    return "NOTFOUND"; // 没有找到要删除的记录
                }
            }
            catch (Exception)
            {
                return "ERROR";
            }
        }

        /// <summary>
        /// 清除用户的黑名单缓存（在用户修改黑名单时调用）
        /// </summary>
        /// <param name="userId">用户ID</param>
        public static void ClearUserBlockCache(string userId)
        {
            if (userId.IsNullOrZero()) return;

            try
            {
                string blockedCacheKey = $"blocked_{userId}";
                string vipBlockedCacheKey = $"vip_blocked_me_{userId}";

                _blockListCache.Remove(blockedCacheKey);
                _blockListCache.Remove(vipBlockedCacheKey);

                // ✅ 清除页面级缓存（可能包含黑名单过滤结果）
                ClearPageRelatedCache(userId);
            }
            catch (Exception)
            {
                // ✅ 缓存清除失败不应该影响主要功能
            }
        }

        /// <summary>
        /// 清除与黑名单相关的页面级缓存
        /// </summary>
        /// <param name="userId">用户ID</param>
        private static void ClearPageRelatedCache(string userId)
        {
            try
            {
                // ✅ 清除所有 BBS 列表缓存（因为可能包含黑名单过滤结果）
                var keysToRemove = new List<string>();
                
                // 收集需要清除的 BBS 列表缓存键
                foreach (var key in KeLin.ClassManager.Tool.WapTool.DataBBSArray.Keys.ToList())
                {
                    if (key.StartsWith("bbs") || key.StartsWith("topPosts"))
                    {
                        keysToRemove.Add(key);
                    }
                }
                
                // 批量清除 BBS 列表缓存
                foreach (var key in keysToRemove)
                {
                    KeLin.ClassManager.Tool.WapTool.DataBBSArray.Remove(key);
                }
                
                // 收集需要清除的统计缓存键
                keysToRemove.Clear();
                foreach (var key in KeLin.ClassManager.Tool.WapTool.DataTempArray.Keys.ToList())
                {
                    if (key.StartsWith("bbsTotal"))
                    {
                        keysToRemove.Add(key);
                    }
                }
                
                // 批量清除统计缓存
                foreach (var key in keysToRemove)
                {
                    KeLin.ClassManager.Tool.WapTool.DataTempArray.Remove(key);
                }
            }
            catch (Exception)
            {
            }
        }

        /// <summary>
        /// 获取缓存统计信息（用于监控和调试）
        /// </summary>
        /// <returns>缓存统计信息</returns>
        public static string GetCacheStats()
        {
            try
            {
                long cacheCount = _blockListCache.GetCount();
                return $"黑名单缓存统计: 当前缓存条目数 = {cacheCount}";
            }
            catch (Exception)
            {
                return $"获取缓存统计失败: 未知错误";
            }
        }

        /// <summary>
        /// 清除所有黑名单缓存（管理员功能，谨慎使用）
        /// </summary>
        public static void ClearAllBlockCache()
        {
            try
            {
                // 获取所有缓存键
                var cacheKeys = new List<string>();
                foreach (var item in _blockListCache)
                {
                    if (item.Key.StartsWith("blocked_") || item.Key.StartsWith("vip_blocked_me_"))
                    {
                        cacheKeys.Add(item.Key);
                    }
                }

                // 清除所有黑名单相关缓存
                foreach (var key in cacheKeys)
                {
                    _blockListCache.Remove(key);
                }
            }
            catch (Exception)
            {
            }
        }

        /// <summary>
        /// 获取排除用户脚本（已弃用，建议使用GetBlockedUserIds和GetVipUsersWhoBlockedMe）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="name">字段名称</param>
        /// <returns></returns>
        [System.Obsolete("此方法存在SQL注入风险，建议使用GetBlockedUserIds和GetVipUsersWhoBlockedMe方法")]
        public static string GetExcludeUserSql(string userId, string name = "book_pub")
        {
            // 为了向后兼容，暂时保留此方法，但标记为过时
            // 实际使用中应该调用新的安全方法
            return string.Empty;
        }
    }
}