// 获取元素
const dengjiSpan = document.querySelector('.dengji');
const touxianSpan = document.querySelector('.touxian');
const qianmingElements = document.querySelectorAll('.qianming');
const onlineSpan = document.querySelector('.online');
const images = document.querySelectorAll('.xunzhangtupian img');

// 设置初始状态
const isDengjiVisible = localStorage.getItem('isDengjiVisible') !== 'false';
const isFirstImageVisible = localStorage.getItem('isFirstImageVisible') !== 'false';
let shouldHideImages = localStorage.getItem("hideMedalImages");
let hidebbsqianming = localStorage.getItem("hidebbsqianming") === 'true';

// 切换显示状态的函数
function toggleDisplay(element1, element2, key, value) {
    element1.style.display = 'none';
    element2.style.display = 'inline';
    localStorage.setItem(key, value);
}

// 隐藏空白签名的函数
function hideEmptySignature(elements) {
    // 新增逻辑：检查是否需要隐藏 "qianming" 元素
    if (hidebbsqianming) {
        elements.forEach((element) => {
            element.style.display = 'none';
        });
    } else {
        elements.forEach((element) => {
            const qianmingneirong = element.querySelector('.qianmingneirong');
            if (qianmingneirong.textContent.trim() === '') {
                element.style.display = 'none';
            }
        });
    }
}

// 切换图片显示状态的函数
function toggleImageDisplay(parentElement, key) {
    const secondImage = parentElement.children[1];
    secondImage.style.display = secondImage.style.display === 'none' ? 'inline' : 'none';
    localStorage.setItem(key, secondImage.style.display !== 'none');
}

// 隐藏指定图片的函数
function hideSpecifiedImages(images, keyword) {
    images.forEach(function (image) {
        const imageUrl = image.getAttribute('src');
        if (imageUrl.includes(keyword)) {
            image.style.display = 'none';
        }
    });
}

// 根据条件显示或隐藏图片的函数
function showOrHideImages(images, shouldHide, maxCount) {
    if (shouldHide === "true") {
        let count = 0;
        images.forEach(function (image) {
            if (image.style.display !== 'none') {
                if (count >= maxCount) {
                    image.style.display = 'none';
                }
                count++;
            }
        });
    } else {
        images.forEach(function (image) {
            if (image.style.display === 'none') {
                image.style.display = 'inline';
            }
        });
    }
}

// 切换等级头衔显示
dengjiSpan.style.display = isDengjiVisible ? 'inline' : 'none';
touxianSpan.style.display = isDengjiVisible ? 'none' : 'inline';

dengjiSpan.addEventListener('click', function() {
    toggleDisplay(dengjiSpan, touxianSpan, 'isDengjiVisible', false);
});

touxianSpan.addEventListener('click', function() {
    toggleDisplay(touxianSpan, dengjiSpan, 'isDengjiVisible', true);
});

// 隐藏空白的签名
hideEmptySignature(qianmingElements);

// 新增功能：点击第一张图片隐藏第二张图片
onlineSpan.children[1].style.display = isFirstImageVisible ? 'inline' : 'none';

onlineSpan.addEventListener('click', function() {
    toggleImageDisplay(onlineSpan, 'isFirstImageVisible');
});

// 隐藏包含 "XinZhang" 的图片
hideSpecifiedImages(images, 'XinZhang');

// 根据 shouldHideImages 的值显示或隐藏图片
showOrHideImages(images, shouldHideImages, 10);

// 新增功能：隐藏 "qianming" 元素
if (hidebbsqianming) {
    qianmingElements.forEach((element) => {
        element.style.display = 'none';
    });
}
