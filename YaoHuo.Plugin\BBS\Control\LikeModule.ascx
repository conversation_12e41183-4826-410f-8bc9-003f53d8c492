﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="LikeModule.ascx.cs" Inherits="YaoHuo.Plugin.BBS.Control.LikeModule" %>
<div class="paw-button-container">
    <a href="#" class="paw-button<%= HasLiked ? " liked" : "" %>" 
       data-id="<%= PostId %>" 
       data-siteid="<%= SiteId %>" 
       data-classid="<%= ClassId %>" 
       data-vpage="<%= CurrentPage %>" 
       data-lpage="<%= LPage %>" 
       data-liked="<%= HasLiked ? "true" : "false" %>">
        <div class="text"><svg xmlns:xlink="http://www.w3.org/1999/xlink"><use xlink:href="#heart"></use></svg></div>
        <div class="number-wrapper">
            <div class="number-container">
                <span><%= LikeCount.ToString("D3") %></span>
            </div>
        </div>
        <div class="paws">
            <svg xmlns:xlink="http://www.w3.org/1999/xlink" class="paw"><use xlink:href="#paw"></use></svg>
            <div class="paw-effect"><div></div></div>
            <svg xmlns:xlink="http://www.w3.org/1999/xlink" class="paw-clap"><use xlink:href="#paw-clap"></use></svg>
        </div>
    </a>
</div>
<script type="text/javascript" src="/NetCSS/JS/BookView/PawClap-Icons.js" defer></script>
<script type="text/javascript" src="/NetCSS/JS/BookView/PawClap-Script.js?6" defer></script>
<link href="/NetCSS/CSS/PawClap-Style.css" rel="stylesheet" type="text/css"/>