﻿.forum-container {
    /* font-family: <PERSON>m<PERSON><PERSON>, <PERSON><PERSON>, sans-serif; */
    line-height: 1.4;
    color: #333;
    background: #f4f4f4;
    margin: 0;
}

.forum-post {
    background: #fff;
    padding: 7px;
    border-top: 1px solid #f0f0f0;
    position: relative;
    padding-bottom: 2px;
}

.post-header {
    display: flex;
    flex-direction: column;
    margin: 3px;
    margin-top: 10px;
}

.user-name {
    margin-top: -8px;
    font-size: 0.9rem;
    font-weight: 500;
}

.post-date {
    font-size: 12px;
    color: #aaa;
}

.floor-info, .post-date {
    font-family: -apple-system,Microsoft YaHei,PingFang SC;
}

.floor-info {
    font-size: 13px;
    color: #aaa;
    position: absolute;
    top: 10px;
    right: 10px;
}

.post-content {
    line-height: 28px;
    line-break: anywhere;
    overflow-wrap: break-word;
    margin: 3px 0 -5px 3px;
}

.admin-actions {
    font-size: 0.8rem;
    text-align: right;
}

    .admin-actions a {
        font-size: 14px;
        color: #6e7775;
        text-decoration: none;
        padding: 0 1px 0 3px;
        padding: 6px;
        margin-right: -5px;
        margin-top: -5px;
    }

        .admin-actions a:hover {
            transform: scale(1.05);
        }

        .admin-actions a:active {
            background-color: #f0f0f0;
        }

.admin-actions {
    display: flex;
    justify-content: space-between;
}

.reward-info {
    order: -1;
    color: #6e7775;
    margin-left: 2px;
    margin-top: 2px;
}

.operate {
    display: flex;
    margin-left: auto;
    transform: translateY(3px);
    margin-bottom: -6px;
}

.reward-info::before {
    content: "";
    display: inline-block;
    width: 18px;
    height: 18px;
    margin-right: 1px;
    background-image: url(/NetCSS/SVG/金币.svg);
    background-size: cover;
    background-repeat: no-repeat;
    vertical-align: middle;
}

.reward-number {
    font-family: Arvo;
    font-size: 12px;
}

.user-id a {
    font-size: 14px;
}

.user-nick a {
    margin-right: 1px;
    font-size: 16px;
}

.user-nick img {
    margin-right: -2px;
    max-height: 20px;
}

.reward-number {
    position: relative;
    top: 1px;
}

.floor-change,
.floor-delete,
.floor-give,
.floor-top {
    margin-right: 3px;
    font-size: 13px;
    color: #aaa;
}

.floor-number {
    margin: 0 0 0 1px;
}

.replyicon {
    transform: translateY(-2px);
}

.giveicon {
    transform: translateY(-3px);
}

.dropdown {
    margin-right: 3px;
    display: inline-block;
    position: relative;
    vertical-align: top;
}

.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    background-color: #f9f9f9;
    min-width: 60px;
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2);
    z-index: 1;
}

    .dropdown-content a {
        color: #000;
        padding: 12px;
        text-decoration: none;
        display: block;
        text-align: center;
    }

        .dropdown-content a:hover {
            background-color: #f1f1f1;
        }

.dropdown:hover .dropdown-content {
    display: block;
}

.floor-info {
    align-items: center;
    display: flex;
    justify-content: flex-start;
}

.floor-name {
    display: inline-block;
    margin-left: 10px;
}

.replay-other {
    padding-right: 1px;
}

.spacer {
    padding: 0 1px;
}

.forum-post.bgColor {
    background-color: #fbfbfb;
}

.floor-highlight {
    background: #fff8dc !important;
    transition: background-color 0.5s;
}