// 动态加载CSS
function loadCSS(href) {
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = href;
    document.head.appendChild(link);
}

// 加载上传相关的CSS
loadCSS('/netcss/css/upload-resource.css');

// 添加所有自定义样式
const style = document.createElement('style');
style.textContent = `
    /* 容器基础样式 */
    .upload-container {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        margin: 0;
        width: 93%;
        max-width: 690px;
        border: none;
        border-radius: 0.5rem;
        padding: .5rem;
        background: white;
        padding-top: 20px;
        overflow: hidden;
        outline: none;
        max-height: 90vh;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    .upload-container::backdrop {
        background: rgba(0, 0, 0, 0.5);
    }
    
    .upload-container .form-group label {
        margin-left: 1px;
        margin-bottom: 4px;
    }

    /* 交互控制样式 */
    .upload-container *:not(input[type="text"]) {
        caret-color: transparent !important;
        user-select: none !important;
    }
    
    .upload-container input[type="text"] {
        caret-color: auto !important;
        user-select: text !important;
    }
    
    .file-select-area::after {
        content: none !important;
    }
    
    .upload-container #previewArea,
    .upload-container .file-select-area {
        caret-color: transparent !important;
        user-select: none !important;
    }

    /* 修改预览区域样式 */
    .upload-container #previewArea {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        min-height: 40vh;
    }

    /* 修改预览图片样式 */
    .upload-container #imagePreview {
        max-width: 100%;
        max-height: 40vh;
        border-radius: 7px;
        object-fit: contain;
        margin: 0 auto;
        display: block;
    }

    /* 图片信息样式调整 */
    .photo-info {
        width: 100%;
        text-align: center;
        margin-top: 10px;
        padding: 0 10px;
        box-sizing: border-box;
    }

    .photo-name {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* 在预览模式下隐藏底部提示 */
    .upload-container #previewArea:not([style*="display: none"]) ~ .triangle-alert {
        display: none;
    }

    /* 添加 file-select-area 样式 */
    .file-select-area {
        padding: 1rem 1rem;
        min-height: 40vh;
    }

    /* 添加媒体查询 */
    @media screen and (max-width: 768px) {
        .upload-container #previewArea {
            min-height: 30vh;
        }
        
        .file-select-area {
            min-height: 30vh !important;
        }
    }

    /* 隐藏元素的默认样式 */
    .upload-container #previewArea,
    .upload-container input[type="file"],
    .upload-container #submitBtn,
    .upload-container iframe[name="uploadFrame"] {
        display: none;
    }

    /* previewArea 的额外样式 */
    .upload-container #previewArea {
        width: 100%;
    }

    body.modal-open {
        position: fixed;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    /* 错误提示对话框样式 */
    .upload-dialog {
        position: fixed;
        background: white;
        border-radius: 8px;
        padding: 24px;
        width: 90%;
        max-width: 400px;
        border: none;
        animation: dialogFadeIn 0.2s ease-out;
    }

    .upload-dialog::backdrop {
        background: rgba(0, 0, 0, 0.5);
    }

    .upload-dialog-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
    }

    .upload-dialog-icon {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        color: #e11d48;
    }

    .upload-dialog-title {
        color: #e11d48;
        font-size: 18px;
        font-weight: bold;
        margin: 0;
    }

    .upload-dialog-content {
        color: #4b5563;
        font-size: 16px;
        margin-bottom: 24px;
    }

    .upload-dialog-footer {
        display: flex;
        justify-content: flex-end;
    }

    .upload-dialog-button {
        background: #212936;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        transition: background 0.2s;
    }

    .upload-dialog-button:hover {
        background: #18181B;
    }

    @keyframes dialogFadeIn {
        from { opacity: 0; transform: translateY(-20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .upload-status-message {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        z-index: 10000;
        font-size: 0.875rem;
        max-width: 90%;
        text-align: center;
    }
    
    .message-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
    
    .animate-spin {
        animation: spin 1s linear infinite;
        width: 1.25rem;
        height: 1.25rem;
    }
`;
document.head.appendChild(style);

// 上传对话框类
class AlbumUploader {
    constructor(config) {
        this.config = config;
        this.dialog = null;
        this.errorDialog = null;
        this.createErrorDialog();
    }

    // 创建错误提示对话框
    createErrorDialog() {
        this.errorDialog = document.createElement('dialog');
        this.errorDialog.className = 'upload-dialog';
        this.errorDialog.innerHTML = `
            <div class="upload-dialog-header">
                <svg class="upload-dialog-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <h3 class="upload-dialog-title">上传失败</h3>
            </div>
            <div class="upload-dialog-content"></div>
            <div class="upload-dialog-footer">
                <button class="upload-dialog-button" onclick="this.closest('dialog').close()">确定</button>
            </div>
        `;
        document.body.appendChild(this.errorDialog);

        // 点击遮罩关闭
        this.errorDialog.addEventListener('click', (e) => {
            if (e.target === this.errorDialog) {
                this.errorDialog.close();
            }
        });
    }

    // 显示错提示
    showError(message) {
        const content = this.errorDialog.querySelector('.upload-dialog-content');
        content.textContent = message;
        this.errorDialog.showModal();
    }

    // 显示上传对话框
    show() {
        this.dialog = document.createElement('dialog');
        this.dialog.className = 'upload-container';
        
        this.dialog.innerHTML = `
            <form name="gt" action="/album/admin_WAPadd.aspx" method="post" enctype="multipart/form-data">
                <div class="form-group">
                    <label>图片标题</label>
                    <input type="text" name="book_title" class="form-control" value="自定义头像" required autofocus="false">
                </div>

                <div class="file-select-area" id="uploadArea">
                    <div id="defaultUploadUI">
                        <div class="big-upload-icon">+</div>
                        <div class="upload-text">点击选择图片</div>
                    </div>
                    <div id="previewArea">
                        <img id="imagePreview"/>
                        <div class="photo-info">
                            <div class="photo-name">
                                <span id="fileName"></span>
                                <span style="margin:0 2px"></span>
                                <span id="fileSize"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <input type="file" name="book_file" accept=".jpg,.jpeg,.png,.gif" required>
                <input type="hidden" name="action" value="gomod">
                <input type="hidden" name="classid" value="0">
                <input type="hidden" name="siteid" value="${this.config.siteId}">
                <input type="hidden" name="num" value="1">
                <input type="hidden" name="smalltypeid" value="0">
                <input type="hidden" name="toclassid" value="0">
                <input type="hidden" name="ishidden" value="1">
                <button type="submit" id="submitBtn" style="display:none;">上传</button>
            </form>
        `;

        document.body.appendChild(this.dialog);
        
        // 绑定事件
        this.bindEvents();
        
        this.dialog.showModal();
        
        // 在对话框显示后，主动移除所有输入框的焦点
        setTimeout(() => {
            const inputs = this.dialog.querySelectorAll('input');
            inputs.forEach(input => input.blur());
        }, 0);
        
        document.body.classList.add('modal-open');
        
        this.dialog.addEventListener('close', () => {
            document.body.classList.remove('modal-open');
        });
    }

    // 绑定所有事件处理
    bindEvents() {
        // 文件选择
        const fileInput = this.dialog.querySelector('input[type="file"]');
        const uploadArea = this.dialog.querySelector('#uploadArea');
        const form = this.dialog.querySelector('form');
        
        uploadArea.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        
        // 拖放处理
        uploadArea.addEventListener('dragover', this.handleDragOver);
        uploadArea.addEventListener('dragleave', this.handleDragLeave);
        uploadArea.addEventListener('drop', (e) => this.handleDrop(e));
        
        // 表单提交处理
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSubmit(form);
        });
        
        // 添加点击遮罩关闭的功能
        this.dialog.addEventListener('click', (e) => {
            if (e.target === this.dialog) {
                this.dialog.close();
            }
        });
    }

    // 新增处理提交的方法
    async handleSubmit(form) {
        let msgDiv;
        try {
            msgDiv = this.showMessage("准备上传...");
            
            const formData = new FormData(form);
            msgDiv.innerHTML = "正在上传...";
            
            const response = await fetch('/album/admin_WAPadd.aspx', {
                method: 'POST',
                body: formData
            });
            
            msgDiv.innerHTML = "正在处理响应...";
            const html = await response.text();
            
            // 解析响应
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const tipElement = doc.querySelector('.tip');
            
            if (tipElement) {
                const tipText = tipElement.textContent.trim();
                msgDiv.innerHTML = `收到响应: ${tipText}`;
                
                if (tipText.includes('上传成功')) {
                    // 获取表单数据
                    const imagePreview = this.dialog.querySelector('#imagePreview');
                    const title = form.querySelector('input[name="book_title"]').value;
                    
                    // 创建新的相册项
                    const photoContainer = document.querySelector('.grid');
                    if (photoContainer) {
                        const newPhotoDiv = document.createElement('div');
                        newPhotoDiv.className = 'group relative overflow-hidden rounded-lg border bg-white shadow-sm hover:shadow-md';
                        
                        newPhotoDiv.innerHTML = `
                            <a href="javascript:void(0);" onclick="showImage('${imagePreview.src}', 'new')" class="block">
                                <div class="aspect-square relative">
                                    <img src="${imagePreview.src}" 
                                         alt="相册图片" 
                                         class="aspect-square w-full h-full object-cover">
                                    <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
                                        <h2 class="font-medium text-white">${title}</h2>
                                    </div>
                                </div>
                            </a>
                            <button onclick="showDeleteDialog('${this.config.httpStart}album/albumlist_del.aspx?action=del&siteid=${this.config.siteId}&classid=0&id=new')" 
                                    class="absolute bottom-4 right-4 text-white opacity-70 hover:opacity-100 z-10 bg-transparent border-0">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                </svg>
                            </button>
                        `;
                        
                        // 插入到网格的开头
                        const firstChild = photoContainer.firstChild;
                        if (firstChild) {
                            photoContainer.insertBefore(newPhotoDiv, firstChild);
                        } else {
                            photoContainer.appendChild(newPhotoDiv);
                        }
                    }
                    
                    // 关闭上传对话框
                    this.dialog.close();
                    
                    // 清空表单
                    form.reset();
                    this.dialog.querySelector('#previewArea').style.display = 'none';
                    this.dialog.querySelector('#defaultUploadUI').style.display = 'block';
                    this.dialog.querySelector('#submitBtn').style.display = 'none';
                    setTimeout(() => msgDiv.remove(), 1000);
                } else {
                    // 显示具体的错误信息
                    setTimeout(() => {
                        msgDiv.remove();
                        this.showError(tipText);
                    }, 1000);
                }
            }
        } catch(e) {
            console.error('Upload error:', e);
            if (msgDiv) {
                msgDiv.innerHTML = `出错: ${e.message}`;
                setTimeout(() => {
                    msgDiv.remove();
                    this.showError('上传过程中发生错误，请重试');
                }, 1000);
            }
        }
    }

    // 添加文件处理相关函数
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return `<span class="size-number">${parseFloat((bytes / Math.pow(k, i)).toFixed(2))}</span><span class="size-unit">${sizes[i]}</span>`;
    }

    handleFileSelect(input) {
        const file = input.target.files[0];
        if (!file) return;
        this.processFile(file);
    }

    processFile(file) {
        if (!file) return;
        
        const defaultUI = this.dialog.querySelector('#defaultUploadUI');
        const previewArea = this.dialog.querySelector('#previewArea');
        const submitBtn = this.dialog.querySelector('#submitBtn');
        const uploadArea = this.dialog.querySelector('#uploadArea');
        
        if (!file.type.match('image.*')) {
            this.showErrorDialog('文件类型错误', '只能上传图片文件！');
            this.dialog.querySelector('input[name=book_file]').value = '';
            return;
        }

        // 添加图片真实性验证
        this.validateImage(file).then(isValid => {
            if (!isValid) {
                this.dialog.querySelector('input[name=book_file]').value = '';
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                this.dialog.querySelector('#imagePreview').src = e.target.result;
                this.dialog.querySelector('#fileName').textContent = file.name;
                this.dialog.querySelector('#fileSize').innerHTML = '(' + this.formatFileSize(file.size) + ')';
                
                defaultUI.style.display = 'none';
                previewArea.style.display = 'block';
                submitBtn.style.display = 'flex';
                
                uploadArea.style.paddingBottom = '1rem';
                uploadArea.style.setProperty('--after-content', 'none');
            };
            reader.onerror = () => {
                this.showErrorDialog('文件读取错误', '文件读取失败，请重试。');
                defaultUI.style.display = 'flex';
                previewArea.style.display = 'none';
                submitBtn.style.display = 'none';
                this.dialog.querySelector('input[name=book_file]').value = '';
            };
            reader.readAsDataURL(file);
        });
    }

    validateImage(file) {
        return new Promise((resolve) => {
            // 只对 GIF 格式进行大小限制
            if (file.type === 'image/gif' && file.size > 1 * 1024 * 1024) {
                this.showErrorDialog('文件过大', `GIF文件大小不能超过1MB，当前文件: ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
                resolve(false);
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                const img = new Image();
                img.onload = function() {
                    // 验证图片尺寸
                    if (this.width < 10 || this.height < 10) {
                        this.showErrorDialog('无效的图片', '图片尺寸太小，请选择更大的图片。');
                        resolve(false);
                        return;
                    }
                    resolve(true);
                }.bind(this);
                img.onerror = () => {
                    this.showErrorDialog('无效的图片文件', '所选文件不是有的图片文件。');
                    resolve(false);
                };
                img.src = e.target.result;
            };
            reader.onerror = () => {
                this.showErrorDialog('文件读取错误', '文件读取失败，请重试。');
                resolve(false);
            };
            reader.readAsDataURL(file);
        });
    }

    showErrorDialog(title, message) {
        const dialog = document.createElement('dialog');
        dialog.className = 'dialog-url';
        
        dialog.innerHTML = `
            <div class="dialog-url-header">
                <svg class="dialog-url-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                <h2 class="dialog-url-title">${title}</h2>
            </div>
            <p class="dialog-url-description">${message}</p>
            <div class="dialog-url-footer">
                <button class="dialog-url-button" onclick="this.closest('dialog').close()">确定</button>
            </div>
        `;
        
        document.body.appendChild(dialog);
        dialog.showModal();
        
        dialog.addEventListener('click', (e) => {
            if (e.target === dialog) dialog.close();
        });
    }

    handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        this.dialog.querySelector('#uploadArea').classList.add('drag-over');
    }

    handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();
        this.dialog.querySelector('#uploadArea').classList.remove('drag-over');
    }

    handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        this.dialog.querySelector('#uploadArea').classList.remove('drag-over');
        
        const file = e.dataTransfer.files[0];
        if (file) {
            this.processFile(file);
        }
    }

    // 添加一个简单的消息提示函数
    showMessage(msg) {
        // 先移除已存在的消息
        const existingMsg = document.querySelector('.upload-status-message');
        if (existingMsg) existingMsg.remove();

        const msgDiv = document.createElement('div');
        msgDiv.className = 'upload-status-message';
        msgDiv.innerHTML = `
            <div class="message-content">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                ${msg}
            </div>`;
        document.body.appendChild(msgDiv);
        return msgDiv;
    }

    // ... 其他方法直接复用 admin_WAPadd.aspx 中的 JavaScript 函数 ...
}

// 修改导出方式，等待 DOM 加载完成
document.addEventListener('DOMContentLoaded', () => {
    // 创建上传器实例
    window.albumUploader = new AlbumUploader({
        siteId: '1000', // 根据实际情况设置
        userId: '1234'  // 根据实际情况设置
    });
});