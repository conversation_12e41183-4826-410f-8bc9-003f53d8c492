using System;
using System.Collections.Generic;
using System.Text;
using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Report_List : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string linkURL = "";

        public string condition = "";

        public string ERROR = "";

        public string string_11 = "0";

        public string lpage = "";

        public List<wap2_bbs_report_Model> listVo = null;

        public StringBuilder strhtml = new StringBuilder();

        public long kk = 1L;

        public long index = 0L;

        public long total = 0L;

        public long pageSize = 10L;

        public long CurrentPage = 1L;

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            lpage = GetRequestValue("lpage");
            CheckManagerLvl("04", classVo.adminusername, "bbs/showadmin.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=1");
            switch (action)
            {
                case "class":
                    showclass();
                    break;
                case "del":
                    godel();
                    break;
                default:
                    showclass();
                    break;
            }
        }

        public void showclass()
        {
            // ✅ 使用QueryBuilder构建安全的查询条件，避免SQL注入
            var queryBuilder = new QueryBuilder()
                .Where("siteid = @ParamN", DapperHelper.SafeParseLong(siteid, "站点ID"))
                .Where("types = @ParamN", 0);

            if (!CheckManagerLvl("04", "") || classid != "0")
            {
                queryBuilder.Where("classid = @ParamN", DapperHelper.SafeParseLong(classid, "版块ID"));
            }
            try
            {
                if (classVo.ismodel < 1L)
                {
                    pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
                }
                else
                {
                    pageSize = Convert.ToInt32(classVo.ismodel);
                }

                // ✅ 在try块开始时统一声明连接字符串
                string reportConnectionString = PubConstant.GetConnectionString(string_10);

                if (GetRequestValue("getTotal") != "" && GetRequestValue("getTotal") != "0")
                {
                    total = Convert.ToInt32(GetRequestValue("getTotal"));
                }
                else
                {
                    // ✅ 使用安全的分页查询获取总数
                    var (countSql, _, parameters) = queryBuilder.BuildWithCount("SELECT COUNT(*)", "wap2_bbs_report");
                    total = DapperHelper.ExecuteScalar<int>(reportConnectionString, countSql, parameters);
                }

                if (GetRequestValue("page") != "")
                {
                    CurrentPage = long.Parse(GetRequestValue("page"));
                }
                CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
                index = pageSize * (CurrentPage - 1L);
                linkURL = http_start + "bbs/Report_List.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;lpage=" + lpage + "&amp;getTotal=" + total;
                linkURL = WapTool.GetPageLink(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL);

                // ✅ 使用安全的分页查询获取数据
                var result = PaginationHelper.GetPagedDataWithBuilder<wap2_bbs_report_Model>(
                    reportConnectionString, "SELECT *", "wap2_bbs_report", queryBuilder,
                    (int)CurrentPage, (int)pageSize, "ORDER BY id DESC");
                listVo = result.Data;
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }

        public void godel()
        {
        }
    }
}