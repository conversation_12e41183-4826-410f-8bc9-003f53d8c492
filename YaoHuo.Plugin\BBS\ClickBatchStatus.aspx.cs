using System;
using System.Text;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.BBS.Service;

namespace YaoHuo.Plugin.BBS
{
    /// <summary>
    /// 点击数批量处理状态监控页面
    /// 用于查看批量处理服务的运行状态和队列信息
    /// </summary>
    public partial class ClickBatchStatus : MyPageWap
    {
        public StringBuilder strhtml = new StringBuilder();
        public string ERROR = "";
        public string queueStatus = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // 检查管理员权限
                if (!IsCheckManagerLvl("|00|01|03|04|", ""))
                {
                    ShowTipInfo("您没有权限访问此页面。", "bbs/book_list.aspx?siteid=" + siteid + "&classid=" + classid);
                    return;
                }

                // 获取批量处理服务状态
                GetBatchServiceStatus();
            }
            catch (Exception ex)
            {
                ERROR = "获取状态失败：" + ex.Message;
                System.Diagnostics.Debug.WriteLine($"ClickBatchStatus: 页面加载失败 - {ex.Message}");
            }
        }

        /// <summary>
        /// 获取批量处理服务状态信息
        /// </summary>
        private void GetBatchServiceStatus()
        {
            try
            {
                // 获取队列状态
                string status = ClickBatchService.GetQueueStatus();
                
                // 格式化显示
                queueStatus = $"<div class=\"info\">";
                queueStatus += $"<b>服务状态：</b>{status}<br/>";
                queueStatus += $"<b>检查时间：</b>{DateTime.Now:yyyy-MM-dd HH:mm:ss}<br/>";
                queueStatus += $"<b>页面访问者：</b>{userVo?.nickname ?? "未知用户"}<br/>";
                queueStatus += $"</div>";
                
                System.Diagnostics.Debug.WriteLine($"ClickBatchStatus: 获取状态成功 - {status}");
            }
            catch (Exception ex)
            {
                queueStatus = $"<div class=\"tip\">获取服务状态失败：{ex.Message}</div>";
                System.Diagnostics.Debug.WriteLine($"ClickBatchStatus: 获取状态失败 - {ex.Message}");
            }
        }
    }
}
