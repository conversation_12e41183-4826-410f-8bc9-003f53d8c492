﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="AlbumList.aspx.cs" Inherits="YaoHuo.Plugin.Album.AlbumList" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    string title = "";
    StringBuilder strhtml = new StringBuilder();
    if (smalltypeid == "0")
    {
        title = this.GetLang("我的相册||");
    }
    else if (bookVo != null)
    {
        title = bookVo.subjectname;
    }
    // 添加头部样式和脚本
    strhtml.Append("<!DOCTYPE html><html lang=\"zh\"><head>");
    strhtml.Append("<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"><title>我的相册</title>");
    strhtml.Append("<script type=\"text/javascript\" src=\"./AlbumList.js?3\"></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"./AlbumUpload.js?4\"></script>");
    strhtml.Append("<link rel=\"stylesheet\" href=\"./AlbumList.css?1\">");
    strhtml.Append("</head>");
    strhtml.Append("<body class=\"min-h-screen bg-[#e8e8e8]\">");

    // 添加弹窗 HTML
    strhtml.Append(@"<dialog id=""deleteDialog"" class=""dialog-delete"">
        <div class=""dialog-delete-header"">
            <svg xmlns=""http://www.w3.org/2000/svg"" class=""dialog-delete-icon"" fill=""none"" viewBox=""0 0 24 24"" stroke=""currentColor"">
                <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16""/>
            </svg>
            <h3 class=""dialog-delete-title"">确认删除</h3>
        </div>
        <div class=""dialog-delete-content"">
            确定要删除这张图片吗？此操作无法撤销。
        </div>
        <div class=""dialog-delete-footer"">
            <button onclick=""closeDeleteDialog()"" class=""dialog-delete-button dialog-delete-button-cancel"">取消</button>
            <button onclick=""confirmDelete()"" class=""dialog-delete-button dialog-delete-button-confirm"">删除</button>
        </div>
    </dialog>");

    strhtml.Append("<div class=\"photo-container mx-auto\">");
    strhtml.Append("<div class=\"mx-auto max-w-4xl space-y-6\">");

    if (this.touserid == this.userid)
    {
        // 面包导航
        strhtml.Append("<div class=\"breadcrumb\">");
        strhtml.Append("<a href=\"/\" class=\"breadcrumb-item\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"></path><polyline points=\"9 22 9 12 15 12 15 22\"></polyline></svg>");
        strhtml.Append("<span>首页</span>");
        strhtml.Append("</a>");
        strhtml.Append("<span class=\"breadcrumb-separator\"></span>");
        strhtml.Append("<a href=\"/myfile.aspx\" class=\"breadcrumb-item\">");
        strhtml.Append("<span>我的地盘</span>");
        strhtml.Append("</a>");
        strhtml.Append("<span class=\"breadcrumb-separator\"></span>");
        strhtml.Append("<a href=\"/album/albumlist.aspx\" class=\"breadcrumb-item\"><span class=\"breadcrumb-item active\">" + title + "</span></a>");
        strhtml.Append("</div>");
        strhtml.Append("<div style=\"padding:7px;margin-top:.7rem;\">");

        // 工具栏 - 添加条件判断
        if (listVo != null && listVo.Count > 0 || !string.IsNullOrEmpty(key))
        {
            strhtml.Append("<div class=\"flex items-center justify-between mb-6\">");

            // 上传按钮
            strhtml.Append("<button onclick=\"window.albumUploader.show()\" ");
            strhtml.Append("class=\"inline-flex items-center px-4 py-2 bg-gray-800 text-white rounded-md hover:bg-black hover:transform-up transition-all\">");
            strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">");
            strhtml.Append("<path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12\"/>");
            strhtml.Append("</svg><span>上传图片</span></button>");

            // 搜索框
            strhtml.Append("<div class=\"flex items-center gap-1 ml-auto\">");
            strhtml.Append("<div id=\"searchContainer\" class=\"origin-right\">");
            strhtml.Append("<form action=\"" + http_start + "album/albumlist.aspx\" method=\"post\" class=\"flex w-full\">");
            strhtml.Append("<input type=\"text\" name=\"key\" value=\"" + key + "\" class=\"rounded-md border px-3 py-2 w-full text-sm\" placeholder=\"搜索相册\">");
            strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"class\" />");
            strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\" />");
            strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\" />");
            strhtml.Append("<input type=\"hidden\" name=\"touserid\" value=\"" + touserid + "\" />");
            strhtml.Append("<input type=\"hidden\" name=\"backurl\" value=\"" + backurl + "\" />");
            strhtml.Append("<input type=\"hidden\" name=\"smalltypeid\" value=\"" + smalltypeid + "\" />");
            strhtml.Append("</form></div>");

            // 搜索按钮
            strhtml.Append("<button id=\"searchToggle\" class=\"p-2 rounded-md border hover:bg-gray-100\">");
            strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">");
            strhtml.Append("<path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"/>");
            strhtml.Append("</svg></button></div></div>");
        }

        // 相册网格
        strhtml.Append("<div class=\"grid gap-4 grid-cols-2 lg:grid-cols-3\">");

        // 修改空状态判逻辑
        if (listVo == null || listVo.Count == 0)
        {
            if (!string.IsNullOrEmpty(key))
            {
                // 搜索无结果的提示 - 使用更简洁的样式
                strhtml.Append(@"<div class=""empty-state"" style=""min-height:200px;"">  <!-- 减小高度 -->
                    <svg class=""empty-state-icon"" xmlns=""http://www.w3.org/2000/svg"" viewBox=""0 0 24 24"" fill=""none"" stroke=""currentColor"" stroke-width=""1"" stroke-linecap=""round"" stroke-linejoin=""round"">
    <path d=""m13.5 8.5-5 5""/>
    <path d=""m8.5 8.5 5 5""/>
    <circle cx=""11"" cy=""11"" r=""8""/>
    <path d=""m21 21-4.3-4.3""/>
</svg>
                    <h3 class=""empty-state-title"">未找到相关图片</h3>
                    <p class=""empty-state-text"">没有找到包含 """ + key + @""" 的图片标题</p>
                </div>");
            }
            else
            {
                // 原有的全空白相册提示（保持不）
                strhtml.Append(@"<div class=""empty-state"">
                    <svg class=""empty-state-icon"" xmlns=""http://www.w3.org/2000/svg"" viewBox=""0 0 24 24"" fill=""none"" stroke=""currentColor"" stroke-width=""1"" stroke-linecap=""round"" stroke-linejoin=""round"">
                        <path d=""M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14zm-5-7l-3 3.72L9 13l-3 4h12l-4-5z""/>
                    </svg>
                    <h3 class=""empty-state-title"">空白相册</h3>
                    <p class=""empty-state-text"">看起来您还没有上传任何相片</p>
                    <a href=""" + this.http_start + "album/admin_WAPadd.aspx?siteid=" + this.siteid + "&amp;classid=0&amp;smalltypeid=" + this.smalltypeid + @""" 
                       class=""empty-state-button"">
                        <svg xmlns=""http://www.w3.org/2000/svg"" fill=""none"" viewBox=""0 0 24 24"" stroke=""currentColor"">
                            <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12""/>
                        </svg>
                        <span>立即上传</span>
                    </a>
                </div>");
            }
        }
        else
        {
            // 原有的相册列表循环代码
            for (int i = 0; (listVo != null && i < listVo.Count); i++)
            {
                strhtml.Append("<div class=\"group relative overflow-hidden rounded-lg border bg-white shadow-sm hover:shadow-md\">");
                // 添加整体链接
                strhtml.Append("<a href=\"javascript:void(0);\" onclick=\"showImage('" + listVo[i].book_img.Replace("'", "\\'") + "', " + listVo[i].id + ")\" ");
                strhtml.Append("class=\"block\">");
                strhtml.Append("<div class=\"aspect-square relative\">");
                strhtml.Append(@"<img 
                    src=""" + listVo[i].book_img + @""" 
                    alt=""相册图片"" 
                    class=""aspect-square w-full h-full object-cover"" 
                    onerror=""this.onerror=null; this.src='data:image/svg+xml,%3Csvg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 24 24%22 fill=%22none%22 stroke=%22%23d1d5db%22 stroke-width=%221%22 stroke-linecap=%22round%22 stroke-linejoin=%22round%22%3E%3Crect x=%223%22 y=%223%22 width=%2218%22 height=%2218%22 rx=%222%22 ry=%222%22%3E%3C/rect%3E%3Ccircle cx=%228.5%22 cy=%228.5%22 r=%221.5%22%3E%3C/circle%3E%3Cpolyline points=%2221 15 16 10 5 21%22%3E%3C/polyline%3E%3C/svg%3E'; this.style.background='#f3f4f6'; this.style.padding='4.5rem'; this.closest('a').onclick=null; this.closest('a').style.cursor='default';"" 
                />");
                strhtml.Append("<div class=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4\">");
                strhtml.Append("<h2 class=\"font-medium text-white\">" + listVo[i].book_title + "</h2>");
                strhtml.Append("</div>");
                strhtml.Append("</div></a>");

                // 除按钮（放在链接外面，避免冲突）
                strhtml.Append("<button onclick=\"showDeleteDialog('" + http_start + "album/albumlist_del.aspx?action=del&siteid=" + this.siteid + "&classid=0&smalltypeid=" + this.smalltypeid + "&id=" + listVo[i].id + "&backurl=" + HttpUtility.UrlEncode(backurl) + "&page=" + this.CurrentPage + "')\" ");
                strhtml.Append("class=\"absolute bottom-4 right-4 text-white opacity-70 hover:opacity-100 z-10 bg-transparent border-0\" data-image-id=\"" + listVo[i].id + "\">");
                strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">");
                strhtml.Append("<path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"/>");
                strhtml.Append("</svg></button>");
                strhtml.Append("</div>");
            }
        }

        strhtml.Append("</div>");

        // 在输出 linkURL 之前，将其包装在一个特定的 div 中
        strhtml.Append("<div id=\"originalPagination\" style=\"display:none;\">");
        strhtml.Append(linkURL);
        strhtml.Append("</div>");

        // 添加新的页容器
        strhtml.Append("<div id=\"modernPagination\" class=\"flex items-center justify-between border-t pt-4\"></div></div></div></div>");


        // 添加模态框 HTML
        strhtml.Append(@"
    <dialog id=""imageDialog"" class=""image-dialog"">
        <img id=""fullImage"" src="""" alt=""Full size image"">
        <button onclick=""setAsAvatar()"" class=""avatar-button"">
            <svg xmlns=""http://www.w3.org/2000/svg"" fill=""none"" viewBox=""0 0 24 24"" stroke=""currentColor"">
                <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"" />
            </svg>
            设为头像
        </button>
    </dialog>

    <div id=""toast"" class=""toast hidden"">
        <span id=""toastMessage""></span>
    </div>
    ");


        // 确保添加了 Toast 提示的 HTML 结构
        strhtml.Append(@"
    <div id=""toast"" class=""toast hidden"">
        <span id=""toastMessage""></span>
    </div>
    ");

        // 确保添加了所有需要的对话框 HTML 结构
        strhtml.Append(@"
    <dialog id=""imageDialog"" class=""image-dialog"">
        <img id=""fullImage"" src="""" alt=""Full size image"">
        <button onclick=""setAsAvatar()"" class=""avatar-button"">
            <svg xmlns=""http://www.w3.org/2000/svg"" fill=""none"" viewBox=""0 0 24 24"" stroke=""currentColor"">
                <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"" />
            </svg>
            设为头像
        </button>
    </dialog>

    <dialog id=""resultDialog"" class=""delete-dialog"">
        <div class=""delete-dialog-content"">
            <div class=""delete-dialog-header"">
                <svg xmlns=""http://www.w3.org/2000/svg"" class=""success-icon"" viewBox=""0 0 24 24"" fill=""none"" stroke=""currentColor"" stroke-width=""2"" stroke-linecap=""round"" stroke-linejoin=""round"">
                    <path d=""M22 11.08V12a10 10 0 1 1-5.93-9.14""></path>
                    <polyline points=""22 4 12 14.01 9 11.01""></polyline>
                </svg>
                <h3 class=""delete-dialog-title"">头像设置成功</h3>
            </div>
            <div class=""delete-dialog-footer"">
                <a href=""/bbs/userinfo.aspx?touserid=" + this.userid + @""" class=""delete-dialog-confirm"">进入空间查看</a>
                <button onclick=""closeResultDialog()"" class=""delete-dialog-cancel"">取消</button>
            </div>
        </div>
    </dialog></body></html>
    ");

        strhtml.Append(@"
    <script>
    var albumConfig = {
        httpStart: '" + this.http_start + @"',
        siteId: '" + this.siteid + @"',
        classId: '" + this.classid + @"',
        userId: '" + this.userid + @"'
    };
    </script>
    ");

        Response.Write(strhtml);
    }
%>