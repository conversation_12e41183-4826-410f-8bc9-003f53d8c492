.paw-button-container {
  display: flex;
  justify-content: center;
  margin: 10px 0;
}
.paw-button {
  --background: #fff;
  --background-active: #FEE8F4;
  --border: #F1ECEB;
  --border-active: #EEC2DB;
  --text: #000;
  --number: #9C9496;
  --number-active: #000;
  --heart-background: #fff;
  --heart-background-active: #FEA5D7;
  --heart-border: #C3C2C0;
  --heart-border-active: #2B2926;
  --heart-shadow-light: #FEE0F2;
  --heart-shadow-dark: #EA5DAF;
  --paw-background: #fff;
  --paw-border: #201E1B;
  --paw-shadow: #EEEDED;
  --paw-inner: var(--heart-background-active);
  --paw-shadow-light: var(--heart-shadow-light);
  --paw-shadow-dark: var(--heart-shadow-dark);
  --paw-clap-background: #FEF0A5;
  --paw-clap-border: var(--paw-border);
  --paw-clap-shadow: #FED75C;
  --circle: #df3dce;
  --circle-line: #000;
  display: inline-flex;
  text-decoration: none;
  font-weight: bold;
  position: relative;
  line-height: 19px;
  padding: 12px 16px;
}
.paw-button:before {
  content: "";
  position: absolute;
  display: block;
  left: -2px;
  top: -2px;
  bottom: -2px;
  right: -2px;
  z-index: 1;
  border-radius: 5px;
  transition: background 0.45s, border-color 0.45s;
  background: var(--background);
  border: 2px solid var(--border);
}
.paw-button svg {
  display: block;
}
.paw-button .text {
  position: relative;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  transform: translateZ(0);
  z-index: 3;
  margin-right: 8px;
  transition: width 0.25s;
  width: 15px; 
}
.paw-button .text span,
.paw-button .text svg {
  transition: transform 0.15s ease-out, opacity 0.2s;
  opacity: var(--o, 1);
}
.paw-button .text span {
  display: block;
  position: absolute;
  left: 30px;
  top: 0;
  transform: translateY(var(--y, 0));
  color: var(--text);
}
.paw-button .text svg {
  --background: var(--heart-background);
  --border: var(--heart-border);
  --shadow-light: transparent;
  --shadow-dark: transparent;
  width: 21px;
  height: 19px;
  transform: translateX(0); /* 确保svg始终在正确位置 */
}
.paw-button .number-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 36px; /* 调整这个值以适应3位数字 */
  height: 19px; /* 与行高保持一致 */
  margin-left: 4px;
}
.paw-button .number-container span {
  font-family: 'Arvo', serif;
  display: block;
  position: relative;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transform: translateZ(0);
  z-index: 2;
  color: var(--number);
  transition: opacity 0.3s ease, color 0.3s ease;
}
.paw-button .number-container span {
  opacity: var(--o, 1);
}
.paw-button:not(.confetti):hover .number-container span {
  opacity: 0;
}
.paw-button.liked .number-container span {
  color: var(--number-active);
}
.paw-button .paws {
  overflow: hidden;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 60px;
  z-index: 2;
}
.paw-button .paws svg {
  position: absolute;
  bottom: 0;
  transition: transform 0.3s ease-out, opacity 0.2s;
  opacity: var(--o, 0);
  transform: translate(var(--x, 0), var(--y, 0));
}
.paw-button .paws svg.paw {
  --x: -24px;
  width: 30px;
  height: 37px;
  left: 32px;
  bottom: 
}
.paw-button .paws svg.paw-clap {
  --x: 16px;
  --y: 34px;
  --o: 1;
  width: 29px;
  height: 34px;
  left: 34px;
}
.paw-button .paws .paw-effect {
  left: 26px;
  top: 12px;
  width: 44px;
  height: 44px;
  position: absolute;
}
.paw-button .paws .paw-effect:before {
  content: "";
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: var(--circle);
  transform: scale(var(--s, 0));
  opacity: var(--o, 1);
  transition: transform 0.15s ease 0.16s, opacity 0.2s linear 0.25s;
}
.paw-button .paws .paw-effect div {
  width: 2px;
  height: 6px;
  border-radius: 1px;
  left: 50%;
  bottom: 50%;
  margin-left: -1px;
  position: absolute;
  background: var(--circle-line);
  transform: translateY(-24px) scaleX(0.7) scaleY(var(--s, 0));
}
.paw-button .paws .paw-effect div:before, .paw-button .paws .paw-effect div:after {
  content: "";
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  background: inherit;
  border-radius: inherit;
  transform: translate(var(--x, -22px), var(--y, 4px)) rotate(var(--r, -45deg)) scaleX(0.8) scaleY(var(--s, 0));
}
.paw-button .paws .paw-effect div:after {
  --x: 22px;
  --r: 45deg;
}
.paw-button .paws .paw-effect div,
.paw-button .paws .paw-effect div:before,
.paw-button .paws .paw-effect div:after {
  opacity: var(--o, 1);
  transform-origin: 50% 100%;
  transition: transform 0.12s ease 0.17s, opacity 0.18s linear 0.21s;
}
.paw-button i {
  position: absolute;
  display: block;
  width: 4px;
  height: 4px;
  top: 50%;
  left: 50%;
  margin: -2px 0 0 -2px;
  opacity: var(--o, 0);
  background: var(--b);
  transform: translate(var(--x), var(--y)) scale(var(--s, 1));
}
.paw-button:not(.confetti):hover .text {
  --o: 0;
  --x: 12px;
  --y: 8px;
}
.paw-button:not(.confetti):hover .paws svg.paw {
  --o: 1;
  --x: 0;
}
.paw-button.animation .text {
  --o: 0;
}
.paw-button.animation .text svg {
  --background: var(--heart-background-active);
  --border: var(--heart-border-active);
  --shadow-light: var(--heart-shadow-light);
  --shadow-dark: var(--heart-shadow-dark);
}
.paw-button.animation .paws svg.paw {
  --x: 0;
  --o: 1;
  transition-delay: 0s;
  -webkit-animation: paw 0.45s ease forwards;
          animation: paw 0.45s ease forwards;
}
.paw-button.animation .paws svg.paw-clap {
  -webkit-animation: paw-clap 0.5s ease-in forwards;
          animation: paw-clap 0.5s ease-in forwards;
}
.paw-button.animation .paws .paw-effect {
  --s: 1;
  --o: 0;
}
.paw-button.confetti i {
  -webkit-animation: confetti 0.6s ease-out forwards;
          animation: confetti 0.6s ease-out forwards;
}
.paw-button.confetti .paws svg.paw {
  --o: 0;
  transition: opacity 0.15s linear 0.2s;
}
.paw-button.liked {
  --background: var(--background-active);
  --border: var(--border-active);
}
.paw-button.liked .text {
  --w: 21px;
}
.paw-button.liked .text svg {
  --o: 1;
}
.paw-button.liked > span {
  --number: var(--number-active);
}

@-webkit-keyframes confetti {
  from {
    transform: translate(0, 0);
    opacity: 1;
  }
}

@keyframes confetti {
  from {
    transform: translate(0, 0);
    opacity: 1;
  }
}
@-webkit-keyframes paw {
  0% {
    transform: translateX(var(--x));
  }
  35% {
    transform: translateX(-16px);
  }
  55%, 70% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-12px);
  }
}
@keyframes paw {
  0% {
    transform: translateX(var(--x));
  }
  35% {
    transform: translateX(-16px);
  }
  55%, 70% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-12px);
  }
}
@-webkit-keyframes paw-clap {
  50%, 70% {
    transform: translate(0, 0);
  }
}
@keyframes paw-clap {
  50%, 70% {
    transform: translate(0, 0);
  }
}
.paw-button:hover,
.paw-button:focus {
  text-decoration: none;
}