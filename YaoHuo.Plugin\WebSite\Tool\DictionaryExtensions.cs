using System.Collections.Generic;

namespace YaoHuo.Plugin.WebSite.Tool
{
    /// <summary>
    /// Dictionary扩展方法
    /// 提供.NET Framework兼容性支持
    /// </summary>
    public static class DictionaryExtensions
    {
        /// <summary>
        /// 获取指定键的值，如果键不存在则返回默认值
        /// 兼容.NET Framework版本，提供GetValueOrDefault功能
        /// </summary>
        /// <typeparam name="TKey">键类型</typeparam>
        /// <typeparam name="TValue">值类型</typeparam>
        /// <param name="dictionary">字典</param>
        /// <param name="key">键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>键对应的值或默认值</returns>
        public static TValue GetValueOrDefault<TKey, TValue>(this Dictionary<TKey, TValue> dictionary, TKey key, TValue defaultValue = default(TValue))
        {
            if (dictionary == null)
                return defaultValue;

            return dictionary.TryGetValue(key, out TValue value) ? value : defaultValue;
        }

        /// <summary>
        /// 获取指定键的值，如果键不存在则返回类型默认值
        /// </summary>
        /// <typeparam name="TKey">键类型</typeparam>
        /// <typeparam name="TValue">值类型</typeparam>
        /// <param name="dictionary">字典</param>
        /// <param name="key">键</param>
        /// <returns>键对应的值或类型默认值</returns>
        public static TValue GetValueOrDefault<TKey, TValue>(this Dictionary<TKey, TValue> dictionary, TKey key)
        {
            return GetValueOrDefault(dictionary, key, default(TValue));
        }
    }
}