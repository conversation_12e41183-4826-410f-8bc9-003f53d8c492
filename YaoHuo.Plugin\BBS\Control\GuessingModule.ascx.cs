﻿using System;
using System.Linq;
using System.Text;
using System.Web.UI;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.BBS.Control
{
    public partial class GuessingModule : System.Web.UI.UserControl
    {
        public GuessData GuessingData { get; set; }
        public user_Model UserVo { get; set; }
        public BetInfo UserBet { get; set; }
        public int? WinningOptionId { get; set; }
        public string WinningOptionText { get; set; }
        public string HttpStart { get; set; }

        // 新增属性用于访问父页面的 strhtml
        private System.Text.StringBuilder ParentStrHtml
        {
            get
            {
                if (Page is Book_View bookView)
                {
                    return bookView.strhtml;
                }
                return null;
            }
        }

        public int TotalAmount
        {
            get
            {
                return GuessingData?.Options?.Sum(o => o.Amount) ?? 0;
            }
        }

        protected override void Render(HtmlTextWriter writer)
        {
            // 使用预分配大小的 StringBuilder 可以减少内存重分配
            using (var sw = new System.IO.StringWriter(new StringBuilder(4096)))
            using (var hw = new HtmlTextWriter(sw))
            {
                base.Render(hw);
                ((Book_View)Page).strhtml.Append(sw.ToString());
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // 页面首次加载时的初始化逻辑
            }
        }
    }
}