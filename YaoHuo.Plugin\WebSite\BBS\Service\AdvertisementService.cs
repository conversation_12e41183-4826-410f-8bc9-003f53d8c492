using System;
using System.Linq;
using System.Runtime.Caching;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.WebSite.BBS.Service
{
    /// <summary>
    /// 广告信息安全查询服务
    /// 集中化广告信息查询逻辑，提供统一的安全查询接口和缓存策略
    /// </summary>
    public static class AdvertisementService
    {
        private static readonly MemoryCache _adCache = MemoryCache.Default;
        private static readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(30); // 30分钟缓存
        private static readonly object _lockObject = new object();

        // 缓存统计
        private static long _cacheHits = 0;
        private static long _cacheMisses = 0;

        /// <summary>
        /// 安全获取BBS广告信息（带缓存）
        /// </summary>
        /// <param name="siteid">站点ID</param>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <returns>广告模型，如果不存在则返回空模型</returns>
        public static sys_ad_show_Model GetBBSAdvertisementSafely(string siteid, string connectionString)
        {
            return GetAdvertisementSafely("bbs", siteid, connectionString);
        }

        /// <summary>
        /// 安全获取相册广告信息（带缓存）
        /// </summary>
        /// <param name="siteid">站点ID</param>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <returns>广告模型，如果不存在则返回空模型</returns>
        public static sys_ad_show_Model GetAlbumAdvertisementSafely(string siteid, string connectionString)
        {
            return GetAdvertisementSafely("album", siteid, connectionString);
        }

        /// <summary>
        /// 安全获取游戏广告信息（带缓存）
        /// </summary>
        /// <param name="siteid">站点ID</param>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <returns>广告模型，如果不存在则返回空模型</returns>
        public static sys_ad_show_Model GetGamesAdvertisementSafely(string siteid, string connectionString)
        {
            return GetAdvertisementSafely("games", siteid, connectionString);
        }

        /// <summary>
        /// 通用的安全广告查询方法（内部使用）
        /// </summary>
        /// <param name="systype">系统类型（bbs、album、games等）</param>
        /// <param name="siteid">站点ID</param>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <returns>广告模型，如果不存在则返回空模型</returns>
        private static sys_ad_show_Model GetAdvertisementSafely(string systype, string siteid, string connectionString)
        {
            try
            {
                // 输入验证
                if (string.IsNullOrEmpty(systype) || string.IsNullOrEmpty(siteid) || string.IsNullOrEmpty(connectionString))
                {
                    System.Diagnostics.Debug.WriteLine($"AdvertisementService: 输入参数无效，systype={systype}, siteid={siteid}");
                    return new sys_ad_show_Model();
                }

                if (!WapTool.IsNumeric(siteid))
                {
                    System.Diagnostics.Debug.WriteLine($"AdvertisementService: 站点ID格式无效，siteid={siteid}");
                    return new sys_ad_show_Model();
                }

                // 构建缓存键
                string cacheKey = $"ad_{systype}_{siteid}";

                // 尝试从缓存获取
                if (_adCache.Get(cacheKey) is sys_ad_show_Model cachedAd)
                {
                    System.Threading.Interlocked.Increment(ref _cacheHits);
                    return cachedAd;
                }

                // 缓存未命中，查询数据库
                System.Threading.Interlocked.Increment(ref _cacheMisses);

                // ✅ 使用DapperHelper进行安全的参数化查询
                string adSql = "SELECT * FROM sys_ad_show WHERE systype = @SysType AND siteid = @SiteId";

                var adVo = DapperHelper.Query<sys_ad_show_Model>(connectionString, adSql, new {
                    SysType = systype,
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID")
                }).FirstOrDefault();

                // 如果查询结果为空，创建空模型
                if (adVo == null)
                {
                    adVo = new sys_ad_show_Model();
                    System.Diagnostics.Debug.WriteLine($"AdvertisementService: 未找到广告信息，systype={systype}, siteid={siteid}");
                }

                // 存入缓存
                lock (_lockObject)
                {
                    var policy = new CacheItemPolicy
                    {
                        AbsoluteExpiration = DateTimeOffset.Now.Add(_cacheExpiry)
                    };
                    _adCache.Set(cacheKey, adVo, policy);
                }

                System.Diagnostics.Debug.WriteLine($"AdvertisementService: 查询成功，systype={systype}, siteid={siteid}, 缓存已更新");
                return adVo;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"AdvertisementService: 获取广告失败，systype={systype}, siteid={siteid}, error={ex.Message}");
                return new sys_ad_show_Model(); // 返回空模型确保页面正常显示
            }
        }

        /// <summary>
        /// 清除指定类型和站点的广告缓存
        /// </summary>
        /// <param name="systype">系统类型（bbs、album、games等）</param>
        /// <param name="siteid">站点ID，为空时清除所有站点</param>
        public static void ClearAdvertisementCache(string systype, string siteid = null)
        {
            try
            {
                if (string.IsNullOrEmpty(systype))
                    return;

                if (string.IsNullOrEmpty(siteid))
                {
                    // 清除指定类型的所有站点缓存
                    var keysToRemove = _adCache.Where(kvp => kvp.Key.StartsWith($"ad_{systype}_"))
                                              .Select(kvp => kvp.Key)
                                              .ToList();

                    foreach (var key in keysToRemove)
                    {
                        _adCache.Remove(key);
                    }

                    System.Diagnostics.Debug.WriteLine($"AdvertisementService: 已清除{systype}类型的所有广告缓存，共{keysToRemove.Count}个");
                }
                else
                {
                    // 清除特定站点的缓存
                    string cacheKey = $"ad_{systype}_{siteid}";
                    _adCache.Remove(cacheKey);
                    System.Diagnostics.Debug.WriteLine($"AdvertisementService: 已清除广告缓存，key={cacheKey}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"AdvertisementService: 清除缓存失败，systype={systype}, siteid={siteid}, error={ex.Message}");
            }
        }

        /// <summary>
        /// 清除所有广告缓存
        /// </summary>
        public static void ClearAllAdvertisementCache()
        {
            try
            {
                var keysToRemove = _adCache.Where(kvp => kvp.Key.StartsWith("ad_"))
                                          .Select(kvp => kvp.Key)
                                          .ToList();

                foreach (var key in keysToRemove)
                {
                    _adCache.Remove(key);
                }

                System.Diagnostics.Debug.WriteLine($"AdvertisementService: 已清除所有广告缓存，共{keysToRemove.Count}个");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"AdvertisementService: 清除所有缓存失败，error={ex.Message}");
            }
        }

        /// <summary>
        /// 获取缓存统计信息（用于监控和调试）
        /// </summary>
        /// <returns>缓存统计信息字符串</returns>
        public static string GetCacheStats()
        {
            try
            {
                var totalRequests = _cacheHits + _cacheMisses;
                var hitRate = totalRequests > 0 ? (_cacheHits * 100.0 / totalRequests) : 0;

                return $"广告缓存统计 - 总请求: {totalRequests}, 命中: {_cacheHits}, 未命中: {_cacheMisses}, 命中率: {hitRate:F1}%";
            }
            catch (Exception ex)
            {
                return $"广告缓存统计获取失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取服务统计信息（用于监控和调试）
        /// </summary>
        /// <returns>统计信息字符串</returns>
        public static string GetServiceStats()
        {
            try
            {
                var cacheStats = GetCacheStats();
                return $"AdvertisementService统计信息 - {cacheStats}, 最后更新时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
            }
            catch (Exception ex)
            {
                return $"AdvertisementService统计信息获取失败: {ex.Message}";
            }
        }
    }
}
