namespace YaoHuo.Plugin.BBS.Components
{
    /// <summary>
    /// BBS样式管理器 - 统一管理所有BBS相关的CSS样式
    /// </summary>
    public static class BBSStyles
    {

        
        /// <summary>
        /// 获取热门帖子天数切换按钮的CSS样式
        /// </summary>
        /// <returns>天数切换按钮CSS样式字符串</returns>
        public static string GetDaysFilterStyles()
        {
            return @"
                <style>
                .days-filter {
                    display: flex;
                    justify-content: space-around;
                    gap: 10px;
                    padding: 10px;
                    background: #fff;
                }
                .days-btn {
                    flex: 1;
                    padding: 2px 0;
                    border: 1px solid #ddd;
                    border-radius: 10px;
                    background: #f5f5f5;
                    color: #666;
                    font-size: 14px;
                    text-decoration: none;
                    text-align: center;
                    box-sizing: border-box;
                    transition: all 0.3s ease;
                }
                .days-btn.active {
                    background: #e8f8f5;
                    color: #1abc9c;
                    border-color: #1abc9c;
                    box-shadow: 0 0 0 1px rgba(26, 188, 156, 0.3);
                    font-weight: bold;
                }
                </style>";
        }
        
        /// <summary>
        /// 获取所有BBS通用样式
        /// </summary>
        /// <returns>所有通用CSS样式字符串</returns>
        public static string GetCommonStyles()
        {
            return GetDaysFilterStyles();
        }
        
        /// <summary>
        /// 获取BBS列表页面专用样式
        /// </summary>
        /// <returns>列表页面CSS样式字符串</returns>
        public static string GetListPageStyles()
        {
            return ""; // 列表页面暂无专用样式
        }
        
        /// <summary>
        /// 获取热门帖子页面专用样式
        /// </summary>
        /// <returns>热门帖子页面CSS样式字符串</returns>
        public static string GetHotPageStyles()
        {
            return GetCommonStyles();
        }
        
        /// <summary>
        /// 获取搜索结果页面专用样式
        /// </summary>
        /// <returns>搜索结果页面CSS样式字符串</returns>
        public static string GetSearchPageStyles()
        {
            return ""; // 搜索页面暂无专用样式
        }
    }
}