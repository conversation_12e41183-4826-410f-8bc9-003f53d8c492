/**
 * 动态图标匹配系统
 * 统一管理所有页面的动态图标匹配逻辑
 * 支持JSON配置文件和fallback机制
 * 
 * @version 1.1
 * <AUTHOR>
 * @date 2025-06-12
 * @location /Template/JS/Components/DynamicIcons.js
 */

class DynamicIconMatcher {
    static _config = null;
    static _configLoaded = false;
    static _configUrl = '/Data/StaticData/DynamicIconConfigs.json';
    
    // 默认配置（fallback）
    static _defaultConfig = {
        iconRules: [
            { id: 'search', pattern: '正在论坛查询', matchType: 'startsWith', icon: 'fas fa-search', color: 'text-primary', priority: 1 },
            { id: 'reply', pattern: '回复了帖子', matchType: 'startsWith', icon: 'fas fa-reply', color: 'text-purple-400', priority: 2 },
            { id: 'newpost', pattern: '发表新帖', matchType: 'startsWith', icon: 'fas fa-feather-alt', color: 'text-emerald-500', priority: 3 },
            { id: 'message1', pattern: '个人空间留言', matchType: 'includes', icon: 'fas fa-comment-dots', color: 'text-pink-400', priority: 4 },
            { id: 'message2', pattern: '在个人空间留言', matchType: 'includes', icon: 'fas fa-comment-dots', color: 'text-pink-400', priority: 5 },
            { id: 'message3', pattern: '空间留言', matchType: 'includes', icon: 'fas fa-comment-dots', color: 'text-pink-400', priority: 6 },
            { id: 'message4', pattern: '留言了', matchType: 'includes', icon: 'fas fa-comment-dots', color: 'text-pink-400', priority: 7 },
            { id: 'game', pattern: '在玩', matchType: 'startsWith', icon: 'fas fa-dice', color: 'text-orange-400', priority: 8 },
            { id: 'upload', pattern: '上传文件', matchType: 'startsWith', icon: 'fas fa-cloud-upload-alt', color: 'text-sky-500', priority: 9 },
            { id: 'buygem', pattern: '购买了妖晶', matchType: 'includes', icon: 'fas fa-gem', color: 'text-yellow-500', priority: 10 },
            { id: 'gembuy', pattern: '使用妖晶购买了身份', matchType: 'includes', icon: 'fas fa-crown', color: 'text-purple-500', priority: 11 },
            { id: 'rmbbuy', pattern: '使用RMB购买了身份', matchType: 'includes', icon: 'fas fa-crown', color: 'text-rose-500', priority: 12 }
        ],
        defaultIcon: {
            icon: 'fas fa-circle-notch',
            color: 'text-gray-400'
        }
    };

    /**
     * 加载配置文件
     * @returns {Promise<Object>} 配置对象
     */
    static async loadConfig() {
        if (this._configLoaded && this._config) {
            return this._config;
        }

        try {
            const response = await fetch(this._configUrl + '?v=' + Date.now());

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const config = await response.json();

            // 验证配置格式
            if (!config.iconRules || !Array.isArray(config.iconRules)) {
                throw new Error('配置文件格式错误：缺少iconRules数组');
            }

            this._config = config;
            this._configLoaded = true;

            return config;

        } catch (error) {
            this._config = this._defaultConfig;
            this._configLoaded = true;
            return this._defaultConfig;
        }
    }

    /**
     * 匹配动态内容并返回图标信息
     * @param {string} content 动态内容
     * @returns {Object} 图标信息 {icon, color, className, matched, rule}
     */
    static async matchIcon(content) {
        if (!content || typeof content !== 'string') {
            const config = await this.loadConfig();
            return {
                icon: config.defaultIcon.icon,
                color: config.defaultIcon.color,
                matched: false,
                rule: null
            };
        }

        const config = await this.loadConfig();
        
        // 按优先级排序，查找第一个匹配的规则
        const enabledRules = config.iconRules
            .filter(rule => rule.enabled !== false)
            .sort((a, b) => (a.priority || 999) - (b.priority || 999));

        for (const rule of enabledRules) {
            if (this._isRuleMatch(content, rule)) {
                return {
                    icon: rule.icon,
                    color: rule.color,
                    matched: true,
                    rule: rule
                };
            }
        }

        // 未匹配到任何规则，使用默认图标
        return {
            icon: config.defaultIcon.icon,
            color: config.defaultIcon.color,
            matched: false,
            rule: null
        };
    }

    /**
     * 检查规则是否匹配内容
     * @param {string} content 内容
     * @param {Object} rule 规则
     * @returns {boolean} 是否匹配
     */
    static _isRuleMatch(content, rule) {
        if (!rule.pattern) return false;

        switch (rule.matchType?.toLowerCase()) {
            case 'startswith':
                return content.startsWith(rule.pattern);
            case 'includes':
            case 'contains':
                return content.includes(rule.pattern);
            case 'equals':
                return content === rule.pattern;
            case 'regex':
                try {
                    return new RegExp(rule.pattern).test(content);
                } catch (e) {
                    return false;
                }
            default:
                return content.startsWith(rule.pattern);
        }
    }

    /**
     * 初始化页面中的动态图标
     * @param {string} selector 图标选择器，默认为 '.dynamic-icon'
     * @returns {Promise<number>} 处理的图标数量
     */
    static async init(selector = '.dynamic-icon') {
        try {
            const icons = document.querySelectorAll(selector);
            if (icons.length === 0) {
                return 0;
            }

            let processedCount = 0;

            for (const icon of icons) {
                const content = icon.getAttribute('data-content');
                if (!content) {
                    continue;
                }

                const iconInfo = await this.matchIcon(content);

                // 清除所有现有的图标和颜色类，但保留其他类
                const existingClasses = icon.className.split(' ').filter(cls =>
                    !cls.startsWith('fas') &&
                    !cls.startsWith('far') &&
                    !cls.startsWith('fab') &&
                    !cls.startsWith('text-') &&
                    cls !== 'dynamic-icon' &&
                    cls.trim() !== ''
                );

                // 设置新的图标类，确保颜色类有 !important
                const newClasses = [...existingClasses, iconInfo.icon, iconInfo.color, 'dynamic-icon'];
                icon.className = newClasses.join(' ');

                // 强制设置颜色样式以确保显示
                if (iconInfo.color.includes('text-')) {
                    const colorMap = {
                        'text-primary': '#58b4b0',
                        'text-emerald-500': '#10b981',
                        'text-orange-400': '#fb923c',
                        'text-purple-400': '#a855f7',
                        'text-pink-400': '#f472b6',
                        'text-sky-500': '#0ea5e9',
                        'text-yellow-500': '#eab308',
                        'text-purple-500': '#8b5cf6',
                        'text-rose-500': '#f43f5e',
                        'text-gray-400': '#9ca3af'
                    };

                    const color = colorMap[iconInfo.color];
                    if (color) {
                        icon.style.color = color + ' !important';
                    }
                }
                processedCount++;
            }

            return processedCount;

        } catch (error) {
            return 0;
        }
    }

    /**
     * 批量匹配多个内容
     * @param {string[]} contents 内容数组
     * @returns {Promise<Object[]>} 匹配结果数组
     */
    static async batchMatch(contents) {
        if (!Array.isArray(contents)) {
            return [];
        }

        const results = [];
        for (const content of contents) {
            const result = await this.matchIcon(content);
            results.push({
                content: content,
                ...result
            });
        }
        
        return results;
    }

    /**
     * 刷新配置缓存
     * @returns {Promise<Object>} 新的配置对象
     */
    static async refreshConfig() {
        this._config = null;
        this._configLoaded = false;
        return await this.loadConfig();
    }

    /**
     * 获取配置统计信息
     * @returns {Promise<Object>} 统计信息
     */
    static async getStats() {
        const config = await this.loadConfig();
        const enabledRules = config.iconRules.filter(rule => rule.enabled !== false);
        
        return {
            totalRules: config.iconRules.length,
            enabledRules: enabledRules.length,
            disabledRules: config.iconRules.length - enabledRules.length,
            configLoaded: this._configLoaded,
            configSource: this._config === this._defaultConfig ? 'fallback' : 'json'
        };
    }
}

// 全局暴露
window.DynamicIconMatcher = DynamicIconMatcher;

// 兼容性：提供全局函数
window.initDynamicIcons = () => DynamicIconMatcher.init();

// 自动初始化（如果页面已加载完成）
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        // 延迟100ms确保其他脚本已执行
        setTimeout(() => DynamicIconMatcher.init(), 100);
    });
} else {
    // 页面已加载，立即初始化
    setTimeout(() => DynamicIconMatcher.init(), 100);
}
