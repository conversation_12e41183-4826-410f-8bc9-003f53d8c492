﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Medal.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Medal" EnableViewState="false" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%@ Import Namespace="YaoHuo.Plugin.WebSite.Services" %>
<%@ Import Namespace="YaoHuo.Plugin.WebSite.Services.Config" %>
<%@ Import Namespace="System.Web" %>
<%@ Import Namespace="System.Linq" %>
<%
string pageType = Request.QueryString["type"] ?? "apply";
string currentTime = DateTime.Now.ToString("H:mm");
string greeting = WapTool.GetHello();

// 清空当前用户的勋章缓存，确保显示最新的勋章状态
if (userVo != null && !string.IsNullOrEmpty(userVo.moneyname))
{
    YaoHuo.Plugin.WebSite.Services.Config.BBSConfigService.ClearUserMedalCache(userVo.moneyname);
    System.Diagnostics.Debug.WriteLine($"[Medal缓存-旧版] 清空用户勋章缓存: {userVo.userid}");
}

// 获取用户勋章信息
string userMedals = "";
if (userVo != null && !string.IsNullOrEmpty(userVo.moneyname))
{
    userMedals = WapTool.GetMedal(userVo.userid.ToString(), userVo.moneyname, WapTool.GetSiteDefault(siteVo.Version, 47), wmlVo);
}

// 获取新消息信息（实现[msg]UBB功能）
string newMessageContent = "";
if (userVo != null && userid != "0")
{
    string messageCount = WapTool.GetMessage(ver, userid, siteid, http_start, sid, classid, "1");
    if (messageCount != "0")
    {
        // 构建消息内容，替换模板中的x为实际消息数量
        string messageTemplate = "[img]/tupian/news.gif[/img]收到x封飞鸽传书 <a href=\"/bbs/Message_Clear.htm\" style=\"font-size:80%;\"><img width=\"20\" height=\"20\" style=\"vertical-align: middle;margin-top:-1.5%;\" src=\"/tupian/clean.png\"></a><script src=\"/NetCSS/JS/BookList/Message_Clear.js\"></script>";
        newMessageContent = messageTemplate.Replace("x", messageCount);
        // 处理[img]标签
        newMessageContent = newMessageContent.Replace("[img]", "<img src=\"" + http_start).Replace("[/img]", "\" />");
        // 包装链接
        newMessageContent = "<a href=\"" + http_start + "bbs/messagelist.aspx?siteid=" + siteid + "&amp;backurl=" + HttpUtility.UrlEncode("bbs/medal.aspx?type=" + pageType) + "\">" + newMessageContent + "</a>";
    }
}

Response.Write(@"<!DOCTYPE html>
<html>
<head>
<meta charset=""UTF-8"">
<meta name=""referrer"" content=""same-origin"" />
<meta name=""Format-Detection"" content=""Telephone=NO"">
<meta name=""viewport"" content=""width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"">
<link rel=""icon"" href=""/favicon.ico"" />
<link rel=""stylesheet"" href=""/NetCSS/CSS/Index/Default/xqx.css"" type=""text/css"" />
<link rel=""stylesheet"" href=""/Template/default/default.css"" type=""text/css"" />
<title>" + (pageType == "buy" ? "购买勋章" : "荣誉勋章") + @"</title>
</head>
<body>
<div class=""newMessage"">" + newMessageContent + @"</div>");

if (pageType == "buy")
{
    Response.Write(@"
<div class=""title""><a href=""/bbs/medal.aspx?type=apply"">接受申请</a> | 购买勋章</div>
<div class=""margin-top""> </div>");

    // 从JSON配置获取购买勋章列表
    try
    {
        var purchaseMedals = BBSConfigService.GetPurchaseMedals();
        string userMoneyName = userVo?.moneyname ?? "";

        // 调试输出
        System.Diagnostics.Debug.WriteLine($"旧版UI - 用户勋章数据: {userMoneyName}");
        System.Diagnostics.Debug.WriteLine($"旧版UI - 购买勋章数量: {purchaseMedals.Count}");

        for (int i = 0; i < purchaseMedals.Count; i++)
        {
            var medal = purchaseMedals[i];
            string cssClass = (i % 2 == 0) ? "line2" : "line1";
            bool isOwned = BBSConfigService.IsMedalOwned(medal.FileName, userMoneyName);
            string buyAction = isOwned ? "已购买" : $"<a href=\"{medal.BuyUrl}\">购买</a>";

            System.Diagnostics.Debug.WriteLine($"旧版UI - 勋章: {medal.Name}, 文件名: {medal.FileName}, 已拥有: {isOwned}");

            Response.Write($@"
<div class=""{cssClass}""><table><tr><td><img src=""{medal.IconUrl}""/> {medal.Name}<br/>【{buyAction}】价格 {medal.Price} 妖晶</td></tr></table></div>");
        }
    }
    catch (Exception ex)
    {
        // 如果JSON配置加载失败，使用备用硬编码数据
        Response.Write(@"
<div class=""line2""><table><tr><td><img src=""/bbs/medal/初级勋章.gif""/> 初级勋章<br/>【<a href=""/XinZhang/book_view_buy.aspx?&id=20&lpage=1&ordertype=1"">购买</a>】价格 10000 妖晶</td></tr></table></div>");
    }
}
else
{
    Response.Write(@"
<div class=""title"">接受申请 | <a href=""/bbs/medal.aspx?type=buy"">购买勋章</a></div>
<div class=""line1"">【<a href=""/bbs-1824.html"">申请流程介绍</a>】下面是条件：</div>");

    // 从JSON配置获取申请勋章列表
    try
    {
        var applyMedals = BBSConfigService.GetApplyMedals();
        string userMoneyName = userVo?.moneyname ?? "";

        for (int i = 0; i < applyMedals.Count; i++)
        {
            var medal = applyMedals[i];
            string cssClass = (i % 2 == 0) ? "line2" : "line1";
            // 旧版UI不显示申请勋章的"已拥有"状态，避免影响排版布局
            string conditionText;
            if (!string.IsNullOrEmpty(medal.ConditionUrl))
            {
                // 有条件链接时，生成超链接，使用自定义链接文本
                string linkText = !string.IsNullOrEmpty(medal.ConditionLinkText) ? medal.ConditionLinkText : "点此查看申请条件";
                conditionText = $@"<a href=""{medal.ConditionUrl}"">{linkText}</a>";
            }
            else
            {
                // 没有条件链接时，显示描述文本
                conditionText = !string.IsNullOrEmpty(medal.ShortDescription) ? medal.ShortDescription : medal.Description;
            }

            Response.Write($@"
<div class=""{cssClass}""><img src=""{medal.IconUrl}""/>{medal.Name}:{conditionText}</div>");
        }
    }
    catch (Exception ex)
    {
        // 如果JSON配置加载失败，使用备用硬编码数据
        Response.Write(@"
<div class=""line2""><img src=""/bbs/medal/67.gif""/>新人进步:累计发表10资源帖</div>
<div class=""line1""><img src=""/bbs/medal/认真学习.gif""/>认真学习:认真回帖满1000次</div>");
    }
}

Response.Write(@"
<div class=""" + (pageType == "buy" ? "mylink" : "top") + @""">" + greeting + @"！现在是" + currentTime + @" <a href=""/"">返回首页</a></div>
<script type=""text/javascript"" src=""/NetCSS/JS/LocalStorage.js""></script>
</body>
</html>");
%>