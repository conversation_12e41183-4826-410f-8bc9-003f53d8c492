﻿using KeLin.ClassManager;
using KeLin.ClassManager.ExUtility;
using KeLin.ClassManager.Model;
using System;
using System.Data;
using System.Text;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.XinZhang
{
    public class Book_View_My : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        private string ConnectionString => PubConstant.GetConnectionString(a);

        public string id = "0";

        public XinZhang_Model bookVo = new XinZhang_Model();

        public StringBuilder strhtml = new StringBuilder();

        public string lpage = "";

        public string INFO = "";

        public string ERROR = "";

        public string action = "";

        public string backurl = "";

        public string pw = "";

        public string ordertype = "";

        public string g = "";

        public string type = "";

        /// <summary>
        /// 隐藏的勋章
        /// </summary>
        public string HideMoneyName
        {
            get
            {
                var sqlStr = string.Empty;
                //检查表存在否
                sqlStr = "select count(0) from sysObjects where id = object_id(N'XinZhang_Plugin') and xtype = 'U'";
                var isExist = DbHelperSQL.ExecuteScalar(ConnectionString, CommandType.Text, sqlStr).ToInt();
                if (isExist <= 0)
                {
                    //初始化新表
                    sqlStr = $@"
CREATE TABLE [dbo].[XinZhang_Plugin] (
    [userid] bigint  NOT NULL,
    [siteid] bigint  NOT NULL,
    [moneyname] ntext COLLATE Chinese_PRC_CI_AS  NULL
);
ALTER TABLE XinZhang_Plugin ADD CONSTRAINT PK_XinZhang_Plugin PRIMARY KEY (userid);";

                    // ✅ 使用DapperHelper进行安全的表创建操作
                    DapperHelper.Execute(ConnectionString, sqlStr, null);
                }
                //查询隐藏的勋章
                sqlStr = $"select top 1 moneyname from XinZhang_Plugin where siteid={base.siteid} and userid={base.userVo.userid}";
                var myMoneyName = DbHelperSQL.ExecuteScalar(ConnectionString, CommandType.Text, sqlStr);
                if (myMoneyName == null)
                {
                    //初始化用户数据
                    string insertUserDataSql = "INSERT INTO XinZhang_Plugin(userid, siteid, moneyname) VALUES (@UserId, @SiteId, @MoneyName)";
                    DapperHelper.Execute(ConnectionString, insertUserDataSql, new {
                        UserId = base.userVo.userid,
                        SiteId = base.siteid,
                        MoneyName = ""
                    });
                }
                return myMoneyName.ToStr();
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            while (true)
            {
                this.id = base.GetRequestValue("id");
                this.g = base.GetRequestValue("g");
                this.lpage = base.GetRequestValue("lpage");
                this.action = base.GetRequestValue("action");
                this.pw = base.GetRequestValue("pw");
                this.ordertype = base.GetRequestValue("ordertype");
                bool flag = !(this.g.Trim() != "");
                int num;
                num = 0;
                while (true)
                {
                    switch (num)
                    {
                        case 0:
                            if (!flag)
                            {
                                num = 3;
                                continue;
                            }
                            goto case 1;
                        case 2:
                            try
                            {
                                while (true)
                                {
                                    flag = !(this.action == "godel");
                                    num = 4;
                                    while (true)
                                    {
                                        switch (num)
                                        {
                                            case 4:
                                                if (!flag)
                                                {
                                                    num = 1;
                                                    continue;
                                                }
                                                goto case 6;
                                            case 1:
                                                flag = !(PubConstant.md5(this.pw).ToLower() != base.userVo.password.ToLower());
                                                num = 0;
                                                continue;
                                            case 0:
                                                if (!flag)
                                                {
                                                    num = 2;
                                                    continue;
                                                }
                                                //查找勋章是否存在（防止不规范的数据）
                                                //if (this.type != "隐藏全部" && this.type != "显示全部")
                                                //{
                                                //    var sqlStr = $"select count(0) from XinZhang where XinZhangTuPian = @XinZhangTuPian";
                                                //    var array = new SqlParameter[]
                                                //    {
                                                //        new SqlParameter("@XinZhangTuPian", SqlDbType.NVarChar),
                                                //    };
                                                //    array[0].Value = this.id;
                                                //    var isXinZhang = DbHelperSQL.ExecuteScalar(ConnectionString, CommandType.Text, sqlStr, array).ToInt();
                                                //    if (isXinZhang == 0)
                                                //    {
                                                //        this.INFO = "NO";
                                                //        return;
                                                //    }
                                                //}
                                                //删除勋章
                                                if (this.type == "删除")
                                                {
                                                    //删除显示
                                                    var myMoneyName = base.userVo.moneyname.Replace("||", "|").Trim('|');
                                                    var updMoneyName = myMoneyName.Replace(this.id, "").Replace("||", "|").Trim('|');
                                                    if (myMoneyName == updMoneyName)
                                                    {
                                                        this.INFO = "NO";
                                                        return;
                                                    }

                                                    // ✅ 使用DapperHelper进行安全的勋章删除操作
                                                    string updateUserMoneySql = "UPDATE [user] SET moneyname = @MoneyName WHERE siteid = @SiteId AND userid = @UserId";
                                                    DapperHelper.Execute(ConnectionString, updateUserMoneySql, new {
                                                        MoneyName = DapperHelper.LimitLength(updMoneyName, 500),
                                                        SiteId = base.siteid,
                                                        UserId = base.userVo.userid
                                                    });

                                                    //立刻刷新界面数据
                                                    base.userVo.moneyname = updMoneyName;
                                                }
                                                //隐藏勋章
                                                else if (this.type == "隐藏")
                                                {
                                                    //删除显示
                                                    var myMoneyName = base.userVo.moneyname.Replace("||", "|").Trim('|');
                                                    var updMoneyName = myMoneyName.Replace(this.id, "").Replace("||", "|").Trim('|');
                                                    if (myMoneyName == updMoneyName)
                                                    {
                                                        this.INFO = "NO";
                                                        return;
                                                    }

                                                    // ✅ 使用DapperHelper进行安全的勋章隐藏操作
                                                    string updateUserMoneySql = "UPDATE [user] SET moneyname = @MoneyName WHERE siteid = @SiteId AND userid = @UserId";
                                                    DapperHelper.Execute(ConnectionString, updateUserMoneySql, new {
                                                        MoneyName = DapperHelper.LimitLength(updMoneyName, 500),
                                                        SiteId = base.siteid,
                                                        UserId = base.userVo.userid
                                                    });

                                                    //添加隐藏
                                                    var myHideMoneyName = HideMoneyName.Replace("||", "|").Trim('|');
                                                    var setHideMoneyName = (!myHideMoneyName.IsNull() ? $"{myHideMoneyName}|" : "") + this.id;
                                                    string updateHideMoneySql = "UPDATE XinZhang_Plugin SET moneyname = @HideMoneyName WHERE siteid = @SiteId AND userid = @UserId";
                                                    DapperHelper.Execute(ConnectionString, updateHideMoneySql, new {
                                                        HideMoneyName = DapperHelper.LimitLength(setHideMoneyName, 500),
                                                        SiteId = base.siteid,
                                                        UserId = base.userVo.userid
                                                    });

                                                    //立刻刷新界面数据
                                                    base.userVo.moneyname = updMoneyName;
                                                }
                                                //隐藏全部勋章
                                                else if (this.type == "隐藏全部")
                                                {
                                                    var showMoneyName = base.userVo.moneyname.Replace("||", "|").Trim('|');
                                                    var myHideMoneyName = HideMoneyName.Replace("||", "|").Trim('|');
                                                    var setHideMoneyName = (!myHideMoneyName.IsNull() ? $"{myHideMoneyName}|" : "") + showMoneyName;
                                                    //执行脚本
                                                    // ✅ 使用DapperHelper进行安全的隐藏全部勋章操作
                                                    // 删除显示
                                                    string clearUserMoneySql = "UPDATE [user] SET moneyname = '' WHERE siteid = @SiteId AND userid = @UserId";
                                                    DapperHelper.Execute(ConnectionString, clearUserMoneySql, new {
                                                        SiteId = base.siteid,
                                                        UserId = base.userVo.userid
                                                    });

                                                    // 添加隐藏
                                                    string updateHideAllSql = "UPDATE XinZhang_Plugin SET moneyname = @HideMoneyName WHERE siteid = @SiteId AND userid = @UserId";
                                                    DapperHelper.Execute(ConnectionString, updateHideAllSql, new {
                                                        HideMoneyName = DapperHelper.LimitLength(setHideMoneyName, 500),
                                                        SiteId = base.siteid,
                                                        UserId = base.userVo.userid
                                                    });
                                                    //立刻刷新界面数据
                                                    base.userVo.moneyname = "";
                                                }
                                                //显示勋章
                                                else if (this.type == "显示")
                                                {
                                                    //删除隐藏
                                                    var myHideMoneyName = HideMoneyName.Replace("||", "|").Trim('|');
                                                    var updHideMoneyName = myHideMoneyName.Replace(this.id, "").Replace("||", "|").Trim('|');
                                                    if (myHideMoneyName == updHideMoneyName)
                                                    {
                                                        this.INFO = "NO";
                                                        return;
                                                    }
                                                    // ✅ 使用DapperHelper进行安全的显示勋章操作
                                                    string updateHideSql = "UPDATE XinZhang_Plugin SET moneyname = @HideMoneyName WHERE siteid = @SiteId AND userid = @UserId";
                                                    DapperHelper.Execute(ConnectionString, updateHideSql, new {
                                                        HideMoneyName = DapperHelper.LimitLength(updHideMoneyName, 500),
                                                        SiteId = base.siteid,
                                                        UserId = base.userVo.userid
                                                    });

                                                    //还原显示
                                                    var myMoneyName = base.userVo.moneyname.Replace("||", "|").Trim('|');
                                                    var setMoneyName = (!myMoneyName.IsNull() ? $"{myMoneyName}|" : "") + this.id;
                                                    string updateShowSql = "UPDATE [user] SET moneyname = @MoneyName WHERE siteid = @SiteId AND userid = @UserId";
                                                    DapperHelper.Execute(ConnectionString, updateShowSql, new {
                                                        MoneyName = DapperHelper.LimitLength(setMoneyName, 500),
                                                        SiteId = base.siteid,
                                                        UserId = base.userVo.userid
                                                    });
                                                    //立刻刷新界面数据
                                                    base.userVo.moneyname = setMoneyName;
                                                }
                                                //显示全部勋章
                                                else if (this.type == "显示全部")
                                                {
                                                    var hideMoneyName = HideMoneyName.Replace("||", "|").Trim('|');
                                                    var myMoneyName = base.userVo.moneyname.Replace("||", "|").Trim('|');
                                                    var setMoneyName = (!myMoneyName.IsNull() ? $"{myMoneyName}|" : "") + hideMoneyName;
                                                    //执行脚本
                                                    // ✅ 使用DapperHelper进行安全的显示全部勋章操作
                                                    // 删除隐藏
                                                    string clearHideSql = "UPDATE XinZhang_Plugin SET moneyname = '' WHERE siteid = @SiteId AND userid = @UserId";
                                                    DapperHelper.Execute(ConnectionString, clearHideSql, new {
                                                        SiteId = base.siteid,
                                                        UserId = base.userVo.userid
                                                    });

                                                    // 还原显示
                                                    string restoreShowSql = "UPDATE [user] SET moneyname = @MoneyName WHERE siteid = @SiteId AND userid = @UserId";
                                                    DapperHelper.Execute(ConnectionString, restoreShowSql, new {
                                                        MoneyName = DapperHelper.LimitLength(setMoneyName, 500),
                                                        SiteId = base.siteid,
                                                        UserId = base.userVo.userid
                                                    });
                                                    //立刻刷新界面数据
                                                    base.userVo.moneyname = setMoneyName;
                                                }
                                                //未知的操作
                                                else
                                                {
                                                    this.INFO = "NO";
                                                    return;
                                                }
                                                this.INFO = "OK";
                                                num = 5;
                                                continue;
                                            case 3:
                                            case 5:
                                                num = 6;
                                                continue;
                                            case 2:
                                                this.INFO = "NOPASS";
                                                num = 3;
                                                continue;
                                            case 6:
                                                num = 7;
                                                continue;
                                            case 7:
                                                return;
                                        }
                                        break;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                this.ERROR = ex.ToString();
                                return;
                            }
                        case 1:
                            this.backurl = "xinzhang/book_view_my.aspx?siteid=" + base.siteid + "&amp;classid=" + base.classid + "&amp;lpage=" + this.lpage + "&amp;ordertype=" + this.ordertype;
                            base.IsLogin(base.userid, this.backurl);
                            num = 2;
                            continue;
                        case 3:
                            //删除
                            this.type = this.g.Contains("删除") ? "删除" : this.type;
                            this.g = this.g.Replace("删除_", "");
                            //隐藏
                            this.type = this.g.Contains("隐藏") ? "隐藏" : this.type;
                            this.g = this.g.Replace("隐藏_", "");
                            //隐藏全部
                            this.type = this.g.Contains("隐藏全部") ? "隐藏全部" : this.type;
                            //显示
                            this.type = this.g.Contains("显示") ? "显示" : this.type;
                            this.g = this.g.Replace("显示_", "");
                            //显示全部
                            this.type = this.g.Contains("显示全部") ? "显示全部" : this.type;
                            //参数传递
                            this.id = this.g;
                            num = 1;
                            continue;
                    }
                    break;
                }
            }
        }
    }
}
