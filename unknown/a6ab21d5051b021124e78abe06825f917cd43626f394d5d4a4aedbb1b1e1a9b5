#searchContainer{width:250px;max-width:50vw;opacity:0;transform:scaleX(0);transition:all 0.3s ease-in-out;}
#searchContainer.slide-in{opacity:1;transform:scaleX(1);}
#searchContainer.slide-out{opacity:0;transform:scaleX(0);}
#searchContainer.hidden{display:none;}
@media (max-width: 640px){
#searchContainer{max-width:40vw;}
}
.photo-container{max-width:720px;box-shadow:0 2px 1px -1px rgba(0, 0, 0, .2), 0 1px 1px 0 rgba(0, 0, 0, .14), 0 1px 3px 0 rgba(0, 0, 0, .12);background-color:#fff;padding:.5rem;}
.breadcrumb{display:flex;align-items:center;padding:0.45rem;color:#6b7280;}
.breadcrumb-item{display:flex;align-items:center;gap:0.375rem;color:#6b7280;text-decoration:none;transition:color 0.2s;}
.breadcrumb-item:hover{color:#374151;}
.breadcrumb-item.active{font-size:16px;cursor:pointer;}
.breadcrumb-item svg{width:16px;height:16px;}
.breadcrumb-separator{position:relative;width:14px;height:14px;margin:0 0.5rem;color:#d1d5db;}
.breadcrumb-separator::after{content:'';position:absolute;top:50%;left:50%;width:5px;height:5px;border-right:1.5px solid #d1d5db;border-top:1.5px solid #d1d5db;transform:translate(-50%, -50%) rotate(45deg);}
@media (max-width: 290px){
.uploadbutton-text::after{content:'上传';}
}
@media (min-width: 291px){
.uploadbutton-text::after{content:'上传相片';}
}
.uploadbutton-text.short::after {
    content: '上传';
}
/*! CSS Used from: Embedded */
*,::before,::after{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-scroll-snap-strictness:proximity;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgb(59 130 246 / 0.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;}
*,::after,::before{box-sizing:border-box;border-width:0;border-style:none;border-color:transparent;}
::after,::before{--tw-content:'';}
body{margin:0;line-height:inherit;}
h2{font-size:inherit;font-weight:inherit;}
a{color:inherit;text-decoration:inherit;}
button,input{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0;}
button{text-transform:none;}
button,input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none;}
h2{margin:0;}
input::placeholder{opacity:1;color:#9ca3af;}
button{cursor:pointer;}
:disabled{cursor:default;}
img,svg{display:block;vertical-align:middle;}
img{max-width:100%;height:auto;}
.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border-width:0;}
.pointer-events-none{pointer-events:none;}
.absolute{position:absolute;}
.relative{position:relative;}
.inset-0{inset:0px;}
.bottom-0{bottom:0px;}
.bottom-4{bottom:1rem;}
.left-0{left:0px;}
.right-0{right:0px;}
.right-4{right:1rem;}
.z-10{z-index:10;}
.mx-auto{margin-left:auto;margin-right:auto;}
.mb-6{margin-bottom:1.5rem;}
.ml-auto{margin-left:auto;}
.mr-2{margin-right:0.5rem;}
.block{display:block;}
.flex{display:flex;}
.inline-flex{display:inline-flex;}
.grid{display:grid;}
.aspect-square{aspect-ratio:1 / 1;}
.h-5{height:1.25rem;}
.h-full{height:100%;}
.h-10{height:2.5rem;}
.h-4{height:1rem;}
.min-h-screen{min-height:100vh;}
.w-5{width:1.25rem;}
.w-full{width:100%;}
.w-10{width:2.5rem;}
.w-4{width:1rem;}
.max-w-4xl{max-width:56rem;}
.origin-right{transform-origin:right;}
.grid-cols-2{grid-template-columns:repeat(2, minmax(0, 1fr));}
.items-center{align-items:center;}
.justify-center{justify-content:center;}
.justify-between{justify-content:space-between;}
.gap-1{gap:0.25rem;}
.gap-4{gap:1rem;}
.space-y-6 > :not([hidden]) ~ :not([hidden]){--tw-space-y-reverse:0;margin-top:calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(1.5rem * var(--tw-space-y-reverse));}
.overflow-hidden{overflow:hidden;}
.rounded-lg{border-radius:0.5rem;}
.rounded-md{border-radius:0.375rem;}
.border{border-width:1px;}
.border-t{border-top-width:1px;}
.bg-\[\#e8e8e8\]{--tw-bg-opacity:1;background-color:rgb(232 232 232 / var(--tw-bg-opacity, 1));}
.bg-gray-800{--tw-bg-opacity:1;background-color:rgb(31 41 55 / var(--tw-bg-opacity, 1));}
.bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1));}
.bg-gradient-to-t{background-image:linear-gradient(to top, 
    rgba(0, 0, 0, 0.4) 0%,      /* 底部阴影减淡到0.4 */
    rgba(0, 0, 0, 0.3) 30%,     /* 中间过渡区域减淡到0.3 */
    rgba(0, 0, 0, 0) 100%       /* 顶部保持透明 */
);}
.from-black\/60{--tw-gradient-from:rgb(0 0 0 / 0.6) var(--tw-gradient-from-position);--tw-gradient-to:rgb(0 0 0 / 0) var(--tw-gradient-to-position);--tw-gradient-stops:var(--tw-gradient-from), var(--tw-gradient-to);}
.to-transparent{--tw-gradient-to:transparent var(--tw-gradient-to-position);}
.object-cover{object-fit:cover;}
.p-2{padding:0.5rem;}
.p-4{padding:1rem;}
.px-3{padding-left:0.75rem;padding-right:0.75rem;}
.px-4{padding-left:1rem;padding-right:1rem;}
.py-2{padding-top:0.5rem;padding-bottom:0.5rem;}
.pt-4{padding-top:1rem;}
.text-sm{font-size:0.875rem;line-height:1.25rem;}
.font-medium{font-weight:500;}
.text-white{--tw-text-opacity:1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));}
.text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128 / var(--tw-text-opacity, 1));}
.opacity-70{opacity:0.7;}
.opacity-50{opacity:0.5;}
.shadow-sm{--tw-shadow:0 1px 2px 0 rgb(0 0 0 / 0.05);--tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}
.transition-colors{transition-property:color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke;transition-property:color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;}
.hover\:bg-gray-100:hover{--tw-bg-opacity:1;background-color:rgb(243 244 246 / var(--tw-bg-opacity, 1));}
.hover\:bg-gray-900:hover{--tw-bg-opacity:1;background-color:rgb(17 24 39 / var(--tw-bg-opacity, 1));}
.hover\:bg-slate-100:hover{background-color:#f1f5f9;}
.hover\:opacity-100:hover{opacity:1;}
.hover\:shadow-md:hover{--tw-shadow:0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);}
.disabled\:pointer-events-none:disabled{pointer-events:none;}
.disabled\:opacity-50:disabled{opacity:0.5;}
@media (min-width: 1024px){
.lg\:grid-cols-3{grid-template-columns:repeat(3, minmax(0, 1fr));}
}
#modernPagination {
    border-top-width: 1px;
    border-right-width: 0;
    border-bottom-width: 0;
    border-left-width: 0;
}
input, #searchToggle, #modernPagination a {
    border: 1px solid #e5e7eb;
}
.grid > div {border:1px solid #e5e7eb;}
.font-medium.text-white {
    position: relative;
    z-index: 2;
}
.absolute.bottom-4.right-4 {
    z-index: 2;
}
.hover\:transform-up:hover {
    transform: translateY(-1px);
}
.hover\:bg-black:hover {
    background-color: #000;
}
.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}
.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
.disabled a {
    pointer-events: none;
}
.disabled:hover a {
    background-color: #f1f5f9;
}
.breadcrumb-item span:hover {
    text-decoration: underline;
}

/* 添加空状态相关样式 */
.empty-state {
    border: 2px dashed #e5e7eb;
    border-radius: 8px;
    background-color: #fff;
    padding: 2rem;
    text-align: center;
    min-height: 360px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    grid-column: 1 / -1;  /* 跨越所有列 */
}

.empty-state-icon {
    width: 64px;
    height: 64px;
    color: #9ca3af;
}

.empty-state-title {
    font-size: 1.25rem;
    font-weight: 500;
    color: #111827;
    margin-bottom: 0.5rem;
}

.empty-state-text {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1.5rem;
}

.empty-state-button {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: #1f2937;
    color: #ffffff;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
}

.empty-state-button:hover {
    background-color: #111827;
    transform: translateY(-1px);
}

.empty-state-button svg {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.5rem;
}

/* 弹窗样式 */
.dialog-delete {
    padding: 0;
    border: none;
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0,0,0,0.1), 0 10px 10px -5px rgba(0,0,0,0.04);
    background: white;
    max-width: 400px;
    width: 90%;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0;
}

.dialog-delete::backdrop {
    background-color: rgba(0, 0, 0, 0.4);
}

.dialog-delete-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.dialog-delete-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: #ef4444;
}

.dialog-delete-title {
    font-size: 1.125rem;
    font-weight: 500;
    color: #111827;
    margin: 0;
}

.dialog-delete-content {
    padding: 1rem;
    color: #4b5563;
}

.dialog-delete-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    padding: .7rem 1rem;
    background: #f9fafb;
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}

.dialog-delete-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.2s;
}

.dialog-delete-button-cancel {
    background: white;
    border: 1px solid #e5e7eb;
    color: #374151;
}

.dialog-delete-button-cancel:hover {
    background: #f3f4f6;
}

.dialog-delete-button-confirm {
    background: #111827;
    color: white;
    border: none;
}

.dialog-delete-button-confirm:hover {
    background: #1f2937;
}

.image-dialog {
    padding: 0;
    border: none;
    border-radius: 0.5rem;
    background: transparent;
    max-width: 90vw;
    max-height: 90vh;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0;
}

.image-dialog img {
    max-width: 90vw;
    max-height: 85vh;
    object-fit: contain;
    border-radius: 0.375rem;
}

.image-dialog::backdrop {
    background-color: rgba(0, 0, 0, 0.75);
}

.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 10px 20px;
    background-color: #333;
    color: #fff;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s;
}

.toast.show {
    opacity: 1;
}

.toast-message {
    margin-right: 10px;
}

/* 新增：头像设置按钮样式 */
.avatar-button {
    position: absolute;
    top: 1rem;
    right: 1rem;
    padding: 0.5rem 1rem;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    z-index: 2;
    -webkit-appearance: none;
    -webkit-tap-highlight-color: transparent;
    outline: none;
}

.avatar-button:hover {
    background-color: rgba(0, 0, 0, 0.7);
}

/* 可选：添加按钮图标 */
.avatar-button svg {
    width: 1.25rem;
    height: 1.25rem;
    flex-shrink: 0;
}

.result-dialog-content {
    background: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    min-width: 300px;
    text-align: center;
}

.result-title {
    font-size: 1.125rem;
    font-weight: 500;
    color: #111827;
    margin: 0 0 1.5rem 0;
}

.result-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.result-link {
    color: #2563eb;
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.2s;
}

.result-link:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

.result-cancel {
    color: #6b7280;
    background: none;
    border: none;
    padding: 0.5rem;
    font-size: 0.875rem;
    cursor: pointer;
    transition: color 0.2s;
}

.result-cancel:hover {
    color: #374151;
}

.delete-dialog {
    padding: 0;
    border: none;
    border-radius: 0.75rem;
    background: transparent;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    margin: 0;
    max-width: 400px;
    width: 90%;
}

.delete-dialog::backdrop {
    background-color: rgba(0, 0, 0, 0.4);
}

.delete-dialog-content {
    background: white;
    border-radius: 0.75rem;
    width: 100%;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.delete-dialog-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.25rem;
    text-align: center;
    border-bottom: 1px solid #e5e7eb;
}

.delete-dialog-title {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 500;
    color: #111827;
}

.delete-dialog-footer {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.delete-dialog-confirm {
    display: block;
    width: 100%;
    padding: 0.625rem;
    background-color: #111827;
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    text-decoration: none;
    transition: background-color 0.2s;
}

.delete-dialog-confirm:hover {
    background-color: #1f2937;
}

.delete-dialog-cancel {
    display: block;
    width: 100%;
    padding: 0.625rem;
    background-color: white;
    color: #6b7280;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.delete-dialog-cancel:hover {
    background-color: #f3f4f6;
    color: #374151;
}

/* 添加遮罩样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.4);
    display: none;
    z-index: 999;
}

/* 确保 dialog 元素不会被浏览器默认样式影响 */
dialog {
    position: fixed;
    margin: 0;
    padding: 0;
}

.image-dialog::backdrop {
    background-color: rgba(0, 0, 0, 0.75);  /* 图片查看的深色遮罩 */
}

.delete-dialog::backdrop {
    background-color: rgba(0, 0, 0, 0.4);  /* 结果弹窗的浅色遮罩 */
}

/* 确保在较小屏幕上的边距适配 */
@media (max-width: 640px) {
    .image-dialog-content {
        padding: 0.5rem;  /* 较小的内边距 */
    }
    
    .image-dialog-content img {
        max-height: calc(90vh - 1rem);  /* 调整最大高度 */
    }
}

/* 小屏幕适配 */
@media (max-width: 640px) {
    .avatar-button {
        top: .5rem;
        right: .5rem;
        padding: .39rem 0.5rem;
        font-size: 0.75rem;
        gap: .25rem;
    }
    
    /* 调整按钮内的图标大小 */
    .avatar-button svg {
        width: 1rem;           /* 减小图标宽度 */
        height: 1rem;          /* 减小图标高度 */
    }
}

/* 确保在较小屏幕上也能正确显示 */
@media (max-width: 640px) {
    .dialog-delete {
        width: 85%;  /* 在小屏幕上稍微缩小宽度 */
        max-width: 350px;
    }
}

/* 添加成功图标样式 */
.success-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: #22c55e; /* 绿色 */
    margin-right: 0.75rem;
}

/* 添加活跃状态样式 */
.avatar-button:active {
    transform: scale(0.98);  /* 添加轻微的按压效果 */
}

/* 为对话框按钮移除 outline */
#searchContainer input,
.dialog-delete-button,
.delete-dialog-confirm,
.delete-dialog-cancel,
.result-cancel {
    outline: none;
    -webkit-tap-highlight-color: transparent;
}
