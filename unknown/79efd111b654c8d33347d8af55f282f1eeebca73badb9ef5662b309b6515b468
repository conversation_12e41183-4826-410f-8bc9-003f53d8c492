document.addEventListener('DOMContentLoaded', function () {
  // 检查本地存储中是否存在字体切换状态
  var fontSwitched = localStorage.getItem('bodyfontswitched');

// 如果切换状态存在且为 true，则修改 body 的字体样式为 'font-family: auto;'
if (fontSwitched && fontSwitched === 'true') {
  document.body.style.fontFamily = 'auto';
  
  // 获取所有的输入框和文本框元素
  const inputs = document.querySelectorAll('input');
  const textareas = document.querySelectorAll('textarea');
  
  // 修改所有输入框的字体样式
  inputs.forEach(input => {
    input.style.fontFamily = 'auto';
  });
  
  // 修改所有文本框的字体样式
  textareas.forEach(textarea => {
    textarea.style.fontFamily = 'auto';
  });
}


  // 函数用于切换字体属性
  function toggleFont() {
    // 切换本地存储中的状态
    if (fontSwitched && fontSwitched === 'true') {
      localStorage.setItem('bodyfontswitched', 'false');
      // 不做任何改动
    } else {
      localStorage.setItem('bodyfontswitched', 'true');
      // 移除默认字体属性
      document.body.style.removeProperty('font-family');
    }
  }

});