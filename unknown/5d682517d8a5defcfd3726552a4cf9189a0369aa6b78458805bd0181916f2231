﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ModifyPW.aspx.cs" Inherits="YaoHuo.Plugin.BBS.ModifyPW" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    StringBuilder strhtml = new StringBuilder();
	strhtml.Append("<!DOCTYPE html><html lang=\"zh-cn\" ><head><meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\"><meta http-equiv=\"Cache-Control\" content=\"no-store, no-cache, must-revalidate, max-age=0\"><meta http-equiv=\"Pragma\" content=\"no-cache\"><meta http-equiv=\"Expires\" content=\"0\"><meta charset=\"UTF-8\">  <style>");
     strhtml.Append("[v-cloak] { display: none; } body{background:#cfd9e7;font-family:Roboto,sans-serif;font-weight:400;user-select: none;} .modal{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:90%;max-width:520px;height:auto;padding:0;background:#f9fafb;box-shadow:rgba(100,100,111,.2) 0 7px 29px 0;border-radius:10px} .modal__description{margin-left:4%;margin-bottom:2%} h1,h4{margin:0;padding:0;line-height:1.2} .modal__description__title{font-size:2rem;text-align:center;color:#111827;padding-top:20px;font-weight:400;margin-right:4%} body .modal__description__rules__item .fa{color:#4ba0a0} .modal__description__details{padding-top:20px;font-size:1.2rem;color:#444;font-weight:500;margin-bottom:-5px} .modal__description__rules__item{font-size:1.2rem;line-height:2rem;color:#9ca3af} .modal__description__rules{margin-top:.6rem} .modal__password{text-align:center} .modal__password__password{width:100%;max-width:90%;height:40px;margin:.5rem 0;font-size:1.2rem;border:2px solid #eee;border-radius:5px;outline:0} input{padding:1%} .modal__password__save{margin-top:.5rem;width:100%;max-width:92%;padding:1rem;background:#4ba0a0;border:none;border-radius:3px;color:#f9fafb;text-transform:uppercase;outline:0;cursor:pointer;font-size:1.2rem;transition:background-color .3s ease-in} .modal__password__cancel{color:#ccc;text-decoration:none;font-size:1.2rem;cursor:pointer} ol,ul{list-style:none;padding:0;margin:0} .cancel{margin-top:7px;padding-bottom:.8rem} .modal__password__save:hover{background-color:#378d8d} .modal__password__cancel:hover{color:#4a4a4a} .modal__password__password{padding-left:10px}");
     strhtml.Append("@media screen and (max-width:300px){.modal__description__title{font-size:1.8rem} .modal__description__details{font-size:1.1rem} .modal__description__rules__item{font-size:1rem;line-height:1.5rem} .modal__password__password{margin:.2rem 0;font-size:1rem} .modal__password__save{width:95%;max-width:100%;padding:.8rem} .cancel a,.modal__password__save{font-size:1rem} } .ui__alert{z-index:100;position:relative} .ui__alert_border{border:1px solid #d9d9d9!important} .ui__alert *{padding:0;margin-top:0;font-size:16px} .ui__alert .ui__alert_bg{top:0;left:0;width:100%;height:100%;position:fixed;background:rgba(0,0,0,.2);animation-duration:.1s} .ui__alert .ui__alert_bg.in{animation-name:bgFadeIn} .ui__alert .ui__alert_bg.out{animation-name:bgFadeOut} .ui__alert .ui__alert_content{text-align:center;position:fixed;min-width:250px;max-width:280px;background:rgb(253 253 253);border-radius:10px;left:50%;top:50%;transform:translate(-50%,-50%);animation-duration:.1s} .ui__alert .ui__alert_content.in{animation-name:contentZoomIn} .ui__alert .ui__alert_content.out{animation-name:contentZoomOut} .ui__alert .ui__alert_content .ui__content_body{font-size:14px;padding:18px} .ui__alert .ui__alert_content .ui__content_body .ui__title{margin:15px;font-size:1.2rem} @keyframes bgFadeIn{0%{opacity:0} 100%{opacity:1} } @keyframes bgFadeOut{0%{opacity:1} 100%{opacity:0} } @keyframes contentZoomIn{0%{opacity:0;transform:translate(-50%,-50%) scale(.8)} 100%{opacity:1;transform:translate(-50%,-50%) scale(1)} } @keyframes contentZoomOut{0%{opacity:1;transform:translate(-50%,-50%) scale(1)} 100%{opacity:0;transform:translate(-50%,-50%) scale(.8)} }");
 strhtml.Append("#card {width: 320px; display: block; margin: auto; text-align: center; font-family: 'Source Sans Pro', sans-serif; position: absolute; top: 50%; left: 50%; transform: translate(-50%,-50%);} #upper-side { padding: 2em; background-color: #50adad; display: block; color: #fff; border-top-right-radius: 8px; border-top-left-radius: 8px; } #checkmark { font-weight: lighter; fill: #fff; margin: -3.5em auto auto 20px; } #status { font-weight: lighter; text-transform: uppercase; letter-spacing: 2px; font-size: 1em; margin-top: -.2em; margin-bottom: 0; } #lower-side { padding: 2em 2em 5em 2em; background: #fff; display: block; border-bottom-right-radius: 8px; border-bottom-left-radius: 8px; } #message { margin-top: -.5em; color: #757575; letter-spacing: 1px; } #contBtn { position: relative; top: 1.5em; text-decoration: none; background: #50adad; color: #fff; margin: auto; padding: .8em 3em; -webkit-box-shadow: 0px 15px 30px rgba(50, 50, 50, 0.21); -moz-box-shadow: 0px 15px 30px rgba(50, 50, 50, 0.21); box-shadow: 0px 15px 30px rgba(50, 50, 50, 0.21); border-radius: 25px; -webkit-transition: all .4s ease; -moz-transition: all .4s ease; -o-transition: all .4s ease; transition: all .4s ease; } #contBtn:hover { -webkit-box-shadow: 0px 15px 30px rgba(50, 50, 50, 0.41); -moz-box-shadow: 0px 15px 30px rgba(50, 50, 50, 0.41); box-shadow: 0px 15px 30px rgba(50, 50, 50, 0.41); -webkit-transition: all .4s ease; -moz-transition: all .4s ease; -o-transition: all .4s ease; transition: all .4s ease; }.card-overlay{position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5);}</style>");
   //strhtml.Append("<link href=\"//lib.baomitu.com/font-awesome/4.7.0/css/font-awesome.min.css\" rel=\"stylesheet\">");
   strhtml.Append("<link href=\"//lf9-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/4.7.0/css/font-awesome.min.css\" rel=\"stylesheet\"><title>更改密码</title><script src=\"/NetCSS/CSS/Login/Vue.Min.2.6.10.js\"></script> </head><body>");
strhtml.Append("<div id=\"app\" v-cloak> <div class=\"modal\"> <div class=\"modal__description\"> <h1 class=\"modal__description__title\">{{ labels.title }}</h1> <h4 class=\"modal__description__details\">{{ labels.subtitle }}</h4> <ul class=\"modal__description__rules\"> <li class=\"modal__description__rules__item\" :class=\"{'modal__description__rules__item--correct': isPasswordLengthValid}\"> <i class=\"fa fa-check\" v-if=\"isPasswordLengthValid\" aria-hidden=\"true\"></i> {{ labels.atLeastSixCharacters }} </li> <li class=\"modal__description__rules__item\" :class=\"{'modal__description__rules__item--correct': hasOneUpperCaseLetter }\"> <i class=\"fa fa-check\" v-if=\"hasOneUpperCaseLetter\" aria-hidden=\"true\"></i> {{ labels.oneUpperCaseletter }} </li> <li class=\"modal__description__rules__item\" :class=\"{'modal__description__rules__item--correct': hasOneLowerCaseLetter }\"> <i class=\"fa fa-check\" v-if=\"hasOneLowerCaseLetter\" aria-hidden=\"true\"></i> {{ labels.oneLowerCaseLetter }} </li> <li class=\"modal__description__rules__item\" :class=\"{'modal__description__rules__item--correct': hasOneNumber }\"> <i class=\"fa fa-check\" v-if=\"hasOneNumber\" aria-hidden=\"true\"></i> {{ labels.oneNumber }} </li> <li class=\"modal__description__rules__item\" :class=\"{'modal__description__rules__item--correct': arePasswordsEqual}\"> <i class=\"fa fa-check\" v-if=\"arePasswordsEqual\" aria-hidden=\"true\"></i> {{ labels.passwordsMatch }} </li> </ul> </div> <form name=\"f\" action=\"/bbs/ModifyPW.aspx\" method=\"post\"> <div class=\"modal__password\"> <div class=\"modal__password__button-wrapper\"> <div class=\"modal__password__wrapper\"> <input name=\"txtoldPW\" class=\"modal__password__password modal__password__password--old-password\" type=\"password\" v-model=\"oldPassword\" required=\"required\" minlength=\"1\" maxlength=\"50\" placeholder=\"原密码\" autocomplete=\"current-password\"/> </div> <div class=\"modal__password__wrapper\"> <input name=\"txtnewPW\" class=\"modal__password__password modal__password__password--new-password--1\" type=\"password\" v-model=\"newPassword1\" required=\"required\" minlength=\"1\" maxlength=\"50\" placeholder=\"新密码\" autocomplete=\"new-password\"/> </div> <div class=\"modal__password__wrapper\"> <input name=\"txtrePW\" class=\"modal__password__password modal__password__password--new-password--2\" type=\"password\" v-model=\"newPassword2\" required=\"required\" minlength=\"1\" maxlength=\"50\" placeholder=\"确认新密码\" autocomplete=\"new-password\"/> </div> <input name=\"action\"  type=\"hidden\" value=\"gomod\" /> <input name=\"siteid\"  type=\"hidden\" value=\"" + this.siteid + "\"  /> <input name=\"classid\"  type=\"hidden\" value=\"" + this.classid + "\"  /><button class=\"modal__password__save\">{{ labels.save }}</button> <div class=\"cancel\"><a href=\"/\" class=\"modal__password__cancel\">返回</a></div> </div> </div> </form> </div>");
    if (ERROR != "")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append(ERROR);
        strhtml.Append("</div>");
    }
    if (INFO == "OK")
    {
		strhtml.Append("<div class=\"card-overlay\"><div id='card' class=\"animated fadeIn\"> <div id='upper-side'> <?xml version=\"1.0\" encoding=\"utf-8\"?> <!-- Generator: Adobe Illustrator 17.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  --> <!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\" \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\"> <svg version=\"1.1\" id=\"checkmark\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" xml:space=\"preserve\"> <path d=\"M131.583,92.152l-0.026-0.041c-0.713-1.118-2.197-1.447-3.316-0.734l-31.782,20.257l-4.74-12.65 c-0.483-1.29-1.882-1.958-3.124-1.493l-0.045,0.017c-1.242,0.465-1.857,1.888-1.374,3.178l5.763,15.382 c0.131,0.351,0.334,0.65,0.579,0.898c0.028,0.029,0.06,0.052,0.089,0.08c0.08,0.073,0.159,0.147,0.246,0.209 c0.071,0.051,0.147,0.091,0.222,0.133c0.058,0.033,0.115,0.069,0.175,0.097c0.081,0.037,0.165,0.063,0.249,0.091 c0.065,0.022,0.128,0.047,0.195,0.063c0.079,0.019,0.159,0.026,0.239,0.037c0.074,0.01,0.147,0.024,0.221,0.027 c0.097,0.004,0.194-0.006,0.292-0.014c0.055-0.005,0.109-0.003,0.163-0.012c0.323-0.048,0.641-0.16,0.933-0.346l34.305-21.865 C131.967,94.755,132.296,93.271,131.583,92.152z\" /> <circle fill=\"none\" stroke=\"#ffffff\" stroke-width=\"5\" stroke-miterlimit=\"10\" cx=\"109.486\" cy=\"104.353\" r=\"32.53\" /> </svg> <h3 id='status'> 修改成功 </h3> </div> <div id='lower-side'> <p id='message'> 密码修改成功，请返回重新登录 </p> <a href=\"/waplogin.aspx\" id=\"contBtn\">返回</a> </div></div></div>");
		strhtml.Append("<script>function deleteCookie(name) { document.cookie = name + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'; } deleteCookie('sidyaohuo');</script>");
    }
    else if (INFO == "NULL")
    {
        strhtml.Append("<script>setTimeout(function() { var errorMessage = document.getElementById(\"errorMessage\"); if (errorMessage) { errorMessage.remove(); } }, 1500); </script><div class='ui__alert ui__alert_bg' id='errorMessage' > <div class='ui__alert_content ui__alert_border in'> <div class='ui__content_body'> <h4 class='ui__title' id='nonealertText'>");
        strhtml.Append("密码不能为空</b>");
        strhtml.Append("</h4> </div> </div> </div>");
    }
    else if (INFO == "TWOERR")
    {
        strhtml.Append("<script>setTimeout(function() { var errorMessage = document.getElementById(\"errorMessage\"); if (errorMessage) { errorMessage.remove(); } }, 1500); </script><div class='ui__alert ui__alert_bg' id='errorMessage' > <div class='ui__alert_content ui__alert_border in'> <div class='ui__content_body'> <h4 class='ui__title' id='samealertText'>");
        strhtml.Append("两次新密码不一致");
        strhtml.Append("</h4> </div> </div> </div>");
    }
    else if (INFO == "OLDERR")
    {
        strhtml.Append("<script>setTimeout(function() { var errorMessage = document.getElementById(\"errorMessage\"); if (errorMessage) { errorMessage.remove(); } }, 1500); </script><div class='ui__alert ui__alert_bg' id='errorMessage' > <div class='ui__alert_content ui__alert_border in'> <div class='ui__content_body'> <h4 class='ui__title' id='erroralertText'>");
        strhtml.Append("原密码错误");
        strhtml.Append("</h4> </div> </div> </div>");
    }
strhtml.Append(" </div> <div class=\"ui__alert ui__alert_bg\" id=\"alertMessage\" style=\"display: none;\"> <div class=\"ui__alert_content ui__alert_border in\"> <div class=\"ui__content_body\"> <h4 class=\"ui__title\" id=\"alertText\"></h4> </div> </div></div>");
strhtml.Append("<script src=\"/NetCSS/JS/ModifyPW.js\"></script>");
string isWebHtml = this.ShowWEB_view(this.classid);
    if (isWebHtml != "")
    {
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml, wmlVo).Replace("[view]", strhtml.ToString()));
        Response.End();
    }
Response.Write(strhtml);
Response.Write(WapTool.showDown(wmlVo));
 %>