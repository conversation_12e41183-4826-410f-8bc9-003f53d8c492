﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_List.aspx.cs" Inherits="YaoHuo.Plugin.Games.Rank.Book_List" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    // 构建页面标题
    string pageTitle = (type == "0" ? "净胜排行" : "赚币排行");
    string title = GamesClassManager.Tool.GetGameCN(id);
    if (this.type == "0")
    {
        title = title + "排行";
    }
    else
    {
        title = title + "排行";
    }
    // 计算分页偏移，默认每页15条，确保 pageIndex 最小为1
    int pageIndex = 1;
    int tmpIndex;
    if (int.TryParse(Request.QueryString["page"], out tmpIndex) && tmpIndex > 0)
    {
        pageIndex = tmpIndex;
    }
    int itemsPerPage = 10;
    int offset = (pageIndex - 1) * itemsPerPage;
    string headHtml = "<meta charset=\"UTF-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\">\n<link rel=\"stylesheet\" href=\"/games/chuiniu/tailwind.min.css?2\"/> <link rel=\"stylesheet\" href=\"/games/chuiniu/styles.css?25\"/>\n<link rel=\"stylesheet\" href=\"//lf6-cdn-tos.bytecdntp.com/cdn/expire-1-y/font-awesome/6.0.0/css/all.min.css\"/>";
    Response.Write(WapTool.showTop(title, wmlVo, false, headHtml));
    // Header
    strhtml.Append("<header class='bg-gradient-to-r from-teal-500 to-teal-700 shadow-md p-4 flex items-center sticky top-0 z-20 text-white'>");
    strhtml.Append("<a href='" + http_start + "games/chuiniu/index.aspx' class='text-white mr-4 hover:opacity-80 transition-opacity'><i class='fas fa-arrow-left text-lg'></i></a>");
    strhtml.Append("<h1 id='header-title' class='text-xl font-semibold flex items-center'>");
    strhtml.Append("<i id='header-icon' class='fas fa-" + (type == "0" ? "trophy" : "coins") + " mr-2'></i>");
    strhtml.Append("<span id='header-text'>" + pageTitle + "</span>");
    strhtml.Append("</h1>");
    strhtml.Append("</header>");
    // Main
    strhtml.Append("<main class='p-4 pb-24 max-w-lg mx-auto'>");
    // Tabs 链接
    strhtml.Append("<div class='flex border-b border-gray-200 mb-5 bg-white rounded-t-lg shadow-sm'>");
    strhtml.Append("  <button id='btn-times' class='flex-1 py-3 px-2 text-sm font-medium text-center transition-colors text-teal-600 border-b-2 border-teal-600'><i class='fas fa-trophy mr-1'></i>净胜排行</button>");
    strhtml.Append("  <button id='btn-money' class='flex-1 py-3 px-2 text-sm font-medium text-center transition-colors text-gray-500 hover:text-teal-600'><i class='fas fa-coins mr-1'></i>赚币排行</button>");
    strhtml.Append("</div>");
    // 提取榜单项渲染函数
    Func<dynamic, int, string, string, string> renderRankItem = (item, rankNo, colorClass, valueHtml) =>
    {
        return "<div class='flex items-center p-4 hover:bg-gray-50 transition duration-150 ease-in-out'>"
            + "<div class='w-6 h-6 flex items-center justify-center rounded-full font-bold text-sm " + colorClass + " mr-4'>" + rankNo + "</div>"
            + "<div class='flex-grow'><p class='font-medium text-gray-900 text-base truncate'><a href='" + http_start + "bbs/userinfo.aspx?touserid=" + item.userid + "'>" + item.nickName + "</a></p></div>"
            + valueHtml
            + "</div>";
    };
    // 【列表内容：双面板】
    // 净胜排行面板
    strhtml.Append("<div id='tab-times' class='active'>");
    if (listRankTimes != null && listRankTimes.Count > 0)
    {
        if (pageIndex == 1)
        {
            // Top3 卡片
            strhtml.Append("<div class='bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden mb-4'>");
            strhtml.Append("<div class='p-3 bg-gradient-to-b from-teal-50 to-white border-b border-teal-100'>");
            strhtml.Append("<div class='flex justify-between items-center'><div class='flex items-center text-teal-700 font-semibold text-sm'><i class='fas fa-crown mr-1.5 text-teal-500'></i>净胜前三名</div><div class='text-xs text-gray-500'>实时更新</div></div>");
            strhtml.Append("</div><div class='divide-y divide-gray-100'>");
            for (int i = 0; i < listRankTimes.Count && i < 3; i++)
            {
                var item = listRankTimes[i];
                string grad = i == 0 ? "bg-gradient-to-br from-amber-400 to-yellow-500" : (i == 1 ? "bg-gradient-to-br from-slate-400 to-gray-500" : "bg-gradient-to-br from-orange-500 to-amber-700");
                strhtml.Append(renderRankItem(item, offset + i + 1, grad + " text-white shadow", "<div class='flex items-center text-teal-600 font-semibold text-sm ml-2 shrink-0'>" + item.rankTimes + " 局</div>"));
            }
            strhtml.Append("</div></div>");
            // 更多列表
            strhtml.Append("<div class='bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden divide-y divide-gray-100'>");
            for (int idx = 3; idx < listRankTimes.Count; idx++)
            {
                var item = listRankTimes[idx]; int rankNo = offset + idx + 1;
                strhtml.Append(renderRankItem(item, rankNo, "bg-gray-200 text-gray-700", "<div class='flex items-center text-teal-600 font-medium text-sm ml-2 shrink-0'>" + item.rankTimes + " 局</div>"));
            }
            strhtml.Append("</div>");
        }
        else
        {
            // 分页后一页
            strhtml.Append("<div class='bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden divide-y divide-gray-100'>");
            for (int idx = 0; idx < listRankTimes.Count; idx++)
            {
                var item = listRankTimes[idx]; int rankNo = offset + idx + 1;
                strhtml.Append(renderRankItem(item, rankNo, "bg-gray-200 text-gray-700", "<div class='flex items-center text-teal-600 font-medium text-sm ml-2 shrink-0'>" + item.rankTimes + " 局</div>"));
            }
            strhtml.Append("</div>");
        }
    }
    else
    {
        strhtml.Append("<div class='p-4 bg-white text-center text-gray-500'>暂无记录！</div>");
    }
    strhtml.Append("</div>");
    // 赚币排行面板
    strhtml.Append("<div id='tab-money'>");
    if (listRankMoney != null && listRankMoney.Count > 0)
    {
        if (pageIndex == 1)
        {
            // Top3 卡片
            strhtml.Append("<div class='bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden mb-4'>");
            strhtml.Append("<div class='p-3 bg-gradient-to-b from-amber-50 to-white border-b border-amber-100'>");
            strhtml.Append("<div class='flex justify-between items-center'><div class='flex items-center text-amber-700 font-semibold text-sm'><i class='fas fa-crown mr-1.5 text-amber-500'></i>赚币前三名</div><div class='text-xs text-gray-500'>实时更新</div></div>");
            strhtml.Append("</div><div class='divide-y divide-gray-100'>");
            for (int i = 0; i < listRankMoney.Count && i < 3; i++)
            {
                var item = listRankMoney[i]; string grad = i == 0 ? "bg-gradient-to-br from-amber-400 to-yellow-500" : (i == 1 ? "bg-gradient-to-br from-slate-400 to-gray-500" : "bg-gradient-to-br from-orange-500 to-amber-700");
                strhtml.Append(renderRankItem(item, offset + i + 1, grad + " text-white shadow", "<div class='flex items-center text-amber-600 font-semibold text-sm ml-2 shrink-0'>" + item.rankMoney + " <i class='fas fa-coins ml-1'></i></div>"));
            }
            strhtml.Append("</div></div>");
            // 更多列表
            strhtml.Append("<div class='bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden divide-y divide-gray-100'>");
            for (int idx = 3; idx < listRankMoney.Count; idx++)
            {
                var item = listRankMoney[idx]; int rankNo = offset + idx + 1;
                strhtml.Append(renderRankItem(item, rankNo, "bg-gray-200 text-gray-700", "<div class='flex items-center text-amber-600 font-medium text-sm ml-2 shrink-0'>" + item.rankMoney + " <i class='fas fa-coins ml-1'></i></div>"));
            }
            strhtml.Append("</div>");
        }
        else
        {
            // 分页后
            strhtml.Append("<div class='bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden divide-y divide-gray-100'>");
            for (int idx = 0; idx < listRankMoney.Count; idx++)
            {
                var item = listRankMoney[idx]; int rankNo = offset + idx + 1;
                strhtml.Append(renderRankItem(item, rankNo, "bg-gray-200 text-gray-700", "<div class='flex items-center text-amber-600 font-medium text-sm ml-2 shrink-0'>" + item.rankMoney + " <i class='fas fa-coins ml-1'></i></div>"));
            }
            strhtml.Append("</div>");
        }
    }
    else
    {
        strhtml.Append("<div class='p-4 bg-white text-center text-gray-500'>暂无记录！</div>");
    }
    strhtml.Append("</div>");
    // 原始分页 (隐藏)
    strhtml.Append("<div class='mt-4 original-pager'>" + linkURL + "</div>");
    // 美化分页
    // 解析页码信息和链接，并根据当前页码判断上一页/下一页是否可用
    int curPage = 1, totPage = 1;
    string pageInfo = "第 1/1 页";
    string prevUrl = "#", nextUrl = "#";
    var mp = System.Text.RegularExpressions.Regex.Match(linkURL, "第\\s*(\\d+)/(\\d+)\\s*页");
    if (mp.Success)
    {
        curPage = int.Parse(mp.Groups[1].Value);
        totPage = int.Parse(mp.Groups[2].Value);
        pageInfo = "第 " + curPage + "/" + totPage + " 页";
    }
    // 提取链接
    var mprev = System.Text.RegularExpressions.Regex.Match(linkURL, "<a[^>]*href=\"([^\"]*)\"[^>]*>上一页</a>");
    if (mprev.Success) { prevUrl = mprev.Groups[1].Value; }
    var mnext = System.Text.RegularExpressions.Regex.Match(linkURL, "<a[^>]*href=\"([^\"]*)\"[^>]*>下一页</a>");
    if (mnext.Success) { nextUrl = mnext.Groups[1].Value; }
    bool hasPrev = curPage > 1;
    bool hasNext = curPage < totPage;
    strhtml.Append("<div id='pager' class='flex justify-between items-center pt-4'>");
    // 上一页按钮
    if (hasPrev)
    {
        strhtml.Append("<a href='" + prevUrl + "' class='flex items-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 transition duration-150 ease-in-out'><i class='fas fa-chevron-left text-xs'></i></a>");
    }
    else
    {
        strhtml.Append("<a href='#' class='flex items-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 opacity-50 cursor-not-allowed'><i class='fas fa-chevron-left text-xs'></i></a>");
    }
    // 页码显示
    strhtml.Append("<span class='text-sm text-gray-500'>" + pageInfo + "</span>");
    // 下一页按钮
    if (hasNext)
    {
        strhtml.Append("<a href='" + nextUrl + "' class='flex items-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 transition duration-150 ease-in-out'><i class='fas fa-chevron-right text-xs'></i></a>");
    }
    else
    {
        strhtml.Append("<a href='#' class='flex items-center px-4 py-2 rounded-lg border border-gray-300 bg-white text-gray-700 opacity-50 cursor-not-allowed'><i class='fas fa-chevron-right text-xs'></i></a>");
    }
    strhtml.Append("</div>");
    strhtml.Append("</main>");
    // 添加当前排行榜类型变量，让JS可以获取
    strhtml.Append("<script>window.rankDefaultType = '" + type + "';</script>");
    strhtml.Append("<script src='rank.js'></script>");
    strhtml.Append("</body></html>");
    Response.Write(strhtml.ToString());
%>