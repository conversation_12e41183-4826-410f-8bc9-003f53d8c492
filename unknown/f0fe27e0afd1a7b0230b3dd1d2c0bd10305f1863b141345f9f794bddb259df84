﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_Re_addfile.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Book_Re_addfile" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    wmlVo.mycss += "\r\n<link href=\"/netcss/css/upload-resource.css?3\" rel=\"stylesheet\" type=\"text/css\"/>";
    Response.Write(WapTool.showTop(this.GetLang("文件回复|文件回复贴|add subject"), wmlVo));
    if (num > 9) num = 9;
    if (num < 1) num = 1;
    StringBuilder strhtml = new StringBuilder();
    strhtml.Append("<div class=\"upload-container\">");
    strhtml.Append("<div class=\"page-title\">文件回复</div>");
    strhtml.Append(this.ERROR);
    if (this.INFO == "OK")
    {
        strhtml.Append("<div class=\"upload-success\">");
        strhtml.Append("<div class=\"upload-success-header\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-upload\"><path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"></path><polyline points=\"17 8 12 3 7 8\"></polyline><line x1=\"12\" x2=\"12\" y1=\"3\" y2=\"15\"></line></svg>");
        strhtml.Append("<div class=\"upload-success-title\">文件回复成功！</div>");
        strhtml.Append("</div>");
        if (siteVo.isCheck == 1)
        {
            strhtml.Append("<div class=\"upload-success-subtitle\">审核后显示！</div>");
        }
        strhtml.Append("<div class=\"upload-success-subtitle\">获得" + WapTool.GetSiteMoneyName(siteVo.sitemoneyname, this.lang) + ":" + getmoney + "，获得经验:" + getexpr + "</div>");
        strhtml.Append("</div>");
    }
    else if (!string.IsNullOrEmpty(this.INFO))
    {
        strhtml.Append("<div class=\"tip\">");
        if (this.INFO == "EXTERR")
        {
            Response.Write("<b>上传文件格式错误，只允许上传：" + siteVo.UpFileType + "</b><br/>");
        }
        else if (this.INFO == "NOTSPACE")
        {
            Response.Write("<b>网站总空间已经大于系统分配给此网站的最大空间了，网站空间：" + siteVo.sitespace + "M；此网站已使用：" + (siteVo.myspace) + "KB</b><br/>");
        }
        else if (this.INFO == "MAXFILE")
        {
            Response.Write("<b>你上传的单个文件超出了最大限制" + siteVo.MaxFileSize + "KB</b><br/>");
        }
        else if (this.INFO == "NULL")
        {
            Response.Write("<b>内容不能小于" + contentmax + "个字符！</b><br/>");
        }
        else if (this.INFO == "ERR_FORMAT")
        {
            Response.Write("<b>取到非法值:“$$(”请更换手机浏览器或重新编辑！</b><br/>");
        }
        else if (this.INFO == "REPEAT")
        {
            Response.Write("<b>请不要发表重复内容</b><br/>");
        }
        else if (this.INFO == "ERROR_Secret")
        {
            Response.Write("<div class=\"tip\"><b>暗号错误，如果忘记联系站长索取！</b><br/></div>");
        }
        else if (this.INFO == "MAX")
        {
            Response.Write("<b>今天你已超过回贴限制：" + this.KL_CheckBBSreCount + " 个贴子了，请明天再来！</b><br/>");
        }
        else if (this.INFO == "LOCK")
        {
            Response.Write("<b>抱歉，您已经被加入黑名单，请注意发贴规则！</b><br/>");
        }
        else if (this.INFO == "NOMONEY")
        {
            Response.Write("<b>你当前的只有:" + userVo.money + "个，发贴需要扣掉：" + getmoney2 + "个</b><br/>");
        }
        strhtml.Append("</div>");
    }
    strhtml.Append("<div class=\"content\">");
    if (this.INFO != "OK")
    {
        // 移除原来的上传数量选择表单
        // 改用新的文件上传方式
        strhtml.Append("<form id=\"uploadForm\" name=\"f\" action=\"" + http_start + "bbs/book_re_addfile.aspx\" enctype=\"multipart/form-data\" method=\"post\">");
        // 添加这些必要的hidden字段
        strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"lpage\" value=\"" + lpage + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"id\" value=\"" + id + "\"/>");
        // 表情选择
        strhtml.Append("<div class=\"emoji-button-wrapper\">");
        strhtml.Append("<button type=\"button\" class=\"emoji-button\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-smile-plus h-4 w-4 mr-2\" data-id=\"10\"><path d=\"M22 11v1a10 10 0 1 1-9-10\"></path><path d=\"M8 14s1.5 2 4 2 4-2 4-2\"></path><line x1=\"9\" x2=\"9.01\" y1=\"9\" y2=\"9\"></line><line x1=\"15\" x2=\"15.01\" y1=\"9\" y2=\"9\"></line><path d=\"M16 5h6\"></path><path d=\"M19 2v6\"></path></svg>");
        strhtml.Append("<span>表情</span>");
        strhtml.Append("</button>");
        strhtml.Append("<select name=\"face\" class=\"face-select\" style=\"display:none;\">");
        strhtml.Append("<option value=\"\">表情</option>");
        for (int i = 0; (facelist != null && i < this.facelist.Length); i++)
        {
            strhtml.Append("<option value=\"" + this.facelistImg[i] + "\">" + this.facelist[i] + "</option>");
        }
        strhtml.Append("</select>");
        strhtml.Append("</div>");
        // 内容输入区
        strhtml.Append("<textarea name=\"book_content\" class=\"content-textarea\" required=\"required\" placeholder=\"请不要乱打字回复，以免被加黑。\" oninput=\"adjustTextareaHeight(this)\">" + book_content + "</textarea>");
        // 新的文件上传区域
        strhtml.Append("<input type=\"file\" id=\"multiFileSelect\" style=\"display:none\" multiple accept=\".txt,.zip,.rar,.7z,.apk,.jpg,.jpeg,.png,.gif,.webp,.torrent,.mp3,.wma,.wav,.pdf,.xls,.doc,.docx\" />");
        strhtml.Append("<div id=\"fileSelectArea\" class=\"file-select-area\" onclick=\"document.getElementById('multiFileSelect').click()\" ondrop=\"handleDrop(event)\" ondragover=\"handleDragOver(event)\" ondragleave=\"handleDragLeave(event)\">");
        strhtml.Append("<div class=\"big-upload-icon\">+</div>");
        strhtml.Append("<div class=\"upload-text\">点击此处上传文件</div>");
        strhtml.Append("</div>");
        strhtml.Append("<div id=\"fileUploadContainer\"></div><script type=\"text/javascript\" src=\"/netcss/js/fileupload/file-common.js\"></script>");
        strhtml.Append(@"<script>document.addEventListener('DOMContentLoaded',function(){updateFileDisplay({isReply:true})});</script>");
        strhtml.Append("<input type=\"hidden\" id=\"numInput\" name=\"num\" value=\"1\"/>");
        strhtml.Append("<button type=\"submit\" name=\"g\" id=\"submitBtn\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-paperclip h-4 w-4 mr-2\" data-id=\"37\"><path d=\"m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48\"></path></svg>");
        strhtml.Append(this.GetLang("附件回帖|回复贴子|submit new back") + "</button>");
        strhtml.Append("</form>");
    }
    strhtml.Append("</div>");
    strhtml.Append("<div class=\"nav-buttons\">");
    strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/book_view.aspx?action=class&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;id=" + this.id + "&amp;lpage=" + this.lpage + "\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m12 19-7-7 7-7\"/><path d=\"M19 12H5\"/></svg>" + this.GetLang("返回主题|返回主题|Back to main") + "</a>");
    strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/book_view_ubb.aspx?action=class&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;page=" + this.page + "&amp;backurl=" + HttpUtility.UrlEncode("bbs/book_re_addfile.aspx?siteid=" + this.siteid + "&classid=" + this.classid + "&id=" + this.id) + "\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12 20h9\"/><path d=\"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z\"/></svg>" + this.GetLang("UBB方法|查看UBB方法|view UBB fuction") + "</a>");
    strhtml.Append("</div>");
    strhtml.Append("</div><script src=\"/netcss/js/bookre/addfile.js\"></script>");
    Response.Write(strhtml);
    Response.Write(WapTool.showDown(wmlVo));
%>