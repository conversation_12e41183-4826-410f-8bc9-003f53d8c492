﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="FavList.aspx.cs" Inherits="YaoHuo.Plugin.BBS.FavList" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    string title = "";
    if (favtypeid == "0")
    {
        title = this.GetLang("收藏夹||");
    }
    else if (bookVo != null)
    {
        title = bookVo.subjectname;
    }
    StringBuilder strhtml = new StringBuilder();
    Response.Write(WapTool.showTop(title, wmlVo));
    strhtml.Append("<div class=\"title\"><a href=\"/\">首页</a>><a href=\"/myfile.aspx\">我的地盘</a>>" + title + "</div>");
    strhtml.Append("<div class=\"content\">");
    strhtml.Append("<form name=\"f\" action=\"" + http_start + "bbs/favlist.aspx\" method=\"post\">");
    strhtml.Append("<input type=\"text\" style=\"width:50%;max-width: 200px;height:19px;\" required=\"required\" name=\"key\" value=\"" + key + "\" size=\"22\"/>");
    strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"class\" />");
    strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\" />");
    strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\" />");
    strhtml.Append("<input type=\"hidden\" name=\"backurl\" value=\"" + (backurl) + "\" />");
    strhtml.Append("<input type=\"hidden\" name=\"favtypeid\" value=\"" + (this.favtypeid) + "\" />");
    strhtml.Append("<input type=\"submit\" name=\"f\" value=\"" + this.GetLang("搜索收藏|搜索|Search") + "\"/>");
    strhtml.Append("</form></div>");
    for (int i = 0; (listVo != null && i < listVo.Count); i++)
    {
        if (i % 2 == 0)
        {
            strhtml.Append("<div class=\"line2\">");
        }
        else
        {
            strhtml.Append("<div class=\"line1\">");
        }
        if (listVo[i].url.IndexOf("http://") >= 0)
        {
            strhtml.Append("<a href=\"" + listVo[i].url + "\">" + listVo[i].title + "</a>");
        }
        else
        {
            strhtml.Append("<a href=\"" + http_start + listVo[i].url + "" + "\">" + listVo[i].title + "</a>");
        }
        strhtml.Append(" [<a href=\"" + http_start + "bbs/favlist_del.aspx?action=godel&amp;siteid=" + this.siteid + "&amp;classid=0&amp;favtypeid=" + this.favtypeid + "&amp;id=" + listVo[i].id + "&amp;backurl=" + HttpUtility.UrlEncode(backurl) + "&amp;page=" + this.CurrentPage + "" + "\">删除</a>]<br/>");
        strhtml.Append("(" + listVo[i].adddate + ")</div>");
    }
    if (listVo == null)
    {
        strhtml.Append("<div class=\"tip\">没有记录</div>");
    }
    strhtml.Append(linkURL);
    string isWebHtml = this.ShowWEB_view(this.classid);
    if (isWebHtml != "")
    {
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml, wmlVo).Replace("[view]", strhtml.ToString()));
        Response.End();
    }
    strhtml.Append("<div class=\"btBox\"><div class=\"bt1\">");
    strhtml.Append("<a class=\"noafter\" href=\"" + http_start + "bbs/favlist_del.aspx?action=delall&amp;siteid=" + this.siteid + "&amp;classid=0&amp;favtypeid=" + this.favtypeid + "&amp;backurl=" + HttpUtility.UrlEncode(backurl) + "" + "\">清空收藏</a>");
    strhtml.Append("<a href=\"" + this.http_start + "wapindex.aspx?siteid=" + siteid + "&amp;classid=0" + "\">返回首页</a>");
    strhtml.Append("</div></div>");
    Response.Write(strhtml);
    Response.Write(ERROR);
    Response.Write(WapTool.showDown(wmlVo));
%>