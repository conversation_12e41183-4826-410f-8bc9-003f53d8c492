/**
 * DomHelpers.js - 通用DOM辅助函数
 * 提供常用的DOM操作和辅助函数，用于多个页面共享
 */
(function() {
    'use strict';

    /**
     * 逐级检查点击的元素及其父元素，找到回复链接元素
     * @param {HTMLElement} element - 起始元素
     * @return {HTMLElement|null} - 找到的回复链接元素或null
     */
    function findParentReplyLink(element) {
        while (element) {
            if (element.classList.contains('replyicon') || element.classList.contains('replyme')) {
                return element;
            }
            element = element.parentElement;
        }
        return null;
    }

    /**
     * 使textarea高度自适应内容
     * @param {HTMLTextAreaElement} textarea - 文本域元素
     */
    function adjustTextareaHeight(textarea) {
        textarea.style.height = "auto";
        textarea.style.height = (textarea.scrollHeight) + "px";
    }

    /**
     * 为所有textarea添加自适应高度功能
     */
    function setupAutoResizeTextareas() {
        // 获取所有的textarea元素
        var textareas = document.getElementsByTagName("textarea");
        // 遍历所有textarea元素
        for (var i = 0; i < textareas.length; i++) {
            // 添加input事件监听器，当文本内容发生变化时自动调整高度
            textareas[i].addEventListener("input", function() {
                adjustTextareaHeight(this);
            });
            // 初始化时也要调整一次高度
            textareas[i].style.overflowY = "hidden"; // 隐藏滚动条
            textareas[i].style.height = "auto";
            textareas[i].style.height = (textareas[i].scrollHeight) + "px";
        }
    }

    /**
     * 为新加载内容中的下拉元素添加hover效果
     */
    function applyHoverEffectToNewContent() {
        document.querySelectorAll('.dropdown').forEach(function(dropDownElem) {
            var dropdownContent = dropDownElem.querySelector('.dropdown-content');
            if (dropdownContent) {
                dropDownElem.addEventListener('mouseover', function() {
                    dropdownContent.style.display = 'block';
                });
                dropDownElem.addEventListener('mouseout', function() {
                    dropdownContent.style.display = 'none';
                });
            }
        });
    }

    /**
     * 重新应用hover效果到所有现有内容
     */
    function refreshAllHoverEffects() {
        document.querySelectorAll('.dropdown').forEach(function(dropDownElem) {
            dropDownElem.classList.remove('hover-effect'); // 先移除可能已有的class
            void dropDownElem.offsetWidth; // 强制浏览器重新计算样式
            dropDownElem.classList.add('hover-effect'); // 重新添加class
        });
    }
    
    /**
     * 添加下拉菜单触摸支持
     */
    function addDropdownTouchSupport() {
        // 此函数在原始代码中被调用但未定义
        // 保留函数名以确保API兼容性
        console.log("Touch support for dropdowns initialized");
    }

    // 导出公共函数
    window.DomHelpers = {
        findParentReplyLink: findParentReplyLink,
        adjustTextareaHeight: adjustTextareaHeight,
        setupAutoResizeTextareas: setupAutoResizeTextareas,
        applyHoverEffectToNewContent: applyHoverEffectToNewContent,
        refreshAllHoverEffects: refreshAllHoverEffects,
        addDropdownTouchSupport: addDropdownTouchSupport
    };
})(); 