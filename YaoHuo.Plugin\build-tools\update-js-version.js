const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 获取所有需要更新的模板文件
const templateDir = path.resolve(__dirname, '../Template');
const hbsFiles = glob.sync('**/*.hbs', { cwd: templateDir });

// 生成新的版本号
const newVersion = Date.now();

// 需要更新的JS文件映射（TypeScript编译后的JS文件）
const jsFiles = [
    'BankList.js',
    'Medal.js',
    'FavList.js',
    'AjaxService.js',
    'ModalService.js',
    'PaginationService.js',
    'ToastService.js',
    'CommonTypes.js'
];

let updatedFiles = 0;

hbsFiles.forEach(hbsFile => {
    const filePath = path.join(templateDir, hbsFile);

    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let hasChanges = false;

        // 更新TypeScript编译的JS文件版本号 - 匹配 import '/Template/TS/pages/BankList.js?v=*************' 格式
        jsFiles.forEach(jsFile => {
            // 匹配 import 语句中的版本号
            const importRegex = new RegExp(`import\\s+([^']*)'([^']*${jsFile})\\?v=\\d+'`, 'g');
            const newImportContent = content.replace(importRegex, `import $1'$2?v=${newVersion}'`);

            // 匹配 import 语句中没有版本号的情况
            const importNoVersionRegex = new RegExp(`import\\s+([^']*)'([^']*${jsFile})'`, 'g');
            const newImportNoVersionContent = newImportContent.replace(importNoVersionRegex, `import $1'$2?v=${newVersion}'`);

            // 匹配 src 属性中的版本号
            const srcRegex = new RegExp(`src="([^"]*${jsFile})\\?v=\\d+"`, 'g');
            const newSrcContent = newImportNoVersionContent.replace(srcRegex, `src="$1?v=${newVersion}"`);

            // 匹配 src 属性中没有版本号的情况
            const srcNoVersionRegex = new RegExp(`src="([^"]*${jsFile})"`, 'g');
            const finalContent = newSrcContent.replace(srcNoVersionRegex, `src="$1?v=${newVersion}"`);

            if (finalContent !== content) {
                content = finalContent;
                hasChanges = true;
            }
        });

        if (hasChanges) {
            fs.writeFileSync(filePath, content, 'utf8');
            updatedFiles++;
            console.log(`Updated JS versions in: ${hbsFile}`);
        }

    } catch (error) {
        console.error(`Error updating ${hbsFile}: ${error.message}`);
    }
});

console.log(`\nUpdated ${updatedFiles} template files with new JS version: ${newVersion}`);
