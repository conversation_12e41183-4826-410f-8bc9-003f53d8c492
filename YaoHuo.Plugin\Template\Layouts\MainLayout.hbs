<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>{{PageTitle}}</title>
    <script src="/NetCSS/JS/BBS/Lucide.0.511.0.min.js"></script>
    <script src="/NetCSS/JS/BBS/ui-switcher.js"></script>
    {{!-- 移除 CDN 引入和内联配置/样式 --}}
    {{!-- <script src="https://cdn.tailwindcss.com/3.4.16"></script> --}}
    {{!-- <script>...</script> --}}
    {{!-- <style>...</style> --}}

    {{!-- 引用本地构建的 Tailwind CSS 文件 --}}
    <link rel="stylesheet" href="/Template/CSS/output.css?1751886180762">

    {{#if PageSpecificCss}}
    <link rel="stylesheet" href="{{PageSpecificCss}}">
    {{/if}}
</head>
<body>
    <div class="container">
        {{> Header PageTitle=PageTitle HeaderOptions=HeaderOptions}}
        
        <div class="main-content">
            {{{Content}}}
        </div>
    </div>

    <script>
        // 初始化 Lucide 图标
        lucide.createIcons();

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // UI切换器已在ui-switcher.js中自动初始化
            // 卡片加载动画已移除以解决滚动问题
        });
    </script>
</body>
</html> 