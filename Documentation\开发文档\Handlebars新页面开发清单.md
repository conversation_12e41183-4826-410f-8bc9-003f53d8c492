# 新页面开发检查清单

在实现新的Handlebars页面时，请按照此检查清单确保避免常见错误。

## 📋 开发前准备

### 1. 文件结构检查
- [ ] 确认页面模板路径：`YaoHuo.Plugin/Template/Pages/PageName.hbs`
- [ ] 确认数据模型路径：`YaoHuo.Plugin/Template/Models/PageNameModel.cs`
- [ ] 确认后端文件路径：`YaoHuo.Plugin/BBS/PageName.aspx.cs`

### 2. 依赖检查
- [ ] 确认TemplateService可用
- [ ] 确认HeaderOptionsModel存在
- [ ] 确认CommonModels.cs中的公共模型

## 🔧 代码实现检查

### 3. Using语句检查
确保在.aspx.cs文件中添加了必要的using语句：

```csharp
using System;
using System.Collections.Generic;
using System.Web;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.Template.Models;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite.Tool;        // ⚠️ 必需：TemplateService
using YaoHuo.Plugin.BBS.Models;          // ⚠️ 必需：HeaderOptionsModel
```

### 4. 数据模型实现检查
- [ ] **继承基础模型**：选择合适的基类
  - [ ] 简单页面：继承 `BasePageModel`
  - [ ] 带分页页面：继承 `BasePageModelWithPagination`
- [ ] **设置页面标题**：在构造函数中设置 `PageTitle`
- [ ] **只定义页面特有属性**：避免重复定义公共属性
- [ ] **添加必要的using语句**：`using YaoHuo.Plugin.Template.Models;`
- [ ] **添加适当的注释和文档**

**简单页面模型示例**：
```csharp
using YaoHuo.Plugin.Template.Models;

public class PageNameModel : BasePageModel
{
    public PageNameModel()
    {
        PageTitle = "页面标题";
    }

    // 只定义页面特有的属性
    public string SpecialProperty { get; set; }
}
```

**带分页页面模型示例**：
```csharp
using YaoHuo.Plugin.Template.Models;

public class PageListModel : BasePageModelWithPagination
{
    public PageListModel()
    {
        PageTitle = "列表页面";
    }

    // 只定义页面特有的属性
    public List<ItemModel> Items { get; set; } = new List<ItemModel>();
}
```

### 5. 后端实现检查
- [ ] 实现UI偏好检查逻辑
- [ ] 使用直接调用而非反射调用TemplateService
- [ ] 添加适当的错误处理
- [ ] 实现数据模型构建方法

**标准的RenderWithHandlebars方法**：
```csharp
private void RenderWithHandlebars()
{
    try
    {
        // 构建页面数据模型
        var pageModel = BuildPageModel();

        // ✅ 直接调用，避免反射
        string finalHtml = TemplateService.RenderPageWithLayout(
            "~/Template/Pages/PageName.hbs",
            pageModel,
            "页面标题",
            new HeaderOptionsModel { ShowViewModeToggle = false }
        );

        // 输出渲染结果
        Response.Clear();
        Response.ContentType = "text/html; charset=utf-8";
        Response.Write(finalHtml);
        Response.End();
    }
    catch (System.Threading.ThreadAbortException)
    {
        throw; // Response.End()的正常行为
    }
    catch (Exception ex)
    {
        // 错误处理
        Response.Clear();
        Response.ContentType = "text/html; charset=utf-8";
        Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}</div>");
        HttpContext.Current.ApplicationInstance.CompleteRequest();
    }
}
```

### 6. 模板实现检查
- [ ] 使用正确的Handlebars语法
- [ ] 实现响应式设计
- [ ] 添加必要的CSS样式
- [ ] 实现JavaScript交互功能

### 6.1 内容处理检查 📝 新增重要检查项
- [ ] **UBB标签处理函数选择**：
  - [ ] 单条内容使用 `WapTool.UBBCode(content, wmlVo)`
  - [ ] 完整HTML文档使用 `WapTool.ToWML(htmlString, wmlVo)`
- [ ] **UBB标签显示测试**：
  - [ ] 测试图片标签：`[img=300]图片URL[/img]`
  - [ ] 测试链接标签：`[url=链接地址]显示文字[/url]`
  - [ ] 测试文字样式：`[b]粗体[/b]`、`[color=red]红色文字[/color]`
- [ ] **新旧版本对比**：确认内容显示效果一致性

## 🧪 测试检查

### 7. 功能测试
- [ ] 新版UI正常显示
- [ ] 旧版UI仍然工作
- [ ] UI切换功能正常
- [ ] 数据正确显示
- [ ] 交互功能正常

### 8. 兼容性测试
- [ ] 桌面端显示正常
- [ ] 移动端显示正常
- [ ] 不同浏览器兼容
- [ ] 权限控制正确

### 9. 错误处理测试
- [ ] 模板文件缺失时的处理
- [ ] 数据异常时的处理
- [ ] 网络错误时的处理

## 🚀 部署前检查

### 10. 代码质量检查
- [ ] 无编译错误
- [ ] 无编译警告
- [ ] 代码格式规范
- [ ] 添加了适当的注释

### 11. 性能检查
- [ ] 页面加载速度正常
- [ ] 内存使用合理
- [ ] 数据库查询优化

### 12. 安全检查
- [ ] 使用参数化查询
- [ ] 验证用户输入
- [ ] 权限控制到位
- [ ] 防止XSS攻击

## ⚠️ 常见错误避免

### 13. 高频错误检查
- [ ] **避免反射调用TemplateService** - 使用直接调用
- [ ] **确保参数数量正确** - RenderPageWithLayout需要4个必需参数
- [ ] **添加必要的using语句** - 特别是TemplateService和HeaderOptionsModel
- [ ] **正确处理ThreadAbortException** - 这是Response.End()的正常行为
- [ ] **使用参数化查询** - 防止SQL注入
- [ ] **⚠️ 避免模型类命名冲突** - 检查是否与现有模型同名
- [ ] **⚠️ 注意数据类型转换** - 特别是long→int、long→string的转换
- [ ] **⚠️ 及时检查编译状态** - 每次修改后立即检查编译错误
- [ ] **⚠️ 正确选择UBB处理函数** - 单条内容用UBBCode，完整HTML用ToWML
- [ ] **⚠️ 测试UBB标签显示** - 确保图片、链接等UBB标签正常转换

### 14. 最佳实践遵循
- [ ] **优先使用直接调用而非反射**
- [ ] **继承基础模型**：使用 `BasePageModel` 或 `BasePageModelWithPagination`
- [ ] **避免重复代码**：不要重复定义公共属性（PageTitle, Message, SiteInfo等）
- [ ] **遵循项目的命名规范**
- [ ] **添加适当的错误处理和日志**
- [ ] **使用强类型模型**：避免匿名对象，提高类型安全性

## 📚 参考资源

- [Handlebars集成参考手册](../技术文档/Handlebars集成参考手册.md)
- [Handlebars常见问题解决方案](../开发文档/Handlebars常见问题解决方案.md)
- [Handlebars模型重构记录](../历史参考/Handlebars模型重构记录.md)
- [项目模板示例](YaoHuo.Plugin/Template/Pages/)

## ✅ 完成确认

完成所有检查项后，在此签名确认：

- 开发者：________________
- 日期：__________________
- 页面名称：______________

---

**提示**: 
- 每次开发新页面时都使用此检查清单
- 发现新的常见问题时及时更新此清单
- 团队成员应该熟悉并遵循此清单