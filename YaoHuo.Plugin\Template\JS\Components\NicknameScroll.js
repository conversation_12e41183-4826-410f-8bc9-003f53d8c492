/**
 * 昵称滚动显示组件
 * 当昵称被截断时，提供滚动显示完整昵称的功能
 */

let nicknameScrollInterval = null;
let isScrolling = false;

/**
 * 检查昵称是否被截断
 * @param {HTMLElement} element 昵称元素
 * @returns {boolean} 是否被截断
 */
function isNicknameTruncated(element) {
    return element.scrollWidth > element.clientWidth;
}

/**
 * 开始昵称滚动（鼠标悬停）
 * @param {HTMLElement} element 昵称元素
 */
function startNicknameScroll(element) {
    // 只有在被截断的情况下才滚动
    if (!isNicknameTruncated(element)) {
        return;
    }

    // 防止重复启动
    if (isScrolling) {
        return;
    }

    // 延迟500ms开始滚动，避免误触
    setTimeout(() => {
        if (element.matches(':hover') && !isScrolling) {
            performAutoScroll(element); // 改为使用缓慢滚动
        }
    }, 500);
}

/**
 * 停止昵称滚动（鼠标离开）
 * @param {HTMLElement} element 昵称元素
 */
function stopNicknameScroll(element) {
    if (nicknameScrollInterval) {
        clearInterval(nicknameScrollInterval);
        nicknameScrollInterval = null;
    }
    isScrolling = false;

    // 重置滚动位置
    if (element) {
        element.scrollLeft = 0;
    }
}

/**
 * 切换昵称滚动（点击）
 * @param {HTMLElement} element 昵称元素
 */
function toggleNicknameScroll(element) {
    // 只有在被截断的情况下才响应点击
    if (!isNicknameTruncated(element)) {
        return;
    }

    // 点击时执行缓慢滚动
    performAutoScroll(element);
}



/**
 * 执行自动滚动（页面加载时的一次性滚动）
 * @param {HTMLElement} element 昵称元素
 */
function performAutoScroll(element) {
    const maxScroll = element.scrollWidth - element.clientWidth;
    let currentScroll = 0;

    const autoScrollInterval = setInterval(() => {
        currentScroll += 1; // 自动滚动速度较慢，每次1px
        element.scrollLeft = currentScroll;

        // 滚动到最右端后暂停2秒，然后回到起始位置
        if (currentScroll >= maxScroll) {
            clearInterval(autoScrollInterval);

            setTimeout(() => {
                // 快速回到起始位置
                let backScroll = maxScroll;
                const backInterval = setInterval(() => {
                    backScroll -= 3; // 回滚速度稍快
                    if (backScroll <= 0) {
                        backScroll = 0;
                        clearInterval(backInterval);
                    }
                    element.scrollLeft = backScroll;
                }, 30);
            }, 2000); // 在右端停留2秒
        }
    }, 80); // 自动滚动较慢，每80ms更新一次
}

/**
 * 初始化昵称滚动功能
 */
function initNicknameScroll() {
    const nicknameElement = document.getElementById('nicknameDisplay');
    if (nicknameElement) {
        // 停止任何正在进行的滚动，防止窗口调整时的抽搐
        stopNicknameScroll(nicknameElement);

        // 添加CSS样式以支持滚动
        nicknameElement.style.whiteSpace = 'nowrap';
        nicknameElement.style.overflowX = 'hidden';

        // 延迟检测，确保DOM和CSS完全渲染
        setTimeout(() => {
            // 如果昵称被截断，添加视觉提示
            if (isNicknameTruncated(nicknameElement)) {
                nicknameElement.title = '点击或悬停查看完整昵称';
                nicknameElement.style.cursor = 'pointer';

                // 只有在页面首次加载时才自动滚动（避免窗口调整时重复滚动）
                if (!nicknameElement.hasAttribute('data-initialized')) {
                    nicknameElement.setAttribute('data-initialized', 'true');
                    // 页面加载后延迟1秒自动滚动一次
                    setTimeout(() => {
                        performAutoScroll(nicknameElement);
                    }, 1000);
                }
            } else {
                // 如果没有截断，移除相关提示
                nicknameElement.title = '';
                nicknameElement.style.cursor = '';
            }
        }, 100); // 延迟100ms确保渲染完成
    }
}

// 防抖函数，避免窗口调整时频繁触发
let resizeTimeout = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initNicknameScroll();
});

// 窗口大小改变时重新检查（使用防抖）
window.addEventListener('resize', function() {
    // 清除之前的定时器
    if (resizeTimeout) {
        clearTimeout(resizeTimeout);
    }

    // 设置新的定时器，300ms后执行
    resizeTimeout = setTimeout(() => {
        initNicknameScroll();
    }, 300);
});
