﻿using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Caching;
using System.Text;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.BBS.Service;

namespace YaoHuo.Plugin.BBS
{
    public abstract class BaseBBSListPage : MyPageWap
    {
        protected string a = PubConstant.GetAppString("InstanceName");

        // 共同的属性
        public string action = "";
        public string linkURL = "";
        public string linkTOP = "";
        public string condition = "";
        public string ERROR = "";
        public List<wap_bbs_Model> listVo = null;
        public StringBuilder strhtml = new StringBuilder();
        public sys_ad_show_Model adVo = new sys_ad_show_Model();
        public List<user_Model> userListVo_IDName = null;
        public long kk = 1L;
        public long index = 0L;
        public long total = 0L;
        public long pageSize = 10L;
        public long CurrentPage = 1L;
        public string hots = "500";

        // 缓存相关
        protected static readonly MemoryCache _hotPostsCache = new MemoryCache("BBSHotPostsCache");

        // 获取缓存策略
        protected CacheItemPolicy GetCachePolicy(int minutes)
        {
            return new CacheItemPolicy
            {
                AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(minutes)
            };
        }

        // 处理分页
        protected void HandlePaging(wap_bbs_BLL bll)
        {
            if (GetRequestValue("getTotal") != "")
            {
                total = long.Parse(GetRequestValue("getTotal"));
            }
            else
            {
                total = bll.GetListCount(condition);
            }

            if (GetRequestValue("page") != "")
            {
                CurrentPage = long.Parse(GetRequestValue("page"));
            }

            CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
            index = pageSize * (CurrentPage - 1L);
        }

        // 构建分页链接
        protected void BuildPageLinks(string baseUrl, string extraParams = "")
        {
            // 检查 baseUrl 是否已包含查询参数
            string connector = baseUrl.Contains("?") ? "&amp;" : "?";

            // 构建基础URL
            linkURL = http_start + baseUrl;

            // 添加额外参数
            if (!string.IsNullOrEmpty(extraParams))
            {
                linkURL += connector + extraParams;
                connector = "&amp;";
            }

            // 添加总数参数
            linkURL += connector + "getTotal=" + total;

            // 构建分页链接
            linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL);
            linkURL = WapTool.GetPageLink(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL, WapTool.GetArryString(classVo.smallimg, '|', 40));
        }

        // 加载广告
        protected void LoadAdvertisement()
        {
            // ✅ 使用AdvertisementService统一广告查询逻辑，提升安全性和性能
            string connectionString = PubConstant.GetConnectionString(a);
            adVo = AdvertisementService.GetBBSAdvertisementSafely(siteid, connectionString);
        }

        // 构建基础查询条件
        protected string BuildBaseCondition()
        {
            if (classid == "0")
            {
                return $" ischeck=0 and userid={siteid}";
            }
            return $" ischeck=0 and userid={siteid} and book_classid in (select classid from [class] where childid={classid} union select '{classid}')";
        }

        // 显示昵称颜色
        public string ShowNickName_color(long userid, string nickname)
        {
            if (userListVo_IDName == null)
            {
                return nickname;
            }

            foreach (var item in userListVo_IDName)
            {
                if (item.userid == userid)
                {
                    nickname = WapTool.GetColorNickName(item.idname, nickname, base.lang, base.ver, item.endTime);
                    break;
                }
            }

            return nickname;
        }

        // 添加 stype 属性
        public string stype = "";

        protected void HandlePageAndLinks(string baseUrl, string extraParams = "")
        {
            // 处理分页
            if (GetRequestValue("page") != "")
            {
                CurrentPage = int.Parse(GetRequestValue("page"));
            }
            CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
            index = pageSize * (CurrentPage - 1L);

            // 构建分页链接
            linkURL = http_start + baseUrl;
            if (!baseUrl.Contains("siteid="))
            {
                linkURL += (baseUrl.Contains("?") ? "&amp;" : "?") + "siteid=" + siteid;
            }
            if (!string.IsNullOrEmpty(extraParams))
            {
                linkURL += "&amp;" + extraParams.TrimStart('&', 'a', 'm', 'p', ';');
            }
            linkURL += "&amp;getTotal=" + total;

            // 生成分页导航
            linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL);
            linkURL = WapTool.GetPageLink(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL,
                WapTool.GetArryString(classVo.smallimg, '|', 40));
        }

        protected void LoadUserInfo()
        {
            if (WapTool.GetSiteDefault(siteVo.Version, 33) != "1" && listVo != null)
            {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.Append("siteid=" + siteid + " and userid in(");

                foreach (var item in listVo.Where(x => !string.IsNullOrEmpty(x.book_pub)))
                {
                    stringBuilder.Append(item.book_pub + ",");
                }

                stringBuilder.Append("0)");
                userListVo_IDName = MainBll.GetUserListVo(stringBuilder.ToString());
            }
        }

        /// <summary>
        /// 渲染帖子图标（普通帖子，带序号）
        /// </summary>
        /// <param name="post">帖子模型</param>
        /// <param name="index">序号</param>
        /// <returns>图标HTML字符串</returns>
        protected string RenderPostIcons(wap_bbs_Model post, int index)
        {
            return YaoHuo.Plugin.BBS.Components.PostIconRenderer.RenderNormalPostIcons(post, this.http_start, this.hots, index);
        }

        /// <summary>
        /// 渲染置顶帖图标
        /// </summary>
        /// <param name="post">帖子模型</param>
        /// <returns>图标HTML字符串</returns>
        protected string RenderTopPostIcons(wap_bbs_Model post)
        {
            return YaoHuo.Plugin.BBS.Components.PostIconRenderer.RenderTopPostIcons(post, this.http_start, this.hots);
        }

        /// <summary>
        /// 渲染热门帖子图标
        /// </summary>
        /// <param name="post">帖子模型</param>
        /// <param name="index">序号</param>
        /// <returns>图标HTML字符串</returns>
        protected string RenderHotPostIcons(wap_bbs_Model post, int index)
        {
            return YaoHuo.Plugin.BBS.Components.PostIconRenderer.RenderHotPostIcons(post, this.http_start, this.hots, index);
        }

        /// <summary>
        /// 渲染搜索结果帖子图标
        /// </summary>
        /// <param name="post">帖子模型</param>
        /// <param name="index">序号</param>
        /// <returns>图标HTML字符串</returns>
        protected string RenderSearchPostIcons(wap_bbs_Model post, int index)
        {
            return YaoHuo.Plugin.BBS.Components.PostIconRenderer.RenderSearchPostIcons(post, this.http_start, this.hots, index);
        }

        /// <summary>
        /// 预加载派币状态到缓存
        /// </summary>
        protected void PreloadFreeMoneyStatus()
        {
            var allPosts = new List<wap_bbs_Model>();
            if (listVo != null) allPosts.AddRange(listVo);

            // 如果是Book_List类型，还需要预加载置顶帖状态
            if (this is YaoHuo.Plugin.BBS.Book_List bookList && bookList.listVoTop != null)
            {
                allPosts.AddRange(bookList.listVoTop);
            }

            // 批量预加载派币状态
            var freeMoneyPosts = allPosts.Where(p => p.freeMoney > 0);
            foreach (var post in freeMoneyPosts)
            {
                FreeMoneyStatusCache.IsFreeMoneyCompleted(post.id, post.freeMoney, post.freeLeftMoney);
            }
        }

        /// <summary>
        /// 获取BBS通用样式
        /// </summary>
        /// <returns>CSS样式字符串</returns>
        protected string GetBBSStyles()
        {
            return YaoHuo.Plugin.BBS.Components.BBSStyles.GetListPageStyles();
        }

        /// <summary>
        /// 获取热门帖子页面样式
        /// </summary>
        /// <returns>CSS样式字符串</returns>
        protected string GetHotPageStyles()
        {
            return YaoHuo.Plugin.BBS.Components.BBSStyles.GetHotPageStyles();
        }

        /// <summary>
        /// 获取搜索页面样式
        /// </summary>
        /// <returns>CSS样式字符串</returns>
        protected string GetSearchPageStyles()
        {
            return YaoHuo.Plugin.BBS.Components.BBSStyles.GetSearchPageStyles();
        }
    }
}