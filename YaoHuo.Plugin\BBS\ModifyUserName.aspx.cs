﻿using System;
using System.Linq;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.WebSite.BBS.Service;

namespace YaoHuo.Plugin.BBS
{
    public class ModifyUserName : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string INFO = "";

        public string ERROR = "";

        public string tousername = "";

        public string touserid = "";

        public string backurl = "";

        public string getuserid = "";

        public user_Model toUserVo = null;

        public string KL_Check_Repeat_Nickname = PubConstant.GetAppString("KL_Check_Repeat_Nickname");

        protected void Page_Load(object sender, EventArgs e)
        {
            string requestValue = GetRequestValue("action");
            if (!IsCheckManagerLvl("|00|", "") || siteVo.siteid != userVo.userid)
            {
                ShowTipInfo("抱歉，只有正超级管理员才有权限操作。", "");
            }
            needPassWordToAdmin();
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "admin/basesitemodifywml.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            touserid = GetRequestValue("touserid");
            if (touserid == siteid)
            {
                ShowTipInfo("抱歉，正站长对应的用户名不在此修改，请到注册会员管理中修改。", "");
            }
            if (touserid != "")
            {
                if (!WapTool.IsNumeric(touserid))
                {
                    touserid = "0";
                }
                // ✅ 使用UserService获取用户信息
                string connectionString = PubConstant.GetConnectionString(string_10);
                toUserVo = UserService.GetUserInfoSafely(touserid, siteid, connectionString);
            }
            if (!(requestValue == "gomod"))
            {
                return;
            }
            tousername = GetRequestValue("tousername");
            tousername = WapTool.Left(tousername, 20);
            // ✅ 使用安全的方法检查用户名是否存在
            getuserid = CheckUserNameExists(tousername, siteid);
            if (tousername.Trim() == "")
            {
                INFO = "NULL";
                return;
            }
            if (getuserid != "0")
            {
                INFO = "HASEXIST";
                return;
            }
            try
            {
                // ✅ 使用Dapper修复SQL注入漏洞
                UpdateUsernameSafely(tousername, siteid, toUserVo.userid.ToString());
                toUserVo.username = tousername;
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
        }

        /// <summary>
        /// 使用Dapper安全地更新用户名，避免SQL注入
        /// </summary>
        /// <param name="username">新用户名</param>
        /// <param name="siteId">站点ID</param>
        /// <param name="userId">用户ID</param>
        private void UpdateUsernameSafely(string username, string siteId, string userId)
        {
            // 参数验证
            if (string.IsNullOrWhiteSpace(username))
                throw new ArgumentException("用户名不能为空");

            // 获取数据库连接字符串
            string connectionString = PubConstant.GetConnectionString(string_10);
            string sql = "UPDATE [user] SET username = @Username WHERE siteid = @SiteId AND userid = @UserId";

            // 使用Dapper执行参数化查询，SafeParseLong内部已包含数字验证
            DapperHelper.Execute(connectionString, sql, new {
                Username = username,
                SiteId = DapperHelper.SafeParseLong(siteId, "站点ID"),
                UserId = DapperHelper.SafeParseLong(userId, "用户ID")
            });
        }



        /// <summary>
        /// 安全检查用户名是否已存在
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="siteid">站点ID</param>
        /// <returns>存在则返回用户ID，不存在返回"0"</returns>
        private string CheckUserNameExists(string username, string siteid)
        {
            try
            {
                string checkConnectionString = PubConstant.GetConnectionString(string_10);
                string checkSql = "SELECT userid FROM [user] WHERE username = @Username AND siteid = @SiteId";

                var existingUserId = DapperHelper.Query<long?>(checkConnectionString, checkSql, new {
                    Username = DapperHelper.LimitLength(username, 50), // username字段长度限制
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID")
                }).FirstOrDefault();

                return existingUserId?.ToString() ?? "0"; // 保持原有返回值格式
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查用户名失败: {ex.Message}");
                return "0"; // 发生错误时返回不存在，安全起见
            }
        }
    }
}