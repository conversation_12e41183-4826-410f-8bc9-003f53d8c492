using System;
using System.Collections.Generic;
using System.Linq;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.WebSite.Services
{
    /// <summary>
    /// 好友查询服务类 - 使用参数化查询，支持智能搜索
    /// </summary>
    public class FriendQueryService
    {
        private readonly string _connectionString;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        public FriendQueryService(string connectionString)
        {
            _connectionString = connectionString ?? throw new ArgumentNullException(nameof(connectionString));
        }

        /// <summary>
        /// 智能搜索好友/黑名单 - 纯数字搜索ID，其他字符搜索昵称
        /// </summary>
        /// <param name="siteid">站点ID</param>
        /// <param name="userid">当前用户ID</param>
        /// <param name="friendtype">好友类型（0=好友，1=黑名单，2=追求等）</param>
        /// <param name="searchKey">搜索关键字</param>
        /// <param name="pageSize">每页数量</param>
        /// <param name="currentPage">当前页码</param>
        /// <returns>查询结果</returns>
        public FriendQueryResult SearchFriends(
            string siteid, 
            string userid, 
            string friendtype, 
            string searchKey = null, 
            int pageSize = 10, 
            int currentPage = 1)
        {
            var result = new FriendQueryResult
            {
                Friends = new List<wap_friends_Model>(),
                Total = 0,
                CurrentPage = currentPage,
                PageSize = pageSize
            };

            try
            {
                // 输入验证
                if (!WapTool.IsNumeric(siteid) || !WapTool.IsNumeric(userid) || !WapTool.IsNumeric(friendtype))
                {
                    return result;
                }

                // 构建基础查询参数
                var parameters = new
                {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                    FriendType = DapperHelper.SafeParseInt(friendtype, "好友类型")
                };

                string whereClause = "siteid = @SiteId AND userid = @UserId AND friendtype = @FriendType";
                object queryParameters = parameters;

                // 处理搜索条件
                if (!string.IsNullOrWhiteSpace(searchKey))
                {
                    searchKey = searchKey.Trim();

                    // 判断是否为纯数字
                    if (WapTool.IsNumeric(searchKey))
                    {
                        // 纯数字时同时搜索用户ID和昵称
                        whereClause += " AND (frienduserid = @SearchUserId OR friendnickname LIKE @SearchNickname)";
                        queryParameters = new
                        {
                            SiteId = parameters.SiteId,
                            UserId = parameters.UserId,
                            FriendType = parameters.FriendType,
                            SearchUserId = DapperHelper.SafeParseLong(searchKey, "搜索用户ID"),
                            SearchNickname = $"%{searchKey}%"
                        };
                    }
                    else
                    {
                        // 非纯数字时只按昵称模糊搜索
                        whereClause += " AND friendnickname LIKE @SearchNickname";
                        queryParameters = new
                        {
                            SiteId = parameters.SiteId,
                            UserId = parameters.UserId,
                            FriendType = parameters.FriendType,
                            SearchNickname = $"%{searchKey}%"
                        };
                    }
                }

                // 查询总数
                string countSql = $"SELECT COUNT(id) FROM wap_friends WHERE {whereClause}";
                result.Total = DapperHelper.ExecuteScalar<int>(_connectionString, countSql, queryParameters);

                // 如果没有数据，直接返回
                if (result.Total == 0)
                {
                    return result;
                }

                // 计算分页
                var totalPages = (int)Math.Ceiling((double)result.Total / pageSize);
                currentPage = Math.Max(1, Math.Min(currentPage, totalPages));
                result.CurrentPage = currentPage;
                result.TotalPages = totalPages;

                var offset = (currentPage - 1) * pageSize;

                // 查询数据
                string dataSql = $@"
                    SELECT id, siteid, userid, frienduserid, friendusername, friendnickname, friendtype, addtime
                    FROM wap_friends
                    WHERE {whereClause}
                    ORDER BY id DESC
                    OFFSET @Offset ROWS
                    FETCH NEXT @PageSize ROWS ONLY";

                // 构建包含分页参数的查询参数
                object dataParameters;
                if (queryParameters.GetType().GetProperty("SearchUserId") != null)
                {
                    // 包含搜索参数的情况
                    var searchParams = (dynamic)queryParameters;
                    dataParameters = new
                    {
                        SiteId = (long)searchParams.SiteId,
                        UserId = (long)searchParams.UserId,
                        FriendType = (int)searchParams.FriendType,
                        SearchUserId = searchParams.SearchUserId != null ? (long)searchParams.SearchUserId : 0,
                        SearchNickname = searchParams.SearchNickname?.ToString() ?? "",
                        Offset = offset,
                        PageSize = pageSize
                    };
                }
                else if (queryParameters.GetType().GetProperty("SearchNickname") != null)
                {
                    // 只包含昵称搜索参数的情况
                    var searchParams = (dynamic)queryParameters;
                    dataParameters = new
                    {
                        SiteId = (long)searchParams.SiteId,
                        UserId = (long)searchParams.UserId,
                        FriendType = (int)searchParams.FriendType,
                        SearchNickname = searchParams.SearchNickname?.ToString() ?? "",
                        Offset = offset,
                        PageSize = pageSize
                    };
                }
                else
                {
                    // 基础查询参数
                    var baseParams = (dynamic)queryParameters;
                    dataParameters = new
                    {
                        SiteId = (long)baseParams.SiteId,
                        UserId = (long)baseParams.UserId,
                        FriendType = (int)baseParams.FriendType,
                        Offset = offset,
                        PageSize = pageSize
                    };
                }

                var friends = DapperHelper.Query<wap_friends_Model>(_connectionString, dataSql, dataParameters);
                result.Friends = friends?.ToList() ?? new List<wap_friends_Model>();
            }
            catch (Exception ex)
            {
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 查询"追求我的人"列表（friendtype=4的特殊情况）
        /// </summary>
        /// <param name="siteid">站点ID</param>
        /// <param name="userid">当前用户ID（作为被追求的人）</param>
        /// <param name="searchKey">搜索关键字</param>
        /// <param name="pageSize">每页数量</param>
        /// <param name="currentPage">当前页码</param>
        /// <returns>查询结果</returns>
        public FriendQueryResult SearchWhoLovesMe(
            string siteid, 
            string userid, 
            string searchKey = null, 
            int pageSize = 10, 
            int currentPage = 1)
        {
            var result = new FriendQueryResult
            {
                Friends = new List<wap_friends_Model>(),
                Total = 0,
                CurrentPage = currentPage,
                PageSize = pageSize
            };

            try
            {
                // 输入验证
                if (!WapTool.IsNumeric(siteid) || !WapTool.IsNumeric(userid))
                {
                    return result;
                }

                // 构建基础查询参数（注意：这里是 frienduserid = userid，表示别人追求当前用户）
                var parameters = new
                {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    FriendUserId = DapperHelper.SafeParseLong(userid, "用户ID")
                };

                string whereClause = "siteid = @SiteId AND frienduserid = @FriendUserId AND friendtype = 2";
                object queryParameters = parameters;

                // 处理搜索条件
                if (!string.IsNullOrWhiteSpace(searchKey))
                {
                    searchKey = searchKey.Trim();

                    // 判断是否为纯数字
                    if (WapTool.IsNumeric(searchKey))
                    {
                        // 纯数字时同时搜索追求者ID和通过用户表查询昵称
                        whereClause += " AND (userid = @SearchUserId OR EXISTS (SELECT 1 FROM [user] u WHERE u.userid = wap_friends.userid AND u.nickname LIKE @SearchNickname))";
                        queryParameters = new
                        {
                            SiteId = parameters.SiteId,
                            FriendUserId = parameters.FriendUserId,
                            SearchUserId = DapperHelper.SafeParseLong(searchKey, "搜索用户ID"),
                            SearchNickname = $"%{searchKey}%"
                        };
                    }
                    else
                    {
                        // 非纯数字时只按昵称搜索
                        whereClause += " AND EXISTS (SELECT 1 FROM [user] u WHERE u.userid = wap_friends.userid AND u.nickname LIKE @SearchNickname)";
                        queryParameters = new
                        {
                            SiteId = parameters.SiteId,
                            FriendUserId = parameters.FriendUserId,
                            SearchNickname = $"%{searchKey}%"
                        };
                    }
                }

                // 查询总数
                string countSql = $"SELECT COUNT(id) FROM wap_friends WHERE {whereClause}";
                result.Total = DapperHelper.ExecuteScalar<int>(_connectionString, countSql, queryParameters);

                // 如果没有数据，直接返回
                if (result.Total == 0)
                {
                    return result;
                }

                // 计算分页
                var totalPages = (int)Math.Ceiling((double)result.Total / pageSize);
                currentPage = Math.Max(1, Math.Min(currentPage, totalPages));
                result.CurrentPage = currentPage;
                result.TotalPages = totalPages;

                var offset = (currentPage - 1) * pageSize;

                // 查询数据
                string dataSql = $@"
                    SELECT id, siteid, userid, frienduserid, friendusername, friendnickname, friendtype, addtime
                    FROM wap_friends
                    WHERE {whereClause}
                    ORDER BY id DESC
                    OFFSET @Offset ROWS
                    FETCH NEXT @PageSize ROWS ONLY";

                // 构建包含分页参数的查询参数
                object dataParameters;
                if (queryParameters.GetType().GetProperty("SearchUserId") != null)
                {
                    // 包含搜索参数的情况
                    var searchParams = (dynamic)queryParameters;
                    dataParameters = new
                    {
                        SiteId = (long)searchParams.SiteId,
                        FriendUserId = (long)searchParams.FriendUserId,
                        SearchUserId = searchParams.SearchUserId != null ? (long)searchParams.SearchUserId : 0,
                        SearchNickname = searchParams.SearchNickname?.ToString() ?? "",
                        Offset = offset,
                        PageSize = pageSize
                    };
                }
                else if (queryParameters.GetType().GetProperty("SearchNickname") != null)
                {
                    // 只包含昵称搜索参数的情况
                    var searchParams = (dynamic)queryParameters;
                    dataParameters = new
                    {
                        SiteId = (long)searchParams.SiteId,
                        FriendUserId = (long)searchParams.FriendUserId,
                        SearchNickname = searchParams.SearchNickname?.ToString() ?? "",
                        Offset = offset,
                        PageSize = pageSize
                    };
                }
                else
                {
                    // 基础查询参数
                    var baseParams = (dynamic)queryParameters;
                    dataParameters = new
                    {
                        SiteId = (long)baseParams.SiteId,
                        FriendUserId = (long)baseParams.FriendUserId,
                        Offset = offset,
                        PageSize = pageSize
                    };
                }

                var friends = DapperHelper.Query<wap_friends_Model>(_connectionString, dataSql, dataParameters);
                result.Friends = friends?.ToList() ?? new List<wap_friends_Model>();
            }
            catch (Exception ex)
            {
                result.ErrorMessage = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 验证好友是否存在
        /// </summary>
        /// <param name="siteid">站点ID</param>
        /// <param name="userid">用户ID</param>
        /// <param name="frienduserid">好友用户ID</param>
        /// <param name="friendtype">好友类型</param>
        /// <returns>是否存在</returns>
        public bool FriendExists(string siteid, string userid, string frienduserid, string friendtype)
        {
            try
            {
                if (!WapTool.IsNumeric(siteid) || !WapTool.IsNumeric(userid) ||
                    !WapTool.IsNumeric(frienduserid) || !WapTool.IsNumeric(friendtype))
                {
                    return false;
                }

                string sql = "SELECT COUNT(id) FROM wap_friends WHERE siteid = @SiteId AND userid = @UserId AND frienduserid = @FriendUserId AND friendtype = @FriendType";

                var parameters = new
                {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                    FriendUserId = DapperHelper.SafeParseLong(frienduserid, "好友用户ID"),
                    FriendType = DapperHelper.SafeParseInt(friendtype, "好友类型")
                };

                var result = DapperHelper.ExecuteScalar<int>(_connectionString, sql, parameters);
                return result > 0;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 获取好友数量
        /// </summary>
        /// <param name="siteid">站点ID</param>
        /// <param name="userid">用户ID</param>
        /// <param name="friendtype">好友类型</param>
        /// <returns>好友数量</returns>
        public int GetFriendCount(string siteid, string userid, string friendtype)
        {
            try
            {
                if (!WapTool.IsNumeric(siteid) || !WapTool.IsNumeric(userid) || !WapTool.IsNumeric(friendtype))
                {
                    return 0;
                }

                string sql;
                object parameters;

                if (friendtype == "4") // 追求我的人
                {
                    sql = "SELECT COUNT(id) FROM wap_friends WHERE siteid = @SiteId AND frienduserid = @FriendUserId AND friendtype = 2";
                    parameters = new
                    {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        FriendUserId = DapperHelper.SafeParseLong(userid, "用户ID")
                    };
                }
                else
                {
                    sql = "SELECT COUNT(id) FROM wap_friends WHERE siteid = @SiteId AND userid = @UserId AND friendtype = @FriendType";
                    parameters = new
                    {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                        FriendType = DapperHelper.SafeParseInt(friendtype, "好友类型")
                    };
                }

                return DapperHelper.ExecuteScalar<int>(_connectionString, sql, parameters);
            }
            catch (Exception)
            {
                return 0;
            }
        }
    }

    /// <summary>
    /// 好友查询结果
    /// </summary>
    public class FriendQueryResult
    {
        /// <summary>
        /// 好友列表
        /// </summary>
        public List<wap_friends_Model> Friends { get; set; } = new List<wap_friends_Model>();

        /// <summary>
        /// 总数量
        /// </summary>
        public int Total { get; set; }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage { get; set; }

        /// <summary>
        /// 每页数量
        /// </summary>
        public int PageSize { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 是否有错误
        /// </summary>
        public bool HasError => !string.IsNullOrEmpty(ErrorMessage);

        /// <summary>
        /// 是否为第一页
        /// </summary>
        public bool IsFirstPage => CurrentPage <= 1;

        /// <summary>
        /// 是否为最后一页
        /// </summary>
        public bool IsLastPage => CurrentPage >= TotalPages;
    }
} 