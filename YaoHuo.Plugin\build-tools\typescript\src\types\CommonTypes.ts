/**
 * YaoHuo项目通用类型定义
 * 统一管理所有TypeScript模块使用的类型
 * 
 * @version 1.0
 * <AUTHOR>
 * @date 2025-01-07
 */

// ==================== AJAX响应类型 ====================

/**
 * 标准AJAX响应接口
 */
export interface AjaxResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    error?: string;
}

/**
 * 分页响应数据
 */
export interface PaginationResponse<T = any> {
    items: T[];
    currentPage: number;
    totalPages: number;
    totalCount: number;
    pageSize: number;
}

// ==================== UI组件配置类型 ====================

/**
 * Toast通知配置
 */
export interface ToastConfig {
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
    duration?: number;
    autoClose?: boolean;
}

/**
 * 模态框配置
 */
export interface ModalConfig {
    title?: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    type?: 'confirm' | 'alert' | 'custom';
    onConfirm?: () => void;
    onCancel?: () => void;
}

/**
 * 分页配置
 */
export interface PaginationConfig {
    currentPage: number;
    totalPages: number;
    baseUrl: string;
    pageParam?: string;
    showFirstLast?: boolean;
    showPrevNext?: boolean;
}

/**
 * 下拉菜单项配置
 */
export interface DropdownItem {
    id: string;
    text: string;
    icon?: string;
    action: string;
    disabled?: boolean;
    divider?: boolean;
}

/**
 * 下拉菜单配置
 */
export interface DropdownConfig {
    items: DropdownItem[];
    position?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
    trigger?: 'click' | 'hover';
    closeOnClick?: boolean;
}

// ==================== 表单验证类型 ====================

/**
 * 表单验证规则类型
 */
export type ValidationRuleType =
    | 'required'
    | 'minLength'
    | 'maxLength'
    | 'pattern'
    | 'email'
    | 'mobile'
    | 'qq'
    | 'number'
    | 'range'
    | 'custom';

/**
 * 表单验证规则
 */
export interface ValidationRule {
    type: ValidationRuleType;
    value?: any; // 规则值（如长度、正则表达式、范围等）
    message?: string; // 错误消息
    validator?: (value: string, element: HTMLInputElement) => ValidationResult; // 自定义验证函数
}

/**
 * 表单字段配置
 */
export interface FormFieldConfig {
    rules: ValidationRule[];
    validateOnBlur?: boolean; // 失焦时验证，默认true
    validateOnInput?: boolean; // 输入时验证，默认false
}

/**
 * 表单验证配置
 */
export interface FormValidationConfig {
    fields: { [fieldName: string]: FormFieldConfig };
    submitButton?: {
        selector: string;
        loadingText: string;
        iconClass?: string;
    };
}

/**
 * 表单字段配置（旧版本，保持兼容性）
 */
export interface FormField {
    name: string;
    rules: ValidationRule[];
    element?: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
}

/**
 * 表单验证结果
 */
export interface ValidationResult {
    isValid: boolean;
    errorMessage?: string; // 单个字段的错误消息
    errors?: { [fieldName: string]: string }; // 多个字段的错误消息（保持兼容性）
}

// ==================== 用户信息类型 ====================

/**
 * 用户基础信息
 */
export interface UserInfo {
    id: number;
    nickname: string;
    avatar?: string;
    level?: number;
    title?: string;
    isOnline?: boolean;
    lastLoginTime?: string;
}

/**
 * 头像配置
 */
export interface AvatarConfig {
    src: string;
    fallbackText: string;
    size: 'small' | 'medium' | 'large' | 'auto';
    timeout?: number;
}

// ==================== 消息相关类型 ====================

/**
 * 消息类型
 */
export interface MessageInfo {
    id: number;
    senderId: number;
    senderNickname: string;
    senderAvatar?: string;
    content: string;
    isRead: boolean;
    isSystem: boolean;
    sendTime: string;
    messageType: 'inbox' | 'outbox';
}

/**
 * 消息列表筛选配置
 */
export interface MessageFilter {
    type: 'all' | 'system' | 'chat';
    searchKey?: string;
    page?: number;
}

// ==================== 导航相关类型 ====================

/**
 * 导航配置
 */
export interface NavigationConfig {
    enableSmartBack: boolean;
    fallbackUrl: string;
    excludePatterns?: string[];
}

/**
 * 页面路径信息
 */
export interface PagePathInfo {
    current: string;
    referrer: string;
    isFromSameSite: boolean;
    shouldUseSmartBack: boolean;
}

// ==================== 事件处理类型 ====================

/**
 * 事件处理器类型
 */
export type EventHandler<T = Event> = (event: T) => void;

/**
 * 异步事件处理器类型
 */
export type AsyncEventHandler<T = Event> = (event: T) => Promise<void>;

// ==================== 工具类型 ====================

/**
 * 可选属性类型
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * 深度只读类型
 */
export type DeepReadonly<T> = {
    readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

// ==================== 样式相关类型 ====================

/**
 * CSS类名配置
 */
export interface StyleClasses {
    base?: string;
    active?: string;
    disabled?: string;
    loading?: string;
    error?: string;
    success?: string;
}

/**
 * 主题配置
 */
export interface ThemeConfig {
    primaryColor: string;
    secondaryColor: string;
    successColor: string;
    errorColor: string;
    warningColor: string;
    infoColor: string;
}

// ==================== 常量定义 ====================

/**
 * 默认配置常量
 */
export const DEFAULT_CONFIG = {
    TOAST_DURATION: 3000,
    AJAX_TIMEOUT: 10000,
    AVATAR_TIMEOUT: 3000,
    PAGINATION_SIZE: 20,
    DEBOUNCE_DELAY: 300
} as const;

/**
 * CSS类名常量
 */
export const CSS_CLASSES = {
    // Toast相关
    TOAST_BASE: 'toast-base',
    TOAST_SUCCESS: 'toast-success',
    TOAST_ERROR: 'toast-error',
    TOAST_WARNING: 'toast-warning',
    TOAST_INFO: 'toast-info',
    
    // Modal相关
    MODAL_OVERLAY: 'confirm-overlay',
    MODAL_CONTENT: 'confirm-content',
    MODAL_TITLE: 'confirm-title',
    MODAL_MESSAGE: 'confirm-message',
    MODAL_ACTIONS: 'confirm-actions',
    
    // Button相关
    BTN_PRIMARY: 'btn btn-primary',
    BTN_OUTLINE: 'btn btn-outline',
    BTN_DESTRUCTIVE: 'btn btn-destructive',
    BTN_GHOST: 'btn btn-ghost',
    
    // 确认按钮
    CONFIRM_DELETE: 'custom-confirm-btn custom-confirm-delete',
    CONFIRM_CANCEL: 'custom-confirm-btn custom-confirm-cancel',
    
    // 状态类
    HIDDEN: 'hidden',
    ACTIVE: 'active',
    DISABLED: 'disabled',
    LOADING: 'loading'
} as const;
