﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="UserInfoMore.aspx.cs" Inherits="YaoHuo.Plugin.BBS.UserInfoMore" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
Response.Write(WapTool.showTop(this.GetLang("个人资料|個人資料|Personal Information"), wmlVo));
string seeRight = WapTool.GetSiteDefault(siteVo.Version, 35);
string seeInfo = this.http_start + "bbs/toGroupInfo.aspx?siteid=" + this.siteid + "&amp;sid=" + this.sid ;
if (this.userid == this.touserid || this.IsCheckManagerLvl("|00|01|","")==true)
{
    seeRight = "0," + seeRight;
}
seeRight = "," + seeRight+",";
    //会员可见
 	if (this.IsCheckManagerLvl("|00|01|02|03|04|",""))
    {
    Response.Write("<div class=\"title\">" + this.GetLang("个人资料|個人資料|Personal Information") + "</div>");
    Response.Write("<div class=\"content\">");
    Response.Write("<span class=\"qianming\">");
    Response.Write(WapTool.ToWML(toUserVo.remark, wmlVo) + "<br/>");
    Response.Write("</span>");
    Response.Write(WapTool.GetHeadImgHTML(http_start, toUserVo.headimg) + "<br/>");
    Response.Write("【ID号】<span class=\"renick\"></span>" + toUserVo.userid + WapTool.GetOLtimePic(this.http_start, siteVo.lvlTimeImg, toUserVo.LoginTimes) + "<br/>");
    Response.Write("【昵称】<span class=\"renick\"></span>" + toUserVo.nickname + "<br/>");
    Response.Write("【" + WapTool.GetSiteMoneyName(siteVo.sitemoneyname, lang) + "】<span class=\"renick\"></span>" + toUserVo.money + "<br/>");
    Response.Write("【经验】<span class=\"renick\"></span>" + toUserVo.expr + "<br/>");
    Response.Write("【等级】<span class=\"renick\"></span>" + WapTool.GetLevl(siteVo.lvlNumer, toUserVo.expr,toUserVo.money,type) + "<br/>");
    Response.Write("【头衔】<span class=\"renick\"></span>" + WapTool.GetHandle(siteVo.lvlNumer, toUserVo.expr,toUserVo.money,type) + "<br/>");
    Response.Write("【身份】<span class=\"renick\"></span>" + WapTool.GetMyID(toUserVo.idname, this.lang) + "<br/>");
    Response.Write("【权限】<span class=\"renick\"></span>" + idtype + "<br/>");
    Response.Write("【勋章】<span class=\"renick\"></span>" + WapTool.GetMedal(toUserVo.moneyname, this.http_start) + "<br/>");
    Response.Write("【性别】<span class=\"renick\"></span>");
    if (toUserVo.sex == 1) { Response.Write(this.GetLang("男|男|Male")); } else { Response.Write(this.GetLang("女|女|Female")); }
    Response.Write("<br/>");
    Response.Write("【年龄】<span class=\"renick\"></span>" + toUserVo.age + "岁<br/>");
    //Response.Write("【地区】<span class=\"renick\"></span>" + toUserVo.city + "<br/>");
    Response.Write("【状态】<span class=\"renick\"></span>" + WapTool.GetOnline(this.http_start, toUserVo.isonline.ToString(), toUserVo.sex.ToString()) + "<br/>");
    Response.Write("【积时】<span class=\"renick\"></span>" + WapTool.DateToString(toUserVo.LoginTimes, this.lang,0) + "<br/>");
    Response.Write("【注册时间】<span class=\"renick\"></span>" + string.Format("{0:yyyy/MM/dd HH:mm}", toUserVo.RegTime) + "<br/>");
    toUserVo.aihao = toUserVo.aihao + "__";
    string[] arryqq = toUserVo.aihao.Split('_');
    //Response.Write("【QQ号】<span class=\"renick\"></span>");
    if (seeRight.IndexOf(",0,") >= 0 || (seeRight.IndexOf("," + userVo.SessionTimeout + ",") >= 0 && WapTool.showIDEndTime(siteVo.siteid ,userVo.userid, userVo.endTime) > 0))
    {
        //Response.Write(arryqq[1] + "<br/>");
    }
    else
    {
        //Response.Write("<a href=\"" + seeInfo + "\">升级VIP会员查看</a><br/>");
    }
    Response.Write("【身高】<span class=\"renick\"></span>" + toUserVo.shenggao + "<br/>");
    Response.Write("【体重】<span class=\"renick\"></span>" + toUserVo.tizhong + "<br/>");
    Response.Write("【星座】<span class=\"renick\"></span>" + toUserVo.xingzuo + "<br/>");
    Response.Write("【爱好】<span class=\"renick\"></span>" + arryqq[0] + "<br/>");
    Response.Write("【婚否】<span class=\"renick\"></span>" + toUserVo.fenfuo + "<br/>");
    Response.Write("【职业】<span class=\"renick\"></span>" + toUserVo.zhiye + "<br/>");
    Response.Write("【城市】<span class=\"renick\"></span>" + toUserVo.city + "<br/>");
    //Response.Write("【邮箱】<span class=\"renick\"></span>");
    if (seeRight.IndexOf(",0,") >= 0 || (seeRight.IndexOf("," + userVo.SessionTimeout + ",") >= 0 && WapTool.showIDEndTime(siteVo.siteid, userVo.userid, userVo.endTime) > 0))
    {
        //Response.Write(toUserVo.email + "<br/>");
    }
    else
    {
        //Response.Write("<a href=\"" + seeInfo + "\">升级VIP会员查看</a><br/>");
    }
    Response.Write("</div><script> document.addEventListener(\"DOMContentLoaded\", function() { var images = document.querySelectorAll(\"img\"); for (var i = 0; i < images.length; i++) { var img = images[i]; if (img.alt === \"头像\" && img.src.includes(\"/bbs/head/\")) { img.style.maxHeight = \"150px\"; } } var signatures = document.querySelectorAll(\"span.qianming\"); for (var j = 0; j < signatures.length; j++) { var signature = signatures[j]; if (signature.textContent.trim() === \"\") { signature.remove(); } } }); </script>");
    //会员可见结束
}
    Response.Write("<div class=\"btBox\"><div class=\"bt2\">");
    Response.Write("<a href=\"" + this.http_start + "bbs/userinfo.aspx?touserid=" + this.touserid + "\">返回上级</a> ");
    Response.Write("<a href=\"/\">返回首页</a>	");
    Response.Write("</div></div>");
Response.Write(WapTool.showDown(wmlVo));
%>