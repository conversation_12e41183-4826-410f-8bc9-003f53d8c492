# OWASP SQL Injection Cheat Sheet (中文速查版)

> 原文地址：<https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html>
> 本文仅保留关键要点，细节请查阅英文原版。

---

## 1️⃣ 核心原则

1. **首选参数化查询** (Prepared Statements)
2. **白名单**输入验证，拒绝黑名单
3. **最小权限原则**：数据库账号仅授予所需权限
4. **避免动态构造查询**：拼接字符串 = 高危
5. **定期审计 & 监控**：慢查询日志 + 安全扫描


## 2️⃣ 参数化示例

| 语言 | 安全示例 |
|------|---------|
| C# (ADO.NET) | `cmd.CommandText = "SELECT * FROM users WHERE userid = @id"; cmd.Parameters.Add("@id", SqlDbType.Int).Value = userId;` |
| Dapper | `conn.Query<User>("SELECT * FROM users WHERE userid = @Id", new { Id = userId });` |
| Java (JDBC) | `PreparedStatement ps = con.prepareStatement("SELECT * FROM users WHERE userid = ?"); ps.setInt(1, id);` |


## 3️⃣ 过滤 & 转义

- **不要依赖转义函数** 作为唯一防护
- 如果确需动态 SQL，使用数据库自带的安全 API，如 `sp_executesql`


## 4️⃣ 存储过程安全

- 仅在存储过程内部使用参数化
- 禁止使用字符串拼接构造动态 SQL


## 5️⃣ ORM 框架注意事项

- **启用** ORM 的 SQL 参数化功能
- **关闭** 无效或危险的 L2 缓存，防止注入片段被缓存


## 6️⃣ 检测与响应

| 技术 | 说明 |
|------|------|
| WAF 规则 | 针对常见注入 Payload，如 `' OR '1'='1` |
| SCA / SAST | 集成到 CI/CD，检测高危 API 调用 |
| RASP | 运行时监控数据库调用模式 |


## 7️⃣ 常用 Payload 用途

| 目的 | Payload 示例 |
|------|--------------|
| 探测 | `' OR 1=1 --` |
| 注释 | `admin' /* comment */` |
| 联合查询 | `' UNION SELECT NULL,NULL,NULL --` |

> **⚠️ 仅供测试安全使用，禁止未授权渗透。**


## 8️⃣ 参考资料

- OWASP Cheat Sheet Series
- MITRE CWE-89: SQL Injection
- Microsoft SQL Server Security Best Practices

---

**最后更新**：2025-06-11
