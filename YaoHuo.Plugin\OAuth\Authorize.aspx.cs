using KeLin.ClassManager.Model;
using System;
using System.Text;
using System.Web;
using YaoHuo.Plugin.WebSite.Services.Config;

namespace YaoHuo.Plugin.OAuth
{
    /// <summary>
    /// OAuth 2.0 标准授权端点
    /// 实现 RFC 6749 授权码流程
    /// </summary>
    public partial class Authorize : OAuthBasePage
    {

        /// <summary>
        /// 输出网页字符
        /// </summary>
        public StringBuilder strhtml { get; } = new StringBuilder();

        /// <summary>
        /// 页面加载事件
        /// </summary>
        protected void Page_Load(object sender, EventArgs e)
        {
            // 使用基类方法验证用户登录状态（带回调URL）
            var userInfo = EnsureUserAuthenticatedWithCallback();
            if (userInfo == null) return;

            try
            {
                ProcessOAuth2Request(userInfo);
            }
            catch (OAuth2Exception ex)
            {
                // 记录OAuth错误
                var clientId = GetRequestValue("client_id");
                OAuthLogger.LogError(ex.Error, clientId, ex.ErrorDescription, Request.UserHostAddress, Request.UserAgent);

                HandleOAuth2Error(ex);
            }
            catch (Exception ex)
            {
                // 记录系统错误
                var clientId = GetRequestValue("client_id");
                OAuthLogger.LogError("server_error", clientId, ex.Message, Request.UserHostAddress, Request.UserAgent);

                LogSecurityEvent($"OAuth2授权异常: {ex.Message}", userInfo.userid.ToString());
                HandleOAuth2Error(new OAuth2Exception("server_error", "服务器内部错误"));
            }
        }

        /// <summary>
        /// 处理 OAuth 2.0 授权请求
        /// </summary>
        private void ProcessOAuth2Request(user_Model userInfo)
        {
            // 获取标准 OAuth 2.0 参数
            var responseType = GetRequestValue("response_type");
            var clientId = GetRequestValue("client_id");
            var redirectUri = GetRequestValue("redirect_uri");
            var scope = GetRequestValue("scope") ?? OAuthConfigService.GetDefaultScope();
            var state = GetRequestValue("state");
            var codeChallenge = GetRequestValue("code_challenge");
            var codeChallengeMethod = GetRequestValue("code_challenge_method") ?? "S256";

            // 验证必需参数
            if (string.IsNullOrEmpty(responseType))
                throw new OAuth2Exception(OAuthConstants.INVALID_REQUEST, "缺少 response_type 参数");

            if (responseType != OAuthConstants.RESPONSE_TYPE_CODE)
                throw new OAuth2Exception(OAuthConstants.UNSUPPORTED_RESPONSE_TYPE, "仅支持 authorization_code 流程");

            if (string.IsNullOrEmpty(clientId))
                throw new OAuth2Exception(OAuthConstants.INVALID_REQUEST, "缺少 client_id 参数");

            if (string.IsNullOrEmpty(redirectUri))
                throw new OAuth2Exception(OAuthConstants.INVALID_REQUEST, "缺少 redirect_uri 参数");

            // 验证客户端应用
            var client = OAuthService.GetClient(clientId);
            if (client == null || !client.IsActive())
                throw new OAuth2Exception(OAuthConstants.INVALID_CLIENT, "客户端应用无效");

            // 验证重定向URI
            if (!client.IsRedirectUriAllowed(redirectUri))
                throw new OAuth2Exception(OAuthConstants.INVALID_REQUEST, "重定向URI未授权");

            // 检查用户是否已同意授权
            var action = GetRequestValue("action");
            if (action == "authorize")
            {
                // 移除 CSRF 验证 - 重定向URI白名单已提供充分保护
                // OAuth 2.0 标准的 state 参数提供 CSRF 保护，无需额外验证

                // 处理用户授权决定
                var userDecision = GetRequestValue("decision");
                if (userDecision == "allow")
                {
                    // 记录用户同意授权
                    OAuthLogger.LogAuthorizeDecision(clientId, userInfo.userid, true, Request.UserHostAddress, Request.UserAgent);

                    // 用户同意授权
                    HandleUserConsent(userInfo, clientId, redirectUri, scope, state, codeChallenge, codeChallengeMethod);
                }
                else
                {
                    // 记录用户拒绝授权
                    OAuthLogger.LogAuthorizeDecision(clientId, userInfo.userid, false, Request.UserHostAddress, Request.UserAgent);

                    // 用户拒绝授权
                    var errorUrl = BuildErrorRedirectUrl(redirectUri, OAuthConstants.ACCESS_DENIED, "用户拒绝授权", state);
                    Response.Redirect(errorUrl, false);
                    Context.ApplicationInstance.CompleteRequest();
                }
                return;
            }

            // 记录授权请求日志
            OAuthLogger.LogAuthorizeRequest(clientId, userInfo.userid, scope, redirectUri, Request.UserHostAddress, Request.UserAgent);

            // 显示授权确认页面
            ShowAuthorizationPage(client, scope, redirectUri, state, codeChallenge, codeChallengeMethod);
        }

        /// <summary>
        /// 处理用户同意授权
        /// </summary>
        private void HandleUserConsent(user_Model userInfo, string clientId, string redirectUri, string scope,
            string state, string codeChallenge, string codeChallengeMethod)
        {
            try
            {
                // 生成授权码（完整业务流程）
                var authCode = OAuthService.CreateAuthorizationFlow(
                    clientId, userInfo.userid, redirectUri, scope, codeChallenge, codeChallengeMethod);

                // 记录授权日志（新架构中通过访问令牌表记录，无需单独的AuthLog）
                LogSecurityEvent($"OAuth2授权成功: ClientId={clientId}, UserId={userInfo.userid}", userInfo.userid.ToString());

                // 构建成功重定向URL
                var successUrl = BuildSuccessRedirectUrl(redirectUri, authCode, state);
                Response.Redirect(successUrl, false);
                Context.ApplicationInstance.CompleteRequest();
            }
            catch (Exception ex)
            {
                LogSecurityEvent($"生成授权码失败: {ex.Message}", userInfo.userid.ToString());
                throw new OAuth2Exception(OAuthConstants.SERVER_ERROR, "生成授权码失败");
            }
        }

        /// <summary>
        /// 显示授权确认页面
        /// </summary>
        private void ShowAuthorizationPage(OAuthClient client, string scope, string redirectUri,
            string state, string codeChallenge, string codeChallengeMethod)
        {
            strhtml.Append("<div class='title'>OAuth 2.0 授权确认</div>");
            
            strhtml.Append("<div class='content'>");
            strhtml.Append($"<h3>应用授权请求</h3>");
            strhtml.Append($"<p><strong>应用名称：</strong>{HttpUtility.HtmlEncode(client.AppName)}</p>");
            strhtml.Append($"<p><strong>应用说明：</strong>{HttpUtility.HtmlEncode(client.AppDescription ?? "无")}</p>");
            strhtml.Append($"<p><strong>请求权限：</strong>{HttpUtility.HtmlEncode(scope)}</p>");
            strhtml.Append($"<p><strong>重定向地址：</strong>{HttpUtility.HtmlEncode(redirectUri)}</p>");
            strhtml.Append("</div>");

            strhtml.Append("<div class='tip'>");
            strhtml.Append("该应用请求访问您的基本信息。您可以选择同意或拒绝此授权请求。");
            strhtml.Append("</div>");

            // 授权表单（移除 CSRF 保护 - 重定向URI白名单已提供充分保护）
            strhtml.Append("<form method='post' action='Authorize.aspx'>");
            strhtml.Append($"<input type='hidden' name='response_type' value='code' />");
            strhtml.Append($"<input type='hidden' name='client_id' value='{HttpUtility.HtmlEncode(client.AppId)}' />");
            strhtml.Append($"<input type='hidden' name='redirect_uri' value='{HttpUtility.HtmlEncode(redirectUri)}' />");
            strhtml.Append($"<input type='hidden' name='scope' value='{HttpUtility.HtmlEncode(scope)}' />");
            strhtml.Append($"<input type='hidden' name='state' value='{HttpUtility.HtmlEncode(state)}' />");
            strhtml.Append($"<input type='hidden' name='code_challenge' value='{HttpUtility.HtmlEncode(codeChallenge)}' />");
            strhtml.Append($"<input type='hidden' name='code_challenge_method' value='{HttpUtility.HtmlEncode(codeChallengeMethod)}' />");
            strhtml.Append($"<input type='hidden' name='action' value='authorize' />");

            strhtml.Append("<div class='btBox'>");
            strhtml.Append("<div class='bt1'>");
            strhtml.Append("<button type='submit' name='decision' value='allow'>同意授权</button>");
            strhtml.Append("</div>");
            strhtml.Append("<div class='bt2'>");
            strhtml.Append("<button type='submit' name='decision' value='deny'>拒绝</button>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
            strhtml.Append("</form>");
        }

        // 注意：重定向URI验证已移至 OAuthClient.IsRedirectUriAllowed() 方法

        /// <summary>
        /// 构建成功重定向URL
        /// </summary>
        private string BuildSuccessRedirectUrl(string redirectUri, string code, string state)
        {
            var uriBuilder = new UriBuilder(redirectUri);
            var query = HttpUtility.ParseQueryString(uriBuilder.Query);
            query["code"] = code;
            if (!string.IsNullOrEmpty(state))
                query["state"] = state;
            uriBuilder.Query = query.ToString();
            return uriBuilder.ToString();
        }

        /// <summary>
        /// 构建错误重定向URL
        /// </summary>
        private string BuildErrorRedirectUrl(string redirectUri, string error, string description, string state)
        {
            var uriBuilder = new UriBuilder(redirectUri);
            var query = HttpUtility.ParseQueryString(uriBuilder.Query);
            query["error"] = error;
            query["error_description"] = description;
            if (!string.IsNullOrEmpty(state))
                query["state"] = state;
            uriBuilder.Query = query.ToString();
            return uriBuilder.ToString();
        }

        /// <summary>
        /// 处理 OAuth 2.0 错误
        /// </summary>
        private void HandleOAuth2Error(OAuth2Exception ex)
        {
            strhtml.Append("<div class='title'>授权错误</div>");
            strhtml.Append($"<div class='tip'>错误：{HttpUtility.HtmlEncode(ex.ErrorDescription)}</div>");
            
            var redirectUri = GetRequestValue("redirect_uri");
            var state = GetRequestValue("state");
            
            if (!string.IsNullOrEmpty(redirectUri))
            {
                strhtml.Append("<div class='btBox'>");
                strhtml.Append("<div class='bt1'>");
                var errorUrl = BuildErrorRedirectUrl(redirectUri, ex.Error, ex.ErrorDescription, state);
                strhtml.Append($"<a href='{HttpUtility.HtmlEncode(errorUrl)}'>返回应用</a>");
                strhtml.Append("</div>");
                strhtml.Append("</div>");
            }
        }

    }
}