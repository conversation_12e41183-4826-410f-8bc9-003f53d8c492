import { FormValidationService } from '../services/FormValidationService.js';
import { NavigationService } from '../services/NavigationService.js';

/**
 * 个人资料编辑页面
 * 应用FormValidationService进行表单验证
 */
export class EditProfilePage {
    private formValidationService: FormValidationService;
    private navigationService: NavigationService;

    constructor() {
        this.formValidationService = FormValidationService.getInstance();
        this.navigationService = NavigationService.getInstance();
    }

    /**
     * 初始化页面
     */
    public init(): void {
        this.setupFormValidation();
        this.setupNavigationService();
        this.setupExpandMoreFields();
        this.initializeLucideIcons();
    }

    /**
     * 设置表单验证
     */
    private setupFormValidation(): void {
        const form = document.getElementById('profile-form') as HTMLFormElement;
        if (!form) {
            console.warn('Profile form not found');
            return;
        }

        // 注册表单验证配置
        const config = FormValidationService.createProfileValidationConfig();
        this.formValidationService.registerForm('profile-form', config);

        // 初始化表单验证
        this.formValidationService.initializeForm(form);

        // 设置表单提交处理
        this.setupFormSubmit(form);
    }

    /**
     * 设置表单提交处理
     */
    private setupFormSubmit(form: HTMLFormElement): void {
        form.addEventListener('submit', (e) => {
            // 验证表单
            if (!this.formValidationService.validateForm(form)) {
                e.preventDefault();
                return;
            }

            // 显示提交状态
            const submitButton = form.querySelector('.form-submit') as HTMLButtonElement;
            if (submitButton) {
                submitButton.innerHTML = '<i data-lucide="loader-2" class="icon animate-spin"></i>保存中...';
                submitButton.disabled = true;
                
                // 重新创建图标
                setTimeout(() => {
                    this.initializeLucideIcons();
                }, 10);
            }
        });
    }

    /**
     * 设置导航服务
     */
    private setupNavigationService(): void {
        // 初始化返回按钮（如果页面有的话）
        this.navigationService.initializeBackButton('#back-button');
    }

    /**
     * 设置展开更多字段功能
     */
    private setupExpandMoreFields(): void {
        const expandBtn = document.getElementById('expand-more-btn');
        const moreFields = document.getElementById('more-fields');
        const expandBtnText = expandBtn?.querySelector('.expand-btn-text') as HTMLElement;
        let isExpanded = false;
        
        if (expandBtn && moreFields && expandBtnText) {
            expandBtn.addEventListener('click', () => {
                isExpanded = !isExpanded;
                
                if (isExpanded) {
                    // 展开
                    moreFields.classList.remove('hidden');
                    expandBtn.classList.add('expanded');
                    expandBtnText.textContent = '收起更多';
                    
                    // 动画辅助类，平滑展开
                    moreFields.classList.add('expanding');
                    setTimeout(() => {
                        moreFields.classList.remove('expanding');
                        this.scrollToElementIfNeeded(moreFields);
                    }, 200);
                } else {
                    // 收起
                    expandBtn.classList.remove('expanded');
                    expandBtnText.textContent = '展开更多';
                    
                    // 动画辅助类，平滑收起
                    moreFields.classList.add('collapsing');
                    setTimeout(() => {
                        moreFields.classList.add('hidden');
                        moreFields.classList.remove('collapsing');
                    }, 200);
                }
            });
        }
    }

    /**
     * 如果元素不在视窗内，滚动到元素位置
     */
    private scrollToElementIfNeeded(element: HTMLElement): void {
        const rect = element.getBoundingClientRect();
        const isVisible = rect.top >= 0 && 
                          rect.left >= 0 && 
                          rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) && 
                          rect.right <= (window.innerWidth || document.documentElement.clientWidth);
        
        if (!isVisible) {
            const headerHeight = 60;
            const scrollPosition = element.offsetTop - headerHeight;
            window.scrollTo({
                top: scrollPosition,
                behavior: 'smooth'
            });
        }
    }

    /**
     * 初始化Lucide图标
     */
    private initializeLucideIcons(): void {
        if (typeof (window as any).lucide !== 'undefined') {
            (window as any).lucide.createIcons();
        }
    }

    /**
     * 手动验证特定字段
     */
    public validateField(fieldName: string): boolean {
        const form = document.getElementById('profile-form') as HTMLFormElement;
        if (!form) return false;

        const fieldElement = form.querySelector(`[name="${fieldName}"]`) as HTMLInputElement;
        if (!fieldElement) return false;

        const config = FormValidationService.createProfileValidationConfig();
        const fieldConfig = config.fields[fieldName];
        if (!fieldConfig) return false;

        const result = this.formValidationService.validateField(fieldElement, fieldConfig);
        return result.isValid;
    }

    /**
     * 清除字段错误
     */
    public clearFieldError(fieldName: string): void {
        const form = document.getElementById('profile-form') as HTMLFormElement;
        if (!form) return;

        const fieldElement = form.querySelector(`[name="${fieldName}"]`) as HTMLInputElement;
        if (!fieldElement) return;

        // 移除错误样式
        fieldElement.classList.remove('border-red-500', 'error', 'border-danger');
        
        // 移除错误消息
        const parent = fieldElement.parentNode;
        if (parent) {
            const existingError = parent.querySelector('.form-error, .text-xs.text-red-500');
            if (existingError) {
                existingError.remove();
            }
        }
    }

    /**
     * 获取表单数据
     */
    public getFormData(): { [key: string]: string } {
        const form = document.getElementById('profile-form') as HTMLFormElement;
        if (!form) return {};

        const formData = new FormData(form);
        const data: { [key: string]: string } = {};
        
        formData.forEach((value, key) => {
            data[key] = value.toString();
        });

        return data;
    }

    /**
     * 设置表单数据
     */
    public setFormData(data: { [key: string]: string }): void {
        const form = document.getElementById('profile-form') as HTMLFormElement;
        if (!form) return;

        Object.entries(data).forEach(([key, value]) => {
            const fieldElement = form.querySelector(`[name="${key}"]`) as HTMLInputElement;
            if (fieldElement) {
                fieldElement.value = value;
            }
        });
    }

    /**
     * 重置表单
     */
    public resetForm(): void {
        const form = document.getElementById('profile-form') as HTMLFormElement;
        if (form) {
            form.reset();
            
            // 清除所有错误状态
            const errorElements = form.querySelectorAll('.form-error, .text-xs.text-red-500');
            errorElements.forEach(element => element.remove());
            
            const inputElements = form.querySelectorAll('.form-input');
            inputElements.forEach(element => {
                element.classList.remove('border-red-500', 'error', 'border-danger');
            });
        }
    }

    /**
     * 检查表单是否有变更
     */
    public hasFormChanged(): boolean {
        const form = document.getElementById('profile-form') as HTMLFormElement;
        if (!form) return false;

        const inputs = form.querySelectorAll('input, textarea, select');
        for (let i = 0; i < inputs.length; i++) {
            const input = inputs[i];
            const element = input as HTMLInputElement;
            if (element.type === 'checkbox' || element.type === 'radio') {
                if (element.checked !== element.defaultChecked) {
                    return true;
                }
            } else {
                if (element.value !== element.defaultValue) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 显示保存成功提示
     */
    public showSaveSuccess(message: string = '保存成功'): void {
        // 这里可以集成ToastService，但为了简化先使用简单的提示
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    /**
     * 显示保存失败提示
     */
    public showSaveError(message: string = '保存失败'): void {
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    const editProfilePage = new EditProfilePage();
    editProfilePage.init();
    
    // 全局暴露实例，方便调试和其他脚本使用
    (window as any).editProfilePage = editProfilePage;
});

// 导出类供其他模块使用
export default EditProfilePage;
