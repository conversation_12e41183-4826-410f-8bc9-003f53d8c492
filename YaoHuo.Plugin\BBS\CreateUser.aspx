﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="CreateUser.aspx.cs" Inherits="YaoHuo.Plugin.BBS.CreateUser" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    Response.Write(WapTool.showTop(this.GetLang("创建新用户|创建新用户|Create New User"), wmlVo));
    StringBuilder strhtml = new StringBuilder();
    if (this.IsCheckManagerLvl("|00|", "") == true)
    {
        strhtml.Append("<div class=\"title\">创建新用户</div>");

        if (ERROR != "")
        {
            strhtml.Append("<div class=\"tip\">");
            strhtml.Append(ERROR);
            strhtml.Append("</div>");
        }

        if (INFO == "OK")
        {
            if (action == "gomod")
            {
                // 创建用户成功的提示
                strhtml.Append("<div class=\"tip\" style=\"display:flex;align-items:center;justify-content:center;gap:10px;line-height:39px;\">");
                strhtml.Append("<b>新用户<span style=\"padding: 2px;\" >" + CreatedUserId + "</span>创建成功</b>");
                strhtml.Append("<button onclick='copyToClipboard()' id='copyBtn' style=' padding: 6px 12px; background-color: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; transition: background-color 0.3s; box-shadow: 0 2px 4px rgba(0,0,0,0.1); '>复制信息</button>");
                strhtml.Append("</div>");

                // 添加帖子更新状态提示
                if (!string.IsNullOrEmpty(UpdatePostStatus))
                {
                    strhtml.Append("<div class=\"tip\" style=\"text-align:center;color:" + (UpdatePostStatus.Contains("失败") ? "red" : "green") + ";\">");
                    strhtml.Append("<small>✓ " + UpdatePostStatus + "</small>");
                    strhtml.Append("</div>");
                }

                // 复制到剪贴板的功能（仅在创建用户成功时显示）
                string clipboardText = string.Format("可以登录了，初始密码：{0}\n手机号和ID号都可以作为登录用户名\n登录后可以修改密码，但不要设置简单的密码以免被盗\n修改密码路径：首页顶部——我的地盘——修改资料\n也可以长按本消息添加到QQ收藏备份初始密码\n如果忘记密码可以联系我找回\n请保存网站的书签避免进入钓鱼网站\nhttps://yaohuo.me/\n谢谢支持！", RandomPassword);

                strhtml.Append("<div style='display:none;'>");
                strhtml.Append("<textarea id='clipboardContent'>" + clipboardText + "</textarea>");
                strhtml.Append("</div>");

                // JavaScript代码（仅在创建用户成功时添加）
                strhtml.Append(@"<script>
            async function copyToClipboard() {
                var text = document.getElementById('clipboardContent').value;
                var btn = document.getElementById('copyBtn');
                
                // 设置按钮状态函数
                function setBtnText(text) {
                    btn.textContent = text;
                    if(text === '复制成功') {
                        setTimeout(() => {
                            btn.textContent = '复制信息';
                        }, 1000);
                    }
                }

                // 尝试使用现代API
                if (navigator.clipboard && window.isSecureContext) {
                    try {
                        await navigator.clipboard.writeText(text);
                        setBtnText('复制成功');
                        return;
                    } catch (err) {
                        console.error('无法使用 Clipboard API');
                    }
                }

                // 降级方案1：使用 execCommand
                try {
                    var textArea = document.getElementById('clipboardContent');
                    textArea.style.display = 'block';
                    textArea.focus();
                    textArea.select();
                    var successful = document.execCommand('copy');
                    textArea.style.display = 'none';
                    if (successful) {
                        setBtnText('复制成功');
                        return;
                    }
                } catch (err) {
                    console.error('execCommand 复制失败');
                }

                // 降级方案2：显示文本让用户手动复制
                textArea.style.display = 'block';
                setBtnText('请手动复制');
            }
            </script>");
            }
        }

        // 添加 CSS 样式
        strhtml.Append("<style>");
        strhtml.Append(@"
        .form-group { 
            margin-bottom: 5px;
        }
        .form-label {
            display: block;
            margin-bottom: -10px;
        }
        .form-input {
            width: 100%;
            padding: 5px;
            box-sizing: border-box;
        }
        .form-select {
            width: 100%;
            padding: 5px;
            box-sizing: border-box;
        }
        .form-submit {
            margin-top: 10px;
            padding: 8px 15px;
        }
    ");
        strhtml.Append("</style>");

        // 修改表单结构
        strhtml.Append("<div class=\"content\">");
        strhtml.Append("<Form name=\"f\" action=\"" + http_start + "bbs/CreateUser.aspx\" method=\"post\">");
        strhtml.Append("<input name=\"action\" type=\"hidden\" value=\"gomod\" />");
        strhtml.Append("<input name=\"siteid\" type=\"hidden\" value=\"" + siteid + "\" />");

        strhtml.Append("<div class=\"form-group\">");
        strhtml.Append("<label class=\"form-label\">ID号</label>");
        strhtml.Append("<input class=\"form-input\" type=\"text\" name=\"userid\" value=\"\" onInput=\"checkIdLength(this.value)\" />");
        strhtml.Append("</div>");

        strhtml.Append("<div class=\"form-group\">");
        strhtml.Append("<label class=\"form-label\">用户名</label>");
        strhtml.Append("<input class=\"form-input\" type=\"text\" name=\"username\" value=\"\" placeholder=\"填写手机号\" />");
        strhtml.Append("</div>");

        strhtml.Append("<div class=\"form-group\">");
        strhtml.Append("<label class=\"form-label\">密码</label>");
        // 添加随机密码生成逻辑
        Random random = new Random(Guid.NewGuid().GetHashCode());
        string upperChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        string lowerChars = "abcdefghijklmnopqrstuvwxyz";
        string numbers = "0123456789";

        // 确保密码含大写、小写和数字
        string randomPassword = new string(new[] {
        upperChars[random.Next(upperChars.Length)],
        upperChars[random.Next(upperChars.Length)],
        upperChars[random.Next(upperChars.Length)],
        lowerChars[random.Next(lowerChars.Length)],
        lowerChars[random.Next(lowerChars.Length)],
        lowerChars[random.Next(lowerChars.Length)],
        lowerChars[random.Next(lowerChars.Length)],
        numbers[random.Next(numbers.Length)],
        numbers[random.Next(numbers.Length)],
        numbers[random.Next(numbers.Length)],
        lowerChars[random.Next(lowerChars.Length)],
        upperChars[random.Next(upperChars.Length)]
    }.OrderBy(x => random.Next()).ToArray());

        strhtml.Append(string.Format("<input class=\"form-input\" type=\"text\" name=\"password\" value=\"{0}\" />", randomPassword));
        strhtml.Append("</div>");

        strhtml.Append("<div class=\"form-group\">");
        strhtml.Append("<label class=\"form-label\">备注</label>");
        strhtml.Append("<input class=\"form-input\" type=\"text\" name=\"qq\" value=\"\" placeholder=\"选填QQ号\" />");
        strhtml.Append("</div>");

        strhtml.Append("<div class=\"form-group\">");
        strhtml.Append("<label class=\"form-label\">昵称</label>");
        strhtml.Append("<input class=\"form-input\" type=\"text\" name=\"nickname\" value=\"\" placeholder=\"留空则默认为'妖友+ID号'\" />");
        strhtml.Append("</div>");

        strhtml.Append("<div class=\"form-group\">");
        strhtml.Append("<label class=\"form-label\">妖晶</label>");
        strhtml.Append("<select class=\"form-select\" name=\"money\" id=\"moneySelect\">");
        strhtml.Append("<option value=\"20000\">20000妖晶（靓号勋章）</option>");
        strhtml.Append("<option value=\"80000\">80000妖晶（靓号勋章）</option>");
        strhtml.Append("<option value=\"10000\">10000妖晶（不赠送靓号勋章）</option>");
        strhtml.Append("<option value=\"0\">不赠送</option>");
        strhtml.Append("</select>");
        strhtml.Append("</div>");

        strhtml.Append("<input class=\"form-submit\" type=\"submit\" name=\"submit\" value=\"创建用户\" />");
        strhtml.Append("</Form>");
        strhtml.Append("</div>");

        // 添加查询表单
        strhtml.Append("<div class=\"title\">查询用户信息</div>");
        strhtml.Append("<div class=\"content\">");
        strhtml.Append("<Form name=\"f2\" action=\"" + http_start + "bbs/CreateUser.aspx\" method=\"post\">");
        strhtml.Append("<input name=\"action\" type=\"hidden\" value=\"query\" />");
        strhtml.Append("<input name=\"siteid\" type=\"hidden\" value=\"" + siteid + "\" />");

        strhtml.Append("<div class=\"form-group\">");
        strhtml.Append("<label class=\"form-label\">用户ID</label>");
        strhtml.Append("<input class=\"form-input\" type=\"text\" name=\"queryuserid\" value=\"" + queryUserid + "\" />");
        strhtml.Append("</div>");

        strhtml.Append("<input class=\"form-submit\" type=\"submit\" name=\"submit\" value=\"查询用户\" />");
        strhtml.Append("</Form>");
        strhtml.Append("</div>");

        if (!string.IsNullOrEmpty(queryResult))
        {
            strhtml.Append("<div class=\"tip\">");
            strhtml.Append(queryResult);
            strhtml.Append("</div>");

            // 修改这里：添加条件 !queryResult.Contains("未找到该用户信息")
            if (!queryResult.Contains("删除成功") && !queryResult.Contains("未找到该用户信息"))
            {
                // 从queryResult中提取数据进行判断
                bool canDelete = true;
                bool needExtraConfirm = false; // 是否需要再次确认
                string extraConfirmReason = ""; // 额外确认的原因

                // 检查是否是站长账号(ID 1000)
                if (queryUserid == "1000")
                {
                    canDelete = false;
                }
                else
                {
                    // 解析queryResult中的数据
                    string[] lines = queryResult.Split(new string[] { "<br/>" }, StringSplitOptions.None);
                    foreach (string line in lines)
                    {
                        if (line.StartsWith("回复数量："))
                        {
                            int replyCount;
                            if (int.TryParse(line.Replace("回复数量：", ""), out replyCount) && replyCount > 100)
                            {
                                needExtraConfirm = true;
                                extraConfirmReason += "\\n* 该用户回复数量超过100条";
                            }
                        }
                        else if (line.StartsWith("发帖数量："))
                        {
                            int postCount;
                            if (int.TryParse(line.Replace("发帖数量：", ""), out postCount) && postCount > 10)
                            {
                                needExtraConfirm = true;
                                extraConfirmReason += "\\n* 该用户发帖数量超过10条";
                            }
                        }
                        else if (line.StartsWith("在线时间："))
                        {
                            string timeStr = line.Replace("在线时间：", "");
                            // 检查是否超过24小时
                            if (timeStr.Contains("天") || timeStr.Contains("月") || timeStr.Contains("年") ||
                                (timeStr.Contains("小时") && int.Parse(timeStr.Replace("小时", "")) >= 24))
                            {
                                needExtraConfirm = true;
                                extraConfirmReason += "\\n* 该用户在线时间超过24小时";
                            }
                        }
                    }
                }

                // 显示删除按钮，根据条件不同显示不同的确认提示
                if (canDelete)
                {
                    strhtml.Append("<div class=\"tip\">");
                    string confirmMsg = "警告：确定要删除该用户吗？\\n\\n此操作将删除：\\n1. 用户所有发帖\\n2. 用户所有回复\\n3. 用户所有信息\\n\\n此操作不可恢复！";
                    
                    // 如果需要额外确认，添加额外警告内容
                    if (needExtraConfirm)
                    {
                        confirmMsg += "\\n\\n⚠️ 特别警告 ⚠️\\n该用户数据较多：" + extraConfirmReason + "\\n\\n请再次确认是否删除？";
                    }
                    
                    strhtml.Append("<Form name=\"f3\" action=\"" + http_start + "bbs/CreateUser.aspx\" method=\"post\" onsubmit=\"return confirm('" + confirmMsg + "');\">");
                    strhtml.Append("<input name=\"action\" type=\"hidden\" value=\"delete\" />");
                    strhtml.Append("<input name=\"siteid\" type=\"hidden\" value=\"" + siteid + "\" />");
                    strhtml.Append("<input name=\"userid\" type=\"hidden\" value=\"" + queryUserid + "\" />");
                    strhtml.Append("<input type=\"submit\" name=\"submit\" value=\"删除用户\"/>");
                    strhtml.Append("</Form>");
                    strhtml.Append("</div>");
                }
            }
        }

        strhtml.Append(@"<script>
        function checkIdLength(value) {
        const moneySelect = document.getElementById('moneySelect');
        const num = value.replace(/D/g, '');
        const numLength = num.length;
        if (numLength === 5 && num.includes('4')) {
            moneySelect.value = '10000';
        } else if (numLength === 5) {
            moneySelect.value = '20000';
        } else if (numLength === 4 || numLength === 3) {
            moneySelect.value = '80000';
        }
    }
    </script>");
    }
    Response.Write(strhtml);
%>