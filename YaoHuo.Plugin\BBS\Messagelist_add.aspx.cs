﻿using System;
using System.Linq;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class MessageList_add : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string page = "";

        public string INFO = "";

        public string ERROR = "";

        public string touseridlist = "";

        public string touseridlistall = "";

        public string title = "";

        public string content = "";

        public string backurl = "";

        public string types = "";

        public string who = "";

        public string id = "";

        public string toid = "";

        public bool isadmin = false;

        public bool classadmin = false;

        public string KL_SendMSGCount = PubConstant.GetAppString("KL_SendMSGCount");

        public string KL_CheckIPTime = PubConstant.GetAppString("KL_CheckIPTime");

        public string KL_SendMsgNeedPW = PubConstant.GetAppString("KL_SendMsgNeedPW");

        public string needpwFlag = "";

        public string needpw = "";

        public string issystem = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            // 修复：AJAX请求且action!=gomod时，直接返回NULL，避免返回HTML
            if (GetRequestValue("ajax") == "1" && GetRequestValue("action") != "gomod")
            {
                Response.Clear();
                Response.ContentType = "text/plain";
                Response.Write("NULL");
                Response.End();
                return;
            }
            action = base.Request.Form.Get("action");
            types = GetRequestValue("types");
            issystem = GetRequestValue("issystem");
            who = GetRequestValue("who");
            touseridlistall = GetRequestValue("touseridlist");
            if (touseridlistall == "")
            {
                touseridlistall = GetRequestValue("touserid");
            }
            title = GetRequestValue("title");
            content = base.Request.Form.Get("content");
            content = ToHtm(content);
            id = GetRequestValue("id");
            toid = GetRequestValue("toid");
            needpw = GetRequestValue("needpw");
            needpwFlag = "0";
            if (WapTool.GetArryString(siteVo.Version, '|', 53) == "1")
            {
                needPassWordToAdmin();
            }
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            IsLogin(userid, "bbs/messagelist.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;types=0");
            if (KL_SendMsgNeedPW == null)
            {
                KL_SendMsgNeedPW = "0";
            }
            if (KL_SendMsgNeedPW == "1")
            {
                needPassWordToAdmin();
            }
            isadmin = IsUserManager(userid, userVo.managerlvl, classVo.adminusername);
            if (!isadmin)
            {
                classadmin = WapTool.IsClassAdmin(siteid, userid);
            }
            if (who == "all")
            {
                touseridlistall = "all";
                needPassWordToAdmin();
            }
            else if (who == "admin")
            {
                touseridlistall = WapTool.GetALLAdmin(siteid);
                needPassWordToAdmin();
            }
            else if (who == "friend")
            {
                touseridlistall = WapTool.GetMyAllFirend(siteid, userid);
            }
            if (WapTool.IsNumeric(id))
            {
                // ✅ 使用DapperHelper安全获取消息内容
                try
                {
                    string connectionString = PubConstant.GetConnectionString(string_10);
                    long messageId = DapperHelper.SafeParseLong(id, "消息ID");

                    string selectSql = "SELECT title, content FROM wap_message WHERE id = @MessageId";
                    var messageData = DapperHelper.Query<dynamic>(connectionString, selectSql, new {
                        MessageId = messageId
                    }).FirstOrDefault();

                    if (messageData != null)
                    {
                        title = messageData.title ?? "";
                        content = messageData.content ?? "";
                    }
                }
                catch (Exception ex)
                {
                    ERROR = WapTool.ErrorToString(ex.ToString());
                }
            }
            if (!(action == "gomod"))
            {
                // 不直接return，统一在后面处理ajax
                INFO = "";
            }
            else
            {
                try
                {
                    if (title.Trim() == "")
                    {
                        if (content.Length > 20)
                        {
                            title = content.Substring(0, 20).Replace("&", "&amp;") + "...";
                        }
                        else
                        {
                            title = content;
                        }
                    }
                    string[] array = touseridlistall.Split('.');
                    if (!WapTool.IsNumeric(id) && content.Equals(Session["content"]))
                    {
                        INFO = "REPEAT";
                    }
                    else if (needpwFlag == "1" && PubConstant.md5(needpw).ToLower() != userVo.password.ToLower())
                    {
                        INFO = "PWERROR";
                    }
                    else if (content.Trim() == "" || touseridlistall.Trim() == "")
                    {
                        INFO = "NULL";
                    }
                    else if (isCheckIPTime(long.Parse(KL_CheckIPTime)))
                    {
                        INFO = "WAITING";
                    }
                    else if (!isadmin && KL_SendMSGCount != "0" && array.Length > long.Parse(KL_SendMSGCount))
                    {
                        INFO = "MAX1";
                    }
                    else if (!isadmin && !classadmin && KL_SendMSGCount != "0" && WapTool.CheckSendMSGCount(siteid, userid) > long.Parse(KL_SendMSGCount))
                    {
                        INFO = "MAX";
                    }
                    else if (WapTool.IsLockuser(siteid, userid, classid) > -1L)
                    {
                        INFO = "LOCK";
                    }
                    else if (touseridlistall == "all" && "|00|01|".IndexOf(userVo.managerlvl) < 0)
                    {
                        INFO = "ALLERR";
                    }
                    else
                    {
                        if (touseridlistall == "all" && isadmin)
                        {
                            // ✅ 使用DapperHelper安全发送全站消息
                            try
                            {
                                string connectionString = PubConstant.GetConnectionString(string_10);
                                long siteIdLong = DapperHelper.SafeParseLong(siteid, "站点ID");
                                long userIdLong = DapperHelper.SafeParseLong(userid, "用户ID");

                                // 发送全站消息的SQL（模拟AddALL功能）
                                string insertSql = @"INSERT INTO wap_message (siteid, userid, nickname, title, content, touserid, tonickname, isnew, issystem, addtime)
                                                   SELECT @SiteId, @UserId, @Nickname, @Title, @Content, userid, NULL, 1, 0, @AddTime
                                                   FROM [user] WHERE siteid = @SiteId";

                                DapperHelper.Execute(connectionString, insertSql, new {
                                    SiteId = siteIdLong,
                                    UserId = userIdLong,
                                    Nickname = DapperHelper.LimitLength(nickname, 50),
                                    Title = DapperHelper.LimitLength(title, 200),
                                    Content = DapperHelper.LimitLength(content, 2000),
                                    AddTime = DateTime.Now
                                });

                                INFO = "OK";
                                Session["content"] = content;
                            }
                            catch (Exception ex)
                            {
                                ERROR = WapTool.ErrorToString(ex.ToString());
                                INFO = "ERROR";
                            }
                        }
                        else
                        {
                            // ✅ 使用DapperHelper安全发送个人消息
                            string connectionString = PubConstant.GetConnectionString(string_10);
                            long siteIdLong = DapperHelper.SafeParseLong(siteid, "站点ID");
                            long userIdLong = DapperHelper.SafeParseLong(userid, "用户ID");

                            bool flag = true;
                            bool flag2 = true;
                            INFO = "OK";

                            for (int i = 0; i < array.Length; i++)
                            {
                                touseridlist = array[i];
                                if (!WapTool.IsExistUser(siteid, touseridlist))
                                {
                                    INFO = "NOTEXSIT";
                                    break;
                                }
                                if (WapTool.IsExistNotFriends(siteid, userid, touseridlist) && !isadmin)
                                {
                                    flag2 = false;
                                }
                                if (flag && flag2)
                                {
                                    try
                                    {
                                        long toUserIdLong = DapperHelper.SafeParseLong(touseridlist, "目标用户ID");
                                        DateTime currentTime = DateTime.Now;

                                        // 插入收件箱消息 (isnew=1)
                                        string insertInboxSql = @"INSERT INTO wap_message (siteid, userid, nickname, title, content, touserid, tonickname, isnew, issystem, addtime)
                                                                VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, NULL, 1, 0, @AddTime)";

                                        DapperHelper.Execute(connectionString, insertInboxSql, new {
                                            SiteId = siteIdLong,
                                            UserId = userIdLong,
                                            Nickname = DapperHelper.LimitLength(nickname, 50),
                                            Title = DapperHelper.LimitLength(title, 200),
                                            Content = DapperHelper.LimitLength(content, 2000),
                                            ToUserId = toUserIdLong,
                                            AddTime = currentTime
                                        });

                                        // 插入发件箱消息 (isnew=2)
                                        string insertOutboxSql = @"INSERT INTO wap_message (siteid, userid, nickname, title, content, touserid, tonickname, isnew, issystem, addtime)
                                                                 VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, NULL, 2, 0, @AddTime)";

                                        DapperHelper.Execute(connectionString, insertOutboxSql, new {
                                            SiteId = siteIdLong,
                                            UserId = toUserIdLong,
                                            Nickname = DapperHelper.LimitLength(nickname, 50),
                                            Title = DapperHelper.LimitLength(title, 200),
                                            Content = DapperHelper.LimitLength(content, 2000),
                                            ToUserId = userIdLong,
                                            AddTime = currentTime
                                        });

                                        touseridlist = "";
                                        Session["content"] = content;
                                        VisiteCount("发了一条站内信息！");
                                        Action_user_doit(7);
                                    }
                                    catch (Exception ex)
                                    {
                                        ERROR = WapTool.ErrorToString(ex.ToString());
                                        INFO = "ERROR";
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    ERROR = WapTool.ErrorToString(ex.ToString());
                }
            }
            // 统一处理AJAX响应，保证所有return都能正确返回INFO
            if (GetRequestValue("ajax") == "1")
            {
                Response.Clear();
                Response.ContentType = "text/plain";
                Response.Write(INFO);
                Response.End();
                return;
            }
        }
    }
}