// 表情数据存储
let emoticons = {};
let emoticonFallbacks = {};

// 从JSON文件加载表情数据
async function loadEmojiData() {
    try {
        console.log('开始加载表情数据...');
        const response = await fetch('/Data/StaticData/Emoji.json');

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('表情数据加载成功:', data.config);

        // 更新全局变量
        emoticons = data.emoticons || {};
        emoticonFallbacks = data.emoticonFallbacks || {};

        console.log(`加载了 ${Object.keys(emoticons).length} 个表情`);
        return true;
    } catch (error) {
        console.error('加载表情数据失败:', error);

        // 使用fallback数据
        console.log('使用fallback表情数据');
        emoticons = getFallbackEmoticons();
        emoticonFallbacks = getFallbackEmoticonFallbacks();

        return false;
    }
}

// Fallback表情数据（当JSON加载失败时使用）
function getFallbackEmoticons() {
    return {
        "踩.gif": "https://article.biliimg.com/bfs/article/92be16daa0ed761297857d86847a671282031844.gif",
        "狂踩.gif": "https://article.biliimg.com/bfs/article/5a072404946d9498bb2b1c706c55d283585906522.gif",
        "淡定.gif": "https://article.biliimg.com/bfs/article/42453f37182aeb93d1367a89f2c6d81e668830195.gif",
        "囧.gif": "https://article.biliimg.com/bfs/article/0de9369b0d1166481e08ff3758971e843493114633259656.gif",
        "不要.gif": "https://article.biliimg.com/bfs/article/0816ade5895b23b252584511e871fddc1196447742.gif",
        "重拳出击.gif": "https://article.biliimg.com/bfs/article/3a2f982f923bf9be90e0b904d7235eb5325579615.gif",
        "砳砳.gif": "https://article.biliimg.com/bfs/article/63607a6076ff09b4493c1e88c974d02e8578624.gif",
        "滑稽砳砳.gif": "https://article.biliimg.com/bfs/article/0527131bafe7388a4b3e5366b8bc9a7b82031844.gif",
        "沙发.gif": "https://article.biliimg.com/bfs/article/1526d0a2a9d9727c6cd833517f43314d1926952713.gif",
        "汗.gif": "https://article.biliimg.com/bfs/article/4bc3c0729a02b786bd88ff3c6f7c7024552492916.gif",
        "亲亲.gif": "https://article.biliimg.com/bfs/article/ef67d7ae35289d8a97e9d30cc4b6b98d403349612.gif",
        "太开心.gif": "https://article.biliimg.com/bfs/article/e108dcf2ed3c7b3e5604dcadbd62681d3493114633259656.gif",
        "酷.gif": "https://article.biliimg.com/bfs/article/db197c3722d2d2ea563642d24d974061627289376.gif",
        "思考.gif": "https://article.biliimg.com/bfs/article/5f437db9070383bc86564eb4a9f88f0b552492916.gif",
        "发呆.gif": "https://article.biliimg.com/bfs/article/8aa57f4c36ab82309c9744b3a0fa5cf518171498.gif",
        "得瑟.gif": "https://article.biliimg.com/bfs/article/1611608d46f423556b4033b3b9d1550b18171498.gif",
        "哈哈.gif": "https://article.biliimg.com/bfs/article/c49daadb129151a82674ad50da965bc23493083039664762.gif",
        "泪流满面.gif": "https://article.biliimg.com/bfs/article/8dff7c2fcbb1bffcdd14d5b6700c041e25285721.gif",
        "放电.gif": "https://article.biliimg.com/bfs/article/3135637787de8390735dcf465977260a1357503349.gif",
        "困.gif": "https://article.biliimg.com/bfs/article/0f61061068ec1a22081841a6481e449b403349612.gif",
        "超人.gif": "https://article.biliimg.com/bfs/article/ec17b5c1e11a4e5fa67124db6aae2eec1926952713.gif",
        "害羞.gif": "https://article.biliimg.com/bfs/article/9aebc41bdd57d7adff32e0f54c8059622043790667.gif",
        "呃.gif": "https://article.biliimg.com/bfs/article/35914f6520e250cc93bb57864b69d6481930467259.gif",
        "哇哦.gif": "https://article.biliimg.com/bfs/article/58193f3209ce73ae93c497d2cb313a048578624.gif",
        "呦呵.gif": "https://article.biliimg.com/bfs/article/cc21c68b32ce508f8b930084d05b83ad18171498.gif",
        "要死了.gif": "https://article.biliimg.com/bfs/article/28fcaaa37726714a105cbd7b92f468cb1930467259.gif",
        "谢谢.gif": "https://article.biliimg.com/bfs/article/b682d4d2bf2cfda9b8789608e6d1a307585906522.gif",
        "抓狂.gif": "https://article.biliimg.com/bfs/article/c01fe27395afe4dcf277d827476aeb56322324115.gif",
        "无奈.gif": "https://article.biliimg.com/bfs/article/2c39a36e8b788cbca7beb2c8be6f998c252535571.gif",
        "不好笑.gif": "https://article.biliimg.com/bfs/article/c2a9a7027b3397268e2adf2ed8b5747d82031844.gif",
        "感动.gif": "https://article.biliimg.com/bfs/article/f11bba2613d99b754334e9c9566ac00e783.gif",
        "喜欢.gif": "https://article.biliimg.com/bfs/article/352d2c479258c588ec99b66dba9c8aa55208806.gif",
        "疑问.gif": "https://article.biliimg.com/bfs/article/bb13e6d029cb8d24585699492d4921da495191322.gif",
        "委屈.gif": "https://article.biliimg.com/bfs/article/5997efbc51eab8de212a58355153d4ff627289376.gif",
        "你不行.gif": "https://article.biliimg.com/bfs/article/aea051681018efa975f6206273a840ef585906522.gif",
        "流口水.gif": "https://article.biliimg.com/bfs/article/a67e37a460acd1ecebb3cb2777a48699323813984.gif",
        "咒骂.gif": "https://article.biliimg.com/bfs/article/87337cd3b73c8d14c786c9a2d7008cf11216213697.gif",
        "耶耶.gif": "https://article.biliimg.com/bfs/article/96a00f036d2f65d8f116bb8b4743ff6b1357503349.gif",
        "被揍.gif": "https://article.biliimg.com/bfs/article/e95e27f7d2c2ea2eb343535fd4fc16d7688442162.gif",
        "抱走.gif": "https://article.biliimg.com/bfs/article/2cf61a32a6ed2c95f96875b310a130d8330807149.gif"
    };
}

// Fallback备用链接数据（当JSON加载失败时使用）
function getFallbackEmoticonFallbacks() {
    return {
        "https://article.biliimg.com/bfs/article/92be16daa0ed761297857d86847a671282031844.gif": {
            fallback1: "https://p1.meituan.net/csc/a9da78bcb7f9fd5316078bd5e8f1244c21926.gif",
            fallback2: "https://fc.sinaimg.cn/large/007cQIj3gy1hizmv6ib31g302s02sdg5.gif"
        },
        "https://article.biliimg.com/bfs/article/5a072404946d9498bb2b1c706c55d283585906522.gif": {
            fallback1: "https://p0.meituan.net/csc/077be3d6bffa0871fd7e8ce604e8745a21813.gif",
            fallback2: "https://fc.sinaimg.cn/large/007cQIj3gy1hizn0w3913g302s02saad.jpg"
        },
        "https://article.biliimg.com/bfs/article/42453f37182aeb93d1367a89f2c6d81e668830195.gif": {
            fallback1: "https://p0.meituan.net/csc/9d17e46fd3695671776fa6a44237e22e23962.gif",
            fallback2: "https://fc.sinaimg.cn/large/007cQIj3gy1hiznt2o91tg301y023dg7.jpg"
        },
        "https://article.biliimg.com/bfs/article/0de9369b0d1166481e08ff3758971e843493114633259656.gif": {
            fallback1: "https://p0.meituan.net/csc/07a03c47279480e44534051fe54611b18482.gif",
            fallback2: "https://fc.sinaimg.cn/large/007cQIj3gy1hiznt8kx2yg301y025aa0.jpg"
        },
        "https://article.biliimg.com/bfs/article/0816ade5895b23b252584511e871fddc1196447742.gif": {
            fallback1: "https://p0.meituan.net/csc/7b8113ed9091f5411fb847682226ac4a16418.gif",
            fallback2: "https://fc.sinaimg.cn/large/007cQIj3gy1hizntoqbawg301y0250sw.jpg"
        },
        "https://article.biliimg.com/bfs/article/3a2f982f923bf9be90e0b904d7235eb5325579615.gif": {
            fallback1: "https://p0.meituan.net/csc/2c0d24745d8e508a158ce73b985bf62e54381.gif",
            fallback2: "https://fc.sinaimg.cn/large/007cQIj3gy1hizntvfsu8g302s02s3zp.jpg"
        },
        "https://article.biliimg.com/bfs/article/63607a6076ff09b4493c1e88c974d02e8578624.gif": {
            fallback1: "https://p0.meituan.net/csc/e405b6e66c05519fa4b249d763b0b8f236097.gif",
            fallback2: "https://fc.sinaimg.cn/large/007cQIj3gy1hiznub96wwg302s03274z.jpg"
        },
        "https://article.biliimg.com/bfs/article/0527131bafe7388a4b3e5366b8bc9a7b82031844.gif": {
            fallback1: "https://p1.meituan.net/csc/0f532b017d8dd67fcfd043096724a45a40284.gif",
            fallback2: "https://fc.sinaimg.cn/large/007cQIj3gy1hiznuge3g5g302s032aav.jpg"
        },
        "https://article.biliimg.com/bfs/article/1526d0a2a9d9727c6cd833517f43314d1926952713.gif": {
            fallback1: "https://p1.meituan.net/csc/bf2f9049ad0c20a8a9f4f45b2b61ba0c45874.gif",
            fallback2: "https://fc.sinaimg.cn/large/007cQIj3gy1hiznuverxug303603mq3w.jpg"
        },
        "https://article.biliimg.com/bfs/article/4bc3c0729a02b786bd88ff3c6f7c7024552492916.gif": {
            fallback1: "https://p0.meituan.net/csc/4b48e90355aca3c04df7b15bb595763c30902.gif",
            fallback2: "https://fc.sinaimg.cn/large/007cQIj3gy1hiznv1rp2sg301y02k3z2.jpg"
        },
        "https://article.biliimg.com/bfs/article/ef67d7ae35289d8a97e9d30cc4b6b98d403349612.gif": {
            fallback1: "https://p0.meituan.net/csc/1cf80deaf58573f2ef93bc94aaedb5c53874.gif",
            fallback2: "https://fc.sinaimg.cn/large/007cQIj3gy1hiznvld68fg301y01yq2r.jpg"
        }
        // 这里省略了其他备用链接，完整数据从JSON文件加载
    };
}

// 主函数 - 异步加载表情数据
async function initEmojiSelector() {
    console.log('初始化表情选择器...');

    // 获取原select元素和新的按钮元素
    const faceSelect = document.querySelector('.face-select');
    const emojiButton = document.querySelector('.emoji-button');
    if (!faceSelect || !emojiButton) {
        console.log('未找到表情选择器元素');
        return;
    }

    // 先加载表情数据
    console.log('加载表情数据...');
    await loadEmojiData();

    // 创建表情容器
    const container = document.createElement('div');
    container.className = 'emoji-container';
    container.style.display = 'none';

    // 创建表情网格
    const grid = document.createElement('div');
    grid.className = 'emoji-grid';
    
    // 遍历select选项创建表情
    Array.from(faceSelect.options).forEach(option => {
        if (!option.value) return; // 跳过空选项
        
        const emojiWrapper = document.createElement('div');
        emojiWrapper.className = 'emoji-item';
        
        const emoji = document.createElement('img');
        emoji.src = emoticons[option.value];
        emoji.alt = option.text;
        emoji.title = option.text;
        emoji.setAttribute('data-value', option.value);
        emoji.setAttribute("referrerpolicy", "no-referrer");
        
        // 处理图片加载失败 - 使用从JSON加载的备用链接
        emoji.onerror = function() {
            console.log('图片加载失败:', this.src);
            const fallbacks = emoticonFallbacks[this.src];
            if (fallbacks) {
                if (fallbacks.fallback1 && !this.fallback1Failed) {
                    console.log('尝试备用链接1:', fallbacks.fallback1);
                    this.fallback1Failed = true;
                    this.src = fallbacks.fallback1;
                } else if (fallbacks.fallback2 && !this.fallback2Failed) {
                    console.log('尝试备用链接2:', fallbacks.fallback2);
                    this.fallback2Failed = true;
                    this.src = fallbacks.fallback2;
                } else {
                    console.log('所有备用链接都失败了:', option.value);
                }
            } else {
                console.log('没有找到备用链接:', this.src);
            }
        };
        
        // 点击选择表情
        emojiWrapper.addEventListener('click', () => {
            faceSelect.value = option.value;
            container.style.display = 'none';
            
            // 更新按钮文本,只显示表情名称
            const buttonText = emojiButton.querySelector('span');
            if (buttonText) {
                // 如果span已存在,直接更新文本
                buttonText.textContent = option.text.replace('.gif', '');
            } else {
                // 如果span不存在,创建新的span并替换按钮内容
                const newSpan = document.createElement('span');
                newSpan.style.marginLeft = '0';
                newSpan.textContent = option.text.replace('.gif', '');
                
                // 清空按钮现有内容
                emojiButton.innerHTML = '';
                // 添加新的span
                emojiButton.appendChild(newSpan);
            }
            
            // 触发change事件
            faceSelect.dispatchEvent(new Event('change'));
        });
        
        emojiWrapper.appendChild(emoji);
        grid.appendChild(emojiWrapper);
    });
    
    container.appendChild(grid);
    
    // 点击按钮切换显示/隐藏
    emojiButton.addEventListener('click', (e) => {
        e.preventDefault();
        container.style.display = container.style.display === 'none' ? 'block' : 'none';
    });
    
    // 点击外部关闭
    document.addEventListener('click', (e) => {
        if (!container.contains(e.target) && !emojiButton.contains(e.target)) {
            container.style.display = 'none';
        }
    });
    
    // 插入DOM
    faceSelect.parentNode.insertBefore(container, faceSelect.nextSibling);
}

// 添加样式
const style = document.createElement('style');
style.textContent = `
.emoji-container {
    position: relative;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 10px;
    margin-top: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    z-index: 1000;
    width: 100%;
    box-sizing: border-box;
}

.emoji-trigger {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background: white;
    text-align: left;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.emoji-trigger:hover {
    background: #f9fafb;
}

/* 默认布局：4行10列 */
.emoji-grid {
    display: grid;
    grid-template-columns: repeat(10, 1fr);
    gap: 8px;
    width: 100%;
    box-sizing: border-box;
}

/* 小屏幕布局：5行8列 */
@media screen and (max-width: 768px) {
    .emoji-grid {
        grid-template-columns: repeat(8, 1fr) !important;
        gap: 4px !important;
    }
    
    .emoji-item {
        padding: 2px !important;
    }
    
    .emoji-container {
        padding: 6px !important;
    }
}

.emoji-item {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 4px;
    padding: 4px;
}

.emoji-item:hover {
    background: #f3f4f6;
}

.emoji-item img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.arrow {
    font-size: 12px;
    color: #6b7280;
}
`;

document.head.appendChild(style);

// 初始化 - 支持异步加载
document.addEventListener('DOMContentLoaded', async () => {
    try {
        console.log('DOM加载完成，开始初始化表情选择器');
        await initEmojiSelector();
        console.log('表情选择器初始化完成');
    } catch (error) {
        console.error('表情选择器初始化失败:', error);
    }
});