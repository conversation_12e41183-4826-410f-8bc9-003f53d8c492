/**
 * FreeMoneyPostCaptcha.js - 派币帖验证码专用模块
 * 📅 版本：v1.2.4
 * 🔧 功能：提供派币帖回复时的验证码验证功能
 * 🛠️ 更新：修复验证码错误提示覆盖层定位问题
 */
(function() {
    'use strict';

    /**
     * 派币帖验证码管理器
     */
    window.FreeMoneyPostCaptcha = {
        // 状态管理
        isProcessing: false,
        isComponentsLoaded: false,
        loadingPromise: null,

        /**
         * 确保验证码令牌存在，如果不存在则显示验证码
         * @param {Function} callback 验证成功后的回调函数
         */
        ensureCaptchaToken: function(callback) {
            // 🔧 新增：保存回调函数以便重试时使用
            this.lastCallback = callback;

            // 检查是否需要验证码
            if (!window.REQUIRES_CAPTCHA) {
                if (typeof callback === 'function') {
                    callback();
                }
                return;
            }
            // 🔧 修复：检查是否存在卡住的状态（模态框已隐藏但isProcessing仍为true）
            var modal = document.getElementById('gocaptcha-modal-overlay');
            if (this.isProcessing && (!modal || !modal.classList.contains('show'))) {
                this.isProcessing = false;
            }

            // 防止重复处理
            if (this.isProcessing) {
                return;
            }

            // 检查是否已有有效令牌
            var tokenInput = document.getElementById('gocaptcha-token');
            if (tokenInput) {
                // 🔧 修复：不再信任现有令牌，每次都重新验证
                // 这是因为令牌使用后会失效，但前端可能仍保留旧值
                if (tokenInput.value) {
                    // 清空旧令牌
                    tokenInput.value = '';
                }
            } else {
            }

            // 需要显示验证码
            this.isProcessing = true;
            this.showCaptchaModal(callback);
        },

        /**
         * 显示验证码模态框
         * @param {Function} callback 验证成功后的回调函数
         */
        showCaptchaModal: function(callback) {
            var self = this;

            // 确保验证码组件已加载
            this.ensureComponentsLoaded().then(function() {
                // 设置验证成功回调
                window.captchaSuccessCallback = function() {
                    self.isProcessing = false;
                    if (typeof callback === 'function') {
                        callback();
                    }
                };

                // 直接使用 GoCaptcha API 创建验证码
                try {
                    self.createCaptchaModal(callback);
                } catch (error) {
                    self.isProcessing = false;
                    self.showError('验证码服务不可用，请刷新页面重试');
                }
            }).catch(function(error) {
                self.isProcessing = false;
                self.showError('验证码组件加载失败，请刷新页面重试');
            });
        },

        /**
         * 确保验证码组件已加载
         * @returns {Promise} 加载完成的Promise
         */
        ensureComponentsLoaded: function() {
            if (this.isComponentsLoaded) {
                return Promise.resolve();
            }

            if (this.loadingPromise) {
                return this.loadingPromise;
            }

            var self = this;
            this.loadingPromise = new Promise(function(resolve, reject) {
                // 检查GoCaptcha是否已加载
                if (typeof window.GoCaptcha !== 'undefined') {
                    self.isComponentsLoaded = true;
                    resolve();
                    return;
                }
                // 动态加载验证码组件
                var scriptsToLoad = [
                    '/NetCSS/CSS/Login/Gocaptcha/gocaptcha.global.js?60',
                    '/NetCSS/CSS/Login/Gocaptcha/gocaptcha-init.js?v89'
                ];

                var cssToLoad = [
                    '/NetCSS/CSS/Login/Gocaptcha/gocaptcha.global.css?v31',
                    '/NetCSS/CSS/Login/Gocaptcha/gocaptcha-modal.css?v31'
                ];
                // 加载CSS
                cssToLoad.forEach(function(href) {
                    if (!document.querySelector('link[href="' + href + '"]')) {
                        var link = document.createElement('link');
                        link.rel = 'stylesheet';
                        link.href = href;
                        document.head.appendChild(link);
                    } else {
                    }
                });
                // 加载JavaScript
                var loadedScripts = 0;
                var totalScripts = scriptsToLoad.length;

                scriptsToLoad.forEach(function(src) {
                    if (document.querySelector('script[src="' + src + '"]')) {
                        loadedScripts++;
                        if (loadedScripts === totalScripts) {
                            self.checkComponentsReady(resolve, reject);
                        }
                        return;
                    }
                    var script = document.createElement('script');
                    script.src = src;
                    script.onload = function() {
                        loadedScripts++;
                        if (loadedScripts === totalScripts) {
                            self.checkComponentsReady(resolve, reject);
                        }
                    };
                    script.onerror = function() {
                        reject(new Error('Failed to load script: ' + src));
                    };
                    document.head.appendChild(script);
                });
            });

            return this.loadingPromise;
        },

        /**
         * 检查组件是否准备就绪
         * @param {Function} resolve Promise resolve函数
         * @param {Function} reject Promise reject函数
         */
        checkComponentsReady: function(resolve, reject) {
            var self = this;
            var maxAttempts = 10; // 最多等待1秒，减少无意义的重试
            var attempts = 0;

            function checkReady() {
                attempts++;
                // 只检查 GoCaptcha 核心对象是否存在
                if (typeof window.GoCaptcha !== 'undefined') {
                    self.isComponentsLoaded = true;
                    resolve();
                } else if (attempts < maxAttempts) {
                    // 如果DOM还没加载完成，等待DOMContentLoaded事件
                    if (document.readyState !== 'complete') {
                        if (document.readyState === 'loading') {
                            document.addEventListener('DOMContentLoaded', function() {
                                setTimeout(checkReady, 100);
                            });
                            return;
                        }
                    }

                    setTimeout(checkReady, 100);
                } else {
                    reject(new Error('验证码组件加载超时'));
                }
            }

            checkReady();
        },

        /**
         * 创建验证码模态框（使用真正的GoCaptcha验证服务）
         * @param {Function} callback 验证成功后的回调函数
         */
        createCaptchaModal: function(callback) {
            var self = this;

            // 创建模态框HTML结构
            this.createModalHTML();

            // 获取容器元素
            var captchaContainer = document.getElementById('gocaptcha-modal-container');
            if (!captchaContainer) {
                throw new Error('验证码容器不存在');
            }
            // 🔧 升级：显示加载状态
            this.showLoading('正在加载验证码...');

            // 按照标准流程实现验证码
            try {
                // 创建验证码实例
                var goCaptcha = new window.GoCaptcha.Slide({
                    width: 300,
                    height: 220
                });
                // 存储当前验证码密钥（用于服务器验证）
                var currentCaptchaKey = null;

                // 设置事件处理
                goCaptcha.setEvents({
                    confirm: function(point, reset) {
                        if (!currentCaptchaKey) {
                            self.showError('验证码数据异常，请刷新重试');
                            return false;
                        }

                        // 🔧 修复：发送到真正的验证服务进行验证
                        self.verifyWithServer(point, currentCaptchaKey, callback, reset, goCaptcha, function(newCaptchaKey) {
                            currentCaptchaKey = newCaptchaKey;
                        });
                    },
                    close: function() {
                        self.hideModal();
                        self.isProcessing = false;
                    },
                    refresh: function() {
                        // 重新获取验证码数据
                        self.loadCaptchaData(goCaptcha, function(captchaKey) {
                            currentCaptchaKey = captchaKey;
                        });
                    }
                });

                // 🔧 升级：先加载验证码数据，再挂载和显示
                this.loadCaptchaData(goCaptcha, function(captchaKey) {
                    currentCaptchaKey = captchaKey;
                    // 数据加载完成后，更新模态框内容为验证码容器
                    var modal = document.getElementById('gocaptcha-modal-overlay');
                    var wrapper = modal.querySelector('.gocaptcha-modal-wrapper');
                    wrapper.innerHTML = '<div id="gocaptcha-modal-container" style="position: relative;"></div>';

                    var newContainer = wrapper.querySelector('#gocaptcha-modal-container');
                    goCaptcha.mount(newContainer);
                });

            } catch (error) {
                this.showError('验证码初始化失败，请刷新页面重试', false);
                throw error;
            }
        },

        /**
         * 加载验证码数据
         * @param {Object} goCaptcha 验证码实例
         * @param {Function} onCaptchaKeyReady 验证码密钥准备就绪的回调
         */
        loadCaptchaData: function(goCaptcha, onCaptchaKeyReady) {
            // 从服务器获取验证码数据
            fetch('/GoCaptchaProxy.ashx?path=get-data&id=slide-default', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                if (data && data.data) {
                    // 按照文档要求映射数据字段
                    var frontendData = {
                        image: data.data.master_image_base64,
                        thumb: data.data.thumb_image_base64,
                        thumbWidth: data.data.thumb_width || 60,
                        thumbHeight: data.data.thumb_height || 60,
                        thumbX: data.data.display_x || 0,  // 注意：使用 display_x
                        thumbY: data.data.display_y || 0   // 注意：使用 display_y
                    };
                    // 设置数据到验证码实例
                    goCaptcha.setData(frontendData);

                    // 🔧 新增：提取验证码密钥用于后续验证
                    var captchaKey = data.data.captcha_key || data.data.key;
                    if (typeof onCaptchaKeyReady === 'function') {
                        onCaptchaKeyReady(captchaKey);
                    }
                } else {
                    throw new Error('验证码数据格式错误');
                }
            })
            .catch(error => {
                // 使用模拟数据作为后备方案
                var mockData = {
                    image: 'data:image/svg+xml;base64,' + btoa('<svg width="300" height="220" xmlns="http://www.w3.org/2000/svg"><rect width="300" height="220" fill="#f0f0f0"/><text x="150" y="110" text-anchor="middle" fill="#666">验证码加载中...</text></svg>'),
                    thumb: 'data:image/svg+xml;base64,' + btoa('<svg width="60" height="60" xmlns="http://www.w3.org/2000/svg"><rect width="60" height="60" fill="#1991fa"/></svg>'),
                    thumbWidth: 60,
                    thumbHeight: 60,
                    thumbX: 0,
                    thumbY: 80
                };

                try {
                    goCaptcha.setData(mockData);
                    // 模拟数据时使用假的密钥
                    if (typeof onCaptchaKeyReady === 'function') {
                        onCaptchaKeyReady('mock-captcha-key');
                    }
                } catch (setDataError) {
                    // 🔧 修复：模拟数据设置失败，使用默认处理
                    if (typeof onCaptchaKeyReady === 'function') {
                        onCaptchaKeyReady('fallback-key');
                    }
                }
            });
        },

        /**
         * 🔧 新增：发送验证码坐标到服务器进行真正的验证
         * @param {Object} point 滑动坐标
         * @param {String} captchaKey 验证码密钥
         * @param {Function} callback 验证成功后的回调函数
         * @param {Function} reset 验证失败时的重置函数
         * @param {Object} goCaptcha 验证码实例（用于刷新数据）
         * @param {Function} updateCaptchaKey 更新验证码密钥的回调
         */
        verifyWithServer: function(point, captchaKey, callback, reset, goCaptcha, updateCaptchaKey) {
            var self = this;

            if (!point || !captchaKey) {
                self.showError('验证参数异常，请重试');
                return;
            }

            var pointValue = point.x + ',' + point.y;
            var verifyBody = {
                id: 'slide-default',
                captchaKey: captchaKey,
                value: pointValue
            };

            // 发送到真正的验证服务
            fetch('/GoCaptchaProxy.ashx?path=check-data', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(verifyBody)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP error! status: ' + response.status);
                }
                return response.json();
            })
            .then(verificationResult => {
                if (verificationResult && verificationResult.code === 200 && verificationResult.data === 'ok') {
                    // 验证成功，获取服务器返回的安全令牌
                    var verificationToken = verificationResult.verificationToken;

                    if (verificationToken) {
                        // 🔧 升级：显示成功状态
                        self.showSuccess('验证成功', '正在提交回复...');

                        // 设置真正的验证令牌
                        var tokenInput = document.getElementById('gocaptcha-token');
                        if (tokenInput) {
                            tokenInput.value = verificationToken;
                        }

                        // 延迟隐藏模态框，让用户看到成功状态
                        setTimeout(function() {
                            self.hideModal();
                            // 注意：hideModal() 已经会重置 isProcessing，这里不需要重复设置

                            // 执行回调
                            if (typeof callback === 'function') {
                                callback();
                            }
                        }, 1000);
                    } else {
                        self.showError('验证成功但令牌异常，请重试');
                        if (typeof reset === 'function') {
                            reset();
                        }
                    }
                } else {
                    // 🔧 升级：验证失败时显示抖动动画，动画结束后自动刷新验证码
                    self.showCaptchaError('验证失败，请重新拖动滑块', function() {
                        // 🔧 优化：在错误提示完全结束后300ms执行恢复逻辑
                        self.refreshCaptchaAndReset(goCaptcha, updateCaptchaKey, reset);
                    });
                }
            })
            .catch(error => {
                self.showError('验证服务异常，请重试');

                // 🔧 优化：使用统一的恢复逻辑
                self.refreshCaptchaAndReset(goCaptcha, updateCaptchaKey, reset);
            });
        },

        /**
         * 🔧 新增：统一的验证码恢复逻辑
         * @param {Object} goCaptcha 验证码实例
         * @param {Function} updateCaptchaKey 更新验证码密钥的回调
         * @param {Function} reset 重置函数
         */
        refreshCaptchaAndReset: function(goCaptcha, updateCaptchaKey, reset) {
            // 🔧 简化版保护：清理可能的状态残留
            if (goCaptcha && typeof goCaptcha.reset === 'function') {
                try {
                    goCaptcha.reset();
                } catch (e) {
                    // 忽略重置错误，避免影响正常流程
                }
            }

            // 刷新验证码数据
            if (goCaptcha && typeof updateCaptchaKey === 'function') {
                this.loadCaptchaData(goCaptcha, updateCaptchaKey);
            }

            // 重置验证码状态
            if (typeof reset === 'function') {
                reset();
            }
        },

        /**
         * 处理验证码验证成功
         * @param {Object} verificationResult 验证结果
         * @param {Function} callback 回调函数
         */
        handleCaptchaSuccess: function(verificationResult, callback) {
            // 设置令牌
            var tokenInput = document.getElementById('gocaptcha-token');
            if (tokenInput && verificationResult && verificationResult.verificationToken) {
                tokenInput.value = verificationResult.verificationToken;
            }

            // 隐藏模态框
            this.hideModal();

            // 重置状态
            this.isProcessing = false;

            // 执行回调
            if (typeof callback === 'function') {
                callback();
            }
        },



        /**
         * 创建模态框HTML结构
         */
        createModalHTML: function() {
            var existingModal = document.getElementById('gocaptcha-modal-overlay');

            if (existingModal) {
                // 🔧 修复：如果模态框已存在，确保其结构正确
                var wrapper = existingModal.querySelector('.gocaptcha-modal-wrapper');
                if (wrapper && !wrapper.querySelector('#gocaptcha-modal-container')) {
                    // 容器不存在，重新创建
                    wrapper.innerHTML = '<div id="gocaptcha-modal-container"></div>';
                }
                return; // 模态框已存在且结构正确
            }

            // 🔧 修复：添加基本的模态框样式
            this.addModalStyles();

            var modalHTML = `
                <div id="gocaptcha-modal-overlay" class="gocaptcha-modal-overlay">
                    <div class="gocaptcha-modal-wrapper">
                        <div id="gocaptcha-modal-container"></div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modalHTML);
            // 🔧 修复：添加点击模态框外部关闭功能
            var modal = document.getElementById('gocaptcha-modal-overlay');
            if (modal) {
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        window.FreeMoneyPostCaptcha.hideModal();
                    }
                });
            }
        },

        /**
         * 🔧 新增：计算动态缩放比例
         */
        calculateDynamicScale: function() {
            var viewportWidth = window.innerWidth;
            var viewportHeight = window.innerHeight;

            // 基础验证码容器尺寸（大约）
            var baseWidth = 350;
            var baseHeight = 280;

            // 判断设备类型
            var isMobile = viewportWidth <= 768;
            var isTablet = viewportWidth > 768 && viewportWidth <= 1024;
            var isDesktop = viewportWidth > 1024;

            var targetWidthRatio, maxScale;

            if (isMobile) {
                // 移动端：占据90%宽度
                targetWidthRatio = 0.9;
                maxScale = 2.0;
            } else if (isTablet) {
                // 平板：占据70%宽度
                targetWidthRatio = 0.7;
                maxScale = 1.5;
            } else {
                // PC端：占据50%宽度，但不超过原始尺寸的1.2倍
                targetWidthRatio = 0.5;
                maxScale = 1.2;
            }

            var targetWidth = viewportWidth * targetWidthRatio;
            var targetHeight = viewportHeight * 0.8; // 高度80%，留出更多空间

            // 计算缩放比例
            var scaleX = targetWidth / baseWidth;
            var scaleY = targetHeight / baseHeight;
            var scale = Math.min(scaleX, scaleY, maxScale);

            // 确保最小缩放比例
            scale = Math.max(scale, 0.5);
            return scale;
        },

        /**
         * 🔧 新增：应用动态缩放
         */
        applyDynamicScale: function() {
            var modal = document.getElementById('gocaptcha-modal-overlay');
            var wrapper = modal && modal.querySelector('.gocaptcha-modal-wrapper');

            if (!wrapper) return;

            var scale = this.calculateDynamicScale();

            // 当弹窗显示时，应用计算出的缩放比例
            if (modal.classList.contains('show')) {
                wrapper.style.transform = 'scale(' + scale + ')';
            } else {
                // 弹窗未显示时，保持初始缩放
                wrapper.style.transform = 'scale(' + (scale * 0.9) + ')';
            }
        },

        /**
         * 🔧 升级：使用与登录页面一致的专业样式
         */
        addModalStyles: function() {
            if (document.getElementById('freemoney-captcha-modal-styles')) {
                return; // 样式已存在
            }

            var styles = `
                <style id="freemoney-captcha-modal-styles">
                /* 与登录页面一致的专业样式 */
                .gocaptcha-modal-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.7);
                    z-index: 9999;
                    display: none;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    align-items: center;
                    justify-content: center;
                }
                .gocaptcha-modal-overlay.show {
                    display: flex !important;
                    opacity: 1;
                }
                .gocaptcha-modal-wrapper {
                    position: relative;
                    transform: scale(0.9);
                    transition: transform 0.3s ease;
                    width: auto;
                    height: auto;
                    overflow: visible;
                    transform-origin: center center;
                    margin: 0 auto;
                }
                .gocaptcha-modal-overlay.show .gocaptcha-modal-wrapper {
                    transform: scale(1);
                }

                /* 加载状态样式 */
                .gocaptcha-modal-loading {
                    background: white;
                    padding: 50px;
                    border-radius: 12px;
                    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
                    text-align: center;
                    color: #333;
                    min-width: 280px;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                }
                .gocaptcha-modal-loading-spinner {
                    width: 40px;
                    height: 40px;
                    border: 4px solid #f1f1f1;
                    border-top: 4px solid #378d8d;
                    border-radius: 50%;
                    animation: gocaptcha-spin 1s linear infinite;
                    margin: 0 auto 20px;
                }
                @keyframes gocaptcha-spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                .gocaptcha-modal-loading div:last-child {
                    font-size: 16px;
                    color: #666;
                    margin-top: 8px;
                }

                /* 成功状态样式 */
                .gocaptcha-modal-success {
                    background: white;
                    padding: 40px;
                    border-radius: 12px;
                    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
                    text-align: center;
                    color: #28a745;
                    font-size: 16px;
                    min-width: 250px;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                }
                .gocaptcha-modal-success div:first-child {
                    font-size: 24px;
                    margin-bottom: 8px;
                }

                /* 错误状态样式 */
                .gocaptcha-modal-error {
                    background: white;
                    padding: 40px;
                    border-radius: 12px;
                    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
                    text-align: center;
                    color: #dc3545;
                    font-size: 16px;
                    min-width: 300px;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                }
                .gocaptcha-modal-error-buttons {
                    margin-top: 15px;
                    text-align: center;
                }
                .gocaptcha-modal-error-buttons button {
                    margin: 5px;
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: all 0.2s ease;
                    font-family: inherit;
                    background-color: #007cba;
                    color: white;
                }
                .gocaptcha-modal-error-buttons button:hover {
                    opacity: 0.8;
                    transform: translateY(-1px);
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                }

                /* 抖动动画 */
                @keyframes gocaptcha-shake {
                    0%, 100% { transform: translateX(0); }
                    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
                    20%, 40%, 60%, 80% { transform: translateX(5px); }
                }
                .gocaptcha-shake-animation {
                    animation: gocaptcha-shake 0.6s ease-in-out;
                }

                /* 防止页面滚动 */
                body.gocaptcha-modal-open {
                    overflow: hidden;
                }

                /* 移动端适配 */
                @media (max-width: 480px) {
                    .gocaptcha-modal-loading,
                    .gocaptcha-modal-error,
                    .gocaptcha-modal-success {
                        margin: 15px;
                        padding: 25px;
                        min-width: auto;
                        max-width: calc(100vw - 30px);
                    }
                }
                </style>
            `;

            document.head.insertAdjacentHTML('beforeend', styles);
        },

        /**
         * 🔧 升级：显示加载状态
         */
        showLoading: function(message = '正在加载验证码...') {
            this.createModalHTML();
            var modal = document.getElementById('gocaptcha-modal-overlay');
            var wrapper = modal.querySelector('.gocaptcha-modal-wrapper');

            if (wrapper) {
                wrapper.innerHTML = `
                    <div class="gocaptcha-modal-loading">
                        <div class="gocaptcha-modal-loading-spinner"></div>
                        <div>${message}</div>
                    </div>
                `;
            }

            this.showModal();
        },

        /**
         * 🔧 新增：显示成功状态
         */
        showSuccess: function(message = '验证成功', subMessage = '正在提交...') {
            var modal = document.getElementById('gocaptcha-modal-overlay');
            var wrapper = modal.querySelector('.gocaptcha-modal-wrapper');

            if (wrapper) {
                wrapper.innerHTML = `
                    <div class="gocaptcha-modal-success">
                        <div>✓ ${message}</div>
                        <div>${subMessage}</div>
                    </div>
                `;
            }
        },

        /**
         * 🔧 新增：显示错误状态
         */
        showError: function(message, showRetryButton = true) {
            var modal = document.getElementById('gocaptcha-modal-overlay');
            var wrapper = modal.querySelector('.gocaptcha-modal-wrapper');

            if (wrapper) {
                var buttons = '';
                if (showRetryButton) {
                    buttons = `
                        <div class="gocaptcha-modal-error-buttons">
                            <button type="button" onclick="window.FreeMoneyPostCaptcha.retryVerification()">重新验证</button>
                        </div>
                    `;
                }

                wrapper.innerHTML = `
                    <div class="gocaptcha-modal-error">
                        <div>${message}</div>
                        ${buttons}
                    </div>
                `;
            }
        },

        /**
         * 显示模态框
         */
        showModal: function() {
            var modal = document.getElementById('gocaptcha-modal-overlay');
            if (modal) {
                document.body.classList.add('gocaptcha-modal-open');
                modal.classList.add('show');

                // 🔧 升级：应用动态缩放
                this.applyDynamicScale();

                // 🔧 新增：监听窗口大小变化
                var self = this;
                if (!this.resizeHandler) {
                    this.resizeHandler = function() {
                        self.applyDynamicScale();
                    };
                    window.addEventListener('resize', this.resizeHandler);
                    window.addEventListener('orientationchange', function() {
                        setTimeout(function() {
                            self.applyDynamicScale();
                        }, 100);
                    });
                }
                // 🔧 新增：添加ESC键关闭功能
                this.addEscapeKeyListener();
            }
        },

        /**
         * 🔧 新增：添加ESC键监听
         */
        addEscapeKeyListener: function() {
            var self = this;

            // 移除之前的监听器（如果存在）
            if (this.escapeKeyHandler) {
                document.removeEventListener('keydown', this.escapeKeyHandler);
            }

            // 添加新的监听器
            this.escapeKeyHandler = function(e) {
                if (e.key === 'Escape' || e.keyCode === 27) {
                    self.hideModal();
                }
            };

            document.addEventListener('keydown', this.escapeKeyHandler);
        },

        /**
         * 隐藏模态框
         */
        hideModal: function() {
            var modal = document.getElementById('gocaptcha-modal-overlay');
            if (modal) {
                document.body.classList.remove('gocaptcha-modal-open');
                modal.classList.remove('show');
            }

            // 🔧 修复：确保每次隐藏模态框时都重置处理状态
            this.isProcessing = false;
            // 🔧 新增：移除ESC键监听器
            if (this.escapeKeyHandler) {
                document.removeEventListener('keydown', this.escapeKeyHandler);
                this.escapeKeyHandler = null;
            }

            // 🔧 新增：移除窗口大小变化监听器
            if (this.resizeHandler) {
                window.removeEventListener('resize', this.resizeHandler);
                this.resizeHandler = null;
            }
        },



        /**
         * 重置状态
         */
        reset: function() {
            this.isProcessing = false;
            this.clearToken(); // 同时清空令牌

            // 🔧 新增：确保模态框已隐藏
            var modal = document.getElementById('gocaptcha-modal-overlay');
            if (modal && modal.classList.contains('show')) {
                this.hideModal();
            }
        },

        /**
         * 🔧 新增：清空验证码令牌
         */
        clearToken: function() {
            var tokenInput = document.getElementById('gocaptcha-token');
            if (tokenInput) {
                tokenInput.value = '';
            }
        },

        /**
         * 显示验证码错误（抖动动画 + 错误提示）
         * @param {String} message 错误消息
         * @param {Function} onComplete 动画完成后的回调函数
         */
        showCaptchaError: function(message = '验证错误，请重试', onComplete = null) {
            var modal = document.getElementById('gocaptcha-modal-overlay');
            var captchaContainer = modal.querySelector('#gocaptcha-modal-container');

            if (captchaContainer) {
                // 🔧 统一抖动目标：优先抖动.go-captcha组件，与登录页面保持一致
                var goCaptcha = captchaContainer.querySelector('.go-captcha');
                var shakeTarget = goCaptcha || captchaContainer; // 如果找不到go-captcha，回退到容器

                shakeTarget.classList.add('gocaptcha-shake-animation');

                // 🔧 新增：尝试添加错误提示文本（如果找到gc-body元素）
                var gcBody = captchaContainer.querySelector('.gc-body');
                var errorOverlay = null;

                if (gcBody) {
                    // 创建或更新错误提示覆盖层
                    errorOverlay = gcBody.querySelector('.gocaptcha-error-overlay');
                    if (!errorOverlay) {
                        errorOverlay = document.createElement('div');
                        errorOverlay.className = 'gocaptcha-error-overlay';
                        gcBody.appendChild(errorOverlay);
                    }
                    errorOverlay.textContent = message;
                    errorOverlay.classList.add('show');
                }

                // 🔧 优化：采用与登录页面一致的时间策略（1.5秒 + 300ms延迟）
                setTimeout(() => {
                    shakeTarget.classList.remove('gocaptcha-shake-animation');

                    // 同时隐藏错误提示（如果存在）
                    if (errorOverlay) {
                        errorOverlay.classList.remove('show');
                    }

                    // 🔧 新增：300ms延迟后执行回调（确保动画完全结束）
                    if (typeof onComplete === 'function') {
                        setTimeout(() => {
                            onComplete();
                        }, 300);
                    }
                }, 1500);
            }
        },

        /**
         * 🔧 新增：重试验证
         */
        retryVerification: function() {
            this.hideModal();
            this.isProcessing = false;

            // 延迟一点时间再重新显示验证码
            setTimeout(() => {
                this.ensureCaptchaToken(this.lastCallback);
            }, 300);
        },

        /**
         * 🔧 新增：强制重置所有状态（用于解决卡住的情况）
         */
        forceReset: function() {
            this.isProcessing = false;
            this.isComponentsLoaded = false;
            this.loadingPromise = null;

            // 清空令牌
            this.clearToken();

            // 隐藏并销毁模态框
            this.hideModal();

            // 🔧 新增：完全移除模态框DOM元素
            var modal = document.getElementById('gocaptcha-modal-overlay');
            if (modal) {
                modal.remove();
            }
        },


    };

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 验证码模块已加载完成
    });

    // 暴露到全局作用域
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = window.FreeMoneyPostCaptcha;
    }

})();
