﻿using KeLin.ClassManager;
using System;
using System.Linq;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class CreateUser : MyPageWap
    {
        private readonly string a = PubConstant.GetAppString("InstanceName");
        public string ConnectionString => PubConstant.GetConnectionString(a);

        public string INFO = "";
        public string ERROR = "";
        public string action = "";
        public string queryUserid = "";
        public string queryResult = "";
        public string RandomPassword { get; private set; }
        public string CreatedUserId { get; private set; }
        public string UpdatePostStatus { get; private set; }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);

            // 验证登录和管理员权限
            IsLogin(userid, "");
            IsCheckManagerLvl("|00|", "");

            // 增加空值检查
            if (siteVo == null || userVo == null)
            {
                RedirectAndStop();
                return;
            }

            // 验证站长权限
            if (siteVo.siteid != userVo.userid)
            {
                RedirectAndStop();
                return;
            }

            try
            {
                needPassWordToAdmin();
                action = Request.Form["action"] ?? Request.QueryString["action"] ?? "";

                if (action == "query")
                {
                    queryUserid = GetRequestValue("queryuserid");
                    if (!string.IsNullOrEmpty(queryUserid))
                    {
                        // ✅ 使用DapperHelper进行安全的参数化查询
                        string sql = "SELECT username,nickname,RegTime,LastLoginTime,bbsReCount,bbsCount,LoginTimes FROM [user] WHERE userid = @UserId";
                        var userInfo = DapperHelper.Query<dynamic>(ConnectionString, sql, new {
                            UserId = DapperHelper.SafeParseLong(queryUserid, "用户ID")
                        }).FirstOrDefault();

                        if (userInfo != null)
                        {
                            string username = userInfo.username?.ToString() ?? "";
                            string nickname = userInfo.nickname?.ToString() ?? "";
                            DateTime regTime = userInfo.RegTime != null ? Convert.ToDateTime(userInfo.RegTime) : DateTime.MinValue;
                            string regTimeStr = regTime != DateTime.MinValue ? regTime.ToString("yyyy/MM/dd") : "未知";
                            string lastLoginStr = userInfo.LastLoginTime != null ?
                                Convert.ToDateTime(userInfo.LastLoginTime).ToString("yyyy/MM/dd") : "从未登录";
                            int totalSeconds = userInfo.LoginTimes != null ? Convert.ToInt32(userInfo.LoginTimes) : 0;
                            string onlineTimeStr = FormatOnlineTime(totalSeconds);
                            int bbsCount = userInfo.bbsCount != null ? Convert.ToInt32(userInfo.bbsCount) : 0;
                            int bbsReCount = userInfo.bbsReCount != null ? Convert.ToInt32(userInfo.bbsReCount) : 0;

                            queryResult = string.Format(
                                "账号：{0}<br/>昵称：{1}<br/>回复数量：{2}<br/>发帖数量：{3}<br/>在线时间：{4}<br/>注册日期：{5}<br/>最后登录：{6}",
                                username,
                                nickname,
                                bbsReCount,
                                bbsCount,
                                onlineTimeStr,
                                regTimeStr,
                                lastLoginStr);
                        }
                        else
                        {
                            queryResult = "未找到该用户信息";
                        }
                    }
                }
                else if (action == "delete")
                {
                    string userid = Request.Form["userid"] ?? "";

                    if (string.IsNullOrEmpty(userid))
                    {
                        ERROR = "用户ID不能为空！";
                        return;
                    }

                    if (userid == siteVo.siteid.ToString())
                    {
                        ERROR = "不能删除站长账号！";
                        return;
                    }

                    try
                    {
                        // ✅ 使用DapperHelper进行安全的参数化删除操作
                        long userIdValue = DapperHelper.SafeParseLong(userid, "用户ID");

                        // 删除用户回复
                        string deleteReplySql = "DELETE FROM [wap_bbsre] WHERE userid = @UserId";
                        DapperHelper.Execute(ConnectionString, deleteReplySql, new { UserId = userIdValue });

                        // 删除用户主题
                        string deleteTopicSql = "DELETE FROM [wap_bbs] WHERE book_pub = @UserId";
                        DapperHelper.Execute(ConnectionString, deleteTopicSql, new { UserId = userIdValue.ToString() });

                        // 删除用户账号
                        string deleteUserSql = "DELETE FROM [user] WHERE userid = @UserId";
                        DapperHelper.Execute(ConnectionString, deleteUserSql, new { UserId = userIdValue });

                        INFO = "OK";
                        queryResult = $"<b style=\"color:red;\">用户<span style=\"padding: 2px;\">{userid}</span>删除成功</b>";
                    }
                    catch (Exception ex)
                    {
                        ERROR = "删除用户时发生错误：" + ex.Message;
                    }
                }
                else if (action == "gomod")
                {
                    string userid = GetRequestValue("userid");
                    string username = GetRequestValue("username");
                    string nickname = GetRequestValue("nickname");
                    string password = GetRequestValue("password");

                    if (string.IsNullOrEmpty(userid))
                    {
                        ERROR = "ID号不能为空！";
                        return;
                    }

                    int userIdNum;
                    if (!int.TryParse(userid, out userIdNum))
                    {
                        ERROR = "ID号必须是纯数字！";
                        return;
                    }

                    if (userid.Length < 3 || userIdNum < 300 || userid.Length > 9)
                    {
                        ERROR = "ID必须是3-9位数字，且不能小于300！";
                        return;
                    }

                    RandomPassword = password;
                    CreatedUserId = userid;
                    string qq = GetRequestValue("qq");

                    if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                    {
                        ERROR = "ID号、用户名和密码不能为空！";
                        return;
                    }

                    if (string.IsNullOrEmpty(nickname))
                    {
                        nickname = "妖友" + userid;
                    }

                    // ✅ 使用安全的方法检查用户名是否存在
                    string getuserid = CheckUserNameExists(username, siteid);
                    if (getuserid != "0")
                    {
                        ERROR = "用户名已存在！";
                        return;
                    }

                    // ✅ 使用DapperHelper进行安全的参数化插入操作
                    string insertSql;
                    object parameters;

                    if (string.IsNullOrEmpty(qq))
                    {
                        insertSql = @"SET IDENTITY_INSERT [USER] ON
                                     INSERT INTO [user] (userid,siteid,username,nickname,password,managerlvl,sex,MaxPerPage_Content,MaxPerPage_Default)
                                     VALUES (@UserId, @SiteId, @Username, @Nickname, @Password, '02', '1', 32767, 15)
                                     SET IDENTITY_INSERT [USER] OFF";
                        parameters = new {
                            UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                            SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                            Username = DapperHelper.LimitLength(username, 50),
                            Nickname = DapperHelper.LimitLength(nickname, 50),
                            Password = PubConstant.md5(password)
                        };
                    }
                    else
                    {
                        insertSql = @"SET IDENTITY_INSERT [USER] ON
                                     INSERT INTO [user] (userid,siteid,username,nickname,password,managerlvl,sex,MaxPerPage_Content,MaxPerPage_Default,MailServerUserName)
                                     VALUES (@UserId, @SiteId, @Username, @Nickname, @Password, '02', '1', 32767, 15, @QQ)
                                     SET IDENTITY_INSERT [USER] OFF";
                        parameters = new {
                            UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                            SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                            Username = DapperHelper.LimitLength(username, 50),
                            Nickname = DapperHelper.LimitLength(nickname, 50),
                            Password = PubConstant.md5(password),
                            QQ = DapperHelper.LimitLength(qq, 50)
                        };
                    }

                    DapperHelper.Execute(ConnectionString, insertSql, parameters);

                    string money = GetRequestValue("money");

                    if (!string.IsNullOrEmpty(userid))
                    {
                        int moneyValue = 0;
                        if (int.TryParse(money, out moneyValue) && moneyValue > 0)
                        {
                            // ✅ 使用DapperHelper进行安全的参数化更新操作
                            string vipSql;
                            object vipParameters;

                            if (moneyValue == 10000)
                            {
                                // 只设置妖晶，不赠送靓号勋章
                                vipSql = "UPDATE [user] SET money = @Money, moneyname = NULL WHERE userid = @UserId";
                                vipParameters = new {
                                    Money = moneyValue,
                                    UserId = DapperHelper.SafeParseLong(userid, "用户ID")
                                };
                            }
                            else
                            {
                                // 赠送靓号勋章
                                vipSql = "UPDATE [user] SET moneyname = '靓号.gif', money = @Money WHERE userid = @UserId";
                                vipParameters = new {
                                    Money = moneyValue,
                                    UserId = DapperHelper.SafeParseLong(userid, "用户ID")
                                };
                            }
                            DapperHelper.Execute(ConnectionString, vipSql, vipParameters);
                        }
                    }

                    if (!string.IsNullOrEmpty(userid))
                    {
                        try
                        {
                            // ✅ 使用DapperHelper进行安全的参数化查询和更新操作
                            const int POST_ID = 138352;

                            // 先尝试删除"ID号，"的格式
                            string pattern1 = userid + "，";
                            int affected = DapperHelper.Execute(ConnectionString,
                                "UPDATE wap_bbs SET book_content = REPLACE(book_content, @Pattern, '') WHERE id = @PostId",
                                new { Pattern = pattern1, PostId = POST_ID });

                            // 检查是否还需要进一步清理
                            string contentAfterFirstUpdate = "";
                            if (affected == 0)
                            {
                                // 第一次替换失败，可能是最后一个ID，尝试删除"，ID号"的格式
                                string pattern2 = "，" + userid;
                                DapperHelper.Execute(ConnectionString,
                                    "UPDATE wap_bbs SET book_content = REPLACE(book_content, @Pattern, '') WHERE id = @PostId",
                                    new { Pattern = pattern2, PostId = POST_ID });
                            }
                            else
                            {
                                // 第一次替换成功，但检查是否还存在该ID（防御性编程）
                                contentAfterFirstUpdate = DapperHelper.ExecuteScalar<string>(ConnectionString,
                                    "SELECT book_content FROM wap_bbs WHERE id = @PostId",
                                    new { PostId = POST_ID }) ?? "";

                                if (contentAfterFirstUpdate.Contains(userid))
                                {
                                    // 仍然存在，进行第二次清理
                                    string pattern2 = "，" + userid;
                                    DapperHelper.Execute(ConnectionString,
                                        "UPDATE wap_bbs SET book_content = REPLACE(book_content, @Pattern, '') WHERE id = @PostId",
                                        new { Pattern = pattern2, PostId = POST_ID });
                                }
                            }

                            UpdatePostStatus = "成功从出售帖中删除ID号";
                        }
                        catch (Exception ex)
                        {
                            UpdatePostStatus = "帖子更新失败：" + ex.Message;
                        }
                    }

                    INFO = "OK";
                }
            }
            catch (Exception ex)
            {
                ERROR = ex.Message;
            }
        }

        private string FormatOnlineTime(int totalSeconds)
        {
            TimeSpan ts = TimeSpan.FromSeconds(totalSeconds);

            int years = (int)(ts.TotalDays / 365);
            int months = (int)((ts.TotalDays % 365) / 30);
            int days = (int)(ts.TotalDays % 30);
            int hours = ts.Hours;

            if (years > 0)
            {
                return string.Format("{0}年{1}月", years, months);
            }
            else if (months > 0)
            {
                return string.Format("{0}月{1}天", months, days);
            }
            else if (days > 0)
            {
                return string.Format("{0}天{1}小时", days, hours);
            }
            else
            {
                return string.Format("{0}小时", hours);
            }
        }

        private void RedirectAndStop()
        {
            Response.Clear();
            Response.Redirect("/", false);
            Context.ApplicationInstance.CompleteRequest();
        }

        /// <summary>
        /// 安全检查用户名是否已存在
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="siteid">站点ID</param>
        /// <returns>存在则返回用户ID，不存在返回"0"</returns>
        private string CheckUserNameExists(string username, string siteid)
        {
            try
            {
                string connectionString = PubConstant.GetConnectionString(a);
                string checkSql = "SELECT userid FROM [user] WHERE username = @Username AND siteid = @SiteId";

                var existingUserId = DapperHelper.Query<long?>(connectionString, checkSql, new {
                    Username = DapperHelper.LimitLength(username, 50), // username字段长度限制
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID")
                }).FirstOrDefault();

                return existingUserId?.ToString() ?? "0"; // 保持原有返回值格式
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查用户名失败: {ex.Message}");
                return "0"; // 发生错误时返回不存在，安全起见
            }
        }
    }
}