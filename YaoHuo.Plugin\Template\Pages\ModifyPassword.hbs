{{#if Message.HasMessage}}
{{#unless Message.IsSuccess}}
<div class="message {{Message.Type}}">
    {{Message.Content}}
</div>
{{/unless}}
{{/if}}

{{#if Message.IsSuccess}}
<!-- 成功状态卡片 -->
<div class="card text-center border border-border-light">
    <div class="bg-white text-text-primary py-8 px-4 pb-6 border-b border-border-light">
        <div class="mb-4">
            <i data-lucide="check-circle" class="w-16 h-16 stroke-2 text-primary mx-auto"></i>
        </div>
        <h3 class="text-xl font-semibold m-0 text-text-primary">密码修改成功</h3>
    </div>
    <div class="py-6 px-4 pb-8 bg-white">
        <p class="text-text-secondary mb-4 leading-6">您的密码已成功修改，请使用新密码重新登录。</p>
        <p class="text-text-secondary text-sm mb-6">
            <span id="countdown-text">10秒后自动返回登录</span>
        </p>
        <div class="flex justify-center items-center">
            <a href="/waplogin.aspx" class="btn btn-primary py-3 px-6 text-base">
                <i data-lucide="log-in" class="mr-2"></i>
                重新登录
            </a>
        </div>
    </div>
    <div class="bg-bg-gray-50 border-t border-border-light p-4 text-center">
        <p class="flex items-center justify-center text-text-light text-sm m-0 gap-2">
            <i data-lucide="alert-triangle" class="w-4 h-4"></i>
            请勿在公共设备保存密码
        </p>
    </div>
</div>
{{else}}
<!-- 修改密码表单 -->
<form id="password-form" action="{{FormData.ActionUrl}}" method="post">
    <div class="card mt-4">
        <div class="card-header">
            <div class="card-title">
                <i data-lucide="shield" class="card-icon"></i>
                修改密码
            </div>
        </div>
        
        <div class="card-body">
            <div class="form-group">
                <label class="form-label required">原密码</label>
                <input type="password" 
                       name="txtoldPW" 
                       id="old-password"
                       class="form-input" 
                       maxlength="50" 
                       placeholder="请输入原密码"
                       autocomplete="current-password"
                       required>
            </div>
            
            <div class="form-group">
                <label class="form-label required">新密码</label>
                <input type="password" 
                       name="txtnewPW" 
                       id="new-password"
                       class="form-input" 
                       maxlength="50" 
                       placeholder="请输入新密码"
                       autocomplete="new-password"
                       required>
            </div>
            
            <div class="form-group">
                <label class="form-label required">确认新密码</label>
                <input type="password" 
                       name="txtrePW" 
                       id="confirm-password"
                       class="form-input" 
                       maxlength="50" 
                       placeholder="请再次输入新密码"
                       autocomplete="new-password"
                       required>
            </div>
            
            <div class="bg-bg-gray-50 rounded p-4 mb-4">
                <h4 class="text-sm font-medium text-text-secondary mb-3">新密码要求</h4>
                <ul class="list-none p-0 m-0">
                    <li class="flex items-center py-1 text-sm text-text-light transition-colors" id="rule-length">
                        <i data-lucide="check" class="w-4 h-4 mr-2 text-text-light transition-colors opacity-30"></i>
                        <span>至少6个字符</span>
                    </li>
                    <li class="flex items-center py-1 text-sm text-text-light transition-colors" id="rule-uppercase">
                        <i data-lucide="check" class="w-4 h-4 mr-2 text-text-light transition-colors opacity-30"></i>
                        <span>包含大写字母</span>
                    </li>
                    <li class="flex items-center py-1 text-sm text-text-light transition-colors" id="rule-lowercase">
                        <i data-lucide="check" class="w-4 h-4 mr-2 text-text-light transition-colors opacity-30"></i>
                        <span>包含小写字母</span>
                    </li>
                    <li class="flex items-center py-1 text-sm text-text-light transition-colors" id="rule-number">
                        <i data-lucide="check" class="w-4 h-4 mr-2 text-text-light transition-colors opacity-30"></i>
                        <span>包含数字</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 隐藏字段 -->
    <input type="hidden" name="action" value="{{FormData.HiddenFields.Action}}">
    <input type="hidden" name="siteid" value="{{FormData.HiddenFields.SiteId}}">
    <input type="hidden" name="classid" value="{{FormData.HiddenFields.ClassId}}">

    <!-- 提交按钮 -->
    <div class="flex justify-center mt-4 px-4">
        <button type="submit" class="btn btn-primary w-full py-3 px-6 text-base" id="submit-btn">
            <i data-lucide="shield-check" class="w-5 h-5"></i>
            修改密码
        </button>
    </div>
</form>
{{/if}}

<!-- 引入TypeScript编译后的ModifyPassword模块 -->
<script type="module" src="/Template/TS/pages/ModifyPassword.js?v=1751889760497"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化图标
        lucide.createIcons();
    });
</script>