﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.BBS.Models;

namespace YaoHuo.Plugin.BBS
{
	public class book_list_log : MyPageWap
	{
		private string string_10 = PubConstant.GetAppString("InstanceName");

		public string action = "";

		public string linkURL = "";

		// ❌ 已删除 condition 变量 - 不再使用SQL字符串拼接

		public string ERROR = "";

		public List<wap_log_Model> listVo = null;

		public StringBuilder strhtml = new StringBuilder();

		public sys_ad_show_Model adVo = new sys_ad_show_Model();

		public long kk = 1L;

		public long index = 0L;

		public long total = 0L;

		public long pageSize = 10L;

		public long CurrentPage = 1L;

		public string touserid = "";

		public string backurl = "";

		public string whos = "TA";

		public user_Model toUserVo = null;

		protected void Page_Load(object sender, EventArgs e)
		{
			// 检查UI偏好，如果是新版UI则渲染Handlebars模板
			if (CheckAndHandleUIPreference())
			{
				return; // 新版UI已渲染，停止执行旧版代码
			}

			action = GetRequestValue("action");
			touserid = GetRequestValue("touserid");
			if (!WapTool.IsNumeric(touserid))
			{
				touserid = "0";
			}
			backurl = "bbs/userinfo.aspx?siteid=" + siteid + "&amp;touserid=" + touserid;
			backurl = ToHtm(backurl);
			backurl = HttpUtility.UrlDecode(backurl);
			backurl = WapTool.URLtoWAP(backurl);
			IsLogin(userid, backurl);
			if (touserid == userid)
			{
				whos = "我的";
			}

			// 获取目标用户信息（旧版ASPX页面需要）
			user_BLL user_BLL = new user_BLL(string_10);
			toUserVo = user_BLL.getUserInfo(touserid, siteid);
			if (toUserVo == null)
			{
				ShowTipInfo("无资料记录，当前您查看的是匿名或游客！", backurl);
			}

			// ✅ 使用QueryBuilder和PaginationHelper进行安全的分页查询
			try
			{
				if (classVo.ismodel < 1L)
				{
					pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
				}
				else
				{
					pageSize = Convert.ToInt32(classVo.ismodel);
				}

				string connectionString = PubConstant.GetConnectionString(string_10);

				// 处理分页参数
				if (GetRequestValue("page") != "")
				{
					CurrentPage = int.Parse(GetRequestValue("page"));
				}

				// ✅ 根据action类型选择查询方式
				PagedResult<wap_log_Model> result;

				if (action == "friends")
				{
					if (touserid != userid)
					{
						ShowTipInfo("当前为TA（ID:" + touserid + "）的空间，请进入<a href=\"" + http_start + "bbs/userinfo.aspx?siteid=" + siteid + "&amp;touserid=" + userid + "\">我的空间</a>查看。", backurl);
					}
					// 好友动态：使用传统方式处理复杂子查询
					string friendsQuery = @"SELECT * FROM wap_log
											WHERE siteid=@SiteId AND oper_type=1
											AND oper_userid IN (SELECT frienduserid FROM wap_friends
																WHERE siteid=@SiteId AND friendtype=0 AND userid=@ToUserId)";
					var friendsParams = new {
						SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
						ToUserId = DapperHelper.SafeParseLong(touserid, "用户ID")
					};
					result = PaginationHelper.GetPagedData<wap_log_Model>(
						connectionString,
						friendsQuery,
						friendsParams,
						(int)CurrentPage,
						(int)pageSize,
						"ORDER BY id DESC"
					);
				}
				else
				{
					// 我的动态：使用QueryBuilder处理简单查询
					var queryBuilder = new QueryBuilder()
						.Where("siteid = @ParamN", DapperHelper.SafeParseLong(siteid, "站点ID"))
						.Where("oper_type = @ParamN", 1)
						.Where("oper_userid = @ParamN", DapperHelper.SafeParseLong(touserid, "用户ID"));

					result = PaginationHelper.GetPagedDataWithBuilder<wap_log_Model>(
						connectionString,
						"SELECT *",
						"wap_log",
						queryBuilder,
						(int)CurrentPage,
						(int)pageSize,
						"ORDER BY id DESC"
					);
				}

				// 设置结果
				listVo = result.Data;
				total = result.Total;
				CurrentPage = result.Page;
				index = pageSize * (CurrentPage - 1L);

				// 构建分页链接
				linkURL = http_start + "bbs/book_list_log.aspx?action=" + action + "&amp;touserid=" + touserid + "&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;getTotal=" + total;
				linkURL = WapTool.GetPageLink(ver, lang, total, pageSize, CurrentPage, linkURL);
			}
			catch (Exception ex)
			{
				ERROR = WapTool.ErrorToString(ex.ToString());
			}
		}



		/// <summary>
		/// 检查并处理UI偏好
		/// </summary>
		/// <returns>如果使用新版UI并成功渲染则返回true，否则返回false</returns>
		private bool CheckAndHandleUIPreference()
		{
			string uiPreference = Request.Cookies["ui_preference"]?.Value ?? "old";

			if (uiPreference == "new")
			{
				try
				{
					RenderWithHandlebars();
					return true;
				}
				catch (System.Threading.ThreadAbortException)
				{
					return true; // 成功渲染
				}
				catch (Exception ex)
				{
					ERROR = "新版模板加载失败: " + ex.Message;
					return false;
				}
			}
			return false;
		}

		/// <summary>
		/// 使用Handlebars渲染页面
		/// </summary>
		private void RenderWithHandlebars()
		{
			try
			{
				// 先执行原有的数据获取逻辑
				ExecuteDataRetrieval();

				// 构建页面数据模型
				var pageModel = BuildBookListLogPageModel();

				// 构建HeaderOptions
				var headerOptions = BuildHeaderOptions(pageModel);

				// 渲染页面
				string finalHtml = TemplateService.RenderPageWithLayout(
					"~/Template/Pages/BookListLog.hbs",
					pageModel,
					pageModel.PageTitle,
					headerOptions
				);

				// 输出渲染结果
				Response.Clear();
				Response.ContentType = "text/html; charset=utf-8";
				Response.Write(finalHtml);
				Response.End();
			}
			catch (System.Threading.ThreadAbortException)
			{
				throw; // Response.End()的正常行为
			}
			catch (Exception ex)
			{
				// 错误处理
				Response.Clear();
				Response.ContentType = "text/html; charset=utf-8";
				Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}</div>");
				HttpContext.Current.ApplicationInstance.CompleteRequest();
			}
		}

		/// <summary>
		/// 执行数据获取逻辑（从原Page_Load方法提取）
		/// </summary>
		private void ExecuteDataRetrieval()
		{
			action = GetRequestValue("action");
			touserid = GetRequestValue("touserid");
			if (!WapTool.IsNumeric(touserid))
			{
				touserid = "0";
			}
			backurl = "bbs/userinfo.aspx?siteid=" + siteid + "&amp;touserid=" + touserid;
			backurl = ToHtm(backurl);
			backurl = HttpUtility.UrlDecode(backurl);
			backurl = WapTool.URLtoWAP(backurl);
			IsLogin(userid, backurl);
			if (touserid == userid)
			{
				whos = "我的";
			}

			// 获取目标用户信息
			user_BLL user_BLL = new user_BLL(string_10);
			toUserVo = user_BLL.getUserInfo(touserid, siteid);
			if (toUserVo == null)
			{
				ShowTipInfo("无资料记录，当前您查看的是匿名或游客！", backurl);
			}
			// ✅ 完全修复SQL注入：直接使用DapperHelper进行参数化查询
			string countSql;
			string listSql;
			object parameters;

			if (action == "friends")
			{
				if (touserid != userid)
				{
					ShowTipInfo("当前为TA（ID:" + touserid + "）的空间，请进入<a href=\"" + http_start + "bbs/userinfo.aspx?siteid=" + siteid + "&amp;touserid=" + userid + "\">我的空间</a>查看。", backurl);
				}
				countSql = @"SELECT COUNT(*) FROM wap_log
							WHERE siteid=@SiteId AND oper_type=1
							AND oper_userid IN (SELECT frienduserid FROM wap_friends
												WHERE siteid=@SiteId AND friendtype=0 AND userid=@ToUserId)";
				listSql = @"SELECT * FROM wap_log
							WHERE siteid=@SiteId AND oper_type=1
							AND oper_userid IN (SELECT frienduserid FROM wap_friends
												WHERE siteid=@SiteId AND friendtype=0 AND userid=@ToUserId)
							ORDER BY id DESC
							OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";
				parameters = new {
					SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
					ToUserId = DapperHelper.SafeParseLong(touserid, "用户ID")
				};
			}
			else
			{
				action = "my";
				countSql = "SELECT COUNT(*) FROM wap_log WHERE siteid=@SiteId AND oper_type=1 AND oper_userid=@ToUserId";
				listSql = @"SELECT * FROM wap_log
							WHERE siteid=@SiteId AND oper_type=1 AND oper_userid=@ToUserId
							ORDER BY id DESC
							OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";
				parameters = new {
					SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
					ToUserId = DapperHelper.SafeParseLong(touserid, "用户ID")
				};
			}

			try
			{
				if (classVo.ismodel < 1L)
				{
					pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
				}
				else
				{
					pageSize = Convert.ToInt32(classVo.ismodel);
				}

				string connectionString = PubConstant.GetConnectionString(string_10);

				if (GetRequestValue("getTotal") != "")
				{
					total = long.Parse(GetRequestValue("getTotal"));
				}
				else
				{
					// ✅ 使用DapperHelper进行安全的参数化查询
					total = DapperHelper.ExecuteScalar<long>(connectionString, countSql, parameters);
				}
				if (GetRequestValue("page") != "")
				{
					CurrentPage = int.Parse(GetRequestValue("page"));
				}
				CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
				index = pageSize * (CurrentPage - 1L);
				linkURL = http_start + "bbs/book_list_log.aspx?action=" + action + "&amp;touserid=" + touserid + "&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;getTotal=" + total;
				linkURL = WapTool.GetPageLink(ver, lang, total, pageSize, CurrentPage, linkURL);

				// ✅ 使用DapperHelper进行安全的分页查询
				var listParameters = new {
					SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
					ToUserId = DapperHelper.SafeParseLong(touserid, "用户ID"),
					Offset = (CurrentPage - 1) * pageSize,
					PageSize = pageSize
				};
				var logList = DapperHelper.Query<wap_log_Model>(connectionString, listSql, listParameters);
				listVo = logList?.ToList() ?? new List<wap_log_Model>();
			}
			catch (Exception ex)
			{
				ERROR = WapTool.ErrorToString(ex.ToString());
			}
		}

		/// <summary>
		/// 构建BookListLog页面数据模型
		/// </summary>
		/// <returns></returns>
		private BookListLogPageModel BuildBookListLogPageModel()
		{
			var model = new BookListLogPageModel();

			// 设置页面标题
			string userNickname = toUserVo?.nickname ?? "用户";
			if (action == "friends")
			{
				model.PageTitle = "我的好友动态";
			}
			else
			{
				model.PageTitle = userNickname + "的动态";
			}

			// 设置基本属性
			model.ActionType = action;
			model.TargetUserId = touserid;
			model.WhoDisplay = whos;

			// 设置性别显示
			if (toUserVo != null)
			{
				model.GenderDisplay = toUserVo.sex == 1 ? "他的" : "她的";
			}
			else
			{
				model.GenderDisplay = "他的"; // 默认值
			}

			// 设置消息状态
			if (!string.IsNullOrEmpty(ERROR))
			{
				model.Message.SetError(ERROR);
			}

			// 设置站点信息
			model.SiteInfo.SiteId = siteid;
			model.SiteInfo.ClassId = classid;
			model.SiteInfo.HttpStart = http_start;
			model.SiteInfo.BackUrl = backurl;

			// 设置隐藏字段
			model.HiddenFields.SiteId = siteid;
			model.HiddenFields.ClassId = classid;
			model.HiddenFields.BackUrl = backurl;
			model.HiddenFields.Sid = sid;

			// 构建动态列表
			if (listVo != null && listVo.Count > 0)
			{
				foreach (var logItem in listVo)
				{
					var dynamicItem = new DynamicItemModel
					{
						LogInfo = ProcessLogInfo(logItem.log_info),
						FriendlyTime = WapTool.DateToString(logItem.oper_time, lang, 1) + "前",
						DetailTime = logItem.oper_time.ToString("yyyy-MM-dd HH:mm:ss"),
						ShowAuthor = action == "friends",
						AuthorNickname = action == "friends" ? logItem.oper_nickname : "",
						AuthorUserId = action == "friends" ? logItem.oper_userid.ToString() : "",
						AuthorSpaceUrl = action == "friends" ?
							http_start + "bbs/userinfo.aspx?siteid=" + siteid + "&touserid=" + logItem.oper_userid : ""
					};



					model.DynamicsList.Add(dynamicItem);
				}
			}

			// 构建分页信息
			BuildPaginationModel(model);

			// 构建导航信息
			BuildNavigationModel(model);

			return model;
		}

		/// <summary>
		/// 处理日志信息，替换特殊标记
		/// </summary>
		/// <param name="logInfo"></param>
		/// <returns></returns>
		private string ProcessLogInfo(string logInfo)
		{
			if (string.IsNullOrEmpty(logInfo))
				return "";

			// 替换[sid]标记
			string processedInfo = logInfo.Replace("[sid]", sid);

			// 注意：数据库中的log_info通常已经是HTML格式，不需要再次UBB转换
			// 旧版代码是在整个页面HTML上调用ToWML，而不是在单个log_info上
			// 如果需要UBB转换，应该在记录动态时进行，而不是在显示时进行

			return processedInfo;
		}



		/// <summary>
		/// 构建分页模型
		/// </summary>
		/// <param name="model"></param>
		private void BuildPaginationModel(BookListLogPageModel model)
		{
			if (total > pageSize)
			{
				model.Pagination.ShowPagination = true;
				model.Pagination.CurrentPage = (int)CurrentPage;
				model.Pagination.TotalPages = (int)Math.Ceiling((double)total / pageSize);
				model.Pagination.TotalItems = (int)total;
				model.Pagination.PageSize = (int)pageSize;
				model.Pagination.IsFirstPage = CurrentPage <= 1;
				model.Pagination.IsLastPage = CurrentPage >= model.Pagination.TotalPages;
			}
			else
			{
				model.Pagination.ShowPagination = false;
			}
		}

		/// <summary>
		/// 构建导航模型
		/// </summary>
		/// <param name="model"></param>
		private void BuildNavigationModel(BookListLogPageModel model)
		{
			// 设置导航链接
			// 我的动态：指向目标用户的动态
			model.Navigation.MyDynamicsUrl = http_start + "bbs/book_list_log.aspx?action=my&touserid=" + touserid;
			// 好友动态：指向当前登录用户的好友动态
			model.Navigation.FriendsDynamicsUrl = http_start + "bbs/book_list_log.aspx?action=friends&touserid=" + userid;

			// 设置当前页面状态
			model.Navigation.IsMyDynamics = action == "my";
			model.Navigation.IsFriendsDynamics = action == "friends";
		}

		/// <summary>
		/// 构建HeaderOptions
		/// </summary>
		/// <param name="model"></param>
		/// <returns></returns>
		private HeaderOptionsModel BuildHeaderOptions(BookListLogPageModel model)
		{
			var headerOptions = new HeaderOptionsModel
			{
				ShowViewModeToggle = false
			};

			// 添加动态类型切换下拉菜单
			headerOptions.CustomButtons.Add(new HeaderButtonModel
			{
				Id = "dynamics-menu",
				Icon = "list",
				Tooltip = "动态类型",
				HasDropdown = true,
				DropdownItems = new List<HeaderDropdownItemModel>
				{
					new HeaderDropdownItemModel
					{
						Text = touserid == userid ? model.WhoDisplay + "动态" : model.GenderDisplay + "动态",
						Icon = "user",
						Link = model.Navigation.MyDynamicsUrl,
						IsActive = model.Navigation.IsMyDynamics
					},
					new HeaderDropdownItemModel
					{
						Text = "好友动态",
						Icon = "users",
						Link = model.Navigation.FriendsDynamicsUrl,
						IsActive = model.Navigation.IsFriendsDynamics
					}
				}
			});

			return headerOptions;
		}

		// ❌ 已删除 BuildSafeConditionForFriends 和 BuildSafeConditionForMy 方法
		// 原因：返回SQL字符串片段违反了参数化查询原则，仍存在SQL注入风险
		// 正确做法：在调用处直接编写完整SQL，将参数作为对象传递给Dapper
	}
}