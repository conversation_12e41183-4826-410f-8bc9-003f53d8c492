﻿using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Book_re_addfileShow : MyPageWap
    {
        private readonly string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string reid = "";

        public string page = "";

        public string lpage = "";

        public string ot = "";

        public string INFO = "";

        public string ERROR = "";

        public wap_bbsre_Model bbsReVo = null;

        public List<wap2_attachment_Model> dlist = new List<wap2_attachment_Model>();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
            }
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            reid = GetRequestValue("reid");
            page = GetRequestValue("page");
            lpage = GetRequestValue("lpage");
            ot = GetRequestValue("ot");
            // ✅ 使用DapperHelper安全获取回帖信息，避免SQL注入
            bbsReVo = GetBbsReplyInfoSafely(reid);

            // ✅ 使用DapperHelper安全获取附件列表，避免SQL注入
            dlist = GetAttachmentListSafely(reid);
        }

        /// <summary>
        /// 使用DapperHelper安全获取回帖信息，避免SQL注入
        /// </summary>
        /// <param name="replyId">回帖ID</param>
        /// <returns>回帖信息</returns>
        private wap_bbsre_Model GetBbsReplyInfoSafely(string replyId)
        {
            string fileConnectionString = PubConstant.GetConnectionString(string_10);
            string sql = "SELECT * FROM wap_bbsre WHERE id = @ReplyId";

            var result = DapperHelper.Query<wap_bbsre_Model>(fileConnectionString, sql, new {
                ReplyId = DapperHelper.SafeParseLong(replyId, "回帖ID")
            });

            return result.FirstOrDefault();
        }

        /// <summary>
        /// 使用DapperHelper安全获取附件列表，避免SQL注入
        /// </summary>
        /// <param name="replyId">回帖ID</param>
        /// <returns>附件列表</returns>
        private List<wap2_attachment_Model> GetAttachmentListSafely(string replyId)
        {
            string fileConnectionString = PubConstant.GetConnectionString(string_10);
            string sql = "SELECT * FROM wap2_attachment WHERE book_type = @BookType AND book_id = @BookId";

            var result = DapperHelper.Query<wap2_attachment_Model>(fileConnectionString, sql, new {
                BookType = "bbsre",
                BookId = DapperHelper.SafeParseLong(replyId, "回帖ID")
            });

            return result.ToList();
        }
    }
}