using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Caching;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.WebSite.Services.Config;
using KeLin.ClassManager;   

namespace YaoHuo.Plugin.BBS
{
    /// <summary>
    /// 用户基本信息缓存模型（只包含Book_List.aspx.cs必要字段）
    /// </summary>
    public class UserBasicInfo
    {
        /// <summary>
        /// 用户ID（主键）
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        /// 用户昵称
        /// </summary>
        public string Nickname { get; set; }

        /// <summary>
        /// VIP到期时间
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 会员等级
        /// </summary>
        public long SessionTimeout { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        public DateTime LastLoginTime { get; set; }

        /// <summary>
        /// 头像路径
        /// </summary>
        public string HeadImg { get; set; }

        /// <summary>
        /// 智能生成的idname（根据SessionTimeout生成）
        /// </summary>
        public string IdName { get; set; }
    }

    /// <summary>
    /// 用户信息缓存服务
    /// </summary>
    public static class UserInfoCacheService
    {
        private static readonly MemoryCache _userCache = MemoryCache.Default;
        private static readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(10); // 10分钟过期
        private static readonly object _lockObject = new object();
        
        // 缓存统计
        private static long _cacheHits = 0;
        private static long _cacheMisses = 0;
        
        /// <summary>
        /// 批量获取用户基本信息（带缓存）
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <returns>用户基本信息列表</returns>
        public static List<UserBasicInfo> GetUserBasicInfoBatch(List<long> userIds, string connectionString)
        {
            if (userIds == null || userIds.Count == 0)
                return new List<UserBasicInfo>();

            var result = new List<UserBasicInfo>();
            var uncachedIds = new List<long>();
            
            // 1. 先从缓存获取
            foreach (var userId in userIds.Distinct()) // 去重
            {
                string cacheKey = $"user_basic_{userId}";
                if (_userCache.Get(cacheKey) is UserBasicInfo cachedUser)
                {
                    result.Add(cachedUser);
                    System.Threading.Interlocked.Increment(ref _cacheHits);
                }
                else
                {
                    uncachedIds.Add(userId);
                    System.Threading.Interlocked.Increment(ref _cacheMisses);
                }
            }
            
            // 2. 查询未缓存的用户
            if (uncachedIds.Count > 0)
            {
                var freshUsers = QueryUsersFromDatabase(uncachedIds, connectionString);
                
                // 3. 存入缓存并添加到结果
                lock (_lockObject)
                {
                    foreach (var user in freshUsers)
                    {
                        string cacheKey = $"user_basic_{user.UserId}";
                        var policy = new CacheItemPolicy
                        {
                            AbsoluteExpiration = DateTimeOffset.Now.Add(_cacheExpiry)
                        };
                        _userCache.Set(cacheKey, user, policy);
                        result.Add(user);
                    }
                }
            }

            return result;
        }
        
        /// <summary>
        /// 从数据库查询用户信息（只查询必要字段）
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <returns>用户基本信息列表</returns>
        private static List<UserBasicInfo> QueryUsersFromDatabase(List<long> userIds, string connectionString)
        {
            try
            {
                if (userIds.Count == 0)
                    return new List<UserBasicInfo>();

                // ✅ 只查询必要字段，减少90%的数据传输
                string sql = @"SELECT userid, nickname, endTime, SessionTimeout, LastLoginTime, headimg
                              FROM [user]
                              WHERE userid IN @UserIds";
                              
                var users = DapperHelper.Query<dynamic>(connectionString, sql, new { UserIds = userIds });
                
                return users.Select(u => new UserBasicInfo
                {
                    UserId = u.userid,
                    Nickname = u.nickname ?? "未知用户",
                    EndTime = u.endTime ?? DateTime.MinValue,
                    SessionTimeout = u.SessionTimeout ?? 0,
                    LastLoginTime = u.LastLoginTime ?? DateTime.MinValue,
                    HeadImg = u.headimg ?? "",
                    IdName = GenerateIdName(u.SessionTimeout ?? 0, u.endTime ?? DateTime.MinValue)
                }).ToList();
            }
            catch (Exception)
            {
                return new List<UserBasicInfo>();
            }
        }
        
        /// <summary>
        /// 清除指定用户的缓存（用户信息更新时调用）
        /// </summary>
        /// <param name="userId">用户ID</param>
        public static void ClearUserCache(long userId)
        {
            string cacheKey = $"user_basic_{userId}";
            _userCache.Remove(cacheKey);
        }
        
        /// <summary>
        /// 清除所有用户缓存（管理员功能）
        /// </summary>
        public static void ClearAllUserCache()
        {
            lock (_lockObject)
            {
                var cacheKeys = new List<string>();
                foreach (var item in _userCache)
                {
                    if (item.Key.StartsWith("user_basic_"))
                    {
                        cacheKeys.Add(item.Key);
                    }
                }
                
                foreach (var key in cacheKeys)
                {
                    _userCache.Remove(key);
                }
            }
        }
        
        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        /// <returns>缓存统计信息</returns>
        public static string GetCacheStats()
        {
            long hits = System.Threading.Interlocked.Read(ref _cacheHits);
            long misses = System.Threading.Interlocked.Read(ref _cacheMisses);
            long total = hits + misses;
            double hitRate = total > 0 ? (double)hits / total * 100 : 0;
            
            long cacheCount = 0;
            foreach (var item in _userCache)
            {
                if (item.Key.StartsWith("user_basic_"))
                    cacheCount++;
            }
            
            return $"用户信息缓存统计: 命中率 {hitRate:F1}% ({hits}/{total}), 当前缓存条目数 {cacheCount}";
        }
        
        /// <summary>
        /// 重置缓存统计
        /// </summary>
        public static void ResetCacheStats()
        {
            System.Threading.Interlocked.Exchange(ref _cacheHits, 0);
            System.Threading.Interlocked.Exchange(ref _cacheMisses, 0);
        }

        /// <summary>
        /// 根据SessionTimeout智能生成idname（混合方案：JSON配置 + 数据库降级）
        /// </summary>
        /// <param name="sessionTimeout">会员等级</param>
        /// <param name="endTime">VIP到期时间</param>
        /// <returns>idname字符串（格式：图标路径#颜色代码）</returns>
        private static string GenerateIdName(long sessionTimeout, DateTime endTime)
        {
            try
            {
                // 检查VIP是否过期
                bool isVipExpired = endTime != DateTime.MinValue && endTime < DateTime.Now;

                // 方案1：优先从JSON配置获取（新版身份）
                var jsonConfig = BBSConfigService.GetIdentityOption((int)sessionTimeout);
                if (jsonConfig != null && !string.IsNullOrEmpty(jsonConfig.ColorCode))
                {
                    return GenerateFromJsonConfig(jsonConfig, isVipExpired);
                }

                // 方案2：检查是否为彩色昵称（特殊处理）
                var colorNicknameConfig = GetColorNicknameConfig((int)sessionTimeout);
                if (colorNicknameConfig != null)
                {
                    return $"#{colorNicknameConfig}";
                }

                // 方案3：降级到数据库查询（历史身份）
                string connectionString = GetConnectionString();
                if (!string.IsNullOrEmpty(connectionString))
                {
                    var dbConfig = DatabaseIdentityService.GetIdentityConfig(sessionTimeout, connectionString);
                    if (dbConfig != null && !string.IsNullOrEmpty(dbConfig.ColorCode))
                    {
                        return GenerateFromDatabaseConfig(dbConfig, isVipExpired);
                    }
                }

                // 方案4：普通用户无身份，返回空字符串保持默认样式
                return "";
            }
            catch (Exception)
            {
                return "";
            }
        }

        /// <summary>
        /// 从JSON配置生成idname
        /// </summary>
        /// <param name="config">JSON配置</param>
        /// <param name="isVipExpired">是否过期</param>
        /// <returns>idname字符串</returns>
        private static string GenerateFromJsonConfig(dynamic config, bool isVipExpired)
        {
            // ✅ 直接使用iconUrl字段，包含完整路径
            string iconUrl = config.IconUrl ?? "";
            string colorCode = config.ColorCode ?? "";

            // 移除颜色代码前的#号（如果有）
            if (colorCode.StartsWith("#"))
                colorCode = colorCode.Substring(1);

            if (isVipExpired)
            {
                // VIP过期：只显示颜色，不显示图标
                return $"#{colorCode}";
            }
            else
            {
                // VIP有效：显示图标和颜色
                return string.IsNullOrEmpty(iconUrl) ? $"#{colorCode}" : $"{iconUrl}#{colorCode}";
            }
        }

        /// <summary>
        /// 从数据库配置生成idname
        /// </summary>
        /// <param name="config">数据库配置</param>
        /// <param name="isVipExpired">是否过期</param>
        /// <returns>idname字符串</returns>
        private static string GenerateFromDatabaseConfig(DatabaseIdentityService.DatabaseIdentityConfig config, bool isVipExpired)
        {
            // ✅ 使用完整的图标路径，与JSON方案保持一致
            string iconUrl = config.IconUrl ?? "";
            string colorCode = config.ColorCode ?? "";

            // 移除颜色代码前的#号（如果有）
            if (colorCode.StartsWith("#"))
                colorCode = colorCode.Substring(1);

            if (isVipExpired)
            {
                // VIP过期：只显示颜色，不显示图标
                return $"#{colorCode}";
            }
            else
            {
                // VIP有效：显示图标和颜色
                return string.IsNullOrEmpty(iconUrl) ? $"#{colorCode}" : $"{iconUrl}#{colorCode}";
            }
        }

        /// <summary>
        /// 获取彩色昵称配置
        /// </summary>
        /// <param name="sessionTimeout">身份ID</param>
        /// <returns>颜色代码</returns>
        private static string GetColorNicknameConfig(int sessionTimeout)
        {
            try
            {
                // 获取彩色昵称配置
                var identityOptions = BBSConfigService.GetIdentityOptions();
                var colorNicknameOption = identityOptions.FirstOrDefault(o => o.IsColorNickname);

                if (colorNicknameOption?.ColorOptions != null)
                {
                    var colorOption = colorNicknameOption.ColorOptions.FirstOrDefault(c => c.TargetId == sessionTimeout);
                    if (colorOption != null && !string.IsNullOrEmpty(colorOption.ColorCode))
                    {
                        string colorCode = colorOption.ColorCode;
                        // 移除#号
                        if (colorCode.StartsWith("#"))
                            colorCode = colorCode.Substring(1);
                        return colorCode;
                    }
                }

                return null;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// 获取数据库连接字符串
        /// </summary>
        /// <returns>连接字符串</returns>
        private static string GetConnectionString()
        {
            try
            {
                // 方法1：使用标准的PubConstant方法（推荐）
                try
                {
                    string instanceName = PubConstant.GetAppString("InstanceName");
                    if (!string.IsNullOrEmpty(instanceName))
                    {
                        var pubConstantConnection = PubConstant.GetConnectionString(instanceName);
                        if (!string.IsNullOrEmpty(pubConstantConnection))
                        {
                            return pubConstantConnection;
                        }
                    }
                }
                catch
                {
                    // PubConstant可能不可用，继续尝试其他方法
                }

                // 方法2：尝试从ConfigurationManager获取常见的连接字符串名称
                var connectionStringNames = new[] {
                    "kelinkWAP_CheckConnectionString1",  // 项目中实际使用的名称
                    "ConnectionString",
                    "DefaultConnection",
                    "Database",
                    "Main"
                };

                foreach (var name in connectionStringNames)
                {
                    var connectionStringSettings = System.Configuration.ConfigurationManager.ConnectionStrings[name];
                    if (connectionStringSettings != null && !string.IsNullOrEmpty(connectionStringSettings.ConnectionString))
                    {
                        return connectionStringSettings.ConnectionString;
                    }
                }

                // 方法3：从AppSettings获取连接字符串
                try
                {
                    var appSettingsConnection = PubConstant.GetAppString("ConnectionString");
                    if (!string.IsNullOrEmpty(appSettingsConnection))
                    {
                        return appSettingsConnection;
                    }
                }
                catch
                {
                    // 忽略异常，继续
                }

                return "";
            }
            catch (Exception)
            {
                return "";
            }
        }
    }
}