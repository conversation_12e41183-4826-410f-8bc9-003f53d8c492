---
description: JavaScript编码规范、DOM操作、AJAX处理、前端UI原则和异步更新同步指导
globs: *.js,*.css,*.html,*.aspx,*.ascx
alwaysApply: false
---
## 前端开发综合规范

### 1. JavaScript 编码规范

#### 1.1 兼容性与环境
- **目标浏览器**: 确保代码兼容 IE11+、Microsoft Edge 及现代版 Chrome
- **ES6+特性**: 谨慎使用，确保兼容性，优先现代特性提升可读性
- **宿主环境**: 明确浏览器端运行环境

#### 1.2 命名规范
- **变量与函数**: `camelCase` (例: `myVariable`, `calculateValue()`)
- **构造函数/类**: `PascalCase` (例: `MyClass`, `UserService`)
- **常量**: `ALL_UPPERCASE` 或 `camelCase` (配置对象属性)
- **全局变量**: **严格禁止，必须使用IIFE或ES6模块封装**

#### 1.3 代码结构与模块化
- **单一职责**: 函数短小精悍，文件聚焦单一功能
- **模块化封装**: 使用IIFE封装，形成独立作用域
- **配置常量化**: 模块顶部定义 `const CONFIG = {...}`
- **文件组织**: 遵循项目结构，存放于 `/NetCSS/JS/`

#### 1.4 DOM 操作与事件处理
- **选择器与缓存**:
    - 优先使用 `document.getElementById` 和 `document.querySelector`
    - **频繁访问的DOM元素必须缓存，避免重复查询**
    - **查询结果使用前必须进行存在性检查，防止运行时错误**
- **DOM 修改**:
    - 减少直接DOM操作次数，批量修改时考虑 `DocumentFragment`
    - **动态UI构建推荐数据与视图分离，使用模板字符串**
- **事件处理**:
    - 使用 `element.addEventListener()` 绑定事件
    - **元素移除时必须调用 `removeEventListener()` 清理，防止内存泄漏**
    - 考虑事件委托处理动态元素
- **安全防护**: 优先使用 `element.textContent`，避免XSS风险
- **减少硬编码**: 避免依赖脆弱的DOM结构，优先使用数据属性定位

#### 1.5 数据管理与AJAX
- **数据分离**: 静态配置数据存放于JSON文件或HTML数据属性
- **本地存储**: localStorage/sessionStorage值为字符串，需类型转换
- **统一请求**: 使用 `fetch` API，封装通用请求函数
- **异步处理**: 使用 `Promise` 或 `async/await`
- **错误处理**: 妥善处理网络错误，提供用户反馈
- **安全通信**: iframe通信必须校验 `event.origin`

#### 1.6 错误处理与性能优化
- **异常处理**: 使用 `try...catch` 包裹关键操作，提供有意义错误信息
- **数值检查**: `parseInt`/`parseFloat` 结果使用 `isNaN()` 检查
- **日志记录**: 开发环境使用 `console`，生产环境考虑上报
- **性能原则**: 减少重绘回流，节流防抖高频事件
- **懒加载**: 非首屏图片和大型模块按需加载
- **安全性**: 严禁 `eval()`，严格校验用户输入

### 2. 异步DOM更新与状态同步

#### 2.1 适用场景
- 通过AJAX获取HTML内容并替换页面DOM元素时
- 需要确保JavaScript状态与DOM更新保持同步时
- 局部DOM更新后原有交互功能需要继续工作时

#### 2.2 核心问题
DOM替换会导致：
- 原有事件监听器自动丢失
- 缓存的DOM元素引用失效
- JavaScript状态变量过时

#### 2.3 解决方案
- **状态保存**: 更新前保存重要的JavaScript状态变量
- **状态恢复**: DOM更新后重新初始化相关变量和配置
- **重新绑定**: DOM更新后重新绑定事件监听器
- **事件委托**: 将监听器绑定在不被替换的父级元素上
- **模块重初始化**: 设计支持"销毁"和"初始化"的模块模式

### 3. CSS组织与UI原则

#### 3.1 系统可见性原则 (核心)
**用户在任何交互时刻都应清楚知道系统状态**
- 所有用户操作必须提供即时、清晰的反馈
- 使用加载指示器、状态消息、按钮状态变化
- 反馈应在毫秒级时间内呈现
- 长时间操作必须提供进度指示或取消选项

#### 3.2 CSS组织与维护
- **模块化组织**: 按功能、模块或页面组织CSS文件
- **命名约定**: 使用一致的命名约定（推荐BEM思想）
- **避免巨大文件**: 禁止单一巨大CSS文件
- **谨慎使用**: 禁止过度依赖 `!important`
- **容器处理**: 父容器设置 `border-radius` 时需添加 `overflow: hidden`

#### 3.3 双UI系统样式管理
- **旧版页面**: 继续使用现有的内联样式和自定义CSS
- **新版模板**: 使用Tailwind CSS工具类和组件化样式
- **样式隔离**: 避免在新版模板中混用旧版样式
- **缓存处理**: CSS文件添加版本号查询字符串 `?v=1`

#### 3.4 响应式设计与可访问性
- **响应式布局**: 确保在所有常见设备上提供良好体验
- **媒体查询**: 针对不同屏幕尺寸优化
- **现代布局**: 采用Flexbox、Grid等现代布局技术
- **触摸目标**: 确保小屏幕上触摸目标足够大
- **色彩对比**: 确保足够的色彩对比度（WCAG AA级别4.5:1）
- **表单标签**: 为表单控件提供关联的 `<label>` 标签
- **键盘导航**: 支持键盘导航，不移除outline样式

#### 3.5 性能优化与最佳实践
- **选择器优化**: 优化CSS选择器性能
- **文件压缩**: 生产环境压缩CSS文件
- **动画优化**: 优先使用 `transform` 和 `opacity` 做动画
- **避免导入**: 避免使用 `@import` 指令
- **图标处理**: Lucide图标CSS选择器应匹配动态生成的 `<svg>` 元素
- **层级管理**: 建立清晰的z-index层级体系

### 4. 开发检查清单
- [ ] 所有交互操作都有即时反馈
- [ ] JavaScript模块使用IIFE封装
- [ ] DOM操作前进行存在性检查
- [ ] 异步DOM更新后重新绑定事件
- [ ] CSS文件按模块化组织
- [ ] 响应式布局在主要设备上正常显示
- [ ] 色彩对比度符合可访问性要求
- [ ] 键盘导航功能正常

### 5. 参考文档
详细的前端UI设计原则、实现案例和最佳实践请参考：
**`Documentation/开发文档/前端UI设计与开发规范.md`**