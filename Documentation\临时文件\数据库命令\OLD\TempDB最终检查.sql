-- TempDB 最终检查 - 基于实际可用的信息
-- 避免使用有问题的DMV列

-- 1. TempDB 基本信息
SELECT 
    'TempDB配置总结' AS 检查项目,
    '8个数据文件，每个256MB，总计2GB' AS 数据文件,
    '1个日志文件，4GB大小，当前使用6%' AS 日志文件,
    '配置符合最佳实践' AS 评估结果;

-- 2. 从性能计数器看使用情况
SELECT 
    '性能计数器分析' AS 分析项目,
    CASE 
        WHEN counter_name = 'Data File(s) Size (KB)' THEN '数据文件总大小: ' + CAST(cntr_value / 1024 AS VARCHAR(10)) + ' MB'
        WHEN counter_name = 'Log File(s) Size (KB)' THEN '日志文件总大小: ' + CAST(cntr_value / 1024 AS VARCHAR(10)) + ' MB'
        WHEN counter_name = 'Log File(s) Used Size (KB)' THEN '日志已使用: ' + CAST(cntr_value / 1024 AS VARCHAR(10)) + ' MB'
        WHEN counter_name = 'Percent Log Used' THEN '日志使用率: ' + CAST(cntr_value AS VARCHAR(10)) + '%'
    END AS 当前状态,
    CASE 
        WHEN counter_name = 'Percent Log Used' AND cntr_value < 20 THEN '日志大小充足'
        WHEN counter_name = 'Percent Log Used' AND cntr_value < 50 THEN '日志使用正常'
        WHEN counter_name = 'Percent Log Used' AND cntr_value >= 50 THEN '需要关注日志使用'
        ELSE '正常'
    END AS 建议
FROM sys.dm_os_performance_counters
WHERE object_name LIKE '%Databases%'
    AND instance_name = 'tempdb'
    AND counter_name IN (
        'Data File(s) Size (KB)',
        'Log File(s) Size (KB)', 
        'Log File(s) Used Size (KB)',
        'Percent Log Used'
    );

-- 3. TempDB 等待分析
SELECT 
    'TempDB等待分析' AS 分析类型,
    wait_type,
    waiting_tasks_count,
    wait_time_ms / 1000 AS wait_time_seconds,
    CASE 
        WHEN wait_type = 'WRITELOG' AND wait_time_ms > 1000000 THEN '日志写入压力较大，但在可接受范围'
        WHEN wait_type LIKE 'PAGELATCH%' AND wait_time_ms > 100000 THEN '可能存在页面争用'
        WHEN wait_type LIKE 'PAGEIOLATCH%' AND wait_time_ms > 100000 THEN '可能存在I/O瓶颈'
        ELSE '等待时间正常'
    END AS 分析结果
FROM sys.dm_os_wait_stats
WHERE wait_type IN ('WRITELOG', 'PAGELATCH_SH', 'PAGELATCH_EX', 'PAGEIOLATCH_SH', 'PAGEIOLATCH_EX')
    AND waiting_tasks_count > 0
ORDER BY wait_time_ms DESC;

-- 4. 检查是否有自动增长事件
SELECT 
    '自动增长检查' AS 检查项目,
    '如果下面没有结果，说明最近没有自动增长事件' AS 说明;

-- 尝试查看错误日志中的自动增长（如果权限允许）
BEGIN TRY
    CREATE TABLE #TempErrorLog (
        LogDate DATETIME,
        ProcessInfo VARCHAR(50),
        LogText NVARCHAR(MAX)
    );
    
    INSERT INTO #TempErrorLog
    EXEC xp_readerrorlog 0, 1, N'autogrow', N'tempdb';
    
    SELECT 
        LogDate,
        '发现自动增长事件' AS 事件类型,
        LogText AS 详细信息
    FROM #TempErrorLog
    WHERE LogDate > DATEADD(day, -7, GETDATE());
    
    DROP TABLE #TempErrorLog;
END TRY
BEGIN CATCH
    SELECT 
        '无法访问错误日志' AS 检查结果,
        '可能需要更高权限或手动检查' AS 建议;
END CATCH;

-- 5. 最终建议
SELECT 
    '最终评估' AS 评估项目,
    '您的TempDB配置' AS 配置评估,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM sys.dm_os_performance_counters 
            WHERE object_name LIKE '%Databases%' 
                AND instance_name = 'tempdb' 
                AND counter_name = 'Percent Log Used' 
                AND cntr_value < 20
        ) THEN '配置合理，无需调整'
        ELSE '需要进一步监控'
    END AS 建议,
    '8个数据文件分散并发，4GB日志预留充足' AS 理由;

-- 6. 监控建议
SELECT 
    '持续监控建议' AS 监控项目,
    '定期检查TempDB使用率' AS 监控内容,
    '如果日志使用率经常超过50%，考虑增加日志大小' AS 日志建议,
    '如果出现大量PAGELATCH等待，考虑增加数据文件大小' AS 数据建议,
    '当前配置已经很好，无需立即调整' AS 总结;
