using System;
using System.Linq;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using KeLin.ClassManager;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public partial class MessagePreview : MyPageWap
    {
        // 数据库实例名称（配置于 Web.config 的 AppSettings）
        private readonly string instanceName = PubConstant.GetAppString("InstanceName");
        
        // 预编译正则表达式，提升性能并避免贪婪匹配
        private static readonly Regex StripHtmlRegex = 
            new Regex(@"</?[^>]+>", RegexOptions.Compiled | RegexOptions.Singleline);

        protected void Page_Load(object sender, EventArgs e)
        {
            // 设置JSON响应头
            Response.ContentType = "application/json; charset=utf-8";
            Response.Clear();
            
            try
            {
                var result = GetMessagePreviewData();
                Response.Write(JsonConvert.SerializeObject(result));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[MessagePreview] 错误: {ex}");
                Response.Write(JsonConvert.SerializeObject(new { 
                    success = false, 
                    error = "服务暂时不可用" 
                }));
            }
            
            Response.Flush();
            // 避免 Response.End() 引发 ThreadAbortException
            Context.ApplicationInstance.CompleteRequest();
        }
        
        private object GetMessagePreviewData()
        {
            // 检查用户登录状态
            if (userid == "0" || string.IsNullOrEmpty(userid))
            {
                return new { success = false, error = "未登录" };
            }
            
            string connectionString = PubConstant.GetConnectionString(instanceName);
            
            // 查询未读消息数量
            string countSql = @"SELECT COUNT(id) 
                                FROM wap_message WITH(NOLOCK)
                                WHERE siteid = @SiteId AND touserid = @UserId AND isnew = 1";
            
            int unreadCount = DapperHelper.ExecuteScalar<int>(connectionString, countSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                UserId = DapperHelper.SafeParseLong(userid, "用户ID")
            });
            
            // 查询最新5条消息预览
            string previewSql = @"SELECT TOP 5 id, title, content, nickname, addtime, issystem, isnew
                                 FROM wap_message WITH(NOLOCK)
                                 WHERE siteid = @SiteId AND touserid = @UserId AND isnew < 2
                                 ORDER BY id DESC";
            
            var messages = DapperHelper.Query<dynamic>(connectionString, previewSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                UserId = DapperHelper.SafeParseLong(userid, "用户ID")
            });
            
            // 格式化消息数据 - 后端完成所有显示格式化
            var formattedMessages = messages.Select(msg => new {
                id = msg.id,
                displayTitle = msg.issystem == 1 ? "系统通知" : "新私信",
                displayContent = msg.issystem == 1 
                    ? DapperHelper.LimitLength(msg.title?.ToString() ?? "无标题", 50)
                    : $"{msg.nickname?.ToString() ?? "系统"}给您发送了私信",
                time = FormatRelativeTime(msg.addtime),
                isUnread = msg.isnew == 1
            }).ToList();
            
            return new {
                success = true,
                unreadCount = unreadCount,
                messages = formattedMessages
            };
        }
        
        /// <summary>
        /// 格式化相对时间显示
        /// </summary>
        private string FormatRelativeTime(DateTime time)
        {
            var diff = DateTime.Now - time;
            if (diff.TotalMinutes < 1) return "刚刚";
            if (diff.TotalMinutes < 60) return $"{(int)diff.TotalMinutes}分钟前";
            if (diff.TotalHours < 24) return $"{(int)diff.TotalHours}小时前";
            if (diff.TotalDays < 7) return $"{(int)diff.TotalDays}天前";
            return time.ToString("MM-dd HH:mm");
        }
        
        /// <summary>
        /// 移除HTML标签
        /// </summary>
        private string StripHtml(string input)
        {
            if (string.IsNullOrEmpty(input)) return "";
            return StripHtmlRegex.Replace(input, "").Trim();
        }
    }
} 