using System.Collections.Generic;
using YaoHuo.Plugin.BBS.Models;

namespace YaoHuo.Plugin.Template.Models
{
    /// <summary>
    /// 购买身份页面数据模型
    /// </summary>
    public class BuyGroupPageModel : BasePageModel
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public BuyGroupPageModel()
        {
            PageTitle = "购买身份";
        }

        /// <summary>
        /// 头部选项
        /// </summary>
        public HeaderOptionsModel HeaderOptions { get; set; } = new HeaderOptionsModel();

        /// <summary>
        /// 用户当前身份信息
        /// </summary>
        public CurrentIdentityModel CurrentIdentity { get; set; } = new CurrentIdentityModel();

        /// <summary>
        /// 身份选项列表
        /// </summary>
        public List<IdentityOptionModel> IdentityOptions { get; set; } = new List<IdentityOptionModel>();
    }

    /// <summary>
    /// 当前身份信息模型
    /// </summary>
    public class CurrentIdentityModel
    {
        /// <summary>
        /// 身份显示名称
        /// </summary>
        public string DisplayName { get; set; } = "普通会员";

        /// <summary>
        /// 身份HTML内容（包含颜色和图标）
        /// </summary>
        public string IdentityHtml { get; set; }

        /// <summary>
        /// 是否为VIP身份
        /// </summary>
        public bool IsVip { get; set; }

        /// <summary>
        /// 有效期至
        /// </summary>
        public string EndTime { get; set; }

        /// <summary>
        /// 是否有有效期
        /// </summary>
        public bool HasEndTime { get; set; }
    }

    /// <summary>
    /// 身份选项模型
    /// </summary>
    public class IdentityOptionModel
    {
        /// <summary>
        /// 身份ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 身份名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 身份显示名称（带颜色）
        /// </summary>
        public string DisplayName { get; set; }

        /// <summary>
        /// 价格（RMB）
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// 价格（妖晶）
        /// </summary>
        public long CoinPrice { get; set; }

        /// <summary>
        /// 计费周期（月/年）
        /// </summary>
        public string Period { get; set; } = "月";

        /// <summary>
        /// 身份类型（basic, premium, vip）
        /// </summary>
        public string Type { get; set; } = "basic";

        /// <summary>
        /// 身份图标URL
        /// </summary>
        public string IconUrl { get; set; }

        /// <summary>
        /// 颜色代码（用于旧版UI）
        /// </summary>
        public string ColorCode { get; set; } = "";

        /// <summary>
        /// 图标文件名（用于旧版UI）
        /// </summary>
        public string IconFileName { get; set; } = "";

        /// <summary>
        /// 特权列表
        /// </summary>
        public List<string> Privileges { get; set; } = new List<string>();

        /// <summary>
        /// 购买链接
        /// </summary>
        public string PurchaseUrl { get; set; }

        /// <summary>
        /// 是否为颜色昵称类型
        /// </summary>
        public bool IsColorNickname { get; set; }

        /// <summary>
        /// 颜色选项（仅用于彩色昵称）
        /// </summary>
        public List<ColorOptionModel> ColorOptions { get; set; } = new List<ColorOptionModel>();

        /// <summary>
        /// 是否启用（从JSON配置读取）
        /// </summary>
        public bool? Enabled { get; set; } = true;

        /// <summary>
        /// 排序顺序（从JSON配置读取）
        /// </summary>
        public int? SortOrder { get; set; }

        // 计算属性，避免在模板中使用复杂的Helper嵌套
        /// <summary>
        /// 是否为基础类型
        /// </summary>
        public bool IsBasic => Type == "basic";

        /// <summary>
        /// 是否为高级类型
        /// </summary>
        public bool IsPremium => Type == "premium";

        /// <summary>
        /// 是否为VIP类型
        /// </summary>
        public bool IsVip => Type == "vip";

        /// <summary>
        /// 徽章CSS类（统一为灰色背景）
        /// </summary>
        public string BadgeCssClass => "bg-gray-100 text-gray-600";

        /// <summary>
        /// 徽章图标
        /// </summary>
        public string BadgeIcon => IsBasic ? "tag" : IsPremium ? "star" : "crown";

        /// <summary>
        /// 徽章文本
        /// </summary>
        public string BadgeText => IsBasic ? "基础" : IsPremium ? "高级" : "尊贵";

        /// <summary>
        /// 名称CSS类（根据身份类型设置颜色）
        /// </summary>
        public string NameCssClass { get; set; } = "text-text-primary";
    }

    /// <summary>
    /// 颜色选项模型
    /// </summary>
    public class ColorOptionModel
    {
        /// <summary>
        /// 颜色代码（如"red"）
        /// </summary>
        public string Color { get; set; }

        /// <summary>
        /// 颜色名称
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// 目标ID
        /// </summary>
        public int TargetId { get; set; }

        /// <summary>
        /// 颜色代码（十六进制，如"#FF0000"）
        /// </summary>
        public string ColorCode { get; set; } = "";

        /// <summary>
        /// 是否为默认选中
        /// </summary>
        public bool IsDefault { get; set; }
    }
}