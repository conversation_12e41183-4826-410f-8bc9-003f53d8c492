﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="book_list_log.aspx.cs" Inherits="YaoHuo.Plugin.BBS.book_list_log" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    // 获取性别化显示文本
    string genderDisplay = "TA";
    if (toUserVo != null)
    {
        genderDisplay = toUserVo.sex == 1 ? "他" : "她";
    }

    // 根据是否为自己的空间决定显示文本
    string displayText = (touserid == userid) ? "我" : genderDisplay;

    // 页面标题：好友动态统一显示"我的好友动态"
    string pageTitle = (action == "friends") ? "我的好友动态" : displayText + "的最近动态";

    Response.Write(WapTool.showTop(pageTitle, wmlVo));
    strhtml.Append("<div class=\"title\">");
    if (this.action == "friends")
    {
        strhtml.Append("好友动态");
    }
    else
    {
        // 好友动态链接指向当前登录用户的好友动态（简化URL参数）
        strhtml.Append("<a href=\"" + http_start + "bbs/book_list_log.aspx?action=friends&amp;touserid=" + this.userid + "" + "\">好友动态</a>");
    }
    strhtml.Append("<span class=\"separate\"> </span>");
    if (this.action == "my")
    {
        strhtml.Append(displayText + "的动态");
    }
    else
    {
        strhtml.Append("<a href=\"" + http_start + "bbs/book_list_log.aspx?action=my&amp;touserid=" + this.touserid + "" + "\">" + displayText + "的动态</a>");
    }
    strhtml.Append("</div>");
    //显示列表
   
    for (int i = 0; (listVo != null && i < listVo.Count); i++)
    {
        index = index + kk;
        if (i % 2 == 0)
        {
            strhtml.Append("<div class=\"line1\">");
        }
        else
        {
            strhtml.Append("<div class=\"line2\">");
        }
        //strhtml.Append(index + ".");
        if (action == "friends")
        {
            strhtml.Append("<a href=\"" + this.http_start + "bbs/userinfo.aspx?siteid=" + this.siteid + "&amp;touserid=" + listVo[i].oper_userid + "\">" + listVo[i].oper_nickname + "</a>于");
        }
        else
        {
            strhtml.Append("");
        }
        strhtml.Append(WapTool.DateToString(listVo[i].oper_time, lang,1) + "前" + listVo[i].log_info.Replace("[sid]", this.sid) + "</div>");
    }
    if (listVo==null)
    {
        strhtml.Append("<div class=\"tip\">暂无动态！</div>");
    }
    //显示导航分页
    strhtml.Append(linkURL);
    //导航按钮
    strhtml.Append("<div class=\"btBox\"><div class=\"bt2\">");
    strhtml.Append("<a href=\"" + this.http_start + "bbs/userinfo.aspx?siteid=" + siteid + "&amp;touserid=" + this.touserid + "" + "\">返回上级</a> ");
    strhtml.Append("<a href=\"" + this.http_start + "wapindex.aspx?siteid=" + siteid + "&amp;classid=0" + "\">返回首页</a>");
    strhtml.Append("</div></div>");
    Response.Write(ERROR);  
    Response.Write(WapTool.ToWML(strhtml.ToString(), wmlVo));
Response.Write(WapTool.showDown(wmlVo));
%>