-- =============================================
-- OAuth 完整数据库部署脚本
-- 版本: v2.1 (生产就绪版)
-- 创建日期: 2025-06-20
-- 说明: 整合表结构创建和视图更新的完整部署脚本
-- 功能: 一次性部署完整的OAuth数据库架构
-- 特性: 幂等性设计，可重复执行，适合生产环境部署
-- =============================================

USE [NETOK]
GO

PRINT '========================================';
PRINT '开始 OAuth 完整数据库部署';
PRINT '版本: v2.1 (生产就绪版)';
PRINT '时间: ' + CONVERT(NVARCHAR(30), GETDATE(), 120);
PRINT '========================================';
PRINT '';

-- =============================================
-- 第一部分: 创建数据表
-- =============================================

PRINT '第一部分: 创建数据表';
PRINT '----------------------------------------';

-- 1. OAuth 客户端应用表
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'oauth_clients')
BEGIN
    CREATE TABLE [dbo].[oauth_clients] (
        [app_id] NVARCHAR(50) NOT NULL,              -- 应用ID（主键）
        [app_name] NVARCHAR(100) NOT NULL,           -- 应用名称
        [app_description] NVARCHAR(500) NULL,        -- 应用描述
        [app_key_hash] NVARCHAR(128) NOT NULL,       -- 应用密钥Hash值
        [app_key_salt] NVARCHAR(64) NOT NULL,        -- 应用密钥Salt值
        [redirect_uris] NVARCHAR(2000) NULL,         -- 重定向URI白名单（分号分隔）
        [allowed_scopes] NVARCHAR(200) DEFAULT 'profile', -- 允许的权限范围
        [is_valid] BIT DEFAULT 1 NOT NULL,           -- 是否有效
        [created_at] DATETIME2 DEFAULT GETUTCDATE() NOT NULL, -- 创建时间
        [updated_at] DATETIME2 DEFAULT GETUTCDATE() NOT NULL, -- 更新时间

        CONSTRAINT [PK_oauth_clients] PRIMARY KEY ([app_id]),
        CONSTRAINT [CK_oauth_clients_app_id_length]
            CHECK (LEN(app_id) >= 8),  -- 确保应用ID有足够长度
        CONSTRAINT [CK_oauth_clients_app_name_length]
            CHECK (LEN(app_name) >= 2), -- 确保应用名称不为空或过短
        CONSTRAINT [CK_oauth_clients_time_logic]
            CHECK (updated_at >= created_at)
    );
    
    -- 创建索引
    CREATE INDEX [IX_oauth_clients_valid] ON [oauth_clients] ([is_valid]);
    
    PRINT 'OAuth 客户端表 oauth_clients 创建成功';
END
ELSE
BEGIN
    PRINT 'OAuth 客户端表 oauth_clients 已存在，跳过创建';
END

-- 2. OAuth 授权码表
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'oauth_authorization_codes')
BEGIN
    CREATE TABLE [dbo].[oauth_authorization_codes] (
        [code] NVARCHAR(128) NOT NULL,               -- 授权码（主键）
        [client_id] NVARCHAR(50) NOT NULL,           -- 客户端ID
        [user_id] BIGINT NOT NULL,                   -- 用户ID
        [redirect_uri] NVARCHAR(500) NOT NULL,       -- 重定向URI
        [scope] NVARCHAR(255) DEFAULT 'profile',     -- 权限范围
        [code_challenge] NVARCHAR(256) NULL,         -- PKCE代码挑战
        [code_challenge_method] NVARCHAR(20) DEFAULT 'S256', -- PKCE方法
        [expires_at] DATETIME2 NOT NULL,             -- 过期时间
        [created_at] DATETIME2 DEFAULT GETUTCDATE() NOT NULL, -- 创建时间
        [used_at] DATETIME2 NULL,                    -- 使用时间（标记是否已使用）

        CONSTRAINT [PK_oauth_authorization_codes] PRIMARY KEY ([code]),
        CONSTRAINT [FK_oauth_codes_client] FOREIGN KEY ([client_id])
            REFERENCES [oauth_clients]([app_id]) ON DELETE CASCADE,
        CONSTRAINT [CK_oauth_codes_challenge_method]
            CHECK (code_challenge_method IN ('S256','plain')),
        CONSTRAINT [CK_oauth_codes_time_logic]
            CHECK (expires_at > created_at)
    );
    
    -- 创建索引
    CREATE INDEX [IX_oauth_codes_expires] ON [oauth_authorization_codes] ([expires_at]);
    CREATE INDEX [IX_oauth_codes_client_user] ON [oauth_authorization_codes] ([client_id], [user_id]);
    
    PRINT 'OAuth 授权码表 oauth_authorization_codes 创建成功';
END
ELSE
BEGIN
    PRINT 'OAuth 授权码表 oauth_authorization_codes 已存在，跳过创建';
END

-- 3. OAuth 访问令牌表
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'oauth_access_tokens')
BEGIN
    CREATE TABLE [dbo].[oauth_access_tokens] (
        [token_id] NVARCHAR(128) NOT NULL,           -- 令牌ID（主键）
        [client_id] NVARCHAR(50) NOT NULL,           -- 客户端ID
        [user_id] BIGINT NOT NULL,                   -- 用户ID
        [scope] NVARCHAR(255) DEFAULT 'profile',     -- 权限范围
        [expires_at] DATETIME2 NOT NULL,             -- 过期时间
        [created_at] DATETIME2 DEFAULT GETUTCDATE() NOT NULL, -- 创建时间
        [last_used_at] DATETIME2 NULL,               -- 最后使用时间
        [revoked_at] DATETIME2 NULL,                 -- 撤销时间
        
        -- 预留字段：为未来扩展Refresh Token功能
        [refresh_token] NVARCHAR(128) NULL,          -- 刷新令牌（预留）
        [refresh_expires_at] DATETIME2 NULL,         -- 刷新令牌过期时间（预留）

        CONSTRAINT [PK_oauth_access_tokens] PRIMARY KEY ([token_id]),
        CONSTRAINT [FK_oauth_tokens_client] FOREIGN KEY ([client_id])
            REFERENCES [oauth_clients]([app_id]) ON DELETE CASCADE,
        CONSTRAINT [CK_oauth_tokens_time_logic]
            CHECK (expires_at > created_at),
        CONSTRAINT [CK_oauth_tokens_revoke_logic]
            CHECK (revoked_at IS NULL OR revoked_at >= created_at),
        CONSTRAINT [CK_oauth_tokens_refresh_logic]
            CHECK (refresh_expires_at IS NULL OR refresh_expires_at > expires_at)
    );
    
    -- 创建核心索引（性能优化版）
    -- 1. 令牌查询专用索引：支持GetUserInfo和令牌验证查询
    CREATE INDEX [IX_oauth_token_lookup] ON [oauth_access_tokens] ([token_id])
        INCLUDE ([client_id], [user_id], [expires_at], [revoked_at], [scope]);
    
    -- 2. 用户授权查询专用索引：支持GetUserAuthorizations和授权管理
    CREATE INDEX [IX_oauth_user_tokens] ON [oauth_access_tokens] ([user_id], [client_id])
        INCLUDE ([expires_at], [revoked_at], [created_at]);
    
    PRINT 'OAuth 访问令牌表 oauth_access_tokens 创建成功';
END
ELSE
BEGIN
    PRINT 'OAuth 访问令牌表 oauth_access_tokens 已存在，跳过创建';
END

PRINT '';

-- =============================================
-- 第二部分: 创建/更新视图
-- =============================================

PRINT '第二部分: 创建/更新视图';
PRINT '----------------------------------------';

-- 删除现有视图（确保使用最新定义）
IF EXISTS (SELECT * FROM sys.views WHERE name = 'v_user_app_authorizations')
BEGIN
    DROP VIEW [dbo].[v_user_app_authorizations];
    PRINT '删除旧版本用户授权历史视图';
END

-- 创建优化后的用户授权历史视图（只显示有活跃令牌的应用）
EXEC('
CREATE VIEW [dbo].[v_user_app_authorizations] AS
SELECT
    c.app_id AS AppID,
    c.app_name AS AppName,
    c.app_description AS Remarks,
    t.user_id,
    MAX(t.created_at) AS CreateTime,
    MAX(t.created_at) AS last_authorized,
    COUNT(CASE WHEN t.revoked_at IS NULL AND t.expires_at > GETDATE() THEN 1 END) AS active_tokens,
    COUNT(*) AS total_tokens,
    ROW_NUMBER() OVER (PARTITION BY t.user_id ORDER BY MAX(t.created_at) DESC) AS AuthID
FROM oauth_clients c
INNER JOIN oauth_access_tokens t ON c.app_id = t.client_id
GROUP BY c.app_id, c.app_name, c.app_description, t.user_id
HAVING COUNT(CASE WHEN t.revoked_at IS NULL AND t.expires_at > GETDATE() THEN 1 END) > 0
');

PRINT '用户授权历史视图 v_user_app_authorizations 创建成功';
PRINT '  - 只显示有活跃令牌的应用';
PRINT '  - 删除授权后应用立即消失';
PRINT '  - 支持Index.aspx页面字段映射';
PRINT '  - 包含活跃令牌统计和行号';

PRINT '';

-- =============================================
-- 第三部分: 创建存储过程
-- =============================================

PRINT '第三部分: 创建存储过程';
PRINT '----------------------------------------';

-- 创建测试应用创建存储过程
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_create_test_oauth_app')
BEGIN
    EXEC('
    CREATE PROCEDURE [dbo].[sp_create_test_oauth_app]
        @AppId NVARCHAR(50),
        @AppName NVARCHAR(100),
        @AppDescription NVARCHAR(500) = NULL,
        @RedirectUris NVARCHAR(2000) = NULL
    AS
    BEGIN
        SET NOCOUNT ON;

        -- 生成测试用的Hash和Salt（实际使用时应该由应用程序生成）
        DECLARE @TestHash NVARCHAR(128) = ''test_hash_'' + @AppId;
        DECLARE @TestSalt NVARCHAR(64) = ''test_salt_'' + @AppId;

        IF @RedirectUris IS NULL
            SET @RedirectUris = ''http://localhost:8080/callback;https://example.com/oauth/callback'';

        INSERT INTO oauth_clients (
            app_id, app_name, app_description, app_key_hash, app_key_salt,
            redirect_uris, allowed_scopes, is_valid
        ) VALUES (
            @AppId, @AppName, @AppDescription, @TestHash, @TestSalt,
            @RedirectUris, ''profile'', 1
        );

        PRINT ''测试应用创建成功: '' + @AppName + '' ('' + @AppId + '')'';
        PRINT ''注意: 这是测试用的Hash值，生产环境请使用真实的密钥Hash'';
    END
    ');

    PRINT '测试应用创建存储过程 sp_create_test_oauth_app 创建成功';
END
ELSE
BEGIN
    PRINT '测试应用创建存储过程 sp_create_test_oauth_app 已存在，跳过创建';
END

PRINT '';

-- =============================================
-- 第四部分: 部署完成总结
-- =============================================

PRINT '第四部分: 部署完成总结';
PRINT '========================================';
PRINT '';

PRINT 'OAuth 基础数据库部署成功！';
PRINT '';

PRINT '已创建的数据库对象:';
PRINT '* 数据表:';
PRINT '  - oauth_clients (OAuth应用管理)';
PRINT '  - oauth_authorization_codes (授权码流程)';
PRINT '  - oauth_access_tokens (访问令牌管理)';
PRINT '* 视图:';
PRINT '  - v_user_app_authorizations (用户授权历史，已优化)';
PRINT '* 存储过程:';
PRINT '  - sp_create_test_oauth_app (测试应用创建)';
PRINT '';

PRINT '可用命令:';
PRINT '* 创建测试应用: EXEC sp_create_test_oauth_app ''test_app'', ''测试应用''';
PRINT '* 查看用户授权: SELECT * FROM v_user_app_authorizations WHERE user_id = [用户ID]';
PRINT '';

PRINT '关键特性:';
PRINT '* 幂等性设计: 可重复执行，不会重复创建对象';
PRINT '* 数据完整性: 包含CHECK约束和外键约束';
PRINT '* 性能优化: 创建了核心索引，专门优化常用查询';
PRINT '  - IX_oauth_token_lookup: 令牌验证和用户信息查询';
PRINT '  - IX_oauth_user_tokens: 用户授权管理和历史查询';
PRINT '* 时区处理: UTC存储，本地显示';
PRINT '* 视图优化: 只显示有活跃令牌的应用';
PRINT '';

PRINT '建议的后续操作:';
PRINT '1. 测试OAuth完整流程';
PRINT '2. 验证用户授权管理功能';
PRINT '3. 运行 OAuth_Enhanced_Cleanup.sql 添加数据清理功能';
PRINT '4. 监控数据库性能和存储使用';
PRINT '';

PRINT '重要提醒:';
PRINT '* 生产环境部署前请先在测试环境验证';
PRINT '* 建议定期备份OAuth相关数据';
PRINT '* 密钥Hash和Salt应由应用程序安全生成';
PRINT '* 如需数据清理功能，请运行 OAuth_Enhanced_Cleanup.sql';
PRINT '';

PRINT '========================================';
PRINT 'OAuth 基础架构部署完成！';
PRINT '时间: ' + CONVERT(NVARCHAR(30), GETDATE(), 120);
PRINT '';
PRINT '下一步: 如需数据清理功能，请运行 OAuth_Enhanced_Cleanup.sql';
PRINT '========================================';
