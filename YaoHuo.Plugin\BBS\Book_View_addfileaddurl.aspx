﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_View_addfileAddURL.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Book_View_addfileAddURL" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    wmlVo.mycss += "\r\n<link href=\"/netcss/css/upload-resource.css?X11\" rel=\"stylesheet\" type=\"text/css\"/>";
    StringBuilder strhtml = new StringBuilder();
    Response.Write(WapTool.showTop(this.GetLang("续传文件|续传文件|add subject"), wmlVo));
    if (num > 9) num = 9;
    if (num < 1) num = 1;
    strhtml.Append("<div class=\"upload-container\">");
    strhtml.Append("<div class=\"tab-header\">");
    strhtml.Append("<a class=\"tab-btn\" href=\"" + this.http_start + "bbs/Book_View_addfileAdd.aspx?action=class&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;page=" + this.lpage + "&amp;id=" + this.id + "\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242\"></path><path d=\"M12 21v-9\"></path><path d=\"m8 16 4-4 4 4\"></path></svg>本地文件</a> ");
    strhtml.Append("<a class=\"tab-btn active\"  href=\"#\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\"></path><path d=\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\"></path></svg>外站资源</a> ");
    strhtml.Append("</div>");
    strhtml.Append(this.ERROR);
    if (this.INFO == "OK")
    {
        strhtml.Append("<div class=\"upload-success\">");
        strhtml.Append("<div class=\"upload-success-header\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-upload\"><path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"></path><polyline points=\"17 8 12 3 7 8\"></polyline><line x1=\"12\" x2=\"12\" y1=\"3\" y2=\"15\"></line></svg>");
        strhtml.Append("<div class=\"upload-success-title\">上传成功！</div>");
        strhtml.Append("</div>");
        strhtml.Append("<div class=\"upload-success-subtitle\">资源链接已更新</div>");
        strhtml.Append("</div>");
        if (siteVo.isCheck == 1)
        {
            strhtml.Append("<b>审核后显示！</b>");
        }
    }
    else if (!string.IsNullOrEmpty(this.INFO))
    {
        strhtml.Append("<div class=\"tip\">");
        if (this.INFO == "EXTERR")
        {
            strhtml.Append("<b>上传文件格式错误，只允许上传：" + siteVo.UpFileType + "</b>");
        }
        else if (this.INFO == "NOTSPACE")
        {
            strhtml.Append("<b>网站总空间已经大于系统分配给此网站的最大空间了，网站空间：" + siteVo.sitespace + "M；此网站已使用：" + (siteVo.myspace) + "KB</b>");
        }
        else if (this.INFO == "MAXFILE")
        {
            strhtml.Append("<b>你上传的单个文件超出了最大限制" + siteVo.MaxFileSize + "KB</b>");
        }
        else if (this.INFO == "LOCK")
        {
            strhtml.Append("<b>抱歉，您已经被加入黑名单，请注意发帖规则！</b>");
        }
        strhtml.Append("</div>");
    }
    if (this.INFO != "OK")
    {
        strhtml.Append("<div class=\"num-selector num-top\">");
        strhtml.Append("<label>" + this.GetLang("资源数量|上传数量|Upload Number") + "</label>");
        strhtml.Append("<div class=\"number-control\">");
        strhtml.Append("<button type=\"button\" class=\"num-btn\" onclick=\"updateNum(1)\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M18 15 12 9 6 15\"/></svg></button>");
        strhtml.Append("<input type=\"number\" id=\"numInput\" readonly name=\"num\" value=\"" + this.num + "\" data-current=\"" + this.num + "\"/>");
        strhtml.Append("<button type=\"button\" class=\"num-btn\" onclick=\"updateNum(-1)\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M6 9 12 15 18 9\"/></svg></button>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"class\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"lpage\" value=\"" + lpage + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"id\" value=\"" + id + "\"/>");
        strhtml.Append("<form name=\"f\" action=\"" + http_start + "bbs/Book_View_addfileAddURL.aspx\" enctype=\"multipart/form-data\" method=\"post\">");
        for (int i = 0; i < this.num; i++)
        {
            strhtml.Append("<div class=\"file-upload-section\">");
            strhtml.Append("<div class=\"file-header-url\" onclick=\"toggleCollapse(this)\">");
            strhtml.Append("<div class=\"file-title-group\">");
            strhtml.Append("<div class=\"file-number\">" + (i + 1) + "</div>");
            strhtml.Append("<div class=\"file-title-url\">资源文件<span style=\"padding-left: 2px;\">" + (i + 1) + "</span></div>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"collapse-btn\">");
            strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"chevron-up\"><polyline points=\"18 15 12 9 6 15\"></polyline></svg>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"form-content\">");
            strhtml.Append("<div class=\"form-group\">");
            strhtml.Append("<label>资源名称</label>");
            strhtml.Append("<input type=\"text\" maxlength=\"35\" placeholder=\"必填项\" required=\"required\" name=\"file_title\" class=\"form-control\"/>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"form-group\">");
            strhtml.Append("<label>链接地址</label>");
            strhtml.Append("<input type=\"text\" placeholder=\"http 或 https 开头的链接\" required=\"required\" name=\"file_url\" class=\"form-control\"/>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"form-row\">");
            strhtml.Append("<div class=\"form-group half\">");
            strhtml.Append("<label>文件大小</label>");
            strhtml.Append("<input type=\"text\" maxlength=\"7\" placeholder=\"选填，例如: 8MB\" name=\"file_size\" class=\"form-control\"/>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"form-group half\">");
            strhtml.Append("<label>文件后缀</label>");
            strhtml.Append("<input type=\"text\" maxlength=\"5\" placeholder=\"选填，例如: zip\" name=\"file_ext\" class=\"form-control\"/>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"form-group\">");
            strhtml.Append("<label>" + this.GetLang("附件说明|附件说明|Source") + "</label>");
            strhtml.Append("<textarea name=\"file_info\" oninput=\"adjustTextareaHeight(this)\" placeholder=\"选填备注信息，例如网盘提取密码、文件解压密码\" class=\"form-control\" rows=\"2\"></textarea>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
        }
        strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"lpage\" value=\"" + lpage + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"id\" value=\"" + id + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"num\" value=\"" + num + "\"/>");
        strhtml.Append("<button type=\"submit\" id=\"submitBtn\" name=\"g\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"upload-icon\"><path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"></path><polyline points=\"17 8 12 3 7 8\"></polyline><line x1=\"12\" x2=\"12\" y1=\"3\" y2=\"15\"></line></svg>");
        strhtml.Append("<span>" + this.GetLang("确认提交|上传文件|upload new subject") + "</span>");
        strhtml.Append("</button>");
        strhtml.Append("</form>");
    }
    strhtml.Append("</div>");
    string isWebHtml = this.ShowWEB_view(this.classid);
    if (isWebHtml != "")
    {
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml, wmlVo).Replace("[view]", strhtml.ToString()));
        Response.End();
    }
    strhtml.Append("<div class=\"triangle-alert\">");
    strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-triangle-alert\"><path d=\"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3\"/><path d=\"M12 9v4\"/><path d=\"M12 17h.01\"/></svg>严禁上传色情文件、病毒文件和恶意软件。");
    strhtml.Append("</div>");
    strhtml.Append("<div class=\"nav-buttons\">");
    strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/Book_View_admin.aspx?action=class&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;id=" + this.id + "\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-arrow-left\"><path d=\"m12 19-7-7 7-7\"/><path d=\"M19 12H5\"/></svg>" + this.GetLang("返回管理|返回管理|add content") + "</a>");
    strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs-" + id + ".html\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-house\"><path d=\"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\"/><path d=\"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"/></svg>返回主题</a>");
    strhtml.Append("</div>");
    strhtml.Append(@"<dialog id=""urlDialog"" class=""dialog-url"">
        <div class=""dialog-url-header"">
            <svg class=""dialog-url-icon"" xmlns=""http://www.w3.org/2000/svg"" fill=""none"" viewBox=""0 0 24 24"" stroke=""currentColor"">
                <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"" />
            </svg>
            <h2 id=""dialog-title"" class=""dialog-url-title"">链接格式错误</h2>
        </div>
        <p id=""dialog-description"" class=""dialog-url-description"">
            链接地址必须以 http:// 或 https:// 开头
        </p>
        <div class=""dialog-url-footer"">
            <button class=""dialog-url-button"" onclick=""closeUrlDialog()"">确定</button>
        </div>
    </dialog>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/netcss/js/fileupload/url-common.js?58\">");
    Response.Write(strhtml);
    Response.Write(WapTool.showDown(wmlVo));
%>