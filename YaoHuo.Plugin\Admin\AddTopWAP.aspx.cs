﻿using System;
using KeLin.ClassManager;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.Admin
{
    public class AddTopWAP : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string page = "";

        public string INFO = "";

        public string ERROR = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = base.Request.Form.Get("action");
            page = GetRequestValue("page");
            IsCheckUserManager(userid, userVo.managerlvl, "", "admin/BaseSiteModifyWML.aspx?siteid=" + siteid);
            if (!(action == "gomod"))
            {
                return;
            }
            try
            {
                string requestValue = GetRequestValue("path");
                requestValue = WapTool.URLtoWAP(requestValue);

                // ✅ 使用DapperHelper进行安全的参数化更新操作
                string connectionString = PubConstant.GetConnectionString(string_10);

                if (classid == "0")
                {
                    // 更新用户站点提示信息
                    string updateUserSql = "UPDATE [user] SET siteuptip = @SiteUpTip WHERE userid = @UserId";
                    DapperHelper.Execute(connectionString, updateUserSql, new {
                        SiteUpTip = DapperHelper.LimitLength(requestValue, 4000), // 限制长度防止溢出
                        UserId = DapperHelper.SafeParseLong(siteid, "站点ID")
                    });
                }
                else
                {
                    // 更新分类介绍信息
                    string updateClassSql = "UPDATE [class] SET introduce = @Introduce WHERE userid = @UserId AND classid = @ClassId";
                    DapperHelper.Execute(connectionString, updateClassSql, new {
                        Introduce = DapperHelper.LimitLength(requestValue, 4000), // 限制长度防止溢出
                        UserId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        ClassId = DapperHelper.SafeParseLong(classid, "分类ID")
                    });
                }

                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}