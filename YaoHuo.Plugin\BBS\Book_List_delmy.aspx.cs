﻿using System;
using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{

    public class Book_List_delmy : MyPageWap
    {
        private readonly string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string touserid = "";

        public string reid = "";

        public string page = "";

        public string lpage = "";

        public string ot = "";

        public string INFO = "";

        public string ERROR = "";

        public string why = "";

        public string sub = "";

        public string pw = "";

        public wap_bbsre_Model bbsReVo = null;

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            touserid = GetRequestValue("touserid").TrimStart('0'); // 去掉前导零
            reid = GetRequestValue("reid");
            page = GetRequestValue("page");
            lpage = GetRequestValue("lpage");
            ot = GetRequestValue("ot");
            why = GetRequestValue("why");
            sub = GetRequestValue("sub");
            pw = GetRequestValue("pw");
            CheckManagerLvl("04", "", GetUrlQueryString());

            // 如果ID为1000，不允许进行清空操作并跳转到首页
            if (touserid == "1000")
            {
                Response.Redirect("/");
                return;
            }
            if (action == "Del_1")
            {
                action = "godel";
                sub = "1";
            }
            else if (action == "Del_2")
            {
                action = "godel";
                sub = "2";
            }
            else if (action == "Del_3")
            {
                action = "godel";
                sub = "3";
            }
            if (!WapTool.IsNumeric(touserid))
            {
                touserid = "0";
            }
            if (!(action == "godel"))
            {
                return;
            }
            try
            {
                if (PubConstant.md5(pw).ToLower() != userVo.password.ToLower())
                {
                    INFO = "PASSERROR";
                    return;
                }
                // ✅ 使用DapperHelper进行安全的参数化删除操作
                string connectionString = PubConstant.GetConnectionString(string_10);

                // 删除用户所有帖子
                string deletePostsSql = "DELETE FROM wap_bbs WHERE userid = @SiteId AND book_pub = @ToUserId";
                DapperHelper.Execute(connectionString, deletePostsSql, new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    ToUserId = DapperHelper.SafeParseLong(touserid, "目标用户ID")
                });

                if (sub == "1")
                {
                    // ✅ 仅清空帖子，不清币和经验
                    string messageContent = "删除时间：" + DateTime.Now + "[br]理由：" + why;

                    string insertMessageSql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                                              VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, 1)";
                    DapperHelper.Execute(connectionString, insertMessageSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                        Nickname = DapperHelper.LimitLength(nickname, 50),
                        Title = "您的所有帖子被清空",
                        Content = DapperHelper.LimitLength(messageContent, 500),
                        ToUserId = DapperHelper.SafeParseLong(touserid, "目标用户ID")
                    });

                    string insertLogSql = @"INSERT INTO wap_log(siteid,oper_userid,oper_nickname,oper_type,log_info,oper_ip)
                                          VALUES (@SiteId, @OperUserId, @OperNickname, 0, @LogInfo, @OperIp)";
                    DapperHelper.Execute(connectionString, insertLogSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        OperUserId = DapperHelper.SafeParseLong(userid, "操作用户ID"),
                        OperNickname = DapperHelper.LimitLength(nickname, 50),
                        LogInfo = DapperHelper.LimitLength("用户ID:" + userid + "清空用户ID:" + touserid + "的所有帖子", 500),
                        OperIp = DapperHelper.LimitLength(IP, 50)
                    });
                }
                else if (sub == "2")
                {
                    // ✅ 清空帖子+清币
                    string updateUserSql = "UPDATE [user] SET money = 0 WHERE siteid = @SiteId AND userid = @UserId";
                    DapperHelper.Execute(connectionString, updateUserSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = DapperHelper.SafeParseLong(touserid, "目标用户ID")
                    });

                    string messageContent = "删除时间：" + DateTime.Now + "[br]同时清币[br]理由：" + why;

                    string insertMessageSql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                                              VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, 1)";
                    DapperHelper.Execute(connectionString, insertMessageSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                        Nickname = DapperHelper.LimitLength(nickname, 50),
                        Title = "您的所有帖子被清空",
                        Content = DapperHelper.LimitLength(messageContent, 500),
                        ToUserId = DapperHelper.SafeParseLong(touserid, "目标用户ID")
                    });

                    string insertLogSql = @"INSERT INTO wap_log(siteid,oper_userid,oper_nickname,oper_type,log_info,oper_ip)
                                          VALUES (@SiteId, @OperUserId, @OperNickname, 0, @LogInfo, @OperIp)";
                    DapperHelper.Execute(connectionString, insertLogSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        OperUserId = DapperHelper.SafeParseLong(userid, "操作用户ID"),
                        OperNickname = DapperHelper.LimitLength(nickname, 50),
                        LogInfo = DapperHelper.LimitLength("用户ID:" + userid + "清空用户ID:" + touserid + "的所有帖子[+清币]", 500),
                        OperIp = DapperHelper.LimitLength(IP, 50)
                    });
                }
                else
                {
                    // ✅ 清空帖子+清币+清经验
                    string updateUserSql = "UPDATE [user] SET money = 0, expr = 0 WHERE siteid = @SiteId AND userid = @UserId";
                    DapperHelper.Execute(connectionString, updateUserSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = DapperHelper.SafeParseLong(touserid, "目标用户ID")
                    });

                    string messageContent = "删除时间：" + DateTime.Now + "[br]同时清币，清经验值[br]理由：" + why;

                    string insertMessageSql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                                              VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, 1)";
                    DapperHelper.Execute(connectionString, insertMessageSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                        Nickname = DapperHelper.LimitLength(nickname, 50),
                        Title = "您的所有帖子被清空",
                        Content = DapperHelper.LimitLength(messageContent, 500),
                        ToUserId = DapperHelper.SafeParseLong(touserid, "目标用户ID")
                    });

                    string insertLogSql = @"INSERT INTO wap_log(siteid,oper_userid,oper_nickname,oper_type,log_info,oper_ip)
                                          VALUES (@SiteId, @OperUserId, @OperNickname, 0, @LogInfo, @OperIp)";
                    DapperHelper.Execute(connectionString, insertLogSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        OperUserId = DapperHelper.SafeParseLong(userid, "操作用户ID"),
                        OperNickname = DapperHelper.LimitLength(nickname, 50),
                        LogInfo = DapperHelper.LimitLength("用户ID:" + userid + "清空用户ID:" + touserid + "的所有帖子[+清币+清经验]", 500),
                        OperIp = DapperHelper.LimitLength(IP, 50)
                    });
                }
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}