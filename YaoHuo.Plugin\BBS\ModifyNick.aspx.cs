using System;
using System.Text.RegularExpressions;
using KeLin.ClassManager;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using System.Linq;

namespace YaoHuo.Plugin.BBS
{
    public class ModifyNick : MyPageWap
    {
        private readonly string a = PubConstant.GetAppString("InstanceName");

        public string INFO = "";
        public string ERROR = "";
        public string tonickname = "";
        public string num = "0";

        protected void Page_Load(object sender, EventArgs e)
        {
            IsLogin(userid, "bbs/modifyuserinfo.aspx?siteid=" + siteid);
            needPassWordToAdmin();
            
            string action = base.Request.Form.Get("action");
            num = WapTool.GetSiteDefault(siteVo.Version, 48);
            if (!WapTool.IsNumeric(num) || num == "0")
            {
                num = "9";
            }
            
            tonickname = userVo.nickname;
            
            if (action != "gomod")
            {
                return;
            }
            
            try
            {
                tonickname = GetRequestValue("tonickname");

                // --- 新增：规范化空格处理 ---
                if (tonickname != null)
                {
                    // 1. 去除首尾空格
                    tonickname = tonickname.Trim();
                    // 2. 将中间的多个连续空白符合并成一个空格
                    tonickname = Regex.Replace(tonickname, @"\s+", " ");
                }
                // --- 结束新增 ---

                // 限制长度 (规范化之后再限制长度更合理)
                if (!string.IsNullOrEmpty(tonickname))
                {
                    tonickname = WapTool.Left(tonickname, int.Parse(num));
                }

                // 验证昵称 (使用处理后的 tonickname)
                if (string.IsNullOrWhiteSpace(tonickname))
                {
                    INFO = "NULL";
                    return;
                }
                
                // 检查昵称是否已存在
                if (IsNicknameExists(tonickname))
                {
                    INFO = "HASEXIST";
                    return;
                }
                
                // 检查修改频率限制
                if (!CanChangeNickname())
                {
                    INFO = "TOOFREQUENT";
                    return;
                }
                
                // 检查禁用词
                if (ContainsForbiddenWords(tonickname))
                {
                    INFO = "FORBIDDEN";
                    return;
                }
                
                // ✅ 使用Dapper安全地更新昵称
                UpdateNicknameSafely(tonickname, siteid, userid);
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }
        }

        /// <summary>
        /// 检查昵称是否已存在
        /// </summary>
        private bool IsNicknameExists(string nickname)
        {
            string connectionString = PubConstant.GetConnectionString(a);
            string sql = "SELECT COUNT(1) FROM [user] WHERE nickname = @Nickname AND siteid = @SiteId AND userid != @UserId";
            
            int count = DapperHelper.ExecuteScalar<int>(connectionString, sql, new { 
                Nickname = nickname, 
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"), 
                UserId = DapperHelper.SafeParseLong(userid, "用户ID") 
            });
            
            return count > 0;
        }

        /// <summary>
        /// 检查是否可以修改昵称（每月限制一次）
        /// </summary>
        private bool CanChangeNickname()
        {
            string connectionString = PubConstant.GetConnectionString(a);
            string sql = "SELECT LastNickChangeDate FROM [user] WHERE siteid = @SiteId AND userid = @UserId";
            
            DateTime? lastChangeDate = DapperHelper.ExecuteScalar<DateTime?>(connectionString, sql, new { 
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"), 
                UserId = DapperHelper.SafeParseLong(userid, "用户ID") 
            });

            if (lastChangeDate.HasValue)
            {
                DateTime now = DateTime.Now;
                if (now.Year == lastChangeDate.Value.Year && now.Month == lastChangeDate.Value.Month)
                {
                    return false;
                }
            }
            return true;
        }

        /// <summary>
        /// 检查昵称是否包含禁用词
        /// </summary>
        private bool ContainsForbiddenWords(string nickname)
        {
            // 基本的禁用词检查
            string[] forbiddenWords = { "admin", "管理员", "系统", "客服" };
            string lowerNickname = nickname.ToLower();
            
            return forbiddenWords.Any(word => lowerNickname.Contains(word.ToLower()));
        }

        /// <summary>
        /// 使用Dapper安全地更新昵称，避免SQL注入
        /// </summary>
        /// <param name="nickname">新昵称</param>
        /// <param name="siteId">站点ID</param>
        /// <param name="userId">用户ID</param>
        private void UpdateNicknameSafely(string nickname, string siteId, string userId)
        {
            // 参数验证
            if (string.IsNullOrWhiteSpace(nickname))
                throw new ArgumentException("昵称不能为空");

            string connectionString = PubConstant.GetConnectionString(a);
            string sql = "UPDATE [user] SET nickname = @Nickname, LastNickChangeDate = GETDATE() WHERE siteid = @SiteId AND userid = @UserId";

            // 使用Dapper执行参数化查询，SafeParseLong内部已包含数字验证
            DapperHelper.Execute(connectionString, sql, new {
                Nickname = nickname,
                SiteId = DapperHelper.SafeParseLong(siteId, "站点ID"),
                UserId = DapperHelper.SafeParseLong(userId, "用户ID")
            });
        }

        private string ConnectionString => PubConstant.GetConnectionString(a);
    }
}