using System.Collections.Generic;
using YaoHuo.Plugin.Template.Models;

namespace YaoHuo.Plugin.BBS.Models
{
    /// <summary>
    /// EditProfile 页面数据模型
    /// </summary>
    public class EditProfilePageModel : BasePageModel
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public EditProfilePageModel()
        {
            PageTitle = "编辑资料";
        }

        /// <summary>
        /// 表单数据
        /// </summary>
        public EditProfileFormModel FormData { get; set; } = new EditProfileFormModel();

        /// <summary>
        /// 选项列表
        /// </summary>
        public OptionListsModel OptionLists { get; set; } = new OptionListsModel();
    }

    /// <summary>
    /// 表单数据模型
    /// </summary>
    public class EditProfileFormModel
    {
        /// <summary>
        /// 论坛资料
        /// </summary>
        public ForumInfoModel ForumInfo { get; set; } = new ForumInfoModel();

        /// <summary>
        /// 联系方式
        /// </summary>
        public ContactInfoModel ContactInfo { get; set; } = new ContactInfoModel();

        /// <summary>
        /// 个人信息
        /// </summary>
        public PersonalInfoModel PersonalInfo { get; set; } = new PersonalInfoModel();

        /// <summary>
        /// 表单操作URL
        /// </summary>
        public string ActionUrl { get; set; }

        /// <summary>
        /// 隐藏字段
        /// </summary>
        public HiddenFieldsModel HiddenFields { get; set; } = new HiddenFieldsModel();
    }

    /// <summary>
    /// 论坛资料模型
    /// </summary>
    public class ForumInfoModel
    {
        /// <summary>
        /// 昵称
        /// </summary>
        public string Nickname { get; set; }

        /// <summary>
        /// 个性签名
        /// </summary>
        public string Signature { get; set; }

        /// <summary>
        /// 昵称修改限制提示
        /// </summary>
        public string NicknameHint { get; set; } = "每月仅可修改1次";
    }

    /// <summary>
    /// 联系方式模型
    /// </summary>
    public class ContactInfoModel
    {
        /// <summary>
        /// 手机号
        /// </summary>
        public string Mobile { get; set; }

        /// <summary>
        /// 邮箱
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// QQ号
        /// </summary>
        public string QQ { get; set; }
    }

    /// <summary>
    /// 个人信息模型
    /// </summary>
    public class PersonalInfoModel
    {
        /// <summary>
        /// 爱好
        /// </summary>
        public string Hobby { get; set; }

        /// <summary>
        /// 婚否
        /// </summary>
        public string MaritalStatus { get; set; }

        /// <summary>
        /// 职业
        /// </summary>
        public string Occupation { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }

        /// <summary>
        /// 身高
        /// </summary>
        public string Height { get; set; }

        /// <summary>
        /// 体重
        /// </summary>
        public string Weight { get; set; }

        /// <summary>
        /// 星座
        /// </summary>
        public string Zodiac { get; set; }

        /// <summary>
        /// 性别（隐藏字段）
        /// </summary>
        public int Gender { get; set; }
    }

    /// <summary>
    /// 选项列表模型
    /// </summary>
    public class OptionListsModel
    {
        /// <summary>
        /// 婚否选项
        /// </summary>
        public List<OptionItem> MaritalStatusOptions { get; set; } = new List<OptionItem>();

        /// <summary>
        /// 星座选项
        /// </summary>
        public List<OptionItem> ZodiacOptions { get; set; } = new List<OptionItem>();
    }
} 