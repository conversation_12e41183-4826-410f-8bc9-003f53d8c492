// KenLin_ClassManager, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// KeLin.ClassManager.DAL.wap_message_DAL
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using KeLin.ClassManager;
using KeLin.ClassManager.ExUtility;
using KeLin.ClassManager.Model;

public class wap_message_DAL
{
	private string a = "kelinkWAP_Check";

	private string b = "";

	public wap_message_DAL(string InstanceName)
	{
		a = InstanceName;
		b = PubConstant.GetConnectionString(a);
	}

	public long Add(wap_message_Model model)
	{
		long result = default(long);
		while (true)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("insert into wap_message(");
			stringBuilder.Append("siteid,userid,nickname,title,content,touserid,tonickname,isnew,issystem,addtime,HangBiaoShi)");
			stringBuilder.Append(" values (");
			stringBuilder.Append("@siteid,@userid,@nickname,@title,@content,@touserid,@tonickname,@isnew,@issystem,@addtime,@HangBiaoShi)");
			SqlParameter[] array = new SqlParameter[11]
			{
				new SqlParameter("@siteid", SqlDbType.BigInt),
				new SqlParameter("@userid", SqlDbType.BigInt),
				new SqlParameter("@nickname", SqlDbType.NVarChar),
				new SqlParameter("@title", SqlDbType.NText),
				new SqlParameter("@content", SqlDbType.NText),
				new SqlParameter("@touserid", SqlDbType.BigInt),
				new SqlParameter("@tonickname", SqlDbType.NVarChar),
				new SqlParameter("@isnew", SqlDbType.SmallInt),
				new SqlParameter("@issystem", SqlDbType.SmallInt),
				new SqlParameter("@addtime", SqlDbType.DateTime),
				new SqlParameter("@HangBiaoShi", SqlDbType.Int)
			};
			array[0].Value = model.siteid;
			array[1].Value = model.userid;
			array[2].Value = model.nickname;
			array[3].Value = model.title;
			array[4].Value = model.content;
			array[5].Value = model.touserid;
			array[6].Value = model.tonickname;
			array[7].Value = model.isnew;
			array[8].Value = model.issystem;
			array[9].Value = model.addtime;
			array[10].Value = model.HangBiaoShi;
			object obj = DbHelperSQL.ExecuteScalar(b, CommandType.Text, stringBuilder.ToString(), array);
			bool flag = obj != null;
			if (true)
			{
			}
			int num = 1;
			while (true)
			{
				switch (num)
				{
				case 1:
					if (!flag)
					{
						num = 3;
						continue;
					}
					result = Convert.ToInt64(obj);
					num = 2;
					continue;
				case 3:
					result = 1L;
					num = 0;
					continue;
				case 0:
				case 2:
					return result;
				}
				break;
			}
		}
	}

	public long AddALL(wap_message_Model model)
	{
		long result = default(long);
		while (true)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("insert into wap_message(siteid,userid,nickname,title,content,touserid,issystem)");
			stringBuilder.Append(" (select @siteid ,@userid,@nickname,@title,@content, userid,1  from [user] where siteid=@tositeid)");
			SqlParameter[] array = new SqlParameter[6]
			{
				new SqlParameter("@siteid", SqlDbType.BigInt),
				new SqlParameter("@userid", SqlDbType.BigInt),
				new SqlParameter("@nickname", SqlDbType.NVarChar),
				new SqlParameter("@title", SqlDbType.NText),
				new SqlParameter("@content", SqlDbType.NText),
				new SqlParameter("@tositeid", SqlDbType.BigInt)
			};
			array[0].Value = model.siteid;
			array[1].Value = model.userid;
			array[2].Value = model.nickname;
			array[3].Value = model.title;
			array[4].Value = model.content;
			array[5].Value = model.siteid;
			object obj = DbHelperSQL.ExecuteScalar(b, CommandType.Text, stringBuilder.ToString(), array);
			bool flag = obj != null;
			if (true)
			{
			}
			int num = 3;
			while (true)
			{
				switch (num)
				{
				case 3:
					if (!flag)
					{
						num = 0;
						continue;
					}
					result = Convert.ToInt64(obj);
					num = 2;
					continue;
				case 0:
					result = 1L;
					num = 1;
					continue;
				case 1:
				case 2:
					return result;
				}
				break;
			}
		}
	}

	public void Update(wap_message_Model model)
	{
		if (true)
		{
		}
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("update wap_message set ");
		stringBuilder.Append("siteid=@siteid,");
		stringBuilder.Append("userid=@userid,");
		stringBuilder.Append("nickname=@nickname,");
		stringBuilder.Append("title=@title,");
		stringBuilder.Append("content=@content,");
		stringBuilder.Append("touserid=@touserid,");
		stringBuilder.Append("tonickname=@tonickname,");
		stringBuilder.Append("isnew=@isnew,");
		stringBuilder.Append("issystem=@issystem,");
		stringBuilder.Append("addtime=@addtime,");
		stringBuilder.Append("HangBiaoShi=@HangBiaoShi");
		stringBuilder.Append(" where id=@id  ");
		SqlParameter[] array = new SqlParameter[12]
		{
			new SqlParameter("@id", SqlDbType.BigInt),
			new SqlParameter("@siteid", SqlDbType.BigInt),
			new SqlParameter("@userid", SqlDbType.BigInt),
			new SqlParameter("@nickname", SqlDbType.NVarChar),
			new SqlParameter("@title", SqlDbType.NText),
			new SqlParameter("@content", SqlDbType.NText),
			new SqlParameter("@touserid", SqlDbType.BigInt),
			new SqlParameter("@tonickname", SqlDbType.NVarChar),
			new SqlParameter("@isnew", SqlDbType.SmallInt),
			new SqlParameter("@issystem", SqlDbType.SmallInt),
			new SqlParameter("@addtime", SqlDbType.DateTime),
			new SqlParameter("@HangBiaoShi", SqlDbType.Int)
		};
		array[0].Value = model.id;
		array[1].Value = model.siteid;
		array[2].Value = model.userid;
		array[3].Value = model.nickname;
		array[4].Value = model.title;
		array[5].Value = model.content;
		array[6].Value = model.touserid;
		array[7].Value = model.tonickname;
		array[8].Value = model.isnew;
		array[9].Value = model.issystem;
		array[10].Value = model.addtime;
		array[11].Value = model.HangBiaoShi;
		DbHelperSQL.ExecuteNonQuery(b, CommandType.Text, stringBuilder.ToString(), array);
	}

	public void Delete(long id)
	{
		if (true)
		{
		}
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("delete from wap_message ");
		stringBuilder.Append(" where id=@id ");
		SqlParameter[] array = new SqlParameter[1]
		{
			new SqlParameter("@id", SqlDbType.BigInt)
		};
		array[0].Value = id;
		DbHelperSQL.ExecuteNonQuery(b, CommandType.Text, stringBuilder.ToString(), array);
	}

	public void Delete(long siteid, long id)
	{
		if (true)
		{
		}
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("delete from wap_message ");
		stringBuilder.Append(" where id=@id and siteid=@siteid ");
		SqlParameter[] array = new SqlParameter[2]
		{
			new SqlParameter("@id", SqlDbType.BigInt),
			new SqlParameter("@siteid", SqlDbType.BigInt)
		};
		array[0].Value = id;
		array[1].Value = siteid;
		DbHelperSQL.ExecuteNonQuery(b, CommandType.Text, stringBuilder.ToString(), array);
	}

	public void DelALL(string siteid)
	{
		if (true)
		{
		}
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("exec DelMessage @siteid");
		SqlParameter[] array = new SqlParameter[1]
		{
			new SqlParameter("@siteid", SqlDbType.VarChar)
		};
		array[0].Value = siteid;
		DbHelperSQL.ExecuteNonQuery(b, CommandType.Text, stringBuilder.ToString(), array);
	}

	public wap_message_Model GetModel(long id)
	{
		wap_message_Model result = default(wap_message_Model);
		while (true)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("select  top 1 id,siteid,userid,nickname,title,content,touserid,tonickname,isnew,issystem,addtime,HangBiaoShi from wap_message ");
			stringBuilder.Append(" where id=@id ");
			SqlParameter[] array = new SqlParameter[1]
			{
				new SqlParameter("@id", SqlDbType.BigInt)
			};
			array[0].Value = id;
			wap_message_Model wap_message_Model = new wap_message_Model();
			DataSet dataSet = DbHelperSQL.ExecuteDataset(b, CommandType.Text, stringBuilder.ToString(), array);
			bool flag = dataSet.Tables[0].Rows.Count <= 0;
			int num = 25;
			while (true)
			{
				switch (num)
				{
				case 25:
					if (!flag)
					{
						num = 11;
						continue;
					}
					result = null;
					num = 10;
					continue;
				case 12:
					flag = !(dataSet.Tables[0].Rows[0]["addtime"].ToString() != "");
					num = 3;
					continue;
				case 3:
					if (!flag)
					{
						num = 19;
						continue;
					}
					goto case 5;
				case 9:
					wap_message_Model.isnew = long.Parse(dataSet.Tables[0].Rows[0]["isnew"].ToString());
					num = 26;
					continue;
				case 23:
					wap_message_Model.siteid = long.Parse(dataSet.Tables[0].Rows[0]["siteid"].ToString());
					num = 24;
					continue;
				case 0:
					wap_message_Model.HangBiaoShi = long.Parse(dataSet.Tables[0].Rows[0]["HangBiaoShi"].ToString());
					num = 18;
					continue;
				case 2:
					wap_message_Model.id = long.Parse(dataSet.Tables[0].Rows[0]["id"].ToString());
					if (true)
					{
					}
					num = 13;
					continue;
				case 1:
					wap_message_Model.issystem = long.Parse(dataSet.Tables[0].Rows[0]["issystem"].ToString());
					num = 12;
					continue;
				case 18:
					result = wap_message_Model;
					num = 22;
					continue;
				case 14:
					wap_message_Model.tonickname = dataSet.Tables[0].Rows[0]["tonickname"].ToString();
					flag = !(dataSet.Tables[0].Rows[0]["isnew"].ToString() != "");
					num = 17;
					continue;
				case 17:
					if (!flag)
					{
						num = 9;
						continue;
					}
					goto case 26;
				case 24:
					flag = !(dataSet.Tables[0].Rows[0]["userid"].ToString() != "");
					num = 6;
					continue;
				case 6:
					if (!flag)
					{
						num = 21;
						continue;
					}
					goto case 7;
				case 5:
					flag = !(dataSet.Tables[0].Rows[0]["HangBiaoShi"].ToString() != "");
					num = 8;
					continue;
				case 8:
					if (!flag)
					{
						num = 0;
						continue;
					}
					goto case 18;
				case 7:
					wap_message_Model.nickname = dataSet.Tables[0].Rows[0]["nickname"].ToString();
					wap_message_Model.title = dataSet.Tables[0].Rows[0]["title"].ToString();
					wap_message_Model.content = dataSet.Tables[0].Rows[0]["content"].ToString();
					flag = !(dataSet.Tables[0].Rows[0]["touserid"].ToString() != "");
					num = 27;
					continue;
				case 27:
					if (!flag)
					{
						num = 4;
						continue;
					}
					goto case 14;
				case 11:
					flag = !(dataSet.Tables[0].Rows[0]["id"].ToString() != "");
					num = 20;
					continue;
				case 20:
					if (!flag)
					{
						num = 2;
						continue;
					}
					goto case 13;
				case 13:
					flag = !(dataSet.Tables[0].Rows[0]["siteid"].ToString() != "");
					num = 16;
					continue;
				case 16:
					if (!flag)
					{
						num = 23;
						continue;
					}
					goto case 24;
				case 21:
					wap_message_Model.userid = long.Parse(dataSet.Tables[0].Rows[0]["userid"].ToString());
					num = 7;
					continue;
				case 4:
					wap_message_Model.touserid = long.Parse(dataSet.Tables[0].Rows[0]["touserid"].ToString());
					num = 14;
					continue;
				case 19:
					wap_message_Model.addtime = DateTime.Parse(dataSet.Tables[0].Rows[0]["addtime"].ToString());
					num = 5;
					continue;
				case 26:
					flag = !(dataSet.Tables[0].Rows[0]["issystem"].ToString() != "");
					num = 15;
					continue;
				case 15:
					if (!flag)
					{
						num = 1;
						continue;
					}
					goto case 12;
				case 10:
				case 22:
					return result;
				}
				break;
			}
		}
	}

	public int GetListCount(string strWhere)
	{
		int result = default(int);
		while (true)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("select count(id)");
			stringBuilder.Append(" FROM wap_message ");
			bool flag = !(strWhere.Trim() != "");
			int num = 2;
			while (true)
			{
				switch (num)
				{
				case 2:
					if (!flag)
					{
						num = 1;
						continue;
					}
					goto case 3;
				case 3:
					result = int.Parse(DbHelperSQL.ExecuteScalar(b, CommandType.Text, stringBuilder.ToString()).ToString());
					if (true)
					{
					}
					num = 0;
					continue;
				case 1:
					stringBuilder.Append(" where " + strWhere);
					num = 3;
					continue;
				case 0:
					return result;
				}
				break;
			}
		}
	}

	public DataSet GetList(string strWhere)
	{
		DataSet result = default(DataSet);
		while (true)
		{
			if (true)
			{
			}
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("select id,siteid,userid,nickname,title,content,touserid,tonickname,isnew,issystem,addtime,HangBiaoShi ");
			stringBuilder.Append(" FROM wap_message ");
			bool flag = !(strWhere.Trim() != "");
			int num = 0;
			while (true)
			{
				switch (num)
				{
				case 0:
					if (!flag)
					{
						num = 1;
						continue;
					}
					goto case 3;
				case 3:
					result = DbHelperSQL.ExecuteDataset(b, CommandType.Text, stringBuilder.ToString());
					num = 2;
					continue;
				case 1:
					stringBuilder.Append(" where " + strWhere);
					num = 3;
					continue;
				case 2:
					return result;
				}
				break;
			}
		}
	}

	public DataSet GetList(int Top, string strWhere, string filedOrder)
	{
		DataSet result = default(DataSet);
		while (true)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("select ");
			bool flag = Top <= 0;
			int num = 1;
			while (true)
			{
				switch (num)
				{
				case 1:
					if (!flag)
					{
						num = 8;
						continue;
					}
					goto case 9;
				case 7:
					stringBuilder.Append(" order by " + filedOrder);
					num = 2;
					continue;
				case 6:
					stringBuilder.Append(" where " + strWhere);
					num = 0;
					continue;
				case 8:
					stringBuilder.Append(" top " + Top);
					num = 9;
					continue;
				case 2:
					result = DbHelperSQL.ExecuteDataset(b, CommandType.Text, stringBuilder.ToString());
					num = 4;
					continue;
				case 9:
					stringBuilder.Append(" id,siteid,userid,nickname,title,content,touserid,tonickname,isnew,issystem,addtime,HangBiaoShi ");
					stringBuilder.Append(" FROM wap_message ");
					flag = !(strWhere.Trim() != "");
					num = 5;
					continue;
				case 5:
					if (!flag)
					{
						num = 6;
						continue;
					}
					goto case 0;
				case 0:
					if (true)
					{
					}
					flag = !(filedOrder.Trim() != "");
					num = 3;
					continue;
				case 3:
					if (!flag)
					{
						num = 7;
						continue;
					}
					goto case 2;
				case 4:
					return result;
				}
				break;
			}
		}
	}

	public DataSet GetList(int PageSize, int PageIndex, int OrderType, string strWhere)
	{
		if (true)
		{
		}
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("exec UP_GetRecordByPage @tblName,@fldName,@PageSize,@PageIndex,@IsReCount,@OrderType,@strWhere");
		SqlParameter[] array = new SqlParameter[7]
		{
			new SqlParameter("@tblName", SqlDbType.VarChar),
			new SqlParameter("@fldName", SqlDbType.VarChar),
			new SqlParameter("@PageSize", SqlDbType.Int),
			new SqlParameter("@PageIndex", SqlDbType.Int),
			new SqlParameter("@IsReCount", SqlDbType.Int),
			new SqlParameter("@OrderType", SqlDbType.Bit),
			new SqlParameter("@strWhere", SqlDbType.VarChar)
		};
		array[0].Value = "wap_message";
		array[1].Value = "hangbiaoshi";
		array[2].Value = PageSize;
		array[3].Value = PageIndex;
		array[4].Value = 0;
		array[5].Value = OrderType;
		array[6].Value = strWhere;
		return DbHelperSQL.ExecuteDataset(b, CommandType.Text, stringBuilder.ToString(), array);
	}

	public DataSet GetList(int PageSize, int PageIndex, string strWhere, string ShowFldName, string OrderfldName, int TotalCount, int OrderType)
	{
		if (true)
		{
		}
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("exec UP_GetRecordByPageOrder @tblName,@fldName,@OrderfldName,@StatfldName,@TotalCount,@PageSize,@PageIndex,@IsReCount,@OrderType,@strWhere");
		SqlParameter[] array = new SqlParameter[10]
		{
			new SqlParameter("@tblName", SqlDbType.VarChar),
			new SqlParameter("@fldName", SqlDbType.VarChar),
			new SqlParameter("@OrderfldName", SqlDbType.VarChar),
			new SqlParameter("@StatfldName", SqlDbType.VarChar),
			new SqlParameter("@TotalCount", SqlDbType.Int),
			new SqlParameter("@PageSize", SqlDbType.Int),
			new SqlParameter("@PageIndex", SqlDbType.Int),
			new SqlParameter("@IsReCount", SqlDbType.Int),
			new SqlParameter("@OrderType", SqlDbType.Bit),
			new SqlParameter("@strWhere", SqlDbType.VarChar)
		};
		array[0].Value = "wap_message";
		array[1].Value = ShowFldName;
		array[2].Value = OrderfldName;
		array[3].Value = "";
		array[4].Value = TotalCount;
		array[5].Value = PageSize;
		array[6].Value = PageIndex;
		array[7].Value = 0;
		array[8].Value = OrderType;
		array[9].Value = strWhere;
		return DbHelperSQL.ExecuteDataset(b, CommandType.Text, stringBuilder.ToString(), array);
	}

	public List<wap_message_Model> GetListVo(long PageSize, long PageIndex, string strWhere, string ShowFldName, string OrderfldName, long TotalCount, long OrderType)
	{
		bool flag = default(bool);
		IEnumerator enumerator = default(IEnumerator);
		List<wap_message_Model> result = default(List<wap_message_Model>);
		while (true)
		{
			StringBuilder stringBuilder = new StringBuilder();
			List<wap_message_Model> list = new List<wap_message_Model>();
			stringBuilder.Append("exec UP_GetRecordByPageOrder @tblName,@fldName,@OrderfldName,@StatfldName,@TotalCount,@PageSize,@PageIndex,@IsReCount,@OrderType,@strWhere");
			SqlParameter[] array = new SqlParameter[10]
			{
				new SqlParameter("@tblName", SqlDbType.VarChar),
				new SqlParameter("@fldName", SqlDbType.VarChar),
				new SqlParameter("@OrderfldName", SqlDbType.VarChar),
				new SqlParameter("@StatfldName", SqlDbType.VarChar),
				new SqlParameter("@TotalCount", SqlDbType.Int),
				new SqlParameter("@PageSize", SqlDbType.Int),
				new SqlParameter("@PageIndex", SqlDbType.Int),
				new SqlParameter("@IsReCount", SqlDbType.Int),
				new SqlParameter("@OrderType", SqlDbType.Bit),
				new SqlParameter("@strWhere", SqlDbType.VarChar)
			};
			array[0].Value = "wap_message";
			array[1].Value = ShowFldName;
			array[2].Value = OrderfldName;
			array[3].Value = "";
			array[4].Value = TotalCount;
			array[5].Value = PageSize;
			array[6].Value = PageIndex;
			array[7].Value = 0;
			array[8].Value = OrderType;
			array[9].Value = strWhere;
			DataSet dataSet = DbHelperSQL.ExecuteDataset(b, CommandType.Text, stringBuilder.ToString(), array);
			int num = 0;
			while (true)
			{
				int num2;
				switch (num)
				{
				case 0:
					if (dataSet != null)
					{
						num = 3;
						continue;
					}
					goto IL_04ad;
				case 1:
					try
					{
						num = 4;
						while (true)
						{
							switch (num)
							{
							default:
								flag = enumerator.MoveNext();
								num = 0;
								continue;
							case 0:
							{
								if (!flag)
								{
									num = 2;
									continue;
								}
								DataRow dataRow = (DataRow)enumerator.Current;
								wap_message_Model wap_message_Model = new wap_message_Model();
								wap_message_Model.id = long.Parse(dataRow["id"].ToString());
								wap_message_Model.siteid = long.Parse(dataRow["siteid"].ToString());
								wap_message_Model.userid = long.Parse(dataRow["userid"].ToString());
								wap_message_Model.nickname = dataRow["nickname"].ToString();
								wap_message_Model.title = dataRow["title"].ToString();
								wap_message_Model.content = dataRow["content"].ToString();
								wap_message_Model.touserid = long.Parse(dataRow["touserid"].ToString());
								wap_message_Model.isnew = long.Parse(dataRow["isnew"].ToString());
								wap_message_Model.issystem = long.Parse(dataRow["issystem"].ToString());
								wap_message_Model.addtime = DateTime.Parse(dataRow["addtime"].ToString());
								list.Add(wap_message_Model);
								num = 1;
								continue;
							}
							case 2:
								num = 3;
								continue;
							case 3:
								break;
							}
							break;
						}
					}
					finally
					{
						while (true)
						{
							IL_0391:
							if (true)
							{
							}
							IDisposable disposable = enumerator as IDisposable;
							flag = disposable == null;
							num = 2;
							while (true)
							{
								switch (num)
								{
								case 2:
									if (!flag)
									{
										num = 1;
										continue;
									}
									goto end_IL_037c;
								case 1:
									disposable.Dispose();
									num = 0;
									continue;
								case 0:
									goto end_IL_037c;
								}
								goto IL_0391;
								continue;
								end_IL_037c:
								break;
							}
							break;
						}
					}
					result = list;
					num = 10;
					continue;
				case 2:
					result = null;
					num = 6;
					continue;
				case 7:
					num = 8;
					continue;
				case 8:
					num2 = ((dataSet.Tables[0].Rows.Count > 0) ? 1 : 0);
					goto IL_0486;
				case 3:
					num = 9;
					continue;
				case 9:
					if (dataSet.Tables.Count > 0)
					{
						num = 7;
						continue;
					}
					goto IL_04ad;
				case 5:
					if (flag)
					{
						enumerator = dataSet.Tables[0].Rows.GetEnumerator();
						num = 1;
					}
					else
					{
						num = 2;
					}
					continue;
				case 4:
					num2 = 0;
					goto IL_0486;
				case 6:
				case 10:
					{
						return result;
					}
					IL_04ad:
					num = 4;
					continue;
					IL_0486:
					flag = (byte)num2 != 0;
					num = 5;
					continue;
				}
				break;
			}
		}
	}
}