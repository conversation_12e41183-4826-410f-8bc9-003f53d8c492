{{#if Message.HasMessage}}
{{#unless Message.IsSuccess}}
<div class="message {{Message.Type}}">
    {{Message.Content}}
</div>
{{/unless}}
{{/if}}

<!-- 当前身份显示 -->
<div class="card mb-4">
    <div class="card-body">
        <div class="flex justify-between items-center py-2">
            <div class="text-sm text-text-secondary flex items-center">
                <i data-lucide="user" class="w-4 h-4 mr-1"></i>
                我当前身份
            </div>
            <div class="font-medium text-text-primary text-right">
                <span class="inline-flex items-center py-1 px-2 rounded text-xs font-medium bg-gray-100 text-gray-600 current-identity">{{{CurrentIdentity.IdentityHtml}}}</span>
            </div>
        </div>
    </div>
</div>

<!-- 身份选项网格 -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-4 mx-4">
    {{#each IdentityOptions}}
    <div class="bg-white rounded-xl overflow-hidden shadow-sm border border-border-light hover:shadow-md transition-all duration-200 flex flex-col {{#if IsColorNickname}}color-nickname-card{{/if}}" data-identity-id="{{Id}}">
        <!-- 卡片头部 -->
        <div class="p-4 border-b border-border-light flex items-center justify-between">
            <div class="flex items-center">
                <span class="font-semibold text-lg identity-name {{NameCssClass}}">{{DisplayName}}</span>
                {{#if IconUrl}}
                <img src="{{IconUrl}}" class="ml-2" alt="{{Name}}图标">
                {{/if}}
            </div>
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{BadgeCssClass}}">
                <i data-lucide="{{BadgeIcon}}" class="w-3 h-3 mr-1"></i>
                {{BadgeText}}
            </span>
        </div>

        <!-- 卡片主体 -->
        <div class="p-4 flex-1">
            <!-- 价格显示 -->
            <div class="mb-4">
                <div class="text-xl font-semibold text-primary mb-1">¥{{Price}} / {{Period}}</div>
                <div class="text-sm text-text-secondary">或 {{formatNumber CoinPrice}}妖晶 / {{Period}}</div>
            </div>

            {{#if IsColorNickname}}
            <!-- 颜色选择器 -->
            <div class="mb-4">
                <div class="color-name-display mb-3">
                    <span class="color-name-text text-base font-medium red-text">红色昵称</span>
                </div>
                <div class="flex flex-wrap gap-2">
                    {{#each ColorOptions}}
                    <div class="color-option w-8 h-8 rounded-full cursor-pointer border-2 border-transparent hover:scale-105 transition-transform {{#if IsDefault}}selected{{/if}} color-{{Color}}"
                         data-color="{{Color}}"
                         data-name="{{Name}}"
                         data-target-id="{{TargetId}}">
                    </div>
                    {{/each}}
                </div>
            </div>
            {{else}}
            <!-- 特权列表 -->
            {{#if Privileges}}
            <div class="mb-4">
                <div class="flex items-center mb-2 text-sm font-medium text-text-secondary">
                    <i data-lucide="award" class="w-4 h-4 mr-1 text-primary"></i>
                    特权
                </div>
                <div class="space-y-1">
                    {{#each Privileges}}
                    <div class="flex items-center text-sm text-text-secondary">
                        <i data-lucide="check" class="w-3 h-3 mr-2 text-primary flex-shrink-0"></i>
                        <span>{{this}}</span>
                    </div>
                    {{/each}}
                </div>
            </div>
            {{/if}}
            {{/if}}
        </div>

        <!-- 卡片底部 -->
        <div class="p-4 border-t border-border-light">
            <button class="btn btn-primary w-full purchase-button-new" data-identity-id="{{Id}}" data-is-color="{{IsColorNickname}}">
                <i data-lucide="shopping-cart" class="w-4 h-4 mr-2"></i>
                购买
            </button>
        </div>
    </div>
    {{/each}}
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // 初始化图标
        lucide.createIcons();

        // 处理当前身份颜色显示
        setupCurrentIdentityColor();

        // 设置颜色选择器
        setupColorSelector();

        // 设置购买按钮
        setupPurchaseButtons();
    });

    // 处理当前身份颜色显示
    function setupCurrentIdentityColor() {
        // 身份颜色映射 - 包含文字颜色和背景色（与MyFile.hbs保持一致）
        const colorMap = {
            '绿色昵称': { color: '#25a444', bgColor: '#f0f9f4' },
            '红色昵称': { color: '#FF0000', bgColor: '#fef2f2' },
            '蓝色昵称': { color: '#228aff', bgColor: '#eff6ff' },
            '紫色昵称': { color: '#c000ff', bgColor: '#faf5ff' },
            '粉色昵称': { color: '#ff6363', bgColor: '#fef7f7' },
            '粉紫昵称': { color: '#ff00c0', bgColor: '#fdf2f8' },
            '灰色昵称': { color: '#BDBDBD', bgColor: '#f9fafb' },
            '红名VIP': { color: '#FF0000', bgColor: '#fef2f2' },
            '年费VIP': { color: '#c000ff', bgColor: '#faf5ff' },
            '靓': { color: '#FF7F00', bgColor: '#fff7ed' },
            '帅': { color: '#228aff', bgColor: '#eff6ff' },
            '金名VIP': { color: '#fa6700', bgColor: '#fff7ed' }
        };

        function processIdentityElement(identityElement) {
            if (identityElement) {
                const identityText = identityElement.innerHTML;

                // 检查是否是普通会员
                if (identityText.includes('普通会员')) {
                    // 普通会员保持默认的灰色样式，不需要特殊处理
                    return;
                }

                // 检查是否有图标
                const imgElement = identityElement.querySelector('img');
                if (imgElement) {
                    // 图标身份：移除所有背景样式，只显示图标
                    identityElement.classList.remove('bg-gray-100', 'text-gray-600', 'py-1', 'px-2', 'rounded', 'text-xs', 'font-medium');
                    identityElement.style.backgroundColor = 'transparent';
                    identityElement.style.padding = '0';
                    identityElement.style.border = 'none';
                    return;
                }

                // 处理彩色昵称
                const regex = /^(绿色昵称|红色昵称|蓝色昵称|紫色昵称|粉色昵称|粉紫昵称|灰色昵称)/;
                const match = identityText.match(regex);

                if (match && colorMap[match[0]]) {
                    const colors = colorMap[match[0]];
                    const coloredText = `<span style="color: ${colors.color}">${match[0]}</span>`;
                    identityElement.innerHTML = identityText.replace(match[0], coloredText);
                    // 应用对应的背景色和样式
                    applyIdentityStyle(identityElement, colors);
                }
            }
        }

        function applyIdentityStyle(element, colors) {
            // 移除普通会员的样式
            element.classList.remove('bg-gray-100', 'text-gray-600');
            // 应用对应身份的样式
            element.style.color = colors.color;
            element.style.backgroundColor = colors.bgColor;
            element.style.border = `1px solid ${colors.color}20`; // 20% 透明度的边框
            element.style.padding = '2px 6px';
            element.style.borderRadius = '4px';
            element.style.fontSize = '12px';
            element.style.fontWeight = '500';
            element.style.display = 'inline-block';
        }

        const currentIdentityElement = document.querySelector('.current-identity');
        processIdentityElement(currentIdentityElement);
    }

    // 设置颜色选择器
    function setupColorSelector() {
        const colorOptions = document.querySelectorAll('.color-option');
        const colorNameText = document.querySelector('.color-name-text');

        colorOptions.forEach(option => {
            option.addEventListener('click', () => {
                // 移除其他选中状态
                colorOptions.forEach(opt => {
                    opt.classList.remove('selected');
                });

                // 添加选中状态
                option.classList.add('selected');

                // 更新显示的颜色名称
                const colorName = option.dataset.name;
                const color = option.dataset.color;

                if (colorNameText) {
                    colorNameText.textContent = colorName;
                    colorNameText.className = 'color-name-text text-base font-medium';

                    // 添加对应的颜色类
                    if (color === 'purple') {
                        colorNameText.classList.add('basic-purple-text');
                    } else {
                        colorNameText.classList.add(`${color}-text`);
                    }
                }
            });
        });
    }

    // 设置购买按钮
    function setupPurchaseButtons() {
        const purchaseButtons = document.querySelectorAll('.purchase-button-new');

        purchaseButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                const identityId = button.dataset.identityId;
                const isColorNickname = button.dataset.isColor === 'True';
                let targetId;

                if (isColorNickname) {
                    // 在当前卡片内查找选中的颜色选项
                    const currentCard = button.closest('.color-nickname-card');
                    let selectedColorOption = null;

                    // 遍历当前卡片内的所有颜色选项，查找选中的选项
                    if (currentCard) {
                        const allColorOptionsInCard = currentCard.querySelectorAll('.color-option');
                        allColorOptionsInCard.forEach((opt) => {
                            if (opt.classList.contains('selected')) {
                                selectedColorOption = opt;
                            }
                        });
                    } else {
                        selectedColorOption = document.querySelector('.color-option.selected');
                    }

                    if (selectedColorOption) {
                        targetId = selectedColorOption.dataset.targetId;
                    } else {
                        alert('请选择一个颜色');
                        return;
                    }
                } else {
                    // 根据身份ID映射到正确的toid
                    const identityToIdMap = {
                        '101': '101',    // 红名VIP
                        '358': '358',    // 金名VIP
                        '105': '105',    // 紫名年费VIP
                        '140': '140',    // 金名靓
                        '180': '180'     // 蓝名帅
                    };
                    targetId = identityToIdMap[identityId] || identityId;
                }

                if (targetId) {
                    const finalToid = Number(targetId);
                    const purchaseUrl = `/bbs/togroupcoinbuy.aspx?toid=${finalToid}`;
                    window.location.href = purchaseUrl;
                } else {
                    alert('购买参数错误');
                }
            });
        });
    }
</script>

<style>
    /* 身份名称颜色样式 - 使用原版HTML的颜色值 */
    .text-red-vip {
        color: #FF0000;
    }

    .text-gold-vip {
        color: #fa6700;
    }

    .text-purple-vip {
        color: #c000ff;
    }

    .text-blue-vip {
        color: #228aff;
    }

    /* 颜色昵称选择器颜色样式 */
    .green-text {
        color: #25a444;
    }

    .red-text {
        color: #FF0000;
    }

    .blue-text {
        color: #228aff;
    }

    .purple-text {
        color: #c000ff;
    }

    .pink-text {
        color: #ff6363;
    }

    .pink-purple-text {
        color: #ff00c0;
    }

    .basic-purple-text {
        color: #9c63ce;
    }

    /* 颜色选择器样式 */
    .color-green {
        background-color: #25a444;
    }

    .color-red {
        background-color: #FF0000;
    }

    .color-blue {
        background-color: #228aff;
    }

    .color-purple {
        background-color: #9c63ce;
    }

    .color-pink {
        background-color: #ff6363;
    }

    .color-pink-purple {
        background-color: #ff00c0;
    }

    .color-option.selected {
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.8), 0 0 0 4px rgba(31, 41, 55, 0.3);
    }

        .color-option.selected::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 1rem;
            height: 1rem;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='white' stroke-width='3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

    .color-option {
        position: relative;
    }
</style>