USE [NETOK]
GO
/****** Object:  Table [dbo].[bbs_guessing]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[bbs_guessing](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[bbs_id] [bigint] NOT NULL,
	[title] [nvarchar](200) NOT NULL,
	[options] [nvarchar](max) NOT NULL,
	[deadline] [datetime] NOT NULL,
	[is_closed] [bit] NULL,
	[result_option_id] [int] NULL,
	[created_at] [datetime] NULL,
	[updated_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[bbs_guessing_bets]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[bbs_guessing_bets](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[guessing_id] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[option_id] [int] NOT NULL,
	[amount] [decimal](18, 2) NOT NULL,
	[created_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[chinaBank_order]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[chinaBank_order](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[order_id] [nvarchar](50) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[sitedates] [bigint] NULL,
	[sitespace] [bigint] NULL,
	[money] [bigint] NOT NULL,
	[add_time] [datetime] NOT NULL,
	[oper_userid] [bigint] NOT NULL,
	[valid] [int] NULL,
	[valid_time] [datetime] NULL,
	[valid_info] [nvarchar](1000) NULL,
	[v_pmode] [nvarchar](1000) NULL,
	[md5info] [nvarchar](1000) NULL,
	[bankType] [int] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_chinaBank_order] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[chinabank_WAP_Config]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[chinabank_WAP_Config](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[p1_MerId] [nvarchar](50) NOT NULL,
	[merchantKey] [nvarchar](100) NOT NULL,
	[Tomoney] [int] NOT NULL,
	[isclose] [int] NOT NULL,
	[remark] [nvarchar](500) NULL,
	[addtime] [datetime] NOT NULL,
	[bankType] [int] NOT NULL,
	[email] [nvarchar](50) NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_chinabank_WAP_Config] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[chinabank_wap_order]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[chinabank_wap_order](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[orderID] [nvarchar](50) NOT NULL,
	[Amount] [money] NOT NULL,
	[cardNO] [nvarchar](50) NOT NULL,
	[cardPwd] [nvarchar](50) NOT NULL,
	[FrpID] [nvarchar](50) NOT NULL,
	[Code] [nvarchar](50) NOT NULL,
	[CodeInfo] [nvarchar](50) NULL,
	[addtime] [datetime] NOT NULL,
	[bankType] [int] NOT NULL,
	[HangBiaoShi] [int] NULL,
	[leftmoney] [money] NULL,
	[opera_userid] [bigint] NULL,
	[opera_nickname] [nvarchar](50) NULL,
	[IP] [nvarchar](50) NULL,
 CONSTRAINT [PK_chinabank_wap_order] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[class]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[class](
	[classid] [bigint] IDENTITY(1,1) NOT NULL,
	[childid] [bigint] NULL,
	[userid] [bigint] NOT NULL,
	[classname] [nvarchar](50) NOT NULL,
	[typeid] [int] NULL,
	[position] [nvarchar](50) NULL,
	[smallimg] [ntext] NULL,
	[siteimg] [nvarchar](255) NULL,
	[sitelist] [int] NULL,
	[siterowremark] [ntext] NULL,
	[sitedowntip] [ntext] NULL,
	[hits] [bigint] NULL,
	[articlenum] [int] NULL,
	[adminusername] [nvarchar](200) NULL,
	[introduce] [ntext] NULL,
	[rank] [bigint] NULL,
	[creatdate] [smalldatetime] NULL,
	[ismodel] [smallint] NULL,
	[ishidden] [smallint] NULL,
	[password] [nvarchar](50) NULL,
	[needMoney] [nvarchar](10) NULL,
	[subMoney] [nvarchar](10) NULL,
	[allowUser] [nvarchar](500) NULL,
	[topicID] [nvarchar](50) NULL,
	[bbsFace] [ntext] NULL,
	[bbsType] [ntext] NULL,
	[total] [bigint] NOT NULL,
	[HangBiaoShi] [int] NULL,
	[isCheck] [int] NOT NULL,
 CONSTRAINT [PK_class] PRIMARY KEY CLUSTERED 
(
	[classid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[DomainName]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[DomainName](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[domain] [nvarchar](100) NULL,
	[realpath] [nvarchar](255) NULL,
	[length] [int] NULL,
	[isJump] [int] NULL,
	[filter] [nvarchar](4000) NULL,
	[title] [nvarchar](255) NULL,
	[siteimg] [nvarchar](255) NULL,
	[sitebanner] [nvarchar](255) NULL,
	[email] [nvarchar](255) NULL,
	[tel] [nvarchar](50) NULL,
	[copyright] [nvarchar](4000) NULL,
	[siteid] [bigint] NULL,
	[sitespace] [bigint] NULL,
	[isUseDNS] [int] NULL,
	[siteDates] [bigint] NULL,
	[isCheck] [smallint] NULL,
	[isClose] [smallint] NULL,
	[remark] [nvarchar](500) NULL,
	[HangBiaoShi] [int] NULL,
	[infolock] [ntext] NULL,
	[infoclose] [ntext] NULL,
	[infodomainname] [ntext] NULL,
 CONSTRAINT [PK_DomainName] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[favdetail]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[favdetail](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[favtypeid] [int] NULL,
	[title] [nvarchar](255) NULL,
	[url] [nvarchar](255) NULL,
	[adddate] [smalldatetime] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_favdetail] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[favsubject]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[favsubject](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NULL,
	[userid] [bigint] NULL,
	[subjectname] [nvarchar](50) NULL,
	[subjectlognum] [int] NULL,
	[ordernum] [int] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_favsubject] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[fcount]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[fcount](
	[fid] [bigint] IDENTITY(1,1) NOT NULL,
	[fyear] [nvarchar](50) NULL,
	[fmonth] [nvarchar](50) NULL,
	[fweek] [nvarchar](50) NULL,
	[fday] [nvarchar](50) NULL,
	[fhour] [nvarchar](50) NULL,
	[fip] [nvarchar](200) NULL,
	[fwindows] [nvarchar](50) NULL,
	[fsystem] [nvarchar](50) NULL,
	[furl] [nvarchar](250) NULL,
	[ftime] [smalldatetime] NULL,
	[fweeknum] [nvarchar](50) NULL,
	[fuserid] [bigint] NULL,
	[fuser] [nvarchar](50) NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](50) NULL,
	[classid] [bigint] NULL,
	[classname] [ntext] NULL,
	[SubMoneyFlag] [ntext] NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[fcount_refer]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[fcount_refer](
	[refertype] [varchar](12) NULL,
	[fvalue] [varchar](2) NULL,
	[fvalueDec] [varchar](8) NULL,
	[OrderID] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Form_Data]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Form_Data](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NULL,
	[classid] [bigint] NULL,
	[bookID] [bigint] NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](50) NULL,
	[FormData] [ntext] NULL,
	[addtime] [datetime] NULL,
	[HangBiaoShi] [int] NULL,
	[rnd] [nvarchar](6) NULL,
	[rndTime] [datetime] NULL,
	[isCheck] [int] NULL,
	[fromlinkid] [bigint] NOT NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Form_List]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Form_List](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [nvarchar](1000) NULL,
	[book_content] [nvarchar](2000) NULL,
	[book_type] [nvarchar](1) NULL,
	[book_submit] [bigint] NULL,
	[submitName] [nvarchar](10) NULL,
	[toEmail] [nvarchar](200) NULL,
	[MakerID] [bigint] NULL,
	[addtime] [datetime] NULL,
	[ischeck] [int] NULL,
	[HangBiaoShi] [int] NULL,
	[toMobile] [nvarchar](20) NULL,
	[toContent] [nvarchar](200) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[Form_List_Detail]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[Form_List_Detail](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NULL,
	[bookID] [bigint] NULL,
	[rank] [int] NULL,
	[formName] [nvarchar](1000) NULL,
	[formType] [int] NULL,
	[formLenth] [int] NULL,
	[formText] [nvarchar](2000) NULL,
	[formNeed] [int] NULL,
	[VoteCount] [bigint] NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[home]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[home](
	[h_id] [int] IDENTITY(1,1) NOT NULL,
	[homeurl] [nvarchar](100) NULL,
	[hometitle] [nvarchar](50) NULL,
	[homeemail] [nvarchar](50) NULL,
	[homebanzhu] [nvarchar](50) NULL,
	[homepassword] [nvarchar](50) NULL,
	[homecontent] [nvarchar](1000) NULL,
	[cookies_time] [int] NULL,
	[t_page] [int] NULL,
	[scount] [int] NULL,
	[stime] [int] NULL,
	[onlinetime] [int] NULL,
	[maxcount] [int] NULL,
	[sopen] [int] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_home] PRIMARY KEY CLUSTERED 
(
	[h_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sendSMS]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sendSMS](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[sms_mobile] [nvarchar](15) NOT NULL,
	[sms_content] [nvarchar](140) NOT NULL,
	[sendtime] [datetime] NOT NULL,
	[state] [int] NOT NULL,
	[addtime] [datetime] NOT NULL,
	[HangBiaoShi] [int] NULL,
	[QR_img] [ntext] NULL,
	[QR_check_state] [int] NOT NULL,
	[QR_check_time] [datetime] NULL,
	[actionType] [bigint] NOT NULL,
 CONSTRAINT [PK_wap_sendSMS] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sitetype]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sitetype](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[classname] [nvarchar](50) NULL,
	[rank] [int] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_sitetype] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sys_ad_show]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sys_ad_show](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[systype] [nvarchar](20) NOT NULL,
	[secondShowTop] [ntext] NULL,
	[secondShowDown] [ntext] NULL,
	[threeShowTop] [ntext] NULL,
	[threeShowDown] [ntext] NULL,
	[makerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sys_check_ip]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sys_check_ip](
	[ip] [nvarchar](50) NOT NULL,
	[lasttime] [datetime] NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_sys_check_ip] PRIMARY KEY CLUSTERED 
(
	[ip] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sys_role_class]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sys_role_class](
	[role_id] [bigint] NOT NULL,
	[classid] [bigint] NOT NULL,
	[siteid] [bigint] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sys_role_info]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sys_role_info](
	[role_id] [bigint] IDENTITY(1,1) NOT NULL,
	[role_name] [nvarchar](50) NOT NULL,
	[role_remark] [nvarchar](500) NULL,
	[siteid] [bigint] NOT NULL,
	[addtime] [datetime] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_sys_role_info] PRIMARY KEY CLUSTERED 
(
	[role_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sys_user_role]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sys_user_role](
	[user_id] [bigint] NOT NULL,
	[role_id] [bigint] NOT NULL,
	[siteid] [bigint] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sys_wap_all_type]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sys_wap_all_type](
	[typeid] [bigint] IDENTITY(1,1) NOT NULL,
	[typename] [nvarchar](100) NOT NULL,
	[addtime] [datetime] NULL,
	[creater] [nvarchar](100) NULL,
	[rank] [bigint] NULL,
	[systype] [nvarchar](50) NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sys_wap_bbs]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sys_wap_bbs](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[username] [nvarchar](50) NULL,
	[book_classid] [bigint] NULL,
	[book_title] [nvarchar](500) NULL,
	[book_author] [nvarchar](50) NULL,
	[book_pub] [nvarchar](50) NULL,
	[book_content] [ntext] NULL,
	[book_date] [smalldatetime] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_sys_wap_bbs] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sys_wap_book]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sys_wap_book](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[username] [nvarchar](50) NULL,
	[book_classid] [bigint] NULL,
	[book_title] [nvarchar](500) NULL,
	[book_author] [nvarchar](50) NULL,
	[book_pub] [nvarchar](50) NULL,
	[book_content] [ntext] NULL,
	[book_date] [smalldatetime] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_sys_wap_book] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sys_wap_download]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sys_wap_download](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[username] [nvarchar](50) NULL,
	[book_classid] [bigint] NULL,
	[book_title] [nvarchar](500) NULL,
	[book_author] [nvarchar](50) NULL,
	[book_ext] [nvarchar](10) NULL,
	[book_size] [nvarchar](50) NULL,
	[book_img] [ntext] NULL,
	[book_file] [nvarchar](500) NULL,
	[book_content] [ntext] NULL,
	[book_date] [smalldatetime] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_sys_wap_download] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sys_wap_files]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sys_wap_files](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[username] [nvarchar](50) NULL,
	[book_classid] [bigint] NOT NULL,
	[book_title] [nvarchar](200) NULL,
	[book_ext] [nvarchar](10) NULL,
	[book_size] [nvarchar](50) NULL,
	[book_file] [nvarchar](200) NULL,
	[book_date] [datetime] NULL,
	[HangBiaoShi] [int] NULL,
	[isCheck] [int] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sys_wap_message]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sys_wap_message](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[username] [nvarchar](50) NULL,
	[msg_title] [nvarchar](500) NULL,
	[msg_content] [ntext] NULL,
	[msg_date] [datetime] NULL,
	[valid_date] [datetime] NULL,
	[pop_flag] [int] NULL,
	[msg_type] [int] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_sys_wap_message] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sys_wap_picture]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sys_wap_picture](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[username] [nvarchar](50) NULL,
	[book_classid] [bigint] NULL,
	[book_title] [nvarchar](500) NULL,
	[book_author] [nvarchar](50) NULL,
	[book_ext] [nvarchar](10) NULL,
	[book_size] [nvarchar](50) NULL,
	[book_img] [nvarchar](500) NULL,
	[book_file] [nvarchar](500) NULL,
	[book_content] [ntext] NULL,
	[book_date] [smalldatetime] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_sys_wap_picture] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sys_wap_ring]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sys_wap_ring](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[username] [nvarchar](50) NULL,
	[book_classid] [bigint] NULL,
	[book_title] [nvarchar](500) NULL,
	[book_author] [nvarchar](50) NULL,
	[book_ext] [nvarchar](10) NULL,
	[book_size] [nvarchar](50) NULL,
	[book_img] [nvarchar](500) NULL,
	[book_file] [nvarchar](500) NULL,
	[book_content] [ntext] NULL,
	[book_date] [smalldatetime] NULL,
	[HangBiaoShi] [int] NULL,
	[book_director] [ntext] NULL,
	[book_city] [ntext] NULL,
	[book_year] [ntext] NULL,
	[book_lang] [ntext] NULL,
	[book_lable] [nvarchar](20) NULL,
	[book_score] [bigint] NULL,
	[makerid] [bigint] NULL,
 CONSTRAINT [PK_sys_wap_ring] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[sys_wap_video]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[sys_wap_video](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[username] [nvarchar](50) NULL,
	[book_classid] [bigint] NULL,
	[book_title] [nvarchar](500) NULL,
	[book_author] [nvarchar](50) NULL,
	[book_ext] [nvarchar](10) NULL,
	[book_size] [nvarchar](50) NULL,
	[book_img] [nvarchar](500) NULL,
	[book_file] [nvarchar](500) NULL,
	[book_content] [ntext] NULL,
	[book_date] [smalldatetime] NULL,
	[HangBiaoShi] [int] NULL,
	[book_director] [ntext] NULL,
	[book_city] [ntext] NULL,
	[book_year] [ntext] NULL,
	[book_lang] [ntext] NULL,
	[book_lable] [nvarchar](20) NULL,
	[book_score] [bigint] NULL,
	[makerid] [bigint] NULL,
 CONSTRAINT [PK_sys_wap_video] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[systype]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[systype](
	[typeid] [int] IDENTITY(1,1) NOT NULL,
	[typename] [nvarchar](50) NOT NULL,
	[typepath] [nvarchar](1000) NOT NULL,
	[type] [smallint] NULL,
	[remark] [nvarchar](1000) NULL,
	[createuserid] [bigint] NULL,
	[createdate] [smalldatetime] NULL,
	[rank] [int] NULL,
	[version] [smallint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_systype] PRIMARY KEY CLUSTERED 
(
	[typeid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[systype_siteid]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[systype_siteid](
	[siteid] [bigint] NOT NULL,
	[systype] [nvarchar](500) NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_systype_siteid] PRIMARY KEY CLUSTERED 
(
	[siteid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[uname]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[uname](
	[ID] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NULL,
	[userid] [bigint] NULL,
	[oname] [varchar](50) NULL,
	[nname] [varchar](50) NULL,
	[shou] [bigint] NULL,
	[jinbi] [bigint] NULL,
	[jingyan] [bigint] NULL,
	[time1] [datetime] NULL,
	[time2] [datetime] NULL,
	[content1] [ntext] NULL,
	[content2] [ntext] NULL,
	[type1] [bigint] NULL,
	[type2] [bigint] NULL,
	[zt] [bigint] NULL,
PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[user]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[user](
	[userid] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NULL,
	[username] [nvarchar](50) NULL,
	[nickname] [nvarchar](16) NOT NULL,
	[password] [nvarchar](20) NOT NULL,
	[managerlvl] [nvarchar](2) NULL,
	[sex] [smallint] NULL,
	[age] [smallint] NULL,
	[shenggao] [nvarchar](20) NULL,
	[tizhong] [nvarchar](20) NULL,
	[xingzuo] [nvarchar](20) NULL,
	[aihao] [nvarchar](100) NULL,
	[fenfuo] [nvarchar](10) NULL,
	[zhiye] [nvarchar](30) NULL,
	[city] [nvarchar](50) NULL,
	[mobile] [nvarchar](15) NULL,
	[email] [nvarchar](50) NULL,
	[money] [bigint] NULL,
	[moneyname] [ntext] NULL,
	[moneyregular] [ntext] NULL,
	[RegTime] [smalldatetime] NULL,
	[LastLoginIP] [nvarchar](20) NULL,
	[LastLoginTime] [smalldatetime] NULL,
	[LoginTimes] [bigint] NULL,
	[LockUser] [smallint] NOT NULL,
	[headimg] [nvarchar](100) NULL,
	[remark] [nvarchar](50) NULL,
	[sitename] [nvarchar](50) NULL,
	[siteimg] [nvarchar](100) NULL,
	[siteuptip] [ntext] NULL,
	[sitedowntip] [ntext] NULL,
	[siteposition] [nvarchar](10) NULL,
	[siterowremark] [ntext] NULL,
	[sitelistflag] [smallint] NULL,
	[sitelist] [smallint] NULL,
	[sitetype] [int] NULL,
	[MaxPerPage_Default] [smallint] NULL,
	[MaxPerPage_Content] [smallint] NULL,
	[MaxFileSize] [int] NULL,
	[SaveUpFilesPath] [nvarchar](20) NULL,
	[UpFileType] [ntext] NULL,
	[CharFilter] [ntext] NULL,
	[UAFilter] [ntext] NULL,
	[SessionTimeout] [bigint] NULL,
	[MailServer] [ntext] NULL,
	[MailServerUserName] [nvarchar](15) NULL,
	[MailServerPassWord] [nvarchar](10) NULL,
	[sitemoneyname] [nvarchar](10) NULL,
	[sitespace] [bigint] NULL,
	[myspace] [bigint] NULL,
	[siteRight] [smallint] NULL,
	[SidTimeOut] [nvarchar](16) NULL,
	[lvlNumer] [ntext] NULL,
	[lvlTimeImg] [ntext] NULL,
	[lvlRegular] [ntext] NULL,
	[myBankMoney] [bigint] NOT NULL,
	[myBankTime] [datetime] NULL,
	[chuiNiu] [nvarchar](1) NULL,
	[expR] [bigint] NOT NULL,
	[endTime] [datetime] NULL,
	[version] [ntext] NULL,
	[RMB] [money] NOT NULL,
	[siteVIP] [nchar](1) NOT NULL,
	[ZoneCount] [bigint] NOT NULL,
	[HangBiaoShi] [int] NULL,
	[isCheck] [smallint] NULL,
	[bbsCount] [int] NOT NULL,
	[bbsReCount] [int] NOT NULL,
	[actionTime] [datetime] NULL,
	[actionState] [nvarchar](1) NULL,
	[TJCount] [int] NOT NULL,
	[LastNickChangeDate] [datetime] NULL,
 CONSTRAINT [PK_user] PRIMARY KEY CLUSTERED 
(
	[userid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[user_Info]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[user_Info](
	[userid] [bigint] NOT NULL,
	[siteid] [bigint] NOT NULL,
	[purpost] [ntext] NULL,
	[name] [ntext] NULL,
	[sex] [ntext] NULL,
	[age] [ntext] NULL,
	[CM] [ntext] NULL,
	[KG] [ntext] NULL,
	[city] [ntext] NULL,
	[birthday] [ntext] NULL,
	[nation] [ntext] NULL,
	[star] [ntext] NULL,
	[blood] [ntext] NULL,
	[education] [ntext] NULL,
	[profession] [ntext] NULL,
	[monthpay] [ntext] NULL,
	[religion] [ntext] NULL,
	[love] [ntext] NULL,
	[nature] [ntext] NULL,
	[looks] [ntext] NULL,
	[marriage] [ntext] NULL,
	[QQ] [ntext] NULL,
	[Email] [ntext] NULL,
	[speciality] [ntext] NULL,
	[condition] [ntext] NULL,
	[aphorism] [ntext] NULL,
	[loveClothes] [ntext] NULL,
	[loveStar] [ntext] NULL,
	[loveAnimal] [ntext] NULL,
	[loveFood] [ntext] NULL,
	[loveColor] [ntext] NULL,
	[loveMusic] [ntext] NULL,
	[other] [ntext] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[user_lock]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[user_lock](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[lockuserid] [bigint] NOT NULL,
	[lockdate] [bigint] NOT NULL,
	[operdate] [datetime] NOT NULL,
	[operuserid] [bigint] NOT NULL,
	[classid] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_user_lock] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[user_regcode]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[user_regcode](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[regcode] [nvarchar](8) NOT NULL,
	[userid] [bigint] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_user_regcode] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[UserPreferences]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[UserPreferences](
	[UserId] [bigint] NOT NULL,
	[NewReplyUIEnabled] [bit] NOT NULL,
 CONSTRAINT [PK_UserPreferences] PRIMARY KEY CLUSTERED 
(
	[UserId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[vcount]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[vcount](
	[startcount] [bigint] NULL,
	[vid] [bigint] IDENTITY(1,1) NOT NULL,
	[vtoday] [bigint] NULL,
	[vyestaday] [bigint] NULL,
	[vtotal] [bigint] NULL,
	[vstarttime] [smalldatetime] NULL,
	[vdate] [smalldatetime] NULL,
	[vtotal1] [bigint] NULL,
	[vip] [nvarchar](100) NULL,
	[vmonth] [nvarchar](10) NULL,
	[vnowmonth] [bigint] NULL,
	[vpremonth] [bigint] NULL,
	[vyear] [nvarchar](10) NULL,
	[vnowyear] [bigint] NULL,
	[vweek] [bigint] NULL,
	[vweeknum] [nvarchar](50) NULL,
	[vuser] [nvarchar](50) NULL,
	[vpass] [nvarchar](50) NULL,
	[vemail] [nvarchar](50) NULL,
	[vtitle] [nvarchar](50) NULL,
	[vurl] [nvarchar](50) NULL,
	[vcontent] [nvarchar](50) NULL,
	[vbanzhu] [nvarchar](50) NULL,
	[vregtime] [smalldatetime] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_vcount] PRIMARY KEY CLUSTERED 
(
	[vid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_action]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_action](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[issystem] [int] NOT NULL,
	[actionID] [int] NOT NULL,
	[actionName] [nvarchar](50) NOT NULL,
	[actionPath] [nvarchar](200) NOT NULL,
	[num] [int] NOT NULL,
	[numFinish] [int] NOT NULL,
	[state] [int] NOT NULL,
	[per] [int] NOT NULL,
	[addtime] [datetime] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_adlink]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_adlink](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [nvarchar](100) NULL,
	[book_author] [nvarchar](50) NULL,
	[book_pub] [nvarchar](50) NULL,
	[book_content] [ntext] NULL,
	[book_re] [bigint] NULL,
	[book_click] [bigint] NULL,
	[book_date] [smalldatetime] NULL,
	[sendMoney] [int] NULL,
	[hasMoney] [int] NULL,
	[isCheck] [smallint] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_adlink] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_adlinkre]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_adlinkre](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[devid] [nvarchar](255) NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](100) NULL,
	[classid] [bigint] NULL,
	[bookid] [bigint] NULL,
	[content] [ntext] NULL,
	[redate] [smalldatetime] NULL,
	[myGetMoney] [int] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_adlinkre] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_airplane]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_airplane](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[classid] [bigint] NOT NULL,
	[planeltd] [nvarchar](255) NOT NULL,
	[smallimg] [nvarchar](100) NULL,
	[planeNum] [nvarchar](255) NOT NULL,
	[planeType] [nvarchar](255) NULL,
	[seatType] [nvarchar](50) NOT NULL,
	[money] [int] NOT NULL,
	[startCity] [nvarchar](50) NOT NULL,
	[endCity] [nvarchar](50) NOT NULL,
	[startTime] [datetime] NOT NULL,
	[endTime] [datetime] NOT NULL,
	[istop] [int] NULL,
	[isgood] [int] NULL,
	[remark] [nvarchar](4000) NULL,
	[hits] [bigint] NULL,
	[addtime] [datetime] NULL,
	[isCheck] [smallint] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_airplane] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_airplaneOrder]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_airplaneOrder](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[airplaneid] [bigint] NOT NULL,
	[airplanename] [nvarchar](255) NULL,
	[userid] [bigint] NULL,
	[username] [nvarchar](50) NOT NULL,
	[num] [int] NULL,
	[tel] [nvarchar](50) NOT NULL,
	[address] [nvarchar](255) NULL,
	[starttime] [nvarchar](50) NOT NULL,
	[content] [nvarchar](4000) NULL,
	[remark] [nvarchar](4000) NULL,
	[addtime] [datetime] NOT NULL,
	[state] [int] NOT NULL,
	[isCheck] [smallint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_airplaneOrder] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_album]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_album](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [nvarchar](500) NULL,
	[book_author] [nvarchar](50) NULL,
	[book_ext] [nvarchar](10) NULL,
	[book_size] [nvarchar](50) NULL,
	[book_img] [ntext] NULL,
	[book_file] [ntext] NULL,
	[book_content] [ntext] NULL,
	[book_re] [bigint] NULL,
	[book_click] [bigint] NULL,
	[book_date] [datetime] NULL,
	[isCheck] [smallint] NULL,
	[ishidden] [smallint] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_album] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_albumre]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_albumre](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[devid] [nvarchar](255) NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](100) NULL,
	[classid] [bigint] NULL,
	[bookid] [bigint] NULL,
	[content] [ntext] NULL,
	[redate] [smalldatetime] NULL,
	[isCheck] [smallint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_albumre] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_albumSubject]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_albumSubject](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NULL,
	[userid] [bigint] NULL,
	[subjectname] [nvarchar](50) NULL,
	[subjectlognum] [int] NULL,
	[ordernum] [int] NULL,
	[isCheck] [smallint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_albumSubject] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_bankLog]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_bankLog](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[actionName] [nvarchar](10) NULL,
	[money] [nvarchar](20) NOT NULL,
	[leftMoney] [nvarchar](20) NULL,
	[opera_userid] [bigint] NULL,
	[opera_nickname] [nvarchar](50) NULL,
	[remark] [nvarchar](200) NOT NULL,
	[ip] [nvarchar](30) NULL,
	[addtime] [datetime] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_bankLog] PRIMARY KEY NONCLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_bbs]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_bbs](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [nvarchar](200) NULL,
	[book_author] [nvarchar](50) NULL,
	[book_pub] [nvarchar](50) NULL,
	[book_content] [nvarchar](max) NULL,
	[book_re] [bigint] NULL,
	[book_click] [bigint] NULL,
	[book_date] [smalldatetime] NULL,
	[book_good] [int] NULL,
	[book_top] [int] NULL,
	[sysid] [bigint] NULL,
	[reDate] [datetime] NULL,
	[reShow] [int] NULL,
	[suport] [bigint] NULL,
	[oppose] [bigint] NULL,
	[topic] [bigint] NULL,
	[islock] [smallint] NULL,
	[isVote] [int] NULL,
	[whylock] [ntext] NULL,
	[sendMoney] [int] NULL,
	[hasMoney] [int] NULL,
	[isdown] [smallint] NULL,
	[isCheck] [smallint] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
	[viewType] [smallint] NULL,
	[viewMoney] [bigint] NULL,
	[freeMoney] [bigint] NULL,
	[freeleftMoney] [bigint] NULL,
	[freeRule] [ntext] NULL,
	[myGetMoney] [bigint] NOT NULL,
	[Type] [nvarchar](50) NULL,
	[book_img] [nvarchar](200) NULL,
	[MarkSixBetID] [bigint] NULL,
	[MarkSixWin] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_bbs_MarkSix]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_bbs_MarkSix](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[years] [nvarchar](4) NOT NULL,
	[period] [bigint] NULL,
	[ball1] [nvarchar](2) NULL,
	[ball2] [nvarchar](2) NULL,
	[ball3] [nvarchar](2) NULL,
	[ball4] [nvarchar](2) NULL,
	[ball5] [nvarchar](2) NULL,
	[ball6] [nvarchar](2) NULL,
	[ballTeMa] [nvarchar](2) NULL,
	[OpenTime] [datetime] NULL,
	[state] [int] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_bbs_MarkSix] PRIMARY KEY NONCLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_bbs_MarkSix_bet]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_bbs_MarkSix_bet](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[classid] [bigint] NOT NULL,
	[bbsid] [bigint] NOT NULL,
	[bookid] [bigint] NOT NULL,
	[years] [nvarchar](4) NOT NULL,
	[peroid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](50) NOT NULL,
	[types] [int] NOT NULL,
	[types_content] [nvarchar](200) NOT NULL,
	[num] [bigint] NOT NULL,
	[betTime] [datetime] NOT NULL,
	[betMoney] [bigint] NOT NULL,
	[GetMoney] [bigint] NOT NULL,
	[state] [int] NOT NULL,
	[attribute] [int] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_bbs_MarkSix_bet] PRIMARY KEY NONCLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_bbs_vote]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_bbs_vote](
	[vid] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NULL,
	[id] [bigint] NULL,
	[voteTitle] [nvarchar](2000) NULL,
	[voteClick] [bigint] NULL,
	[voteWho] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
	[whoVote] [ntext] NULL,
 CONSTRAINT [PK_wap_bbs_vote] PRIMARY KEY CLUSTERED 
(
	[vid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_bbsre]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_bbsre](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[devid] [nvarchar](255) NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](100) NULL,
	[classid] [bigint] NULL,
	[bookid] [bigint] NULL,
	[content] [nvarchar](max) NULL,
	[redate] [smalldatetime] NULL,
	[myGetMoney] [int] NULL,
	[book_top] [smallint] NOT NULL,
	[isCheck] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
	[isdown] [bigint] NULL,
	[reply] [bigint] NULL,
 CONSTRAINT [PK_wap_bbsre_id] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_book]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_book](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [ntext] NULL,
	[book_author] [ntext] NULL,
	[book_pub] [ntext] NULL,
	[book_content] [ntext] NULL,
	[book_re] [bigint] NULL,
	[book_click] [bigint] NULL,
	[book_date] [smalldatetime] NULL,
	[sysid] [bigint] NULL,
	[xi] [bigint] NOT NULL,
	[nu] [bigint] NOT NULL,
	[han] [bigint] NOT NULL,
	[isCheck] [smallint] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
	[book_file] [ntext] NULL,
	[book_fileInfo] [ntext] NULL,
	[book_img] [nvarchar](200) NULL,
 CONSTRAINT [PK_wap_book] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_bookre]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_bookre](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[devid] [nvarchar](255) NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](100) NULL,
	[classid] [bigint] NULL,
	[bookid] [bigint] NULL,
	[content] [ntext] NULL,
	[redate] [smalldatetime] NULL,
	[isCheck] [bigint] NULL,
	[OperateID] [bigint] NULL,
	[OperateDate] [datetime] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_bookre] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_car_list]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_car_list](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [int] NOT NULL,
	[car_name] [nvarchar](50) NOT NULL,
	[car_prise] [int] NOT NULL,
	[car_img] [nvarchar](500) NOT NULL,
	[car_type] [int] NOT NULL,
	[property_tire] [int] NOT NULL,
	[property_steady] [int] NOT NULL,
	[property_control] [int] NOT NULL,
	[property_power] [int] NOT NULL,
	[property_oilvolume] [int] NOT NULL,
	[buy_count] [int] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_car_race]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_car_race](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [int] NOT NULL,
	[userid] [int] NOT NULL,
	[nickname] [nvarchar](50) NOT NULL,
	[road] [tinyint] NOT NULL,
	[stake] [int] NOT NULL,
	[timeout] [bit] NOT NULL,
	[time] [datetime] NOT NULL,
	[score1] [nvarchar](50) NOT NULL,
	[score2] [nvarchar](50) NOT NULL,
	[car_id] [int] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_car_user]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_car_user](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [int] NOT NULL,
	[userid] [int] NOT NULL,
	[nickname] [nvarchar](50) NOT NULL,
	[car_id] [int] NOT NULL,
	[car_name] [nvarchar](50) NOT NULL,
	[car_img] [nvarchar](500) NOT NULL,
	[car_type] [tinyint] NOT NULL,
	[buy_time] [datetime] NOT NULL,
	[race_times] [int] NOT NULL,
	[race_win] [int] NOT NULL,
	[race_lost] [int] NOT NULL,
	[property_tire] [int] NOT NULL,
	[property_steady] [int] NOT NULL,
	[property_control] [int] NOT NULL,
	[property_power] [int] NOT NULL,
	[property_oilvolume] [int] NOT NULL,
	[car_default] [bit] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_card]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_card](
	[userid] [bigint] NOT NULL,
	[ltd] [nvarchar](500) NULL,
	[realname] [nvarchar](50) NULL,
	[nameID] [nvarchar](20) NULL,
	[zhiye] [nvarchar](50) NULL,
	[tel] [nvarchar](50) NULL,
	[mobile] [nvarchar](50) NULL,
	[email] [nvarchar](50) NULL,
	[website] [nvarchar](100) NULL,
	[address] [nvarchar](200) NULL,
	[post] [nvarchar](20) NULL,
	[sale] [nvarchar](2000) NULL,
	[bank] [nvarchar](200) NULL,
	[cardnum] [nvarchar](50) NULL,
	[addtime] [datetime] NULL,
	[HangBiaoShi] [int] NULL,
	[ishidden] [bigint] NOT NULL,
 CONSTRAINT [PK_wap_card] PRIMARY KEY CLUSTERED 
(
	[userid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_clan_list]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_clan_list](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [int] NOT NULL,
	[createid] [int] NOT NULL,
	[clan_name] [nvarchar](50) NOT NULL,
	[clan_memberCount] [int] NOT NULL,
	[clan_money] [int] NOT NULL,
	[clan_mark] [int] NOT NULL,
	[clan_createdate] [datetime] NOT NULL,
	[clan_joinmoney] [int] NOT NULL,
	[clan_img] [nvarchar](500) NOT NULL,
	[clan_notice] [nvarchar](200) NOT NULL,
	[clan_join] [bit] NOT NULL,
	[clan_bbs] [bigint] NOT NULL,
	[clan_chat] [bigint] NOT NULL,
	[HangBiaoShi] [int] NULL,
	[clan_maxMemberCount] [int] NOT NULL,
	[adminstrator] [ntext] NULL,
 CONSTRAINT [PK_wap_clan_list] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_clan_pk]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_clan_pk](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [int] NOT NULL,
	[userid] [int] NOT NULL,
	[jointime] [datetime] NOT NULL,
	[attacktime] [datetime] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_clan_pk] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_clan_pk_message]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_clan_pk_message](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [int] NOT NULL,
	[userid] [int] NOT NULL,
	[nickname] [nvarchar](200) NOT NULL,
	[douserid] [int] NOT NULL,
	[donickname] [nvarchar](200) NOT NULL,
	[time] [datetime] NOT NULL,
	[type] [tinyint] NOT NULL,
	[message] [nvarchar](400) NULL,
	[HangBiaoShi] [int] NULL,
	[clan_id] [int] NOT NULL,
 CONSTRAINT [PK_wap_clan_pk_message] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_clan_request]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_clan_request](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [int] NOT NULL,
	[userid] [int] NOT NULL,
	[doid] [int] NOT NULL,
	[clan_id] [int] NOT NULL,
	[request_state] [tinyint] NOT NULL,
	[request_message] [nvarchar](400) NOT NULL,
	[request_time] [datetime] NOT NULL,
	[request_type] [tinyint] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_clan_request] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_clan_setting]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_clan_setting](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [int] NOT NULL,
	[createmoney] [int] NOT NULL,
	[weekmenber] [int] NOT NULL,
	[power] [int] NOT NULL,
	[transfermoney] [int] NOT NULL,
	[needlvls] [int] NOT NULL,
	[HangBiaoShi] [int] NULL,
	[addMenberNeedMoney] [int] NOT NULL,
	[maxclan] [int] NOT NULL,
	[maxclanadd] [int] NOT NULL,
	[addPowerNeedMoney] [int] NOT NULL,
 CONSTRAINT [PK_wap_clan_setting] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_clan_user]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_clan_user](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [int] NOT NULL,
	[userid] [int] NOT NULL,
	[ismaster] [bit] NOT NULL,
	[clan_id] [int] NOT NULL,
	[clan_joindate] [datetime] NOT NULL,
	[power] [int] NOT NULL,
	[mark] [int] NOT NULL,
	[pk_attack] [int] NOT NULL,
	[pk_attacked] [int] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_clan_user] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_download]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_download](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [ntext] NULL,
	[book_author] [ntext] NULL,
	[book_ext] [nvarchar](10) NULL,
	[book_size] [nvarchar](50) NULL,
	[book_img] [ntext] NULL,
	[book_file] [ntext] NULL,
	[book_content] [ntext] NULL,
	[book_re] [bigint] NULL,
	[book_click] [bigint] NULL,
	[book_date] [smalldatetime] NULL,
	[sysid] [bigint] NULL,
	[money] [bigint] NULL,
	[book_down] [bigint] NOT NULL,
	[ding] [bigint] NOT NULL,
	[yiban] [bigint] NOT NULL,
	[cai] [bigint] NOT NULL,
	[isCheck] [bigint] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
	[book_imgTrue] [nvarchar](100) NULL,
	[updateInfo] [nvarchar](100) NULL,
	[softMoney] [nvarchar](100) NULL,
	[softSafe] [nvarchar](100) NULL,
	[softVer] [nvarchar](100) NULL,
	[softLtd] [nvarchar](100) NULL,
	[softLang] [nvarchar](100) NULL,
	[book_lable] [nvarchar](20) NULL,
	[book_top] [int] NOT NULL,
	[book_good] [int] NOT NULL,
	[book_recommend] [int] NOT NULL,
	[book_score] [bigint] NOT NULL,
 CONSTRAINT [PK_wap_download] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_downloadre]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_downloadre](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[devid] [nvarchar](255) NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](100) NULL,
	[classid] [bigint] NULL,
	[bookid] [bigint] NULL,
	[content] [ntext] NULL,
	[redate] [smalldatetime] NULL,
	[isCheck] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_downloadre] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_friends]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_friends](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[frienduserid] [bigint] NULL,
	[friendusername] [nvarchar](50) NULL,
	[friendnickname] [nvarchar](50) NULL,
	[rank] [int] NULL,
	[addtime] [datetime] NULL,
	[friendtype] [int] NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_fun_setting]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_fun_setting](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [int] NOT NULL,
	[marry_proposemoney] [int] NOT NULL,
	[marry_seekmoney] [int] NOT NULL,
	[marry_seektitle] [nvarchar](50) NULL,
	[marry_seekcontent] [nvarchar](1000) NULL,
	[car_racemoney] [int] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_game_chuiniu]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_game_chuiniu](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[txt] [nvarchar](200) NOT NULL,
	[d1] [nvarchar](100) NOT NULL,
	[d2] [nvarchar](100) NOT NULL,
	[zs] [int] NOT NULL,
	[fbz] [bigint] NOT NULL,
	[tzz] [bigint] NOT NULL,
	[fbdate] [datetime] NOT NULL,
	[zb] [int] NOT NULL,
	[tzcg] [int] NOT NULL,
	[siteid] [bigint] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_game_chuiniu] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_games_count]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_games_count](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](50) NOT NULL,
	[times] [bigint] NOT NULL,
	[counts] [bigint] NOT NULL,
	[gametype] [int] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_games] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_gongqiu]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_gongqiu](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [ntext] NULL,
	[book_author] [ntext] NULL,
	[book_pub] [ntext] NULL,
	[book_content] [ntext] NULL,
	[book_re] [bigint] NULL,
	[book_click] [bigint] NULL,
	[book_date] [datetime] NULL,
	[sysid] [bigint] NULL,
	[isCheck] [smallint] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_gongqiu] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_guangbo]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_guangbo](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](16) NOT NULL,
	[book_classid] [bigint] NOT NULL,
	[book_title] [nvarchar](30) NOT NULL,
	[book_content] [nvarchar](500) NOT NULL,
	[book_click] [bigint] NOT NULL,
	[addtime] [datetime] NOT NULL,
	[endtime] [datetime] NOT NULL,
	[ischeck] [int] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_guessbook]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_guessbook](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [ntext] NULL,
	[book_author] [ntext] NULL,
	[book_pub] [ntext] NULL,
	[book_content] [ntext] NULL,
	[book_re] [bigint] NULL,
	[book_click] [bigint] NULL,
	[book_date] [smalldatetime] NULL,
	[book_good] [int] NULL,
	[book_top] [int] NULL,
	[ishidden] [smallint] NULL,
	[isCheck] [smallint] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_guessbookre]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_guessbookre](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[devid] [nvarchar](255) NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](100) NULL,
	[classid] [bigint] NULL,
	[bookid] [bigint] NULL,
	[content] [nvarchar](4000) NULL,
	[redate] [smalldatetime] NULL,
	[isCheck] [bigint] NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_hotel]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_hotel](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[classid] [bigint] NOT NULL,
	[name] [nvarchar](255) NOT NULL,
	[smallimg] [nvarchar](255) NULL,
	[address] [nvarchar](1000) NOT NULL,
	[city] [nvarchar](50) NOT NULL,
	[roomtype] [nvarchar](1000) NOT NULL,
	[lvl] [nvarchar](50) NOT NULL,
	[showMoney] [nvarchar](255) NOT NULL,
	[orderMoney] [nvarchar](255) NOT NULL,
	[otherroomtype] [nvarchar](1000) NULL,
	[othershowmoney] [nvarchar](255) NULL,
	[otherordermoney] [nvarchar](255) NULL,
	[about] [ntext] NULL,
	[hits] [bigint] NULL,
	[istop] [int] NULL,
	[isgood] [int] NULL,
	[addtime] [datetime] NULL,
	[isCheck] [smallint] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_hotel] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_hotelOrder]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_hotelOrder](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[hotelid] [bigint] NOT NULL,
	[hotelname] [nvarchar](255) NOT NULL,
	[userid] [bigint] NULL,
	[username] [nvarchar](50) NULL,
	[days] [int] NULL,
	[starttime] [datetime] NULL,
	[endtime] [datetime] NULL,
	[tel] [nvarchar](50) NOT NULL,
	[content] [nvarchar](4000) NULL,
	[remark] [nvarchar](4000) NULL,
	[addtime] [datetime] NOT NULL,
	[state] [int] NOT NULL,
	[isCheck] [smallint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_hotelOrder] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_house_list]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_house_list](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [int] NOT NULL,
	[house_name] [nvarchar](50) NOT NULL,
	[house_img] [nvarchar](500) NOT NULL,
	[house_prise] [int] NOT NULL,
	[house_roomnumber] [int] NOT NULL,
	[buy_count] [int] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_house_user]    Script Date: 2025/6/11 13:40:05 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_house_user](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [int] NOT NULL,
	[userid] [int] NOT NULL,
	[house_id] [int] NOT NULL,
	[buy_time] [datetime] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_link]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_link](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [nvarchar](255) NULL,
	[book_author] [nvarchar](50) NULL,
	[book_pub] [nvarchar](1000) NULL,
	[book_content] [nvarchar](2000) NULL,
	[book_re] [bigint] NULL,
	[book_click] [bigint] NULL,
	[book_date] [smalldatetime] NULL,
	[book_good] [int] NULL,
	[book_top] [int] NULL,
	[ishidden] [smallint] NULL,
	[last_time] [datetime] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
	[redirectURL] [nvarchar](200) NULL,
	[book_img] [nvarchar](200) NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_log]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_log](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[oper_userid] [bigint] NOT NULL,
	[oper_nickname] [nvarchar](50) NULL,
	[oper_type] [int] NOT NULL,
	[log_info] [ntext] NULL,
	[oper_ip] [nvarchar](50) NULL,
	[oper_time] [datetime] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_mailAddress]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_mailAddress](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[name] [nvarchar](50) NOT NULL,
	[mobile] [nvarchar](15) NULL,
	[email] [nvarchar](50) NULL,
	[address] [nvarchar](255) NULL,
	[remark] [nvarchar](4000) NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_mailSubject]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_mailSubject](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[subjectname] [nvarchar](50) NOT NULL,
	[pop3] [nvarchar](50) NOT NULL,
	[smtp] [nvarchar](50) NOT NULL,
	[logname] [nvarchar](50) NOT NULL,
	[logpassword] [nvarchar](50) NOT NULL,
	[signature] [nvarchar](255) NULL,
	[ordernum] [int] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_mailSubject] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_marry_propose]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_marry_propose](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [int] NOT NULL,
	[userid] [int] NOT NULL,
	[requestedid] [int] NOT NULL,
	[requesttime] [datetime] NOT NULL,
	[lovesay] [nvarchar](200) NOT NULL,
	[state] [bit] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_marry_relationship]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_marry_relationship](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [int] NOT NULL,
	[userid] [int] NOT NULL,
	[marryid] [int] NOT NULL,
	[lovesay] [nvarchar](200) NOT NULL,
	[marrytime] [datetime] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_marry_seek]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_marry_seek](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[siteid] [int] NOT NULL,
	[userid] [int] NOT NULL,
	[title] [nvarchar](50) NOT NULL,
	[content] [nvarchar](1000) NOT NULL,
	[time] [datetime] NOT NULL,
	[property_money] [int] NOT NULL,
	[property_experience] [int] NOT NULL,
	[state] [bit] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_message]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_message](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](50) NULL,
	[title] [ntext] NULL,
	[content] [ntext] NULL,
	[touserid] [bigint] NULL,
	[tonickname] [nvarchar](50) NULL,
	[isnew] [smallint] NULL,
	[issystem] [smallint] NULL,
	[addtime] [smalldatetime] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_message] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_myAccount]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_myAccount](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[book_classid] [bigint] NOT NULL,
	[toType] [int] NOT NULL,
	[money] [money] NOT NULL,
	[remark] [nvarchar](200) NULL,
	[payTime] [bigint] NOT NULL,
	[addTime] [datetime] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_Oauth]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_Oauth](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[OpenId] [nvarchar](32) NULL,
	[userid] [bigint] NULL,
	[typeid] [bigint] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_paimai]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_paimai](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [ntext] NULL,
	[book_author] [ntext] NULL,
	[book_pub] [ntext] NULL,
	[book_content] [ntext] NULL,
	[book_re] [bigint] NULL,
	[book_click] [bigint] NULL,
	[book_date] [smalldatetime] NULL,
	[book_good] [int] NULL,
	[book_top] [int] NULL,
	[book_smallimg] [nvarchar](255) NULL,
	[book_hottel] [nvarchar](15) NULL,
	[book_shortmessage] [datetime] NOT NULL,
	[book_xinghao] [nvarchar](50) NULL,
	[book_jiage] [bigint] NOT NULL,
	[book_yhjiage] [bigint] NOT NULL,
	[is_valid] [smallint] NULL,
	[isCheck] [smallint] NULL,
	[book_file] [ntext] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_paimai] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_paimaiOrder]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_paimaiOrder](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](50) NOT NULL,
	[productid] [bigint] NOT NULL,
	[productmoney] [bigint] NOT NULL,
	[remark] [nvarchar](4000) NULL,
	[orderdate] [smalldatetime] NULL,
	[state] [smallint] NULL,
	[iswinner] [smallint] NULL,
	[wintime] [datetime] NULL,
	[isCheck] [smallint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_paimaiOrder] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_paimaire]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_paimaire](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[devid] [nvarchar](255) NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](100) NULL,
	[classid] [bigint] NULL,
	[bookid] [bigint] NULL,
	[content] [nvarchar](4000) NULL,
	[redate] [smalldatetime] NULL,
	[isCheck] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_paimaire] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_picture]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_picture](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [ntext] NULL,
	[book_author] [ntext] NULL,
	[book_ext] [nvarchar](10) NULL,
	[book_size] [nvarchar](50) NULL,
	[book_img] [ntext] NULL,
	[book_file] [ntext] NULL,
	[book_content] [ntext] NULL,
	[book_re] [bigint] NULL,
	[book_click] [bigint] NULL,
	[book_date] [smalldatetime] NULL,
	[sysid] [bigint] NULL,
	[money] [bigint] NULL,
	[nextpage] [int] NULL,
	[book_down] [bigint] NOT NULL,
	[isCheck] [bigint] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
	[book_fileOri] [ntext] NULL,
	[book_fileInfo] [ntext] NULL,
 CONSTRAINT [PK_wap_picture] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_picturere]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_picturere](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[devid] [nvarchar](255) NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](100) NULL,
	[classid] [bigint] NULL,
	[bookid] [bigint] NULL,
	[content] [ntext] NULL,
	[redate] [smalldatetime] NULL,
	[isCheck] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_picturere] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_qiandao]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_qiandao](
	[ID] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[content] [nvarchar](150) NULL,
	[date] [datetime] NULL,
	[siteid] [bigint] NULL,
	[hangbiaoshi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_qiandao_config]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_qiandao_config](
	[ID] [bigint] IDENTITY(1,1) NOT NULL,
	[coin] [bigint] NULL,
	[experience] [bigint] NULL,
	[face] [nvarchar](250) NULL,
	[SigninCap] [bigint] NULL,
	[SigninFloor] [bigint] NULL,
	[CoinIncrease] [nvarchar](4) NULL,
	[ExperienceIncrease] [nvarchar](4) NULL,
	[NewSigninTop] [bigint] NULL,
	[StarSigninTop] [bigint] NULL,
	[FirstSigninCoin] [bigint] NULL,
	[FirstSigninExperience] [bigint] NULL,
	[ALLFirstSigninCoin] [bigint] NULL,
	[ALLFirstSigninExperience] [bigint] NULL,
	[ALLCoinIncrease] [bigint] NULL,
	[Sitename] [nvarchar](250) NULL,
	[Greetings] [nvarchar](250) NULL,
	[siteid] [bigint] NULL,
	[DLLKEY] [nvarchar](250) NULL,
	[hangbiaoshi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_qiandao_log]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_qiandao_log](
	[ID] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[log] [nvarchar](100) NULL,
	[ip] [nvarchar](15) NULL,
	[time] [datetime] NULL,
	[coin] [bigint] NULL,
	[experience] [bigint] NULL,
	[siteid] [bigint] NULL,
	[hangbiaoshi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_qiandao_total]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_qiandao_total](
	[ID] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[Total] [bigint] NULL,
	[LastSigninTime] [datetime] NULL,
	[siteid] [bigint] NULL,
	[hangbiaoshi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_question]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_question](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [nvarchar](1000) NULL,
	[book_author] [nvarchar](10) NULL,
	[book_pub] [nvarchar](50) NULL,
	[book_content] [nvarchar](4000) NULL,
	[book_re] [bigint] NULL,
	[book_click] [bigint] NULL,
	[book_date] [datetime] NULL,
	[book_good] [int] NULL,
	[book_top] [int] NULL,
	[ishidden] [smallint] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_questionre]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_questionre](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[devid] [nvarchar](255) NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](100) NULL,
	[classid] [bigint] NULL,
	[bookid] [bigint] NULL,
	[content] [nvarchar](4000) NULL,
	[redate] [smalldatetime] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_ring]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_ring](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [ntext] NULL,
	[book_author] [ntext] NULL,
	[book_ext] [nvarchar](10) NULL,
	[book_size] [nvarchar](50) NULL,
	[book_img] [ntext] NULL,
	[book_file] [ntext] NULL,
	[book_content] [ntext] NULL,
	[book_re] [bigint] NULL,
	[book_click] [bigint] NULL,
	[book_date] [smalldatetime] NULL,
	[sysid] [bigint] NULL,
	[money] [bigint] NULL,
	[book_down] [bigint] NOT NULL,
	[isCheck] [smallint] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
	[book_director] [ntext] NULL,
	[book_city] [ntext] NULL,
	[book_year] [ntext] NULL,
	[book_lang] [ntext] NULL,
	[book_lable] [nvarchar](20) NULL,
	[book_score] [bigint] NULL,
 CONSTRAINT [PK_wap_ring] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_ringre]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_ringre](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[devid] [nvarchar](255) NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](100) NULL,
	[classid] [bigint] NULL,
	[bookid] [bigint] NULL,
	[content] [ntext] NULL,
	[redate] [smalldatetime] NULL,
	[isCheck] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_ringre] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_room]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_room](
	[ID] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[classid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](50) NOT NULL,
	[content] [nvarchar](300) NOT NULL,
	[tonickname] [nvarchar](50) NOT NULL,
	[times] [datetime] NOT NULL,
	[HangBiaoShi] [int] NULL,
	[isTop] [int] NOT NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_shop]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_shop](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [ntext] NULL,
	[book_author] [ntext] NULL,
	[book_pub] [ntext] NULL,
	[book_content] [ntext] NULL,
	[book_re] [bigint] NULL,
	[book_click] [bigint] NULL,
	[book_date] [smalldatetime] NULL,
	[book_good] [int] NULL,
	[book_top] [int] NULL,
	[book_smallimg] [nvarchar](255) NULL,
	[book_hottel] [bigint] NULL,
	[book_shortmessage] [nvarchar](15) NULL,
	[book_xinghao] [nvarchar](50) NULL,
	[book_jiage] [money] NOT NULL,
	[book_yhjiage] [money] NOT NULL,
	[isCheck] [smallint] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
	[book_file] [ntext] NULL,
	[book_fileINFO] [ntext] NULL,
	[book_charge] [money] NULL,
	[saleCount] [bigint] NOT NULL,
	[book_content_img] [ntext] NULL,
	[book_gongxiao] [ntext] NULL,
	[book_tishi] [ntext] NULL,
	[book_xingxing] [ntext] NULL,
	[book_otherTitle] [ntext] NULL,
	[book_otherContent] [ntext] NULL,
	[book_tip] [ntext] NULL,
	[book_copyid] [bigint] NOT NULL,
 CONSTRAINT [PK_wap_shop] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_shopOrder]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_shopOrder](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](50) NOT NULL,
	[productid] [bigint] NOT NULL,
	[productname] [nvarchar](2000) NULL,
	[productxinghao] [nvarchar](1000) NULL,
	[productjiage] [money] NULL,
	[productcount] [bigint] NOT NULL,
	[productmoney] [money] NULL,
	[remark] [ntext] NULL,
	[state] [int] NULL,
	[orderdate] [smalldatetime] NULL,
	[isCheck] [smallint] NULL,
	[HangBiaoShi] [int] NULL,
	[book_charge] [money] NULL,
	[book_reply] [nvarchar](1000) NULL,
	[orderID] [nvarchar](50) NULL,
	[payState] [nvarchar](1) NULL,
	[payTime] [datetime] NULL,
	[book_classid] [bigint] NULL,
	[mobile] [nvarchar](20) NULL,
	[fromUserID] [bigint] NOT NULL,
	[fromLinkID] [bigint] NOT NULL,
 CONSTRAINT [PK_wap_shopOrder] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_shopre]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_shopre](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[devid] [nvarchar](255) NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](100) NULL,
	[classid] [bigint] NULL,
	[bookid] [bigint] NULL,
	[content] [ntext] NULL,
	[redate] [smalldatetime] NULL,
	[isCheck] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_shopre] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_sms_reg]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_sms_reg](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[myorder] [nvarchar](50) NOT NULL,
	[myorderid] [bigint] NOT NULL,
	[checkCode] [nvarchar](20) NOT NULL,
	[isClose] [int] NOT NULL,
	[remark] [nvarchar](200) NULL,
	[firstName] [nvarchar](10) NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_sms_reg] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_vcount_Detail]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_vcount_Detail](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[types] [bigint] NOT NULL,
	[bookid] [bigint] NOT NULL,
	[localURL] [nvarchar](300) NULL,
	[welcomeURL] [nvarchar](300) NULL,
	[fromTypes] [nvarchar](4) NULL,
	[search] [nvarchar](10) NULL,
	[searchKey] [nvarchar](10) NULL,
	[UA] [ntext] NULL,
	[browser] [nvarchar](10) NULL,
	[IP] [nvarchar](20) NULL,
	[city1] [nvarchar](10) NULL,
	[city2] [nvarchar](10) NULL,
	[net] [nvarchar](2) NULL,
	[everyDate] [datetime] NULL,
	[HangBiaoShi] [int] NULL,
	[mobile] [nvarchar](20) NULL,
	[cookies] [nvarchar](16) NULL,
	[userid] [bigint] NOT NULL,
	[classname] [nvarchar](50) NULL,
	[book_title] [nvarchar](50) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_vcount_everyDate]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_vcount_everyDate](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[types] [bigint] NOT NULL,
	[everyDate] [datetime] NOT NULL,
	[PV] [bigint] NOT NULL,
	[UV] [bigint] NOT NULL,
	[VV] [bigint] NOT NULL,
	[IP] [bigint] NOT NULL,
	[SH_google] [bigint] NOT NULL,
	[SH_soso] [bigint] NOT NULL,
	[SH_baidu] [bigint] NOT NULL,
	[SH_sogou] [bigint] NOT NULL,
	[SH_yahoo] [bigint] NOT NULL,
	[SH_bing] [bigint] NOT NULL,
	[SH_youdao] [bigint] NOT NULL,
	[SH_gougou] [bigint] NOT NULL,
	[CT_beijing] [bigint] NOT NULL,
	[CT_shanghai] [bigint] NOT NULL,
	[CT_guangzhou] [bigint] NOT NULL,
	[CT_shenzhen] [bigint] NOT NULL,
	[NT_ChinaMobile] [bigint] NOT NULL,
	[NT_ChinaUnicom] [bigint] NOT NULL,
	[NT_ChinaTelecom] [bigint] NOT NULL,
	[BS_Safari] [bigint] NOT NULL,
	[BS_Chrome] [bigint] NOT NULL,
	[BS_Opera] [bigint] NOT NULL,
	[BS_IE] [bigint] NOT NULL,
	[BS_UC] [bigint] NOT NULL,
	[BS_QQ] [bigint] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_video]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_video](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [ntext] NULL,
	[book_author] [ntext] NULL,
	[book_ext] [nvarchar](10) NULL,
	[book_size] [nvarchar](50) NULL,
	[book_img] [ntext] NULL,
	[book_file] [ntext] NULL,
	[book_content] [ntext] NULL,
	[book_re] [bigint] NULL,
	[book_click] [bigint] NULL,
	[book_date] [smalldatetime] NULL,
	[sysid] [bigint] NULL,
	[money] [bigint] NULL,
	[book_down] [bigint] NOT NULL,
	[isCheck] [smallint] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
	[book_director] [ntext] NULL,
	[book_city] [ntext] NULL,
	[book_year] [ntext] NULL,
	[book_lang] [ntext] NULL,
	[book_lable] [nvarchar](20) NULL,
	[book_score] [bigint] NULL,
 CONSTRAINT [PK_wap_video] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_videore]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_videore](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[devid] [nvarchar](255) NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](100) NULL,
	[classid] [bigint] NULL,
	[bookid] [bigint] NULL,
	[content] [ntext] NULL,
	[redate] [smalldatetime] NULL,
	[isCheck] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_videore] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_wabao]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_wabao](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[book_classid] [bigint] NOT NULL,
	[book_title] [nvarchar](50) NOT NULL,
	[book_content] [ntext] NULL,
	[book_click] [bigint] NOT NULL,
	[makerid] [bigint] NOT NULL,
	[rand] [int] NOT NULL,
	[needMoney] [bigint] NOT NULL,
	[needExp] [bigint] NOT NULL,
	[needCardID] [nvarchar](50) NULL,
	[subMoney] [bigint] NOT NULL,
	[addMoney] [bigint] NOT NULL,
	[addExp] [bigint] NOT NULL,
	[startTime] [int] NOT NULL,
	[endTime] [int] NOT NULL,
	[book_date] [datetime] NULL,
	[countAll] [bigint] NOT NULL,
	[countHasGet] [bigint] NOT NULL,
	[ischeck] [int] NOT NULL,
	[HangBiaoShi] [int] NULL,
	[sendtypes] [nvarchar](1) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_wabaoRe]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_wabaoRe](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[bookid] [bigint] NOT NULL,
	[content] [ntext] NULL,
	[userid] [bigint] NOT NULL,
	[addtime] [datetime] NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_weixin_Config]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_weixin_Config](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[publicName] [nvarchar](20) NULL,
	[publicID] [nvarchar](20) NULL,
	[weiXinName] [nvarchar](20) NULL,
	[token] [nvarchar](32) NULL,
	[AppId] [nvarchar](18) NULL,
	[AppSecret] [nvarchar](32) NULL,
	[publicType] [nvarchar](3) NULL,
	[sendMassTime] [nvarchar](20) NULL,
	[addTime] [datetime] NULL,
	[HangBiaoShi] [bigint] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_weixin_Menu]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_weixin_Menu](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[buttonID] [int] NULL,
	[subButtonID] [int] NULL,
	[name] [nvarchar](20) NULL,
	[type] [nvarchar](5) NULL,
	[typeValue] [nvarchar](200) NULL,
	[addtime] [datetime] NULL,
	[makeID] [bigint] NULL,
	[HangBiaoShi] [bigint] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_weixin_MsgList]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_weixin_MsgList](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[defaultType] [int] NOT NULL,
	[msgType] [int] NOT NULL,
	[keyWord] [nvarchar](200) NULL,
	[title] [ntext] NULL,
	[description] [ntext] NULL,
	[picUrl] [ntext] NULL,
	[url] [ntext] NULL,
	[sendTimes] [bigint] NULL,
	[sendMassTime] [nvarchar](20) NULL,
	[makeID] [bigint] NULL,
	[addTime] [datetime] NULL,
	[HangBiaoShi] [bigint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_weixin_PlugConfig]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_weixin_PlugConfig](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[types] [nvarchar](50) NOT NULL,
	[config] [ntext] NULL,
	[addtime] [datetime] NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_weixin_ReceiveMsg]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_weixin_ReceiveMsg](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[weixinID] [nvarchar](50) NULL,
	[nickname] [nvarchar](20) NULL,
	[content] [nvarchar](500) NULL,
	[msgType] [int] NULL,
	[addtime] [datetime] NULL,
	[HangBiaoShi] [bigint] NULL,
	[Replay] [ntext] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_wml]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_wml](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[book_classid] [bigint] NULL,
	[book_title] [ntext] NULL,
	[book_author] [ntext] NULL,
	[book_pub] [ntext] NULL,
	[book_content] [ntext] NULL,
	[book_re] [bigint] NULL,
	[book_click] [bigint] NULL,
	[book_date] [smalldatetime] NULL,
	[sysid] [bigint] NULL,
	[isCheck] [smallint] NULL,
	[book_content2] [ntext] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_wml] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_yuehui]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_yuehui](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](50) NULL,
	[classid] [bigint] NULL,
	[yh_title] [nvarchar](4000) NULL,
	[yh_type] [nvarchar](255) NULL,
	[yh_city] [nvarchar](100) NULL,
	[yh_age] [nvarchar](255) NULL,
	[yh_sex] [nvarchar](15) NULL,
	[yh_people] [int] NULL,
	[yh_plan] [nvarchar](4000) NULL,
	[yh_address] [nvarchar](1000) NULL,
	[yh_time] [nvarchar](255) NULL,
	[yh_re] [bigint] NULL,
	[yh_click] [bigint] NULL,
	[yh_date] [datetime] NULL,
	[yh_good] [int] NULL,
	[yh_top] [int] NULL,
	[isCheck] [smallint] NULL,
	[smalltype] [bigint] NULL,
	[MakerID] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_yuehui] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_yuehuiJoin]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_yuehuiJoin](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](100) NULL,
	[classid] [bigint] NULL,
	[bookid] [bigint] NULL,
	[redate] [smalldatetime] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_yuehuiJoin] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap_yuehuire]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap_yuehuire](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[devid] [nvarchar](255) NULL,
	[userid] [bigint] NULL,
	[nickname] [nvarchar](100) NULL,
	[classid] [bigint] NULL,
	[bookid] [bigint] NULL,
	[content] [ntext] NULL,
	[redate] [smalldatetime] NULL,
	[isCheck] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap_yuehuire] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_attachment]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_attachment](
	[ID] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[book_id] [bigint] NOT NULL,
	[book_title] [nvarchar](100) NULL,
	[book_ext] [nvarchar](10) NULL,
	[book_size] [nvarchar](20) NULL,
	[book_type] [nvarchar](15) NULL,
	[book_file] [nvarchar](500) NULL,
	[book_content] [ntext] NULL,
	[book_date] [datetime] NULL,
	[book_click] [bigint] NOT NULL,
	[platform] [nvarchar](30) NULL,
	[screen] [nvarchar](30) NULL,
	[serial] [nvarchar](30) NULL,
	[manual] [ntext] NULL,
 CONSTRAINT [PK_wap2_attachment_1] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_bbs_report]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_bbs_report](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[classid] [bigint] NOT NULL,
	[bbsid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](20) NOT NULL,
	[ReportType] [nvarchar](20) NOT NULL,
	[ReportWhy] [nvarchar](100) NULL,
	[addtime] [datetime] NOT NULL,
	[types] [int] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_apple]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_apple](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[appleID] [bigint] NOT NULL,
	[startTime] [datetime] NOT NULL,
	[endTime] [datetime] NULL,
	[num] [int] NULL,
	[Result] [nvarchar](10) NULL,
	[type1] [bigint] NULL,
	[type2] [bigint] NULL,
	[type4] [bigint] NULL,
	[type6] [bigint] NULL,
	[type8] [bigint] NULL,
	[type10] [bigint] NULL,
	[type12] [bigint] NULL,
	[type14] [bigint] NULL,
	[winAllMoney] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap2_games_apple] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_appleUser]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_appleUser](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](50) NOT NULL,
	[appleID] [bigint] NOT NULL,
	[types] [int] NOT NULL,
	[typesName] [nvarchar](50) NOT NULL,
	[myMultiple] [bigint] NOT NULL,
	[myMoney] [bigint] NOT NULL,
	[getMoney] [bigint] NOT NULL,
	[state] [int] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_chat]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_chat](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[gameEn] [nvarchar](20) NOT NULL,
	[gameCn] [nvarchar](20) NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickName] [nvarchar](20) NOT NULL,
	[content] [nvarchar](200) NOT NULL,
	[addtime] [datetime] NOT NULL,
	[ischeck] [int] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap2_games_chat] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_chuiniu]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_chuiniu](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickName] [nvarchar](20) NOT NULL,
	[Question] [nvarchar](50) NOT NULL,
	[Answer1] [nvarchar](30) NOT NULL,
	[Answer2] [nvarchar](30) NOT NULL,
	[myMoney] [bigint] NOT NULL,
	[myAnswer] [int] NOT NULL,
	[state] [int] NOT NULL,
	[winUserid] [bigint] NULL,
	[winNickname] [nvarchar](20) NULL,
	[winAnswer] [int] NULL,
	[winTime] [datetime] NULL,
	[addtime] [datetime] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap2_games_chuiniu] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_config]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_config](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[gameEn] [nvarchar](20) NOT NULL,
	[gameCn] [nvarchar](20) NOT NULL,
	[config] [ntext] NULL,
	[todayTimes] [bigint] NOT NULL,
	[todayMoney] [bigint] NOT NULL,
	[updateTime] [datetime] NOT NULL,
	[addtime] [datetime] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap2_games_config] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_happyTen]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_happyTen](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[years] [nvarchar](4) NOT NULL,
	[period] [bigint] NULL,
	[ball1] [nvarchar](2) NULL,
	[ball2] [nvarchar](2) NULL,
	[ball3] [nvarchar](2) NULL,
	[ball4] [nvarchar](2) NULL,
	[ball5] [nvarchar](2) NULL,
	[ball6] [nvarchar](2) NULL,
	[ball7] [nvarchar](2) NULL,
	[ball8] [nvarchar](2) NULL,
	[OpenTime] [datetime] NULL,
	[state] [int] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_happyTen_bet]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_happyTen_bet](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[bookid] [bigint] NOT NULL,
	[years] [nvarchar](4) NOT NULL,
	[peroid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](50) NOT NULL,
	[types] [int] NOT NULL,
	[types_content] [nvarchar](200) NOT NULL,
	[num] [bigint] NOT NULL,
	[betTime] [datetime] NOT NULL,
	[betMoney] [bigint] NOT NULL,
	[GetMoney] [bigint] NOT NULL,
	[state] [int] NOT NULL,
	[attribute] [int] NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_horse]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_horse](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[periodID] [bigint] NOT NULL,
	[startTime] [datetime] NOT NULL,
	[endTime] [datetime] NULL,
	[num] [int] NULL,
	[Result] [nvarchar](10) NULL,
	[type1Money] [bigint] NULL,
	[type2Money] [bigint] NULL,
	[type3Money] [bigint] NULL,
	[type4Money] [bigint] NULL,
	[type5Money] [bigint] NULL,
	[type6Money] [bigint] NULL,
	[winAllMoney] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap2_games_horse] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_horseUser]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_horseUser](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](50) NOT NULL,
	[periodID] [bigint] NOT NULL,
	[types] [int] NOT NULL,
	[typesName] [nvarchar](50) NOT NULL,
	[myMoney] [bigint] NOT NULL,
	[getMoney] [bigint] NOT NULL,
	[state] [int] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_lucky28]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_lucky28](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[periodID] [bigint] NOT NULL,
	[startTime] [datetime] NOT NULL,
	[endTime] [datetime] NULL,
	[num1] [int] NULL,
	[num2] [int] NULL,
	[num3] [int] NULL,
	[Result] [nvarchar](10) NULL,
	[People] [bigint] NULL,
	[winAllMoney] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap2_games_lucky28] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_lucky28User]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_lucky28User](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](50) NOT NULL,
	[periodID] [bigint] NOT NULL,
	[types] [int] NOT NULL,
	[typesName] [nvarchar](50) NOT NULL,
	[myMoney] [bigint] NOT NULL,
	[getMoney] [bigint] NOT NULL,
	[state] [int] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap2_games_lucky28User] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_MarkSix]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_MarkSix](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[years] [nvarchar](4) NOT NULL,
	[period] [bigint] NULL,
	[ball1] [nvarchar](2) NULL,
	[ball2] [nvarchar](2) NULL,
	[ball3] [nvarchar](2) NULL,
	[ball4] [nvarchar](2) NULL,
	[ball5] [nvarchar](2) NULL,
	[ball6] [nvarchar](2) NULL,
	[ballTeMa] [nvarchar](2) NULL,
	[OpenTime] [datetime] NULL,
	[state] [int] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_MarkSix_bet]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_MarkSix_bet](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[bookid] [bigint] NOT NULL,
	[years] [nvarchar](4) NOT NULL,
	[peroid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](50) NOT NULL,
	[types] [int] NOT NULL,
	[types_content] [nvarchar](200) NOT NULL,
	[num] [bigint] NOT NULL,
	[betTime] [datetime] NOT NULL,
	[betMoney] [bigint] NOT NULL,
	[GetMoney] [bigint] NOT NULL,
	[state] [int] NOT NULL,
	[attribute] [int] NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_quankun]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_quankun](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[periodID] [bigint] NOT NULL,
	[startTime] [datetime] NOT NULL,
	[endTime] [datetime] NULL,
	[num] [int] NULL,
	[Result] [nvarchar](10) NULL,
	[type1Money] [bigint] NULL,
	[type2Money] [bigint] NULL,
	[type3Money] [bigint] NULL,
	[type4Money] [bigint] NULL,
	[type5Money] [bigint] NULL,
	[type6Money] [bigint] NULL,
	[winAllMoney] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap2_games_quankun] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_quankunUser]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_quankunUser](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](50) NOT NULL,
	[periodID] [bigint] NOT NULL,
	[types] [int] NOT NULL,
	[typesName] [nvarchar](50) NOT NULL,
	[myMoney] [bigint] NOT NULL,
	[getMoney] [bigint] NOT NULL,
	[state] [int] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_rank]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_rank](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[gameEn] [nvarchar](20) NOT NULL,
	[gameCn] [nvarchar](20) NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickName] [nvarchar](20) NOT NULL,
	[TimesTotal] [bigint] NOT NULL,
	[rankTimes] [bigint] NOT NULL,
	[moneyTotal] [bigint] NOT NULL,
	[rankMoney] [bigint] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap2_games_rank] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_shoot]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_shoot](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickName] [nvarchar](20) NOT NULL,
	[myMoney] [bigint] NOT NULL,
	[myShoot] [int] NOT NULL,
	[state] [int] NOT NULL,
	[winUserid] [bigint] NULL,
	[winNickname] [nvarchar](20) NULL,
	[winShoot] [int] NULL,
	[winTime] [datetime] NULL,
	[addtime] [datetime] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap2_games_shoot] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_stone]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_stone](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickName] [nvarchar](20) NOT NULL,
	[myMoney] [bigint] NOT NULL,
	[myStone] [int] NOT NULL,
	[state] [int] NOT NULL,
	[winUserid] [bigint] NULL,
	[winNickname] [nvarchar](20) NULL,
	[winStone] [int] NULL,
	[winTime] [datetime] NULL,
	[addtime] [datetime] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap2_games_stone] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_touzi]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_touzi](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[touziID] [bigint] NOT NULL,
	[startTime] [datetime] NOT NULL,
	[endTime] [datetime] NULL,
	[num1] [int] NULL,
	[num2] [int] NULL,
	[num3] [int] NULL,
	[Result] [nvarchar](10) NULL,
	[type1Money] [bigint] NULL,
	[type2Money] [bigint] NULL,
	[type3Money] [bigint] NULL,
	[type4Money] [bigint] NULL,
	[type5Money] [bigint] NULL,
	[winAllMoney] [bigint] NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap2_games_touzi] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_touziUser]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_touziUser](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](50) NOT NULL,
	[touziID] [bigint] NOT NULL,
	[types] [int] NOT NULL,
	[typesName] [nvarchar](50) NOT NULL,
	[myMoney] [bigint] NOT NULL,
	[getMoney] [bigint] NOT NULL,
	[state] [int] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_waBao]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_waBao](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[years] [nvarchar](4) NOT NULL,
	[period] [bigint] NULL,
	[ball1] [nvarchar](2) NULL,
	[ball2] [nvarchar](2) NULL,
	[ball3] [nvarchar](2) NULL,
	[ball4] [nvarchar](2) NULL,
	[ball5] [nvarchar](2) NULL,
	[ball6] [nvarchar](2) NULL,
	[ball7] [nvarchar](2) NULL,
	[ball8] [nvarchar](2) NULL,
	[OpenTime] [datetime] NULL,
	[state] [int] NOT NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_waBao_bet]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_waBao_bet](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[bookid] [bigint] NOT NULL,
	[years] [nvarchar](4) NOT NULL,
	[peroid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](50) NOT NULL,
	[types] [int] NOT NULL,
	[types_content] [nvarchar](200) NOT NULL,
	[num] [bigint] NOT NULL,
	[betTime] [datetime] NOT NULL,
	[betMoney] [bigint] NOT NULL,
	[GetMoney] [bigint] NOT NULL,
	[state] [int] NOT NULL,
	[attribute] [int] NULL,
	[HangBiaoShi] [int] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_games_war]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_games_war](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickName] [nvarchar](20) NOT NULL,
	[myMoney] [bigint] NOT NULL,
	[myWar] [int] NOT NULL,
	[state] [int] NOT NULL,
	[winUserid] [bigint] NULL,
	[winNickname] [nvarchar](20) NULL,
	[winWar] [int] NULL,
	[winTime] [datetime] NULL,
	[addtime] [datetime] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_wap2_games_war] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_mobile_UA]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_mobile_UA](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[nameCN] [nvarchar](50) NULL,
	[nameEN] [nvarchar](50) NULL,
	[Mode] [nvarchar](50) NULL,
	[Series] [nvarchar](20) NULL,
	[OSystem] [nvarchar](50) NULL,
	[widthpx] [int] NOT NULL,
	[heightpx] [int] NOT NULL,
	[version] [nvarchar](50) NOT NULL,
	[ischeck] [smallint] NOT NULL,
	[ismodify] [nvarchar](500) NULL,
	[userid] [bigint] NOT NULL,
	[siteid] [bigint] NOT NULL,
	[rand] [bigint] NOT NULL,
	[addtime] [datetime] NOT NULL,
	[hangbiaoshi] [bigint] NULL,
	[ShowIndex] [int] NOT NULL,
	[Remark] [nvarchar](200) NULL,
 CONSTRAINT [PK_wap2_mobile_UA] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_smallType]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_smallType](
	[id] [bigint] IDENTITY(100,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[subclassName] [nvarchar](200) NOT NULL,
	[maker] [bigint] NOT NULL,
	[rank] [bigint] NOT NULL,
	[systype] [nvarchar](50) NOT NULL,
	[addtime] [datetime] NULL,
	[HangBiaoShi] [int] NULL,
	[jinbi] [bigint] NULL,
	[jinyan] [bigint] NULL,
	[xian] [bigint] NULL,
 CONSTRAINT [PK_wap2_smallType] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_style]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_style](
	[ID] [bigint] IDENTITY(10,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[style_name] [nvarchar](20) NOT NULL,
	[style] [ntext] NOT NULL,
	[style_type] [int] NOT NULL,
	[style_color] [nvarchar](7) NOT NULL,
	[rank] [bigint] NOT NULL,
	[create_user] [bigint] NOT NULL,
	[create_time] [datetime] NOT NULL,
	[isSystem] [bigint] NOT NULL,
	[HangBiaoShi] [bigint] NULL,
 CONSTRAINT [PK_wap2_style_1] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_userGuessBook]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_userGuessBook](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[fromuserid] [bigint] NOT NULL,
	[fromnickname] [nvarchar](50) NOT NULL,
	[content] [ntext] NOT NULL,
	[addtime] [datetime] NOT NULL,
	[ischeck] [smallint] NOT NULL,
	[hangbiaoshi] [bigint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap2_visitZone]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap2_visitZone](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[userid] [bigint] NOT NULL,
	[nickname] [nvarchar](16) NOT NULL,
	[touserid] [bigint] NOT NULL,
	[tonickname] [nvarchar](16) NOT NULL,
	[addtime] [datetime] NOT NULL,
	[hangbiaoshi] [bigint] NULL
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap3_htmlContent]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap3_htmlContent](
	[siteid] [bigint] NOT NULL,
	[classid] [bigint] NOT NULL,
	[html3_2] [ntext] NULL,
	[config3_2] [ntext] NULL,
	[html3_3] [ntext] NULL,
	[config3_3] [ntext] NULL,
	[html4_2] [ntext] NULL,
	[config4_2] [ntext] NULL,
	[html4_3] [ntext] NULL,
	[config4_3] [ntext] NULL,
	[MakerID] [bigint] NOT NULL,
	[addtime] [datetime] NOT NULL,
	[hangbiaoshi] [bigint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap3_ModelFunc]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap3_ModelFunc](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[typeid] [bigint] NOT NULL,
	[systemID] [bigint] NOT NULL,
	[isSystem] [smallint] NOT NULL,
	[HtmlTitle] [nvarchar](50) NOT NULL,
	[HtmlContent] [ntext] NULL,
	[config] [ntext] NULL,
	[MakerID] [bigint] NOT NULL,
	[addtime] [datetime] NOT NULL,
	[hangbiaoshi] [int] NULL,
 CONSTRAINT [PK_wap3_ModelFunc] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap3_ModelPage]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap3_ModelPage](
	[id] [bigint] IDENTITY(1,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[typeid] [bigint] NOT NULL,
	[systemID] [bigint] NOT NULL,
	[isSystem] [smallint] NOT NULL,
	[HtmlTitle] [nvarchar](50) NOT NULL,
	[HtmlContent] [ntext] NULL,
	[config] [ntext] NULL,
	[MakerID] [bigint] NOT NULL,
	[addtime] [datetime] NOT NULL,
	[hangbiaoshi] [int] NULL,
 CONSTRAINT [PK_wap3_ModelPage] PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[wap3_style]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[wap3_style](
	[ID] [bigint] IDENTITY(10,1) NOT NULL,
	[siteid] [bigint] NOT NULL,
	[style_name] [nvarchar](20) NOT NULL,
	[style] [ntext] NOT NULL,
	[style_type] [int] NOT NULL,
	[style_color] [nvarchar](7) NOT NULL,
	[rank] [bigint] NOT NULL,
	[create_user] [bigint] NOT NULL,
	[create_time] [datetime] NOT NULL,
	[isSystem] [bigint] NOT NULL,
	[HangBiaoShi] [bigint] NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[XinZhang]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[XinZhang](
	[ID] [int] NOT NULL,
	[XinZhangMingChen] [nvarchar](50) NOT NULL,
	[XinZhangTuPian] [nvarchar](200) NOT NULL,
	[XinZhangJiaGe] [int] NOT NULL,
	[ChuangJianShiJian] [datetime] NOT NULL,
	[siteid] [int] NOT NULL,
	[ShiFouMoRen] [bit] NOT NULL,
	[HangBiaoShi] [int] NULL,
 CONSTRAINT [PK_XinZhang] PRIMARY KEY CLUSTERED 
(
	[ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 90, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[XinZhang_Plugin]    Script Date: 2025/6/11 13:40:06 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[XinZhang_Plugin](
	[userid] [bigint] NOT NULL,
	[siteid] [bigint] NOT NULL,
	[moneyname] [ntext] NULL,
 CONSTRAINT [PK_XinZhang_Plugin] PRIMARY KEY CLUSTERED 
(
	[userid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
ALTER TABLE [dbo].[bbs_guessing] ADD  DEFAULT ((0)) FOR [is_closed]
GO
ALTER TABLE [dbo].[bbs_guessing] ADD  DEFAULT (getdate()) FOR [created_at]
GO
ALTER TABLE [dbo].[bbs_guessing] ADD  DEFAULT (getdate()) FOR [updated_at]
GO
ALTER TABLE [dbo].[bbs_guessing_bets] ADD  DEFAULT (getdate()) FOR [created_at]
GO
ALTER TABLE [dbo].[chinaBank_order] ADD  CONSTRAINT [DF_chinaBank_order_addtime]  DEFAULT (getdate()) FOR [add_time]
GO
ALTER TABLE [dbo].[chinaBank_order] ADD  CONSTRAINT [DF_chinaBank_order_v_pmode]  DEFAULT (N'支付方式') FOR [v_pmode]
GO
ALTER TABLE [dbo].[chinaBank_order] ADD  CONSTRAINT [DF__chinaBank__bankT__1FB8AE52]  DEFAULT (0) FOR [bankType]
GO
ALTER TABLE [dbo].[chinabank_WAP_Config] ADD  CONSTRAINT [DF_chinabank_WAP_Config_Tomoney]  DEFAULT (0) FOR [Tomoney]
GO
ALTER TABLE [dbo].[chinabank_WAP_Config] ADD  CONSTRAINT [DF_chinabank_WAP_Config_isclose]  DEFAULT (0) FOR [isclose]
GO
ALTER TABLE [dbo].[chinabank_WAP_Config] ADD  CONSTRAINT [DF_chinabank_WAP_Config_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[chinabank_WAP_Config] ADD  CONSTRAINT [DF__chinaBank__bankT__20ACD28B]  DEFAULT (0) FOR [bankType]
GO
ALTER TABLE [dbo].[chinabank_wap_order] ADD  CONSTRAINT [DF_chinabank_wap_order_Code]  DEFAULT (0) FOR [Code]
GO
ALTER TABLE [dbo].[chinabank_wap_order] ADD  CONSTRAINT [DF_chinabank_wap_order_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[chinabank_wap_order] ADD  CONSTRAINT [DF__chinaBank__bankT__21A0F6C4]  DEFAULT (0) FOR [bankType]
GO
ALTER TABLE [dbo].[chinabank_wap_order] ADD  DEFAULT ((0)) FOR [opera_userid]
GO
ALTER TABLE [dbo].[class] ADD  CONSTRAINT [DF_class_childid]  DEFAULT (0) FOR [childid]
GO
ALTER TABLE [dbo].[class] ADD  CONSTRAINT [DF_class_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[class] ADD  CONSTRAINT [DF_class_typeid]  DEFAULT (0) FOR [typeid]
GO
ALTER TABLE [dbo].[class] ADD  CONSTRAINT [DF_class_position]  DEFAULT (N'left') FOR [position]
GO
ALTER TABLE [dbo].[class] ADD  CONSTRAINT [DF_class_sitelist]  DEFAULT (1) FOR [sitelist]
GO
ALTER TABLE [dbo].[class] ADD  CONSTRAINT [DF_class_hits]  DEFAULT (0) FOR [hits]
GO
ALTER TABLE [dbo].[class] ADD  CONSTRAINT [DF_class_articlenum]  DEFAULT (0) FOR [articlenum]
GO
ALTER TABLE [dbo].[class] ADD  CONSTRAINT [DF_class_rank]  DEFAULT (0) FOR [rank]
GO
ALTER TABLE [dbo].[class] ADD  CONSTRAINT [DF_class_creatdate]  DEFAULT (getdate()) FOR [creatdate]
GO
ALTER TABLE [dbo].[class] ADD  CONSTRAINT [DF_class_ismodel]  DEFAULT (0) FOR [ismodel]
GO
ALTER TABLE [dbo].[class] ADD  CONSTRAINT [DF_class_ishidden]  DEFAULT (0) FOR [ishidden]
GO
ALTER TABLE [dbo].[class] ADD  CONSTRAINT [DF__class__total__7345FA8E]  DEFAULT (0) FOR [total]
GO
ALTER TABLE [dbo].[class] ADD  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[DomainName] ADD  CONSTRAINT [DF_DomainName_length]  DEFAULT (0) FOR [length]
GO
ALTER TABLE [dbo].[DomainName] ADD  CONSTRAINT [DF_DomainName_isJump]  DEFAULT (0) FOR [isJump]
GO
ALTER TABLE [dbo].[DomainName] ADD  CONSTRAINT [DF_DomainName_sitespace]  DEFAULT (500) FOR [sitespace]
GO
ALTER TABLE [dbo].[DomainName] ADD  CONSTRAINT [DF_DomainName_isUseDNS]  DEFAULT (0) FOR [isUseDNS]
GO
ALTER TABLE [dbo].[DomainName] ADD  CONSTRAINT [DF__domainnam__isChe__22951AFD]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[DomainName] ADD  CONSTRAINT [DF__domainnam__isClo__23893F36]  DEFAULT (0) FOR [isClose]
GO
ALTER TABLE [dbo].[favdetail] ADD  CONSTRAINT [DF_favdetail_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[favdetail] ADD  CONSTRAINT [DF_favdetail_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[favdetail] ADD  CONSTRAINT [DF_favdetail_favtypeid]  DEFAULT (0) FOR [favtypeid]
GO
ALTER TABLE [dbo].[favdetail] ADD  CONSTRAINT [DF_favdetail_adddate]  DEFAULT (getdate()) FOR [adddate]
GO
ALTER TABLE [dbo].[favsubject] ADD  CONSTRAINT [DF_favsubject_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[favsubject] ADD  CONSTRAINT [DF_favsubject_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[favsubject] ADD  CONSTRAINT [DF_favsubject_subjectlognum]  DEFAULT (0) FOR [subjectlognum]
GO
ALTER TABLE [dbo].[favsubject] ADD  CONSTRAINT [DF_favsubject_ordernum]  DEFAULT (0) FOR [ordernum]
GO
ALTER TABLE [dbo].[fcount] ADD  CONSTRAINT [DF_fcount_ftime]  DEFAULT (getdate()) FOR [ftime]
GO
ALTER TABLE [dbo].[fcount] ADD  CONSTRAINT [DF_fcount_fuserid]  DEFAULT ((0)) FOR [fuserid]
GO
ALTER TABLE [dbo].[fcount] ADD  CONSTRAINT [DF_fcount_userid]  DEFAULT ((0)) FOR [userid]
GO
ALTER TABLE [dbo].[fcount] ADD  CONSTRAINT [DF_fcount_classid]  DEFAULT ((0)) FOR [classid]
GO
ALTER TABLE [dbo].[Form_Data] ADD  DEFAULT ((0)) FOR [fromlinkid]
GO
ALTER TABLE [dbo].[home] ADD  CONSTRAINT [DF_home_cookies_time]  DEFAULT (0) FOR [cookies_time]
GO
ALTER TABLE [dbo].[home] ADD  CONSTRAINT [DF_home_t_page]  DEFAULT (0) FOR [t_page]
GO
ALTER TABLE [dbo].[home] ADD  CONSTRAINT [DF_home_scount]  DEFAULT (0) FOR [scount]
GO
ALTER TABLE [dbo].[home] ADD  CONSTRAINT [DF_home_stime]  DEFAULT (0) FOR [stime]
GO
ALTER TABLE [dbo].[home] ADD  CONSTRAINT [DF_home_onlinetime]  DEFAULT (0) FOR [onlinetime]
GO
ALTER TABLE [dbo].[home] ADD  CONSTRAINT [DF_home_maxcount]  DEFAULT (0) FOR [maxcount]
GO
ALTER TABLE [dbo].[home] ADD  CONSTRAINT [DF_home_sopen]  DEFAULT (0) FOR [sopen]
GO
ALTER TABLE [dbo].[sendSMS] ADD  CONSTRAINT [DF_wap_sendSMS_state]  DEFAULT (0) FOR [state]
GO
ALTER TABLE [dbo].[sendSMS] ADD  DEFAULT (0) FOR [QR_check_state]
GO
ALTER TABLE [dbo].[sendSMS] ADD  DEFAULT (0) FOR [actionType]
GO
ALTER TABLE [dbo].[sitetype] ADD  CONSTRAINT [DF_sitetype_rank]  DEFAULT (0) FOR [rank]
GO
ALTER TABLE [dbo].[sys_check_ip] ADD  CONSTRAINT [DF_sys_check_ip_lasttime]  DEFAULT (getdate()) FOR [lasttime]
GO
ALTER TABLE [dbo].[sys_role_info] ADD  CONSTRAINT [DF_sys_role_info_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[sys_wap_all_type] ADD  CONSTRAINT [DF_sys_wap_all_type_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[sys_wap_bbs] ADD  CONSTRAINT [DF_sys_wap_bbs_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[sys_wap_bbs] ADD  CONSTRAINT [DF_sys_wap_bbs_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[sys_wap_book] ADD  CONSTRAINT [DF_sys_wap_book_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[sys_wap_book] ADD  CONSTRAINT [DF_sys_wap_book_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[sys_wap_download] ADD  CONSTRAINT [DF_sys_wap_download_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[sys_wap_download] ADD  CONSTRAINT [DF_sys_wap_download_book_classid]  DEFAULT (0) FOR [book_classid]
GO
ALTER TABLE [dbo].[sys_wap_download] ADD  CONSTRAINT [DF_sys_wap_download_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[sys_wap_files] ADD  CONSTRAINT [DF_sys_wap_files_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[sys_wap_files] ADD  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[sys_wap_message] ADD  CONSTRAINT [DF_sys_wap_message_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[sys_wap_message] ADD  CONSTRAINT [DF_sys_wap_message_msg_date]  DEFAULT (getdate()) FOR [msg_date]
GO
ALTER TABLE [dbo].[sys_wap_message] ADD  CONSTRAINT [DF_sys_wap_message_valid_date]  DEFAULT (getdate()) FOR [valid_date]
GO
ALTER TABLE [dbo].[sys_wap_message] ADD  CONSTRAINT [DF_sys_wap_message_pop_flag]  DEFAULT (0) FOR [pop_flag]
GO
ALTER TABLE [dbo].[sys_wap_message] ADD  CONSTRAINT [DF__sys_wap_m__msg_t__02333863]  DEFAULT (0) FOR [msg_type]
GO
ALTER TABLE [dbo].[sys_wap_picture] ADD  CONSTRAINT [DF_sys_wap_picture_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[sys_wap_picture] ADD  CONSTRAINT [DF_sys_wap_picture_book_classid]  DEFAULT (0) FOR [book_classid]
GO
ALTER TABLE [dbo].[sys_wap_picture] ADD  CONSTRAINT [DF_sys_wap_picture_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[sys_wap_ring] ADD  CONSTRAINT [DF_sys_wap_ring_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[sys_wap_ring] ADD  CONSTRAINT [DF_sys_wap_ring_book_classid]  DEFAULT (0) FOR [book_classid]
GO
ALTER TABLE [dbo].[sys_wap_ring] ADD  CONSTRAINT [DF_sys_wap_ring_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[sys_wap_ring] ADD  DEFAULT (0) FOR [book_score]
GO
ALTER TABLE [dbo].[sys_wap_ring] ADD  DEFAULT (0) FOR [makerid]
GO
ALTER TABLE [dbo].[sys_wap_video] ADD  CONSTRAINT [DF_sys_wap_video_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[sys_wap_video] ADD  CONSTRAINT [DF_sys_wap_video_book_classid]  DEFAULT (0) FOR [book_classid]
GO
ALTER TABLE [dbo].[sys_wap_video] ADD  CONSTRAINT [DF_sys_wap_video_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[sys_wap_video] ADD  DEFAULT (0) FOR [book_score]
GO
ALTER TABLE [dbo].[sys_wap_video] ADD  DEFAULT (0) FOR [makerid]
GO
ALTER TABLE [dbo].[systype] ADD  CONSTRAINT [DF_systype_type]  DEFAULT (0) FOR [type]
GO
ALTER TABLE [dbo].[systype] ADD  CONSTRAINT [DF_systype_createuserid]  DEFAULT (0) FOR [createuserid]
GO
ALTER TABLE [dbo].[systype] ADD  CONSTRAINT [DF_systype_createdate]  DEFAULT (getdate()) FOR [createdate]
GO
ALTER TABLE [dbo].[systype] ADD  CONSTRAINT [DF_systype_rank]  DEFAULT (0) FOR [rank]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_sex]  DEFAULT (0) FOR [sex]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_age]  DEFAULT (0) FOR [age]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_money]  DEFAULT (100) FOR [money]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_RegTime]  DEFAULT (getdate()) FOR [RegTime]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_LastLoginTime]  DEFAULT (getdate()) FOR [LastLoginTime]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_LoginTimes]  DEFAULT (1) FOR [LoginTimes]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_LockUser]  DEFAULT (0) FOR [LockUser]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_headimg]  DEFAULT (N'64.gif') FOR [headimg]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_siteposition]  DEFAULT (N'left') FOR [siteposition]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_sitelistflag]  DEFAULT (1) FOR [sitelistflag]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_sitelist]  DEFAULT (2) FOR [sitelist]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_sitetype]  DEFAULT (1) FOR [sitetype]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_MaxPerPage_Default]  DEFAULT (5) FOR [MaxPerPage_Default]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_MaxPerPage_Content]  DEFAULT (300) FOR [MaxPerPage_Content]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_MaxFileSize]  DEFAULT (100) FOR [MaxFileSize]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_sessiontimeout]  DEFAULT (0) FOR [SessionTimeout]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_sitespace]  DEFAULT (500) FOR [sitespace]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_myspace]  DEFAULT (0) FOR [myspace]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF_user_siteRight]  DEFAULT (0) FOR [siteRight]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF__user__myBankMone__004AEFF1]  DEFAULT (0) FOR [myBankMoney]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF__user__expR__7251D655]  DEFAULT (0) FOR [expR]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF__user__RMB__247D636F]  DEFAULT (0) FOR [RMB]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF__user__siteVIP__257187A8]  DEFAULT (0) FOR [siteVIP]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF__user__ZoneCount__2665ABE1]  DEFAULT (0) FOR [ZoneCount]
GO
ALTER TABLE [dbo].[user] ADD  CONSTRAINT [DF__user__isCheck__2759D01A]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[user] ADD  DEFAULT ((0)) FOR [bbsCount]
GO
ALTER TABLE [dbo].[user] ADD  DEFAULT ((0)) FOR [bbsReCount]
GO
ALTER TABLE [dbo].[user] ADD  DEFAULT ((0)) FOR [TJCount]
GO
ALTER TABLE [dbo].[user_lock] ADD  CONSTRAINT [DF__user_lock__class__013F142A]  DEFAULT (0) FOR [classid]
GO
ALTER TABLE [dbo].[UserPreferences] ADD  DEFAULT ((1)) FOR [NewReplyUIEnabled]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_startcount]  DEFAULT (1) FOR [startcount]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vtoday]  DEFAULT (0) FOR [vtoday]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vyestaday]  DEFAULT (0) FOR [vyestaday]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vtotal]  DEFAULT (0) FOR [vtotal]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vstarttime]  DEFAULT (getdate()) FOR [vstarttime]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vdate]  DEFAULT (getdate()) FOR [vdate]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vtotal1]  DEFAULT (0) FOR [vtotal1]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vip]  DEFAULT (127) FOR [vip]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vmonth]  DEFAULT (0) FOR [vmonth]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vnowmonth]  DEFAULT (0) FOR [vnowmonth]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vpremonth]  DEFAULT (0) FOR [vpremonth]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vyear]  DEFAULT (0) FOR [vyear]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vnowyear]  DEFAULT (0) FOR [vnowyear]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vweek]  DEFAULT (0) FOR [vweek]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vweeknum]  DEFAULT (0) FOR [vweeknum]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vuser]  DEFAULT (N'kelink.com') FOR [vuser]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vpass]  DEFAULT (N'kelink.com') FOR [vpass]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vemail]  DEFAULT (N'www.kelink.com') FOR [vemail]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vtitle]  DEFAULT (N'www.kelink.com') FOR [vtitle]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vurl]  DEFAULT (N'http://www.kelink.com') FOR [vurl]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vcontent]  DEFAULT (N'kelink.com') FOR [vcontent]
GO
ALTER TABLE [dbo].[vcount] ADD  CONSTRAINT [DF_vcount_vregtime]  DEFAULT (getdate()) FOR [vregtime]
GO
ALTER TABLE [dbo].[wap_adlink] ADD  CONSTRAINT [DF_wap_adlink_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_adlink] ADD  CONSTRAINT [DF_wap_adlink_book_classid]  DEFAULT (0) FOR [book_classid]
GO
ALTER TABLE [dbo].[wap_adlink] ADD  CONSTRAINT [DF_wap_adlink_book_re]  DEFAULT (0) FOR [book_re]
GO
ALTER TABLE [dbo].[wap_adlink] ADD  CONSTRAINT [DF_wap_adlink_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_adlink] ADD  CONSTRAINT [DF_wap_adlink_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[wap_adlink] ADD  CONSTRAINT [DF__wap_adlin__isChe__2942188C]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_adlinkre] ADD  CONSTRAINT [DF_wap_adlinkre_userid]  DEFAULT ((0)) FOR [userid]
GO
ALTER TABLE [dbo].[wap_adlinkre] ADD  CONSTRAINT [DF_wap_adlinkre_classid]  DEFAULT ((0)) FOR [classid]
GO
ALTER TABLE [dbo].[wap_adlinkre] ADD  CONSTRAINT [DF_wap_adlinkre_bookid]  DEFAULT ((0)) FOR [bookid]
GO
ALTER TABLE [dbo].[wap_adlinkre] ADD  CONSTRAINT [DF_wap_adlinkre_redate]  DEFAULT (getdate()) FOR [redate]
GO
ALTER TABLE [dbo].[wap_airplane] ADD  CONSTRAINT [DF_wap_airplane_istop]  DEFAULT (0) FOR [istop]
GO
ALTER TABLE [dbo].[wap_airplane] ADD  CONSTRAINT [DF_wap_airplane_isgood]  DEFAULT (0) FOR [isgood]
GO
ALTER TABLE [dbo].[wap_airplane] ADD  CONSTRAINT [DF_wap_airplane_hits]  DEFAULT (0) FOR [hits]
GO
ALTER TABLE [dbo].[wap_airplane] ADD  CONSTRAINT [DF_wap_airplane_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap_airplane] ADD  CONSTRAINT [DF__wap_airpl__isChe__2A363CC5]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_airplaneOrder] ADD  CONSTRAINT [DF_wap_airplaneOrder_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_airplaneOrder] ADD  CONSTRAINT [DF_wap_airplaneOrder_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap_airplaneOrder] ADD  CONSTRAINT [DF_wap_airplaneOrder_state]  DEFAULT (0) FOR [state]
GO
ALTER TABLE [dbo].[wap_airplaneOrder] ADD  CONSTRAINT [DF__wap_airpl__isChe__2B2A60FE]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_album] ADD  CONSTRAINT [DF_wap_album_book_re]  DEFAULT (0) FOR [book_re]
GO
ALTER TABLE [dbo].[wap_album] ADD  CONSTRAINT [DF_wap_album_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_album] ADD  CONSTRAINT [DF_wap_album_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[wap_album] ADD  CONSTRAINT [DF__wap_album__isChe__2C1E8537]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_album] ADD  CONSTRAINT [DF__wap_album__ishid__2D12A970]  DEFAULT (0) FOR [ishidden]
GO
ALTER TABLE [dbo].[wap_albumre] ADD  CONSTRAINT [DF_wap_albumre_redate]  DEFAULT (getdate()) FOR [redate]
GO
ALTER TABLE [dbo].[wap_albumre] ADD  CONSTRAINT [DF__wap_album__isChe__2E06CDA9]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_albumSubject] ADD  CONSTRAINT [DF_wap_albumSubject_subjectlognum]  DEFAULT (0) FOR [subjectlognum]
GO
ALTER TABLE [dbo].[wap_albumSubject] ADD  CONSTRAINT [DF__wap_album__isChe__2EFAF1E2]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_bankLog] ADD  CONSTRAINT [DF_wap_bankLog_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  CONSTRAINT [DF_wap_bbs_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  CONSTRAINT [DF_wap_bbs_book_classid]  DEFAULT (0) FOR [book_classid]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  CONSTRAINT [DF_wap_bbs_book_re]  DEFAULT (0) FOR [book_re]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  CONSTRAINT [DF_wap_bbs_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  CONSTRAINT [DF_wap_bbs_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  CONSTRAINT [DF_wap_bbs_book_good]  DEFAULT (0) FOR [book_good]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  CONSTRAINT [DF_wap_bbs_book_top]  DEFAULT (0) FOR [book_top]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  CONSTRAINT [DF_wap_bbs_sysid]  DEFAULT (0) FOR [sysid]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  CONSTRAINT [DF_wap_bbs_reDate]  DEFAULT (getdate()) FOR [reDate]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  CONSTRAINT [DF_wap_bbs_reShow]  DEFAULT (0) FOR [reShow]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  CONSTRAINT [DF_wap_bbs_support]  DEFAULT (0) FOR [suport]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  CONSTRAINT [DF_wap_bbs_oppose]  DEFAULT (0) FOR [oppose]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  CONSTRAINT [DF_wap_bbs_islock]  DEFAULT (0) FOR [islock]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  CONSTRAINT [DF__wap_bbs__isCheck__2FEF161B]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  DEFAULT (0) FOR [freeMoney]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  DEFAULT (0) FOR [freeleftMoney]
GO
ALTER TABLE [dbo].[wap_bbs] ADD  DEFAULT ((0)) FOR [myGetMoney]
GO
ALTER TABLE [dbo].[wap_bbs_vote] ADD  CONSTRAINT [DF_wap_bbs_vote_voteClick]  DEFAULT (0) FOR [voteClick]
GO
ALTER TABLE [dbo].[wap_bbsre] ADD  CONSTRAINT [DF_wap_bbsre_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_bbsre] ADD  CONSTRAINT [DF_wap_bbsre_classid]  DEFAULT (0) FOR [classid]
GO
ALTER TABLE [dbo].[wap_bbsre] ADD  CONSTRAINT [DF_wap_bbsre_bookid]  DEFAULT (0) FOR [bookid]
GO
ALTER TABLE [dbo].[wap_bbsre] ADD  CONSTRAINT [DF_wap_bbsre_redate]  DEFAULT (getdate()) FOR [redate]
GO
ALTER TABLE [dbo].[wap_bbsre] ADD  CONSTRAINT [DF__wap_bbsre__book___15D01CBC]  DEFAULT (0) FOR [book_top]
GO
ALTER TABLE [dbo].[wap_book] ADD  CONSTRAINT [DF_wap_book_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_book] ADD  CONSTRAINT [DF_wap_book_book_classid]  DEFAULT (0) FOR [book_classid]
GO
ALTER TABLE [dbo].[wap_book] ADD  CONSTRAINT [DF_wap_book_book_re]  DEFAULT (0) FOR [book_re]
GO
ALTER TABLE [dbo].[wap_book] ADD  CONSTRAINT [DF_wap_book_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_book] ADD  CONSTRAINT [DF_wap_book_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[wap_book] ADD  CONSTRAINT [DF_wap_book_sysid]  DEFAULT (0) FOR [sysid]
GO
ALTER TABLE [dbo].[wap_book] ADD  CONSTRAINT [DF__wap_book__xi__12F3B011]  DEFAULT (0) FOR [xi]
GO
ALTER TABLE [dbo].[wap_book] ADD  CONSTRAINT [DF__wap_book__nu__13E7D44A]  DEFAULT (0) FOR [nu]
GO
ALTER TABLE [dbo].[wap_book] ADD  CONSTRAINT [DF__wap_book__han__14DBF883]  DEFAULT (0) FOR [han]
GO
ALTER TABLE [dbo].[wap_book] ADD  CONSTRAINT [DF__wap_book__isChec__30E33A54]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_bookre] ADD  CONSTRAINT [DF_wap_bookre_devid]  DEFAULT (0) FOR [devid]
GO
ALTER TABLE [dbo].[wap_bookre] ADD  CONSTRAINT [DF_wap_bookre_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_bookre] ADD  CONSTRAINT [DF_wap_bookre_classid]  DEFAULT (0) FOR [classid]
GO
ALTER TABLE [dbo].[wap_bookre] ADD  CONSTRAINT [DF_wap_bookre_bookid]  DEFAULT (0) FOR [bookid]
GO
ALTER TABLE [dbo].[wap_bookre] ADD  CONSTRAINT [DF_wap_bookre_redate]  DEFAULT (getdate()) FOR [redate]
GO
ALTER TABLE [dbo].[wap_bookre] ADD  CONSTRAINT [DF__wap_bookr__isChe__31D75E8D]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_car_list] ADD  CONSTRAINT [DF_wap_car_list_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_car_list] ADD  CONSTRAINT [DF_wap_car_list_car_prise]  DEFAULT (0) FOR [car_prise]
GO
ALTER TABLE [dbo].[wap_car_list] ADD  CONSTRAINT [DF_wap_car_list_car_img]  DEFAULT ('http://') FOR [car_img]
GO
ALTER TABLE [dbo].[wap_car_list] ADD  CONSTRAINT [DF_wap_car_list_car_type]  DEFAULT (1) FOR [car_type]
GO
ALTER TABLE [dbo].[wap_car_list] ADD  CONSTRAINT [DF_wap_car_list_property_tire]  DEFAULT (0) FOR [property_tire]
GO
ALTER TABLE [dbo].[wap_car_list] ADD  CONSTRAINT [DF_wap_car_list_property_stead]  DEFAULT (0) FOR [property_steady]
GO
ALTER TABLE [dbo].[wap_car_list] ADD  CONSTRAINT [DF_wap_car_list_property_control]  DEFAULT (0) FOR [property_control]
GO
ALTER TABLE [dbo].[wap_car_list] ADD  CONSTRAINT [DF_wap_car_list_property_power]  DEFAULT (0) FOR [property_power]
GO
ALTER TABLE [dbo].[wap_car_list] ADD  CONSTRAINT [DF_wap_car_list_property_oilvolume]  DEFAULT (0) FOR [property_oilvolume]
GO
ALTER TABLE [dbo].[wap_car_list] ADD  CONSTRAINT [DF_wap_car_list_buy_count]  DEFAULT (0) FOR [buy_count]
GO
ALTER TABLE [dbo].[wap_car_race] ADD  CONSTRAINT [DF_wap_car_race_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_car_race] ADD  CONSTRAINT [DF_wap_car_race_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_car_race] ADD  CONSTRAINT [DF_wap_car_race_road_1]  DEFAULT (0) FOR [road]
GO
ALTER TABLE [dbo].[wap_car_race] ADD  CONSTRAINT [DF_wap_car_race_money]  DEFAULT (0) FOR [stake]
GO
ALTER TABLE [dbo].[wap_car_race] ADD  CONSTRAINT [DF_wap_car_race_timeout]  DEFAULT (0) FOR [timeout]
GO
ALTER TABLE [dbo].[wap_car_race] ADD  CONSTRAINT [DF_wap_car_race_time]  DEFAULT (getdate()) FOR [time]
GO
ALTER TABLE [dbo].[wap_car_race] ADD  CONSTRAINT [DF_wap_car_race_state]  DEFAULT ('0') FOR [score1]
GO
ALTER TABLE [dbo].[wap_car_race] ADD  CONSTRAINT [DF_wap_car_race_score2]  DEFAULT ('0') FOR [score2]
GO
ALTER TABLE [dbo].[wap_car_race] ADD  CONSTRAINT [DF_wap_car_race_car_id]  DEFAULT (0) FOR [car_id]
GO
ALTER TABLE [dbo].[wap_car_user] ADD  CONSTRAINT [DF_wap_car_user_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_car_user] ADD  CONSTRAINT [DF_wap_car_user_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_car_user] ADD  CONSTRAINT [DF_wap_car_user_car_id]  DEFAULT (0) FOR [car_id]
GO
ALTER TABLE [dbo].[wap_car_user] ADD  CONSTRAINT [DF_wap_car_user_car_img]  DEFAULT ('http://') FOR [car_img]
GO
ALTER TABLE [dbo].[wap_car_user] ADD  CONSTRAINT [DF_wap_car_user_car_type]  DEFAULT (1) FOR [car_type]
GO
ALTER TABLE [dbo].[wap_car_user] ADD  CONSTRAINT [DF_wap_car_user_buy_time]  DEFAULT (getdate()) FOR [buy_time]
GO
ALTER TABLE [dbo].[wap_car_user] ADD  CONSTRAINT [DF_wap_car_user_race_times]  DEFAULT (0) FOR [race_times]
GO
ALTER TABLE [dbo].[wap_car_user] ADD  CONSTRAINT [DF_wap_car_user_race_wim]  DEFAULT (0) FOR [race_win]
GO
ALTER TABLE [dbo].[wap_car_user] ADD  CONSTRAINT [DF_wap_car_user_race_lost]  DEFAULT (0) FOR [race_lost]
GO
ALTER TABLE [dbo].[wap_car_user] ADD  CONSTRAINT [DF_wap_car_user_property_tire]  DEFAULT (0) FOR [property_tire]
GO
ALTER TABLE [dbo].[wap_car_user] ADD  CONSTRAINT [DF_wap_car_user_property_steady]  DEFAULT (0) FOR [property_steady]
GO
ALTER TABLE [dbo].[wap_car_user] ADD  CONSTRAINT [DF_wap_car_user_property_control]  DEFAULT (0) FOR [property_control]
GO
ALTER TABLE [dbo].[wap_car_user] ADD  CONSTRAINT [DF_wap_car_user_property_power]  DEFAULT (0) FOR [property_power]
GO
ALTER TABLE [dbo].[wap_car_user] ADD  CONSTRAINT [DF_wap_car_user_property_oilvolume]  DEFAULT (0) FOR [property_oilvolume]
GO
ALTER TABLE [dbo].[wap_car_user] ADD  CONSTRAINT [DF_wap_car_user_car_default]  DEFAULT (0) FOR [car_default]
GO
ALTER TABLE [dbo].[wap_card] ADD  CONSTRAINT [DF_wap_card_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap_card] ADD  DEFAULT (0) FOR [ishidden]
GO
ALTER TABLE [dbo].[wap_clan_list] ADD  CONSTRAINT [DF_wap_clan_list_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_clan_list] ADD  CONSTRAINT [DF_wap_clan_list_createid]  DEFAULT (0) FOR [createid]
GO
ALTER TABLE [dbo].[wap_clan_list] ADD  CONSTRAINT [DF_wap_clan_list_clan_memberCount]  DEFAULT (0) FOR [clan_memberCount]
GO
ALTER TABLE [dbo].[wap_clan_list] ADD  CONSTRAINT [DF_wap_clan_list_clan_money]  DEFAULT (0) FOR [clan_money]
GO
ALTER TABLE [dbo].[wap_clan_list] ADD  CONSTRAINT [DF_wap_clan_list_clan_mark]  DEFAULT (0) FOR [clan_mark]
GO
ALTER TABLE [dbo].[wap_clan_list] ADD  CONSTRAINT [DF_wap_clan_list_clan_createdate]  DEFAULT (getdate()) FOR [clan_createdate]
GO
ALTER TABLE [dbo].[wap_clan_list] ADD  CONSTRAINT [DF_wap_clan_list_clan_joinmoney]  DEFAULT (0) FOR [clan_joinmoney]
GO
ALTER TABLE [dbo].[wap_clan_list] ADD  CONSTRAINT [DF_wap_clan_list_clan_img]  DEFAULT ('http://') FOR [clan_img]
GO
ALTER TABLE [dbo].[wap_clan_list] ADD  CONSTRAINT [DF_wap_clan_list_clan_notice]  DEFAULT ('暂无公告') FOR [clan_notice]
GO
ALTER TABLE [dbo].[wap_clan_list] ADD  CONSTRAINT [DF_wap_clan_list_clam_join]  DEFAULT (1) FOR [clan_join]
GO
ALTER TABLE [dbo].[wap_clan_list] ADD  CONSTRAINT [DF_wap_clan_list_clan_bbs]  DEFAULT (0) FOR [clan_bbs]
GO
ALTER TABLE [dbo].[wap_clan_list] ADD  CONSTRAINT [DF_wap_clan_list_clan_chat]  DEFAULT (0) FOR [clan_chat]
GO
ALTER TABLE [dbo].[wap_clan_list] ADD  DEFAULT ((50)) FOR [clan_maxMemberCount]
GO
ALTER TABLE [dbo].[wap_clan_pk] ADD  CONSTRAINT [DF_wap_clan_pk_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_clan_pk] ADD  CONSTRAINT [DF_wap_clan_pk_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_clan_pk] ADD  CONSTRAINT [DF_wap_clan_pk_jointime]  DEFAULT (getdate()) FOR [jointime]
GO
ALTER TABLE [dbo].[wap_clan_pk] ADD  CONSTRAINT [DF_wap_clan_pk_attacktime]  DEFAULT (getdate()) FOR [attacktime]
GO
ALTER TABLE [dbo].[wap_clan_pk_message] ADD  CONSTRAINT [DF_wap_clan_pk_message_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_clan_pk_message] ADD  CONSTRAINT [DF_wap_clan_pk_message_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_clan_pk_message] ADD  CONSTRAINT [DF_wap_clan_pk_message_douserid]  DEFAULT (0) FOR [douserid]
GO
ALTER TABLE [dbo].[wap_clan_pk_message] ADD  CONSTRAINT [DF_wap_clan_pk_message_time]  DEFAULT (getdate()) FOR [time]
GO
ALTER TABLE [dbo].[wap_clan_pk_message] ADD  CONSTRAINT [DF_wap_clan_pk_message_type]  DEFAULT (0) FOR [type]
GO
ALTER TABLE [dbo].[wap_clan_pk_message] ADD  DEFAULT ((0)) FOR [clan_id]
GO
ALTER TABLE [dbo].[wap_clan_request] ADD  CONSTRAINT [DF_wap_clan_request_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_clan_request] ADD  CONSTRAINT [DF_wap_clan_request_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_clan_request] ADD  CONSTRAINT [DF_wap_clan_request_doid]  DEFAULT (0) FOR [doid]
GO
ALTER TABLE [dbo].[wap_clan_request] ADD  CONSTRAINT [DF_wap_clan_request_clan_id]  DEFAULT (0) FOR [clan_id]
GO
ALTER TABLE [dbo].[wap_clan_request] ADD  CONSTRAINT [DF_wap_clan_request_request_state]  DEFAULT (0) FOR [request_state]
GO
ALTER TABLE [dbo].[wap_clan_request] ADD  CONSTRAINT [DF_wap_clan_request_request_message]  DEFAULT ('无') FOR [request_message]
GO
ALTER TABLE [dbo].[wap_clan_request] ADD  CONSTRAINT [DF_wap_clan_request_request_time]  DEFAULT (getdate()) FOR [request_time]
GO
ALTER TABLE [dbo].[wap_clan_setting] ADD  CONSTRAINT [DF_wap_clan_setting_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_clan_setting] ADD  CONSTRAINT [DF_wap_clan_setting_createmoney]  DEFAULT (1000) FOR [createmoney]
GO
ALTER TABLE [dbo].[wap_clan_setting] ADD  CONSTRAINT [DF_wap_clan_setting_weekmenber]  DEFAULT (50) FOR [weekmenber]
GO
ALTER TABLE [dbo].[wap_clan_setting] ADD  CONSTRAINT [DF_wap_clan_setting_power]  DEFAULT (1000) FOR [power]
GO
ALTER TABLE [dbo].[wap_clan_setting] ADD  CONSTRAINT [DF_wap_clan_setting_transfermoney]  DEFAULT (200) FOR [transfermoney]
GO
ALTER TABLE [dbo].[wap_clan_setting] ADD  CONSTRAINT [DF_wap_clan_setting_needlvls]  DEFAULT (0) FOR [needlvls]
GO
ALTER TABLE [dbo].[wap_clan_setting] ADD  DEFAULT ((100)) FOR [addMenberNeedMoney]
GO
ALTER TABLE [dbo].[wap_clan_setting] ADD  DEFAULT ((2)) FOR [maxclan]
GO
ALTER TABLE [dbo].[wap_clan_setting] ADD  DEFAULT ((3)) FOR [maxclanadd]
GO
ALTER TABLE [dbo].[wap_clan_setting] ADD  DEFAULT ((1)) FOR [addPowerNeedMoney]
GO
ALTER TABLE [dbo].[wap_clan_user] ADD  CONSTRAINT [DF_wap_clan_user_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_clan_user] ADD  CONSTRAINT [DF_wap_clan_user_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_clan_user] ADD  CONSTRAINT [DF_wap_clan_user_ismaster]  DEFAULT (0) FOR [ismaster]
GO
ALTER TABLE [dbo].[wap_clan_user] ADD  CONSTRAINT [DF_wap_clan_user_clan_id]  DEFAULT (0) FOR [clan_id]
GO
ALTER TABLE [dbo].[wap_clan_user] ADD  CONSTRAINT [DF_wap_clan_user_clan_joindate]  DEFAULT (getdate()) FOR [clan_joindate]
GO
ALTER TABLE [dbo].[wap_clan_user] ADD  CONSTRAINT [DF_wap_clan_user_power]  DEFAULT (0) FOR [power]
GO
ALTER TABLE [dbo].[wap_clan_user] ADD  CONSTRAINT [DF_wap_clan_user_mark]  DEFAULT (0) FOR [mark]
GO
ALTER TABLE [dbo].[wap_clan_user] ADD  CONSTRAINT [DF_wap_clan_user_pk_attack]  DEFAULT (0) FOR [pk_attack]
GO
ALTER TABLE [dbo].[wap_clan_user] ADD  CONSTRAINT [DF_wap_clan_user_pk_attacked]  DEFAULT (0) FOR [pk_attacked]
GO
ALTER TABLE [dbo].[wap_download] ADD  CONSTRAINT [DF_wap_download_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_download] ADD  CONSTRAINT [DF_wap_download_book_classid]  DEFAULT (0) FOR [book_classid]
GO
ALTER TABLE [dbo].[wap_download] ADD  CONSTRAINT [DF_wap_download_book_re]  DEFAULT (0) FOR [book_re]
GO
ALTER TABLE [dbo].[wap_download] ADD  CONSTRAINT [DF_wap_download_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_download] ADD  CONSTRAINT [DF_wap_download_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[wap_download] ADD  CONSTRAINT [DF_wap_download_sysid]  DEFAULT (0) FOR [sysid]
GO
ALTER TABLE [dbo].[wap_download] ADD  CONSTRAINT [DF_wap_download_money]  DEFAULT (0) FOR [money]
GO
ALTER TABLE [dbo].[wap_download] ADD  CONSTRAINT [DF__wap_downl__book___22800C64]  DEFAULT (0) FOR [book_down]
GO
ALTER TABLE [dbo].[wap_download] ADD  CONSTRAINT [DF__wap_downlo__ding__69BC9054]  DEFAULT (0) FOR [ding]
GO
ALTER TABLE [dbo].[wap_download] ADD  CONSTRAINT [DF__wap_downl__yiban__6AB0B48D]  DEFAULT (0) FOR [yiban]
GO
ALTER TABLE [dbo].[wap_download] ADD  CONSTRAINT [DF__wap_downloa__cai__6BA4D8C6]  DEFAULT (0) FOR [cai]
GO
ALTER TABLE [dbo].[wap_download] ADD  CONSTRAINT [DF__wap_downl__isChe__32CB82C6]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_download] ADD  DEFAULT (0) FOR [book_top]
GO
ALTER TABLE [dbo].[wap_download] ADD  DEFAULT (0) FOR [book_good]
GO
ALTER TABLE [dbo].[wap_download] ADD  DEFAULT (0) FOR [book_recommend]
GO
ALTER TABLE [dbo].[wap_download] ADD  DEFAULT (0) FOR [book_score]
GO
ALTER TABLE [dbo].[wap_downloadre] ADD  CONSTRAINT [DF_wap_downloadre_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_downloadre] ADD  CONSTRAINT [DF_wap_downloadre_classid]  DEFAULT (0) FOR [classid]
GO
ALTER TABLE [dbo].[wap_downloadre] ADD  CONSTRAINT [DF_wap_downloadre_bookid]  DEFAULT (0) FOR [bookid]
GO
ALTER TABLE [dbo].[wap_downloadre] ADD  CONSTRAINT [DF_wap_downloadre_redate]  DEFAULT (getdate()) FOR [redate]
GO
ALTER TABLE [dbo].[wap_downloadre] ADD  CONSTRAINT [DF__wap_downl__isChe__33BFA6FF]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_friends] ADD  CONSTRAINT [DF_wap_friends_rank]  DEFAULT (0) FOR [rank]
GO
ALTER TABLE [dbo].[wap_friends] ADD  CONSTRAINT [DF_wap_friends_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap_friends] ADD  CONSTRAINT [DF_wap_friends_friendtype]  DEFAULT (0) FOR [friendtype]
GO
ALTER TABLE [dbo].[wap_fun_setting] ADD  CONSTRAINT [DF_wap_fun_setting_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_fun_setting] ADD  CONSTRAINT [DF_wap_fun_setting_marry_proposemoney]  DEFAULT (1314) FOR [marry_proposemoney]
GO
ALTER TABLE [dbo].[wap_fun_setting] ADD  CONSTRAINT [DF_wap_fun_setting_marry_seekmoney]  DEFAULT (1314) FOR [marry_seekmoney]
GO
ALTER TABLE [dbo].[wap_fun_setting] ADD  CONSTRAINT [DF_wap_fun_setting_marry_seektitle]  DEFAULT ('我要征婚') FOR [marry_seektitle]
GO
ALTER TABLE [dbo].[wap_fun_setting] ADD  CONSTRAINT [DF_wap_fun_setting_marry_seekcontent]  DEFAULT ('征婚内容') FOR [marry_seekcontent]
GO
ALTER TABLE [dbo].[wap_fun_setting] ADD  CONSTRAINT [DF_wap_fun_setting_car_racemoney]  DEFAULT (100) FOR [car_racemoney]
GO
ALTER TABLE [dbo].[wap_game_chuiniu] ADD  CONSTRAINT [DF_wap_game_chuiniu_fbz]  DEFAULT (0) FOR [fbz]
GO
ALTER TABLE [dbo].[wap_game_chuiniu] ADD  CONSTRAINT [DF_wap_game_chuiniu_tzz]  DEFAULT (0) FOR [tzz]
GO
ALTER TABLE [dbo].[wap_game_chuiniu] ADD  CONSTRAINT [DF_wap_game_chuiniu_fbdate]  DEFAULT (getdate()) FOR [fbdate]
GO
ALTER TABLE [dbo].[wap_game_chuiniu] ADD  CONSTRAINT [DF_wap_game_chuiniu_zb]  DEFAULT (0) FOR [zb]
GO
ALTER TABLE [dbo].[wap_game_chuiniu] ADD  CONSTRAINT [DF_wap_game_chuiniu_tzcg]  DEFAULT (0) FOR [tzcg]
GO
ALTER TABLE [dbo].[wap_game_chuiniu] ADD  CONSTRAINT [DF_wap_game_chuiniu_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_games_count] ADD  CONSTRAINT [DF_wap_games_times]  DEFAULT (0) FOR [times]
GO
ALTER TABLE [dbo].[wap_games_count] ADD  CONSTRAINT [DF_wap_games_counts]  DEFAULT (0) FOR [counts]
GO
ALTER TABLE [dbo].[wap_games_count] ADD  CONSTRAINT [DF_wap_games_gametype]  DEFAULT (0) FOR [gametype]
GO
ALTER TABLE [dbo].[wap_gongqiu] ADD  CONSTRAINT [DF_wap_gongqiu_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_gongqiu] ADD  CONSTRAINT [DF_wap_gongqiu_book_classid]  DEFAULT (0) FOR [book_classid]
GO
ALTER TABLE [dbo].[wap_gongqiu] ADD  CONSTRAINT [DF_wap_gongqiu_book_re]  DEFAULT (0) FOR [book_re]
GO
ALTER TABLE [dbo].[wap_gongqiu] ADD  CONSTRAINT [DF_wap_gongqiu_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_gongqiu] ADD  CONSTRAINT [DF_wap_gongqiu_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[wap_gongqiu] ADD  CONSTRAINT [DF_wap_gongqiu_sysid]  DEFAULT (0) FOR [sysid]
GO
ALTER TABLE [dbo].[wap_gongqiu] ADD  CONSTRAINT [DF__wap_gongq__isChe__34B3CB38]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_guangbo] ADD  CONSTRAINT [DF_wap_guangbo_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_guangbo] ADD  CONSTRAINT [DF_wap_guangbo_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap_guangbo] ADD  CONSTRAINT [DF_wap_guangbo_ischeck]  DEFAULT (0) FOR [ischeck]
GO
ALTER TABLE [dbo].[wap_guessbook] ADD  CONSTRAINT [DF_wap_guessbook_book_re]  DEFAULT (0) FOR [book_re]
GO
ALTER TABLE [dbo].[wap_guessbook] ADD  CONSTRAINT [DF_wap_guessbook_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_guessbook] ADD  CONSTRAINT [DF_wap_guessbook_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[wap_guessbook] ADD  CONSTRAINT [DF_wap_guessbook_book_good]  DEFAULT (0) FOR [book_good]
GO
ALTER TABLE [dbo].[wap_guessbook] ADD  CONSTRAINT [DF_wap_guessbook_book_top]  DEFAULT (0) FOR [book_top]
GO
ALTER TABLE [dbo].[wap_guessbook] ADD  CONSTRAINT [DF_wap_guessbook_ishidden]  DEFAULT (0) FOR [ishidden]
GO
ALTER TABLE [dbo].[wap_guessbook] ADD  CONSTRAINT [DF__wap_guess__isChe__35A7EF71]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_guessbookre] ADD  CONSTRAINT [DF_wap_guessbookre_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_guessbookre] ADD  CONSTRAINT [DF_wap_guessbookre_redate]  DEFAULT (getdate()) FOR [redate]
GO
ALTER TABLE [dbo].[wap_guessbookre] ADD  CONSTRAINT [DF__wap_guess__isChe__369C13AA]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_hotel] ADD  CONSTRAINT [DF_wap_hotel_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_hotel] ADD  CONSTRAINT [DF_wap_hotel_hits]  DEFAULT (0) FOR [hits]
GO
ALTER TABLE [dbo].[wap_hotel] ADD  CONSTRAINT [DF_wap_hotel_istop]  DEFAULT (0) FOR [istop]
GO
ALTER TABLE [dbo].[wap_hotel] ADD  CONSTRAINT [DF_wap_hotel_isgood]  DEFAULT (0) FOR [isgood]
GO
ALTER TABLE [dbo].[wap_hotel] ADD  CONSTRAINT [DF_wap_hotel_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap_hotel] ADD  CONSTRAINT [DF__wap_hotel__isChe__379037E3]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_hotelOrder] ADD  CONSTRAINT [DF_wap_hotelOrder_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_hotelOrder] ADD  CONSTRAINT [DF_wap_hotelOrder_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap_hotelOrder] ADD  CONSTRAINT [DF_wap_hotelOrder_state]  DEFAULT (0) FOR [state]
GO
ALTER TABLE [dbo].[wap_hotelOrder] ADD  CONSTRAINT [DF__wap_hotel__isChe__38845C1C]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_house_list] ADD  CONSTRAINT [DF_wap_house_list_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_house_list] ADD  CONSTRAINT [DF_wap_house_list_house_img]  DEFAULT ('http://') FOR [house_img]
GO
ALTER TABLE [dbo].[wap_house_list] ADD  CONSTRAINT [DF_wap_house_list_house_prise]  DEFAULT (0) FOR [house_prise]
GO
ALTER TABLE [dbo].[wap_house_list] ADD  CONSTRAINT [DF_wap_house_list_house_roomnumber]  DEFAULT (0) FOR [house_roomnumber]
GO
ALTER TABLE [dbo].[wap_house_list] ADD  CONSTRAINT [DF_wap_house_list_buy_count]  DEFAULT (0) FOR [buy_count]
GO
ALTER TABLE [dbo].[wap_house_user] ADD  CONSTRAINT [DF_wap_house_user_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_house_user] ADD  CONSTRAINT [DF_wap_house_user_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_house_user] ADD  CONSTRAINT [DF_wap_house_user_buy_time]  DEFAULT (getdate()) FOR [buy_time]
GO
ALTER TABLE [dbo].[wap_link] ADD  CONSTRAINT [DF_wap_link_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_link] ADD  CONSTRAINT [DF_wap_link_book_re]  DEFAULT (0) FOR [book_re]
GO
ALTER TABLE [dbo].[wap_link] ADD  CONSTRAINT [DF_wap_link_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_link] ADD  CONSTRAINT [DF_wap_link_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[wap_link] ADD  CONSTRAINT [DF_wap_link_book_good]  DEFAULT (0) FOR [book_good]
GO
ALTER TABLE [dbo].[wap_link] ADD  CONSTRAINT [DF_wap_link_book_top]  DEFAULT (0) FOR [book_top]
GO
ALTER TABLE [dbo].[wap_link] ADD  CONSTRAINT [DF_wap_link_ishidden]  DEFAULT (0) FOR [ishidden]
GO
ALTER TABLE [dbo].[wap_log] ADD  CONSTRAINT [DF_wap_log_siteid]  DEFAULT ((0)) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_log] ADD  CONSTRAINT [DF_wap_log_oper_type]  DEFAULT ((0)) FOR [oper_type]
GO
ALTER TABLE [dbo].[wap_log] ADD  CONSTRAINT [DF_wap_log_oper_date]  DEFAULT (getdate()) FOR [oper_time]
GO
ALTER TABLE [dbo].[wap_marry_propose] ADD  CONSTRAINT [DF_wap_marry_propose_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_marry_propose] ADD  CONSTRAINT [DF_wap_marry_propose_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_marry_propose] ADD  CONSTRAINT [DF_wap_marry_propose_requestedid]  DEFAULT (0) FOR [requestedid]
GO
ALTER TABLE [dbo].[wap_marry_propose] ADD  CONSTRAINT [DF_wap_marry_propose_requesttime]  DEFAULT (getdate()) FOR [requesttime]
GO
ALTER TABLE [dbo].[wap_marry_propose] ADD  CONSTRAINT [DF_wap_marry_propose_lovasay]  DEFAULT ('无') FOR [lovesay]
GO
ALTER TABLE [dbo].[wap_marry_propose] ADD  CONSTRAINT [DF_wap_marry_propose_state]  DEFAULT (0) FOR [state]
GO
ALTER TABLE [dbo].[wap_marry_relationship] ADD  CONSTRAINT [DF_wap_marry_relationships_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_marry_relationship] ADD  CONSTRAINT [DF_wap_marry_relationships_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_marry_relationship] ADD  CONSTRAINT [DF_wap_marry_relationships_marryid]  DEFAULT (0) FOR [marryid]
GO
ALTER TABLE [dbo].[wap_marry_relationship] ADD  CONSTRAINT [DF_wap_marry_relationships_lovesay]  DEFAULT ('无') FOR [lovesay]
GO
ALTER TABLE [dbo].[wap_marry_relationship] ADD  CONSTRAINT [DF_wap_marry_relationships_marrytime]  DEFAULT (getdate()) FOR [marrytime]
GO
ALTER TABLE [dbo].[wap_marry_seek] ADD  CONSTRAINT [DF_wap_mary_seek_siteid]  DEFAULT (0) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_marry_seek] ADD  CONSTRAINT [DF_wap_mary_seek_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_marry_seek] ADD  CONSTRAINT [DF_wap_mary_seek_time]  DEFAULT (getdate()) FOR [time]
GO
ALTER TABLE [dbo].[wap_marry_seek] ADD  CONSTRAINT [DF_wap_mary_seek_property_money]  DEFAULT (100) FOR [property_money]
GO
ALTER TABLE [dbo].[wap_marry_seek] ADD  CONSTRAINT [DF_wap_mary_seek_property_experience]  DEFAULT (100) FOR [property_experience]
GO
ALTER TABLE [dbo].[wap_marry_seek] ADD  CONSTRAINT [DF_wap_mary_seek_state]  DEFAULT (0) FOR [state]
GO
ALTER TABLE [dbo].[wap_message] ADD  CONSTRAINT [DF_wap_message_siteid]  DEFAULT ((0)) FOR [siteid]
GO
ALTER TABLE [dbo].[wap_message] ADD  CONSTRAINT [DF_wap_message_userid]  DEFAULT ((0)) FOR [userid]
GO
ALTER TABLE [dbo].[wap_message] ADD  CONSTRAINT [DF_wap_message_touserid]  DEFAULT ((0)) FOR [touserid]
GO
ALTER TABLE [dbo].[wap_message] ADD  CONSTRAINT [DF_wap_message_isnew]  DEFAULT ((1)) FOR [isnew]
GO
ALTER TABLE [dbo].[wap_message] ADD  CONSTRAINT [DF_wap_message_issystem]  DEFAULT ((0)) FOR [issystem]
GO
ALTER TABLE [dbo].[wap_message] ADD  CONSTRAINT [DF_wap_message_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap_paimai] ADD  CONSTRAINT [DF_wap_paimai_book_re]  DEFAULT (0) FOR [book_re]
GO
ALTER TABLE [dbo].[wap_paimai] ADD  CONSTRAINT [DF_wap_paimai_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_paimai] ADD  CONSTRAINT [DF_wap_paimai_book_good]  DEFAULT (0) FOR [book_good]
GO
ALTER TABLE [dbo].[wap_paimai] ADD  CONSTRAINT [DF_wap_paimai_book_top]  DEFAULT (0) FOR [book_top]
GO
ALTER TABLE [dbo].[wap_paimai] ADD  CONSTRAINT [DF_wap_paimai_book_jiage]  DEFAULT (0) FOR [book_jiage]
GO
ALTER TABLE [dbo].[wap_paimai] ADD  CONSTRAINT [DF_wap_paimai_book_yhjiage]  DEFAULT (0) FOR [book_yhjiage]
GO
ALTER TABLE [dbo].[wap_paimai] ADD  CONSTRAINT [DF_wap_paimai_is_valid]  DEFAULT (0) FOR [is_valid]
GO
ALTER TABLE [dbo].[wap_paimai] ADD  CONSTRAINT [DF__wap_paima__isChe__39788055]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_paimaiOrder] ADD  CONSTRAINT [DF_wap_paimaiOrder_productmoney]  DEFAULT (0) FOR [productmoney]
GO
ALTER TABLE [dbo].[wap_paimaiOrder] ADD  CONSTRAINT [DF_wap_paimaiOrder_orderdate]  DEFAULT (getdate()) FOR [orderdate]
GO
ALTER TABLE [dbo].[wap_paimaiOrder] ADD  CONSTRAINT [DF_wap_paimaiOrder_state]  DEFAULT (0) FOR [state]
GO
ALTER TABLE [dbo].[wap_paimaiOrder] ADD  CONSTRAINT [DF_wap_paimaiOrder_iswinner]  DEFAULT (0) FOR [iswinner]
GO
ALTER TABLE [dbo].[wap_paimaiOrder] ADD  CONSTRAINT [DF__wap_paima__isChe__3A6CA48E]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_paimaire] ADD  CONSTRAINT [DF_wap_paimaire_redate]  DEFAULT (getdate()) FOR [redate]
GO
ALTER TABLE [dbo].[wap_paimaire] ADD  CONSTRAINT [DF__wap_paima__isChe__3B60C8C7]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_picture] ADD  CONSTRAINT [DF_wap_picture_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_picture] ADD  CONSTRAINT [DF_wap_picture_book_classid]  DEFAULT (0) FOR [book_classid]
GO
ALTER TABLE [dbo].[wap_picture] ADD  CONSTRAINT [DF_wap_picture_book_re]  DEFAULT (0) FOR [book_re]
GO
ALTER TABLE [dbo].[wap_picture] ADD  CONSTRAINT [DF_wap_picture_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_picture] ADD  CONSTRAINT [DF_wap_picture_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[wap_picture] ADD  CONSTRAINT [DF_wap_picture_sysid]  DEFAULT (0) FOR [sysid]
GO
ALTER TABLE [dbo].[wap_picture] ADD  CONSTRAINT [DF_wap_picture_money]  DEFAULT (0) FOR [money]
GO
ALTER TABLE [dbo].[wap_picture] ADD  CONSTRAINT [DF_wap_picture_nextpage]  DEFAULT (0) FOR [nextpage]
GO
ALTER TABLE [dbo].[wap_picture] ADD  CONSTRAINT [DF__wap_pictu__book___1FA39FB9]  DEFAULT (0) FOR [book_down]
GO
ALTER TABLE [dbo].[wap_picture] ADD  CONSTRAINT [DF__wap_pictu__isChe__3C54ED00]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_picturere] ADD  CONSTRAINT [DF_wap_picturere_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_picturere] ADD  CONSTRAINT [DF_wap_picturere_classid]  DEFAULT (0) FOR [classid]
GO
ALTER TABLE [dbo].[wap_picturere] ADD  CONSTRAINT [DF_wap_picturere_bookid]  DEFAULT (0) FOR [bookid]
GO
ALTER TABLE [dbo].[wap_picturere] ADD  CONSTRAINT [DF_wap_picturere_redate]  DEFAULT (getdate()) FOR [redate]
GO
ALTER TABLE [dbo].[wap_picturere] ADD  CONSTRAINT [DF__wap_pictu__isChe__3D491139]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_question] ADD  CONSTRAINT [DF_wap_question_book_re]  DEFAULT (0) FOR [book_re]
GO
ALTER TABLE [dbo].[wap_question] ADD  CONSTRAINT [DF_wap_question_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_question] ADD  CONSTRAINT [DF_wap_question_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[wap_question] ADD  CONSTRAINT [DF_wap_question_book_good]  DEFAULT (0) FOR [book_good]
GO
ALTER TABLE [dbo].[wap_question] ADD  CONSTRAINT [DF_wap_question_book_top]  DEFAULT (0) FOR [book_top]
GO
ALTER TABLE [dbo].[wap_question] ADD  CONSTRAINT [DF_wap_question_ishidden]  DEFAULT (0) FOR [ishidden]
GO
ALTER TABLE [dbo].[wap_questionre] ADD  CONSTRAINT [DF_wap_questionre_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_questionre] ADD  CONSTRAINT [DF_wap_questionre_redate]  DEFAULT (getdate()) FOR [redate]
GO
ALTER TABLE [dbo].[wap_ring] ADD  CONSTRAINT [DF_wap_ring_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_ring] ADD  CONSTRAINT [DF_wap_ring_book_classid]  DEFAULT (0) FOR [book_classid]
GO
ALTER TABLE [dbo].[wap_ring] ADD  CONSTRAINT [DF_wap_ring_book_re]  DEFAULT (0) FOR [book_re]
GO
ALTER TABLE [dbo].[wap_ring] ADD  CONSTRAINT [DF_wap_ring_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_ring] ADD  CONSTRAINT [DF_wap_ring_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[wap_ring] ADD  CONSTRAINT [DF_wap_ring_sysid]  DEFAULT (0) FOR [sysid]
GO
ALTER TABLE [dbo].[wap_ring] ADD  CONSTRAINT [DF_wap_ring_money]  DEFAULT (0) FOR [money]
GO
ALTER TABLE [dbo].[wap_ring] ADD  CONSTRAINT [DF__wap_ring__book_d__2097C3F2]  DEFAULT (0) FOR [book_down]
GO
ALTER TABLE [dbo].[wap_ring] ADD  CONSTRAINT [DF__wap_ring__isChec__3E3D3572]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_ring] ADD  DEFAULT (0) FOR [book_score]
GO
ALTER TABLE [dbo].[wap_ringre] ADD  CONSTRAINT [DF_wap_ringre_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_ringre] ADD  CONSTRAINT [DF_wap_ringre_classid]  DEFAULT (0) FOR [classid]
GO
ALTER TABLE [dbo].[wap_ringre] ADD  CONSTRAINT [DF_wap_ringre_bookid]  DEFAULT (0) FOR [bookid]
GO
ALTER TABLE [dbo].[wap_ringre] ADD  CONSTRAINT [DF_wap_ringre_redate]  DEFAULT (getdate()) FOR [redate]
GO
ALTER TABLE [dbo].[wap_ringre] ADD  CONSTRAINT [DF__wap_ringr__isChe__3F3159AB]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_room] ADD  CONSTRAINT [DF__wap_room__times__19D5B7CA]  DEFAULT (getdate()) FOR [times]
GO
ALTER TABLE [dbo].[wap_room] ADD  CONSTRAINT [DF__wap_room__isTop__65570293]  DEFAULT (0) FOR [isTop]
GO
ALTER TABLE [dbo].[wap_shop] ADD  CONSTRAINT [DF_wap_shop_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_shop] ADD  CONSTRAINT [DF_wap_shop_book_classid]  DEFAULT (0) FOR [book_classid]
GO
ALTER TABLE [dbo].[wap_shop] ADD  CONSTRAINT [DF_wap_shop_book_re]  DEFAULT (0) FOR [book_re]
GO
ALTER TABLE [dbo].[wap_shop] ADD  CONSTRAINT [DF_wap_shop_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_shop] ADD  CONSTRAINT [DF_wap_shop_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[wap_shop] ADD  CONSTRAINT [DF_wap_shop_book_good]  DEFAULT (0) FOR [book_good]
GO
ALTER TABLE [dbo].[wap_shop] ADD  CONSTRAINT [DF_wap_shop_book_top]  DEFAULT (0) FOR [book_top]
GO
ALTER TABLE [dbo].[wap_shop] ADD  CONSTRAINT [DF_wap_shop_book_jiage]  DEFAULT (0) FOR [book_jiage]
GO
ALTER TABLE [dbo].[wap_shop] ADD  CONSTRAINT [DF_wap_shop_book_yhjiage]  DEFAULT (0) FOR [book_yhjiage]
GO
ALTER TABLE [dbo].[wap_shop] ADD  CONSTRAINT [DF__wap_shop__isChec__40257DE4]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_shop] ADD  DEFAULT (0) FOR [saleCount]
GO
ALTER TABLE [dbo].[wap_shop] ADD  DEFAULT (0) FOR [book_copyid]
GO
ALTER TABLE [dbo].[wap_shopOrder] ADD  CONSTRAINT [DF_wap_shopOrder_productcount]  DEFAULT (0) FOR [productcount]
GO
ALTER TABLE [dbo].[wap_shopOrder] ADD  CONSTRAINT [DF_wap_shopOrder_state]  DEFAULT (0) FOR [state]
GO
ALTER TABLE [dbo].[wap_shopOrder] ADD  CONSTRAINT [DF_wap_shopOrder_orderdate]  DEFAULT (getdate()) FOR [orderdate]
GO
ALTER TABLE [dbo].[wap_shopOrder] ADD  CONSTRAINT [DF__wap_shopO__isChe__4119A21D]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_shopOrder] ADD  DEFAULT (0) FOR [fromUserID]
GO
ALTER TABLE [dbo].[wap_shopOrder] ADD  DEFAULT (0) FOR [fromLinkID]
GO
ALTER TABLE [dbo].[wap_shopre] ADD  CONSTRAINT [DF_wap_shopre_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_shopre] ADD  CONSTRAINT [DF_wap_shopre_classid]  DEFAULT (0) FOR [classid]
GO
ALTER TABLE [dbo].[wap_shopre] ADD  CONSTRAINT [DF_wap_shopre_bookid]  DEFAULT (0) FOR [bookid]
GO
ALTER TABLE [dbo].[wap_shopre] ADD  CONSTRAINT [DF_wap_shopre_redate]  DEFAULT (getdate()) FOR [redate]
GO
ALTER TABLE [dbo].[wap_shopre] ADD  CONSTRAINT [DF__wap_shopr__isChe__420DC656]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_sms_reg] ADD  CONSTRAINT [DF_wap_sms_reg_isClose]  DEFAULT (0) FOR [isClose]
GO
ALTER TABLE [dbo].[wap_vcount_Detail] ADD  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_vcount_everyDate] ADD  CONSTRAINT [DF_wap_vcount_everyDate_PV]  DEFAULT (0) FOR [PV]
GO
ALTER TABLE [dbo].[wap_vcount_everyDate] ADD  CONSTRAINT [DF_wap_vcount_everyDate_UV]  DEFAULT (0) FOR [UV]
GO
ALTER TABLE [dbo].[wap_vcount_everyDate] ADD  CONSTRAINT [DF_wap_vcount_everyDate_VV]  DEFAULT (0) FOR [VV]
GO
ALTER TABLE [dbo].[wap_vcount_everyDate] ADD  CONSTRAINT [DF_wap_vcount_everyDate_IP]  DEFAULT (0) FOR [IP]
GO
ALTER TABLE [dbo].[wap_video] ADD  CONSTRAINT [DF_wap_video_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_video] ADD  CONSTRAINT [DF_wap_video_book_classid]  DEFAULT (0) FOR [book_classid]
GO
ALTER TABLE [dbo].[wap_video] ADD  CONSTRAINT [DF_wap_video_book_re]  DEFAULT (0) FOR [book_re]
GO
ALTER TABLE [dbo].[wap_video] ADD  CONSTRAINT [DF_wap_video_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_video] ADD  CONSTRAINT [DF_wap_video_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[wap_video] ADD  CONSTRAINT [DF_wap_video_sysid]  DEFAULT (0) FOR [sysid]
GO
ALTER TABLE [dbo].[wap_video] ADD  CONSTRAINT [DF_wap_video_money]  DEFAULT (0) FOR [money]
GO
ALTER TABLE [dbo].[wap_video] ADD  CONSTRAINT [DF__wap_video__book___218BE82B]  DEFAULT (0) FOR [book_down]
GO
ALTER TABLE [dbo].[wap_video] ADD  CONSTRAINT [DF__wap_video__isChe__44EA3301]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_video] ADD  DEFAULT (0) FOR [book_score]
GO
ALTER TABLE [dbo].[wap_videore] ADD  CONSTRAINT [DF_wap_videore_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_videore] ADD  CONSTRAINT [DF_wap_videore_classid]  DEFAULT (0) FOR [classid]
GO
ALTER TABLE [dbo].[wap_videore] ADD  CONSTRAINT [DF_wap_videore_bookid]  DEFAULT (0) FOR [bookid]
GO
ALTER TABLE [dbo].[wap_videore] ADD  CONSTRAINT [DF_wap_videore_redate]  DEFAULT (getdate()) FOR [redate]
GO
ALTER TABLE [dbo].[wap_videore] ADD  CONSTRAINT [DF__wap_video__isChe__45DE573A]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_wabao] ADD  CONSTRAINT [DF_wap_wabao_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_wabao] ADD  CONSTRAINT [DF_wap_wabao_rand]  DEFAULT (0) FOR [rand]
GO
ALTER TABLE [dbo].[wap_wabao] ADD  CONSTRAINT [DF_wap_wabao_needMoney]  DEFAULT (0) FOR [needMoney]
GO
ALTER TABLE [dbo].[wap_wabao] ADD  CONSTRAINT [DF_wap_wabao_needExp]  DEFAULT (0) FOR [needExp]
GO
ALTER TABLE [dbo].[wap_wabao] ADD  CONSTRAINT [DF_wap_wabao_subMoney]  DEFAULT (0) FOR [subMoney]
GO
ALTER TABLE [dbo].[wap_wabao] ADD  CONSTRAINT [DF_wap_wabao_addMoney]  DEFAULT (0) FOR [addMoney]
GO
ALTER TABLE [dbo].[wap_wabao] ADD  CONSTRAINT [DF_wap_wabao_addExp]  DEFAULT (0) FOR [addExp]
GO
ALTER TABLE [dbo].[wap_wabao] ADD  CONSTRAINT [DF_wap_wabao_startTime]  DEFAULT (0) FOR [startTime]
GO
ALTER TABLE [dbo].[wap_wabao] ADD  CONSTRAINT [DF_wap_wabao_endTime]  DEFAULT (0) FOR [endTime]
GO
ALTER TABLE [dbo].[wap_wabao] ADD  CONSTRAINT [DF_wap_wabao_repeatGet]  DEFAULT (0) FOR [countAll]
GO
ALTER TABLE [dbo].[wap_wabao] ADD  CONSTRAINT [DF_wap_wabao_GetCount]  DEFAULT (0) FOR [countHasGet]
GO
ALTER TABLE [dbo].[wap_wabao] ADD  CONSTRAINT [DF_wap_wabao_isCheck]  DEFAULT (0) FOR [ischeck]
GO
ALTER TABLE [dbo].[wap_wabaoRe] ADD  CONSTRAINT [DF_wap_wabaoRe_userid]  DEFAULT (0) FOR [userid]
GO
ALTER TABLE [dbo].[wap_wml] ADD  CONSTRAINT [DF_wap_wml_book_re]  DEFAULT (0) FOR [book_re]
GO
ALTER TABLE [dbo].[wap_wml] ADD  CONSTRAINT [DF_wap_wml_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap_wml] ADD  CONSTRAINT [DF_wap_wml_sysid]  DEFAULT (0) FOR [sysid]
GO
ALTER TABLE [dbo].[wap_wml] ADD  CONSTRAINT [DF__wap_wml__isCheck__46D27B73]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_yuehui] ADD  CONSTRAINT [DF_wap_yuehui_yh_re]  DEFAULT (0) FOR [yh_re]
GO
ALTER TABLE [dbo].[wap_yuehui] ADD  CONSTRAINT [DF_wap_yuehui_yh_click]  DEFAULT (0) FOR [yh_click]
GO
ALTER TABLE [dbo].[wap_yuehui] ADD  CONSTRAINT [DF_wap_yuehui_yh_date]  DEFAULT (getdate()) FOR [yh_date]
GO
ALTER TABLE [dbo].[wap_yuehui] ADD  CONSTRAINT [DF_wap_yuehui_yh_good]  DEFAULT (0) FOR [yh_good]
GO
ALTER TABLE [dbo].[wap_yuehui] ADD  CONSTRAINT [DF_wap_yuehui_yh_top]  DEFAULT (0) FOR [yh_top]
GO
ALTER TABLE [dbo].[wap_yuehui] ADD  CONSTRAINT [DF__wap_yuehu__isChe__47C69FAC]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap_yuehuire] ADD  CONSTRAINT [DF__wap_yuehu__isChe__48BAC3E5]  DEFAULT (0) FOR [isCheck]
GO
ALTER TABLE [dbo].[wap2_attachment] ADD  CONSTRAINT [DF_wap2_attachment_book_date]  DEFAULT (getdate()) FOR [book_date]
GO
ALTER TABLE [dbo].[wap2_attachment] ADD  CONSTRAINT [DF_wap2_attachment_book_click]  DEFAULT (0) FOR [book_click]
GO
ALTER TABLE [dbo].[wap2_bbs_report] ADD  CONSTRAINT [DF_wap2_bbs_report_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap2_bbs_report] ADD  CONSTRAINT [DF__wap2_bbs___types__284DF453]  DEFAULT (0) FOR [types]
GO
ALTER TABLE [dbo].[wap2_games_appleUser] ADD  CONSTRAINT [DF_wap2_games_appleUser_state]  DEFAULT (0) FOR [state]
GO
ALTER TABLE [dbo].[wap2_games_chuiniu] ADD  CONSTRAINT [DF_wap2_games_chuiniu_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap2_games_config] ADD  CONSTRAINT [DF_wap2_games_config_todayTimes]  DEFAULT (0) FOR [todayTimes]
GO
ALTER TABLE [dbo].[wap2_games_config] ADD  CONSTRAINT [DF_wap2_games_config_todayMoney]  DEFAULT (0) FOR [todayMoney]
GO
ALTER TABLE [dbo].[wap2_games_config] ADD  CONSTRAINT [DF_wap2_games_config_updateTime]  DEFAULT (getdate()) FOR [updateTime]
GO
ALTER TABLE [dbo].[wap2_games_config] ADD  CONSTRAINT [DF_wap2_games_config_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap2_games_horseUser] ADD  CONSTRAINT [DF_wap2_games_horseUser_state]  DEFAULT (0) FOR [state]
GO
ALTER TABLE [dbo].[wap2_games_lucky28User] ADD  CONSTRAINT [DF_wap2_games_lucky28User_state]  DEFAULT (0) FOR [state]
GO
ALTER TABLE [dbo].[wap2_games_quankunUser] ADD  CONSTRAINT [DF_wap2_games_quankunUser_state]  DEFAULT (0) FOR [state]
GO
ALTER TABLE [dbo].[wap2_games_shoot] ADD  CONSTRAINT [DF_wap2_games_shoot_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap2_games_stone] ADD  CONSTRAINT [DF_wap2_games_stone_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap2_games_touziUser] ADD  CONSTRAINT [DF_wap2_games_touziUser_state]  DEFAULT (0) FOR [state]
GO
ALTER TABLE [dbo].[wap2_games_war] ADD  CONSTRAINT [DF_wap2_games_war_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap2_mobile_UA] ADD  CONSTRAINT [DF_wap2_mobile_UA_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap2_mobile_UA] ADD  CONSTRAINT [DF_wap2_mobile_UA_ShowIndex]  DEFAULT (0) FOR [ShowIndex]
GO
ALTER TABLE [dbo].[wap2_smallType] ADD  CONSTRAINT [DF_wap2_smallType_rank]  DEFAULT (0) FOR [rank]
GO
ALTER TABLE [dbo].[wap2_style] ADD  CONSTRAINT [DF_wap2_style_style_type]  DEFAULT (0) FOR [style_type]
GO
ALTER TABLE [dbo].[wap2_style] ADD  CONSTRAINT [DF_wap2_style_rank]  DEFAULT (0) FOR [rank]
GO
ALTER TABLE [dbo].[wap2_style] ADD  CONSTRAINT [DF_wap2_style_create_time]  DEFAULT (getdate()) FOR [create_time]
GO
ALTER TABLE [dbo].[wap2_style] ADD  CONSTRAINT [DF_wap2_style_isSystem]  DEFAULT (0) FOR [isSystem]
GO
ALTER TABLE [dbo].[wap2_userGuessBook] ADD  CONSTRAINT [DF_wap2_userGuessBook_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap2_userGuessBook] ADD  CONSTRAINT [DF_wap2_userGuessBook_ischeck]  DEFAULT (0) FOR [ischeck]
GO
ALTER TABLE [dbo].[wap2_visitZone] ADD  CONSTRAINT [DF_wap2_visitZone_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap3_htmlContent] ADD  CONSTRAINT [DF_wap3_htmlContent_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap3_ModelFunc] ADD  CONSTRAINT [DF_wap3_ModelFunc_systemID]  DEFAULT (0) FOR [systemID]
GO
ALTER TABLE [dbo].[wap3_ModelFunc] ADD  CONSTRAINT [DF_wap3_ModelFunc_isSystem]  DEFAULT (0) FOR [isSystem]
GO
ALTER TABLE [dbo].[wap3_ModelFunc] ADD  CONSTRAINT [DF_wap3_ModelFunc_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap3_ModelPage] ADD  CONSTRAINT [DF_wap3_ModelPage_systemID]  DEFAULT (0) FOR [systemID]
GO
ALTER TABLE [dbo].[wap3_ModelPage] ADD  CONSTRAINT [DF_wap3_ModelPage_isSystem]  DEFAULT (0) FOR [isSystem]
GO
ALTER TABLE [dbo].[wap3_ModelPage] ADD  CONSTRAINT [DF_wap3_ModelPage_addtime]  DEFAULT (getdate()) FOR [addtime]
GO
ALTER TABLE [dbo].[wap3_style] ADD  CONSTRAINT [DF_wap3_style_style_type]  DEFAULT (0) FOR [style_type]
GO
ALTER TABLE [dbo].[wap3_style] ADD  CONSTRAINT [DF_wap3_style_rank]  DEFAULT (0) FOR [rank]
GO
ALTER TABLE [dbo].[wap3_style] ADD  CONSTRAINT [DF_wap3_style_create_time]  DEFAULT (getdate()) FOR [create_time]
GO
ALTER TABLE [dbo].[wap3_style] ADD  CONSTRAINT [DF_wap3_style_isSystem]  DEFAULT (0) FOR [isSystem]
GO
ALTER TABLE [dbo].[XinZhang] ADD  CONSTRAINT [DF_XinZhang_ChuangJianShiJian]  DEFAULT (getdate()) FOR [ChuangJianShiJian]
GO
ALTER TABLE [dbo].[XinZhang] ADD  CONSTRAINT [DF_XinZhang_ShiFouMoRen]  DEFAULT (1) FOR [ShiFouMoRen]
GO
ALTER TABLE [dbo].[bbs_guessing]  WITH CHECK ADD  CONSTRAINT [FK_bbs_guessing_bbs_id] FOREIGN KEY([bbs_id])
REFERENCES [dbo].[wap_bbs] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[bbs_guessing] CHECK CONSTRAINT [FK_bbs_guessing_bbs_id]
GO
ALTER TABLE [dbo].[bbs_guessing_bets]  WITH CHECK ADD  CONSTRAINT [FK_bbs_guessing_bets_guessing_id] FOREIGN KEY([guessing_id])
REFERENCES [dbo].[bbs_guessing] ([id])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[bbs_guessing_bets] CHECK CONSTRAINT [FK_bbs_guessing_bets_guessing_id]
GO
ALTER TABLE [dbo].[UserPreferences]  WITH CHECK ADD  CONSTRAINT [FK_UserPreferences_user] FOREIGN KEY([UserId])
REFERENCES [dbo].[user] ([userid])
ON DELETE CASCADE
GO
ALTER TABLE [dbo].[UserPreferences] CHECK CONSTRAINT [FK_UserPreferences_user]
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'关联到 user 表的用户ID' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'UserPreferences', @level2type=N'COLUMN',@level2name=N'UserId'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'新版回帖UI开关 (1=开启, 0=关闭)' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'UserPreferences', @level2type=N'COLUMN',@level2name=N'NewReplyUIEnabled'
GO
EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'存储用户个性化偏好设置' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'UserPreferences'
GO
