using System;
using System.Collections.Generic;
using System.Linq;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;

namespace YaoHuo.Plugin.WebSite.Tool
{
    /// <summary>
    /// 头像信息数据模型
    /// </summary>
    public class AvatarInfo
    {
        /// <summary>
        /// 头像URL
        /// </summary>
        public string AvatarUrl { get; set; } = "";

        /// <summary>
        /// 是否为默认头像
        /// </summary>
        public bool IsDefaultAvatar { get; set; } = true;

        /// <summary>
        /// 用户昵称首字母（用于fallback显示）
        /// </summary>
        public string FirstChar { get; set; } = "?";

        /// <summary>
        /// 是否在线（可选）
        /// </summary>
        public bool IsOnline { get; set; } = false;

        /// <summary>
        /// 用户昵称（可选）
        /// </summary>
        public string Nickname { get; set; } = "";
    }

    /// <summary>
    /// 头像处理统一工具类
    /// 提供统一的头像数据获取、URL处理、fallback机制
    /// </summary>
    public static class AvatarHelper
    {
        /// <summary>
        /// 获取单个用户的头像信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteid">站点ID</param>
        /// <param name="connectionString">数据库连接字符串标识</param>
        /// <param name="httpStart">站点URL前缀</param>
        /// <returns>头像信息</returns>
        public static AvatarInfo GetUserAvatar(string userId, string siteid, string connectionString, string httpStart)
        {
            var result = new AvatarInfo();

            try
            {
                // 参数验证
                if (string.IsNullOrEmpty(userId) || !WapTool.IsNumeric(userId))
                {
                    return GetDefaultAvatar(httpStart);
                }

                // 查询用户信息
                user_BLL userBll = new user_BLL(connectionString);
                user_Model user = userBll.getUserInfo(userId, siteid);

                if (user != null)
                {
                    result.AvatarUrl = GetCorrectAvatarUrl(user.headimg, httpStart);
                    result.IsDefaultAvatar = IsDefaultAvatar(user.headimg);
                    result.FirstChar = GetFirstChar(user.nickname);
                    result.IsOnline = (user.isonline == "1");
                    result.Nickname = user.nickname ?? "";
                }
                else
                {
                    return GetDefaultAvatar(httpStart);
                }
            }
            catch (Exception)
            {
                return GetDefaultAvatar(httpStart);
            }

            return result;
        }

        /// <summary>
        /// 获取多个用户的头像信息（批量查询优化）
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="siteid">站点ID</param>
        /// <param name="connectionString">数据库连接字符串标识</param>
        /// <param name="httpStart">站点URL前缀</param>
        /// <returns>用户ID到头像信息的字典</returns>
        public static Dictionary<string, AvatarInfo> GetUsersAvatars(List<string> userIds, string siteid, string connectionString, string httpStart)
        {
            var result = new Dictionary<string, AvatarInfo>();

            try
            {
                // 参数验证
                if (userIds == null || userIds.Count == 0)
                {
                    return result;
                }

                // 过滤有效的用户ID
                var validUserIds = userIds.Where(id => !string.IsNullOrEmpty(id) && WapTool.IsNumeric(id)).ToList();
                if (validUserIds.Count == 0)
                {
                    return result;
                }

                // 使用DapperHelper进行批量查询
                string sql = "SELECT userid, nickname, headimg, isonline FROM [user] WHERE siteid = @SiteId AND userid IN @UserIds";
                var users = DapperHelper.Query<user_Model>(PubConstant.GetConnectionString(connectionString), sql, new 
                { 
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    UserIds = validUserIds.Select(id => DapperHelper.SafeParseLong(id, "用户ID")).ToList()
                });

                // 处理查询结果
                foreach (var user in users)
                {
                    var avatarInfo = new AvatarInfo
                    {
                        AvatarUrl = GetCorrectAvatarUrl(user.headimg, httpStart),
                        IsDefaultAvatar = IsDefaultAvatar(user.headimg),
                        FirstChar = GetFirstChar(user.nickname),
                        IsOnline = (user.isonline == "1"),
                        Nickname = user.nickname ?? ""
                    };
                    result[user.userid.ToString()] = avatarInfo;
                }

                // 为未查询到的用户ID添加默认头像
                foreach (var userId in validUserIds)
                {
                    if (!result.ContainsKey(userId))
                    {
                        result[userId] = GetDefaultAvatar(httpStart);
                    }
                }
            }
            catch (Exception)
            {
                // 异常时为所有用户返回默认头像
                foreach (var userId in userIds.Where(id => !string.IsNullOrEmpty(id)))
                {
                    result[userId] = GetDefaultAvatar(httpStart);
                }
            }

            return result;
        }

        /// <summary>
        /// 获取默认头像信息
        /// </summary>
        /// <param name="httpStart">站点URL前缀</param>
        /// <returns>默认头像信息</returns>
        public static AvatarInfo GetDefaultAvatar(string httpStart)
        {
            return new AvatarInfo
            {
                AvatarUrl = GetCorrectAvatarUrl("", httpStart),
                IsDefaultAvatar = true,
                FirstChar = "?",
                IsOnline = false,
                Nickname = ""
            };
        }

        /// <summary>
        /// 获取正确的头像URL
        /// 统一的头像URL处理逻辑
        /// </summary>
        /// <param name="headimg">头像路径</param>
        /// <param name="httpStart">站点URL前缀</param>
        /// <returns>完整的头像URL</returns>
        public static string GetCorrectAvatarUrl(string headimg, string httpStart)
        {
            // 如果头像为空，使用默认头像
            if (string.IsNullOrEmpty(headimg))
            {
                return httpStart + "bbs/head/64.gif";
            }

            // 如果是外部链接（以http开头），直接返回
            if (headimg.StartsWith("http://") || headimg.StartsWith("https://"))
            {
                return headimg;
            }

            // 如果包含斜杠，说明是自定义路径（如相册图片），直接拼接站点URL
            if (headimg.IndexOf("/") >= 0)
            {
                return httpStart + headimg;
            }

            // 否则是系统头像，拼接系统头像路径
            return httpStart + "bbs/head/" + headimg;
        }

        /// <summary>
        /// 判断是否为默认头像
        /// </summary>
        /// <param name="headimg">头像路径</param>
        /// <returns>是否为默认头像</returns>
        public static bool IsDefaultAvatar(string headimg)
        {
            return string.IsNullOrEmpty(headimg) || headimg == "64.gif";
        }

        /// <summary>
        /// 获取昵称的首字母用于头像fallback
        /// </summary>
        /// <param name="nickname">昵称</param>
        /// <returns>首字母</returns>
        public static string GetFirstChar(string nickname)
        {
            if (string.IsNullOrEmpty(nickname))
                return "?";

            char firstChar = nickname[0];

            // 如果是中文字符，直接返回
            if (firstChar >= 0x4e00 && firstChar <= 0x9fbb)
                return firstChar.ToString();

            // 如果是英文字符，转换为大写
            if (char.IsLetter(firstChar))
                return firstChar.ToString().ToUpper();

            // 如果是数字字符，直接返回
            if (char.IsDigit(firstChar))
                return firstChar.ToString();

            // 其他情况返回问号
            return "?";
        }
    }
}