<Project>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30706</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{E444**************-4444-************}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>YaoHuo.Plugin</RootNamespace>
    <AssemblyName>YaoHuo.Plugin</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>false</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Template\Models\HeaderOptionsModel.cs" />
    <Compile Include="Template\Models\ModifyHeadPageModel.cs" />
    <Compile Include="WebSite\Services\FriendQueryService.cs" />
    <Compile Include="WebSite\Services\FriendQueryServiceTest.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="NetCSS\CSS\HandlebarsCommon.css" />
    <Content Include="NetCSS\CSS\ModifyHead.css" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Template\Pages\MyFile.hbs" />
    <Content Include="Template\Pages\ModifyHead.hbs" />
  </ItemGroup>
</Project> 