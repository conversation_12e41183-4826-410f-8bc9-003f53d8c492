﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_List_Search.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Book_List_Search" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%@ Import Namespace="System.Data" %>
<%
    Response.Write(WapTool.showTop(classVo.classname, wmlVo));
    StringBuilder strhtml_list = new StringBuilder();

    // 添加搜索页面样式（包含派币帖变灰功能）
    strhtml.Append(GetSearchPageStyles());

    if (adVo.secondShowTop != "")
    {
        strhtml.Append(adVo.secondShowTop);
    }

    //显示清空用户的所有贴子
    if (this.type == "pub")
    {
        if (this.IsUserManager(this.userid, userVo.managerlvl, "") && this.key != "1000")
        {
            strhtml.Append("<div class=\"tip\"><a class=\"urlbtn\" onclick=\"return confirm('清空帖子前请先确认操作');\" href=\"" + this.http_start + "bbs/book_list_delmy.aspx?action=go&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;page=" + this.CurrentPage + "&amp;touserid=" + this.key + "\">清空(" + this.key + ")的所有贴子</a></div>");
        }
    }

    // 修改搜索框显示条件
    string pubUserId = Request.QueryString["pub"];
    string searchKey = Request.QueryString["key"];
    string searchType = Request.QueryString["type"];

    // 修改显示条件，增加 type=content 和 type=title 的情况
    if (!string.IsNullOrEmpty(pubUserId) ||
    (searchType == "pub" && !string.IsNullOrEmpty(searchKey)) ||
    searchType == "content" ||
    searchType == "title")
    {
        // 引入 CSS
        strhtml.Append("<link rel='stylesheet' href='/NetCSS/JS/BookList/BookSearch.css' />");

        // 构建搜索框 HTML
        strhtml.Append("<div class='search-container'>");
        strhtml.Append("<div class='search-type' id='searchType'>");

        // 根据当前搜索类型设置默认选中项
        string currentType = searchType == "content" ? "内容" : "标题";

        // 获取用户ID
        string userIdForPlaceholder = pubUserId;
        if (string.IsNullOrEmpty(userIdForPlaceholder) && searchType == "pub")
        {
            userIdForPlaceholder = searchKey;
        }

        // 构建placeholder文本
        string placeholderText = !string.IsNullOrEmpty(userIdForPlaceholder)
            ? "搜索" + userIdForPlaceholder + "的帖子" + currentType
            : "搜索" + currentType;

        // 构建搜索类型选择器
        strhtml.Append("<span class='search-type-text'>" + currentType + "</span>");
        strhtml.Append("<div class='search-type-arrow'></div>");
        strhtml.Append("<div class='search-type-options'>");
        strhtml.Append("<div class='search-type-option" + (currentType == "标题" ? " active" : "") + "' data-type='标题'>标题</div>");
        strhtml.Append("<div class='search-type-option" + (currentType == "内容" ? " active" : "") + "' data-type='内容'>内容</div>");
        strhtml.Append("</div></div>");

        // 搜索输入框
        string inputValue = "";
        if (searchType != "pub")
        {
            inputValue = HttpUtility.HtmlEncode(searchKey ?? "");
        }
        strhtml.Append("<input type='text' class='search-input' id='searchInput' placeholder='" + placeholderText + "' value='" + inputValue + "' />");

        // 搜索按钮
        strhtml.Append("<button class='search-button' onclick='BookSearch.submitSearch()'>搜索</button>");
        strhtml.Append("</div>");

        // 注入全局变量
        strhtml.Append("<script>");
        strhtml.Append("window.baseUrl = '" + this.http_start + "';");
        strhtml.Append("window.siteid = '" + siteid + "';");
        strhtml.Append("window.classid = '" + classid + "';");
        strhtml.Append("</script>");

        // 引入 JS 文件
        strhtml.Append("<script src='/NetCSS/JS/BookList/BookSearch.js'></script>");
    }

    // 显示列表
    string lpagetemp = "";
    if (this.CurrentPage > 1)
    {
        if (WapTool.ISAPI_Rewrite3_Open == "1")
        {
            lpagetemp = "?lpage=" + CurrentPage;
        }
        else
        {
            lpagetemp = "&amp;lpage=" + CurrentPage;
        }
    }
    strhtml_list.Append("<!--listS-->");
    //所有列表
    for (int i = 0; (listVo != null && i < listVo.Count); i++)
    {
        if (i % 2 == 0)
        {
            strhtml_list.Append("<div class=\"listdata line2\">");
        }
        else
        {
            strhtml_list.Append("<div class=\"listdata line1\">");
        }
        index = index + kk;
        // 使用统一的搜索结果帖子图标渲染器（包含派币帖变灰功能）
        strhtml_list.Append(RenderSearchPostIcons(listVo[i], (int)index));
        if (WapTool.ISAPI_Rewrite3_Open == "1")
        {
            strhtml_list.Append("<a class=\"topic-link\" href=\"" + http_start + "bbs-" + listVo[i].id + ".html" + lpagetemp + "" + "\">" + listVo[i].book_title + "</a><br/><span class=\"louzhunicheng\">" + ShowNickName_color(long.Parse(listVo[i].book_pub), listVo[i].book_author) + "</span>/<a class=\"topic-link\" href=\"" + this.http_start + "bbs/book_re.aspx?actoin=class&amp;siteid=" + this.siteid + "&amp;classid=" + listVo[i].book_classid + "&amp;id=" + listVo[i].id + "&amp;getTotal=" + listVo[i].book_re + "&amp;lpage=" + this.CurrentPage + "\">" + listVo[i].book_re + "</a>回/" + listVo[i].book_click + "阅 <span class=\"right\">" + WapTool.ShowTime(listVo[i].book_date) + "<span></div>");
        }
        else
        {
            strhtml_list.Append("<a class=\"topic-link\" href=\"" + http_start + "bbs/view.aspx?id=" + listVo[i].id + lpagetemp + "" + "\">" + listVo[i].book_title + "</a><br/><span class=\"louzhunicheng\">" + ShowNickName_color(long.Parse(listVo[i].book_pub), listVo[i].book_author) + "</span>/<a class=\"topic-link\" href=\"" + this.http_start + "bbs/book_re.aspx?actoin=class&amp;siteid=" + this.siteid + "&amp;classid=" + listVo[i].book_classid + "&amp;id=" + listVo[i].id + "&amp;getTotal=" + listVo[i].book_re + "&amp;lpage=" + this.CurrentPage + "\">" + listVo[i].book_re + "</a>回/" + listVo[i].book_click + "阅 <span class=\"right\">" + WapTool.ShowTime(listVo[i].book_date) + "<span></div>");
        }
    }
    if (listVo == null)
    {
        strhtml_list.Append("<div class=\"tip\">暂无记录！</div>");
    }
    strhtml_list.Append("<!--listE-->");

    //显示导航分页
    strhtml_list.Append(linkURL);
    strhtml.Append(strhtml_list);

    if (adVo.secondShowDown != "")
    {
        strhtml.Append(adVo.secondShowDown);
    }
    strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookList/KL_common.js\"></script>");
    strhtml.Append(WapTool.GetVS(wmlVo));
    strhtml.Append(classVo.sitedowntip);
    Response.Write(WapTool.ToWML(strhtml.ToString(), wmlVo));
    Response.Write(ERROR);
    Response.Write(WapTool.showDown(wmlVo));
%>