<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 妖火网</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons CDN -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- 自定义配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#58b4b0',
                        'primary-dark': '#4a9c98',
                        'primary-light': '#7cd0cb',
                        'secondary': '#9CCCC8',
                        'text-primary': '#1f2937',
                        'text-secondary': '#6b7280',
                        'text-tertiary': '#4b5563',
                        'text-light': '#9ca3af',
                        'success': '#10b981',
                        'danger': '#dc2626',
                        'danger-dark': '#b91c1c',
                        'warning': '#d97706',
                        'info': '#3b82f6',
                        'bg-primary': '#f9fafb',
                        'bg-gray-100': '#f3f4f6',
                        'border-light': '#f3f4f6',
                        'border-normal': '#e5e7eb',
                        'border-dark': '#d1d5db',
                    },
                    screens: {
                        'max-768': {'max': '768px'},
                        'xs-480': {'max': '480px'},
                        'xs-400': {'max': '400px'},
                        'xs-390': {'max': '390px'},
                        'xs-350': {'max': '350px'},
                        'xs-310': {'max': '310px'},
                    },
                    zIndex: {
                        'dropdown': '10',
                        'modal': '1000',
                        'toast': '1001',
                    }
                }
            }
        }
    </script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #E8E8E8;
        }
        
        .container {
            max-width: 720px !important;
            margin: 0 auto !important;
            background-color: #f9fafb;
            min-height: 100vh;
        }
        
        .main-content {
            padding-top: 4rem;
            padding-bottom: 1rem;
            overflow-x: hidden;
        }
        
        .header {
            max-width: 720px;
            margin: 0 auto;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
        }

        .header-icon {
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 0.25rem;
            transition: all 0.2s;
            position: relative;
        }

        .header-icon:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .header-title {
            font-size: 1.125rem;
            font-weight: 500;
            flex: 1;
            text-align: center;
            margin: 0 0.5rem;
        }

        .header-actions-right {
            min-width: 2rem;
            display: flex;
            justify-content: flex-end;
            align-items: center;
        }
        
        /* 卡片组件 */
        .card {
            background-color: white;
            border-radius: 0.375rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
            overflow: hidden;
            margin-left: 1rem;
            margin-right: 1rem;
            margin-top: 1rem;
        }
        
        .card-header {
            padding: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .card-title {
            font-size: 1rem;
            font-weight: 500;
            color: #1f2937;
            display: flex;
            align-items: center;
        }
        
        .card-icon {
            color: #58b4b0;
            margin-right: 0.5rem;
        }
        
        .card-body {
            padding: 1rem;
        }
        
        /* 按钮组件 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.2s;
            cursor: pointer;
            user-select: none;
            border: 1px solid transparent;
        }
        
        .btn-primary {
            background-color: #58b4b0;
            color: white;
            border-color: #58b4b0;
        }
        
        .btn-primary:hover {
            background-color: #4a9c98;
            border-color: #4a9c98;
        }
        
        .btn-outline {
            background-color: transparent;
            color: #4b5563;
            border-color: #e5e7eb;
        }
        
        .btn-outline:hover {
            background-color: #f9fafb;
            border-color: #d1d5db;
            color: #1f2937;
        }
        
        .btn-destructive {
            background-color: #dc2626;
            color: white;
            border-color: #dc2626;
        }
        
        .btn-destructive:hover {
            background-color: #b91c1c;
            border-color: #b91c1c;
        }
        
        /* 网格布局 */
        .grid-2 {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
        }
        
        .grid-3 {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.75rem;
        }
        
        .grid-4 {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0.75rem;
        }
        
        /* 统计项组件 */
        .stats-item {
            display: flex;
            flex-direction: column;
            cursor: pointer;
            padding: 0.5rem;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
            border-radius: 0.125rem;
        }
        
        .stats-item:hover {
            transform: translateY(-0.125rem);
            background-color: rgba(88, 180, 176, 0.05);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            padding: 0.75rem;
            text-align: center;
            border-bottom: 1px solid #f3f4f6;
        }
        
        /* 下拉菜单 */
        .dropdown {
            position: relative;
            z-index: 10;
        }
        
        .dropdown-menu {
            position: absolute;
            left: 50%;
            top: 100%;
            margin-top: 0.5rem;
            background-color: white;
            border-radius: 0.375rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            width: auto;
            min-width: fit-content;
            z-index: 90;
            overflow: hidden;
            opacity: 0;
            visibility: hidden;
            transform: translateX(-50%) translateY(-10px);
            transition: all 0.2s;
            pointer-events: none;
        }
        
        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(0);
            pointer-events: auto;
        }
        
        .dropdown-item {
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            color: #4b5563;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            cursor: pointer;
            transition: all 0.2s;
            text-align: left;
            white-space: nowrap;
        }
        
        .dropdown-item:hover {
            background-color: #f3f4f6;
        }
        
        .edit-profile {
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 0.375rem;
            padding: 0.5rem 0.75rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: all 0.2s;
            user-select: none;
            position: relative;
            z-index: 50;
        }
        
        .edit-profile:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <header class="header fixed top-0 w-full z-50 shadow-md text-white" style="background: linear-gradient(135deg, #58b4b0 0%, #4a9c98 100%);">
            <div class="header-content">
                <div class="header-icon" id="back-button">
                    <i data-lucide="arrow-left" class="w-6 h-6"></i>
                </div>
                <div class="header-title">个人中心</div>
                <div class="header-actions-right">
                    <div class="header-icon dropdown" id="theme-toggle">
                        <i data-lucide="brush" class="w-5 h-5"></i>
                        <div class="dropdown-menu" id="skin-dropdown">
                            <div class="dropdown-item active">
                                <span>新版</span>
                            </div>
                            <div class="dropdown-item" onclick="alert('切换到旧版');">
                                <span>旧版</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <div class="main-content">
            <!-- 个人信息卡片 -->
            <div class="card">
                <div class="bg-gradient-to-br from-primary to-primary-dark p-4">
                    <div class="flex items-center justify-between">
                        <div class="text-white">
                            <div class="flex items-center mb-1 text-left">
                                <span class="text-2xl font-bold">Clover</span>
                            </div>
                            <div class="mt-1 text-left">
                                <span class="ml-0 bg-white bg-opacity-20 text-white text-xs py-0.5 px-2 rounded-full opacity-80">ID: 1000</span>
                            </div>
                        </div>
                        <!-- 编辑资料下拉菜单 -->
                        <div class="edit-profile dropdown" id="profile-edit-dropdown">
                            <i data-lucide="pen-line" class="w-4 h-4 text-white"></i>
                            <span class="text-white text-sm font-medium ml-1">编辑资料</span>
                            <i data-lucide="chevron-down" class="w-4 h-4 text-white"></i>
                            <div class="dropdown-menu" id="profile-edit-dropdown-menu">
                                <div class="dropdown-item">
                                    <i data-lucide="user-cog" class="w-4 h-4 mr-2"></i>
                                    <span>修改资料</span>
                                </div>
                                <div class="dropdown-item">
                                    <i data-lucide="key" class="w-4 h-4 mr-2"></i>
                                    <span>更改密码</span>
                                </div>
                                <div class="dropdown-item">
                                    <i data-lucide="user-circle" class="w-4 h-4 mr-2"></i>
                                    <span>更换头像</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据统计 -->
                <div class="stats-grid">
                    <div class="stats-item">
                        <span class="text-text-primary font-medium transition-colors">0/16892</span>
                        <span class="text-xs text-text-secondary transition-colors">信箱</span>
                    </div>
                    <div class="stats-item">
                        <span class="text-text-primary font-medium transition-colors">279</span>
                        <span class="text-xs text-text-secondary transition-colors">好友</span>
                    </div>
                    <div class="stats-item">
                        <span class="text-text-primary font-medium transition-colors">2428</span>
                        <span class="text-xs text-text-secondary transition-colors">帖子</span>
                    </div>
                    <div class="stats-item">
                        <span class="text-text-primary font-medium transition-colors">64440</span>
                        <span class="text-xs text-text-secondary transition-colors">回复</span>
                    </div>
                </div>

                <!-- 等级进度 -->
                <div class="p-4">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm text-text-secondary">经验值: 1,170,548</span>
                        <span class="text-sm text-primary">12级</span>
                    </div>
                    <div class="w-full h-2 bg-gray-200 rounded-sm overflow-hidden">
                        <div class="h-full bg-gradient-to-r from-primary to-primary-light rounded-sm transition-all duration-1000" style="width: 85%;"></div>
                    </div>
                </div>
            </div>

            <!-- 我的资产 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i data-lucide="wallet" class="card-icon"></i>
                        我的资产
                    </h2>
                </div>
                <div class="card-body">
                    <div class="flex justify-between items-center py-2 border-b border-border-light">
                        <div class="text-sm text-text-secondary flex items-center">
                            <i data-lucide="gem" class="w-4 h-4 mr-1"></i>
                            我的妖晶
                        </div>
                        <div class="font-medium text-primary font-semibold text-right">1,241,443</div>
                    </div>
                    <div class="flex justify-between items-center py-2">
                        <div class="text-sm text-text-secondary flex items-center">
                            <i data-lucide="credit-card" class="w-4 h-4 mr-1"></i>
                            我的RMB
                        </div>
                        <div class="font-medium text-primary font-semibold text-right">￥100.00</div>
                    </div>
                    <div class="grid-2 mt-4">
                        <button class="btn btn-primary">
                            <i data-lucide="plus" class="w-4 h-4 mr-1"></i>
                            充值
                        </button>
                        <button class="btn btn-outline">
                            <i data-lucide="receipt" class="w-4 h-4 mr-1"></i>
                            明细
                        </button>
                    </div>
                </div>
            </div>

            <!-- 个人信息 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i data-lucide="user-circle" class="card-icon"></i>
                        个人信息
                    </h2>
                </div>
                <div class="card-body">
                    <div class="flex justify-between items-center py-2 border-b border-border-light">
                        <div class="text-sm text-text-secondary flex items-center">
                            <i data-lucide="user" class="w-4 h-4 mr-1"></i>
                            我的身份
                        </div>
                        <div class="font-medium text-text-primary text-right">
                            <span class="inline-flex items-center py-1 px-2 rounded text-xs font-medium" style="background-color: #fef2f2; color: #dc2626; border: 1px solid rgba(220, 38, 38, 0.2);">红色昵称</span>
                        </div>
                    </div>
                    <div class="flex justify-between items-center py-2">
                        <div class="text-sm text-text-secondary flex items-center">
                            <i data-lucide="calendar" class="w-4 h-4 mr-1"></i>
                            有效期至
                        </div>
                        <div class="font-medium text-text-primary text-right">
                            <span>无期限</span>
                            <button class="ml-2 text-primary hover:text-primary-dark transition-colors text-sm">开通VIP</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的内容 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i data-lucide="settings" class="card-icon"></i>
                        我的内容
                    </h2>
                </div>
                <div class="card-body">
                    <div class="grid-2">
                        <button class="btn btn-outline flex items-center">
                            <i data-lucide="bookmark" class="w-4 h-4 mr-1"></i>
                            我的收藏
                        </button>
                        <button class="btn btn-outline flex items-center">
                            <i data-lucide="images" class="w-4 h-4 mr-1"></i>
                            我的相册
                        </button>
                        <button class="btn btn-outline flex items-center">
                            <i data-lucide="user-round" class="w-4 h-4 mr-1"></i>
                            我的空间
                        </button>
                        <button class="btn btn-outline flex items-center">
                            <i data-lucide="user-x" class="w-4 h-4 mr-1"></i>
                            黑名单
                        </button>
                    </div>
                </div>
            </div>

            <!-- 网站规则 -->
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <i data-lucide="book-open" class="card-icon"></i>
                        网站规则
                    </h2>
                </div>
                <div class="card-body">
                    <div class="grid-3">
                        <button class="btn btn-outline flex flex-col items-center justify-center h-auto p-2">
                            <i data-lucide="gem" class="w-4 h-4 mb-1"></i>
                            <span class="text-xs">妖晶规则</span>
                        </button>
                        <button class="btn btn-outline flex flex-col items-center justify-center h-auto p-2">
                            <i data-lucide="trending-up" class="w-4 h-4 mb-1"></i>
                            <span class="text-xs">等级规则</span>
                        </button>
                        <button class="btn btn-outline flex flex-col items-center justify-center h-auto p-2">
                            <i data-lucide="clock" class="w-4 h-4 mb-1"></i>
                            <span class="text-xs">在线时间</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 退出登录按钮 -->
            <div class="mx-4 my-6">
                <button class="btn btn-destructive w-full py-3 flex items-center justify-center">
                    <i data-lucide="log-out" class="w-4 h-4 mr-2"></i>
                    安全退出
                </button>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化 Lucide 图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }

            // 设置编辑资料下拉菜单
            setupEditProfileDropdown();

            // 设置header下拉菜单
            setupHeaderDropdowns();

            // 设置返回按钮
            setupBackButton();

            // 为所有按钮添加点击效果
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('按钮被点击:', this.textContent.trim());
                });
            });
        });

        // 设置编辑资料下拉菜单
        function setupEditProfileDropdown() {
            const dropdownToggle = document.querySelector('#profile-edit-dropdown');
            const dropdownMenu = document.getElementById('profile-edit-dropdown-menu');

            if (!dropdownToggle || !dropdownMenu) {
                return;
            }

            // 点击按钮切换下拉菜单显示/隐藏
            dropdownToggle.addEventListener('click', function(event) {
                event.stopPropagation();

                const isCurrentlyOpen = dropdownMenu.classList.contains('show');

                if (isCurrentlyOpen) {
                    dropdownMenu.classList.remove('show');
                } else {
                    closeAllDropdowns();
                    dropdownMenu.classList.add('show');
                }
            });

            // 点击文档其他地方关闭下拉菜单
            document.addEventListener('click', function(event) {
                if (!dropdownMenu.contains(event.target) && !dropdownToggle.contains(event.target)) {
                    closeAllDropdowns();
                }
            });
        }

        // 设置header下拉菜单
        function setupHeaderDropdowns() {
            const themeToggle = document.getElementById('theme-toggle');
            const skinDropdown = document.getElementById('skin-dropdown');

            if (themeToggle && skinDropdown) {
                themeToggle.addEventListener('click', function(e) {
                    e.stopPropagation();

                    const isCurrentlyOpen = skinDropdown.classList.contains('show');
                    closeAllDropdowns();

                    if (!isCurrentlyOpen) {
                        skinDropdown.classList.add('show');
                    }
                });

                // 处理新版按钮点击
                const activeItem = skinDropdown.querySelector('.dropdown-item.active');
                if (activeItem) {
                    activeItem.addEventListener('click', function(e) {
                        e.stopPropagation();
                        closeAllDropdowns();
                        showToast('当前已是新版界面');
                    });
                }
            }
        }

        // 设置返回按钮
        function setupBackButton() {
            const backButton = document.getElementById('back-button');
            if (backButton) {
                backButton.addEventListener('click', function() {
                    console.log('返回按钮被点击');
                    // 这里可以添加返回逻辑
                });
            }
        }

        // 显示提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.textContent = message;
            toast.className = 'fixed bottom-5 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 text-white py-2.5 px-5 rounded z-[1000]';
            toast.style.opacity = '0';
            toast.style.transition = 'opacity 0.3s ease';

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '1';
            }, 10);

            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 2000);
        }

        // 关闭所有下拉菜单
        function closeAllDropdowns() {
            const profileDropdown = document.getElementById('profile-edit-dropdown-menu');
            if (profileDropdown) {
                profileDropdown.classList.remove('show');
            }

            const skinDropdown = document.getElementById('skin-dropdown');
            if (skinDropdown) {
                skinDropdown.classList.remove('show');
            }
        }
    </script>
</body>
</html>
