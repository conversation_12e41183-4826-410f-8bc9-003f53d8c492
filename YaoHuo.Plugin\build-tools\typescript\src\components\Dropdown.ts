/**
 * 下拉菜单组件
 * 统一管理所有页面的下拉菜单功能
 * 消除重复代码，提供一致的下拉菜单体验
 * 
 * @version 1.0
 * <AUTHOR>
 * @date 2025-01-07
 */

import { DropdownConfig, DropdownItem } from '../types/CommonTypes.js';

/**
 * 下拉菜单组件类
 * 提供统一的下拉菜单创建、显示和交互功能
 */
export class DropdownComponent {
    private static instance: DropdownComponent;
    private activeDropdowns: Map<string, HTMLElement> = new Map();
    private dropdownCounter: number = 0;

    /**
     * 获取单例实例
     */
    public static getInstance(): DropdownComponent {
        if (!DropdownComponent.instance) {
            DropdownComponent.instance = new DropdownComponent();
        }
        return DropdownComponent.instance;
    }

    /**
     * 创建下拉菜单
     * @param triggerId 触发按钮ID
     * @param config 下拉菜单配置
     */
    public static create(triggerId: string, config: DropdownConfig): string {
        return DropdownComponent.getInstance().createDropdown(triggerId, config);
    }

    /**
     * 显示下拉菜单
     * @param dropdownId 下拉菜单ID
     */
    public static show(dropdownId: string): void {
        DropdownComponent.getInstance().showDropdown(dropdownId);
    }

    /**
     * 隐藏下拉菜单
     * @param dropdownId 下拉菜单ID
     */
    public static hide(dropdownId: string): void {
        DropdownComponent.getInstance().hideDropdown(dropdownId);
    }

    /**
     * 隐藏所有下拉菜单
     */
    public static hideAll(): void {
        DropdownComponent.getInstance().hideAllDropdowns();
    }

    /**
     * 创建下拉菜单
     */
    public createDropdown(triggerId: string, config: DropdownConfig): string {
        const dropdownId = this.generateDropdownId();
        const triggerElement = document.getElementById(triggerId);
        
        if (!triggerElement) {
            console.error(`Dropdown trigger element not found: ${triggerId}`);
            return '';
        }

        // 创建下拉菜单元素
        const dropdownElement = this.createDropdownElement(dropdownId, config);
        
        // 插入到触发元素的父容器中
        const container = triggerElement.parentElement;
        if (container) {
            // 确保容器是相对定位
            if (getComputedStyle(container).position === 'static') {
                container.style.position = 'relative';
            }
            container.appendChild(dropdownElement);
        }

        // 绑定触发事件
        this.bindTriggerEvents(triggerElement, dropdownId, config);
        
        // 存储下拉菜单
        this.activeDropdowns.set(dropdownId, dropdownElement);

        return dropdownId;
    }

    /**
     * 显示下拉菜单
     */
    public showDropdown(dropdownId: string): void {
        const dropdown = this.activeDropdowns.get(dropdownId);
        if (!dropdown) return;

        // 隐藏其他下拉菜单
        this.hideAllDropdowns();

        // 显示当前下拉菜单
        dropdown.classList.add('show');

        // 调整位置
        this.adjustDropdownPosition(dropdown);

        // 绑定外部点击关闭
        this.bindOutsideClick(dropdownId);
    }

    /**
     * 隐藏下拉菜单
     */
    public hideDropdown(dropdownId: string): void {
        const dropdown = this.activeDropdowns.get(dropdownId);
        if (!dropdown) return;

        dropdown.classList.remove('show');
        this.unbindOutsideClick(dropdownId);
    }

    /**
     * 隐藏所有下拉菜单
     */
    public hideAllDropdowns(): void {
        for (const dropdownId of this.activeDropdowns.keys()) {
            this.hideDropdown(dropdownId);
        }
    }

    /**
     * 创建下拉菜单元素
     */
    private createDropdownElement(dropdownId: string, config: DropdownConfig): HTMLElement {
        const dropdown = document.createElement('div');
        dropdown.id = dropdownId;
        dropdown.className = 'dropdown-menu'; // 使用现有的CSS类

        // 根据配置调整位置
        if (config.position) {
            this.applyPositionStyles(dropdown, config.position);
        }

        // 创建菜单项
        config.items.forEach(item => {
            if (item.divider) {
                // 分割线
                const divider = document.createElement('div');
                divider.className = 'dropdown-divider';
                dropdown.appendChild(divider);
            } else {
                // 菜单项
                const menuItem = this.createMenuItem(item, dropdownId, config);
                dropdown.appendChild(menuItem);
            }
        });

        return dropdown;
    }

    /**
     * 创建菜单项
     */
    private createMenuItem(item: DropdownItem, dropdownId: string, config: DropdownConfig): HTMLElement {
        const menuItem = document.createElement('a');
        menuItem.href = '#';
        menuItem.className = `dropdown-item ${item.disabled ? 'opacity-50 cursor-not-allowed' : ''}`;
        menuItem.setAttribute('data-action', item.action);

        // 添加图标（如果有）
        if (item.icon) {
            const icon = document.createElement('i');
            icon.className = `w-4 h-4 mr-2`;
            icon.setAttribute('data-lucide', item.icon);
            menuItem.appendChild(icon);
        }

        // 添加文本
        const text = document.createElement('span');
        text.textContent = item.text;
        menuItem.appendChild(text);

        // 绑定点击事件
        if (!item.disabled) {
            menuItem.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                // 触发自定义事件
                const customEvent = new CustomEvent('dropdown-item-click', {
                    detail: {
                        action: item.action,
                        item: item,
                        dropdownId: dropdownId
                    }
                });
                document.dispatchEvent(customEvent);

                // 如果配置了点击后关闭
                if (config.closeOnClick !== false) {
                    this.hideDropdown(dropdownId);
                }
            });
        }

        return menuItem;
    }

    /**
     * 应用位置样式
     */
    private applyPositionStyles(dropdown: HTMLElement, position: DropdownConfig['position']): void {
        // 移除默认的居中样式
        dropdown.classList.remove('left-1/2', '-translate-x-1/2');

        switch (position) {
            case 'bottom-left':
                dropdown.classList.add('left-0');
                break;
            case 'bottom-right':
                dropdown.classList.add('right-0');
                break;
            case 'top-left':
                dropdown.classList.add('left-0', 'bottom-full', 'top-auto', 'mb-2', 'mt-0');
                break;
            case 'top-right':
                dropdown.classList.add('right-0', 'bottom-full', 'top-auto', 'mb-2', 'mt-0');
                break;
        }
    }

    /**
     * 绑定触发事件
     */
    private bindTriggerEvents(trigger: HTMLElement, dropdownId: string, config: DropdownConfig): void {
        const triggerType = config.trigger || 'click';

        if (triggerType === 'click') {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                const dropdown = this.activeDropdowns.get(dropdownId);
                if (dropdown && dropdown.classList.contains('show')) {
                    this.hideDropdown(dropdownId);
                } else {
                    this.showDropdown(dropdownId);
                }
            });
        } else if (triggerType === 'hover') {
            trigger.addEventListener('mouseenter', () => {
                this.showDropdown(dropdownId);
            });

            trigger.addEventListener('mouseleave', () => {
                // 延迟隐藏，允许鼠标移动到下拉菜单
                setTimeout(() => {
                    const dropdown = this.activeDropdowns.get(dropdownId);
                    if (dropdown && !dropdown.matches(':hover')) {
                        this.hideDropdown(dropdownId);
                    }
                }, 100);
            });
        }
    }

    /**
     * 调整下拉菜单位置
     */
    private adjustDropdownPosition(dropdown: HTMLElement): void {
        const rect = dropdown.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;

        // 检查是否超出视口
        if (rect.bottom > viewportHeight) {
            // 向上显示
            dropdown.style.bottom = '100%';
            dropdown.style.top = 'auto';
        }

        if (rect.right > viewportWidth) {
            // 向左显示
            dropdown.style.right = '0';
            dropdown.style.left = 'auto';
        }
    }

    /**
     * 绑定外部点击关闭
     */
    private bindOutsideClick(dropdownId: string): void {
        const handler = (e: Event) => {
            const dropdown = this.activeDropdowns.get(dropdownId);
            if (dropdown && !dropdown.contains(e.target as Node)) {
                this.hideDropdown(dropdownId);
            }
        };

        document.addEventListener('click', handler);
        // 存储处理器以便后续移除
        (this.activeDropdowns.get(dropdownId) as any).__outsideClickHandler = handler;
    }

    /**
     * 解绑外部点击
     */
    private unbindOutsideClick(dropdownId: string): void {
        const dropdown = this.activeDropdowns.get(dropdownId);
        if (dropdown && (dropdown as any).__outsideClickHandler) {
            document.removeEventListener('click', (dropdown as any).__outsideClickHandler);
            delete (dropdown as any).__outsideClickHandler;
        }
    }

    /**
     * 生成唯一的下拉菜单ID
     */
    private generateDropdownId(): string {
        return `dropdown-${++this.dropdownCounter}-${Date.now()}`;
    }
}

// ==================== 全局函数，供模板调用 ====================

/**
 * 创建下拉菜单（简化接口）
 * @param triggerId 触发按钮ID
 * @param items 菜单项
 */
export function createDropdown(triggerId: string, items: DropdownItem[]): string {
    return DropdownComponent.create(triggerId, {
        items,
        position: 'bottom-right',
        trigger: 'click',
        closeOnClick: true
    });
}

/**
 * 显示下拉菜单
 * @param dropdownId 下拉菜单ID
 */
export function showDropdown(dropdownId: string): void {
    DropdownComponent.show(dropdownId);
}

/**
 * 隐藏下拉菜单
 * @param dropdownId 下拉菜单ID
 */
export function hideDropdown(dropdownId: string): void {
    DropdownComponent.hide(dropdownId);
}

// 导出默认实例
export default DropdownComponent;
