textarea.ipmb{width:100%;height:167px;background-color:#fdfdfd;color:#000}
select{background-color:#fff;border:1px solid #d4d4d4;color:#585858;border-radius:7px;margin-right:5px}
input[type=submit]{background-image:linear-gradient(#f4f4f4,#ececec);position: relative;display:inline-block;overflow:visible;margin:0 5px 7px;padding:.5em 1em;outline:0;border:1px solid #d4d4d4;color:#333;text-decoration:none;white-space:nowrap;font:11px/normal sans-serif;cursor:pointer;border-radius:5px}
input[type=submit]:hover{border-color:#3072b3;background-color:#3072b3;background-image:linear-gradient(#599bdc,#3072b3);color:#fff;text-decoration:none;text-shadow:-1px -1px 0 rgba(0,0,0,.3);border-bottom-color:#2a65a0}
input.ipma{width:100%;background-color:#fdfdfd;color:#000}
input,textarea{outline-color:invert;padding:4px;border-width:2px;outline-style:none;outline-width:0;border:none;text-shadow:none;-webkit-appearance:none;-webkit-user-select:text;outline-color:transparent;box-shadow:none;background-color:transparent;border:1px solid #d4d4d4;border-radius:8px}
hr{border:0;border-bottom:1px dashed #dcdcdc;margin-bottom:15px;margin-top:15px}
div.justify{letter-spacing:.7px;overflow:hidden;height:36px;padding:5px 0 0 5px}
div.justify>span{display:inline-block;padding-left:100%}
body{font-family:SimSun;margin:0 auto;max-width:720px;box-shadow:0 2px 1px -1px rgba(0,0,0,.2),0 1px 1px 0 rgba(0,0,0,.14),0 1px 3px 0 rgba(0,0,0,.12);line-height:32px;background-color:#fff}
a{color:#3d68a8;text-decoration:none;}
a:hover{color:#4f80c7;text-decoration:underline}
a.relink:hover{color:#000}
:root{background-color:#e8e8e8}
.welcome{padding:7px 7px;border-top:1px solid #378d8d;border-bottom:1px solid #378d8d;background:#4ba0a0;color:#fdfdfd;text-align:left;text-shadow:0 0 0 #e5f3ee}
.welcome a{color:#fff;padding-right:6px}
.toupiao{border:1px solid #ebebeb;padding:5px;line-height:50px;margin: 0px 15px 15px 15px;}
.toupiao a{line-height:58px;margin:2px}
.top{padding:5px;border-bottom:1px solid #1abc9c;background:#e5f3ee;color:#000;line-height:20px}
.top2{padding:5px;border-bottom:1px solid #1abc9c;background:#e5f3ee;color:#000;line-height:20px}
.top2 a{padding-right:8px}
.title{padding:5px;border-bottom:1px solid #1abc9c;background:#e5f3ee;color:#000;line-height:25px}
.tip,.tipmini{margin:auto;padding-left:5px;border:0;background-color:#f7f7f7;color:#000}
.tipmini {font-size:0.9em;}
.subtitle{padding:5px;border:1px solid #eee}
.subtitle2{padding:5px;border:1px solid #eee}
.subtitle2 a{padding-right:5px;padding-left:2px}
.subtitle a{padding-right:2px;padding-left:2px}
.sticky{z-index: 1;background-color:rgb(255 255 255)}
.showpage{margin:auto;padding:5px;background-color:#f0f0f0}
.separate{padding:.35em;}
.separate2{padding:.09em;}
.separate,.separate2{background:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAABAQMAAAAsMPawAAAAA1BMVEV0fnr17jEMAAAACklEQVQI12NgAAAAAgAB4iG8MwAAAABJRU5ErkJggg==) no-repeat center}
.sch{padding:0 0 2px 5px}
.right{float:right;color:#999;font-size:10px}
.retime{color:#404040;font-size:15px;}
.retext{}
.renick{padding-right:1px;padding-left:1px}
.remoney{color:#505050}
.reline{line-height:35px;padding:8px 0 15px 0}
.recolon{color:#464646;padding-right:3px}
.number{font:18px/1.8 Sans-serif;word-break:normal;line-break: auto;}
.noafter:after{display:none}
.nexttitle{padding:5px;background-color:#e5f3ee}
.mylink{margin:auto;padding:5px;border:0;background-color:#f2f2f2;color:#000}
.more{margin:auto;margin-bottom:8px;border-radius:8px;overflow:hidden;width:95%;border:1px solid #eaeaea;cursor: pointer;}
.more a{width:100%;color:#555;text-align:center;position: relative;float:left;line-height:35px}
.more a:hover{color:#1abc9c;text-decoration: none;}
.more a:after{position:absolute;right:0;height:100%;border-right:1px solid #7272721f;content:''}
.margin-top{margin-top:10px}
.logo{margin-left:6px;margin-top:4px;line-height:28px}
.logo img{width:180px;height:61px}
.logo a{display:inline-block}
.logo a:hover { background: url(https://p0.meituan.net/csc/532f101cb25498fd6af30f46486d844d325287.png) no-repeat; background-size: cover; width: 180px; height: 61px; }
.logo a:hover img{opacity:0;visibility:hidden;-webkit-transform:scale(1.2);-moz-transform:scale(1.2);transform:scale(1.2);-webkit-transition:all .3s;-o-transition:all .3s;-moz-transition:all .3s;transition:all .3s}
.list{letter-spacing:.7px;padding:7px;line-height:33px;margin-bottom:-3px;}
.line3{padding:8px;background-color:#fbfbfb}
.line2{padding:8px;background-color:#fbfbfb}
.line1{padding:8px}
.line img{width:auto}
.h4{text-align:center;font:20px/2.2 'Microsoft Yahei',Helvetica,Arial,Sans-serif;font-weight:700}
.google{line-height:0;text-align:center}
.footer{padding:5px;border-top:#1abc9c 1px solid;background:#f2f2f2;color:#666;text-align:center}
.dashed{border-bottom:1px dashed #dcdcdc;margin-bottom:15px;margin-top:10px}
.content{letter-spacing:.3px;padding:7px;line-height:35.9px}
.content1{padding:7px;line-height:35.9px}
.content1 a{margin-right:5px}
.btBox{padding:4px 0}
.bt5 a{width:20%;color:#555;text-align:center}
.bt4 a{width:25%;color:#555;text-align:center}
.bt3 a{width:33%;color:#555;text-align:center}
.bt2 a{width:50%;color:#555;text-align:center}
.bt1,.bt2,.bt3,.bt4,.bt5{overflow:hidden;margin:0 auto;width:97%;border:1px solid #1abc9c;border-radius:8px}
.bt1 a{width:100%;color:#555;text-align:center}
.bt1 a:last-child:after,.bt2 a:last-child:after,.bt3 a:last-child:after,.bt4 a:last-child:after,.bt5 a:last-child:after{display:none}
.bt1 a:last-child:after,.bt2 a:last-child:after,.bt3 a:last-child:after,.bt4 a:last-child:after,.bt5 a:last-child:after{display:none}
.bt1 a:hover,.bt2 a:hover,.bt3 a:hover,.bt4 a:hover,.bt5 a:hover{color:#1abc9c;text-decoration: none;}
.bt1 a:after,.bt2 a:after,.bt3 a:after,.bt4 a:after,.bt5 a:after{position:absolute;top:0;right:0;width:0;height:37px;border-right:1px solid #1abc9c;content:''}
.bt1 a,.bt2 a,.bt3 a,.bt4 a,.bt5 a{position: relative;float:left;line-height:30px}
.brackets{padding:3px}
.bbscontent,.bubble,.line1,.line2,.list-reply,.reline,.content{line-break:anywhere;overflow-wrap:break-word;}
.repic,.bbscontent img{border-radius:3px;}
.recolon,.redate{display:inline-block;}
a.topic-link:visited,div.list a:visited{color:#A3A3A3;}
a, .top, .top2, .title, .subtitle, .subtitle2, .retext, .line3, .content {font-size:17px}
/*.border-solid img{border:1px solid #d3d3d3;}
.border-solid2 img{border:2px solid #d3d3d3;max-width:99%;} */
.listdata img{padding-right:0.1em;}
.newMessage{line-height:30px;}
@font-face { font-family: "LxgwWenKai"; font-display: swap; src: url(./css/font/LXGWWenKai-520.ttf); }
.LXGW{text-align:center;font:21px/2.2 'LxgwWenKai';font-weight:700;}
.LxgwWenKai{font:20px/2.2 'LxgwWenKai';font-weight:500}
.LXGW2{text-align:right;font:18px/2.2 'LxgwWenKai';}
.saveDraft{margin: 3px 0px 3px 3px;border-radius: 3px;background: none;border: 1px solid #cecece; padding: 2px; color: #333333; font-size: 11px;opacity: 0.7;}
.book_view_add_height{line-height: 30px;}
.spacer {padding: 0 1px;}
.forum-post.bgColor{background-color:#fbfbfb;}
.biaotiwenzi, .dengji, .touxian,  .online img {cursor: pointer;}
.louzhunicheng img {margin-right: -3px;max-height: 20px;}
.showpage input.urlbtn{margin-left: -3px!important;}
.bbscontent{line-height: 33px;}

/* 附件样式 */
.attachment {
    padding: 10px;
    border-radius: 5px;
    border: 1px dashed #dcdcdc;
    background-color: #fbfbfb;
    line-height:2;
	font-family: -apple-system,Microsoft YaHei,PingFang SC;
}
.attachmentimage img{width:auto}
.attachmentinfo {
    border-bottom: 1px dashed #eaeaea;
}
.attachment:last-child .attachmentinfo {
    border-bottom: none; 
}
.downloadname {
    font-size: 1.1rem; 
    font-weight: bold;
    margin-bottom: 6px;
}
.attachmentsize{display: inline-block;}
.attachmentsize, .downloadcount, .attachmentCharge {
    font-size: 0.85rem; 
    color: #999; 
}
.attachmentsize, .downloadcount {
    margin-right: 3px;
	margin-left: 2px;
    font-family: Source Han Sans SC,Noto Sans CJK SC,WenQuanYi Micro Hei,sans-serif;
    font-weight: 500;
}
.downloadurl{padding: 0 1px 0 1px;font-size: 0.9rem; }
.
.downloadurl, .attachmentNote {
    margin-left: 0;
    display: inline-block; 
}
.downloadurl a {
    color: #007bff;
    padding: 4px 6px;
    border-radius: 4px; 
    background-color: #eef4ff; 
}
.downloadurl a:hover {
    background-color: #dce4ff;
text-decoration: none;
}
.downloadlink {
  white-space: nowrap;
}
.attachmentNote {
    line-height: 1.6;
    /* font-size: .95rem; */
}
.attachmenSum, .attachmentnumber{display: none;}
.attachmentCharge{    margin-left: 1px;}
.attachmentlistnum{margin: 0 1px 0 1px;}
.attachmentNote {
  white-space: pre-wrap;
  display: block;
}
.attachmentinfo {
  margin-bottom: 10px;
    line-height: 1.5;
}
.attachmentinfo:last-child {
  margin-bottom: 0; 
}
@media (max-width: 768px) {
    .attachment {
        padding: 9px;
    }
    .downloadname {
        font-size: 1rem;
    }
.attachmenSum{ font-size: .95rem;}
    .attachmentsize, .downloadcount, .attachmentCharge {
        font-size: 0.8rem; 
    }
}
a.urlbtn{font-size: 16px;}
/* 附件样式结束 */

@font-face {
    font-family: Arvo;
    font-style: normal;
    font-weight: regular;
    src: url(//lib.baomitu.com/fonts/arvo/arvo-regular.eot);
    src: local('Arvo'),local('Arvo-Normal'),url(//lib.baomitu.com/fonts/arvo/arvo-regular.eot?#iefix) format('embedded-opentype'),url(//lib.baomitu.com/fonts/arvo/arvo-regular.woff2) format('woff2'),url(//lib.baomitu.com/fonts/arvo/arvo-regular.woff) format('woff'),url(//lib.baomitu.com/fonts/arvo/arvo-regular.ttf) format('truetype'),url(//lib.baomitu.com/fonts/arvo/arvo-regular.svg#Arvo) format('svg')
}
/* CODE标签复制代码样式 */
.CodeContainer {
    position: relative;
    background-color: rgb(250 250 250);
    padding: .7rem .7rem .58rem .7rem;
    border-radius: .3rem;
    box-shadow: rgb(0 0 0 / 10%) 0px 2px 4px;
    margin: 2px;
}
.CodeSnippet {
    overflow-x: auto;
    font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
    white-space: pre;
    word-wrap: break-word;
    line-height: 20px;
    display: block;
}
.CodeSnippet, .CodeSnippet a{font-size: .8rem;padding: 0.12rem !important;}
.CopyButton {
    position: absolute;
    top: .3rem;
    right: .1rem;
    background: transparent;
    border: none;
    cursor: pointer;
    opacity: 0.3;
    transition: opacity 0.3s ease-in-out;
}
.CodeContainer:hover .CopyButton {
    opacity: 1;
}
.CopyIcon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-top: 2px;
    border: 1px solid #dadde1;
    border-radius: 5px;
    padding: 1px;
    background: #f6f8fa;
}
.CopyIcon img {
    width: 18px;
    height: 18px;
    transition: transform 0.2s ease-in-out, opacity 0.2s ease-in-out;
}
.CopyIcon img.checkmark {
    animation: scaleUp 0.2s ease-in-out;
}
@keyframes scaleUp {
    0% {
        transform: scale(0.6);
    }
    100% {
        transform: scale(1);
    }
}
/* 复制代码结束 */