using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using System;
using System.Collections.Generic;
using System.Web;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Services.Config;

namespace YaoHuo.Plugin.OAuth
{
    /// <summary>
    /// OAuth 页面基类 - 提供通用功能和消除重复代码
    /// 所有OAuth相关页面应该继承此基类
    /// </summary>
    public abstract class OAuthBasePage : MyPageWap
    {
        private OAuthService _oauthService;

        /// <summary>
        /// OAuth 服务实例（延迟初始化）
        /// </summary>
        protected OAuthService OAuthService
        {
            get
            {
                if (_oauthService == null)
                {
                    var instanceName = PubConstant.GetAppString("InstanceName");
                    var connectionString = PubConstant.GetConnectionString(instanceName);
                    var repository = new OAuthRepository(connectionString);
                    _oauthService = new OAuthService(repository);
                }
                return _oauthService;
            }
            set => _oauthService = value;
        }

        /// <summary>
        /// 验证用户登录状态
        /// </summary>
        /// <param name="redirectUrl">未登录时的重定向地址，默认为首页</param>
        /// <returns>用户信息，未登录时为null（已重定向）</returns>
        protected user_Model EnsureUserAuthenticated(string redirectUrl = "/")
        {
            var userInfo = this.userVo;
            if (userInfo.userid == 0)
            {
                Response.Redirect(redirectUrl, false);
                Context.ApplicationInstance.CompleteRequest();
                return null;
            }
            return userInfo;
        }

        /// <summary>
        /// 验证用户登录状态（带回调URL）
        /// </summary>
        /// <param name="loginPageUrl">登录页面URL</param>
        /// <returns>用户信息，未登录时为null（已重定向到登录页）</returns>
        protected user_Model EnsureUserAuthenticatedWithCallback(string loginPageUrl = "/waplogin.aspx")
        {
            var userInfo = this.userVo;
            if (userInfo.userid == 0)
            {
                var backUrl = HttpUtility.UrlEncode(Request.Url.ToString());
                var loginUrl = $"{loginPageUrl}?backurl={backUrl}";
                Response.Redirect(loginUrl, false);
                Context.ApplicationInstance.CompleteRequest();
                return null;
            }
            return userInfo;
        }

        /// <summary>
        /// 设置API响应的CORS头
        /// </summary>
        protected void SetCorsHeaders()
        {
            try
            {
                var corsSettings = OAuthConfigService.GetCorsSettings();
                
                // 处理请求来源
                string origin = Request.Headers["Origin"];
                string allowOrigin = null;
                
                // 如果配置了特定的允许来源，检查请求来源是否在列表中
                if (corsSettings.allowedOrigins != null && corsSettings.allowedOrigins.Count > 0)
                {
                    if (corsSettings.allowedOrigins.Contains("*"))
                    {
                        allowOrigin = "*";
                    }
                    else if (!string.IsNullOrEmpty(origin) && corsSettings.allowedOrigins.Contains(origin))
                    {
                        allowOrigin = origin;
                    }
                }
                
                // 只有在确定允许的情况下才设置CORS头
                if (!string.IsNullOrEmpty(allowOrigin))
                {
                    // 先清除可能存在的CORS头，避免重复
                    Response.Headers.Remove("Access-Control-Allow-Origin");
                    Response.Headers.Remove("Access-Control-Allow-Methods");
                    Response.Headers.Remove("Access-Control-Allow-Headers");
                    Response.Headers.Remove("Access-Control-Allow-Credentials");

                    Response.AddHeader("Access-Control-Allow-Origin", allowOrigin);
                    Response.AddHeader("Access-Control-Allow-Methods", string.Join(", ", corsSettings.allowedMethods ?? new List<string> { "GET", "POST", "OPTIONS" }));
                    Response.AddHeader("Access-Control-Allow-Headers", string.Join(", ", corsSettings.allowedHeaders ?? new List<string> { "Content-Type", "Authorization" }));
                    Response.AddHeader("Access-Control-Allow-Credentials", "true");
                }
                
                // 处理预检请求
                if (Request.HttpMethod.ToUpper() == "OPTIONS")
                {
                    Response.StatusCode = 200;
                    Response.Flush();
                    Context.ApplicationInstance.CompleteRequest();
                    return;
                }
            }
            catch (Exception)
            {
                // 如果配置加载失败，使用默认的CORS设置
                // 先清除可能存在的CORS头
                Response.Headers.Remove("Access-Control-Allow-Origin");
                Response.Headers.Remove("Access-Control-Allow-Methods");
                Response.Headers.Remove("Access-Control-Allow-Headers");

                Response.AddHeader("Access-Control-Allow-Origin", "*");
                Response.AddHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
                Response.AddHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
            }
        }

        /// <summary>
        /// 设置JSON响应格式
        /// </summary>
        protected void SetJsonResponse()
        {
            Response.Clear();
            Response.ContentType = OAuthConstants.CONTENT_TYPE_JSON;
        }

        /// <summary>
        /// 记录OAuth安全事件
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="userId">用户ID（可选）</param>
        protected void LogSecurityEvent(string message, string userId = null)
        {
            try
            {
                long.TryParse(userId, out long userIdLong);
                OAuthLogger.LogAdminAction("security_event", "SYSTEM", userIdLong, message, Request.UserHostAddress, Request.UserAgent);
            }
            catch (Exception ex)
            {
                // 记录日志失败时的备用处理
                System.Diagnostics.Debug.WriteLine($"OAuth安全事件日志记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理OAuth2异常并记录日志
        /// </summary>
        /// <param name="ex">OAuth2异常</param>
        /// <param name="userId">用户ID（可选）</param>
        protected void HandleOAuth2Exception(OAuth2Exception ex, string userId = null)
        {
            SafeLogWithContext(
                userId,
                (clientId, _, ip, userAgent) => OAuthLogger.LogError(ex.Error, clientId, ex.ErrorDescription, ip, userAgent),
                () => $"OAuth2异常: {ex.Error} - {ex.ErrorDescription}",
                "OAuth2异常"
            );
        }

        /// <summary>
        /// 处理一般异常并记录日志
        /// </summary>
        /// <param name="ex">异常</param>
        /// <param name="context">上下文信息</param>
        /// <param name="userId">用户ID（可选）</param>
        protected void HandleGeneralException(Exception ex, string context, string userId = null)
        {
            SafeLogWithContext(
                userId,
                (clientId, userIdLong, ip, userAgent) => OAuthLogger.LogException(ex, context, clientId, userIdLong, ip, userAgent),
                () => $"{context}异常: {ex.Message}",
                "一般异常"
            );
        }

        /// <summary>
        /// 安全地执行日志记录操作，封装通用上下文获取和异常处理
        /// </summary>
        /// <param name="userId">用户ID字符串</param>
        /// <param name="logAction">执行日志记录的核心委托</param>
        /// <param name="securityMessageFunc">生成安全事件消息的委托</param>
        /// <param name="errorContext">用于在日志记录失败时提供上下文</param>
        private void SafeLogWithContext(string userId, Action<string, long, string, string> logAction, Func<string> securityMessageFunc, string errorContext)
        {
            try
            {
                var clientId = GetRequestValue("client_id") ?? "unknown";
                long.TryParse(userId, out long userIdLong);
 
                logAction(clientId, userIdLong, Request.UserHostAddress, Request.UserAgent);
 
                LogSecurityEvent(securityMessageFunc(), userId);
            }
            catch (Exception logEx)
            {
                System.Diagnostics.Debug.WriteLine($"{errorContext}日志记录失败: {logEx.Message}");
            }
        }

        /// <summary>
        /// 验证管理员权限
        /// </summary>
        /// <param name="userInfo">用户信息</param>
        /// <param name="requiredLevels">需要的管理员级别，如"|00|"</param>
        /// <param name="redirectUrl">权限不足时的重定向地址</param>
        /// <returns>是否有权限</returns>
        protected bool EnsureManagerPermission(user_Model userInfo, string requiredLevels, string redirectUrl = "/OAuth/Index.aspx")
        {
            if (!this.IsCheckManagerLvl(requiredLevels, userInfo.managerlvl))
            {
                LogSecurityEvent($"未授权访问: UserID={userInfo.userid}, ManagerLvl='{userInfo.managerlvl}', RequiredLvl='{requiredLevels}', IP={Request.UserHostAddress}", userInfo.userid.ToString());
                Response.Redirect(redirectUrl, false);
                Context.ApplicationInstance.CompleteRequest();
                return false;
            }
            return true;
        }
    }
}