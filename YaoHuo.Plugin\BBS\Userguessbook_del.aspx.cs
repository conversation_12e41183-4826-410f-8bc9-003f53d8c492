﻿using System;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{

	public class userGuessBook_del : MyPageWap
    {
		private string string_10 = PubConstant.GetAppString("InstanceName");

		public string action = "";

		public string touserid = "";

		public string reid = "";

		public string page = "";

		public string lpage = "";

		public string ot = "";

		public string INFO = "";

		public string ERROR = "";

		public wap2_userGuessBook_Model bbsReVo = null;

		protected void Page_Load(object sender, EventArgs e)
		{
			action = GetRequestValue("action");
			touserid = GetRequestValue("touserid");
			reid = GetRequestValue("reid");
			page = GetRequestValue("page");
			lpage = GetRequestValue("lpage");
			ot = GetRequestValue("ot");
			wap2_userGuessBook_BLL wap2_userGuessBook_BLL = new wap2_userGuessBook_BLL(string_10);
			bbsReVo = wap2_userGuessBook_BLL.GetModel(long.Parse(reid));
			if (userid != bbsReVo.userid.ToString())
			{
				IsCheckUserManager(userid, userVo.managerlvl, classVo.adminusername, "bbs/userguessbook.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + page + "&amp;ot=" + ot);
			}
			if (action == "godel")
			{
				try
				{
					// ✅ 修复SQL注入漏洞：使用DapperHelper替换MainBll.UpdateSQL
					string connectionString = PubConstant.GetConnectionString(string_10);
					string deleteSql = "DELETE FROM wap2_userguessbook WHERE siteid = @SiteId AND id = @Id";
					DapperHelper.Execute(connectionString, deleteSql, new {
						SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
						Id = DapperHelper.SafeParseLong(reid, "留言ID")
					});
					INFO = "OK";
				}
				catch (Exception ex)
				{
					ERROR = ex.ToString();
				}
			}
		}
	}
}