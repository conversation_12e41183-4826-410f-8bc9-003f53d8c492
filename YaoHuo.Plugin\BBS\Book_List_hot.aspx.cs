﻿using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using System;
using System.Collections.Generic;
using YaoHuo.Plugin.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Book_List_hot : BaseBBSListPage
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                // 获取天数参数并限制范围
                string daysStr = GetRequestValue("days");
                int days = 1;
                if (WapTool.IsNumeric(daysStr))
                {
                    int inputDays = Convert.ToInt32(daysStr);
                    if (inputDays >= 180) days = 180;
                    else if (inputDays >= 30) days = 30;
                    else if (inputDays >= 7) days = 7;
                    else if (inputDays >= 3) days = 3;
                    else if (inputDays >= 1) days = 1;
                }

                // 构建基础查询条件
                condition = BuildBaseCondition();
                condition += $" and book_date >= DATEADD(day, -{days}, GETDATE())";

                // 设置页面标题
                if (classid == "0")
                {
                    classVo.classname = $"全站热门・{days}天内";
                    SetupEmptyClass();
                }
                else
                {
                    classVo.classname += $"・{days}天热门";
                }

                // 先获取总数
                var bll = new wap_bbs_BLL(a);
                total = bll.GetListCount(condition);

                // 处理分页和链接
                pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
                var baseUrl = "bbs/book_list_hot.aspx";
                var extraParams = $"classid={classid}&amp;days={days}";
                HandlePageAndLinks(baseUrl, extraParams);

                // 使用缓存获取数据
                string cacheKey = $"HOT_POSTS_{siteid}_{classid}_{days}_{CurrentPage}";
                if (_hotPostsCache.Get(cacheKey) is List<wap_bbs_Model> cachedData)
                {
                    listVo = cachedData;
                }
                else
                {
                    listVo = GetHotPosts(bll, days);
                    if (listVo != null)
                    {
                        _hotPostsCache.Set(cacheKey, listVo, GetCachePolicy(GetCacheMinutes(days)));
                    }
                }

                // 获取数据后加载用户信息
                if (listVo != null)
                {
                    LoadUserInfo();
                    // 注意：派币图标现在直接判断 freeLeftMoney > 0，无需预加载缓存
                }

                // 加载广告
                LoadAdvertisement();
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }

        private void SetupEmptyClass()
        {
            classVo.classid = 0L;
            classVo.position = "left";
            classVo.siteimg = "NetImages/no.gif";
            classVo.introduce = "";
        }

        private int GetCacheMinutes(int days)
        {
            if (days <= 1) return 5;
            if (days <= 7) return 30;
            return 60;
        }

        private List<wap_bbs_Model> GetHotPosts(wap_bbs_BLL bll, int days)
        {
            return bll.GetListVo(
                pageSize,
                CurrentPage,
                BuildHotPostsQuery(),
                "book_classid,classname,id,book_title,book_date,book_click,book_re,book_author,book_pub,book_top,book_good,topic,islock,ischeck,sendMoney,isvote,isdown,hangbiaoshi,freeMoney,freeleftMoney,book_img,MarkSixBetID,MarkSixWin",
                "",
                total,
                1
            );
        }

        private string BuildHotPostsQuery()
        {
            return $@"id IN (
                SELECT id FROM (
                    SELECT TOP {pageSize * CurrentPage} id 
                    FROM vw_bbs_hot_score 
                    WHERE {condition}
                    ORDER BY hot_score DESC
                ) AS t 
                WHERE t.id NOT IN (
                    SELECT TOP {pageSize * (CurrentPage - 1)} id 
                    FROM vw_bbs_hot_score 
                    WHERE {condition}
                    ORDER BY hot_score DESC
                )
            )";
        }
    }
}