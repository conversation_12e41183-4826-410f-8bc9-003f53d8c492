﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ClickBatchStatus.aspx.cs" Inherits="YaoHuo.Plugin.BBS.ClickBatchStatus" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    Response.Write(WapTool.showTop("点击数批量处理状态", wmlVo));
    
    strhtml.Append("<div class=\"content\">");
    strhtml.Append("<div class=\"tip\">");
    strhtml.Append("<b>点击数批量处理服务状态监控</b><br/>");
    strhtml.Append("此页面用于监控点击数批量处理服务的运行状态");
    strhtml.Append("</div>");
    
    strhtml.Append("<div class=\"subtitle\">服务状态</div>");
    strhtml.Append("<div class=\"content\">");
    strhtml.Append(queueStatus);
    strhtml.Append("</div>");
    
    strhtml.Append("<div class=\"subtitle\">服务说明</div>");
    strhtml.Append("<div class=\"content\">");
    strhtml.Append("• 批量处理间隔：30秒<br/>");
    strhtml.Append("• 最大队列长度：10,000个请求<br/>");
    strhtml.Append("• 处理方式：按帖子分组合并点击数<br/>");
    strhtml.Append("• 优势：减少数据库压力，降低死锁风险<br/>");
    strhtml.Append("</div>");
    
    strhtml.Append("<div class=\"subtitle\">操作</div>");
    strhtml.Append("<div class=\"content\">");
    strhtml.Append("<a href=\"ClickBatchStatus.aspx?siteid=" + siteid + "&amp;classid=" + classid + "\">刷新状态</a><br/>");
    strhtml.Append("<a href=\"book_list.aspx?siteid=" + siteid + "&amp;classid=" + classid + "\">返回帖子列表</a>");
    strhtml.Append("</div>");
    
    strhtml.Append("</div>");
    
    strhtml.Append(WapTool.GetVS(wmlVo));
    strhtml.Append(ERROR);
    Response.Write(WapTool.ToWML(strhtml.ToString(), wmlVo));
    Response.Write(WapTool.showDown(wmlVo));
%>
