﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_View_add.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Book_View_add" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    wmlVo.mycss += "\r\n<link href=\"/netcss/css/upload-resource.css?X13\" rel=\"stylesheet\" type=\"text/css\"/>";
    if (this.INFO == "OK")
    {
        wmlVo.timer = "2";
        wmlVo.strUrl = "bbs-" + this.getid + ".html";
    }
    Response.Write(WapTool.showTop(this.GetLang("发表新主题|發表主題|add subject"), wmlVo));
    strhtml.Append("<div class=\"upload-container\">");
    strhtml.Append("<div class=\"breadcrumb\">");
    strhtml.Append("<a href=\"/\" class=\"breadcrumb-item\">");
    strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-home h-4 w-4\" data-id=\"7\"><path d=\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"></path><polyline points=\"9 22 9 12 15 12 15 22\"></polyline></svg>");
    strhtml.Append("<span>首页</span>");
    strhtml.Append("</a>");
    strhtml.Append("<span class=\"breadcrumb-separator\"></span>");
    strhtml.Append("<a href=\"/bbslist-" + this.classid + ".html\" class=\"breadcrumb-item\">" + classVo.classname + "</a>");
    strhtml.Append("<span class=\"breadcrumb-separator\"></span>");
    strhtml.Append("<span class=\"breadcrumb-item active\">发表新主题</span>");
    strhtml.Append("</div>");
    strhtml.Append("<div class=\"notification-container\" style=\"display:none\"><div class=\"custom-notification\"> <div class=\"custom-notification-container\"> <p class=\"custom-notification-content\">草稿保存成功!</p> </div> </div></div>");
    strhtml.Append(this.ERROR);
    if (this.INFO == "NULL")
    {
        strhtml.Append("<div class=\"tip\"><b>标题最少" + this.titlemax + "字，内容最少" + this.contentmax + "字！</b></div>");
    }
    else if (this.INFO == "TITLEMAX")
    {
        if (title_max != "0")
        {
            strhtml.Append("<div class=\"tip\"><b>标题最大" + this.title_max + "字。</b></div>");
        }
        if (content_max != "0")
        {
            strhtml.Append("<div class=\"tip\"><b>内容最大" + this.content_max + "字。</b></div>");
        }
    }
    else if (this.INFO == "ERR_FORMAT")
    {
        strhtml.Append("<div class=\"tip\"><b>取到非法值:\"$$\"请更换手机浏览器或重新编辑！</b></div>");
    }
    else if (this.INFO == "REPEAT")
    {
        strhtml.Append("<div class=\"tip\"><b>请不要发表重复内容</b></div>");
    }
    else if (this.INFO == "PWERROR")
    {
        strhtml.Append("<div class=\"tip\"><b>密码错误，请重新录入我的密码！</b></div>");
    }
    else if (this.INFO == "ERROR_Secret")
    {
        strhtml.Append("<div class=\"tip\"><b>暗号错误，如果忘记联系站长索取！</b></div>");
    }
    else if (this.INFO == "MAX")
    {
        strhtml.Append("<div class=\"tip\"><b>今天你已超过发帖限制，请明天再来！</b></div>");
    }
    else if (this.INFO == "SENDMONEY")
    {
        strhtml.Append("<div class=\"tip\"><b>你当前的只有:" + userVo.money + "个，所以你悬赏值只能小于或等于" + userVo.money + "个</b></div>");
    }
    else if (this.INFO == "NOMONEY")
    {
        strhtml.Append("<div class=\"tip\"><b>你当前的只有:" + userVo.money + "个，发帖需要扣掉：" + getmoney2 + "个</b></div>");
    }
    else if (this.INFO == "LOCK")
    {
        strhtml.Append("<div class=\"tip\"><b>抱歉，您已经被加入黑名单，请注意发帖规则！</b></div>");
    }
    if (this.INFO == "OK")
    {
        strhtml.Append("<div class=\"upload-success\">");
        strhtml.Append("<div class=\"upload-success-header\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><line x1=\"22\" y1=\"2\" x2=\"11\" y2=\"13\"></line><polygon points=\"22 2 15 22 11 13 2 9 22 2\"></polygon></svg>");
        strhtml.Append("<div class=\"upload-success-title\">发表主题成功！</div>");
        strhtml.Append("</div>");
        if (siteVo.isCheck == 1)
        {
            strhtml.Append("<div class=\"upload-success-subtitle\">审核后显示！</div>");
        }
        strhtml.Append("<div class=\"upload-success-subtitle\">获得" + WapTool.GetSiteMoneyName(siteVo.sitemoneyname, this.lang) + ":" + getmoney + "、经验:" + getexpr + "</div>");
        strhtml.Append("</div>");
        strhtml.Append("<script type=\"text/javascript\" src=\"/netcss/js/ClearDraft.js?X1\"></script>");

        // 成功后显示新的两个按钮
        strhtml.Append("<div class=\"nav-buttons\">");
        strhtml.Append("<a class=\"nav-btn\" href=\"/bbs-" + this.getid + ".html\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-circle-arrow-right\"><circle cx=\"12\" cy=\"12\" r=\"10\"/><path d=\"M8 12h8\"/><path d=\"m12 16 4-4-4-4\"/></svg>");
        strhtml.Append("进入主题</a>");
        strhtml.Append("<a class=\"nav-btn\" href=\"/\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-house\"><path d=\"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\"/><path d=\"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"/></svg>");
        strhtml.Append("返回首页</a>");
        strhtml.Append("</div>");
    }
    else
    {
        strhtml.Append("<div class=\"content\">");
        strhtml.Append("<form name=\"f\" action=\"" + http_start + "bbs/book_view_add.aspx\" method=\"post\">");
        strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
        strhtml.Append("<div class=\"form-group\">");
        strhtml.Append("<label>" + this.GetLang("标题|標題|Title") + "</label>");
        strhtml.Append("<input type=\"text\" name=\"book_title\" minlength=\"5\" maxlength=\"25\" required=\"required\" class=\"form-control\" value=\"" + book_title + "\"/>");
        strhtml.Append("</div>");
        if (this.action == "friends")
        {
            strhtml.Append(this.GetLang("内容|內容|Content") + " <a href=\"" + http_start + "bbs/ModifyUserFriends.aspx?siteid=" + siteid + "" + "\">完善交友资料</a><br/>");
        }
        else
        {
            strhtml.Append("<div class=\"form-group\" style=\"margin-bottom:0;\">");
            strhtml.Append("<div class=\"content-header\">");
            strhtml.Append("<label>" + this.GetLang("内容|內容|Content") + "</label>");
            strhtml.Append("<div class=\"textarea-actions\">");
            strhtml.Append("<button type=\"button\" class=\"action-btn-small\" id=\"saveDraftButton\">");
            strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-save w-4 h-4\" data-id=\"3\"><path d=\"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z\"></path><path d=\"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7\"></path><path d=\"M7 3v4a1 1 0 0 0 1 1h7\"></path></svg>");
            strhtml.Append("<span>保存草稿</span></button>");
            strhtml.Append("<button type=\"button\" class=\"action-btn-small\" style=\"display:none\" id=\"clearDraftButton\">");
            strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-trash2 w-4 h-4\" data-id=\"5\"><path d=\"M3 6h18\"></path><path d=\"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\"></path><path d=\"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\"></path><line x1=\"10\" x2=\"10\" y1=\"11\" y2=\"17\"></line><line x1=\"14\" x2=\"14\" y1=\"11\" y2=\"17\"></line></svg>");
            strhtml.Append("<span style=\"margin-left:2px;\">清除草稿</span></button>");
            strhtml.Append("</div>");
            strhtml.Append("</div>");
            strhtml.Append("<textarea name=\"book_content\" oninput=\"adjustTextareaHeight(this)\" minlength=\"15\" required=\"required\" placeholder=\"帖子发到对应版块，以免被删除。\" class=\"form-control\" rows=\"8\">" + book_content + "</textarea>");
            strhtml.Append("</div>");
        }
        strhtml.Append("<div class=\"reward-section\">");
        strhtml.Append("<button type=\"button\" class=\"collapse-trigger\" onclick=\"toggleReward()\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-gift h-4 w-4 mr-2\" data-id=\"27\"><rect x=\"3\" y=\"8\" width=\"18\" height=\"4\" rx=\"1\"></rect><path d=\"M12 8v13\"></path><path d=\"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7\"></path><path d=\"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5\"></path></svg>");
        strhtml.Append("<span>悬赏妖晶</span>");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"arrow-icon\"><polyline points=\"6 9 12 15 18 9\"></polyline></svg>");
        strhtml.Append("</button>");
        strhtml.Append("<div class=\"reward-content\" style=\"display:none\">");
        strhtml.Append("<div class=\"form-group half\" style=\"display:flex; align-items:center;gap:5px;flex-wrap:wrap;max-width:100%;background: white;\">");
        strhtml.Append("<input type=\"number\" name=\"sendmoney\" min=\"1000\" maxlength=\"8\" oninput=\"if(this.value.length > 8) this.value=this.value.slice(0,8)\" value=\"" + (sendmoney != "0" ? sendmoney : "") + "\" placeholder=\"选填，最少1000\" class=\"form-control\" style=\"padding: 0.5rem; height: 32px;width: 240px;max-width: 45%;\"/>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        if (this.needpwFlag == "1")
        {
            strhtml.Append("<div class=\"form-group\">");
            strhtml.Append("<label>我的密码</label>");
            strhtml.Append("<input type=\"text\" name=\"needpw\" value=\"" + needpw + "\" size=\"10\" /><br/>");
            strhtml.Append("</div>");
        }
        if (this.isNeedSecret == true)
        {
            strhtml.Append("<div class=\"form-group\">");
            strhtml.Append("<label>本版暗号</label>");
            strhtml.Append("<input type=\"text\" name=\"secret\" value=\"\" size=\"10\" /><br/>");
            strhtml.Append("</div>");
        }
        strhtml.Append("<button type=\"submit\" name=\"g\" id=\"submitBtn\" class=\"submit-btn\" style=\"margin-right:auto; margin-top: 0; margin-left: 0;\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><line x1=\"22\" y1=\"2\" x2=\"11\" y2=\"13\"></line><polygon points=\"22 2 15 22 11 13 2 9 22 2\"></polygon></svg>");
        strhtml.Append("<span>发表新帖</span></button>");
        strhtml.Append("</form>");
        strhtml.Append("</div>");

        strhtml.Append("<div class=\"nav-buttons grid-2\">");
        // 发表派币帖
        strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/book_view_sendmoney.aspx?classid=" + this.classid + "\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-database w-4 h-4 mr-2\" data-id=\"40\"><ellipse cx=\"12\" cy=\"5\" rx=\"9\" ry=\"3\"></ellipse><path d=\"M3 5V19A9 3 0 0 0 21 19V5\"></path><path d=\"M3 12A9 3 0 0 0 21 12\"></path></svg>");
        strhtml.Append("发表派币帖</a>");

        // 发表投票帖
        strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/book_view_addvote.aspx?classid=" + this.classid + "\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-vote mr-2 h-4 w-4\" data-id=\"31\"><path d=\"m9 12 2 2 4-4\"></path><path d=\"M5 7c0-1.1.9-2 2-2h10a2 2 0 0 1 2 2v12H5V7Z\"></path><path d=\"M22 19H2\"></path></svg>");
        strhtml.Append("发表投票帖</a>");

        // 发表资源帖
        strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/book_view_addurl.aspx?classid=" + this.classid + "&amp;num=1\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-file-box mr-2 h-4 w-4\" data-id=\"33\"><path d=\"M14.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v4\"></path><path d=\"M14 2v4a2 2 0 0 0 2 2h4\"></path><path d=\"M3 13.1a2 2 0 0 0-1 1.76v3.24a2 2 0 0 0 .97 1.78L6 21.7a2 2 0 0 0 2.03.01L11 19.9a2 2 0 0 0 1-1.76V14.9a2 2 0 0 0-.97-1.78L8 11.3a2 2 0 0 0-2.03-.01Z\"></path><path d=\"M7 17v5\"></path><path d=\"M11.7 14.2 7 17l-4.7-2.8\"></path></svg>");
        strhtml.Append("发表资源帖</a>");

        // UBB方法
        strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/book_view_ubb.aspx?classid=" + this.classid + "&amp;backurl=" + HttpUtility.UrlEncode("bbs/book_view_add.aspx?classid=" + this.classid) + "\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M12 20h9\"></path><path d=\"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z\"></path></svg>");
        strhtml.Append("UBB方法</a>");
        strhtml.Append("</div>");
    }
    string isWebHtml = this.ShowWEB_view(this.classid);
    if (isWebHtml != "")
    {
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml.Replace("[view]", strhtml.ToString()), wmlVo));
        Response.End();
    }
    strhtml.Append("</div>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/netcss/js/SaveDraft.js\"></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/netcss/js/fileupload/post-editor.js?8\" defer></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/netcss/js/fileupload/image-uploader.js\" defer></script>");
    strhtml.Append(@"<script type=""text/javascript"">
    // 悬赏妖晶折叠展开
    function toggleReward() {
        const trigger = document.querySelector('.collapse-trigger');
        const content = document.querySelector('.reward-content');
        
        trigger.classList.toggle('active');
        
        if (content.style.display === 'none') {
            content.style.display = 'block';
        } else {
            content.style.display = 'none';
        }
    }
    </script>");
    Response.Write(strhtml);
    Response.Write(WapTool.showDown(wmlVo));
%>