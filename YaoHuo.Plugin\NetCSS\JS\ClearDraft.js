﻿// 检查localStorage是否可用
function storageAvailable(type) {
  var storage;
  try {
    storage = window[type];
    var x = '__storage_test__';
    storage.setItem(x, x);
    storage.removeItem(x);
    return true;
  } catch (e) {
    return false;
  }
}

// 页面加载完毕时执行函数
window.addEventListener('load', clearDraftOnSuccess);
function clearDraftOnSuccess() {
  // 检查localStorage是否可用
  if (storageAvailable('localStorage')) {
    // 清除本地存储中的草稿标题和内容
    localStorage.removeItem('draft_title');
    localStorage.removeItem('draft_content');
  } else {
    // 如果localStorage不可用，输出一个错误消息
    console.error('本地存储不可用，草稿未能清除');
  }
}