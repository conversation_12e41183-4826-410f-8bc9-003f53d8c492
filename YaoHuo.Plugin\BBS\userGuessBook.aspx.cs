﻿using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Web;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.BBS.Models;
using YaoHuo.Plugin.BBS.Base.Helper;
using YaoHuo.Plugin.Template.Models;

namespace YaoHuo.Plugin.BBS
{
    public class UserGuessBook : MyPageWap
    {

        private readonly string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string linkURL = "";

        public string condition = "";

        public string ERROR = "";

        public string INFO = "";

        public string id = "0";

        public string lpage = "";

        public string ot = "0";

        public string touserid = "";

        public string face = "";

        public string linkTOP = "";

        public List<wap2_userGuessBook_Model> listVo = null;

        public StringBuilder strhtml = new StringBuilder();

        public long kk = 1L;

        public long index = 0L;

        public long total = 0L;

        public long pageSize = 10L;

        public long CurrentPage = 1L;

        public string KL_CheckIPTime = PubConstant.GetAppString("KL_CheckIPTime");

        public string KL_CheckZoneReCount = PubConstant.GetAppString("KL_CheckZoneReCount");

        protected void Page_Load(object sender, EventArgs e)
        {
            // 检查并处理新版UI偏好
            CheckAndHandleUIPreference();

            action = base.Request.Form.Get("action");
            if (string.IsNullOrEmpty(action))
            {
                action = GetRequestValue("action"); // 也检查QueryString
            }

            lpage = GetRequestValue("lpage");
            ot = GetRequestValue("ot");
            touserid = GetRequestValue("touserid");
            face = GetRequestValue("face");
            if (face.Trim() == "")
            {
                face = "face";
            }

            // 优先处理Ajax删除请求，不受UI版本影响
            if (action == "delete")
            {
                HandleDeleteMessage();
                return;
            }

            // 调试日志（仅在调试模式下输出）
            if (GetRequestValue("debug") == "1")
            {
                System.Diagnostics.Debug.WriteLine($"[UserGuessBook] Page_Load - action: {action}, touserid: {touserid}, userid: {userid}");
                System.Diagnostics.Debug.WriteLine($"[UserGuessBook] Request Method: {Request.HttpMethod}");
                System.Diagnostics.Debug.WriteLine($"[UserGuessBook] Form Keys: {string.Join(", ", Request.Form.AllKeys ?? new string[0])}");
            }

            switch (action)
            {
                case "add":
                case "gomod":  // 添加对 gomod action 的处理
                    Method_2();
                    break;
                case "add_ajax":  // 新增AJAX提交处理
                    HandleAddMessageAjax();
                    break;
                case "del":
                    DeleteMessage();
                    break;
                case "class":
                    Showclass();
                    break;
                default:
                    Showclass();
                    break;
            }
        }

        public void Showclass()
        {
            // 尝试使用新版UI渲染
            if (RenderWithHandlebars())
            {
                return; // 新版渲染成功，直接返回
            }

            // 对于旧版UI，执行数据加载
            LoadGuessBookData();
        }

        public void Method_2()
        {
            IsLogin(userid, GetUrlQueryString());
            string text = GetRequestValue("content");
            string backurl = base.Request.Form.Get("backurl");
            string requestValue = GetRequestValue("face");
            IsLogin(userid, backurl);
            if (!WapTool.IsNumeric(KL_CheckZoneReCount))
            {
                KL_CheckZoneReCount = "20";
            }
            // ✅ 修复SQL注入漏洞：使用DapperHelper替换BLL调用
            string dailyCheckConnectionString = PubConstant.GetConnectionString(a);
            string dailyCountSql = "SELECT COUNT(*) FROM wap2_userGuessBook WHERE DATEDIFF(dd, addtime, GETDATE()) < 1 AND siteid = @SiteId AND fromuserid = @FromUserId";
            long num = DapperHelper.ExecuteScalar<long>(dailyCheckConnectionString, dailyCountSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                FromUserId = DapperHelper.SafeParseLong(userid, "发送用户ID")
            });
            if (text.Trim().Length < 2)
            {
                INFO = "NULL";
            }
            else if (num > long.Parse(KL_CheckZoneReCount))
            {
                INFO = "MAX";
            }
            else if (text.Equals(Session["content"]))
            {
                INFO = "REPEAT";
            }
            else if (IsUserSubmitTooFrequent(userid, 10)) // 10秒内只能提交一次
            {
                INFO = "WAITING";
            }
            else if (WapTool.IsLockuser(siteid, userid, classid) > -1L)
            {
                INFO = "LOCK";
            }
            else
            {
                try
                {
                    Session["content"] = text;
                    if (text.ToLower().IndexOf("[sid]") > 0 && !IsUserManager(userid, userVo.managerlvl, ""))
                    {
                        text = text.ToLower().Replace("[sid]", "[sid2]");
                        text = text.ToLower().Replace("[sid1]", "[sid2]");
                    }
                    if (requestValue.Trim() != "" && requestValue.Trim() != "face")
                    {
                        text = "[img]face/" + requestValue + ".gif[/img]" + text;
                    }
                    text = "<a href=\"" + http_start + "bbs/userinfo.aspx?siteid=" + siteid + "&amp;touserid=" + userid + "\">" + nickname + "</a> <span class=\"right\">" + $"{DateTime.Now:MM-dd HH:mm}" + "</span><br/>" + text;

                    // ✅ 修复编译错误：使用DapperHelper替换BLL.Add调用，避免SQL注入
                    string insertConnectionString = PubConstant.GetConnectionString(a);
                    string insertSql = @"INSERT INTO wap2_userGuessBook(siteid, userid, fromuserid, fromnickname, content, addtime, ischeck)
                                        VALUES (@SiteId, @UserId, @FromUserId, @FromNickname, @Content, @AddTime, @IsCheck)";

                    DapperHelper.Execute(insertConnectionString, insertSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = DapperHelper.SafeParseLong(touserid, "用户ID"),
                        FromUserId = DapperHelper.SafeParseLong(userid, "发送用户ID"),
                        FromNickname = DapperHelper.LimitLength(nickname, 50),
                        Content = DapperHelper.LimitLength(text, 1000),
                        AddTime = DateTime.Now,
                        IsCheck = siteVo.isCheck
                    });

                    if (userid != touserid)
                    {
                        // ✅ 使用DapperHelper安全插入消息，避免SQL注入
                        InsertGuessBookMessageSafely();
                    }
                    INFO = "OK";
                }
                catch (Exception ex)
                {
                    ERROR = ex.ToString();
                }
            }
            VisiteCount("在<a href=\"" + http_start + "bbs/userinfo.aspx?touserid=" + touserid + "\">个人空间</a>留言了");
            Action_user_doit(5);

            // 尝试使用新版UI渲染
            if (RenderWithHandlebars())
            {
                return; // 新版渲染成功，直接返回
            }
            Showclass();
        }

        /// <summary>
        /// AJAX方式处理添加留言
        /// </summary>
        private void HandleAddMessageAjax()
        {
            try
            {
                IsLogin(userid, GetUrlQueryString());

                // 执行留言验证和添加逻辑
                var result = ValidateAndAddMessage();

                // 返回JSON响应
                WriteJsonResponse(result.Success, result.Message, result.Type, result.NewMessage);
            }
            catch (System.Threading.ThreadAbortException)
            {
                throw;
            }
            catch (Exception ex)
            {
                WriteJsonResponse(false, "系统错误：" + ex.Message, "error");
            }
        }

        /// <summary>
        /// 验证并添加留言的核心逻辑（从Method_2提取）
        /// </summary>
        private (bool Success, string Message, string Type, object NewMessage) ValidateAndAddMessage()
        {
            string text = GetRequestValue("content");
            string requestValue = GetRequestValue("face");

            if (!WapTool.IsNumeric(KL_CheckZoneReCount))
            {
                KL_CheckZoneReCount = "20";
            }

            // ✅ 修复SQL注入漏洞：使用DapperHelper替换BLL调用
            string dailyCheckConnectionString = PubConstant.GetConnectionString(a);
            string dailyCountSql = "SELECT COUNT(*) FROM wap2_userGuessBook WHERE DATEDIFF(dd, addtime, GETDATE()) < 1 AND siteid = @SiteId AND fromuserid = @FromUserId";
            long num = DapperHelper.ExecuteScalar<long>(dailyCheckConnectionString, dailyCountSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                FromUserId = DapperHelper.SafeParseLong(userid, "发送用户ID")
            });

            // 验证逻辑
            if (text.Trim().Length < 2)
            {
                return (false, "留言内容不能为空", "warning", null);
            }
            else if (num > long.Parse(KL_CheckZoneReCount))
            {
                return (false, "今日留言次数已达上限", "error", null);
            }
            else if (text.Equals(Session["content"]))
            {
                return (false, "请不要发重复内容", "warning", null);
            }
            else if (IsUserSubmitTooFrequent(userid, 10)) // 10秒内只能提交一次
            {
                return (false, "操作过快，请10秒后再试", "warning", null);
            }
            else if (WapTool.IsLockuser(siteid, userid, classid) > -1L)
            {
                return (false, "您已被加入黑名单", "error", null);
            }

            // 添加留言
            try
            {
                Session["content"] = text;
                if (text.ToLower().IndexOf("[sid]") > 0 && !IsUserManager(userid, userVo.managerlvl, ""))
                {
                    text = text.ToLower().Replace("[sid]", "[sid2]");
                    text = text.ToLower().Replace("[sid1]", "[sid2]");
                }
                if (requestValue.Trim() != "" && requestValue.Trim() != "face")
                {
                    text = "[img]face/" + requestValue + ".gif[/img]" + text;
                }
                text = "<a href=\"" + http_start + "bbs/userinfo.aspx?siteid=" + siteid + "&amp;touserid=" + userid + "\">" + nickname + "</a> <span class=\"right\">" + $"{DateTime.Now:MM-dd HH:mm}" + "</span><br/>" + text;

                // ✅ 修复编译错误：使用DapperHelper替换BLL.Add调用，避免SQL注入
                string insertConnectionString = PubConstant.GetConnectionString(a);
                string insertSql = @"INSERT INTO wap2_userGuessBook(siteid, userid, fromuserid, fromnickname, content, addtime, ischeck)
                                    VALUES (@SiteId, @UserId, @FromUserId, @FromNickname, @Content, @AddTime, @IsCheck)";

                DapperHelper.Execute(insertConnectionString, insertSql, new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    UserId = DapperHelper.SafeParseLong(touserid, "用户ID"),
                    FromUserId = DapperHelper.SafeParseLong(userid, "发送用户ID"),
                    FromNickname = DapperHelper.LimitLength(nickname, 50),
                    Content = DapperHelper.LimitLength(text, 1000),
                    AddTime = DateTime.Now,
                    IsCheck = siteVo.isCheck
                });

                if (userid != touserid)
                {
                    // ✅ 使用DapperHelper安全插入消息，避免SQL注入
                    InsertGuessBookMessageSafely();
                }

                VisiteCount("在<a href=\"" + http_start + "bbs/userinfo.aspx?touserid=" + touserid + "\">个人空间</a>留言了");
                Action_user_doit(5);

                string successMessage = siteVo.isCheck == 1 ? "留言成功，审核后显示！" : "留言成功！";

                // 构建新留言数据用于前端显示
                var newMessageData = BuildNewMessageData(text, DateTime.Now);

                return (true, successMessage, "success", newMessageData);
            }
            catch (Exception ex)
            {
                return (false, "留言发表失败：" + ex.Message, "error", null);
            }
        }

        /// <summary>
        /// 构建新留言数据用于前端显示
        /// </summary>
        private object BuildNewMessageData(string processedContent, DateTime addTime)
        {
            try
            {
                // ✅ 使用统一的AvatarHelper获取当前用户头像信息
                var currentUserAvatar = AvatarHelper.GetUserAvatar(userid, siteid, a, http_start);

                // 处理UBB内容，移除用户信息重复显示
                string cleanContent = ProcessUBBContent(processedContent);

                // 生成友好时间显示
                string friendlyTime = TimeHelper.ToFriendlyTime(addTime);
                string detailTime = TimeHelper.ToDetailTime(addTime);

                // 生成作者空间链接
                string authorSpaceUrl = $"{http_start}bbs/userinfo.aspx?siteid={siteid}&touserid={userid}";

                // 检查删除权限（新留言，当前用户肯定可以删除自己的留言）
                bool canDelete = true;

                // 计算新留言的楼层号
                // 需要重新获取当前的留言总数，因为在AJAX提交时total可能还没有初始化
                string connectionString = PubConstant.GetConnectionString(a);
                string countSql = "SELECT COUNT(*) FROM wap2_userGuessBook WHERE siteid = @SiteId AND userid = @UserId";
                long currentTotal = DapperHelper.ExecuteScalar<long>(connectionString, countSql, new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    UserId = DapperHelper.SafeParseLong(touserid, "用户ID")
                });

                // 新留言总是最新的，楼层号就是当前总数（因为查询时已经包含了刚插入的留言）
                long newMessageIndex = currentTotal;

                return new
                {
                    id = "new-" + DateTime.Now.Ticks, // 临时ID，用于前端标识
                    authorNickname = nickname,
                    authorUserId = userid,
                    content = cleanContent,
                    addTime = addTime.ToString("yyyy-MM-dd HH:mm:ss"),
                    friendlyTime = friendlyTime,
                    detailTime = detailTime,
                    authorSpaceUrl = authorSpaceUrl,
                    authorAvatarUrl = currentUserAvatar.AvatarUrl,
                    isDefaultAvatar = currentUserAvatar.IsDefaultAvatar,
                    canDelete = canDelete,
                    messageIndex = newMessageIndex, // 使用正确的楼层号
                    isNewMessage = true // 标记为新留言
                };
            }
            catch (Exception ex)
            {
                // 如果构建失败，返回null，前端会fallback到刷新页面
                System.Diagnostics.Debug.WriteLine($"[UserGuessBook] BuildNewMessageData 失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 使用DapperHelper安全插入留言本消息，避免SQL注入
        /// </summary>
        private void InsertGuessBookMessageSafely()
        {
            string guessBookConnectionString = PubConstant.GetConnectionString(a);
            string title = nickname + "在您的空间留言了";
            string content = nickname + "给您留言了：[url=/bbs/userinfo.aspx?touserid=" + touserid + "]前往个人空间查看[/url]";

            string sql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                          VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, 1)";

            DapperHelper.Execute(guessBookConnectionString, sql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                UserId = DapperHelper.SafeParseLong(siteid, "用户ID"),
                Nickname = "系统",
                Title = DapperHelper.LimitLength(title, 255),
                Content = DapperHelper.LimitLength(content, 500),
                ToUserId = DapperHelper.SafeParseLong(touserid, "目标用户ID")
            });
        }

        /// <summary>
        /// 检查并处理UI偏好设置
        /// </summary>
        private void CheckAndHandleUIPreference()
        {
            bool useNewUI = false;

            // 1. 检查URL参数强制设置
            string forceNewUI = GetRequestValue("newui");
            if (forceNewUI == "1")
            {
                useNewUI = true;
            }
            else
            {
                // 2. 检查Cookie中的UI偏好
                string uiPreference = "";
                if (Request.Cookies["ui_preference"] != null)
                {
                    uiPreference = Request.Cookies["ui_preference"].Value;
                }

                // 3. 根据Cookie决定是否使用新版UI
                if (uiPreference == "new")
                {
                    useNewUI = true;
                }
            }

            // 4. 设置ViewState标记
            if (useNewUI)
            {
                ViewState["UseNewUI"] = true;
            }
        }

        /// <summary>
        /// 使用Handlebars模板渲染页面
        /// </summary>
        /// <returns>是否成功渲染</returns>
        private bool RenderWithHandlebars()
        {
            try
            {
                // 检查是否应该使用新版UI
                bool useNewUI = ViewState["UseNewUI"] != null && (bool)ViewState["UseNewUI"];

                if (!useNewUI)
                {
                    return false; // 不使用新版UI，返回false让旧版继续处理
                }

                // 构建页面数据模型
                var pageModel = BuildUserGuessBookPageModel();

                // 构建头部选项 - 隐藏右上角按钮
                var headerOptions = new HeaderOptionsModel { ShowViewModeToggle = false };

                // 调用TemplateService渲染页面
                string finalHtml = TemplateService.RenderPageWithLayout(
                    "~/Template/Pages/UserGuessBook.hbs",
                    pageModel,
                    pageModel.PageTitle,
                    headerOptions
                );

                // 输出渲染结果
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write(finalHtml);
                Response.End();

                return true; // 渲染成功
            }
            catch (System.Threading.ThreadAbortException)
            {
                throw; // Response.End()的正常行为
            }
            catch (Exception ex)
            {
                // 记录错误但不阻止旧版UI的使用
                if (GetRequestValue("debug") == "1")
                {
                    Response.Write($"<!-- 渲染失败: {ex.Message} -->");
                }

                return false; // 渲染失败，让旧版UI继续处理
            }
        }

        /// <summary>
        /// 加载留言板数据（从Showclass方法提取的数据查询逻辑）
        /// </summary>
        private void LoadGuessBookData()
        {
            try
            {
                pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
                string connectionString = PubConstant.GetConnectionString(a);

                if (GetRequestValue("getTotal") != "" && GetRequestValue("getTotal") != "0")
                {
                    total = Convert.ToInt32(GetRequestValue("getTotal"));
                }
                else
                {
                    string countSql = "SELECT COUNT(*) FROM wap2_userGuessBook WHERE siteid = @SiteId AND userid = @UserId";
                    total = DapperHelper.ExecuteScalar<long>(connectionString, countSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = DapperHelper.SafeParseLong(touserid, "用户ID")
                    });
                }

                if (GetRequestValue("page") != "")
                {
                    CurrentPage = long.Parse(GetRequestValue("page"));
                }
                CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
                index = pageSize * (CurrentPage - 1L);
                linkURL = http_start + "bbs/userGuessBook.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;touserid=" + touserid + "&amp;ot=" + ot + "&amp;getTotal=" + total;
                linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, total, pageSize, CurrentPage, linkURL);
                linkURL = WapTool.GetPageLink(ver, lang, total, pageSize, CurrentPage, linkURL, "1");

                string orderDirection = (ot == "1") ? "ASC" : "DESC";
                string listSql = $@"SELECT * FROM wap2_userGuessBook
                                   WHERE siteid = @SiteId AND userid = @UserId
                                   ORDER BY id {orderDirection}
                                   OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

                listVo = DapperHelper.Query<wap2_userGuessBook_Model>(connectionString, listSql, new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    UserId = DapperHelper.SafeParseLong(touserid, "用户ID"),
                    Offset = (CurrentPage - 1) * pageSize,
                    PageSize = pageSize
                })?.ToList() ?? new List<wap2_userGuessBook_Model>();
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }

        /// <summary>
        /// 构建UserGuessBook页面数据模型
        /// </summary>
        /// <returns>页面数据模型</returns>
        private UserGuessBookPageModel BuildUserGuessBookPageModel()
        {
            var pageModel = new UserGuessBookPageModel();

            // 首先执行数据查询逻辑（从Showclass方法移过来）
            LoadGuessBookData();

            // 设置基本信息
            pageModel.TargetUserId = touserid;
            pageModel.CurrentUserId = userid;
            pageModel.SortOrder = ot;
            pageModel.TotalMessages = total;
            pageModel.IsOwnSpace = (userid == touserid);
            pageModel.IsCurrentUserAdmin = IsUserManager(userid, userVo.managerlvl, "");

            // ✅ 使用统一的AvatarHelper获取当前用户头像信息
            var currentUserAvatar = AvatarHelper.GetUserAvatar(userid, siteid, a, http_start);
            pageModel.CurrentUserAvatarUrl = currentUserAvatar.AvatarUrl;
            pageModel.CurrentUserIsDefaultAvatar = currentUserAvatar.IsDefaultAvatar;

            // 获取目标用户昵称
            try
            {
                string connectionString = PubConstant.GetConnectionString(a);
                string userSql = "SELECT nickname FROM [user] WHERE siteid = @SiteId AND userid = @UserId";
                string targetNickname = DapperHelper.ExecuteScalar<string>(connectionString, userSql, new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    UserId = DapperHelper.SafeParseLong(touserid, "用户ID")
                });
                pageModel.TargetUserNickname = targetNickname ?? "未知用户";
                pageModel.PageTitle = $"{pageModel.TargetUserNickname}的留言板";
            }
            catch
            {
                pageModel.TargetUserNickname = "未知用户";
                pageModel.PageTitle = "空间留言板";
            }

            // 处理留言列表数据
            pageModel.MessagesList = new List<GuessBookMessageModel>();
            if (listVo != null && listVo.Count > 0)
            {
                // 根据排序方向计算楼层号
                bool isLatestFirst = (ot != "1"); // ot=0或空表示最新在前，ot=1表示最早在前
                int startIndex;

                if (isLatestFirst)
                {
                    // 最新排序：最新的留言楼层号最高
                    // 总留言数 - (当前页-1)*每页数量 = 当前页第一条留言的楼层号
                    startIndex = (int)(total - (CurrentPage - 1) * pageSize);
                }
                else
                {
                    // 最早排序：最早的留言楼层号为1
                    startIndex = (int)((CurrentPage - 1) * pageSize) + 1;
                }

                int currentIndex = startIndex;
                foreach (var item in listVo)
                {
                    var messageModel = new GuessBookMessageModel
                    {
                        Id = item.id,
                        AuthorNickname = item.fromnickname,
                        AuthorUserId = item.fromuserid.ToString(),
                        Content = ProcessUBBContent(item.content), // 处理UBB代码
                        AddTime = item.addtime,
                        MessageIndex = currentIndex,
                        CanDelete = (userid == item.userid.ToString() || userid == item.fromuserid.ToString() || IsUserManager(userid, userVo.managerlvl, ""))
                    };

                    // 根据排序方向递增或递减楼层号
                    currentIndex = isLatestFirst ? currentIndex - 1 : currentIndex + 1;

                    // 生成友好时间显示
                    messageModel.FriendlyTime = TimeHelper.ToFriendlyTime(item.addtime);
                    messageModel.DetailTime = TimeHelper.ToDetailTime(item.addtime);

                    // 调试日志
                    if (GetRequestValue("debug") == "1")
                    {
                        System.Diagnostics.Debug.WriteLine($"[UserGuessBook] 留言时间处理 - 原始时间: {item.addtime}, 友好时间: {messageModel.FriendlyTime}, 详细时间: {messageModel.DetailTime}");
                    }

                    // 生成作者空间链接
                    messageModel.AuthorSpaceUrl = $"{http_start}bbs/userinfo.aspx?siteid={siteid}&touserid={item.fromuserid}";

                    // 首字母将由模板中的firstChar helper生成，这里不需要设置

                    // ✅ 使用统一的AvatarHelper获取留言者头像信息
                    string userId = item.fromuserid.ToString();
                    var messageUserAvatar = AvatarHelper.GetUserAvatar(userId, siteid, a, http_start);
                    messageModel.AuthorAvatarUrl = messageUserAvatar.AvatarUrl;
                    messageModel.IsDefaultAvatar = messageUserAvatar.IsDefaultAvatar;

                    pageModel.MessagesList.Add(messageModel);
                }
            }

            // 构建分页数据
            pageModel.Pagination = new PaginationModel
            {
                CurrentPage = (int)CurrentPage,
                TotalPages = (int)Math.Ceiling((double)total / pageSize),
                TotalItems = (int)total,
                PageSize = (int)pageSize,
                HasPages = total > pageSize,
                StartItem = (int)((CurrentPage - 1) * pageSize + 1),
                EndItem = (int)Math.Min(CurrentPage * pageSize, total),
                PrevPage = (int)Math.Max(1, CurrentPage - 1),
                NextPage = (int)Math.Min(Math.Ceiling((double)total / pageSize), CurrentPage + 1)
            };

            // 构建表单数据
            pageModel.FormData = new GuessBookFormModel
            {
                Content = "",
                Face = face,
                BackUrl = GetUrlQueryString(),
                ActionUrl = $"{http_start}bbs/userGuessBook.aspx",
                MaxLength = 1000,
                MinLength = 2
            };

            // 设置状态消息
            pageModel.Info = INFO;
            pageModel.Error = ERROR;

            // 设置站点信息
            pageModel.SiteInfo = new SiteInfoModel
            {
                SiteId = siteid,
                SiteName = siteVo.sitename,
                SiteUrl = http_start,
                IsCheck = (siteVo.isCheck == 1)  // 添加审核设置
            };

            // 设置其他必要属性
            pageModel.ClassId = classid;
            pageModel.ShowSortTabs = true;

            return pageModel;
        }





        /// <summary>
        /// 处理UBB内容转换为HTML
        /// </summary>
        /// <param name="content">原始UBB内容</param>
        /// <returns>转换后的HTML内容</returns>
        private string ProcessUBBContent(string content)
        {
            if (string.IsNullOrEmpty(content))
            {
                return "";
            }

            try
            {
                // 使用WapTool的ToWML方法进行UBB转换
                string processedContent = WapTool.ToWML(content, wmlVo);

                // 移除可能包含的用户信息重复显示
                if (!string.IsNullOrEmpty(processedContent))
                {
                    // 移除开头可能包含的用户名和时间信息
                    // 匹配格式: "<a href="...">用户名</a> <span class="right">MM-dd HH:mm</span><br/>"
                    var linkTimeRegex = new System.Text.RegularExpressions.Regex(
                        @"^<a\s+href=""[^""]*""[^>]*>[^<]*</a>\s*<span[^>]*>[^<]*</span><br\s*\/?>",
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                    processedContent = linkTimeRegex.Replace(processedContent, "").Trim();

                    // 备用匹配：简单的 "用户名 MM-dd HH:mm<br/>" 格式
                    var simpleTimeRegex = new System.Text.RegularExpressions.Regex(
                        @"^[^<\n]*\s+\d{2}-\d{2}\s+\d{2}:\d{2}(<br\s*\/?>|\n|$)",
                        System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                    processedContent = simpleTimeRegex.Replace(processedContent, "").Trim();

                    // 如果还有多余的<br/>开头，也移除
                    while (processedContent.StartsWith("<br/>") || processedContent.StartsWith("<br>"))
                    {
                        processedContent = System.Text.RegularExpressions.Regex.Replace(
                            processedContent, @"^<br\s*\/?>", "", System.Text.RegularExpressions.RegexOptions.IgnoreCase).Trim();
                    }
                }

                return processedContent;
            }
            catch
            {
                // 如果UBB转换失败，返回原始内容
                return content;
            }
        }

        /// <summary>
        /// ✅ 使用DapperHelper处理删除单个留言（AJAX删除）
        /// </summary>
        private void HandleDeleteMessage()
        {
            try
            {
                IsLogin(userid, GetUrlQueryString());

                string messageId = GetRequestValue("id");
                if (string.IsNullOrEmpty(messageId) || !WapTool.IsNumeric(messageId))
                {
                    WriteJsonResponse(false, "无效的留言ID");
                    return;
                }

                string connectionString = PubConstant.GetConnectionString(a);

                // 首先检查留言是否存在以及获取留言信息
                string checkSql = "SELECT fromuserid, userid FROM wap2_userGuessBook WHERE id = @Id AND siteid = @SiteId";
                var messageInfoList = DapperHelper.Query<dynamic>(connectionString, checkSql, new {
                    Id = DapperHelper.SafeParseLong(messageId, "留言ID"),
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID")
                });
                var messageInfo = messageInfoList.FirstOrDefault();

                if (messageInfo == null)
                {
                    WriteJsonResponse(false, "留言不存在或已被删除");
                    return;
                }

                // 检查删除权限：空间主人、留言作者、管理员都可以删除
                string spaceOwnerId = messageInfo.userid.ToString(); // 留言所属空间的主人ID
                string messageAuthorId = messageInfo.fromuserid.ToString(); // 留言作者ID
                bool canDelete = (userid == spaceOwnerId || userid == messageAuthorId || IsUserManager(userid, userVo.managerlvl, ""));

                // 调试日志
                if (GetRequestValue("debug") == "1")
                {
                    System.Diagnostics.Debug.WriteLine($"[UserGuessBook] 删除权限检查 - 当前用户ID: {userid}, 空间主人ID: {spaceOwnerId}, 留言作者ID: {messageAuthorId}, 是否管理员: {IsUserManager(userid, userVo.managerlvl, "")}, 可以删除: {canDelete}");
                }

                if (!canDelete)
                {
                    WriteJsonResponse(false, $"没有权限删除此留言 (当前用户: {userid}, 空间主人: {spaceOwnerId}, 留言作者: {messageAuthorId})");
                    return;
                }

                // 执行删除操作
                string deleteSql = "DELETE FROM wap2_userGuessBook WHERE id = @Id AND siteid = @SiteId";
                int result = DapperHelper.Execute(connectionString, deleteSql, new {
                    Id = DapperHelper.SafeParseLong(messageId, "留言ID"),
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID")
                });

                if (result > 0)
                {
                    WriteJsonResponse(true, "删除成功");
                }
                else
                {
                    WriteJsonResponse(false, "删除失败，留言不存在或已被删除");
                }
            }
            catch (System.Threading.ThreadAbortException)
            {
                throw;
            }
            catch (Exception ex)
            {
                WriteJsonResponse(false, "删除失败：" + ex.Message);
            }
        }

        /// <summary>
        /// 输出JSON响应（重载版本，支持类型和新留言数据）
        /// </summary>
        private void WriteJsonResponse(bool success, string message, string type = null, object newMessage = null)
        {
            try
            {
                Response.Clear();
                Response.ContentType = "application/json; charset=utf-8";
                Response.Cache.SetCacheability(HttpCacheability.NoCache);

                // 转义消息中的引号，防止JSON格式错误
                string escapedMessage = message.Replace("\"", "\\\"").Replace("\r", "").Replace("\n", "");

                // 构建JSON响应
                var jsonParts = new List<string>
                {
                    $"\"success\": {success.ToString().ToLower()}",
                    $"\"message\": \"{escapedMessage}\""
                };

                if (!string.IsNullOrEmpty(type))
                {
                    jsonParts.Add($"\"type\": \"{type}\"");
                }

                if (newMessage != null)
                {
                    jsonParts.Add($"\"newMessage\": {Newtonsoft.Json.JsonConvert.SerializeObject(newMessage)}");
                }

                string json = "{" + string.Join(", ", jsonParts) + "}";

                // 调试日志
                System.Diagnostics.Debug.WriteLine($"[UserGuessBook] WriteJsonResponse - JSON: {json}");

                Response.Write(json);
                Response.Flush();
                Response.End();
            }
            catch (System.Threading.ThreadAbortException)
            {
                // Response.End() 的正常行为
                throw;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[UserGuessBook] WriteJsonResponse 异常: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 输出JSON响应（原版本，保持向后兼容）
        /// </summary>
        private void WriteJsonResponse(bool success, string message)
        {
            WriteJsonResponse(success, message, null, null);
        }

        /// <summary>
        /// 删除留言（兼容旧版UI）
        /// </summary>
        public void DeleteMessage()
        {
            IsLogin(userid, GetUrlQueryString());

            string messageId = GetRequestValue("id");
            if (string.IsNullOrEmpty(messageId) || !WapTool.IsNumeric(messageId))
            {
                INFO = "INVALID_ID";
                Showclass();
                return;
            }

            try
            {
                string connectionString = PubConstant.GetConnectionString(a);

                // 首先检查留言是否存在以及获取留言信息
                string checkSql = "SELECT fromuserid, userid FROM wap2_userGuessBook WHERE id = @Id AND siteid = @SiteId";
                var messageInfoList = DapperHelper.Query<dynamic>(connectionString, checkSql, new {
                    Id = DapperHelper.SafeParseLong(messageId, "留言ID"),
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID")
                });
                var messageInfo = messageInfoList.FirstOrDefault();

                if (messageInfo == null)
                {
                    INFO = "NOT_FOUND";
                    Showclass();
                    return;
                }

                // 检查删除权限：空间主人、留言作者、管理员都可以删除
                string spaceOwnerId = messageInfo.userid.ToString(); // 留言所属空间的主人ID
                string messageAuthorId = messageInfo.fromuserid.ToString(); // 留言作者ID
                bool canDelete = (userid == spaceOwnerId || userid == messageAuthorId || IsUserManager(userid, userVo.managerlvl, ""));
                if (!canDelete)
                {
                    INFO = "NO_PERMISSION";
                    Showclass();
                    return;
                }

                // 执行删除操作
                string deleteSql = "DELETE FROM wap2_userGuessBook WHERE id = @Id AND siteid = @SiteId";
                int affectedRows = DapperHelper.Execute(connectionString, deleteSql, new {
                    Id = DapperHelper.SafeParseLong(messageId, "留言ID"),
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID")
                });

                if (affectedRows > 0)
                {
                    INFO = "DELETE_SUCCESS";
                }
                else
                {
                    INFO = "DELETE_FAILED";
                }
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }

            // 删除完成后重新显示列表
            Showclass();
        }

        /// <summary>
        /// 检查用户提交是否过于频繁
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="intervalSeconds">间隔秒数</param>
        /// <returns>true表示提交过于频繁</returns>
        private bool IsUserSubmitTooFrequent(string userId, int intervalSeconds)
        {
            try
            {
                string sessionKey = $"LastSubmitTime_{userId}";
                DateTime? lastSubmitTime = Session[sessionKey] as DateTime?;
                DateTime currentTime = DateTime.Now;

                if (lastSubmitTime.HasValue)
                {
                    TimeSpan timeDiff = currentTime - lastSubmitTime.Value;
                    if (timeDiff.TotalSeconds < intervalSeconds)
                    {
                        return true; // 提交过于频繁
                    }
                }

                // 更新最后提交时间
                Session[sessionKey] = currentTime;
                return false; // 可以提交
            }
            catch (Exception ex)
            {
                // 如果检查失败，为了安全起见，允许提交
                System.Diagnostics.Debug.WriteLine($"[UserGuessBook] 频率检查失败: {ex.Message}");
                return false;
            }
        }

    }
}