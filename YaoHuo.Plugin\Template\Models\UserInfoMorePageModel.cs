using YaoHuo.Plugin.Template.Models;

namespace YaoHuo.Plugin.BBS.Models
{
    /// <summary>
    /// 详细资料页面数据模型
    /// </summary>
    public class UserInfoMorePageModel : BasePageModel
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public UserInfoMorePageModel()
        {
            PageTitle = "详细资料";
        }

        /// <summary>
        /// 用户基本信息（论坛资料）
        /// </summary>
        public UserInfoMoreBasicModel BasicInfo { get; set; } = new UserInfoMoreBasicModel();

        /// <summary>
        /// 用户个人资料
        /// </summary>
        public UserPersonalInfoModel PersonalInfo { get; set; } = new UserPersonalInfoModel();

        /// <summary>
        /// 权限控制
        /// </summary>
        public UserPermissionModel Permission { get; set; } = new UserPermissionModel();

        /// <summary>
        /// 目标用户ID
        /// </summary>
        public string TargetUserId { get; set; }

        /// <summary>
        /// 是否为查看自己的资料
        /// </summary>
        public bool IsOwnProfile { get; set; }
    }

    /// <summary>
    /// 用户基本信息模型（论坛资料）
    /// </summary>
    public class UserInfoMoreBasicModel
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 昵称
        /// </summary>
        public string Nickname { get; set; }

        /// <summary>
        /// 显示昵称（带HTML格式）
        /// </summary>
        public string DisplayNickname { get; set; }

        /// <summary>
        /// 妖晶数量
        /// </summary>
        public long Money { get; set; }

        /// <summary>
        /// 妖晶显示文本
        /// </summary>
        public string MoneyDisplay { get; set; }

        /// <summary>
        /// 经验值
        /// </summary>
        public long Experience { get; set; }

        /// <summary>
        /// 等级
        /// </summary>
        public string Level { get; set; }

        /// <summary>
        /// 头衔
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 身份
        /// </summary>
        public string Identity { get; set; }

        /// <summary>
        /// 权限描述
        /// </summary>
        public string Permission { get; set; }

        /// <summary>
        /// 勋章HTML
        /// </summary>
        public string MedalHtml { get; set; }

        /// <summary>
        /// 在线状态
        /// </summary>
        public string OnlineStatus { get; set; }

        /// <summary>
        /// 积时显示
        /// </summary>
        public string LoginTimeDisplay { get; set; }

        /// <summary>
        /// 注册时间
        /// </summary>
        public string RegisterTime { get; set; }

        /// <summary>
        /// 个性签名
        /// </summary>
        public string Remark { get; set; }
    }

    /// <summary>
    /// 用户个人资料模型
    /// </summary>
    public class UserPersonalInfoModel
    {
        /// <summary>
        /// 性别
        /// </summary>
        public int Sex { get; set; }

        /// <summary>
        /// 性别显示文本
        /// </summary>
        public string SexDisplay { get; set; }

        /// <summary>
        /// 年龄
        /// </summary>
        public int Age { get; set; }

        /// <summary>
        /// 身高
        /// </summary>
        public string Height { get; set; }

        /// <summary>
        /// 体重
        /// </summary>
        public string Weight { get; set; }

        /// <summary>
        /// 星座
        /// </summary>
        public string Constellation { get; set; }

        /// <summary>
        /// 爱好
        /// </summary>
        public string Hobby { get; set; }

        /// <summary>
        /// 婚否
        /// </summary>
        public string MaritalStatus { get; set; }

        /// <summary>
        /// 职业
        /// </summary>
        public string Profession { get; set; }

        /// <summary>
        /// 城市
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// QQ号（需要权限查看）
        /// </summary>
        public string QQ { get; set; }

        /// <summary>
        /// 邮箱（需要权限查看）
        /// </summary>
        public string Email { get; set; }
    }

    /// <summary>
    /// 用户权限控制模型
    /// </summary>
    public class UserPermissionModel
    {
        /// <summary>
        /// 是否可以查看详细信息
        /// </summary>
        public bool CanViewDetails { get; set; }

        /// <summary>
        /// 是否可以查看敏感信息（QQ、邮箱等）
        /// </summary>
        public bool CanViewSensitiveInfo { get; set; }

        /// <summary>
        /// 是否为本人或管理员
        /// </summary>
        public bool IsOwnerOrAdmin { get; set; }

        /// <summary>
        /// 权限等级
        /// </summary>
        public string PermissionLevel { get; set; }

        /// <summary>
        /// VIP升级链接（当无权限查看敏感信息时显示）
        /// </summary>
        public string VipUpgradeUrl { get; set; }
    }
}
