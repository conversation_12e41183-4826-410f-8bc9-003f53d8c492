<!-- MessageDetail.hbs - 消息详情页面（聊天界面） -->
<!-- 消息提示 -->
{{#if Message.HasMessage}}
<div class="message {{Message.Type}}">
    {{Message.Content}}
</div>
{{/if}}

<!-- 聊天内容区域 -->
<main class="flex-1 pt-0 pb-20 overflow-y-auto">
    <!-- 隐藏的页面数据，供JavaScript使用 -->
    <script type="application/json" id="page-data">
    {
        "partnerId": "{{ConversationPartner.UserId}}",
        "currentUserId": "{{UserInfo.CurrentUserId}}",
        "anchorMessageId": "{{AnchorMessageId}}"
    }
    </script>
    <div class="px-4 xs-400:px-3 xs-310:px-1 chat-container">

        <!-- 手动加载更早消息按钮 -->
        {{#if ConversationSettings.HasMoreMessages}}
        <div class="my-4 text-center" id="load-older-container">
            <button id="load-older-btn" class="text-sm text-primary hover:text-primary-dark font-medium transition-colors duration-200">
                查看更早消息
            </button>
        </div>
        {{/if}}

        {{#if ConversationSettings.ShowChatHistory}}
        {{#each ChatHistory}}
            <!-- 日期分割线 -->
            {{#if IsNewDateGroup}}
            <div class="text-center my-4">
                <span class="text-xs text-text-secondary bg-gray-200 px-3 py-1 rounded-full">{{DateGroup}}</span>
            </div>
            {{/if}}
            
            {{#if IsFromCurrentUser}}
            <!-- 当前用户的消息（右对齐） -->
            <div class="chat-message-out flex items-start gap-3 xs-400:gap-2 xs-310:gap-1 justify-end {{SpacingClass}}" data-message-id="{{Id}}">
                <!-- 消息内容 -->
                <div class="message-content flex flex-col items-end max-w-[75%]">
                    <div class="flex flex-col items-end space-y-1">
                        <div class="p-3 shadow-sm bg-gradient-to-br from-primary to-primary-dark text-white rounded-xl rounded-br-sm break-words break-all bubble-right"
                            {{#if this.IsAnchor}}
                                id="anchor-message"
                            {{/if}}>
                            <div class="text-base leading-relaxed">{{{ProcessedContent}}}</div>
                        </div>
                        {{#if ShowTime}}
                        <div class="text-xs text-text-secondary mt-1 mr-1 message-time">{{TimeDisplay}}</div>
                        {{/if}}
                    </div>
                </div>
                <!-- 头像或占位空间 -->
                {{#if ShowAvatar}}
                <a href="/bbs/userinfo.aspx?touserid={{../UserInfo.CurrentUserId}}" class="user-avatar-link block">
                    <div class="user-avatar w-10 h-10 rounded-full bg-gray-150 flex items-center justify-center flex-shrink-0 shadow-sm hover:shadow-md hover:bg-gray-300 transition-all duration-200 cursor-pointer relative">
                        <!-- 首字母fallback -->
                        <span class="avatar-fallback-main text-lg font-semibold text-primary-dark" data-fallback="true">我</span>
                        <!-- 头像图片 -->
                        {{#if SenderAvatarUrl}}
                        {{#unless SenderIsDefaultAvatar}}
                        <img src="{{SenderAvatarUrl}}"
                             alt="我的头像"
                             class="w-10 h-10 object-fill absolute top-0 left-0 z-[1] hidden rounded-full"
                             data-avatar-src="{{SenderAvatarUrl}}">
                        {{/unless}}
                        {{/if}}
                    </div>
                </a>
                {{else}}
                <!-- 连续消息：使用占位空间保持对齐 -->
                <div class="w-10 h-10 flex-shrink-0"></div>
                {{/if}}
            </div>
            {{else}}
            <!-- 对方的消息（左对齐） -->
            <div class="chat-message-in flex items-start gap-3 xs-400:gap-2 xs-310:gap-1 {{SpacingClass}}" data-message-id="{{Id}}">
                <!-- 头像或占位空间 -->
                {{#if ShowAvatar}}
                <a href="/bbs/userinfo.aspx?touserid={{SenderId}}" class="user-avatar-link block">
                    <div class="user-avatar w-10 h-10 rounded-full bg-gray-150 flex items-center justify-center flex-shrink-0 shadow-sm hover:shadow-md hover:bg-gray-300 transition-all duration-200 cursor-pointer relative">
                        <!-- 首字母fallback -->
                        <span class="avatar-fallback-small text-lg font-semibold text-primary-dark" data-fallback="true">{{SenderFirstChar}}</span>
                        <!-- 头像图片 -->
                        {{#if SenderAvatarUrl}}
                        {{#unless SenderIsDefaultAvatar}}
                        <img src="{{SenderAvatarUrl}}"
                             alt="{{SenderNickname}}的头像"
                             class="w-10 h-10 object-fill absolute top-0 left-0 z-[1] hidden rounded-full"
                             data-avatar-src="{{SenderAvatarUrl}}">
                        {{/unless}}
                        {{/if}}
                    </div>
                </a>
                {{else}}
                <!-- 连续消息：使用占位空间保持对齐 -->
                <div class="w-10 h-10 flex-shrink-0"></div>
                {{/if}}
                <!-- 消息内容 -->
                <div class="message-content flex flex-col max-w-[75%]">
                    <div class="flex flex-col items-start space-y-1">
                        <div class="p-3 shadow-sm bg-gray-100 text-black rounded-xl rounded-bl-sm break-words break-all bubble-left"
                            {{#if this.IsAnchor}}
                                id="anchor-message"
                            {{/if}}>
                            <div class="text-base leading-relaxed">{{{ProcessedContent}}}</div>
                        </div>
                        {{#if ShowTime}}
                        <div class="text-xs text-text-secondary mt-1 ml-1 message-time">{{TimeDisplay}}</div>
                        {{/if}}
                    </div>
                </div>
            </div>
            {{/if}}
        {{/each}}
        {{else}}
        <!-- 无聊天记录时的提示 -->
        <div class="text-center py-8">
            <div class="text-text-secondary">显示最近50条聊天记录</div>
        </div>
        {{/if}}

        <!-- 手动加载更新消息按钮 -->
        {{#if ConversationSettings.HasMoreNewerMessages}}
        <div class="my-4 text-center" id="load-newer-container">
            <button id="load-newer-btn" class="text-sm text-primary hover:text-primary-dark font-medium transition-colors duration-200">
                查看后续消息
            </button>
        </div>
        {{/if}}

    </div>
</main>

<!-- 底部固定输入框 -->
{{#if ConversationSettings.CanReply}}
<footer class="fixed bottom-0 w-full max-w-[720px] mx-auto z-40 bg-white border-t border-gray-200 shadow-[0_-2px_10px_rgba(0,0,0,0.05)]">
    <div class="flex items-center p-3 gap-3">
        <form name="f" action="/bbs/messagelist_add.aspx" method="post" class="flex items-center gap-1 w-full" id="message-form">
            
            {{#if ReplyForm.NeedPassword}}
            <!-- 需要密码验证时的简化输入 -->
            <input type="text" name="content" placeholder="回复内容..." class="flex-1 bg-gray-100 focus:bg-white rounded-lg px-4 py-2.5 text-base border border-transparent focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all duration-200" />
            <input type="text" name="needpw" value="{{ReplyForm.Password}}" placeholder="我的密码" class="w-24 bg-gray-100 focus:bg-white rounded-lg px-3 py-2.5 text-sm border border-transparent focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all duration-200" />
            {{else}}
            <!-- 正常回复时的文本域 -->
            <textarea name="content" id="message-input" rows="1" placeholder="输入消息..." class="flex-1 bg-gray-100 focus:bg-white rounded-lg px-4 py-2.5 text-base border border-transparent focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary/50 transition-all duration-200 resize-none max-h-32" oninput="adjustTextareaHeight(this)"></textarea>
            {{/if}}
            
            <!-- 隐藏字段 -->
            {{#each ReplyForm.HiddenFields}}
            <input type="hidden" name="{{@key}}" value="{{this}}" />
            {{/each}}
            
            <input type="hidden" name="action" value="gomod" />
            <input type="hidden" name="toid" value="{{ReplyForm.TargetMessageId}}" />
            <input type="hidden" name="touseridlist" value="{{ReplyForm.TargetUserIds}}" />
            
            <button type="submit" id="send-button" class="bg-primary text-white rounded-lg px-5 py-2.5 font-semibold hover:bg-primary-dark transition-colors duration-200 flex-shrink-0 disabled:bg-primary-light disabled:cursor-not-allowed">
                发送
            </button>
        </form>
    </div>
</footer>
{{/if}}

<!-- 图片预览悬浮层 -->
<div class="image-overlay" id="imageOverlay" onclick="hideImageOverlay(event)">
    <div class="image-overlay-content" onclick="event.stopPropagation()">
        <img src="" alt="预览图片" class="image-overlay-image" id="overlayImage">
    </div>
</div>

<style>
/* 锚点消息高亮样式 - 区分对方和己方的边框脉冲效果 */

/* 对方气泡高亮（bubble-left）- 保持原绿色 */
@keyframes highlight-border-pulse-left {
    0% {
        box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05), 0 0 0 0 rgba(88, 180, 176, 0.7);
    }
    40% {
        box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05), 0 0 0 3px rgba(88, 180, 176, 0.7);
    }
    100% {
        box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05), 0 0 0 3px rgba(88, 180, 176, 0);
    }
}

/* 己方气泡高亮（bubble-right）- 醒目黄色 */
@keyframes highlight-border-pulse-right {
    0% {
        box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05), 0 0 0 0 rgba(251, 191, 36, 0.9);
    }
    40% {
        box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05), 0 0 0 3px rgba(251, 191, 36, 0.9);
    }
    100% {
        box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05), 0 0 0 3px rgba(251, 191, 36, 0);
    }
}

/* 高亮样式类 */
.highlight-anchor-left {
    animation: highlight-border-pulse-left 2.5s ease-out;
}

.highlight-anchor-right {
    animation: highlight-border-pulse-right 2.5s ease-out;
}

/* 兼容性：保留原有类名，默认使用对方气泡样式 */
.highlight-anchor {
    animation: highlight-border-pulse-left 2.5s ease-out;
}

/* 动态加载指示器样式 */
.loading-indicator {
    opacity: 0;
    animation: fadeIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 旋转动画 */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* 滚动哨兵元素样式 */
.scroll-sentinel {
    height: 1px;
    width: 100%;
    pointer-events: none;
    opacity: 0;
    position: relative;
}

.scroll-sentinel-top {
    /* 顶部哨兵：用于检测向上滚动 */
}

.scroll-sentinel-bottom {
    /* 底部哨兵：用于检测向下滚动 */
}

/* 头像链接样式 */
.user-avatar-link {
    text-decoration: none;
    outline: none;
}

/* 头像悬停效果 */
.user-avatar:hover {
    transform: scale(1.05);
}

/* 消息气泡内图片样式 */
.message-content img {
    max-height: min(300px, 40vh);
    max-width: 100%;
    object-fit: contain;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.message-content img:hover {
    opacity: 0.9;
    transform: scale(1.02);
}

/* 图片预览悬浮层样式 */
.image-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.image-overlay.show {
    opacity: 1;
    visibility: visible;
}

.image-overlay-content {
    max-width: 95vw;
    max-height: 95vh;
    position: relative;
    cursor: default;
}

.image-overlay-image {
    max-width: 90vw;
    max-height: 90vh;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}
</style>

<script>
// 文本域自动调整高度功能
function adjustTextareaHeight(textarea) {
    // 先重置高度为auto，让浏览器重新计算所需高度
    textarea.style.height = 'auto';

    // 获取内容实际需要的高度
    const scrollHeight = textarea.scrollHeight;

    // 设置新的高度，但不超过最大高度限制
    textarea.style.height = scrollHeight + 'px';
}

// 图片预览功能
function showImageOverlay(imageSrc) {
    const overlay = document.getElementById('imageOverlay');
    const overlayImage = document.getElementById('overlayImage');

    if (overlay && overlayImage) {
        overlayImage.src = imageSrc;
        overlay.classList.add('show');

        // 添加ESC键关闭功能
        document.addEventListener('keydown', handleEscapeKey);
    }
}

function hideImageOverlay(event) {
    // 如果点击的是图片本身，不关闭悬浮层
    if (event && event.target.classList.contains('image-overlay-image')) {
        return;
    }

    const overlay = document.getElementById('imageOverlay');
    if (overlay) {
        overlay.classList.remove('show');

        // 移除ESC键监听
        document.removeEventListener('keydown', handleEscapeKey);
    }
}

function handleEscapeKey(event) {
    if (event.key === 'Escape') {
        hideImageOverlay();
    }
}

// 为消息内容中的图片添加点击预览功能
function initImagePreview() {
    const messageImages = document.querySelectorAll('.message-content img:not([data-preview-enabled])');
    messageImages.forEach(img => {
        // 🔗 检查图片是否在 <a> 标签内（超链接图片）
        const parentLink = img.closest('a');
        
        if (parentLink) {
            // 如果图片在 <a> 标签内，不添加预览功能，让链接正常工作
            img.setAttribute('data-preview-enabled', 'link');
        } else {
            // 如果图片不在 <a> 标签内，添加大图预览功能
            img.addEventListener('click', function(e) {
                e.preventDefault();
                showImageOverlay(this.src);
            });
            // 标记已添加预览功能，避免重复绑定
            img.setAttribute('data-preview-enabled', 'preview');
        }
    });
}

// === 锚点自动滚动锁定全局变量 ===
window.anchorAutoScrollLocked = false;

// 🎯 使用新的统一锚点系统（替换原有的分散滚动机制）
function initUnifiedAnchorSystem() {
    // 如果已锁定，则不再执行自动滚动逻辑，但保持可能的首次高亮
    if (window.anchorAutoScrollLocked) {
        return;
    }

    // 获取页面数据中的锚点ID
    const pageData = getPageData();
    const urlAnchorId = pageData?.anchorMessageId;

    // 查找锚点元素
    let targetElement = document.getElementById('anchor-message');

    // 如果没有找到标准锚点元素，尝试根据URL参数查找
    if (!targetElement && urlAnchorId) {
        const allMessages = document.querySelectorAll('[data-message-id]');
        for (const msgElement of allMessages) {
            const messageId = msgElement.getAttribute('data-message-id');
            if (messageId === urlAnchorId) {
                targetElement = msgElement;
                // 为找到的元素添加锚点标识
                targetElement.id = 'anchor-message';
                break;
            }
        }
    }

    if (!targetElement) {
        return;
    }

    // 🚀 核心优化：统一滚动控制
    let isScrolled = false;
    let scrollTimer = null;

    // 统一的滚动和高亮函数
    function performAnchorScrollAndHighlight() {
        // 若已锁定，直接退出，避免再次滚动
        if (window.anchorAutoScrollLocked) {
            return;
        }
        if (isScrolled) {
            return;
        }
        isScrolled = true;

        // 🔧 移动端适配的精确滚动定位
        if (isMobileDevice()) {
            // 移动端使用自定义计算，避免视口高度问题
            const targetRect = targetElement.getBoundingClientRect();
            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const viewportHeight = window.innerHeight;
            
            // 计算目标位置：元素顶部 - 30% 视口高度（让锚点稍微偏上显示）
            const targetScrollTop = currentScrollTop + targetRect.top - (viewportHeight * 0.3);
            
            window.scrollTo({
                top: Math.max(0, targetScrollTop),
                behavior: 'smooth'
            });
        } else {
            // 桌面端使用标准方法
            targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'nearest'
            });
        }

        // 添加高亮效果
        applyHighlightEffect(targetElement);
        
        // 🔧 移动端滚动完成后的微调检查
        if (isMobileDevice()) {
            setTimeout(() => {
                checkAndAdjustMobileScroll();
            }, 600); // 等待滚动动画完成
        }
    }

    // 防抖滚动函数：防止多次触发
    function debouncedScroll() {
        if (scrollTimer) {
            clearTimeout(scrollTimer);
        }
        
        scrollTimer = setTimeout(() => {
            if (!isScrolled) {
                performAnchorScrollAndHighlight();
            }
        }, 50); // 很短的延迟，但足以防抖
    }

    // 🔧 智能策略：结合图片加载状态和页面稳定性
    const allImages = document.querySelectorAll('img');
    const unloadedImages = Array.from(allImages).filter(img => !img.complete);

    if (unloadedImages.length === 0 || unloadedImages.length === 1) {
        // 图片很少或已全部加载，立即滚动
        if (isMobileDevice()) {
            // 移动端给更多时间让页面稳定
            setTimeout(() => {
                performAnchorScrollAndHighlight();
            }, 200);
        } else {
            requestAnimationFrame(() => {
                performAnchorScrollAndHighlight();
            });
        }
    } else {
        // 有多个未加载图片，使用优化的等待策略
        let loadedCount = 0;
        const targetLoadCount = Math.min(2, Math.ceil(unloadedImages.length * 0.5)); // 最多等待2个图片或50%

        // 为关键图片添加加载监听
        const criticalImages = unloadedImages.slice(0, 3); // 只监听前3个图片
        
        criticalImages.forEach((img, index) => {
            const handleLoad = () => {
                loadedCount++;
                
                if (loadedCount >= targetLoadCount && !isScrolled) {
                    debouncedScroll();
                }
                
                // 清理监听器
                img.removeEventListener('load', handleLoad);
                img.removeEventListener('error', handleLoad);
            };

            img.addEventListener('load', handleLoad);
            img.addEventListener('error', handleLoad);
        });

        // 设置合理的最大等待时间
        const waitTime = isMobileDevice() ? 1200 : 800; // 移动端等待更长时间
        setTimeout(() => {
            if (!isScrolled) {
                performAnchorScrollAndHighlight();
            }
        }, waitTime);
    }

    // 🔍 使用ResizeObserver监听页面高度变化，进行微调（带稳定期保护）
    if (window.ResizeObserver) {
        let scrollTime = 0; // 记录滚动完成时间
        
        const resizeObserver = new ResizeObserver(entries => {
            // 锁定后直接跳出，避免再次滚动到锚点
            if (window.anchorAutoScrollLocked) {
                return;
            }
            if (isScrolled) {
                const currentTime = Date.now();
                const timeSinceScroll = currentTime - scrollTime;
                
                // 🛡️ 设置稳定期：移动端需要更长的稳定时间
                const stableTime = isMobileDevice() ? 5000 : 3000;
                if (timeSinceScroll < stableTime) {
                    return;
                }
                
                // 滚动后的微调：只在高度变化较大时才微调
                const currentHeight = entries[0].contentRect.height;
                const lastHeight = window.lastObservedHeight || currentHeight;
                const heightChange = Math.abs(currentHeight - lastHeight);
                
                // 移动端使用更高的阈值，避免频繁微调
                const threshold = isMobileDevice() ? 150 : 100;
                if (heightChange > threshold) {
                    // 重置滚动状态，允许微调
                    isScrolled = false;
                    scrollTime = Date.now(); // 更新滚动时间
                    debouncedScroll();
                }
                window.lastObservedHeight = currentHeight;
            }
        });

        // 重写滚动函数，记录滚动时间
        const originalPerformScroll = performAnchorScrollAndHighlight;
        performAnchorScrollAndHighlight = function() {
            const result = originalPerformScroll.call(this);
            scrollTime = Date.now(); // 记录滚动完成时间
            return result;
        };

        resizeObserver.observe(document.body);
        
        // 页面离开时清理
        window.addEventListener('beforeunload', () => {
            resizeObserver.disconnect();
        });
    }
}

// 优化的高亮效果函数
function applyHighlightEffect(targetElement) {
    if (!targetElement) {
        return;
    }

    // 根据气泡类型选择合适的高亮样式
    let highlightClass = 'highlight-anchor'; // 默认样式（对方气泡）
    
    // 检测是否为己方气泡（bubble-right）
    if (targetElement.classList.contains('bubble-right')) {
        highlightClass = 'highlight-anchor-right'; // 己方气泡使用醒目黄
    } else if (targetElement.classList.contains('bubble-left')) {
        highlightClass = 'highlight-anchor-left'; // 对方气泡使用原绿色
    }

    // 添加高亮效果
    targetElement.classList.add(highlightClass);
    
    // 2.5秒后移除高亮
    setTimeout(() => {
        targetElement.classList.remove(highlightClass);
    }, 2500);
}

// 移动端设备检测函数
function isMobileDevice() {
    // 检测移动设备的多种方法
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    
    // 1. 检测触摸支持
    const isTouchDevice = ('ontouchstart' in window) || 
                         (navigator.maxTouchPoints > 0) || 
                         (navigator.msMaxTouchPoints > 0);
    
    // 2. 检测屏幕宽度
    const isSmallScreen = window.innerWidth <= 768;
    
    // 3. 检测 User Agent
    const isMobileUA = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
    
    // 综合判断：同时满足触摸支持和（小屏幕或移动UA）
    return isTouchDevice && (isSmallScreen || isMobileUA);
}

// 移动端滚动位置检查和微调函数
function checkAndAdjustMobileScroll() {
    const anchor = document.getElementById('anchor-message');
    if (!anchor) return;
    
    const rect = anchor.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    
    // 检查锚点是否在合适的可视区域内（30%-70% 的位置）
    const targetMinTop = viewportHeight * 0.2;
    const targetMaxTop = viewportHeight * 0.6;
    
    if (rect.top < targetMinTop || rect.top > targetMaxTop) {
        // 如果位置不合适，进行微调
        const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const idealTop = viewportHeight * 0.35; // 理想位置：35% 处
        const adjustment = rect.top - idealTop;
        
        window.scrollTo({
            top: currentScrollTop + adjustment,
            behavior: 'smooth'
        });
    }
}

// 获取页面数据的辅助函数
function getPageData() {
    try {
        const pageDataElement = document.getElementById('page-data');
        if (pageDataElement) {
            return JSON.parse(pageDataElement.textContent);
        }
    } catch (e) {
        // 静默处理解析失败的情况
    }
    return null;
}

// 智能滚动到底部
function scrollToBottom() {
    // 使用 requestAnimationFrame 确保 DOM 更新完成后再滚动
    requestAnimationFrame(() => {
        // 使用全局窗口滚动，符合用户偏好设置
        const documentHeight = Math.max(
            document.body.scrollHeight,
            document.body.offsetHeight,
            document.documentElement.clientHeight,
            document.documentElement.scrollHeight,
            document.documentElement.offsetHeight
        );

        // 平滑滚动到页面底部
        window.scrollTo({
            top: documentHeight,
            behavior: 'smooth'
        });
    });
}

// 即时添加新消息到聊天记录
function addNewMessage(content) {
    const chatContainer = document.querySelector('.chat-container');
    if (!chatContainer) return;

    // 获取当前时间
    const now = new Date();
    const timeDisplay = now.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: false 
    });

    // 检查是否需要显示日期分割线
    const lastMessage = chatContainer.querySelector('.chat-message-out:last-child, .chat-message-in:last-child');
    let needDateSeparator = false;
    
    if (lastMessage) {
        const lastTimeElement = lastMessage.querySelector('.message-time');
        if (lastTimeElement) {
            // 简单判断：如果最后一条消息的时间不是今天，则显示日期分割线
            const lastTime = lastTimeElement.textContent;
            const currentTime = timeDisplay;
            // 这里简化处理，实际项目中可能需要更精确的日期比较
        }
    }

    // 检查与上一条消息的连续性
    const lastCurrentUserMessage = chatContainer.querySelector('.chat-message-out:last-child');
    const shouldShowAvatar = !lastCurrentUserMessage; // 简化逻辑：如果没有上一条当前用户消息，显示头像
    const shouldShowTime = true; // 新消息总是显示时间
    const spacingClass = lastMessage ? 'mt-4' : ''; // 如果有上一条消息，添加间距

    // 构建新消息HTML
    const messageHtml = `
        <div class="chat-message-out flex items-start gap-3 xs-400:gap-2 xs-310:gap-1 justify-end ${spacingClass}">
            <!-- 消息内容 -->
            <div class="message-content flex flex-col items-end max-w-[75%]">
                <div class="flex flex-col items-end space-y-1">
                    <div class="p-3 shadow-sm bg-gradient-to-br from-primary to-primary-dark text-white rounded-xl rounded-br-sm break-words break-all bubble-right">
                        <div class="text-base leading-relaxed">${escapeHtml(content)}</div>
                    </div>
                    ${shouldShowTime ? `<div class="text-xs text-text-secondary mt-1 mr-1 message-time">${timeDisplay}</div>` : ''}
                </div>
            </div>
            <!-- 头像或占位空间 -->
            ${shouldShowAvatar ?
                `<a href="/bbs/userinfo.aspx?touserid=${getCurrentUserId()}" class="user-avatar-link block">
                    <div class="user-avatar w-10 h-10 rounded-full bg-gray-150 flex items-center justify-center flex-shrink-0 shadow-sm hover:shadow-md hover:bg-gray-300 transition-all duration-200 cursor-pointer relative">
                        <!-- 首字母fallback -->
                        <span class="avatar-fallback-main text-lg font-semibold text-primary-dark" data-fallback="true">我</span>
                        <!-- 当前用户头像可以后续扩展 -->
                    </div>
                </a>` :
                `<div class="w-10 h-10 flex-shrink-0"></div>`
            }
        </div>
    `;

    // 添加新消息到聊天容器
    chatContainer.insertAdjacentHTML('beforeend', messageHtml);

    // ✅ 初始化新添加消息的头像
    if (typeof AvatarHandler !== 'undefined') {
        AvatarHandler.initPageAvatars();
    }

    // ✅ 为新添加的消息初始化图片预览功能
    initImagePreview();

    // 确保 DOM 更新完成后再滚动，使用双重 requestAnimationFrame 确保可靠性
    requestAnimationFrame(() => {
        requestAnimationFrame(() => {
            scrollToBottom();
        });
    });
}

// HTML转义函数，防止XSS
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 获取当前用户ID的辅助函数
function getCurrentUserId() {
    try {
        const pageDataElement = document.getElementById('page-data');
        if (pageDataElement) {
            const pageData = JSON.parse(pageDataElement.textContent);
            return pageData.currentUserId || '';
        }
    } catch (error) {
        // 静默处理获取用户ID失败的情况
    }
    return '';
}

// 🔄 统一的页面初始化 - 优化锚点滚动体验
document.addEventListener('DOMContentLoaded', function() {
    // 初始化图片预览功能
    initImagePreview();

    // 🎯 使用新的统一锚点系统（替换原有的分散滚动机制）
    initUnifiedAnchorSystem();

    // 初始化手动加载功能
    initManualLoading();

    const messageForm = document.getElementById('message-form');
    const messageInput = document.getElementById('message-input');
    const sendButton = document.getElementById('send-button');

    // AJAX表单提交
    if (messageForm) {
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault(); // 阻止默认提交行为

            const formData = new FormData(messageForm);
            
            // 验证输入内容
            const content = formData.get('content');
            if (!content || !content.trim()) {
                showToast('消息内容不能为空');
                return;
            }

            // 添加AJAX标识
            formData.append('ajax', '1');
            
            // 动态填充缺失的字段值
            try {
                // 从页面数据获取信息
                const pageDataElement = document.getElementById('page-data');
                const pageData = pageDataElement ? JSON.parse(pageDataElement.textContent) : {};
                
                // 从URL获取参数
                const urlParams = new URLSearchParams(window.location.search);
                
                // 填充 toid (从URL的id参数)
                if (!formData.get('toid') || !formData.get('toid').trim()) {
                    const messageId = urlParams.get('id') || '';
                    formData.set('toid', messageId);
                }
                
                // 填充 touseridlist (从页面数据的partnerId)
                if (!formData.get('touseridlist') || !formData.get('touseridlist').trim()) {
                    const partnerId = pageData.partnerId || '';
                    formData.set('touseridlist', partnerId);
                }
            } catch (error) {
                console.error('填充表单字段出错:', error);
            }
            
            // 调试：输出表单数据
            console.log('📝 表单提交数据:');
            for (let [key, value] of formData.entries()) {
                console.log(`  ${key}: ${value}`);
            }

            // 显示发送中状态
            if (sendButton) {
                sendButton.disabled = true;
                sendButton.textContent = '发送中...';
            }

            // 使用正确的URL
            const actionUrl = '/bbs/messagelist_add.aspx';

            // 发送AJAX请求
            fetch(actionUrl, {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.text())
            .then(code => {
                code = code.trim();
                
                if (code === 'OK') {
                    showToast('发送成功');
                    
                    // 即时添加新消息到界面
                    addNewMessage(content);
                    
                    // 清空输入框
                    if (messageInput) {
                        messageInput.value = '';
                        messageInput.style.height = 'auto'; // 重置文本域高度
                    }
                    
                } else if (code === 'REPEAT') {
                    showToast('内容重复，请修改后再发');
                } else if (code === 'NULL') {
                    showToast('请完整填写表单');
                } else if (code === 'WAITING') {
                    showToast('操作过快，请稍后再试');
                } else if (code === 'PWERROR') {
                    showToast('密码错误，请重新输入');
                } else if (code === 'MAX1' || code === 'MAX') {
                    showToast('已达今日发信上限');
                } else if (code === 'LOCK') {
                    showToast('你已被加入黑名单');
                } else if (code === 'CANTSELF') {
                    showToast('不能给自己发消息');
                } else if (code === 'NOTEXSIT') {
                    showToast('用户ID不存在');
                } else {
                    showToast('发送失败，请重试');
                }
            })
            .catch(error => {
                console.error('发送消息出错:', error);
                showToast('发送失败，请重试');
            })
            .finally(() => {
                // 恢复按钮状态
                if (sendButton) {
                    sendButton.disabled = false;
                    sendButton.textContent = '发送';
                }
            });
        });
    }

    // 回车键发送（Ctrl+Enter）
    if (messageInput) {
        messageInput.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                if (messageForm) {
                    messageForm.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
                }
            }
        });
    }
});

// 显示Toast提示
function showToast(message) {
    // 确保message是字符串
    const displayMessage = typeof message === 'string' ? message : String(message);
    
    const toast = document.createElement('div');
    toast.textContent = displayMessage;
    toast.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 text-white py-2 px-4 rounded-lg z-[1001] shadow-lg';
    Object.assign(toast.style, { 
        opacity: '0', 
        transition: 'opacity 0.3s ease, transform 0.3s ease', 
        transform: 'translate(-50%, -20px)' 
    });
    document.body.appendChild(toast);

    setTimeout(() => Object.assign(toast.style, { 
        opacity: '1', 
        transform: 'translate(-50%, 0)' 
    }), 10);
    
    setTimeout(() => {
        Object.assign(toast.style, { 
            opacity: '0', 
            transform: 'translate(-50%, -20px)' 
        });
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 2000);
}

// 手动加载功能
function initManualLoading() {
    const loadOlderBtn = document.getElementById('load-older-btn');
    const loadOlderContainer = document.getElementById('load-older-container');
    const loadNewerBtn = document.getElementById('load-newer-btn');
    const loadNewerContainer = document.getElementById('load-newer-container');
    const chatContainer = document.querySelector('.chat-container');

    if (!chatContainer) {
        return;
    }

    let isLoading = false;

    // 加载更早消息按钮点击事件
    if (loadOlderBtn) {
        loadOlderBtn.addEventListener('click', async function() {
            // 用户开始浏览历史，锁定锚点自动滚动
            window.anchorAutoScrollLocked = true;
            if (isLoading) return;

            isLoading = true;
            loadOlderBtn.textContent = '加载中...';
            loadOlderBtn.disabled = true;

            try {
                await loadMoreMessages('up');
            } catch (error) {
                console.error('加载失败:', error);
                showToast('加载失败，请重试');
            } finally {
                isLoading = false;
            }
        });
    }

    // 加载更新消息按钮点击事件
    if (loadNewerBtn) {
        loadNewerBtn.addEventListener('click', async function() {
            // 用户开始浏览历史，锁定锚点自动滚动
            window.anchorAutoScrollLocked = true;
            if (isLoading) return;

            isLoading = true;
            loadNewerBtn.textContent = '加载中...';
            loadNewerBtn.disabled = true;

            try {
                await loadMoreMessages('down');
            } catch (error) {
                console.error('加载失败:', error);
                showToast('加载失败，请重试');
            } finally {
                isLoading = false;
            }
        });
    }

    // 加载更多消息的核心函数
    async function loadMoreMessages(direction = 'up') {
        // 根据方向获取锚点消息ID
        let lastMessageId;
        if (direction === 'up') {
            // 向上加载：获取第一条消息的ID
            const firstMessage = chatContainer.querySelector('[data-message-id]');
            if (!firstMessage) {
                showToast('没有找到消息');
                return;
            }
            lastMessageId = firstMessage.getAttribute('data-message-id');
        } else {
            // 向下加载：获取最后一条消息的ID
            const allMessages = chatContainer.querySelectorAll('[data-message-id]');
            if (allMessages.length === 0) {
                showToast('没有找到消息');
                return;
            }
            lastMessageId = allMessages[allMessages.length - 1].getAttribute('data-message-id');
        }

        const partnerId = getPartnerIdFromPage();

        // 发送AJAX请求
        const response = await fetch(window.location.pathname + window.location.search, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'loadMore',
                direction: direction,
                lastMessageId: lastMessageId,
                partnerId: partnerId
            })
        });

        const data = await response.json();

        if (data.success && data.messages.length > 0) {
            if (direction === 'up') {
                // 向上加载：记录当前滚动位置
                const scrollTop = window.pageYOffset;
                const scrollHeight = document.documentElement.scrollHeight;

                // 渲染新消息到顶部
                renderNewMessages(data.messages, 'up');

                // 恢复滚动位置（补偿新内容的高度）
                const newScrollHeight = document.documentElement.scrollHeight;
                const heightDiff = newScrollHeight - scrollHeight;
                window.scrollTo(0, scrollTop + heightDiff);

                // 处理按钮状态
                if (data.hasMore) {
                    loadOlderBtn.textContent = '查看更早消息';
                    loadOlderBtn.disabled = false;
                } else {
                    // 往前加载完毕：显示分割线指示器
                    showTopIndicator();
                    loadOlderContainer.style.display = 'none';
                }
            } else {
                // 向下加载：直接添加到底部
                renderNewMessages(data.messages, 'down');

                // 处理按钮状态
                if (data.hasMore) {
                    loadNewerBtn.textContent = '查看后续消息';
                    loadNewerBtn.disabled = false;
                } else {
                    loadNewerContainer.style.display = 'none';
                    showBottomToast('已加载至最新消息');
                }
            }
        } else {
            // 没有更多消息
            if (direction === 'up') {
                // 往前加载完毕：显示分割线指示器
                showTopIndicator();
                loadOlderContainer.style.display = 'none';
            } else {
                loadNewerContainer.style.display = 'none';
                showBottomToast('已加载至最新消息');
            }
        }
    }

    // 渲染新消息
    function renderNewMessages(messages, direction = 'up') {
        let allHtml = '';
        const existingDates = new Set();

        // 获取已存在的日期标签
        const dateElements = chatContainer.querySelectorAll('.date-separator span');
        dateElements.forEach(el => {
            const dateText = el.textContent.trim();
            if (dateText.includes('月') && dateText.includes('日')) {
                existingDates.add(dateText);
            }
        });

        messages.forEach((msg, index) => {
            // 解析消息日期
            const messageDate = new Date(msg.addTime);
            const dateString = formatDateString(messageDate);

            // 检查是否需要添加日期分隔符
            const needDateSeparator = index === 0 || !existingDates.has(dateString);
            if (needDateSeparator) {
                existingDates.add(dateString);
            }

            // 创建日期分隔符HTML
            const dateSeparatorHtml = needDateSeparator ? createDateSeparatorHtml(dateString) : '';

            // 创建消息HTML
            const messageHtml = createMessageHtml(msg);

            // 组合HTML
            allHtml += dateSeparatorHtml + messageHtml;
        });

        if (allHtml) {
            if (direction === 'up') {
                // 向上加载：插入到加载更早消息按钮之后
                const loadOlderContainer = document.getElementById('load-older-container');
                if (loadOlderContainer) {
                    loadOlderContainer.insertAdjacentHTML('afterend', allHtml);
                }
            } else {
                // 向下加载：插入到加载更新消息按钮之前
                const loadNewerContainer = document.getElementById('load-newer-container');
                if (loadNewerContainer) {
                    loadNewerContainer.insertAdjacentHTML('beforebegin', allHtml);
                } else {
                    // 如果没有加载更新消息按钮，直接添加到聊天容器末尾
                    chatContainer.insertAdjacentHTML('beforeend', allHtml);
                }
            }

            // ✅ 动态加载完成后，初始化新添加的头像
            if (typeof AvatarHandler !== 'undefined') {
                AvatarHandler.initPageAvatars();
            }

            // ✅ 为动态加载的消息初始化图片预览功能
            initImagePreview();

            // ✅ 为动态加载的消息重新初始化HyperLink功能
            reinitializeHyperLinks();
        }
    }
}

// 🛠️ 动态加载相关的辅助函数
// 格式化日期字符串 - 当年不显示年份，非当年显示年份
function formatDateString(date) {
    const currentYear = new Date().getFullYear();
    const messageYear = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    // 如果是当年消息，不显示年份保持简洁
    if (messageYear === currentYear) {
        return `${month}月${day}日`;
    } else {
        // 非当年消息，显示年份
        return `${messageYear}年${month}月${day}日`;
    }
}

// 创建日期分隔符HTML
function createDateSeparatorHtml(dateString) {
    return `
        <div class="date-separator text-center my-4">
            <span class="text-xs text-text-secondary bg-gray-200 px-3 py-1 rounded-full">
                ${dateString}
            </span>
        </div>
    `;
}

// 创建消息HTML
function createMessageHtml(msg) {
        const isFromCurrentUser = msg.isFromCurrentUser;
        const messageClass = isFromCurrentUser ? 'chat-message-out' : 'chat-message-in';
        const justifyClass = isFromCurrentUser ? 'justify-end' : '';
        const bgClass = isFromCurrentUser ? 'bg-gradient-to-br from-primary to-primary-dark text-white' : 'bg-gray-100 text-black';
        const roundedClass = isFromCurrentUser ? 'rounded-br-sm' : 'rounded-bl-sm';
        const timeClass = isFromCurrentUser ? 'mr-1' : 'ml-1';
        const bubbleClass = isFromCurrentUser ? 'bubble-right' : 'bubble-left';

        return `
            <div class="${messageClass} flex items-start gap-3 xs-400:gap-2 xs-310:gap-1 ${justifyClass} mt-4" data-message-id="${msg.id}">
                ${!isFromCurrentUser ? `
                    <a href="/bbs/userinfo.aspx?touserid=${msg.senderId}" class="user-avatar-link block">
                        <div class="user-avatar w-10 h-10 rounded-full bg-gray-150 flex items-center justify-center flex-shrink-0 shadow-sm hover:shadow-md hover:bg-gray-300 transition-all duration-200 cursor-pointer relative">
                            <!-- 首字母fallback -->
                            <span class="avatar-fallback-small text-lg font-semibold text-primary-dark" data-fallback="true">${msg.senderFirstChar || msg.senderNickname.charAt(0)}</span>
                            <!-- 头像图片 -->
                            ${!msg.senderIsDefaultAvatar && msg.senderAvatarUrl ? `
                                <img src="${msg.senderAvatarUrl}"
                                     alt="${msg.senderNickname}的头像"
                                     class="w-10 h-10 object-fill absolute top-0 left-0 z-[1] hidden rounded-full"
                                     data-avatar-src="${msg.senderAvatarUrl}">
                            ` : ''}
                        </div>
                    </a>
                ` : ''}
                <div class="message-content flex flex-col ${isFromCurrentUser ? 'items-end' : ''} max-w-[75%]">
                    <div class="flex flex-col ${isFromCurrentUser ? 'items-end' : 'items-start'} space-y-1">
                        <div class="p-3 shadow-sm ${bgClass} rounded-xl ${roundedClass} break-words break-all ${bubbleClass}">
                            <div class="text-base leading-relaxed">${msg.processedContent}</div>
                        </div>
                        <div class="text-xs text-text-secondary mt-1 ${timeClass} message-time">${msg.timeDisplay}</div>
                    </div>
                </div>
                ${isFromCurrentUser ? `
                    <a href="/bbs/userinfo.aspx?touserid=${getCurrentUserId()}" class="user-avatar-link block">
                        <div class="user-avatar w-10 h-10 rounded-full bg-gray-150 flex items-center justify-center flex-shrink-0 shadow-sm hover:shadow-md hover:bg-gray-300 transition-all duration-200 cursor-pointer relative">
                            <!-- 首字母fallback -->
                            <span class="avatar-fallback-main text-lg font-semibold text-primary-dark" data-fallback="true">我</span>
                            <!-- 当前用户头像可以后续扩展 -->
                        </div>
                    </a>
                ` : ''}
            </div>
        `;
    }

// 显示顶部"已到达对话开头"指示器
function showTopIndicator() {
    // 检查是否已经存在指示器
    const existingIndicator = document.getElementById('top-indicator');
    if (existingIndicator) {
        return; // 已存在，不重复添加
    }

    // 创建分割线样式的指示器
    const indicatorHtml = `
        <div id="top-indicator" class="py-4">
            <div class="relative flex py-1 items-center">
                <div class="flex-grow border-t border-gray-200"></div>
                <span class="flex-shrink mx-4 text-xs text-text-secondary">已到达对话开头</span>
                <div class="flex-grow border-t border-gray-200"></div>
            </div>
        </div>
    `;

    // 插入到聊天容器的最顶部
    const chatContainer = document.querySelector('.chat-container');
    if (chatContainer) {
        chatContainer.insertAdjacentHTML('afterbegin', indicatorHtml);
    }
}

// 显示底部Toast提示（在固定输入框上方）
function showBottomToast(message) {
    // 检查是否已存在底部Toast，避免重复显示
    const existingToast = document.getElementById('bottom-toast');
    if (existingToast) {
        existingToast.remove();
    }

    // 创建Toast元素
    const toast = document.createElement('div');
    toast.id = 'bottom-toast';
    toast.textContent = message;

    // 设置样式：固定在输入框上方
    toast.className = 'fixed left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 text-white py-2 px-4 rounded-lg shadow-lg z-[1001] max-w-[720px] mx-auto';

    // 计算位置：输入框上方留出间距
    const footer = document.querySelector('footer');
    const footerHeight = footer ? footer.offsetHeight : 80; // 默认80px
    toast.style.bottom = `${footerHeight + 16}px`; // 输入框上方16px间距

    // 初始状态：透明且稍微向下偏移
    Object.assign(toast.style, {
        opacity: '0',
        transition: 'opacity 0.3s ease, transform 0.3s ease',
        transform: 'translate(-50%, 20px)'
    });

    // 添加到页面
    document.body.appendChild(toast);

    // 显示动画
    setTimeout(() => {
        Object.assign(toast.style, {
            opacity: '1',
            transform: 'translate(-50%, 0)'
        });
    }, 10);

    // 自动消失
    setTimeout(() => {
        Object.assign(toast.style, {
            opacity: '0',
            transform: 'translate(-50%, 20px)'
        });

        // 动画完成后移除元素
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000); // 3秒后开始消失动画
}

// 从页面获取对话伙伴ID
function getPartnerIdFromPage() {
    try {
        const pageDataElement = document.getElementById('page-data');
        if (pageDataElement) {
            const pageData = JSON.parse(pageDataElement.textContent);
            return pageData.partnerId;
        }
            } catch (error) {
            // 静默处理获取页面数据失败的情况
        }
    return '0';
}

</script>

<!-- 消息气泡中链接的差异化样式 -->
<style>
/* 左边气泡（对方消息）中的链接样式 - 红色系 */
.bubble-left a {
    color: #dc2626;
    transition: all 0.2s ease;
    text-decoration: none;
    border-bottom: 1px solid transparent;
}

.bubble-left a:hover {
    color: #b91c1c;
    border-bottom-color: #b91c1c;
}

/* 右边气泡（当前用户消息）中的链接样式 - 淡黄色系 */
.bubble-right a {
    color: #fef3c7;
    transition: all 0.2s ease;
    text-decoration: none;
    border-bottom: 1px solid transparent;
}

.bubble-right a:hover {
    color: #fde68a;
    border-bottom-color: #fde68a;
}
</style>

<!-- 引入HyperLink.js脚本，用于处理链接转换和优化 -->
<script src="/NetCSS/JS/HyperLink.js" defer></script>
<!-- 引入AvatarHandler.js脚本，用于处理头像加载 -->
<script src="/Template/JS/Components/AvatarHandler.js?v=3"></script>

<script>
// 自定义确认对话框函数 - 与FriendList.hbs完全一致
function showCustomConfirm(message, onConfirm) {
    const confirmDialogOverlay = document.createElement('div');
    confirmDialogOverlay.style.cssText = `
        position: fixed; inset: 0; background-color: rgba(0,0,0,0.5);
        display: flex; align-items: center; justify-content: center;
        z-index: 1030;
    `;

    const confirmDialogContent = document.createElement('div');
    confirmDialogContent.style.cssText = `
        background-color: white;
        border-radius: 12px;
        padding: 24px 20px 20px;
        width: 85%; max-width: 360px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        text-align: center;
    `;

    confirmDialogContent.innerHTML = `
        <h3 style="font-size: 18px; font-weight: 600; color: #1f2937; margin-bottom: 12px;">确认操作</h3>
        <p style="color: #6b7280; margin-bottom: 24px; font-size: 15px; line-height: 1.5;">${message}</p>
        <div style="display: flex; justify-content: center; gap: 12px;">
            <button class="custom-confirm-btn custom-confirm-delete" id="confirmCustomConfirm">确定</button>
            <button class="custom-confirm-btn custom-confirm-cancel" id="cancelCustomConfirm">取消</button>
        </div>
    `;

    confirmDialogOverlay.appendChild(confirmDialogContent);
    document.body.appendChild(confirmDialogOverlay);

    // 关闭对话框函数
    function closeDialog() {
        if (document.body.contains(confirmDialogOverlay)) {
            document.body.removeChild(confirmDialogOverlay);
        }
    }

    const cancelBtn = document.getElementById('cancelCustomConfirm');
    if(cancelBtn) cancelBtn.onclick = closeDialog;

    const confirmBtn = document.getElementById('confirmCustomConfirm');
    if(confirmBtn) confirmBtn.onclick = () => {
        onConfirm();
        closeDialog();
    };

    // 点击遮罩关闭 - 只有点击遮罩本身才关闭，点击内容区域不关闭
    confirmDialogOverlay.addEventListener('click', function(e) {
        if (e.target === confirmDialogOverlay) {
            closeDialog();
        }
    });
}

// 显示删除对话确认弹窗 - 使用标准的showCustomConfirm函数
function showDeleteConversationConfirm() {
    const partnerName = '{{ConversationPartner.Nickname}}';
    const message = `确定要删除与 ${partnerName} 的所有对话记录吗？`;
    
    showCustomConfirm(message, function() {
        const pageDataElement = document.getElementById('page-data');
        const pageData = pageDataElement ? JSON.parse(pageDataElement.textContent) : {};
        const partnerId = pageData.partnerId;
        
        if (!partnerId) {
            showToast('无法获取对话伙伴信息');
            return;
        }
        
        // 构建删除URL - 使用MessageList_Del.aspx的godelother功能
        const urlParams = new URLSearchParams(window.location.search);
        const currentMessageId = urlParams.get('id') || '';
        const deleteUrl = `/bbs/messagelist_del.aspx?action=godelother&id=${currentMessageId}`;
        
        // 执行删除操作
        fetch(deleteUrl, { method: 'GET' })
        .then(res => res.text())
        .then(html => {
            if (html.includes('删除成功') || html.includes('Deleted successfully')) {
                showToast('删除成功！');
                
                // 延迟跳转回消息列表
                setTimeout(() => {
                    window.location.href = '/bbs/messagelist.aspx';
                }, 1000);
            } else {
                showToast('删除失败，请重试！');
            }
        })
        .catch(() => {
            showToast('删除失败，请重试！');
        });
    });
}

// 🔗 重新初始化HyperLink功能（极简版）
function reinitializeHyperLinks() {
    try {
        // 手动执行HyperLink.js的核心处理逻辑，避免重复加载脚本
        setTimeout(() => {
            // 只处理新加载的消息内容
            const newElements = document.querySelectorAll('.bubble:not([data-hyperlink-init]), .bubble-left:not([data-hyperlink-init]), .bubble-right:not([data-hyperlink-init])');

            newElements.forEach(element => {
                // 标记为已处理
                element.setAttribute('data-hyperlink-init', 'true');

                // 执行HyperLink的文本处理逻辑
                if (typeof processTextContent === 'function') {
                    processTextContent(element);
                }
            });

            // 处理新的a标签
            const newLinks = document.querySelectorAll('.bubble a:not([data-link-init]), .bubble-left a:not([data-link-init]), .bubble-right a:not([data-link-init])');
            newLinks.forEach(link => {
                link.setAttribute('data-link-init', 'true');
            });

            // 重新执行链接转换
            if (typeof convertJdLinksInATags === 'function') convertJdLinksInATags();
            if (typeof convertTbLinksInATags === 'function') convertTbLinksInATags();
            if (typeof convertPddLinksInATags === 'function') convertPddLinksInATags();
            if (typeof convertMtLinksInATags === 'function') convertMtLinksInATags();

        }, 100);
    } catch (error) {
        console.warn('重新初始化HyperLink功能时出错:', error);
    }
}
</script>