{"config": {"version": "2.0", "lastUpdated": "2025-06-19T00:00:00Z", "description": "OAuth 2.0 配置文件 - 第二阶段标准实现版本", "author": "系统管理员", "environment": "development"}, "tokenSettings": {"authorizationCodeLifetimeMinutes": 10, "accessTokenLifetimeHours": 24, "refreshTokenLifetimeDays": 30, "defaultScope": "profile", "supportedScopes": ["profile", "email"], "supportedGrantTypes": ["authorization_code"], "requirePKCE": true}, "securitySettings": {"requireHttps": false, "allowLocalhostHttp": true, "appKeySalt": "YourSecureRandomSalt_ChangeInProduction_2025_v2", "enableTokenFingerprinting": false, "maxRedirectUris": 10}, "endpointSettings": {"authorizationEndpoint": "/OAuth/Authorize.aspx", "tokenEndpoint": "/OAuth/Token.aspx", "revokeEndpoint": "/OAuth/Revoke.aspx"}, "corsSettings": {"allowedOrigins": ["*"], "allowedMethods": ["GET", "POST"], "allowedHeaders": ["Content-Type", "Authorization"]}, "tokenManagementSettings": {"mode": "aggressive", "enableActivityLog": true}, "loggingSettings": {"enabled": true, "logPath": "../Logs/OAuth", "logFileNamePattern": "oauth-.log", "rollingInterval": "Day", "retainedFileCountLimit": 30, "useLocalTime": true, "logLevel": "Information"}}