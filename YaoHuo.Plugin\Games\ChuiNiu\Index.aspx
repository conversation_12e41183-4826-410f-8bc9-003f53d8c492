﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Index.aspx.cs" Inherits="YaoHuo.Plugin.Games.ChuiNiu.Index" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    // 添加必要的CSS和JS
    string headHtml = "<meta charset=\"UTF-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\">\n<link rel=\"stylesheet\" href=\"/games/chuiniu/tailwind.min.css?2\"/> <link rel=\"stylesheet\" href=\"/games/chuiniu/styles.css?25\"/>\n<link rel=\"stylesheet\" href=\"//lf6-cdn-tos.bytecdntp.com/cdn/expire-1-y/font-awesome/6.0.0/css/all.min.css\"/>";
    Response.Write(WapTool.showTop("吹牛大厅", wmlVo, false, headHtml));
    
    //会员可见
    if (this.IsCheckManagerLvl("|00|01|02|03|04|", "") == true)
    {
        //显示广告
        if (adVo != null && adVo.threeShowTop != "")
        {
            strhtml.Append(adVo.threeShowTop);
        }
        // Header
        strhtml.Append("<header class=\"bg-gradient-to-r from-teal-500 to-teal-700 shadow-md p-4 sticky top-0 z-10 text-white\" style=\"position:relative;\">");
        strhtml.Append("<a href=\"/\" class=\"text-white mr-4\" style=\"position:absolute;left:16px;top:50%;transform:translateY(-50%);\">");
        strhtml.Append("<i class=\"fas fa-arrow-left\"></i>");
        strhtml.Append("</a>");
        strhtml.Append("<h1 class=\"text-xl font-bold\" style=\"margin:0 auto;text-align:center;\">");
        strhtml.Append("<i class=\"fas fa-comments-dollar mr-2\"></i>");
        strhtml.Append("吹牛大厅");
        strhtml.Append("</h1>");
        strhtml.Append("</header>");
        
        // Main Content
        strhtml.Append("<main class=\"p-4 pb-12 max-w-lg mx-auto\">");
        
        // User Info/Stats Card
        strhtml.Append("<section class=\"bg-white rounded-xl shadow-sm p-5 mb-6 border border-gray-100\">");
        strhtml.Append("<div class=\"flex justify-between items-center mb-4\">");
        strhtml.Append("<div class=\"flex items-center\">");
        strhtml.Append("<div class=\"bg-gradient-to-r from-blue-400 to-blue-600 text-white rounded-full w-12 h-12 flex items-center justify-center mr-3 shadow-sm\">");
        strhtml.Append("<i class=\"fas fa-user text-2xl\"></i>");
        strhtml.Append("</div>");
        strhtml.Append("<div>");
        strhtml.Append("<a href=\"" + this.http_start + "bbs/userinfo.aspx?touserid=" + this.userid + "\" class=\"font-bold text-gray-800\">" + userVo.nickname + "</a>");
        strhtml.Append("<div class=\"flex items-center text-sm text-gray-600\">");
        strhtml.Append("<span class=\"font-semibold text-amber-600 mr-1\">" + userVo.money.ToString("N0") + "</span>");
        strhtml.Append("<span class=\"coin-icon\">");
        strhtml.Append("<i class=\"fas fa-coins text-xs\"></i>");
        strhtml.Append("</span>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/book_list.aspx?type=0&amp;touserid=" + this.userid + "\" class=\"bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-full text-sm font-medium transition-all flex items-center\">");
        strhtml.Append("<i class=\"fas fa-history mr-1\"></i>");
        strhtml.Append("我的记录");
        strhtml.Append("</a>");
        strhtml.Append("</div>");
        strhtml.Append("<div class=\"grid grid-cols-2 gap-4 text-sm bg-gray-50 p-3 rounded-lg\">");
        strhtml.Append("<div class=\"flex flex-col items-center\">");
        strhtml.Append("<p class=\"text-gray-500 mb-1\">今日已吹</p>");
        strhtml.Append("<p class=\"font-bold text-lg text-teal-600\">" + configVo.todayTimes + "<span class=\"text-xs font-normal text-gray-500 ml-1\">次</span></p>");
        strhtml.Append("</div>");
        strhtml.Append("<div class=\"flex flex-col items-center border-l border-gray-200\">");
        strhtml.Append("<p class=\"text-gray-500 mb-1\">今日流水</p>");
        strhtml.Append("<p class=\"font-bold text-lg text-amber-600 flex items-center\">");
        strhtml.Append(Convert.ToInt64(configVo.todayMoney).ToString("N0"));
        strhtml.Append("<span class=\"coin-icon ml-1\">");
        strhtml.Append("<i class=\"fas fa-coins text-xs\"></i>");
        strhtml.Append("</span>");
        strhtml.Append("</p>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("</section>");

        // Create Challenge Button
        strhtml.Append("<section class=\"mb-6\">");
        strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/add.aspx\" class=\"block w-full text-center py-4 px-4 rounded-xl gradient-btn font-bold shadow-sm text-white transition-all\">");
        strhtml.Append("<i class=\"fas fa-plus-circle mr-2\"></i>发起新的挑战");
        strhtml.Append("</a>");
        strhtml.Append("</section>");

        // Ongoing Challenges List
        strhtml.Append("<section class=\"mb-6\">");
        strhtml.Append("<div class=\"flex justify-between items-center mb-3\">");
        strhtml.Append("<h2 class=\"text-lg font-bold text-gray-800 flex items-center\">");
        strhtml.Append("<i class=\"fas fa-fire-alt text-amber-500 mr-2\"></i>");
        strhtml.Append("进行中的挑战");
        strhtml.Append("</h2>");
        if (listVo != null)
        {
            strhtml.Append("<span class=\"bg-amber-100 text-amber-800 text-xs px-2 py-1 rounded-full font-medium\">" + listVo.Count + "个挑战</span>");
        }
        else
        {
            //strhtml.Append("<span class=\"bg-amber-100 text-amber-800 text-xs px-2 py-1 rounded-full font-medium\">0个挑战</span>");
        }
        strhtml.Append("</div>");
        
        // 挑战列表容器
        strhtml.Append("<div id=\"challenges-list-container\" class=\"bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden divide-y divide-gray-100\">");
        
        // 循环生成挑战项
        if (listVo != null && listVo.Count > 0)
        {
            for (int i = 0; i < listVo.Count; i++)
            {
                strhtml.Append("<div class=\"challenge-item p-4 hover:bg-gray-50\">");
                strhtml.Append("<div class=\"flex justify-between items-start gap-2 mb-2\">");
                strhtml.Append("<div class=\"min-w-0 flex-1\">");
                strhtml.Append("<p class=\"font-bold text-gray-800 flex items-center\">");
                strhtml.Append("<span class=\"truncate mr-1\">" + listVo[i].Question + "</span> ");
                strhtml.Append("<span class=\"inline-flex items-center text-sm font-medium text-amber-600 flex-shrink-0\">");
                strhtml.Append(Convert.ToInt64(listVo[i].myMoney).ToString("N0") + " <i class=\"fas fa-coins text-amber-500 ml-1\"></i>");
                strhtml.Append("</span>");
                strhtml.Append("</p>");
                strhtml.Append("<div class=\"flex items-center mt-1 text-sm text-gray-600\">");
                strhtml.Append("<div class=\"bg-green-100 text-green-700 rounded-full w-5 h-5 flex items-center justify-center mr-1\">");
                strhtml.Append("<i class=\"fas fa-user text-xs\"></i>");
                strhtml.Append("</div>");
                // 将用户名称改为超链接形式，但保持文本颜色与原样式一致
                strhtml.Append("<a href=\"" + this.http_start + "bbs/userinfo.aspx?touserid=" + listVo[i].userid + "\" class=\"text-gray-600\">");
                strhtml.Append(listVo[i].nickName != null ? listVo[i].nickName : "用户");
                strhtml.Append("</a>");
                // 添加时间显示
                if (listVo[i].addtime != null)
                {
                    DateTime now = DateTime.Now;
                    TimeSpan diff = now - listVo[i].addtime;
                    string timeText = "";
                    
                    if (diff.TotalMinutes < 60)
                    {
                        timeText = Math.Max(1, (int)diff.TotalMinutes) + "分钟前";
                    }
                    else if (diff.TotalHours < 24)
                    {
                        timeText = (int)diff.TotalHours + "小时前";
                    }
                    else if (diff.TotalDays < 7)
                    {
                        timeText = (int)diff.TotalDays + "天前";
                    }
                    else if (diff.TotalDays < 30)
                    {
                        timeText = ((int)diff.TotalDays / 7) + "周前";
                    }
                    else
                    {
                        timeText = ((int)diff.TotalDays / 30) + "个月前";
                    }
                    
                    strhtml.Append("<span class=\"text-xs text-gray-400 ml-2\">" + timeText + "</span>");
                }
                strhtml.Append("</div>");
                strhtml.Append("</div>");
                
                // 检查是否是自己发起的挑战 - 修复类型不匹配问题
                if (listVo[i].userid.ToString() == this.userid)
                {
                    // 自己发起的挑战，显示"等待中"按钮
                    strhtml.Append("<div class=\"flex-shrink-0 py-1.5 px-3 rounded-full bg-gray-200 text-sm text-gray-600 font-medium shadow-sm flex items-center\">");
                    strhtml.Append("<i class=\"fas fa-hourglass-half mr-1\"></i>等待中");
                    strhtml.Append("</div>");
                }
                else
                {
                    // 别人发起的挑战，显示"去应战"按钮
                    strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/doit.aspx?id=" + listVo[i].id + "\" class=\"flex-shrink-0 py-1.5 px-3 rounded-full gradient-btn text-sm text-white font-medium shadow-sm\">");
                    strhtml.Append("<i class=\"fas fa-running mr-1\"></i>去应战");
                    strhtml.Append("</a>");
                }
                
                strhtml.Append("</div>");
                strhtml.Append("</div>");
            }
        }
        else
        {
            strhtml.Append("<div class=\"p-4 text-center text-gray-500\">暂无挑战记录</div>");
        }
        
        strhtml.Append("</div>"); // End of challenges-list-container
        
        // 判断是否需要显示"查看更多"按钮
        if (listVo != null && listVo.Count > 5)
        {
            strhtml.Append("<div class=\"text-center mt-3\">");
            strhtml.Append("<a href=\"#\" id=\"load-more-challenges\" class=\"inline-flex items-center text-sm text-teal-600 hover:text-teal-800 font-medium transition-colors\">");
            strhtml.Append("查看更多");
            strhtml.Append("<i class=\"fas fa-chevron-down ml-1 text-xs\"></i>");
            strhtml.Append("</a>");
            strhtml.Append("</div>");
        }
        
        strhtml.Append("</section>");

        // Game Chat
        if (showRe > 0)
        {
            strhtml.Append("<section class=\"mt-6 mb-6\">");
            strhtml.Append("<div class=\"flex justify-between items-center mb-3\">");
            strhtml.Append("<h2 class=\"text-lg font-bold text-gray-800 flex items-center\">");
            strhtml.Append("<i class=\"fas fa-comments text-teal-500 mr-2\"></i>");
            strhtml.Append("游戏闲聊");
            strhtml.Append("</h2>");
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"bg-white rounded-xl shadow-sm p-4 border border-gray-100\">");
            strhtml.Append("<div class=\"space-y-3 max-h-64 overflow-y-auto mb-4 py-1\" id=\"chat-messages-container\">");
            
            // 循环生成聊天消息 - 最早的消息在上方，最新的消息在下方
            if (relistVo != null && relistVo.Count > 0)
            {
                // 反转列表顺序，使最新的消息显示在底部
                for (int i = relistVo.Count - 1; i >= 0; i--)
                {
                    // 判断消息是否是当前用户发送的
                    bool isCurrentUser = relistVo[i].userid.ToString() == this.userid;
                    
                    // 计算相对时间，复用已有的相对时间计算逻辑
                    string relativeTime = "";
                    if (relistVo[i].addtime != null)
                    {
                        DateTime now = DateTime.Now;
                        TimeSpan diff = now - relistVo[i].addtime;
                        
                        if (diff.TotalMinutes < 1)
                        {
                            relativeTime = "刚刚";
                        }
                        else if (diff.TotalMinutes < 60)
                        {
                            relativeTime = Math.Max(1, (int)diff.TotalMinutes) + "分钟前";
                        }
                        else if (diff.TotalHours < 24)
                        {
                            relativeTime = (int)diff.TotalHours + "小时前";
                        }
                        else if (diff.TotalDays < 7)
                        {
                            relativeTime = (int)diff.TotalDays + "天前";
                        }
                        else if (diff.TotalDays < 30)
                        {
                            relativeTime = ((int)diff.TotalDays / 7) + "周前";
                        }
                        else
                        {
                            relativeTime = ((int)diff.TotalDays / 30) + "个月前";
                        }
                    }
                    
                    if (isCurrentUser)
                    {
                        // 自己发送的消息 - 右侧布局
                        strhtml.Append("<div class=\"flex items-start space-x-2 justify-end mb-3\">");
                        strhtml.Append("<div class=\"max-w-[75%] flex flex-col items-end\">");
                        strhtml.Append("<a href=\"" + this.http_start + "bbs/userinfo.aspx?touserid=" + relistVo[i].userid + "\" class=\"text-xs text-green-600 font-semibold mb-1 no-underline\">" + relistVo[i].nickName + "</a>");
                        strhtml.Append("<div class=\"bg-gray-100 p-2 text-sm rounded-xl rounded-tr-none break-all\">");
                        strhtml.Append(relistVo[i].content);
                        strhtml.Append("</div>");
                        strhtml.Append("<p class=\"text-xs text-gray-400 mt-1\">" + relativeTime + "</p>");
                        strhtml.Append("</div>");
                        strhtml.Append("<div class=\"bg-green-100 text-green-700 rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0\">");
                        strhtml.Append("<i class=\"fas fa-user text-xs\"></i>");
                        strhtml.Append("</div>");
                        strhtml.Append("</div>");
                    }
                    else
                    {
                        // 其他人发送的消息 - 左侧布局
                        strhtml.Append("<div class=\"flex items-start space-x-2 mb-3\">");
                        strhtml.Append("<div class=\"bg-blue-100 text-blue-700 rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0\">");
                        strhtml.Append("<i class=\"fas fa-user text-xs\"></i>");
                        strhtml.Append("</div>");
                        strhtml.Append("<div class=\"max-w-[75%] flex flex-col items-start\">");
                        strhtml.Append("<a href=\"" + this.http_start + "bbs/userinfo.aspx?touserid=" + relistVo[i].userid + "\" class=\"text-xs text-blue-600 font-semibold mb-1 no-underline\">" + relistVo[i].nickName + "</a>");
                        strhtml.Append("<div class=\"bg-gray-100 p-2 text-sm rounded-xl rounded-tl-none break-all\">");
                        strhtml.Append(relistVo[i].content);
                        strhtml.Append("</div>");
                        strhtml.Append("<p class=\"text-xs text-gray-400 mt-1\">" + relativeTime + "</p>");
                        strhtml.Append("</div>");
                        strhtml.Append("</div>");
                    }
                }
            }
            else
            {
                strhtml.Append("<div class=\"text-center text-gray-500 py-4\">暂无聊天记录！</div>");
            }
            
            strhtml.Append("</div>");
            
            // 聊天输入表单
            strhtml.Append("<form method=\"post\" action=\"" + http_start + "games/chat/book_re.aspx\" class=\"mt-3 chat-form\" autocomplete=\"off\">");
            strhtml.Append("<div class=\"chat-input-container\">");
            strhtml.Append("<input type=\"text\" name=\"content\" placeholder=\"参与闲聊...\" required=\"required\" class=\"chat-input\" autocomplete=\"off\">");
            strhtml.Append("<button type=\"submit\" class=\"chat-send-btn\">");
            strhtml.Append("<i class=\"fas fa-paper-plane\"></i>");
            strhtml.Append("</button>");
            strhtml.Append("</div>");
            strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"add\"/>");
            strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
            strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
            strhtml.Append("<input type=\"hidden\" name=\"nid\" value=\"chuiniu\"/>");
            strhtml.Append("</form>");
            strhtml.Append("</div>");
            strhtml.Append("</section>");
        }
        
        strhtml.Append("</main>");

        // Bottom Navigation
        strhtml.Append("<footer class=\"fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t border-gray-200 p-1 z-10\">");
        strhtml.Append("<nav class=\"flex justify-around max-w-lg mx-auto\">");
        strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/index.aspx\" class=\"text-center text-teal-600 p-2 flex-1\">");
        strhtml.Append("<i class=\"fas fa-comments-dollar block text-xl mb-1\"></i>");
        strhtml.Append("<span class=\"text-xs font-medium\">大厅</span>");
        strhtml.Append("</a>");
        strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/add.aspx\" class=\"text-center text-gray-500 hover:text-teal-600 p-2 flex-1 transition-colors\">");
        strhtml.Append("<i class=\"fas fa-plus-circle block text-xl mb-1\"></i>");
        strhtml.Append("<span class=\"text-xs font-medium\">发起</span>");
        strhtml.Append("</a>");
        strhtml.Append("<a href=\"" + this.http_start + "games/rank/book_list.aspx?nid=chuiniu&amp;type=0\" class=\"text-center text-gray-500 hover:text-teal-600 p-2 flex-1 transition-colors\">");
        strhtml.Append("<i class=\"fas fa-trophy block text-xl mb-1\"></i>");
        strhtml.Append("<span class=\"text-xs font-medium\">排行</span>");
        strhtml.Append("</a>");
        strhtml.Append("<a href=\"/\" class=\"text-center text-gray-500 hover:text-teal-600 p-2 flex-1 transition-colors\">");
        strhtml.Append("<i class=\"fas fa-home block text-xl mb-1\"></i>");
        strhtml.Append("<span class=\"text-xs font-medium\">首页</span>");
        strhtml.Append("</a>");
        strhtml.Append("</nav>");
        strhtml.Append("</footer>");
        //显示广告
        if (adVo != null && adVo.threeShowDown != "")
        {
            strhtml.Append(adVo.threeShowDown);
        }
        
        // 管理员功能链接
        if (base.IsCheckManagerLvl("|00|01|", ""))
        {
            //strhtml.Append("<div class=\"p-4 text-center text-sm text-gray-500\">");
            //strhtml.Append("【<a href=\"" + this.http_start + "games/chuiniu/classconfigall.aspx?backtype=wap&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "\" class=\"text-teal-600\">参数属性配置</a>】");
            //strhtml.Append("</div>");
        }
    }
    else
    {
        strhtml.Append("<div class=\"p-10 text-center\">");
        strhtml.Append("<div class=\"p-5 bg-white rounded-lg shadow-md\">");
        strhtml.Append("<i class=\"fas fa-exclamation-triangle text-amber-500 text-3xl mb-3\"></i>");
        strhtml.Append("<p class=\"text-lg font-medium text-gray-800 mb-4\">您需要登录才能访问此页面</p>");
        strhtml.Append("<a href=\"" + this.http_start + "waplogin.aspx?siteid=" + this.siteid + "&amp;classid=" + this.classid + "\" class=\"inline-block py-2 px-4 gradient-btn rounded-lg text-white font-medium shadow-sm\">");
        strhtml.Append("立即登录");
        strhtml.Append("</a>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
    }
    
    // 结束HTML
    strhtml.Append("<script src='main.js'></script>");
    strhtml.Append("<script src=\"./ChatFastPost.js?18\"></script>");
    strhtml.Append("</body>");
    strhtml.Append("</html>");
    
    // 直接输出HTML，不使用WapTool.ToWML
    Response.Write(strhtml.ToString());
    // 不需要显示底部信息
    // Response.Write(WapTool.showDown(wmlVo));
%>