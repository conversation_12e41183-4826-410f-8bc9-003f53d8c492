﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_Search_New.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Book_Search_New" %>
<%
    bool isIframeMode = Request.QueryString["iframe"] == "true";
    string bodyClass = isIframeMode ? "iframe-mode" : "";
    string h1Html = isIframeMode ? "" : "<h1>论坛搜索</h1>";
%>
<html lang="zh-cn">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="UTF-8">
    <title>论坛搜索</title>
    <link href="/NetCSS/BookView/SimpleSearch.css" rel="stylesheet" type="text/css"/>
</head>
<body class="<%= bodyClass %>">
    <div class="container">
        <%= h1Html %>
        <div class="search-tabs">
            <button type="button" class="search-tab-button active" data-search-type="title">标题</button>
            <button type="button" class="search-tab-button" data-search-type="content">内容</button>
            <button type="button" class="search-tab-button" data-search-type="author">作者</button>
            <button type="button" class="search-tab-button" data-search-type="userid">ID号</button>
        </div>
        <form class="search-form" onsubmit="return generateSearchUrl();">
            <input id="search" autocomplete="off" name="search" type="text" placeholder="请输入帖子标题" minlength="1" required>
            <button id="searchButton" type="submit">搜索</button>
        </form>
    </div>
    <script type="text/javascript">
        var selectedSearchType = 'title'; // Default search type
        var searchInputElement = document.getElementById("search");

        function generateSearchUrl() {
            var searchKeyword = searchInputElement.value;

            var targetUrl;
            if (selectedSearchType === 'userid') {
                // ID号搜索使用不同的URL
                targetUrl = "/search/book_list.aspx?touserid=" + encodeURIComponent(searchKeyword);
            } else {
                // 其他搜索类型使用原有URL
                targetUrl = "/bbs/book_list_search.aspx?action=search&classid=0&type=" + selectedSearchType + "&key=" + encodeURIComponent(searchKeyword);
            }

            try {
                if (window.top && window.top.location.href !== window.location.href) {
                    window.top.location.href = targetUrl;
                } else {
                    window.location.href = targetUrl;
                }
            } catch (e) {
                window.location.href = targetUrl;
            }
            return false;
        }

        var searchTabButtons = document.querySelectorAll('.search-tab-button');
        if (searchTabButtons.length > 0 && searchInputElement) {
            searchTabButtons.forEach(function (button) {
                button.addEventListener('click', function () {
                    searchTabButtons.forEach(function (btn) { btn.classList.remove('active'); });
                    this.classList.add('active');
                    selectedSearchType = this.getAttribute('data-search-type');

                    if (selectedSearchType === 'title') {
                        searchInputElement.placeholder = "请输入帖子标题";
                    } else if (selectedSearchType === 'content') {
                        searchInputElement.placeholder = "请输入帖子内容";
                    } else if (selectedSearchType === 'author') {
                        searchInputElement.placeholder = "请输入作者昵称";
                    } else if (selectedSearchType === 'userid') {
                        searchInputElement.placeholder = "请输入用户ID号";
                    }
                    searchInputElement.type = "text"; // All types are text now
                });
            });
        }

        var containerElement = document.querySelector(".container");
        if (searchInputElement && containerElement) {
            searchInputElement.addEventListener("focus", function () {
                containerElement.classList.add("active");
            });
            searchInputElement.addEventListener("blur", function () {
                containerElement.classList.remove("active");
            });
        }
    </script>
</body>
</html>