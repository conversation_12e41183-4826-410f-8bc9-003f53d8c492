/*下拉选择表情*/
.centered-container,.kuaisuhuifu{padding-left:7px;} .centered-container {margin-top: -2px;padding-right: 7px;} .newselect * { position: relative; margin: 0; padding: 0; box-sizing: border-box; font-weight: 300; } .newselect button, .newselect input, .newselect optgroup, .newselect select, .newselect textarea  { color: inherit; font: inherit; margin: 0; background: transparent; outline: none; border: none; border-radius: 0; -webkit-appearance: none; -moz-appearance: none; appearance: none; } .newselect ul, .newselect ol, .newselect menu { list-style: none; } .ulselect { margin-left: 8px;margin-bottom: -4px;display: inline-block;width: 75px; height: 18px; cursor: pointer; background-color: white; border-radius: 7px; } .select_expand { width: 0; height: 18px; position: absolute; top: 0; right: 0; } .select_expand::after { font-family: sans-serif;content: '\003E'; position: absolute; top: 50%; right: 0; transform: translate(-50%, -50%) rotate(90deg) scaleY(1.75); color: #3e3e3e; font-size: 12px; pointer-events: none; z-index: 2; opacity: 0.7; } .select_expand:hover::after { opacity: 1; } .select_expand:checked::after { transform: translate(-50%, -50%) rotate(90deg) scaleX(-1) scaleY(1.75); } .select_expandLabel { display: block; width: 100%; height: 18px; position: absolute; top: 0; left: 0; cursor: pointer; border-radius: 7px; } .select_close {display: none;} .select_closeLabel { width: 100vw; height: 100vh; position: fixed; top: 0; left: 0; display: none; } .select_items { z-index: 90;width: 100%; position: absolute; top: 0; left: 0; border-radius: 7px; padding-top: 18px; border: 1px solid #d4d4d4; } .select_input {display: none;} .select_label { display: block; height: 0; font-size: 12px; line-height: 18px; overflow: hidden; color: #3e3e3e; background-color: #fff; cursor: pointer; padding-left: 8px; } .select_option .select_label { border-radius: 0; } .select_option:last-child .select_label { border-radius: 0 0 7px 7px; } .select_label-placeholder { height: 18px; vertical-align: middle; position: absolute; top: 0; left: 0; opacity: 0.7; background-color: transparent; } .select_expand:checked + .select_closeLabel { display: block; } .select_expand:checked + .select_closeLabel + .select_options .select_label { height: 18px; } .select_options { margin-top: 0px; } .select_expand:checked + .select_closeLabel + .select_options .select_label:hover { background-color: #f7f7f7; } .select_expand:checked + .select_closeLabel + .select_options + .select_expandLabel { display: none; } .select_input:checked + .select_label { padding-top: 1px;height: 18px; margin-top: -18px; border-radius: 7px; } .select_label-placeholder { padding-top: 1.5px;} .select_option:first-child .select_label { border-top-left-radius: 7px; border-top-right-radius: 7px; }

/*表情容器*/
.emoticon-popup{cursor: pointer;touch-action: manipulation;margin-top: -3px;border-radius: 5px;box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);display: flex; justify-content: center; flex-wrap: wrap;position: absolute; z-index: 91; background-color: white; width: 100%; max-width: 720px; }  select{background-color: transparent;outline: none;} select::-ms-expand, select::-webkit-inner-spin-button, select::-webkit-outer-spin-button { display: none; } table { border-collapse: collapse; table-layout: fixed; width: 100%; } table, th, td { border: none; }

/*复选框通知楼主*/
.cbx{margin:auto;margin-left:6px;-webkit-user-select:none;user-select:none;cursor:pointer}.cbx span{display:inline-block;vertical-align:middle;transform:translate3d(0,0,0)}.cbx span:first-child{position:relative;width:18px;height:18px;border-radius:4px;transform:scale(1);vertical-align:middle;border:1px solid #c7c7c7;transition:all .2s ease}.cbx span:first-child svg{position:absolute;top:3px;left:2px;fill:none;stroke:#fff;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-dasharray:16px;stroke-dashoffset:16px;transition:all .3s ease;transition-delay:.1s;transform:translate3d(0,0,0)}.cbx span:first-child:before{content:"";width:100%;height:100%;background:#1abc9c;display:block;transform:scale(0);opacity:1;border-radius:50%}.cbx span:last-child{font-size:0.85rem;opacity: 0.6;padding-left:2px}.cbx:hover span:first-child{border-color:#1abc9c}.inp-cbx:checked+.cbx span:first-child{background:#1abc9c;border-color:#1abc9c;animation:wave .4s ease}.inp-cbx:checked+.cbx span:first-child svg{stroke-dashoffset:0}
 */:root{--blue:#007bff;--indigo:#6610f2;--purple:#6f42c1;--pink:#e83e8c;--red:#dc3545;--orange:#fd7e14;--yellow:#ffc107;--green:#28a745;--teal:#20c997;--cyan:#17a2b8;--white:#fff;--gray:#6c757d;--gray-dark:#343a40;--primary:#007bff;--secondary:#6c757d;--success:#28a745;--info:#17a2b8;--warning:#ffc107;--danger:#dc3545;--light:#f8f9fa;--dark:#343a40;--breakpoint-xs:0;--breakpoint-sm:576px;--breakpoint-md:768px;--breakpoint-lg:992px;--breakpoint-xl:1200px;--font-family-sans-serif:-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";--font-family-monospace:SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace}svg:not(:root){overflow:hidden}input{margin:0;font-family:inherit;font-size:inherit;}input{overflow:visible}input[type=checkbox]{box-sizing:border-box;padding:0}::-webkit-file-upload-button{font:inherit;-webkit-appearance:button}@media print{*,::after,::before{text-shadow:none!important;box-shadow:none!important}@page{size:a3}
 .tongzhi {
  display: flex;
  align-items: center;
}