﻿using System;
using System.Web;
using KeLin.ClassManager;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class favList_del : MyPageWap
    {
        private readonly string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string linkURL = "";

        public string condition = "";

        public string ERROR = "";

        public string key = "";

        public string favtypeid = "";

        public string id = "";

        public string backurl = "";

        public string INFO = "";

        public string page = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            backurl = base.Request.QueryString.Get("backurl");
            id = base.Request.QueryString.Get("id");
            page = base.Request.QueryString.Get("page");
            favtypeid = base.Request.QueryString.Get("favtypeid");
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            if (!WapTool.IsNumeric(id))
            {
                id = "0";
            }
            IsLogin(userid, backurl);
            switch (action)
            {
                case "godelall":
                    godelall();
                    break;
                case "godel":
                    godel();
                    break;
            }
        }

        public void godel()
        {
            // ✅ 使用DapperHelper进行安全的参数化删除操作
            string connectionString = PubConstant.GetConnectionString(a);
            string deleteSql = "DELETE FROM favdetail WHERE siteid = @SiteId AND userid = @UserId AND id = @FavId";
            DapperHelper.Execute(connectionString, deleteSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                FavId = DapperHelper.SafeParseLong(id, "收藏ID")
            });
            INFO = "OK";
        }

        public void godelall()
        {
            // ✅ 使用DapperHelper进行安全的参数化删除操作
            string connectionString = PubConstant.GetConnectionString(a);
            string deleteSql;
            object parameters;

            if (favtypeid == "0")
            {
                deleteSql = "DELETE FROM favdetail WHERE siteid = @SiteId AND userid = @UserId";
                parameters = new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    UserId = DapperHelper.SafeParseLong(userid, "用户ID")
                };
            }
            else
            {
                deleteSql = "DELETE FROM favdetail WHERE siteid = @SiteId AND userid = @UserId AND favtypeid = @FavTypeId";
                parameters = new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                    FavTypeId = DapperHelper.SafeParseLong(favtypeid, "收藏类型ID")
                };
            }

            DapperHelper.Execute(connectionString, deleteSql, parameters);
            INFO = "OK";
        }
    }
}