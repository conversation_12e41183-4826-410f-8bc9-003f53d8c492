﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="MessageList.aspx.cs" Inherits="YaoHuo.Plugin.BBS.MessageList" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
string msgbox = "";
if (types == "0")
{
    msgbox = "收件箱|收件箱|Receive";
}
else
{
    msgbox = "发件箱|发件箱|Send"; 
}
StringBuilder strhtml = new StringBuilder();
Response.Write(WapTool.showTop(this.GetLang(msgbox), wmlVo));
strhtml.Append("<script src=\"/NetCSS/JS/BBS/ui-switcher.js\"></script>");
    strhtml.Append("<div class=\"title\"><a href=\"/\">首页</a>><a href=\"/myfile.aspx\">我的地盘</a>>" + this.GetLang(msgbox) + "<span style=\"float: right; cursor: pointer; font-size: 14px; color: #000;\" onclick=\"switchToNewUI()\" title=\"切换到新版\">[新版]</span>");
    strhtml.Append("</div>");
    strhtml.Append("<div class=\"content\">");
    strhtml.Append("<form name=\"f\" action=\"" + http_start + "bbs/messagelist.aspx\" method=\"get\">");
    strhtml.Append("<input type=\"text\" required=\"required\" name=\"key\" value=\"" + key + "\" style=\"width:50%;max-width:200px;height:19px;\"/>");
    strhtml.Append("<input type=\"hidden\" name=\"types\" value=\"" + (types) + "\" />");
    if (!string.IsNullOrEmpty(issystem))
    {
        strhtml.Append("<input type=\"hidden\" name=\"issystem\" value=\"" + issystem + "\" />");
    }
    strhtml.Append("<input type=\"submit\" value=\"" + this.GetLang("搜索消息|搜索|Search") + "\"/><br/>");
    strhtml.Append("</form>");
    strhtml.Append("</div>");
    strhtml.Append("<div class=\"subtitle\">");
    strhtml.Append("<a class=\"urlbtn\" href=\"" + http_start + "bbs/messagelist_add.aspx\">发私信</a>-");
    if (types == "0")
    {
        strhtml.Append("<a class=\"urlbtn\" href=\"" + http_start + "bbs/messagelist.aspx?types=2\">" + this.GetLang("发件箱|发件箱|Send") + "</a>");
        //strhtml.Append("-<a href=\"" + http_start + "bbs/messagelist_clear.aspx?action=godelall&siteid=1000&classid=0&id=0&page=1&types=0&issystem=3&backurl=myfile.aspx%3fsiteid%3d1000\">全部标为已读</a> ");
        strhtml.Append("-<a class=\"noafter\" href=\"" + http_start + "bbs/messagelist_del.aspx?action=godelall&types=0&issystem=1\">清空系统消息</a>");
        strhtml.Append("</div><div class=\"line3\">");
        strhtml.Append("收件箱 [");
        if (issystem == "")
        {
            strhtml.Append("所有<span class=\"separate2\"> </span><a class=\"urlbtn\" href=\"" + http_start + "bbs/messagelist.aspx?types=0&amp;issystem=1\">系统</a><span class=\"separate2\"> </span><a class=\"urlbtn\" href=\"" + http_start + "bbs/messagelist.aspx?types=0&amp;issystem=0\">聊天</a>");
        }
        else if (issystem == "0")
        {
            strhtml.Append("<a class=\"urlbtn\" href=\"" + http_start + "bbs/messagelist.aspx?types=0\">所有</a><span class=\"separate2\"> </span><a class=\"urlbtn\" href=\"" + http_start + "bbs/messagelist.aspx?types=0&amp;issystem=1\">系统</a><span class=\"separate2\"> </span>聊天");
        }
        else if (issystem == "1")
        {
            strhtml.Append("<a class=\"urlbtn\" href=\"" + http_start + "bbs/messagelist.aspx?types=0\">所有</a><span class=\"separate2\"> </span>系统<span class=\"separate2\"> </span><a class=\"urlbtn\" href=\"" + http_start + "bbs/messagelist.aspx?types=0&amp;issystem=0\">聊天</a>");
        }
        else if (issystem == "2")
        {
            strhtml.Append("<a class=\"urlbtn\" href=\"" + http_start + "bbs/messagelist.aspx?types=0\">所有</a><span class=\"separate2\"> </span><a class=\"urlbtn\" href=\"" + http_start + "bbs/messagelist.aspx?types=0&amp;issystem=1\">系统</a><span class=\"separate2\"> </span><a class=\"urlbtn\" href=\"" + http_start + "bbs/messagelist.aspx?types=0&amp;issystem=0\">聊天</a>");
        }
        strhtml.Append("]");
    }
    else
    {
        strhtml.Append("<a class=\"urlbtn\" href=\"" + http_start + "bbs/messagelist.aspx?types=0\">" + this.GetLang("收件箱|收件箱|Receive") + "</a>");
    }
    strhtml.Append("</div>");
    //显示列表
    for (int i = 0; (listVo != null && i < listVo.Count); i++)
    {
        if (i % 2 == 0)
        {
            strhtml.Append("<div class=\"listmms line1\">");
        }
        else
        {
            strhtml.Append("<div class=\"listmms line2\">");
        }
        index = index + kk;
        if (listVo[i].title.Length > 20)
        {
            listVo[i].title = listVo[i].title.Substring(0, 20) + "...";
        }
        if (listVo[i].issystem == 1)
        {
            strhtml.Append("<img src=\"" + http_start + "NetImages/msg.png\" alt=\"系统\"/>");
        }
        if (listVo[i].isnew == 1)
        {
            strhtml.Append("<img src=\"" + http_start + "NetImages/new.gif\" alt=\"新\"/>");
        }
        string keyParam = string.IsNullOrEmpty(key) ? "" : "&amp;key=" + HttpUtility.UrlEncode(key);
        string issystemParam = string.IsNullOrEmpty(issystem) ? "" : "&amp;issystem=" + issystem;
        strhtml.Append("<a href=\"" + http_start + "bbs/messagelist_view.aspx?types=" + this.types + "&amp;id=" + listVo[i].id + "&amp;page=" + this.CurrentPage + issystemParam + keyParam + "\">" + listVo[i].title + "</a><span class=\"laizi\" style=\"padding:0 1px 0 3px;\">来自</span>" + listVo[i].nickname );
        strhtml.Append("<br/>" + string.Format("{0:yyyy/M/d HH:mm}", listVo[i].addtime) + "");
        strhtml.Append(" [<a class=\"urlbtn\" href=\"" + http_start + "bbs/messagelist_del.aspx?action=del&amp;types=" + this.types + "&amp;id=" + listVo[i].id + "&amp;page=" + this.CurrentPage + issystemParam + keyParam + "\" >删除</a>");
        if (types!="2" && issystem != "2")
        {
        }
        strhtml.Append("]");
        strhtml.Append("</div>");
    }
    if (listVo == null)
    {
        strhtml.Append("<div class=\"tip\">暂无短信！</div>");
    }
    //显示导航分页
    strhtml.Append(linkURL);
    string isWebHtml = this.ShowWEB_view(this.classid);
    if (isWebHtml != "")
    {
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml, wmlVo).Replace("[view]", strhtml.ToString()));
        Response.End();
    }
    strhtml.Append("<div class=\"btBox\"><div class=\"bt1\">");
    if (types == "0")
    {
        //strhtml.Append("<a class=\"noafter\" href=\"" + http_start + "bbs/messagelist_del.aspx?action=godelall&siteid=1000&classid=0&id=0&page=1&types=0&issystem=1&backurl=myfile.aspx%3fsiteid%3d1000\">清空系统消息</a>");
        //strhtml.Append("<a class=\"noafter\" href=\"" + http_start + "bbs/messagelist_del.aspx?action=delall&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;types=0&amp;issystem=1&amp;backurl=" + HttpUtility.UrlEncode(backurl) + "&amp;page=" + this.CurrentPage + "" + "\">清空系统消息</a> ");
        strhtml.Append("<a class=\"noafter\" href=\"" + http_start + "bbs/messagelist_del.aspx?action=delall&amp;types=0&amp;issystem=0\">清空聊天消息</a> ");
        //strhtml.Append("<a href=\"" + http_start + "bbs/messagelist_del.aspx?action=delall&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;types=0&amp;issystem=2&amp;backurl=" + HttpUtility.UrlEncode(backurl) + "&amp;page=" + this.CurrentPage + "" + "\">清空收藏消息</a> ");
        //strhtml.Append("<a href=\"" + http_start + "bbs/messagelist_clear.aspx?action=delall&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;types=0&amp;issystem=3&amp;backurl=" + HttpUtility.UrlEncode(backurl) + "&amp;page=" + this.CurrentPage + "" + "\">一键阅读消息</a><br />");
    }
    strhtml.Append("<a href=\"" + http_start + "bbs/messagelist_del.aspx?action=delall&amp;types=" + this.types + "\">清所有" + this.GetLang(msgbox) + "</a> ");
    strhtml.Append("</div></div>");
    strhtml.Append("<div class=\"btBox\"><div class=\"bt2\">");
    strhtml.Append("<a href=\"" + this.http_start + "myfile.aspx\">返回上级</a> ");
    strhtml.Append("<a href=\"" + this.http_start + "\">返回首页</a>");
    strhtml.Append(WapTool.GetVS(wmlVo));
    strhtml.Append("</div></div>");
    Response.Write(strhtml);
Response.Write(ERROR);                                                                                                                                                                              
//显示底部
Response.Write(WapTool.showDown(wmlVo)); %>