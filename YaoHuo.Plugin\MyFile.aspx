﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="MyFile.aspx.cs" Inherits="YaoHuo.Plugin.MyFile" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    // 旧版逻辑 - Code-Behind已处理Cookie切换
    StringBuilder sb = new StringBuilder();
    String welcome = "<div class=\"welcome\">";
    string line1 = "<div class=\"content\">";
    string line2 = "<div class=\"content\">";
    string title = "<div class=\"title\">";
    string div = "</div>";
    string line = "";
    // 查看电脑版
    string isWebHtml = this.ShowWEB_list ("-1");
    Response.Write(WapTool.showTop(this.GetLang("我的地盘|我的地盤|my zone"), wmlVo));
    Response.Write("<script src=\"/NetCSS/JS/BBS/ui-switcher.js\"></script>");
    if (strHtml.IndexOf("[myfile]") > -1 || isWebHtml.IndexOf("[view]") > -1)
    {
    sb.Append(welcome + "欢迎您：<span class=\"nickname\">" + WapTool.GetColorNickName(userVo.idname, userVo.nickname, lang, ver) + "</span><span style=\"float: right; cursor: pointer; font-size: 14px; color: #FFF;\" onclick=\"switchToNewUI()\" title=\"切换到新版\">[新版]</span>" + div);
    sb.Append(line1 + "<a href=\"" + http_start + "bbs/messagelist.aspx?siteid=" + siteid + "&amp;classid=0&amp;types=0" + "\">信箱</a>(" + messagecount + "/" + messageAll + ") <a href=\"" + http_start + "bbs/FriendList.aspx?siteid=" + siteid + "&amp;classid=0&amp;friendtype=0" + "\">好友</a>(" + goodfriend + ")" + div);
    sb.Append(line2 + "我的ID<span class=\"recolon\">:</span>" + userVo.userid + WapTool.GetOLtimePic(http_start, siteVo.lvlTimeImg, userVo.LoginTimes) + div);
    sb.Append("<div class=\"content\">" + "我的" + WapTool.GetSiteMoneyName(siteVo.sitemoneyname, this.lang) + "<span class=\"recolon\">:</span>" + userVo.money + "<br><a href=\"" + http_start + "bbs/RMBtoMoney.aspx?siteid=" + siteid + "\">充值" + siteVo.sitemoneyname + "</a>/<a href=\"" + http_start + "bbs/banklist.aspx?siteid=" + siteid + "&amp;classid=0&amp;key=" + this.userid + "\">账目明细</a>" + div);
    //sb.Append(line2 + "<a href=\"" + http_start + "chinabank_wap/RMBtoMoney.aspx?siteid=" + siteid + "" + "\">充值" + siteVo.sitemoneyname + "</a>/<a href=\"" + this.http_start + "bbs/tomoney.aspx?siteid=" + this.siteid + "\">转账</a>/<a href=\"" + http_start + "bbs/banklist.aspx?siteid=" + siteid + "&amp;classid=0&amp;key=" + this.userid + "" + "\">明细</a>" + div);
    sb.Append(line1 + "银行账户<span class=\"recolon\">:</span><span class=\"bankmoney\">" + userVo.myBankMoney + "</span> <a href=\"/bbs/tomybankmoney.aspx?type=1\">取款</a>" + div);
    sb.Append(line2 + "我的经验<span class=\"recolon\">:</span>" + userVo.expr + div);
    sb.Append(line1 + "我的等级<span class=\"recolon\">:</span>" + WapTool.GetLevl(siteVo.lvlNumer, userVo.expr, userVo.money, type) + div);
    sb.Append(line2 + "我的头衔<span class=\"recolon\">:</span>" + WapTool.GetHandle(siteVo.lvlNumer, userVo.expr,userVo.money,type) + div);
    sb.Append(line1 + "我的身份<span class=\"recolon\">:</span><span class=\"current-identity\">" + WapTool.GetMyID(userVo.idname, lang) + "</span><br/><span class=\"ExpirationDate\"><a href=\"/wapindex.aspx?siteid=" + siteid + "&amp;classid=171" + "\">" + WapTool.showIDEndTime(userVo.siteid, userVo.userid, userVo.endTime, this.lang) + "</a></span>" + div);
    sb.Append("<div class=\"content\" id=\"admin-permissions\">管理权限<span class=\"recolon\">:</span><span name=\"quanxian\">" + WapTool.GetIDName(siteid, this.userid, userVo.managerlvl, this.lang) + "</span></div>");
    sb.Append("<div class=\"content\" id=\"medals\"><a href=\"" + http_start + "xinzhang/book_view_my.aspx?siteid=" + siteid + "&amp;classid=0" + "\">我的勋章</a><span class=\"recolon\">:</span><span name=\"mymedals\">" + WapTool.GetMedal(userVo.userid.ToString(), userVo.moneyname, WapTool.GetSiteDefault(siteVo.Version, 47), wmlVo) + "</span></div>");
    sb.Append(line2 + "<a href=\"/bbs/medal.aspx" + "\">申请勋章</a>/<a href=\"/bbs/medal.aspx?type=buy\">购买勋章</a>" + div);
    sb.Append(line);
    sb.Append(line2 + "我的RMB<span class=\"recolon\">:</span>￥" + userVo.RMB.ToString("f2") + "<br/><a href=\"" + http_start + "chinabank_wap/selbank_wap.aspx?siteid=" + siteid +"\">在线充值</a>/<a href=\"" + http_start + "chinabank_wap/banklist.aspx?siteid=" + siteid + "&amp;tositeid=" + this.siteid + "&amp;touserid=" + this.userid + "" + "\">明细</a>" + div);
    sb.Append(title + "我的设置" + div);
    sb.Append(line1 + "<a href=\"/bbs/ModifyPW.aspx?siteid=1000\">更改密码</a> <a href=\"" + this.http_start + "bbs/modifyuserinfo.aspx?siteid=" + siteid + "" + "\">修改资料</a>" + div);
    sb.Append(line2 + "<a href=\"" + http_start + "bbs/favlist.aspx?siteid=" + siteid + "&amp;classid=0" + "\">我的收藏</a> <a href=\"/album/albumlist.aspx?siteid=1000&classid=0&smalltypeid=0&touserid="+this.userid+"" + "\">我的相册</a>" + div);
    sb.Append(line);
    sb.Append(title + "相关信息" + div);
    sb.Append(line2 + "<a href=\"" + this.http_start + "bbs/book_list.aspx?action=search&amp;siteid=" + this.siteid + "&amp;classid=0&amp;key=" + this.userid + "&amp;type=pub\">我的帖子</a> <a href=\"" + this.http_start + "bbs/book_re_my.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=0&amp;touserid=" + userid + "&amp;" + "\">回复(" + userVo.bbsReCount + ")</a>" + div);
    //sb.Append(line1 + "<a href=\"" + http_start + "bbs/FriendList.aspx?siteid=" + siteid + "&amp;classid=0&amp;friendtype=2" + "\">我的追求</a> <a href=\"" + http_start + "bbs/FriendList.aspx?siteid=" + siteid + "&amp;classid=0&amp;friendtype=4" + "\">追求我的人</a>" + div);
    sb.Append(line2 + "<a href=\"" + http_start + "clan/main.aspx?siteid=" + this.siteid + "&amp;classid=0\">我的家族</a> <a href=\"" + http_start + "bbs/FriendList.aspx?siteid=" + siteid + "&amp;classid=0&amp;friendtype=1" + "\">我的黑名单</a>" + div);
    sb.Append(title + "网站规则" + div);
    sb.Append(line1 + "<a href=\"" + this.http_start + "bbs/tomoneyinfo.aspx?siteid=" + this.siteid + "\">" + siteVo.sitemoneyname + "获取消费规则</a><br/><a href=\"" + this.http_start + "bbs/tolvlinfo.aspx?siteid=" + this.siteid + "\">经验头衔等级规则</a><br/><a href=\"" + this.http_start + "bbs/totimeinfo.aspx?siteid=" + this.siteid + "" + "\">在线时间图标规则</a>" + div);
    
    // 简化JavaScript处理，移到页面底部
    strHtml = strHtml.Replace("[myfile]", sb.ToString());
    isWebHtml = isWebHtml.Replace("[view]", sb.ToString());
    }
//显示电脑效果
if (isWebHtml != "")
{
    Response.Clear();
    Response.Write(WapTool.ToWML(isWebHtml, wmlVo));
    Response.End();
}
//解析UBB方法:
if (strHtml.Trim() == "")
{
    Response.Write("<b>请在WEB/WAP后台---页面综合排版---编辑我的地盘---[顶] 录入[myfile]或自己排版</b><br/>");
}
else
{
    Response.Write(WapTool.ToWML(strHtml, wmlVo));
}
bool isclassadm = WapTool.IsClassAdmin(siteid, userid);
if (this.IsCheckManagerLvl("|00|","")==true) Response.Write(title + "管理后台" + div); 
if (userVo.managerlvl == "00" || userVo.managerlvl == "01")
{
    Response.Write(line1 + "<a href=\"" + this.http_start + "admin/basesitemodifywml.aspx?siteid=" + siteid + "" + "\">站长管理后台</a><br/>");
}
if (userVo.managerlvl == "00")
{
    Response.Write("<a href=\"" + this.http_start + "admin/basesitemodifywml00.aspx?siteid=" + siteid + "" + "\">超级管理后台</a>" + div);
}
if (userVo.managerlvl == "03")
{
    //Response.Write(line1 + "[<a href=\"" + this.http_start + "admin/admin_waplist.aspx?siteid=" + siteid + "" + "\">总编辑管理后台</a>]" + div);
}
if (isclassadm==true)
{
    //Response.Write(line1 + "[<a href=\"" + this.http_start + "admin/admin_waplist.aspx?siteid=" + siteid + "" + "\">栏目管理员后台</a>]" + div);
}
Response.Write(line);
    Response.Write("<div class=\"btBox\"><div class=\"bt2\">");
    Response.Write("<a href=\"/\">返回上级</a> ");
    Response.Write("<a href=\"" + this.http_start + "waplogout.aspx?siteid=" + siteid + "" + "\">安全退出</a> " );
    Response.Write("</div></div>");

Response.Write(WapTool.showDown(wmlVo));
%>

<script>
    // UI切换器已通过ui-switcher.js加载，使用全局函数即可
    // 页面加载时会自动检测并修复Cookie问题
</script>

<script>
    // 银行账户隐藏逻辑
    document.addEventListener("DOMContentLoaded", function () {
        var bankMoneyElement = document.querySelector('.bankmoney');
        if (bankMoneyElement) {
            var parentDivElement = bankMoneyElement.closest('.content');
            var bankMoneyValue = parseInt(bankMoneyElement.textContent);
            if (bankMoneyValue === 0) {
                parentDivElement.style.display = 'none';
            }
        }

        // UI优化逻辑
        var colorMap = {
            '绿色昵称': '#25a444',
            '红色昵称': '#FF0000',
            '蓝色昵称': '#228aff',
            '紫色昵称': '#c000ff',
            '粉色昵称': '#ff6363',
            '粉紫昵称': '#ff00c0',
            '/netimages/vip.gif': { name: '红名VIP', color: '#FF0000' },
            '/netimages/年费vip.gif': { name: '年费VIP', color: '#c000ff' },
            '/netimages/靓号.gif': { name: '靓', color: '#FF7F00' },
            '/netimages/帅.gif': { name: '帅', color: '#228aff' },
            '/netimages/newvip.gif': { name: '金名VIP', color: '#fa6700' }
        };

        function processIdentityElement(identityElement) {
            if (identityElement) {
                var identityText = identityElement.innerHTML;
                var regex = /^(绿色昵称|红色昵称|蓝色昵称|紫色昵称|粉色昵称|粉紫昵称)/;
                var match = identityText.match(regex);

                if (match && colorMap[match[0]]) {
                    var coloredText = '<span style="color: ' + colorMap[match[0]] + ';">' + match[0] + '</span>';
                    identityElement.innerHTML = identityText.replace(match[0], coloredText);
                }

                var imgElement = identityElement.querySelector('img');
                if (imgElement) {
                    var imgSrc = imgElement.getAttribute('src').toLowerCase();
                    if (colorMap[imgSrc]) {
                        var info = colorMap[imgSrc];
                        imgElement.insertAdjacentHTML('afterend', '<span style="color: ' + info.color + '; margin-left: 3px; font-weight: bold;">' + info.name + '</span>');
                    }
                }
            }
        }

        function processExpirationDate(expirationElement) {
            if (expirationElement) {
                var expirationText = expirationElement.innerHTML;
                if (expirationText.indexOf('无期限') === -1) {
                    var dateMatch = expirationText.match(/\d{4}-\d{2}-\d{2}/);
                    if (dateMatch) {
                        var formattedDate = dateMatch[0].replace(/-/g, '/');
                        expirationElement.innerHTML = '有效期至<span class="recolon">:</span>' + formattedDate + ' [<a href="/bbs/BuyGroup.aspx">续费</a>]';
                    }
                } else {
                    expirationElement.innerHTML = '有效期至<span class="recolon">:</span>无期限 [<a href="/bbs/BuyGroup.aspx">开通VIP</a>]';
                }
            }
        }

        function processNicknameElement(nicknameElement) {
            if (nicknameElement) {
                var fontElement = nicknameElement.querySelector('font');
                if (fontElement) {
                    fontElement.removeAttribute('color');
                }
            }
        }

        function processContentElement(contentElement) {
            var identityElement = contentElement.querySelector('.current-identity');
            var expirationElement = contentElement.querySelector('.ExpirationDate');
            if (identityElement && identityElement.textContent.trim() === '普通会员' && expirationElement) {
                contentElement.innerHTML = '我的身份<span class="recolon">:</span><span style="padding-right: 3px;">普通会员</span>[<a href="/wapindex.aspx?siteid=1000&amp;classid=171">购买VIP</a>]';
            }
        }

        function removeAdminPermissionsDiv() {
            var adminPermissionsDiv = document.getElementById('admin-permissions');
            if (adminPermissionsDiv) {
                var quanxianSpan = adminPermissionsDiv.querySelector('span[name="quanxian"]');
                if (quanxianSpan && quanxianSpan.textContent.trim() === '普通') {
                    adminPermissionsDiv.remove();
                }
            }
        }

        function removeMedalsDiv() {
            var medalsDiv = document.getElementById('medals');
            if (medalsDiv) {
                var mymedalsSpan = medalsDiv.querySelector('span[name="mymedals"]');
                if (mymedalsSpan && mymedalsSpan.children.length === 0) {
                    medalsDiv.remove();
                }
            }
        }

        // 执行所有优化处理
        var currentIdentityElements = document.querySelectorAll('.current-identity');
        for (var i = 0; i < currentIdentityElements.length; i++) {
            processIdentityElement(currentIdentityElements[i]);
        }

        var expirationDateElements = document.querySelectorAll('.ExpirationDate a');
        for (var j = 0; j < expirationDateElements.length; j++) {
            var expirationElement = expirationDateElements[j];
            var parentElement = expirationElement.parentElement;
            processExpirationDate(expirationElement);
            parentElement.innerHTML = expirationElement.innerHTML;
        }

        var nicknameElements = document.querySelectorAll('.nickname');
        for (var k = 0; k < nicknameElements.length; k++) {
            processNicknameElement(nicknameElements[k]);
        }

        var contentElements = document.querySelectorAll('.content');
        for (var l = 0; l < contentElements.length; l++) {
            processContentElement(contentElements[l]);
        }

        removeAdminPermissionsDiv();
        removeMedalsDiv();
    });
</script>