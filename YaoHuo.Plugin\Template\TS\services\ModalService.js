const CSS_CLASSES = {
    MODAL_OVERLAY: 'confirm-overlay',
    MODAL_CONTENT: 'confirm-content',
    MODAL_TITLE: 'confirm-title',
    MODAL_MESSAGE: 'confirm-message',
    MODAL_ACTIONS: 'confirm-actions',
    BTN_PRIMARY: 'btn btn-primary',
    BTN_OUTLINE: 'btn btn-outline',
    BTN_DESTRUCTIVE: 'btn btn-destructive',
    CONFIRM_DELETE: 'custom-confirm-btn custom-confirm-delete',
    CONFIRM_CANCEL: 'custom-confirm-btn custom-confirm-cancel',
    HIDDEN: 'hidden'
};
export class ModalService {
    constructor() {
        this.activeModals = new Map();
        this.modalCounter = 0;
    }
    static getInstance() {
        if (!ModalService.instance) {
            ModalService.instance = new ModalService();
        }
        return ModalService.instance;
    }
    static confirm(message, onConfirm, onCancel) {
        return ModalService.getInstance().showConfirm({
            title: '确认操作',
            message,
            type: 'confirm',
            confirmText: '确定',
            cancelText: '取消',
            onConfirm,
            onCancel
        });
    }
    static alert(message, onConfirm) {
        return ModalService.getInstance().showConfirm({
            message,
            type: 'alert',
            confirmText: '确定',
            onConfirm
        });
    }
    static confirmDelete(message, onConfirm, onCancel) {
        return ModalService.getInstance().showConfirm({
            title: '确认操作',
            message,
            type: 'confirm',
            confirmText: '确定',
            cancelText: '取消',
            onConfirm,
            onCancel
        });
    }
    static closeAll() {
        ModalService.getInstance().closeAllModals();
    }
    showConfirm(config) {
        return new Promise((resolve) => {
            const modalId = this.generateModalId();
            const modalElement = this.createModalElement(modalId, config, resolve);
            document.body.appendChild(modalElement);
            this.activeModals.set(modalId, modalElement);
            requestAnimationFrame(() => {
                modalElement.style.display = 'flex';
            });
            this.bindEscapeKey(modalId, () => {
                resolve(false);
                if (config.onCancel) {
                    config.onCancel();
                }
            });
        });
    }
    closeModal(modalId) {
        const modalElement = this.activeModals.get(modalId);
        if (!modalElement)
            return;
        if (modalElement.parentNode) {
            modalElement.parentNode.removeChild(modalElement);
        }
        this.activeModals.delete(modalId);
        this.unbindEscapeKey(modalId);
    }
    closeAllModals() {
        for (const modalId of this.activeModals.keys()) {
            this.closeModal(modalId);
        }
    }
    createModalElement(modalId, config, resolve) {
        const overlay = document.createElement('div');
        overlay.id = modalId;
        overlay.className = CSS_CLASSES.MODAL_OVERLAY;
        overlay.style.display = 'none';
        overlay.onclick = (e) => {
            if (e.target === overlay) {
                this.closeModal(modalId);
                resolve(false);
                if (config.onCancel) {
                    config.onCancel();
                }
            }
        };
        const content = document.createElement('div');
        content.className = CSS_CLASSES.MODAL_CONTENT;
        if (config.title) {
            const title = document.createElement('h3');
            title.className = CSS_CLASSES.MODAL_TITLE;
            title.textContent = config.title;
            content.appendChild(title);
        }
        const message = document.createElement('p');
        message.className = CSS_CLASSES.MODAL_MESSAGE;
        message.textContent = config.message;
        content.appendChild(message);
        const actions = document.createElement('div');
        actions.className = CSS_CLASSES.MODAL_ACTIONS;
        const confirmBtn = document.createElement('button');
        confirmBtn.className = CSS_CLASSES.CONFIRM_DELETE;
        confirmBtn.textContent = config.confirmText || '确定';
        confirmBtn.onclick = () => {
            this.closeModal(modalId);
            resolve(true);
            if (config.onConfirm) {
                config.onConfirm();
            }
        };
        actions.appendChild(confirmBtn);
        if (config.type === 'confirm') {
            const cancelBtn = document.createElement('button');
            cancelBtn.className = CSS_CLASSES.CONFIRM_CANCEL;
            cancelBtn.textContent = config.cancelText || '取消';
            cancelBtn.onclick = () => {
                this.closeModal(modalId);
                resolve(false);
                if (config.onCancel) {
                    config.onCancel();
                }
            };
            actions.appendChild(cancelBtn);
        }
        content.appendChild(actions);
        overlay.appendChild(content);
        return overlay;
    }
    bindEscapeKey(modalId, onEscape) {
        const handler = (e) => {
            if (e.key === 'Escape') {
                this.closeModal(modalId);
                onEscape();
            }
        };
        document.addEventListener('keydown', handler);
        this.activeModals.get(modalId).__escapeHandler = handler;
    }
    unbindEscapeKey(modalId) {
        const modalElement = this.activeModals.get(modalId);
        if (modalElement && modalElement.__escapeHandler) {
            document.removeEventListener('keydown', modalElement.__escapeHandler);
            delete modalElement.__escapeHandler;
        }
    }
    showCustomModal(config) {
        const modalId = this.generateModalId();
        const modal = this.createCustomModal(modalId, config);
        this.activeModals.set(modalId, modal);
        document.body.appendChild(modal);
        requestAnimationFrame(() => {
            modal.style.opacity = '1';
            const contentSelector = config.contentClass ? `.${config.contentClass.split(' ')[0]}` : '.confirm-content';
            const content = modal.querySelector(contentSelector);
            if (content) {
                content.style.transform = 'scale(1)';
            }
        });
        return modalId;
    }
    createCustomModal(modalId, config) {
        const modal = document.createElement('div');
        modal.id = modalId;
        modal.className = `${CSS_CLASSES.MODAL_OVERLAY} ${config.customClass || ''}`;
        modal.style.opacity = '0';
        const content = document.createElement('div');
        content.className = config.contentClass || CSS_CLASSES.MODAL_CONTENT;
        content.style.transform = 'scale(0.95)';
        content.style.transition = 'transform 0.2s ease-out';
        if (config.title) {
            const title = document.createElement('h3');
            title.className = CSS_CLASSES.MODAL_TITLE;
            title.textContent = config.title;
            content.appendChild(title);
        }
        const contentDiv = document.createElement('div');
        contentDiv.innerHTML = config.content;
        content.appendChild(contentDiv);
        modal.appendChild(content);
        if (config.showCloseButton !== false) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal(modalId);
                    if (config.onClose) {
                        config.onClose();
                    }
                }
            });
        }
        return modal;
    }
    generateModalId() {
        return `modal-${++this.modalCounter}-${Date.now()}`;
    }
}
export function showCustomConfirm(message, onConfirm, onCancel) {
    return ModalService.confirm(message, onConfirm, onCancel);
}
export function confirm(message) {
    return ModalService.confirm(message);
}
export function alert(message) {
    return ModalService.alert(message);
}
export function confirmDelete(itemName) {
    return ModalService.confirmDelete(`确定要删除"${itemName}"吗？`);
}
export default ModalService;
