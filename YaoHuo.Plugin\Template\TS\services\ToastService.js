import { CSS_CLASSES, DEFAULT_CONFIG } from '../types/CommonTypes.js';
export class ToastService {
    constructor() {
        this.activeToasts = new Map();
        this.toastCounter = 0;
    }
    static getInstance() {
        if (!ToastService.instance) {
            ToastService.instance = new ToastService();
        }
        return ToastService.instance;
    }
    static showSuccess(message, duration) {
        return ToastService.getInstance().show({
            type: 'success',
            message,
            duration
        });
    }
    static showError(message, duration) {
        return ToastService.getInstance().show({
            type: 'error',
            message,
            duration
        });
    }
    static showWarning(message, duration) {
        return ToastService.getInstance().show({
            type: 'warning',
            message,
            duration
        });
    }
    static showInfo(message, duration) {
        return ToastService.getInstance().show({
            type: 'info',
            message,
            duration
        });
    }
    static close(toastId) {
        ToastService.getInstance().closeToast(toastId);
    }
    static closeAll() {
        ToastService.getInstance().closeAllToasts();
    }
    show(config) {
        const toastId = this.generateToastId();
        const duration = config.duration ?? DEFAULT_CONFIG.TOAST_DURATION;
        const autoClose = config.autoClose ?? true;
        const toastElement = this.createToastElement(toastId, config);
        document.body.appendChild(toastElement);
        this.activeToasts.set(toastId, toastElement);
        requestAnimationFrame(() => {
            toastElement.style.opacity = '1';
            toastElement.style.transform = 'translate(-50%, 0)';
        });
        if (autoClose && duration > 0) {
            setTimeout(() => {
                this.closeToast(toastId);
            }, duration);
        }
        return toastId;
    }
    closeToast(toastId) {
        const toastElement = this.activeToasts.get(toastId);
        if (!toastElement)
            return;
        toastElement.classList.add('fade-out');
        setTimeout(() => {
            if (toastElement.parentNode) {
                toastElement.parentNode.removeChild(toastElement);
            }
            this.activeToasts.delete(toastId);
        }, 300);
    }
    closeAllToasts() {
        for (const toastId of this.activeToasts.keys()) {
            this.closeToast(toastId);
        }
    }
    createToastElement(toastId, config) {
        const toast = document.createElement('div');
        toast.id = toastId;
        toast.className = this.getToastClassName(config.type);
        toast.style.opacity = '0';
        toast.style.transform = 'translate(-50%, -20px)';
        const isMessageDetailStyle = window.useMessageDetailToast !== false;
        if (isMessageDetailStyle) {
            toast.textContent = config.message;
        }
        else {
            const icon = document.createElement('i');
            icon.className = 'w-5 h-5 flex-shrink-0 toast-icon';
            icon.setAttribute('data-lucide', this.getIconName(config.type));
            toast.appendChild(icon);
            const messageSpan = document.createElement('span');
            messageSpan.className = 'flex-1 leading-relaxed';
            messageSpan.textContent = config.message;
            toast.appendChild(messageSpan);
            const closeButton = document.createElement('button');
            closeButton.className = 'bg-transparent border-none cursor-pointer p-1 rounded flex items-center justify-center transition-colors duration-200 flex-shrink-0 hover:bg-gray-100 text-gray-400 hover:text-gray-600';
            closeButton.onclick = () => this.closeToast(toastId);
            const closeIcon = document.createElement('i');
            closeIcon.className = 'w-4 h-4';
            closeIcon.setAttribute('data-lucide', 'x');
            closeButton.appendChild(closeIcon);
            toast.appendChild(closeButton);
            if (typeof window.lucide !== 'undefined') {
                window.lucide.createIcons();
            }
        }
        return toast;
    }
    getToastClassName(type) {
        if (window.useMessageDetailToast !== false) {
            return 'toast-messagedetail';
        }
        switch (type) {
            case 'success':
                return CSS_CLASSES.TOAST_SUCCESS;
            case 'error':
                return CSS_CLASSES.TOAST_ERROR;
            case 'warning':
                return CSS_CLASSES.TOAST_WARNING;
            case 'info':
                return CSS_CLASSES.TOAST_INFO;
            default:
                return CSS_CLASSES.TOAST_INFO;
        }
    }
    getIconName(type) {
        switch (type) {
            case 'success':
                return 'check-circle';
            case 'error':
                return 'x-circle';
            case 'warning':
                return 'alert-triangle';
            case 'info':
                return 'info';
            default:
                return 'info';
        }
    }
    generateToastId() {
        return `toast-${++this.toastCounter}-${Date.now()}`;
    }
}
export function closeToast(toastId) {
    ToastService.close(toastId);
}
export function autoCloseToast(toastId, delay = DEFAULT_CONFIG.TOAST_DURATION) {
    setTimeout(() => {
        ToastService.close(toastId);
    }, delay);
}
export function showToast(type, message, duration) {
    switch (type) {
        case 'success':
            return ToastService.showSuccess(message, duration);
        case 'error':
            return ToastService.showError(message, duration);
        case 'warning':
            return ToastService.showWarning(message, duration);
        case 'info':
            return ToastService.showInfo(message, duration);
        default:
            return ToastService.showInfo(message, duration);
    }
}
export default ToastService;
