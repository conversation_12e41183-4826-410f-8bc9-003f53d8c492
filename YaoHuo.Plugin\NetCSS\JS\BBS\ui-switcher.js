/**
 * UI切换管理脚本
 * 统一管理用户界面偏好设置的Cookie操作
 * 解决重复Cookie值问题，提供稳定的切换机制
 * 
 * @version 1.0.0
 * <AUTHOR>
 */

(function(window) {
    'use strict';

    // 🔧 优化的Cookie管理类
    var UISwitcher = {
        
        // Cookie名称常量
        COOKIE_NAME: 'ui_preference',
        
        // Cookie有效期（天数）
        COOKIE_DAYS: 30,
        
        // 有效的UI模式
        VALID_MODES: ['new', 'old'],
        
        // 默认模式
        DEFAULT_MODE: 'old',

        /**
         * 彻底清理所有ui_preference相关的Cookie
         * 解决重复Cookie值问题
         */
        cleanAllUiPreferenceCookies: function() {
            var cookies = document.cookie.split(';');
            
            for (var i = 0; i < cookies.length; i++) {
                var cookie = cookies[i].trim();
                if (cookie.indexOf(this.COOKIE_NAME + '=') === 0) {
                    var cookieName = cookie.split('=')[0];
                    // 尝试多种路径删除Cookie
                    document.cookie = cookieName + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/';
                    document.cookie = cookieName + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=' + window.location.hostname;
                    document.cookie = cookieName + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT';
                }
            }
        },

        /**
         * 设置UI偏好Cookie
         * @param {string} value - UI模式值 ('new' 或 'old')
         * @param {number} days - Cookie有效期天数（可选，默认30天）
         */
        setUiPreferenceCookie: function(value, days) {
            // 验证输入值
            if (this.VALID_MODES.indexOf(value) === -1) {
                console.warn('无效的UI模式值:', value, '，使用默认值:', this.DEFAULT_MODE);
                value = this.DEFAULT_MODE;
            }
            
            days = days || this.COOKIE_DAYS;
            
            // 🚨 先彻底清理所有ui_preference cookie
            this.cleanAllUiPreferenceCookies();

            // 短暂延迟确保清理完成
            setTimeout(function() {
                // ✅ 设置新的ui_preference cookie
                var date = new Date();
                date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
                var expires = 'expires=' + date.toUTCString();
                document.cookie = UISwitcher.COOKIE_NAME + '=' + value + ';' + expires + ';path=/';
                console.log('UI偏好已设置为:', value);
            }, 50);
        },

        /**
         * 获取UI偏好Cookie值
         * @returns {string} UI模式值，如果未设置则返回空字符串
         */
        getUiPreferenceCookie: function() {
            var name = this.COOKIE_NAME + '=';
            var decodedCookie = decodeURIComponent(document.cookie);
            var cookieArray = decodedCookie.split(';');
            
            for (var i = 0; i < cookieArray.length; i++) {
                var cookie = cookieArray[i];
                while (cookie.charAt(0) === ' ') {
                    cookie = cookie.substring(1);
                }
                if (cookie.indexOf(name) === 0) {
                    return cookie.substring(name.length, cookie.length);
                }
            }
            return '';
        },

        /**
         * 切换到新版UI
         * @param {boolean} reload - 是否立即刷新页面（默认true）
         */
        switchToNewUI: function(reload) {
            reload = reload !== false; // 默认为true
            
            // 🚨 彻底清理所有ui_preference cookie
            this.cleanAllUiPreferenceCookies();

            // 短暂延迟确保清理完成
            setTimeout(function() {
                // ✅ 设置新的ui_preference cookie
                document.cookie = UISwitcher.COOKIE_NAME + "=new;path=/;max-age=" + (UISwitcher.COOKIE_DAYS * 24 * 60 * 60);
                console.log('已切换到新版UI');
                
                if (reload) {
                    window.location.reload();
                }
            }, 50);
        },

        /**
         * 切换到旧版UI
         * @param {boolean} reload - 是否立即刷新页面（默认true）
         */
        switchToOldUI: function(reload) {
            reload = reload !== false; // 默认为true
            
            // 🚨 彻底清理所有ui_preference cookie
            this.cleanAllUiPreferenceCookies();

            // 短暂延迟确保清理完成
            setTimeout(function() {
                // ✅ 设置新的ui_preference cookie
                document.cookie = UISwitcher.COOKIE_NAME + "=old;path=/;max-age=" + (UISwitcher.COOKIE_DAYS * 24 * 60 * 60);
                console.log('已切换到旧版UI');
                
                if (reload) {
                    window.location.reload();
                }
            }, 50);
        },

        /**
         * 检测并修复重复的Cookie值
         * 在页面加载时自动调用，确保Cookie状态正确
         */
        fixDuplicateCookies: function() {
            var cookieString = document.cookie;
            var uiPrefCount = (cookieString.match(new RegExp(this.COOKIE_NAME + '=', 'g')) || []).length;

            if (uiPrefCount > 1) {
                console.log('检测到重复的ui_preference cookie，正在清理...');
                this.cleanAllUiPreferenceCookies();

                // 保留最后一个有效值（如果存在）
                setTimeout(function() {
                    var lastValue = UISwitcher.DEFAULT_MODE; // 默认值
                    var regex = new RegExp(UISwitcher.COOKIE_NAME + '=([^;]*)', 'g');
                    var matches = cookieString.match(regex);
                    
                    if (matches && matches.length > 0) {
                        var lastMatch = matches[matches.length - 1];
                        var value = lastMatch.split('=')[1];
                        if (UISwitcher.VALID_MODES.indexOf(value) !== -1) {
                            lastValue = value;
                        }
                    }
                    
                    document.cookie = UISwitcher.COOKIE_NAME + "=" + lastValue + ";path=/;max-age=" + (UISwitcher.COOKIE_DAYS * 24 * 60 * 60);
                    console.log('Cookie重复问题已修复，当前值:', lastValue);
                }, 100);
            }
        },

        /**
         * 初始化UI切换器
         * 自动检测并修复Cookie问题
         */
        init: function() {
            var self = this;
            
            // 页面加载完成后自动检测Cookie状态
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', function() {
                    self.fixDuplicateCookies();
                });
            } else {
                // 如果页面已经加载完成，立即执行
                self.fixDuplicateCookies();
            }
        }
    };

    // 🌍 全局暴露UISwitcher对象
    window.UISwitcher = UISwitcher;

    // 🔧 向后兼容：提供旧的函数名
    window.cleanAllUiPreferenceCookies = function() {
        UISwitcher.cleanAllUiPreferenceCookies();
    };

    window.setUiPreferenceCookie = function(value, days) {
        UISwitcher.setUiPreferenceCookie(value, days);
    };

    window.getUiPreferenceCookie = function() {
        return UISwitcher.getUiPreferenceCookie();
    };

    window.switchToNewUI = function(reload) {
        UISwitcher.switchToNewUI(reload);
    };

    window.switchToOldUI = function(reload) {
        UISwitcher.switchToOldUI(reload);
    };

    // 🚀 自动初始化
    UISwitcher.init();

})(window); 