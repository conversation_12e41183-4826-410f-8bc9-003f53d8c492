﻿using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using System;
using System.Data;
using Dapper;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using System.Collections.Concurrent;
using System.Collections.Generic;

namespace YaoHuo.Plugin.BBS
{
    public class Book_View_del : MyPageWap
    {
        private static readonly ConcurrentDictionary<string, Queue<DateTime>> _userDeleteTimes = new ConcurrentDictionary<string, Queue<DateTime>>();
        private const int MAX_REQUESTS = 3;  // 普通用户60秒内最多删除3次
        private const int WINDOW_SECONDS = 60;

        public string why = "";
        private readonly string a = PubConstant.GetAppString("InstanceName");
        public string action = "";
        public string id = "";
        public string lpage = "";
        public string INFO = "";
        public string ERROR = "";
        public string ot = "";
        public wap_bbs_Model bbsVo = null;

        // 检查删除频率
        private bool CheckDeleteRate(string userId)
        {
            // 管理员不受频率限制
            if (CheckManagerLvl("04", classVo.adminusername))
            {
                return true;
            }

            var now = DateTime.Now;
            string cacheKey = $"delete_{userId}";

            // 获取或创建用户的请求队列
            var times = _userDeleteTimes.GetOrAdd(cacheKey, _ => new Queue<DateTime>());

            lock (times)
            {
                // 清理过期记录
                while (times.Count > 0 && (now - times.Peek()).TotalSeconds > WINDOW_SECONDS)
                {
                    times.Dequeue();
                }

                // 检查是否超过限制
                if (times.Count >= MAX_REQUESTS)
                {
                    return false;
                }

                // 记录新的请求时间
                times.Enqueue(now);
                return true;
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            // 1. 先进行基础验证
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
                return;
            }

            action = GetRequestValue("action");
            id = GetRequestValue("id");
            lpage = GetRequestValue("lpage");
            ot = GetRequestValue("sub");
            why = GetRequestValue("why");

            // 2. 统一action处理
            if (action.ToLower() == "del_1" || action.ToLower() == "del_2" || action.ToLower() == "del_3")
            {
                ot = action.ToLower() == "del_1" ? "1" : (action.ToLower() == "del_2" ? "2" : "0");
                action = "godel";
            }

            // 3. 登录验证
            IsLogin(userid, "bbs/book_view_admin.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id + "&amp;lpage=" + lpage);

            // 4. 权限验证
            NeedPassWordToAdminNew();

            wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(a);

            if (!long.TryParse(id, out long parsedId))
            {
                ShowTipInfo("无效的帖子ID", "");
                return;
            }

            bbsVo = wap_bbs_BLL.GetModel(parsedId);
            if (bbsVo == null)
            {
                ShowTipInfo("已删除！或不存在！", "");
            }
            else if (bbsVo.ischeck == 1L)
            {
                ShowTipInfo("正在审核中！", "");
            }
            else if (bbsVo.book_classid.ToString() != classid)
            {
                ShowTipInfo("栏目ID对不上！可能没有传classid值！", "");
            }
            else if (bbsVo.islock == 1L)
            {
                ShowTipInfo("此帖已锁！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bbsVo.book_classid + "&amp;id=" + bbsVo.id + "&amp;lpage=" + lpage);
            }
            if (bbsVo == null || (bbsVo != null && bbsVo.ischeck == 2L))
            {
                ShowTipInfo(GetLang("已删除|已删除|Not Exist"), "bbs/book_list.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + lpage);
            }

            // 5. 关键：权限检查优化
            if (bbsVo != null)
            {
                bool hasPermission = false;
                if (userid == bbsVo.book_pub.ToString())
                {
                    // 作者本人
                    hasPermission = true;
                }
                else if (CheckManagerLvl("04", classVo.adminusername))
                {
                    // 管理员
                    hasPermission = true;
                }

                if (!hasPermission)
                {
                    ShowTipInfo("对不起,您没有权限删除此贴！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id);
                    return;
                }
            }

            // 6. 执行删除操作
            if (action == "godel")
            {
                // 1. 验证请求方法
                if (Request.HttpMethod != "POST")
                {
                    ShowTipInfo("不允许的请求方法", "bbs/book_view.aspx?siteid=" + siteid + "&classid=" + classid + "&id=" + id);
                    return;
                }

                // 1.1 检查频率限制(仅针对普通用户)
                if (!CheckDeleteRate(userid))
                {
                    ShowTipInfo($"操作太频繁，请{WINDOW_SECONDS}秒后再试）",
                        "bbs/book_view.aspx?siteid=" + siteid + "&classid=" + classid + "&id=" + id);
                    return;
                }

                // 1. 首先检查帖子状态
                if (bbsVo.ischeck == 2)
                {
                    Response.Write("帖子已删除");
                    return;
                }

                // 2. 验证token
                string token = Request["token"];
                string tokenKey = "formTokenList_view_del_" + id;
                if (!ValidateFormToken(tokenKey, token))
                {
                    Response.Write("安全验证失败，请刷新页面重试");
                    return;
                }

                // 3. 验证用户权限
                bool hasPermission = false;
                if (userid == bbsVo.book_pub.ToString())
                {
                    // 作者本人
                    hasPermission = true;
                }
                else if (CheckManagerLvl("04", classVo.adminusername))
                {
                    // 管理员
                    hasPermission = true;
                }

                if (!hasPermission)
                {
                    Response.Write("无权限执行此操作");
                    return;
                }

                // 4. 执行删除操作
                try
                {
                    // 声明变量
                    long num = WapTool.GetMoneyRegular(siteVo.moneyregular, 0);
                    long num2 = WapTool.GetLvLRegular(siteVo.lvlRegular, 0);

                    // 根据ot参数确定扣币倍数
                    long moneyMultiplier = 0;
                    long expMultiplier = 0;

                    if (userid == bbsVo.book_pub.ToString())
                    {
                        // 作者删除自己的帖子，固定扣2倍
                        moneyMultiplier = 2L;
                        expMultiplier = 2L;
                    }
                    else
                    {
                        // 管理员删除，根据选择的选项确定倍数
                        switch (ot)
                        {
                            case "1": // Del_1: 扣1倍
                                moneyMultiplier = 1L;
                                expMultiplier = 1L;
                                break;
                            case "2": // Del_2: 扣2倍
                                moneyMultiplier = 2L;
                                expMultiplier = 2L;
                                break;
                            case "0": // Del_3: 不扣币和经验
                            default:
                                moneyMultiplier = 0L;
                                expMultiplier = 0L;
                                break;
                        }
                    }

                    long finalMoneyToDeduct = num * moneyMultiplier;
                    long finalExpToDeduct = num2 * expMultiplier;

                    // 检查余额（仅当需要扣币时）
                    if (moneyMultiplier > 0 && userid == bbsVo.book_pub.ToString())
                    {
                        if (userVo.money < 300 || userVo.money < finalMoneyToDeduct)
                        {
                            string moneyName = WapTool.GetSiteMoneyName(siteVo.sitemoneyname, this.lang);
                            ShowTipInfo($"删除失败，您的{moneyName}余额不足！删除帖子需要扣除{finalMoneyToDeduct}{moneyName}，且保留最少300{moneyName}。当前余额:{userVo.money}",
                                "bbs-" + id + ".html");
                            return;
                        }
                    }

                    // ✅ 使用TransactionHelper进行安全的事务性删除操作
                    string connectionString = PubConstant.GetConnectionString(a);
                    long siteIdLong = DapperHelper.SafeParseLong(siteid, "站点ID");
                    long classIdLong = DapperHelper.SafeParseLong(classid, "版块ID");

                    TransactionHelper.ExecuteMoneyTransaction(connectionString, (connection, transaction) =>
                    {
                        // 1. 标记帖子为已删除
                        string updatePostSql = "UPDATE wap_bbs SET ischeck = 2 WHERE userid = @SiteId AND id = @PostId";
                        connection.Execute(updatePostSql, new {
                            SiteId = siteIdLong,
                            PostId = parsedId
                        }, transaction);

                        // 2. 更新版块帖子总数
                        string updateClassSql = "UPDATE [class] SET total = total - 1 WHERE classid = @ClassId";
                        connection.Execute(updateClassSql, new {
                            ClassId = classIdLong
                        }, transaction);

                        // 3. 扣除用户金币和经验（如果需要）
                        if (finalMoneyToDeduct > 0 || finalExpToDeduct > 0)
                        {
                            string updateUserSql = @"UPDATE [user] SET
                                                    money = money - @MoneyToDeduct,
                                                    expR = expR - @ExpToDeduct
                                                    WHERE siteid = @SiteId AND userid = @UserId";
                            connection.Execute(updateUserSql, new {
                                MoneyToDeduct = finalMoneyToDeduct,
                                ExpToDeduct = finalExpToDeduct,
                                SiteId = siteIdLong,
                                UserId = bbsVo.book_pub
                            }, transaction);
                        }
                    });

                    // 4. 记录银行日志（在事务外执行）
                    if (finalMoneyToDeduct > 0)
                    {
                        // ✅ 先获取帖子作者当前余额，避免SaveBankLog中的SELECT操作导致死锁
                        string bankLogConnectionString = PubConstant.GetConnectionString(a);
                        string getAuthorMoneySql = "SELECT money FROM [user] WHERE userid = @UserId AND siteid = @SiteId";
                        long authorCurrentMoney = DapperHelper.ExecuteScalar<long>(bankLogConnectionString, getAuthorMoneySql, new {
                            UserId = bbsVo.book_pub,
                            SiteId = siteIdLong
                        });
                        long authorNewBalance = authorCurrentMoney; // 用户余额已在事务中更新，这里记录更新后的余额

                        // ✅ 使用SaveBankLogWithBalance替换SaveBankLog，避免死锁
                        SaveBankLogWithBalance(bbsVo.book_pub.ToString(), "删除帖子", "-" + finalMoneyToDeduct, userid, nickname, "帖子[ID:" + bbsVo.id + "]", authorNewBalance);
                    }

                    string book_title = bbsVo.book_title;
                    book_title = book_title.Replace("[", "［");
                    book_title = book_title.Replace("]", "］");

                    string title = "您的一篇帖子删除";
                    string deleteReason = "";
                    string penaltyInfo = "";

                    if (userid == bbsVo.book_pub.ToString())
                    {
                        title = "您删除了自己的帖子";
                        penaltyInfo = $"，扣除{finalMoneyToDeduct}妖晶、{finalExpToDeduct}经验";
                    }
                    else
                    {
                        deleteReason = !string.IsNullOrEmpty(why) ? "[br]删帖提示：" + why : "";
                        if (finalMoneyToDeduct > 0 || finalExpToDeduct > 0)
                        {
                            penaltyInfo = $"，扣除{finalMoneyToDeduct}妖晶、{finalExpToDeduct}经验";
                        }
                        else
                        {
                            penaltyInfo = "，未扣除妖晶和经验";
                        }
                    }

                    // ✅ 使用DapperHelper进行安全的参数化插入操作
                    string messageConnectionString = PubConstant.GetConnectionString(a);

                    // 5. 发送系统消息
                    string messageContent = $"[br]删除时间：{DateTime.Now}[br]帖子标题：{bbsVo.book_title}{deleteReason}";
                    string insertMessageSql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                                               VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, 1)";
                    DapperHelper.Execute(messageConnectionString, insertMessageSql, new {
                        SiteId = siteIdLong,
                        UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                        Nickname = DapperHelper.LimitLength(nickname, 50),
                        Title = DapperHelper.LimitLength(title + penaltyInfo, 100),
                        Content = DapperHelper.LimitLength(messageContent, 500),
                        ToUserId = bbsVo.book_pub
                    });

                    // 6. 记录操作日志
                    string logInfo = $"用户ID:{userid}删除用户ID:{bbsVo.book_pub}发表的ID={parsedId}主题:{bbsVo.book_title}";
                    string insertLogSql = @"INSERT INTO wap_log(siteid,oper_userid,oper_nickname,oper_type,log_info,oper_ip)
                                           VALUES (@SiteId, @UserId, @Nickname, 0, @LogInfo, @IP)";
                    DapperHelper.Execute(messageConnectionString, insertLogSql, new {
                        SiteId = siteIdLong,
                        UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                        Nickname = DapperHelper.LimitLength(nickname, 50),
                        LogInfo = DapperHelper.LimitLength(logInfo, 500),
                        IP = DapperHelper.LimitLength(IP, 50)
                    });

                    INFO = "OK";
                    WapTool.ClearDataBBS("bbs" + siteid + classid);
                    WapTool.ClearDataBBS("bbsTop" + siteid + classid);
                    WapTool.ClearDataTemp("bbsTotal" + siteid + classid);

                    // 5. 删除成功后立即使token失效
                    ClearFormToken(tokenKey);
                }
                catch (Exception ex)
                {
                    ERROR = "删除失败：" + ex.Message;
                    return;
                }
            }
            else if (action == "go")
            {
                // 生成新的token供确认页面使用
                string tokenKey = "formTokenList_view_del_" + id;
                string formToken = GenerateFormToken(tokenKey);
            }
        }
    }
}