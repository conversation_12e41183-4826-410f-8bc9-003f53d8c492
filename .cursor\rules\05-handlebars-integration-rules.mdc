---
description: Handlebars.NET集成开发、渐进式重构、双UI系统管理和模板开发规范
globs: *.cs,*.hbs
alwaysApply: false
---
# Handlebars集成与渐进式重构规范

## 使用场景
当需要在ASP.NET Web Forms项目中集成Handlebars.NET进行UI现代化时使用此规则。适用于页面适配、模板开发、渐进式重构等场景。

## 1. 核心集成规则

### 1.1 必须遵循 (MUST)
1. **全局唯一TemplateService**: 所有Handlebars操作必须通过 `TemplateService.RenderPageWithLayout()` 方法
2. **正确处理ThreadAbortException**: 在渲染方法中正确处理，避免使用 `Thread.ResetAbort()`
3. **统一页面适配模式**: 实现 `CheckAndHandleUIPreference()` → `TryRenderWithHandlebars()` → `RenderWithHandlebars()` 流程
4. **统一命名空间**: HeaderOptionsModel使用 `YaoHuo.Plugin.BBS.Models` 命名空间
5. **文件路径一致性**: 代码中模板路径与实际 `.hbs` 文件名完全匹配
6. **DOM选择器同步**: 修改模板HTML结构时，同步更新相关JavaScript的DOM选择器
7. **避免CSS类名冲突**: 自定义CSS类名避免与浏览器默认样式冲突

### 1.2 渐进式重构原则
- **新功能优先**: 新功能优先使用Handlebars模板系统
- **旧功能保持**: 旧功能保持现有实现，确保系统稳定性
- **平滑切换**: 通过ViewState或查询参数支持新旧UI切换
- **技术栈隔离**: 避免在同一页面混用新旧技术栈

### 1.3 自动化特性 (v2.0+)
- **HeaderOptions自动补全**: `RenderPageWithLayout` 支持传入 `null`，系统自动创建默认配置
- **登录状态智能检测**: 基于 `MyPageWap.userid` 自动判断是否显示消息通知
- **消息通知统一管理**: 已登录用户自动显示铃铛图标，游客自动隐藏
- **性能优化**: 通过 `MessageService.GetUnreadCountCached()` 提供30秒缓存

### 1.4 应该遵循 (SHOULD)
- **错误恢复机制**: 新版渲染失败时优雅回退到旧版UI
- **分层错误处理**: 在不同层次捕获和处理异常，提供详细日志
- **模板缓存利用**: 依赖TemplateService内置的模板缓存机制
- **项目文件管理**: 新创建的 `.hbs`、`.css` 文件添加到 `.csproj` 项目文件中
- **前端状态同步**: 处理异步DOM更新时，确保JavaScript状态和事件管理同步
- **利用自动化特性**: 优先使用系统自动处理机制，减少手动配置代码

### 1.5 禁止操作 (MUST NOT)
- **创建新的IHandlebars实例**: 禁止在页面代码中直接创建Handlebars实例
- **使用Thread.ResetAbort()**: 会导致新旧内容拼接问题
- **忽略ThreadAbortException**: 这是Response.End()的正常行为
- **Tailwind构建路径错误**: 禁止使用错误的输入/输出文件路径
- **不必要的构建操作**: 避免执行如更新browserslist数据库等操作
- **过度配置HeaderOptions**: 避免在每个页面重复设置相同的HeaderOptions配置

## 2. 双UI系统管理

### 2.1 系统架构
- **旧版UI**: 传统ASP.NET Web Forms + 内联样式/自定义CSS
- **新版UI**: Handlebars模板 + Tailwind CSS + 现代化组件
- **切换机制**: 用户可在旧版界面切换到新版（新版尚未完全覆盖）
- **兼容性**: 开发时优先完善新版UI，保持旧版兼容性

### 2.2 样式系统分离
- **旧版页面**: 继续使用现有的内联样式和自定义CSS
- **新版模板**: 使用Tailwind CSS工具类和组件化样式
- **样式隔离**: 避免在新版模板中混用旧版样式
- **构建管理**: Tailwind构建输入 `./build-tools/style.css`，输出 `../Template/CSS/output.css`

## 3. 文件组织规范

### 3.1 目录结构
```
Template/
├── Layouts/MainLayout.hbs
├── Pages/PageName.hbs
├── Partials/ComponentName.hbs
└── CSS/output.css

build-tools/
├── package.json
├── postcss.config.js
├── tailwind.config.js
├── style.css
└── node_modules/
```

### 3.2 命名映射
- 页面类名 → 模板文件 → 数据模型
- 特殊情况需在代码中指定路径
- 保持命名一致性和可预测性

## 4. 标准开发流程

### 4.1 页面适配标准结构
```csharp
// 标准流程
CheckAndHandleUIPreference() → 
TryRenderWithHandlebars() → 
RenderWithHandlebars()
```

### 4.2 ThreadAbortException处理
```csharp
try
{
    // 渲染逻辑
    Response.End();
}
catch (System.Threading.ThreadAbortException)
{
    throw; // Response.End()的正常行为
}
catch (Exception ex)
{
    // 其他异常处理
}
```

## 5. 开发检查清单
- [ ] 反射类型名称是否正确
- [ ] SQL查询是否使用参数化
- [ ] ThreadAbortException是否正确处理
- [ ] 模板文件路径是否与代码引用匹配
- [ ] 新文件是否添加到项目(.csproj)中
- [ ] DOM选择器是否与模板结构同步
- [ ] CSS类名是否避免冲突
- [ ] 新旧UI切换是否正常工作

## 6. 参考文档
详细的技术实现、最佳实践、问题解决方案请参考：
**`Documentation/技术文档/Handlebars集成参考手册.md`**

该文档包含完整的：
- TemplateService架构设计
- Helper系统使用
- 数据模型设计模式
- 性能优化策略
- 实战问题解决方案
- 部署维护指南