using System;

namespace YaoHuo.Plugin.OAuth
{
    /// <summary>
    /// OAuth 客户端实体（对应 oauth_clients 表）
    /// </summary>
    public class OAuthClient
    {
        /// <summary>
        /// 应用ID（主键）
        /// </summary>
        public string AppId { get; set; }

        /// <summary>
        /// 应用名称
        /// </summary>
        public string AppName { get; set; }

        /// <summary>
        /// 应用描述
        /// </summary>
        public string AppDescription { get; set; }

        /// <summary>
        /// 应用密钥 Hash 值
        /// </summary>
        public string AppKeyHash { get; set; }

        /// <summary>
        /// 应用密钥 Salt 值
        /// </summary>
        public string AppKeySalt { get; set; }

        /// <summary>
        /// 重定向URI白名单（分号分隔）
        /// </summary>
        public string RedirectUris { get; set; }

        /// <summary>
        /// 允许的权限范围
        /// </summary>
        public string AllowedScopes { get; set; }

        /// <summary>
        /// 是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdatedAt { get; set; }

        #region 轻量业务方法

        /// <summary>
        /// 检查重定向URI是否在白名单中（轻量业务方法）
        /// </summary>
        /// <param name="redirectUri">要检查的重定向URI</param>
        /// <returns>是否允许</returns>
        public bool IsRedirectUriAllowed(string redirectUri)
        {
            if (string.IsNullOrEmpty(RedirectUris) || string.IsNullOrEmpty(redirectUri))
                return false;

            var allowedUris = RedirectUris.Split(new[] { OAuthConstants.SEPARATOR_REDIRECT_URI }, StringSplitOptions.RemoveEmptyEntries);
            foreach (var allowedUri in allowedUris)
            {
                if (string.Equals(allowedUri.Trim(), redirectUri.Trim(), StringComparison.OrdinalIgnoreCase))
                    return true;
            }

            return false;
        }

        /// <summary>
        /// 检查权限范围是否被允许（轻量业务方法）
        /// </summary>
        /// <param name="requestedScope">请求的权限范围</param>
        /// <returns>是否允许</returns>
        public bool IsScopeAllowed(string requestedScope)
        {
            // 数据库层面已保证AllowedScopes不为空，无需额外处理
            if (string.IsNullOrEmpty(AllowedScopes))
                return false;

            if (string.IsNullOrEmpty(requestedScope))
                requestedScope = OAuthConstants.SCOPE_PROFILE; // 默认权限

            var allowedScopes = AllowedScopes.Split(new[] { OAuthConstants.SEPARATOR_SCOPE_PRIMARY, OAuthConstants.SEPARATOR_SCOPE_ALTERNATIVE }, StringSplitOptions.RemoveEmptyEntries);
            var requestedScopes = requestedScope.Split(new[] { OAuthConstants.SEPARATOR_SCOPE_PRIMARY, OAuthConstants.SEPARATOR_SCOPE_ALTERNATIVE }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var requested in requestedScopes)
            {
                bool found = false;
                foreach (var allowed in allowedScopes)
                {
                    if (string.Equals(allowed.Trim(), requested.Trim(), StringComparison.OrdinalIgnoreCase))
                    {
                        found = true;
                        break;
                    }
                }
                if (!found)
                    return false;
            }

            return true;
        }

        /// <summary>
        /// 检查客户端是否处于有效状态（轻量业务方法）
        /// </summary>
        /// <returns>是否有效</returns>
        public bool IsActive()
        {
            return IsValid && !string.IsNullOrEmpty(AppId) && !string.IsNullOrEmpty(AppName);
        }

        #endregion
    }
}