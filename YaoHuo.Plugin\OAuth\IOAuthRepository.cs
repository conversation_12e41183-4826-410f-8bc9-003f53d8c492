using System.Collections.Generic;

namespace YaoHuo.Plugin.OAuth
{
    /// <summary>
    /// OAuth 数据访问接口
    /// </summary>
    public interface IOAuthRepository
    {
        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        string ConnectionString { get; }
        #region 客户端管理

        /// <summary>
        /// 获取客户端信息
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>客户端信息</returns>
        OAuthClient GetClient(string clientId);

        /// <summary>
        /// 获取所有客户端列表
        /// </summary>
        /// <returns>客户端列表</returns>
        List<OAuthClient> GetAllClients();

        /// <summary>
        /// 创建客户端
        /// </summary>
        /// <param name="client">客户端信息</param>
        void CreateClient(OAuthClient client);

        /// <summary>
        /// 更新客户端信息
        /// </summary>
        /// <param name="client">客户端信息</param>
        void UpdateClient(OAuthClient client);

        /// <summary>
        /// 删除客户端
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <returns>是否成功</returns>
        bool DeleteClient(string clientId);

        #endregion

        #region 授权码管理

        /// <summary>
        /// 创建授权码
        /// </summary>
        /// <param name="code">授权码信息</param>
        void CreateAuthorizationCode(OAuthAuthorizationCode code);

        /// <summary>
        /// 获取并验证授权码
        /// </summary>
        /// <param name="code">授权码</param>
        /// <param name="clientId">客户端ID</param>
        /// <param name="redirectUri">重定向URI</param>
        /// <returns>授权码信息</returns>
        OAuthAuthorizationCode GetAuthorizationCode(string code, string clientId, string redirectUri);

        /// <summary>
        /// 标记授权码为已使用
        /// </summary>
        /// <param name="code">授权码</param>
        /// <returns>是否成功</returns>
        bool MarkAuthorizationCodeAsUsed(string code);

        #endregion

        #region 访问令牌管理

        /// <summary>
        /// 创建访问令牌
        /// </summary>
        /// <param name="token">访问令牌信息</param>
        void CreateAccessToken(OAuthAccessToken token);

        /// <summary>
        /// 创建访问令牌（智能模式：自动撤销该用户在同一应用的现有令牌）
        /// </summary>
        /// <param name="token">新的访问令牌</param>
        /// <param name="revokeExisting">是否撤销现有令牌（默认true）</param>
        /// <returns>撤销的现有令牌数量</returns>
        int CreateAccessTokenSmart(OAuthAccessToken token, bool revokeExisting = true);

        /// <summary>
        /// 创建访问令牌（激进模式：物理删除该用户在同一应用的现有令牌）
        /// </summary>
        /// <param name="token">新的访问令牌</param>
        /// <param name="deleteExisting">是否物理删除现有令牌（默认true）</param>
        /// <returns>删除的现有令牌数量</returns>
        int CreateAccessTokenAggressive(OAuthAccessToken token, bool deleteExisting = true);

        /// <summary>
        /// 获取访问令牌
        /// </summary>
        /// <param name="tokenId">令牌ID</param>
        /// <returns>访问令牌信息</returns>
        OAuthAccessToken GetAccessToken(string tokenId);

        /// <summary>
        /// 撤销访问令牌
        /// </summary>
        /// <param name="tokenId">令牌ID</param>
        /// <returns>是否成功</returns>
        bool RevokeAccessToken(string tokenId);

        /// <summary>
        /// 撤销用户在指定应用的所有令牌
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="clientId">客户端ID</param>
        /// <returns>撤销的令牌数量</returns>
        int RevokeUserTokensForClient(long userId, string clientId);

        /// <summary>
        /// 撤销用户的所有令牌
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>撤销的令牌数量</returns>
        int RevokeAllUserTokens(long userId);

        /// <summary>
        /// 更新令牌最后使用时间
        /// </summary>
        /// <param name="tokenId">令牌ID</param>
        /// <returns>是否成功</returns>
        bool UpdateTokenLastUsed(string tokenId);

        #endregion

        #region 用户授权历史

        /// <summary>
        /// 获取用户授权历史（使用视图查询）
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>授权历史列表</returns>
        List<dynamic> GetUserAuthorizations(long userId);

        /// <summary>
        /// 获取令牌和用户信息（优化版 - 单次JOIN查询）
        /// </summary>
        /// <param name="tokenId">令牌ID</param>
        /// <returns>令牌和用户信息的组合对象</returns>
        dynamic GetTokenWithUserInfo(string tokenId);

        #endregion




    }
}