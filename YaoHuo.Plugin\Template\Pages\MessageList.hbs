<div class="message-center-{{MessageType}} pb-4">
{{#if Message.IsError}}
<div class="toast-error" id="errorToast">
    <div class="flex items-center flex-1">
        <i class="w-5 h-5 mr-3 flex-shrink-0" data-lucide="x-circle"></i>
        <span class="flex-1 leading-[1.4]">{{Message.Content}}</span>
    </div>
    <button class="bg-transparent border-none text-white cursor-pointer p-1 ml-3 rounded flex items-center justify-center transition-colors duration-200 flex-shrink-0 hover:bg-white/20" onclick="closeToast('errorToast')">
        <i class="w-4 h-4" data-lucide="x"></i>
    </button>
</div>
{{/if}}

{{#if Message.IsSuccess}}
<div class="toast-success" id="infoToast">
    <div class="flex items-center flex-1">
        <i class="w-5 h-5 mr-3 flex-shrink-0" data-lucide="check-circle"></i>
        <span class="flex-1 leading-[1.4]">{{Message.Content}}</span>
    </div>
    <button class="bg-transparent border-none text-white cursor-pointer p-1 ml-3 rounded flex items-center justify-center transition-colors duration-200 flex-shrink-0 hover:bg-white/20" onclick="closeToast('infoToast')">
        <i class="w-4 h-4" data-lucide="x"></i>
    </button>
</div>
{{/if}}

<main class="pb-4 overflow-x-hidden">


    <div class="card">
        <!-- 标签导航 -->
        <nav class="flex border-b border-gray-100">
            <button class="tab-item {{#eq MessageType "0"}}active{{/eq}} flex-1 p-4 text-center relative font-medium {{#eq MessageType "0"}}text-primary font-bold{{else}}text-gray-500{{/eq}}" id="inbox-tab" data-message-type="0">
                收件箱
                {{#if UnreadCount}}
                <span class="unread-count bg-primary text-white text-xs font-semibold py-0.5 px-1.5 rounded-xl ml-1 min-w-[1.25rem] leading-none" id="inbox-count">{{UnreadCount}}</span>
                {{/if}}
            </button>
            <button class="tab-item {{#eq MessageType "2"}}active{{/eq}} flex-1 p-4 text-center relative font-medium {{#eq MessageType "2"}}text-primary font-bold{{else}}text-gray-500{{/eq}}" id="outbox-tab" data-message-type="2">
                发件箱
            </button>
        </nav>

        {{#if ShowSearchBox}}
        <!-- 搜索框 -->
        <div id="search-container" class="bg-white px-3 py-3 border-b border-gray-100 {{#if IsInboxCompletelyEmpty}}hidden{{/if}} {{#eq MessageType "2"}}{{#unless MessagesList}}hidden{{/unless}}{{/eq}}">
            <form method="get" action="">
                <input type="hidden" name="types" value="{{MessageType}}">
                {{#if FilterType}}
                <input type="hidden" name="issystem" value="{{FilterType}}">
                {{/if}}
                <div class="relative flex items-center">
                    <input type="text" name="key" value="{{SearchKey}}" placeholder="搜索消息内容或昵称" class="search-input">
                    <button type="submit" class="search-button">
                        <i data-lucide="search" class="w-5 h-5"></i>
                    </button>
                </div>
            </form>
        </div>
        {{/if}}

        {{#if ShowFilterActions}}
        {{#eq MessageType "0"}}
        <!-- 筛选与批量操作 -->
        <div id="filter-container" class="bg-white px-3 py-3 border-b border-gray-100 flex items-center justify-between gap-2 {{#if IsInboxCompletelyEmpty}}hidden{{/if}}">
            <!-- 筛选按钮组 -->
            <div class="filter-group inline-flex bg-gray-100 p-1 rounded-md">
                <button class="filter-btn {{#eq FilterType ""}}active{{/eq}} px-3 py-1 rounded text-sm font-medium {{#eq FilterType ""}}bg-white text-primary font-semibold shadow-sm{{else}}text-gray-500{{/eq}} max-[350px]:px-2" data-filter="">所有</button>
                <button class="filter-btn {{#eq FilterType "1"}}active{{/eq}} px-3 py-1 rounded text-sm font-medium {{#eq FilterType "1"}}bg-white text-primary font-semibold shadow-sm{{else}}text-gray-500{{/eq}} max-[350px]:px-2" data-filter="1">系统</button>
                <button class="filter-btn {{#eq FilterType "0"}}active{{/eq}} px-3 py-1 rounded text-sm font-medium {{#eq FilterType "0"}}bg-white text-primary font-semibold shadow-sm{{else}}text-gray-500{{/eq}} max-[350px]:px-2" data-filter="0">聊天</button>
            </div>
            
            <!-- 操作按钮组 -->
            <div class="action-buttons flex items-center">
                <div class="inline-flex rounded-md" role="group">
                    <button id="mark-all-read-btn" type="button" class="btn-outline inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium border border-gray-100 bg-transparent text-gray-400 rounded-l-md" title="一键标记已读">
                        <i data-lucide="check-circle" class="w-4 h-4"></i>
                    </button>
                    <div class="relative">
                        <button id="clean-actions-btn" type="button" class="btn-outline inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium border border-gray-100 bg-transparent text-gray-400 rounded-r-md border-l-0" title="批量清理">
                            <i data-lucide="trash-2" class="w-4 h-4"></i>
                        </button>
                        <!-- 下拉菜单 -->
                        <div id="clean-actions-menu" style="display: none; position: absolute; right: 0; width: max-content; background: white; border-radius: 6px; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); padding: 4px 0; z-index: 50; border: 1px solid rgba(0, 0, 0, 0.05);">
                            <a href="#" class="menu-item flex items-center px-4 py-2 text-sm text-text-secondary hover:bg-gray-100" data-action="clear-system">
                                <i data-lucide="bell-off" class="w-4 h-4 mr-2"></i>清空系统消息
                            </a>
                            <div class="menu-divider my-1 h-px bg-gray-100"></div>
                            <a href="#" class="menu-item flex items-center px-4 py-2 text-sm text-text-secondary hover:bg-gray-100" data-action="clear-chat">
                                <i data-lucide="message-circle-x" class="w-4 h-4 mr-2"></i>清空聊天消息
                            </a>
                            <a href="#" class="menu-item flex items-center px-4 py-2 text-sm text-text-secondary hover:bg-gray-100" data-action="clear-all">
                                <i data-lucide="archive-x" class="w-4 h-4 mr-2"></i>清空全部消息
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{/eq}}
        {{/if}}

        <!-- 消息列表 -->
        <div class="message-list bg-white" id="message-list">
            {{#if MessagesList}}
            {{#each MessagesList}}
            <div class="message-item {{MessageTypeClass}} {{#if IsNew}}unread{{/if}} flex items-center p-4 border-b border-gray-100 cursor-pointer {{#if IsNew}}bg-primary-alpha-5{{/if}}" onclick="location.href='{{ViewUrl}}'">
                {{#if IsNew}}
                <div class="w-2 h-2 rounded-full flex-shrink-0 mr-3 unread-dot"></div>
                {{else}}
                {{#if ../UnreadCount}}
                <div class="read-dot-placeholder w-2 h-2 flex-shrink-0 mr-3"></div>
                {{/if}}
                {{/if}}
                
                {{#if IsSystem}}
                <div class="system-icon w-10 h-10 rounded-full mr-3 flex-shrink-0 flex items-center justify-center text-primary-alpha-90 bg-gray-100">
                    <i data-lucide="bell" style="width: 18px; height: 18px;"></i>
                </div>
                {{else}}
                <div class="user-avatar w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3 flex-shrink-0 relative">
                    <!-- 统一显示对话对象头像（收件箱显示发信人，发件箱显示收信人） -->
                    <!-- 首字母fallback -->
                    <span class="avatar-fallback-small text-primary-alpha-90" data-fallback="true">{{SenderFirstChar}}</span>
                    <!-- 头像图片 -->
                    {{#if SenderAvatarUrl}}
                    {{#unless SenderIsDefaultAvatar}}
                    <img src="{{SenderAvatarUrl}}"
                         alt="{{{SenderName}}}的头像"
                         class="w-10 h-10 object-fill absolute top-0 left-0 z-[1] hidden rounded-full"
                         data-avatar-src="{{SenderAvatarUrl}}"
                         onload="handleSmallAvatarLoad(this)"
                         onerror="handleSmallAvatarError(this)">
                    {{/unless}}
                    {{/if}}
                </div>
                {{/if}}
                
                <div class="message-content flex-1 min-w-0">
                    <div class="message-header flex items-center mb-1 justify-between">
                        <div class="message-sender font-semibold text-gray-900 mr-2">{{#if IsSystem}}系统通知{{else}}{{{SenderName}}}{{/if}}</div>
                        <div class="message-time text-xs {{#if IsNew}}text-primary font-semibold{{else}}text-gray-400{{/if}} ml-auto">{{FriendlyTime}}</div>
                    </div>
                    <div class="message-preview text-sm leading-relaxed overflow-hidden text-ellipsis whitespace-nowrap {{#if IsNew}}text-gray-900{{else}}text-gray-500{{/if}}">{{{TruncatedTitle}}}</div>
                </div>
            </div>
            {{/each}}
            {{else}}
            <div class="text-center py-12 px-4 flex flex-col items-center justify-center">
                <div class="mb-4 w-20 h-20">
                    <i data-lucide="inbox" class="w-full h-full text-gray-300"></i>
                </div>
                <div class="text-lg font-medium text-text-primary mb-1">
                    {{#eq MessageType "0"}}
                        {{#eq FilterType "1"}}系统消息为空{{/eq}}
                        {{#eq FilterType "0"}}聊天消息为空{{/eq}}
                        {{#unless FilterType}}收件箱为空{{/unless}}
                    {{else}}
                        发件箱为空
                    {{/eq}}
                </div>
                <div class="text-sm text-text-secondary">
                    {{#eq MessageType "0"}}
                    {{#eq FilterType "1"}}暂无系统消息{{/eq}}
                    {{#eq FilterType "0"}}暂无聊天消息{{/eq}}
                    {{#unless FilterType}}暂未收到消息{{/unless}}
                    {{else}}
                    暂未发送消息
                    {{/eq}}
                </div>
            </div>
            {{/if}}
        </div>

        {{#if Pagination.ShowPagination}}
        <div class="flex items-center justify-center p-4 mt-1 mb-1">
            <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light" id="prevPageBtn" {{#if Pagination.IsFirstPage}}disabled{{/if}}>
                <i data-lucide="chevron-left" class="w-5 h-5"></i>
            </button>
            <div class="flex-1 text-center text-sm text-text-secondary px-2">
                第 {{Pagination.CurrentPage}} / {{Pagination.TotalPages}} 页
            </div>
            <button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary active:bg-primary-dark active:border-primary-dark active:translate-y-0.5 disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed disabled:hover:bg-bg-gray-100 disabled:hover:text-text-light disabled:hover:border-border-light" id="nextPageBtn" {{#if Pagination.IsLastPage}}disabled{{/if}}>
                <i data-lucide="chevron-right" class="w-5 h-5"></i>
            </button>
        </div>
        {{/if}}
    </div>

    {{!-- 清空发件箱按钮 - 只在发件箱页面显示，放在卡片容器外但在main内 --}}
    {{#eq MessageType "2"}}
    {{#if MessagesList}}
    <div class="mx-4 mt-4">
        <button class="w-full bg-danger text-white border border-danger rounded-md px-4 py-3 font-medium text-sm transition-all duration-200 hover:bg-danger-dark hover:border-danger-dark flex items-center justify-center" id="clear-outbox-btn">
            <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
            清空发件箱
        </button>
    </div>
    {{/if}}
    {{/eq}}
</main>

{{!-- 发送私信弹窗 --}}
<div id="send-msg-modal" class="fixed inset-0 z-modal flex items-center justify-center hidden">
    <div class="absolute inset-0 bg-black/50"></div>
    <div class="relative bg-white rounded-md shadow-[0_10px_25px_rgba(0,0,0,0.15)] w-[90%] max-w-[360px] max-h-[80vh] overflow-hidden">
        <div class="flex items-center justify-between px-5 pt-5 pb-3">
            <h3 class="text-lg font-semibold text-text-primary m-0">发送私信</h3>
            <button class="bg-transparent border-none text-2xl text-text-light cursor-pointer p-0 w-8 h-8 flex items-center justify-center rounded-md transition-colors duration-200 hover:bg-bg-gray-100 hover:text-text-secondary" id="send-msg-close">×</button>
        </div>
        <form id="send-msg-form">
            <div class="px-5 pb-4">
                <div class="mb-3">
                    <input type="number" name="touseridlist" id="send-msg-touserid" placeholder="收信人ID" required class="w-full px-3 py-2 border border-border-normal rounded text-sm transition-all duration-200 focus:outline-none focus:border-primary focus:shadow-[0_0_0_3px_rgba(88,180,176,0.1)]" maxlength="9" inputmode="numeric" min="1" max="999999999">
                </div>
                <div class="mb-0">
                    <textarea name="content" id="send-msg-content" placeholder="请输入消息内容..." required rows="4" class="w-full px-3 py-2 border border-border-normal rounded text-sm transition-all duration-200 focus:outline-none focus:border-primary focus:shadow-[0_0_0_3px_rgba(88,180,176,0.1)] resize-none"></textarea>
                </div>
            </div>
            <div class="flex gap-3 px-5 pb-5">
                <button type="submit" class="flex-1 px-4 py-2 rounded text-sm font-medium transition cursor-pointer select-none border border-transparent bg-primary text-white hover:bg-primary-dark">发送</button>
                <button type="button" class="flex-1 px-4 py-2 rounded text-sm font-medium transition cursor-pointer select-none border border-border-normal bg-transparent text-text-secondary hover:bg-bg-gray-50" id="send-msg-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

</div>

<style>
/* 颜色定义 */
.text-primary-alpha-90 { color: rgba(88, 180, 176, 0.9) !important; }
.bg-primary-alpha-5 { background-color: rgba(88, 180, 176, 0.05) !important; }

/* 保留特殊视觉效果 - 无法用Tailwind替代 */
.tab-item.active::after { 
    content: ''; 
    position: absolute; 
    bottom: 0; 
    left: 0; 
    right: 0; 
    height: 2px; 
    background-color: #58b4b0; 
}

.unread-dot { 
    background: radial-gradient(circle, #7cd0cb 0%, #58b4b0 100%); 
    box-shadow: 0 1px 2px rgba(88, 180, 176, 0.5); 
    animation: breathing-dot 2s ease-in-out infinite; 
}

@keyframes breathing-dot {
    0%, 100% { box-shadow: 0 1px 2px rgba(88, 180, 176, 0.5); }
    50% { box-shadow: 0 1px 2px rgba(88, 180, 176, 0.5), 0 0 0 4px rgba(88, 180, 176, 0.3); }
}
</style>

<script>


// Toast 通知处理函数
function closeToast(toastId) {
    const toast = document.getElementById(toastId);
    if (toast) {
        toast.classList.add('fade-out');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }
}

function autoCloseToast(toastId, delay = 3000) {
    setTimeout(() => {
        closeToast(toastId);
    }, delay);
}

// 显示简约Toast提示
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.textContent = message;
    toast.className = 'fixed bottom-5 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 text-white py-2.5 px-5 rounded-lg z-toast shadow-lg transition-all duration-300 opacity-0';

    // 设置初始位置（向下偏移20px）
    toast.style.transform = 'translate(-50%, 20px)';
    document.body.appendChild(toast);

    // 显示动画（淡入+上滑）
    setTimeout(() => {
        toast.classList.remove('opacity-0');
        toast.classList.add('opacity-100');
        toast.style.transform = 'translate(-50%, 0)';
    }, 10);

    // 隐藏动画（淡出+下滑）
    setTimeout(() => {
        toast.classList.remove('opacity-100');
        toast.classList.add('opacity-0');
        toast.style.transform = 'translate(-50%, 20px)';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 2000);
}

// 标签切换功能
function switchTab(messageType) {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('types', messageType);
    currentUrl.searchParams.delete('page'); // 切换标签时重置页码
    window.location.href = currentUrl.toString();
}

// 筛选功能
function applyFilter(filterType) {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('issystem', filterType);
    currentUrl.searchParams.delete('page'); // 筛选时重置页码
    window.location.href = currentUrl.toString();
}

// 分页导航函数
function navigateToPage(page) {
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('page', page);
    window.location.href = currentUrl.toString();
}



// 自定义确认对话框函数
function showCustomConfirm(message, onConfirm) {
    const confirmDialogOverlay = document.createElement('div');
    confirmDialogOverlay.className = 'confirm-overlay';

    const confirmDialogContent = document.createElement('div');
    confirmDialogContent.className = 'confirm-content';

    confirmDialogContent.innerHTML = `
        <h3 class="confirm-title">确认操作</h3>
        <p class="confirm-message">${message}</p>
        <div class="confirm-actions">
            <button class="custom-confirm-btn custom-confirm-delete" id="confirmCustomConfirm">确定</button>
            <button class="custom-confirm-btn custom-confirm-cancel" id="cancelCustomConfirm">取消</button>
        </div>
    `;

    confirmDialogOverlay.appendChild(confirmDialogContent);
    document.body.appendChild(confirmDialogOverlay);

    // 关闭对话框函数
    function closeDialog() {
        if (document.body.contains(confirmDialogOverlay)) {
            document.body.removeChild(confirmDialogOverlay);
        }
    }

    const cancelBtn = document.getElementById('cancelCustomConfirm');
    if(cancelBtn) cancelBtn.onclick = closeDialog;

    const confirmBtn = document.getElementById('confirmCustomConfirm');
    if(confirmBtn) confirmBtn.onclick = () => {
        onConfirm();
        closeDialog();
    };

    // 点击遮罩关闭 - 只有点击遮罩本身才关闭，点击内容区域不关闭
    confirmDialogOverlay.addEventListener('click', function(e) {
        if (e.target === confirmDialogOverlay) {
            closeDialog();
        }
    });
}

// 执行清理操作
function performCleanAction(action, url, actionText) {
    // 构建正确的清理URL
    let cleanUrl = '';
    switch(action) {
        case 'clear-system':
            cleanUrl = '/bbs/messagelist_del.aspx?action=godelall&types=0&issystem=1';
            break;
        case 'clear-chat':
            cleanUrl = '/bbs/messagelist_del.aspx?action=godelall&types=0&issystem=0';
            break;
        case 'clear-all':
            cleanUrl = '/bbs/messagelist_del.aspx?action=godelall&types=0&issystem=';
            break;
        default:
            showToast('未知的清理操作');
            return;
    }

    fetch(cleanUrl, {
        method: 'GET',
        credentials: 'same-origin'
    })
    .then(response => {
        if (response.ok) {
            // 成功后刷新消息列表
            return refreshMessageList();
        } else {
            throw new Error(`${actionText}失败`);
        }
    })
    .then(() => {
        // 刷新完成后显示成功提示
        showToast(`${actionText}成功`);
    })
    .catch(error => {
        console.error('清理操作出错:', error);
        showToast(`${actionText}失败，请重试`);
    });
}

// AJAX刷新消息列表
function refreshMessageList() {
    return new Promise((resolve, reject) => {
        // 获取当前页面URL用于刷新
        const refreshUrl = window.location.pathname + window.location.search;

        fetch(refreshUrl, {
            method: 'GET',
            credentials: 'same-origin'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('刷新失败');
            }
            return response.text();
        })
        .then(html => {
            // 解析返回的HTML
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // 提取新的消息列表内容
            const newMessageList = doc.getElementById('message-list');
            const newSearchContainer = doc.getElementById('search-container');
            const newFilterContainer = doc.getElementById('filter-container');

            // 查找分页容器（更精确的选择器）
            const newPaginationContainer = doc.querySelector('.flex.items-center.justify-center.p-4.mt-1.mb-1');

            if (newMessageList) {
                // 淡出动画
                const messageList = document.getElementById('message-list');
                const searchContainer = document.getElementById('search-container');
                const filterContainer = document.getElementById('filter-container');
                const paginationContainer = document.querySelector('.flex.items-center.justify-center.p-4.mt-1.mb-1');

                messageList.style.transition = 'opacity 0.2s ease';
                messageList.style.opacity = '0';

                setTimeout(() => {
                    // 更新消息列表内容
                    messageList.innerHTML = newMessageList.innerHTML;

                    // 更新搜索框显示状态（保持新页面的类名）
                    if (searchContainer && newSearchContainer) {
                        searchContainer.className = newSearchContainer.className;
                    }

                    // 更新筛选容器显示状态（保持新页面的类名）
                    if (filterContainer && newFilterContainer) {
                        filterContainer.className = newFilterContainer.className;
                    }

                    // 更新分页显示
                    if (paginationContainer && newPaginationContainer) {
                        paginationContainer.innerHTML = newPaginationContainer.innerHTML;
                        paginationContainer.style.display = '';
                    } else if (paginationContainer && !newPaginationContainer) {
                        paginationContainer.style.display = 'none';
                    }

                    // 淡入动画
                    messageList.style.opacity = '1';

                    // 重新初始化图标
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons();
                    }

                    // 重新初始化占位符显示状态
                    updateUnreadCountAndPlaceholders();

                    // 检查是否需要隐藏清空按钮（当消息列表为空时）
                    const clearOutboxBtn = document.getElementById('clear-outbox-btn');
                    const messageItems = document.querySelectorAll('.message-item');
                    if (clearOutboxBtn && messageItems.length === 0) {
                        clearOutboxBtn.style.display = 'none';
                    }

                    resolve();
                }, 200);
            } else {
                throw new Error('无法解析页面内容');
            }
        })
        .catch(error => {
            console.error('刷新消息列表失败:', error);
            showToast('刷新失败，请重试');
            reject(error);
        });
    });
}





// 一键标记已读功能
function markAllAsRead() {
    const unreadMessages = document.querySelectorAll('.message-item.unread');

    if (unreadMessages.length === 0) {
        showToast('暂无未读消息');
        return;
    }

    // 显示加载状态
    const markAllReadBtn = document.getElementById('mark-all-read-btn');
    const originalContent = markAllReadBtn.innerHTML;
    markAllReadBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 animate-spin"></i>';
    markAllReadBtn.disabled = true;

    // 调用后端API标记已读
    fetch('/bbs/messagelist_clear.aspx?action=godelall&issystem=3', {
        method: 'GET',
        credentials: 'same-origin'
    })
    .then(response => {
        if (response.ok) {
            // 成功后刷新消息列表
            return refreshMessageList();
        } else {
            throw new Error('标记已读失败');
        }
    })
    .then(() => {
        // 刷新完成后显示成功提示和恢复按钮状态
        showToast('标记完成');
        const markAllReadBtn = document.getElementById('mark-all-read-btn');
        if (markAllReadBtn) {
            markAllReadBtn.innerHTML = '<i data-lucide="check-circle" class="w-4 h-4"></i>';
            markAllReadBtn.disabled = false;
            if (typeof lucide !== 'undefined') lucide.createIcons();
        }
    })
    .catch(error => {
        console.error('标记已读出错:', error);
        showToast('标记已读失败，请重试');

        // 恢复按钮状态
        const markAllReadBtn = document.getElementById('mark-all-read-btn');
        if (markAllReadBtn) {
            markAllReadBtn.innerHTML = '<i data-lucide="check-circle" class="w-4 h-4"></i>';
            markAllReadBtn.disabled = false;
            if (typeof lucide !== 'undefined') lucide.createIcons();
        }
    });
}

// 更新未读数量和占位符显示
function updateUnreadCountAndPlaceholders() {
    const unreadCount = document.querySelectorAll('.message-item.unread').length;
    const countBadge = document.getElementById('inbox-count');

    if (unreadCount === 0) {
        // 没有未读消息时，隐藏数量徽章并移除所有占位符
        if (countBadge) {
            countBadge.style.display = 'none';
        }
        document.querySelectorAll('.read-dot-placeholder').forEach(placeholder => {
            placeholder.remove();
        });
    } else {
        // 有未读消息时，显示数量徽章
        if (countBadge) {
            countBadge.textContent = unreadCount;
            countBadge.style.display = 'inline-block';
        }
    }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化 Toast 自动关闭
    const errorToast = document.getElementById('errorToast');
    const infoToast = document.getElementById('infoToast');

    if (errorToast) {
        autoCloseToast('errorToast', 3000);
    }

    if (infoToast) {
        autoCloseToast('infoToast', 3000);
    }

    // 初始化 Lucide 图标
    if (typeof lucide !== 'undefined') {
        lucide.createIcons();
    }

    // 初始化占位符显示状态
    updateUnreadCountAndPlaceholders();

    // 初始化发送私信功能
    initSendMessageFeature();

    // 拦截Header中的写信按钮，改为AJAX发送
    interceptHeaderWriteButton();



    // 标签切换事件
    document.querySelectorAll('.tab-item').forEach(tab => {
        tab.addEventListener('click', function() {
            const messageType = this.dataset.messageType;
            if (messageType) {
                switchTab(messageType);
            }
        });
    });

    // 筛选按钮事件
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const filterType = this.dataset.filter;
            applyFilter(filterType);
        });
    });

    // 一键标记已读按钮
    const markAllReadBtn = document.getElementById('mark-all-read-btn');
    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', markAllAsRead);
    }

    // 智能下拉菜单定位函数
    function positionDropdownMenu(menu, button) {
        const buttonRect = button.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const menuHeight = 120; // 菜单大概高度
        const spaceBelow = viewportHeight - buttonRect.bottom;
        const spaceAbove = buttonRect.top;
        
        // 检测消息数量 - 如果消息很少或者下方空间不足，显示在上方
        const messageItems = document.querySelectorAll('.message-item');
        const shouldShowAbove = messageItems.length <= 2 || spaceBelow < menuHeight;
        
        if (shouldShowAbove && spaceAbove > menuHeight) {
            // 显示在按钮上方
            menu.style.top = 'auto';
            menu.style.bottom = '100%';
            menu.style.marginBottom = '4px';
            menu.style.marginTop = '0';
        } else {
            // 显示在按钮下方（默认）
            menu.style.bottom = 'auto';
            menu.style.top = '100%';
            menu.style.marginTop = '4px';
            menu.style.marginBottom = '0';
        }
    }

    // 清理操作下拉菜单
    const dropdownBtn = document.getElementById('clean-actions-btn');
    const dropdownMenu = document.getElementById('clean-actions-menu');

    if (dropdownBtn && dropdownMenu) {
        dropdownBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            if (dropdownMenu.style.display === 'none' || dropdownMenu.style.display === '') {
                // 显示菜单前先定位
                positionDropdownMenu(dropdownMenu, dropdownBtn);
                dropdownMenu.style.display = 'block';
                dropdownMenu.style.opacity = '0';
                dropdownMenu.style.transform = 'scale(0.95)';
                
                // 添加显示动画
                requestAnimationFrame(() => {
                    dropdownMenu.style.opacity = '1';
                    dropdownMenu.style.transform = 'scale(1)';
                });
            } else {
                // 添加隐藏动画
                dropdownMenu.style.opacity = '0';
                dropdownMenu.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    dropdownMenu.style.display = 'none';
                }, 150);
            }
        });

        dropdownMenu.addEventListener('click', function(e) {
            e.preventDefault();
            const target = e.target.closest('a');
            if (!target) return;

            const action = target.dataset.action;
            const actionText = target.textContent.trim();

            // 关闭下拉菜单（带动画）
            this.style.opacity = '0';
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.display = 'none';
            }, 150);

            // 根据操作类型决定是否需要确认
            if (action === 'clear-system') {
                // 清空系统消息不需要确认，直接执行
                performCleanAction(action, '', actionText);
            } else if (action === 'clear-chat') {
                // 清空聊天消息需要确认
                showCustomConfirm('确定要清空聊天消息吗？此操作不可恢复。', function() {
                    performCleanAction(action, '', actionText);
                });
            } else if (action === 'clear-all') {
                // 清空所有消息需要确认
                showCustomConfirm('确定要清空收件箱吗？此操作不可恢复。', function() {
                    performCleanAction(action, '', actionText);
                });
            }
        });

        // 点击其他地方关闭下拉菜单
        document.addEventListener('click', () => {
            if (dropdownMenu.style.display === 'block') {
                dropdownMenu.style.opacity = '0';
                dropdownMenu.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    dropdownMenu.style.display = 'none';
                }, 150);
            }
        });
    }

    // 清空发件箱按钮事件处理
    const clearOutboxBtn = document.getElementById('clear-outbox-btn');
    if (clearOutboxBtn) {
        clearOutboxBtn.addEventListener('click', function(event) {
            event.stopPropagation();
            
            showCustomConfirm('确定要清空发件箱吗？此操作不可恢复。', function() {
                // 显示加载状态
                clearOutboxBtn.disabled = true;
                clearOutboxBtn.style.opacity = '0.5';
                const originalContent = clearOutboxBtn.innerHTML;
                clearOutboxBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-2 animate-spin"></i>清空中...';
                
                // 执行异步清空操作
                fetch('/bbs/messagelist_del.aspx?action=godelall&types=2', {
                    method: 'GET',
                    credentials: 'same-origin'
                })
                .then(response => {
                    if (response.ok) {
                        // 成功后刷新消息列表
                        return refreshMessageList();
                    } else {
                        throw new Error('清空发件箱失败');
                    }
                })
                .then(() => {
                    // 刷新完成后显示成功提示
                    showToast('清空发件箱成功');
                    
                    // 隐藏清空按钮（因为已经没有消息了）
                    clearOutboxBtn.style.display = 'none';
                })
                .catch(error => {
                    console.error('清空发件箱操作出错:', error);
                    showToast('清空发件箱失败，请重试');
                })
                .finally(() => {
                    // 恢复按钮状态
                    clearOutboxBtn.disabled = false;
                    clearOutboxBtn.style.opacity = '';
                    clearOutboxBtn.innerHTML = originalContent;
                    if (typeof lucide !== 'undefined') {
                        lucide.createIcons();
                    }
                });
            });
        });
    }

    // 分页按钮事件
    const prevPageBtn = document.getElementById('prevPageBtn');
    const nextPageBtn = document.getElementById('nextPageBtn');

    if (prevPageBtn) {
        prevPageBtn.addEventListener('click', function() {
            if (!this.disabled) {
                const currentPage = {{Pagination.CurrentPage}};
                if (currentPage > 1) {
                    navigateToPage(currentPage - 1);
                }
            }
        });
    }

    if (nextPageBtn) {
        nextPageBtn.addEventListener('click', function() {
            if (!this.disabled) {
                const currentPage = {{Pagination.CurrentPage}};
                const totalPages = {{Pagination.TotalPages}};
                if (currentPage < totalPages) {
                    navigateToPage(currentPage + 1);
                }
            }
        });
    }
});

// 全局函数：显示发送私信弹窗 (供Header按钮调用)
window.showSendMessageModal = function(touserid = '') {
    const modal = document.getElementById('send-msg-modal');
    const touseridInput = document.getElementById('send-msg-touserid');
    const contentTextarea = document.getElementById('send-msg-content');
    
    if (modal && touseridInput && contentTextarea) {
        touseridInput.value = touserid;
        contentTextarea.value = '';
        modal.classList.remove('hidden');
        contentTextarea.focus();
    }
}

// 显示发送私信弹窗
function showSendMessageModal(touserid = '') {
    return window.showSendMessageModal(touserid);
}

// 隐藏发送私信弹窗
function hideSendMessageModal() {
    const modal = document.getElementById('send-msg-modal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

// 初始化发送私信功能
function initSendMessageFeature() {
    const modal = document.getElementById('send-msg-modal');
    const form = document.getElementById('send-msg-form');
    const closeBtn = document.getElementById('send-msg-close');
    const cancelBtn = document.getElementById('send-msg-cancel');
    const overlay = modal?.querySelector('.absolute.inset-0');
    
    // 关闭按钮事件
    if (closeBtn) {
        closeBtn.addEventListener('click', hideSendMessageModal);
    }
    
    if (cancelBtn) {
        cancelBtn.addEventListener('click', hideSendMessageModal);
    }
    
    // 点击遮罩关闭
    if (overlay) {
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                hideSendMessageModal();
            }
        });
    }
    
    // 表单提交事件
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(form);
            formData.append('action', 'gomod');
            formData.append('ajax', '1');
            
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.textContent = '发送中...';
            }
            
            fetch('/bbs/messagelist_add.aspx', {
                method: 'POST',
                body: formData
            })
            .then(res => res.text())
            .then(code => {
                code = code.trim();
                if (code === 'OK') {
                    showToast('发送成功');
                    form.reset();
                    hideSendMessageModal();
                    // 如果当前在发件箱，刷新列表
                    const currentMessageType = document.querySelector('.tab-item.active')?.dataset.messageType;
                    if (currentMessageType === '2') {
                        setTimeout(() => {
                            refreshMessageList();
                        }, 800);
                    }
                } else if (code === 'REPEAT') {
                    showToast('内容重复，请修改后再发');
                } else if (code === 'NULL') {
                    showToast('请完整填写表单');
                } else if (code === 'WAITING') {
                    showToast('操作过快，请稍后再试');
                } else if (code === 'PWERROR') {
                    showToast('密码错误，请重新输入');
                } else if (code === 'MAX1' || code === 'MAX') {
                    showToast('已达今日发信上限');
                } else if (code === 'LOCK') {
                    showToast('你已被加入黑名单');
                } else if (code === 'CANTSELF') {
                    showToast('不能给自己发消息');
                } else if (code === 'NOTEXSIT') {
                    showToast('用户ID不存在');
                } else {
                    showToast('发送失败，请重试');
                }
            })
            .catch(() => {
                showToast('发送失败，请重试');
            })
            .finally(() => {
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.textContent = '发送';
                }
            });
        });
    }
}

// 拦截Header中的写信按钮，改为AJAX发送
function interceptHeaderWriteButton() {
    // 查找Header中的写信按钮
    const headerWriteBtn = document.getElementById('write-message-btn');
    
    if (headerWriteBtn) {
        // 移除原有的onclick属性，防止跳转
        headerWriteBtn.removeAttribute('onclick');
        
        // 添加新的点击事件
        headerWriteBtn.addEventListener('click', function(e) {
            e.preventDefault(); // 阻止默认行为
            e.stopPropagation(); // 阻止事件冒泡
            
            // 调用AJAX发送私信弹窗
            showSendMessageModal();
        });
        
        // 添加cursor指针样式，确保看起来可点击
        headerWriteBtn.style.cursor = 'pointer';
    }
}
</script>

<!-- 引入AvatarHandler.js脚本，用于处理头像加载 -->
<script src="/Template/JS/Components/AvatarHandler.js?v=1" defer></script>