﻿using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using System;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Book_View_end : MyPageWap
    {
        private readonly string a = PubConstant.GetAppString("InstanceName");
        public string action = "";
        public string id = "";
        public string lpage = "";
        public string INFO = "";
        public string ERROR = "";
        public string tops = "";
        public string whylock = "";
        public wap_bbs_Model bookVo = null;

        protected void Page_Load(object sender, EventArgs e)
        {
            // 1. 先进行基础验证
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
                return;
            }

            // 2. 获取请求参数
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            lpage = GetRequestValue("lpage");
            tops = GetRequestValue("tops");
            whylock = GetRequestValue("whylock")?.Replace("|", "");

            // 3. 登录验证
            IsLogin(userid, "bbs/book_view_admin.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id + "&amp;lpage=" + lpage);

            // 4. 管理员密码验证
            NeedPassWordToAdminNew();

            // 5. 获取帖子信息
            wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(a);
            if (!long.TryParse(id, out long parsedId))
            {
                ShowTipInfo("无效的帖子ID", "");
                return;
            }

            bookVo = wap_bbs_BLL.GetModel(parsedId);

            // 6. 帖子状态验证
            if (bookVo == null)
            {
                ShowTipInfo("已删除！或不存在！", "");
                return;
            }
            if (bookVo.ischeck == 1L)
            {
                ShowTipInfo("正在审核中！", "");
                return;
            }
            if (bookVo.book_classid.ToString() != classid)
            {
                ShowTipInfo("栏目ID对不上！可能没有传classid值！", "");
                return;
            }

            // 7. 权限验证
            bool hasPermission = false;
            if (userid == bookVo.book_pub.ToString())
            {
                // 作者本人
                hasPermission = true;
            }
            else if (CheckManagerLvl("04", classVo.adminusername))
            {
                // 管理员
                hasPermission = true;
            }

            if (!hasPermission)
            {
                ShowTipInfo("对不起,您没有权限操作此贴！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id);
                return;
            }

            // 8. 执行结帖/解除结帖操作
            if (action == "gomod")
            {
                // 8.1 Token验证
                string token = Request["token"];
                string tokenKey = "formTokenList_end_" + id;
                if (!ValidateFormToken(tokenKey, token))
                {
                    ShowTipInfo("安全验证失败，请刷新页面重试", "");
                    return;
                }

                try
                {
                    // 8.2 结帖操作
                    if (tops == "2") // 结帖
                    {
                        if (bookVo.islock == 2L)
                        {
                            INFO = "ERR";
                            return;
                        }
                        ExecuteLockPost(true);
                    }
                    else // 解除结帖
                    {
                        // 增加权限验证：只有管理员可以解除结帖
                        if (!CheckManagerLvl("04", classVo.adminusername))
                        {
                            ShowTipInfo("对不起，只有管理员才能解除结帖！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id);
                            return;
                        }

                        if (bookVo.islock == 0L)
                        {
                            INFO = "ERR";
                            return;
                        }
                        ExecuteLockPost(false);
                    }

                    // 8.3 清理缓存
                    WapTool.ClearDataBBS("bbs" + siteid + classid);
                    WapTool.ClearDataBBS("bbsTop" + siteid + classid);

                    // 8.4 清除token并设置成功标志
                    ClearFormToken(tokenKey);
                    INFO = "OK";
                }
                catch (Exception ex)
                {
                    ERROR = ex.ToString();
                }
            }
            else if (action == "go")
            {
                // 9. 生成新的token供确认页面使用
                // 如果是解除结帖请求且用户不是管理员，直接拒绝
                if (tops != "2" && !CheckManagerLvl("04", classVo.adminusername))
                {
                    ShowTipInfo("对不起，只有管理员才能解除结帖！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id);
                    return;
                }
                string tokenKey = "formTokenList_end_" + id;
                string formToken = GenerateFormToken(tokenKey);
            }
        }

        /// <summary>
        /// 执行结帖或解除结帖操作
        /// </summary>
        /// <param name="isLock">true:结帖 false:解除结帖</param>
        private void ExecuteLockPost(bool isLock)
        {
            // 1. 准备操作相关参数
            string operationType = isLock ? "结束" : "解除结束";
            string lockStatus = isLock ? "2" : "0";
            var whylockContent = isLock
                ? $"{{{userVo.nickname}(ID{userVo.userid}){operationType}原因:{whylock} {DateTime.Now:MM-dd HH:mm}}}<br/>{bookVo.whylock}"
                : $"{{{userVo.nickname}(ID{userVo.userid}){operationType}帖子 {DateTime.Now:MM-dd HH:mm}}}<br/>{bookVo.whylock}";

            // 计算需要返还的悬赏金额
            long remainingBounty = 0;
            string returnBountyMessage = "";

            if (isLock)
            {
                remainingBounty = bookVo.sendMoney - bookVo.hasMoney;
                if (remainingBounty > 0)
                {
                    // 计算税后金额（95%）
                    long taxRate = 5; // 税率5%
                    long returnAmount = remainingBounty * (100 - taxRate) / 100;

                    // 使用事务处理关键SQL
                    var connStr = $"Data Source={PubConstant.GetAppString("KL_SQL_SERVERIP")};Initial Catalog={PubConstant.GetAppString("KL_DatabaseName")};User ID={PubConstant.GetAppString("KL_SQL_UserName")};Password={PubConstant.GetAppString("KL_SQL_PassWord")}";
                    using (var conn = new System.Data.SqlClient.SqlConnection(connStr))
                    {
                        conn.Open();
                        var tran = conn.BeginTransaction();
                        try
                        {
                            // 1. 乐观锁更新帖子
                            string updateSql = $"update wap_bbs set islock={lockStatus},whylock=@whylock,hasMoney={bookVo.sendMoney} where userid={siteid} and id={id} and hasMoney={bookVo.hasMoney}";
                            var cmd1 = new System.Data.SqlClient.SqlCommand(updateSql, conn, tran);
                            cmd1.Parameters.AddWithValue("@whylock", whylockContent.Replace("'", "''"));
                            long rows = cmd1.ExecuteNonQuery();

                            if (rows > 0)
                            {
                                // 2. 退钱
                                string updateUserSql = $"update [user] set money=money+{returnAmount} where userid={bookVo.book_pub} and siteid={siteid}";
                                var cmd2 = new System.Data.SqlClient.SqlCommand(updateUserSql, conn, tran);
                                cmd2.ExecuteNonQuery();

                                tran.Commit();

                                // ✅ 先获取帖子作者当前余额，避免SaveBankLog中的SELECT操作导致死锁
                                string connectionString = PubConstant.GetConnectionString(a);
                                string getAuthorMoneySql = "SELECT money FROM [user] WHERE userid = @UserId AND siteid = @SiteId";
                                long authorCurrentMoney = DapperHelper.ExecuteScalar<long>(connectionString, getAuthorMoneySql, new {
                                    UserId = bookVo.book_pub,
                                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID")
                                });
                                long authorNewBalance = authorCurrentMoney; // 用户余额已在事务中更新，这里记录更新后的余额

                                // ✅ 使用SaveBankLogWithBalance替换SaveBankLog，避免死锁
                                SaveBankLogWithBalance(bookVo.book_pub.ToString(), "悬赏返还(税后)", returnAmount.ToString(),
                                    userid, nickname, "结帖返还未发放悬赏[" + id + "]，扣除5%税费", authorNewBalance);

                                returnBountyMessage = "[br]返还未发放悬赏：" + returnAmount + "妖晶";
                            }
                            else
                            {
                                tran.Rollback();
                                // 已经被其他请求处理过，直接返回
                                return;
                            }
                        }
                        catch
                        {
                            tran.Rollback();
                            throw;
                        }
                    }
                }
                else
                {
                    // ✅ 没有悬赏需要退，正常结贴，使用DapperHelper
                    string normalEndConnectionString = PubConstant.GetConnectionString(a);
                    string updateSql = "UPDATE wap_bbs SET islock = @LockStatus, whylock = @WhyLock WHERE userid = @SiteId AND id = @PostId";
                    DapperHelper.Execute(normalEndConnectionString, updateSql, new {
                        LockStatus = int.Parse(lockStatus),
                        WhyLock = DapperHelper.LimitLength(whylockContent, 1000),
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        PostId = DapperHelper.SafeParseLong(id, "帖子ID")
                    });
                }
            }
            else
            {
                // ✅ 解除结贴逻辑，使用DapperHelper
                string unlockConnectionString = PubConstant.GetConnectionString(a);
                string updateSql = "UPDATE wap_bbs SET islock = @LockStatus, whylock = @WhyLock WHERE userid = @SiteId AND id = @PostId";
                DapperHelper.Execute(unlockConnectionString, updateSql, new {
                    LockStatus = int.Parse(lockStatus),
                    WhyLock = DapperHelper.LimitLength(whylockContent, 1000),
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    PostId = DapperHelper.SafeParseLong(id, "帖子ID")
                });
            }

            // 3. 发送消息通知
            string book_title = bookVo.book_title.Replace("[", "［").Replace("]", "］");
            string messageTitle = isLock ? "您的一个主题被结束" : "您的一个主题取消结束!";

            // 如果返还了妖晶，修改消息标题
            if (isLock && remainingBounty > 0)
            {
                messageTitle = "您的一个主题被结束，悬赏已返还";
            }

            // ✅ 3.1 使用DapperHelper插入消息记录
            string messageConnectionString = PubConstant.GetConnectionString(a);
            string messageContent = "设置时间：" + DateTime.Now + returnBountyMessage + "[br]论坛主题：[url=/bbs-" + id + ".html]" + book_title + "[/url]";

            string insertMessageSql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                                      VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, 1)";
            DapperHelper.Execute(messageConnectionString, insertMessageSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                Nickname = DapperHelper.LimitLength(nickname, 50),
                Title = DapperHelper.LimitLength(messageTitle, 100),
                Content = DapperHelper.LimitLength(messageContent, 500),
                ToUserId = bookVo.book_pub
            });

            // ✅ 4. 使用DapperHelper记录操作日志
            string bountyInfo = remainingBounty > 0 ? $",返还悬赏(税后){(remainingBounty * 95 / 100)}妖晶" : "";
            string logInfo = "用户ID:" + userid + operationType + "用户ID:" + bookVo.book_pub + "发表的ID=" + id + "主题:" + book_title + bountyInfo;

            string insertLogSql = @"INSERT INTO wap_log(siteid,oper_userid,oper_nickname,oper_type,log_info,oper_ip)
                                  VALUES (@SiteId, @OperUserId, @OperNickname, 0, @LogInfo, @OperIp)";
            DapperHelper.Execute(messageConnectionString, insertLogSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                OperUserId = DapperHelper.SafeParseLong(userid, "操作用户ID"),
                OperNickname = DapperHelper.LimitLength(nickname, 50),
                LogInfo = DapperHelper.LimitLength(logInfo, 500),
                OperIp = DapperHelper.LimitLength(IP, 50)
            });
        }
    }
}