<!-- Font Awesome 图标库 -->
<link href="//lf6-cdn-tos.bytecdntp.com/cdn/expire-1-y/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

<!-- 页面特定样式 -->
<style>
/* CSS变量定义 */
:root {
    --avatar-overlay-z-index: 1000;
    --avatar-transition-duration: 0.3s;
    --avatar-hover-transition: 0.2s;
}

.main-content{overflow-x: hidden;}

/* 头像悬浮层样式 */
.avatar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: var(--avatar-overlay-z-index);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all var(--avatar-transition-duration) ease;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
}

.avatar-overlay.show {
    opacity: 1;
    visibility: visible;
}

.avatar-overlay-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    transform: scale(0.8);
    transition: transform var(--avatar-transition-duration) ease;
}

.avatar-overlay.show .avatar-overlay-content {
    transform: scale(1);
}

.avatar-overlay-image {
    width: 100%;
    height: auto;
    display: block;
    max-width: 80vw;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 5px; /* 刻意设计的圆角，保留 */
}

.avatar-container {
    transition: all var(--avatar-hover-transition) ease;
    position: relative;
}

.avatar-container.clickable {
    cursor: pointer;
}

.avatar-container.clickable:hover {
    transform: scale(1.05);
}

.avatar-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    border-radius: inherit;
    opacity: 0;
    transition: opacity var(--avatar-hover-transition) ease;
}

.avatar-container.clickable:hover::after {
    opacity: 1;
}

/* 可访问性 - Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}
</style>

<!-- 个人资料展示区域 -->
<section class="px-4 mb-4 mt-4 max-768:px-3 max-768:mb-3 max-768:mt-3 xs-400:px-2 xs-400:mb-3 xs-400:mt-3">
    <div class="bg-white rounded-xl shadow-[0_2px_10px_rgba(0,0,0,0.1)] pt-5 px-5 pb-5 xs-400:pt-4 xs-400:px-4 xs-400:pb-4 xs-350:pt-4 xs-350:px-3 xs-350:pb-3 xs-310:pt-4 xs-310:px-2 xs-310:pb-2">
        <!-- 头像和基本信息 -->
        <div class="flex items-start space-x-4 mb-6 xs-400:mb-4 xs-350:mb-3">
            <div class="relative">
                <!-- 头像容器 -->
                <div class="avatar-container w-20 h-20 xs-400:w-18 xs-400:h-18 xs-350:w-16 xs-350:h-16 rounded-2xl overflow-hidden bg-gray-50 flex items-center justify-center relative border-2 border-primary-alpha-20">
                    <!-- 首字母fallback -->
                    <span class="avatar-fallback-main" data-fallback="true">{{firstChar UserInfo.Nickname}}</span>
                    <!-- 头像图片 -->
                    {{#if UserInfo.AvatarUrl}}
                    {{#unless UserInfo.IsDefaultAvatar}}
                    <img src="{{UserInfo.AvatarUrl}}"
                         alt="{{UserInfo.Nickname}}"
                         class="w-20 h-20 xs-400:w-18 xs-400:h-18 xs-350:w-16 xs-350:h-16 object-fill absolute top-0 left-0 z-[1] hidden"
                         data-avatar-src="{{UserInfo.AvatarUrl}}"
                         onload="handleAvatarLoad(this)"
                         onerror="handleAvatarError(this)">
                    {{/unless}}
                    {{/if}}
                </div>
            </div>
            <div class="flex-1 -mt-0.5">
                <!-- 昵称行，右侧添加私信按钮 -->
                <div class="flex items-center justify-between mb-1 xs-400:mb-0.5 xs-350:mb-0">
                    <h2 class="text-xl xs-400:text-lg xs-350:text-base font-bold text-gray-800 nickname-display truncate max-w-[calc(100%-4.5rem)] xs-400:max-w-[calc(100%-3rem)] xs-350:max-w-[calc(100%-2.5rem)] xs-310:max-w-[calc(100%-2rem)] cursor-pointer relative overflow-hidden"
                        id="nicknameDisplay"
                        onmouseenter="startNicknameScroll(this)"
                        onmouseleave="stopNicknameScroll(this)"
                        onclick="toggleNicknameScroll(this)">{{{UserInfo.DisplayNickname}}}</h2>
                    {{#unless IsOwnSpace}}
                    <button onclick="sendMessage()" class="text-primary hover:text-primary/80 transition-all duration-300 flex items-center hover:-translate-y-0.5 text-sm xs-400:text-xs px-3 xs-400:px-2 xs-400:py-1 xs-350:px-1.5 xs-350:py-0.5">
                        <i class="fas fa-envelope mr-1 xs-400:mr-0.5 text-lg xs-400:text-sm transition-transform duration-300"></i>
                        <span class="xs-400:hidden transition-transform duration-300">私信</span>
                    </button>
                    {{/unless}}
                </div>
                <p class="text-gray-500 text-sm mb-2 xs-400:mb-1 xs-350:mb-0.5">ID: {{UserInfo.UserId}}</p>
                <div class="flex items-center space-x-4 text-sm xs-350:text-xs text-gray-500">
                    <span><i class="fas {{#eq UserInfo.Sex 1}}fa-mars text-blue-500{{else}}fa-venus text-pink-500{{/eq}} mr-1"></i>{{UserInfo.SexDisplay}}</span>
                    <span><i class="fas fa-birthday-cake text-pink-400 mr-1"></i>{{UserInfo.Age}}岁</span>
                    <span class="{{#if UserInfo.IsOnline}}text-green-500{{else}}text-gray-400{{/if}}">
                        <i class="fas fa-circle text-xs mr-1"></i>{{UserInfo.OnlineStatusDisplay}}
                    </span>
                </div>
                {{!-- 城市信息隐藏，用户可在详细资料中查看 --}}
                {{!-- {{#if UserInfo.City}}
                <div class="mt-1 text-sm text-gray-500">
                    <i class="fas fa-map-marker-alt mr-1"></i>{{UserInfo.City}}
                </div>
                {{/if}} --}}
            </div>
        </div>

        <!-- 个性签名 -->
        {{#if UserInfo.Remark}}
        <div class="bg-gradient-to-r from-primary/5 to-secondary/5 p-3 rounded-lg mb-6 xs-400:mb-4 xs-350:mb-3">
            <div class="flex items-start space-x-2">
                <i class="fas fa-quote-left text-primary/60 text-sm mt-1"></i>
                <p id="remarkText" class="text-sm text-text/80 italic leading-relaxed flex-1 text-center">{{{UserInfo.Remark}}}</p>
                <i class="fas fa-quote-right text-primary/60 text-sm mt-1"></i>
            </div>
        </div>
        {{/if}}

        <!-- 帖子和回复统计 -->
        <div class="grid grid-cols-2 gap-4 mb-6 xs-400:mb-4 xs-350:mb-3 xs-400:gap-2 xs-310:gap-1">
            <button onclick="viewPosts()" type="button" aria-label="查看帖子" class="bg-gradient-posts-s2 p-4 xs-350:p-3.5 xs-310:p-3 rounded-xl transition-all duration-300 border border-transparent cursor-pointer hover:shadow-[0_8px_25px_rgba(88,180,176,0.15)] hover:border-primary-alpha-30 hover:-translate-y-1">
                <div class="flex items-center justify-between">
                    <div class="text-left">
                        <p class="text-sky-700 text-sm xs-350:text-xs font-medium">帖子</p>
                        <p class="text-xl xs-350:text-lg font-bold text-sky-500">{{formatNumber Stats.PostCount}}</p>
                    </div>
                    <i class="fas fa-edit text-2xl text-sky-500 opacity-90"></i>
                </div>
            </button>
            <button onclick="viewReplies()" type="button" aria-label="查看回复" class="bg-gradient-replies-s2 p-4 xs-350:p-3.5 xs-310:p-3 rounded-xl transition-all duration-300 border border-transparent cursor-pointer hover:shadow-[0_8px_25px_rgba(88,180,176,0.15)] hover:border-primary-alpha-30 hover:-translate-y-1">
                <div class="flex items-center justify-between">
                    <div class="text-left">
                        <p class="text-teal-700 text-sm xs-350:text-xs font-medium">回复</p>
                        <p class="text-xl xs-350:text-lg font-bold text-primary">{{formatNumber Stats.ReplyCount}}</p>
                    </div>
                    <i class="fas fa-comments text-2xl text-primary opacity-90"></i>
                </div>
            </button>
        </div>

        <!-- 数据统计 -->
        <div class="grid grid-cols-3 gap-3 mb-4 xs-400:mb-3 xs-350:mb-2 xs-400:gap-2">
            {{#if IsAdmin}}
            <!-- 管理员版本 - 可点击 -->
            <a href="/bbs/banklist.aspx?key={{TargetUserId}}" class="block text-center p-3 bg-gray-50 rounded-lg relative cursor-pointer transition-colors hover:bg-gray-200 no-underline">
                <p id="goldCoinsDisplay" class="text-xl xs-350:text-lg font-medium text-text" data-full-value="{{Stats.Money}}">{{Stats.MoneyDisplay}}</p>
                <p class="text-sm xs-350:text-xs text-text-secondary">妖晶</p>
                <div id="goldCoinsTooltip" class="absolute z-50 bg-gray-800 text-white text-sm px-3 py-1 rounded-md shadow-lg opacity-0 pointer-events-none transition-all duration-200 ease-in-out whitespace-nowrap"></div>
            </a>
            {{else}}
            <!-- 普通用户版本 - 不可点击 -->
            <div class="text-center p-3 bg-gray-50 rounded-lg relative cursor-pointer transition-colors hover:bg-gray-200">
                <p id="goldCoinsDisplay" class="text-xl xs-350:text-lg font-medium text-text" data-full-value="{{Stats.Money}}">{{Stats.MoneyDisplay}}</p>
                <p class="text-sm xs-350:text-xs text-text-secondary">妖晶</p>
                <div id="goldCoinsTooltip" class="absolute z-50 bg-gray-800 text-white text-sm px-3 py-1 rounded-md shadow-lg opacity-0 pointer-events-none transition-all duration-200 ease-in-out whitespace-nowrap"></div>
            </div>
            {{/if}}
            <div class="text-center p-3 bg-gray-50 rounded-lg relative cursor-pointer transition-colors hover:bg-gray-200">
                <p id="levelDisplay" class="text-xl xs-350:text-lg font-medium text-text" data-full-value="{{Stats.Experience}}" data-title="{{Stats.Title}}">{{#if Stats.Level}}{{Stats.Level}}{{else}}0级{{/if}}</p>
                <p class="text-sm xs-350:text-xs text-text-secondary">等级</p>
                <div id="levelTooltip" class="absolute z-50 bg-gray-800 text-white text-sm px-3 py-1 rounded-md shadow-lg opacity-0 pointer-events-none transition-all duration-200 ease-in-out whitespace-nowrap"></div>
            </div>
            <div class="text-center p-3 bg-gray-50 rounded-lg relative cursor-pointer transition-colors hover:bg-gray-200">
                <p id="regTimeDisplay" class="text-xl xs-350:text-lg font-medium text-text" data-full-value="{{UserInfo.RegisterTime}}">{{UserInfo.RegisterDuration}}</p>
                <p class="text-sm xs-350:text-xs text-text-secondary">注册时长</p>
                <div id="regTimeTooltip" class="absolute z-50 bg-gray-800 text-white text-sm px-3 py-1 rounded-md shadow-lg opacity-0 pointer-events-none transition-all duration-200 ease-in-out whitespace-nowrap"></div>
            </div>
        </div>

        <!-- 详细资料链接 -->
        <button onclick="viewProfile()" class="w-full text-center text-primary font-medium relative inline-flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300 bg-transparent border border-transparent hover:bg-primary-alpha-10 hover:border-primary-alpha-20 hover:-translate-y-0.5">
            <i class="fas fa-id-card mr-2"></i>查看详细资料
        </button>
    </div>
</section>

<!-- 勋章墙区域 -->
{{#if Medals.HasMedals}}
<section class="px-4 mb-4 max-768:px-3 max-768:mb-3 xs-400:px-2 xs-400:mb-3">
    <div class="bg-white rounded-xl shadow-[0_2px_10px_rgba(0,0,0,0.1)] pt-5 px-5 pb-5 xs-400:pt-4 xs-400:px-4 xs-400:pb-4 xs-350:pt-4 xs-350:px-3 xs-350:pb-4 xs-310:pt-4 xs-310:px-2 xs-310:pb-4">
        <div class="flex items-center justify-between mb-2">
            <h3 class="text-lg font-semibold text-gray-800">勋章墙</h3>
            <span class="text-sm text-gray-500">共 {{Medals.MedalCount}} 枚</span>
        </div>

        <div class="medal-container">{{{Medals.MedalHtml}}}</div>
    </div>
</section>
{{/if}}

<!-- 用户动态区域 -->
{{#if DynamicsList}}
<section class="px-4 mb-4 max-768:px-3 max-768:mb-3 xs-400:px-2 xs-400:mb-3">
    <div class="bg-white rounded-xl shadow-[0_2px_10px_rgba(0,0,0,0.1)] pt-5 px-5 pb-5 xs-400:pt-4 xs-400:px-4 xs-400:pb-4 xs-350:pt-4 xs-350:px-3 xs-350:pb-3 xs-310:pt-4 xs-310:px-2 xs-310:pb-2">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">最新动态</h3>

        <div class="space-y-4">
            {{#each DynamicsList}}
            <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <i class="dynamic-icon" data-content="{{Content}}"></i>
                <div class="flex-1">
                    <p class="text-sm text-gray-700 break-words break-all [&_img]:max-w-full [&_iframe]:max-w-full">{{{Content}}}</p>
                    <p class="text-xs text-gray-500 mt-1">{{FriendlyTime}}</p>
                </div>
            </div>
            {{/each}}
        </div>

        <!-- 查看更多动态链接 -->
        <button onclick="loadMoreDynamics()" class="w-full text-center text-primary font-medium relative inline-flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300 bg-transparent border border-transparent hover:bg-primary-alpha-10 hover:border-primary-alpha-20 hover:-translate-y-0.5 mt-4">
            <i class="fas fa-stream mr-2"></i>查看更多动态
        </button>
    </div>
</section>
{{/if}}

<!-- 留言板区域 -->
<section class="px-4 mb-4 max-768:px-3 max-768:mb-3 xs-400:px-2 xs-400:mb-3">
    <div class="bg-white rounded-xl shadow-[0_2px_10px_rgba(0,0,0,0.1)] pt-5 px-5 pb-5 xs-400:pt-4 xs-400:px-4 xs-400:pb-4 xs-350:pt-4 xs-350:px-3 xs-350:pb-3 xs-310:pt-4 xs-310:px-2 xs-310:pb-2">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">空间留言</h3>

        <!-- 留言输入框 -->
        <div class="mb-6 relative after:content-[''] after:absolute after:bottom-[-12px] after:left-[-20px] after:right-[-20px] after:h-px after:bg-gradient-to-r after:from-transparent after:via-secondary/15 after:to-transparent">
            <form id="messageForm" action="{{ActionButtons.SubmitMessageUrl}}" method="post">
                <div class="flex space-x-3">
                    <div class="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center overflow-hidden">
                        {{#if CurrentUserAvatarUrl}}
                        {{#unless CurrentUserIsDefaultAvatar}}
                        <img src="{{CurrentUserAvatarUrl}}"
                             alt="我的头像"
                             class="w-full h-full object-fill rounded-lg"
                             onerror="this.style.display='none'; this.parentElement.innerHTML='<i class=&quot;fas fa-user text-gray-500&quot;></i>';">
                        {{else}}
                        <i class="fas fa-user text-gray-500"></i>
                        {{/unless}}
                        {{else}}
                        <i class="fas fa-user text-gray-500"></i>
                        {{/if}}
                    </div>
                    <div class="flex-1">
                        <textarea id="messageInput"
                                  name="content"
                                  placeholder="{{#if IsOwnSpace}}给自己留个言吧...{{else}}写下你的留言...{{/if}}"
                                  class="w-full p-3 border border-gray-200 bg-gray-50/60 rounded-lg resize-none focus:outline-none focus:border-primary focus:bg-white transition-colors focus:shadow-[0_0_0_2px_rgba(88,180,176,0.2)]"
                                  rows="3"
                                  required></textarea>
                        <div class="flex justify-between items-center mt-2">
                            <div></div>
                            <button type="submit" class="bg-gradient-to-r from-primary to-secondary text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-primary/30">
                                <i class="fas fa-paper-plane mr-2"></i>发送留言
                            </button>
                        </div>
                    </div>
                </div>
                <!-- 隐藏字段 -->
                <input type="hidden" name="action" value="add">
                <input type="hidden" name="touserid" value="{{TargetUserId}}">
                <input type="hidden" name="siteid" value="{{SiteInfo.SiteId}}">
                <input type="hidden" name="classid" value="{{SiteInfo.ClassId}}">
                <input type="hidden" name="sid" value="{{HiddenFields.Sid}}">
            </form>
        </div>

        <!-- 留言列表 -->
        {{#if MessagesList}}
        <h4 class="text-sm font-medium text-text-secondary mb-2">最新留言</h4>
        <div class="space-y-0">
            {{#each MessagesList}}
            <div class="py-4 border-b border-gray-100">
                <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center overflow-hidden relative">
                        <!-- 首字母fallback -->
                        <span class="avatar-fallback-small" data-fallback="true">{{firstChar AuthorNickname}}</span>
                        <!-- 头像图片 -->
                        {{#if AuthorAvatarUrl}}
                        {{#unless IsDefaultAvatar}}
                        <img src="{{AuthorAvatarUrl}}"
                             alt="{{AuthorNickname}}"
                             class="w-8 h-8 object-fill absolute top-0 left-0 z-[1] hidden rounded-lg"
                             data-avatar-src="{{AuthorAvatarUrl}}"
                             onload="handleSmallAvatarLoad(this)"
                             onerror="handleSmallAvatarError(this)">
                        {{/unless}}
                        {{/if}}
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center justify-between mb-1">
                            {{#if AuthorSpaceUrl}}
                            <a href="{{AuthorSpaceUrl}}" class="font-medium text-sm text-primary hover:text-primary/80 transition-colors">{{AuthorNickname}}</a>
                            {{else}}
                            <span class="font-medium text-sm text-gray-700">{{AuthorNickname}}</span>
                            {{/if}}
                            <span class="text-xs text-gray-500 cursor-pointer relative"
                                  data-detail-time="{{DetailTime}}"
                                  onmouseenter="showTimeTooltip(this)"
                                  onmouseleave="hideTimeTooltip(this)">{{FriendlyTime}}</span>
                        </div>
                        <p class="retext text-sm text-gray-700 break-words break-all [&_img]:max-w-full [&_iframe]:max-w-full">{{{Content}}}</p>
                    </div>
                </div>
            </div>
            {{/each}}
        </div>
        {{else}}
        <div class="text-center py-8">
            <i class="fas fa-comments text-4xl text-gray-300 mb-2"></i>
            <p class="text-gray-500">暂时木有留言，快抢沙发哦</p>
        </div>
        {{/if}}

        <!-- 查看更多留言链接 -->
        {{#if MessagesList}}
        <button onclick="loadMoreMessages()" class="w-full text-center text-primary font-medium relative inline-flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300 bg-transparent border border-transparent hover:bg-primary-alpha-10 hover:border-primary-alpha-20 hover:-translate-y-0.5 mt-4">
            <i class="fas fa-comments mr-2"></i>查看更多留言
        </button>
        {{/if}}
    </div>
</section>

{{!-- 空间访问统计 - 已注释，如需恢复请取消注释 --}}
{{
!-- <div class="px-4 mb-6">
    <div class="text-center text-sm text-gray-500">
        {{SpaceStats.VisitStatsDisplay
}}
    </div>
</div> --}}

<!-- 头像悬浮层 -->
{{#if UserInfo.AvatarUrl}}
{{#unless UserInfo.IsDefaultAvatar}}
<div class="avatar-overlay"
     id="avatarOverlay"
     role="dialog"
     aria-modal="true"
     aria-labelledby="avatar-title"
     aria-describedby="avatar-description"
     onclick="hideAvatarOverlay(event)">
    <div class="avatar-overlay-content">
        <img src="{{UserInfo.AvatarUrl}}"
             alt="{{UserInfo.Nickname}}的高清头像"
             class="avatar-overlay-image"
             id="avatarOverlayImage"
             role="img"
             aria-labelledby="avatar-title">
        <!-- 隐藏的可访问性标题 -->
        <span id="avatar-title" class="sr-only">{{UserInfo.Nickname}}的头像</span>
        <span id="avatar-description" class="sr-only">按ESC键或点击外部区域关闭</span>
    </div>
</div>
{{/unless}}
{{/if}}

<script src="/NetCSS/JS/HyperLink.js" defer></script>
<script src="/Template/JS/Components/AvatarHandler.js?v=1"></script>
<script src="/Template/JS/Components/DynamicIcons.js"></script>
<script src="/Template/JS/Components/TimeTooltip.js"></script>
<script src="/Template/JS/Components/NicknameScroll.js?v=1"></script>
<script src="/Template/JS/Pages/UserInfo.js?v=7"></script>

<script>
// 常量定义
const AVATAR_CONSTANTS = {
    SIZES: {
        LARGE: 'large',
        MEDIUM: 'medium',
        SMALL: 'small'
    },
    SELECTORS: {
        OVERLAY: 'avatarOverlay',
        CONTAINER: '.avatar-container',
        MAIN_AVATAR: '.avatar-container img[data-avatar-src]',
        OVERLAY_CONTENT: '.avatar-overlay-content',
        OVERLAY_IMAGE: '.avatar-overlay-image'
    },
    CLASSES: {
        SHOW: 'show',
        CLICKABLE: 'clickable'
    }
};

// 头像悬浮层命名空间
const AvatarOverlay = {
    /**
     * 显示头像悬浮层
     */
    show() {
        const overlay = document.getElementById(AVATAR_CONSTANTS.SELECTORS.OVERLAY);
        if (overlay) {
            overlay.classList.add(AVATAR_CONSTANTS.CLASSES.SHOW);
            // 可访问性：设置焦点
            overlay.focus();
        }
    },

    /**
     * 隐藏头像悬浮层
     * @param {Event} event - 事件对象
     */
    hide(event) {
        // 如果点击的是图片本身，不关闭悬浮层
        if (event && event.target.classList.contains(AVATAR_CONSTANTS.SELECTORS.OVERLAY_IMAGE.slice(1))) {
            return;
        }

        const overlay = document.getElementById(AVATAR_CONSTANTS.SELECTORS.OVERLAY);
        if (overlay) {
            overlay.classList.remove(AVATAR_CONSTANTS.CLASSES.SHOW);
        }
    }
};

// 向后兼容的全局函数
function showAvatarOverlay() {
    AvatarOverlay.show();
}

function hideAvatarOverlay(event) {
    AvatarOverlay.hide(event);
}

/**
 * 扩展AvatarHandler，在头像加载成功后添加点击功能
 */
const AvatarClickFeature = {
    /**
     * 为头像容器添加点击功能
     * @param {HTMLElement} container - 头像容器元素
     */
    enableClick(container) {
        if (container) {
            container.onclick = showAvatarOverlay;
            container.classList.add(AVATAR_CONSTANTS.CLASSES.CLICKABLE);
        }
    },

    /**
     * 初始化扩展功能
     */
    init() {
        // 保存原始的handleLoad方法
        const originalHandleLoad = AvatarHandler.handleLoad;

        // 扩展handleLoad方法
        AvatarHandler.handleLoad = function(img, size = 'auto') {
            // 调用原始方法
            originalHandleLoad.call(this, img, size);

            // 如果是主头像（large尺寸），添加点击功能
            if (size === AVATAR_CONSTANTS.SIZES.LARGE) {
                const container = img.closest(AVATAR_CONSTANTS.SELECTORS.CONTAINER);
                AvatarClickFeature.enableClick(container);
            }
        };
    }
};

// 向后兼容函数
function addAvatarClickFeature() {
    AvatarClickFeature.init();
}

/**
 * 页面初始化管理器
 */
const UserInfoPageManager = {
    /**
     * 初始化缓存的头像
     */
    initCachedAvatar() {
        const mainAvatar = document.querySelector(AVATAR_CONSTANTS.SELECTORS.MAIN_AVATAR);
        if (mainAvatar && mainAvatar.complete && mainAvatar.naturalWidth > 0) {
            // 图片已经加载成功，手动添加点击功能
            const container = mainAvatar.closest(AVATAR_CONSTANTS.SELECTORS.CONTAINER);
            AvatarClickFeature.enableClick(container);
        }
    },

    /**
     * 初始化悬浮层事件
     */
    initOverlayEvents() {
        const avatarOverlayContent = document.querySelector(AVATAR_CONSTANTS.SELECTORS.OVERLAY_CONTENT);
        if (avatarOverlayContent) {
            // 阻止点击悬浮层内容时关闭
            avatarOverlayContent.addEventListener('click', function(e) {
                e.stopPropagation();
            });
        }
    },

    /**
     * 初始化键盘事件
     */
    initKeyboardEvents() {
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                AvatarOverlay.hide();
            }
        });
    },

    /**
     * 主初始化函数
     */
    init() {
        UserInfoPage.init();
        AvatarClickFeature.init();
        this.initCachedAvatar();
        this.initOverlayEvents();
        this.initKeyboardEvents();
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    UserInfoPageManager.init();
});
</script>

<!-- 添加数据属性以支持新的组件系统 -->
<div class="hidden">
    <span data-back-url="{{SiteInfo.BackUrl}}"></span>
    <span data-send-message-url="{{{ActionButtons.SendMessageUrl}}}"></span>
    <span data-posts-url="{{{Stats.PostsUrl}}}"></span>
    <span data-replies-url="{{{Stats.RepliesUrl}}}"></span>
    <span data-profile-url="{{{ActionButtons.ViewDetailUrl}}}"></span>
    <span data-more-dynamics-url="{{{ActionButtons.ViewMoreDynamicsUrl}}}"></span>
    <span data-more-messages-url="{{{ActionButtons.ViewMoreMessagesUrl}}}"></span>
</div>