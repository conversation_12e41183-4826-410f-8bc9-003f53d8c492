/**
 * 头像服务
 * 统一管理所有页面的头像加载、错误处理和超时检测
 * 消除重复代码，提供一致的头像显示体验
 * 
 * @version 1.0
 * <AUTHOR>
 * @date 2025-01-07
 */

import { AvatarConfig, DEFAULT_CONFIG } from '../types/CommonTypes.js';

/**
 * 头像服务类
 * 提供统一的头像加载、错误处理和超时检测功能
 */
export class AvatarService {
    private static instance: AvatarService;
    private readonly sizeClasses = {
        small: 'w-8 h-8',
        medium: 'w-12 h-12',
        large: 'w-20 h-20'
    };

    /**
     * 获取单例实例
     */
    public static getInstance(): AvatarService {
        if (!AvatarService.instance) {
            AvatarService.instance = new AvatarService();
        }
        return AvatarService.instance;
    }

    /**
     * 处理头像加载成功
     * @param img 图片元素
     * @param size 尺寸类型
     */
    public static handleLoad(img: HTMLImageElement, size: 'small' | 'medium' | 'large' | 'auto' = 'auto'): void {
        AvatarService.getInstance().handleAvatarLoad(img, size);
    }

    /**
     * 处理头像加载失败
     * @param img 图片元素
     * @param size 尺寸类型
     */
    public static handleError(img: HTMLImageElement, size: 'small' | 'medium' | 'large' | 'auto' = 'auto'): void {
        AvatarService.getInstance().handleAvatarError(img, size);
    }

    /**
     * 设置头像加载超时检查
     * @param img 图片元素
     * @param timeout 超时时间（毫秒）
     */
    public static setLoadTimeout(img: HTMLImageElement, timeout?: number): void {
        AvatarService.getInstance().setAvatarTimeout(img, timeout);
    }

    /**
     * 初始化页面中的所有头像
     */
    public static initPageAvatars(): void {
        AvatarService.getInstance().initializeAllAvatars();
    }

    /**
     * 创建头像元素
     * @param config 头像配置
     */
    public static createAvatar(config: AvatarConfig): HTMLElement {
        return AvatarService.getInstance().createAvatarElement(config);
    }

    /**
     * 处理头像加载成功
     */
    public handleAvatarLoad(img: HTMLImageElement, size: 'small' | 'medium' | 'large' | 'auto' = 'auto'): void {
        const containerClass = this.getContainerClass(img, size);
        const container = img.closest(containerClass);
        const fallback = container?.querySelector('[data-fallback="true"]') as HTMLElement;

        // 图片加载成功，显示图片并隐藏首字母 fallback
        img.classList.remove('hidden');
        if (fallback) {
            fallback.classList.add('hidden');
        }
    }

    /**
     * 处理头像加载失败
     */
    public handleAvatarError(img: HTMLImageElement, size: 'small' | 'medium' | 'large' | 'auto' = 'auto'): void {
        const containerClass = this.getContainerClass(img, size);
        const container = img.closest(containerClass);
        const fallback = container?.querySelector('[data-fallback="true"]') as HTMLElement;

        // 图片加载失败，隐藏图片并显示首字母 fallback
        img.classList.add('hidden');
        img.style.display = 'none';
        if (fallback) {
            fallback.classList.remove('hidden');
        }
    }

    /**
     * 设置头像加载超时检查
     */
    public setAvatarTimeout(img: HTMLImageElement, timeout?: number): void {
        const timeoutMs = timeout || DEFAULT_CONFIG.AVATAR_TIMEOUT;
        
        setTimeout(() => {
            // 如果图片仍然隐藏且没有完成加载，强制显示首字母
            if (img.classList.contains('hidden') && !img.complete) {
                this.handleAvatarError(img);
            }
        }, timeoutMs);
    }

    /**
     * 初始化页面中的所有头像
     */
    public initializeAllAvatars(): void {
        document.querySelectorAll('img[data-avatar-src]').forEach(img => {
            const imgElement = img as HTMLImageElement;
            
            if (imgElement.complete) {
                if (imgElement.naturalWidth > 0) {
                    // 图片加载成功
                    this.handleAvatarLoad(imgElement);
                } else {
                    // 图片加载失败
                    this.handleAvatarError(imgElement);
                }
            } else {
                // 设置超时检查
                this.setAvatarTimeout(imgElement);
            }
        });
    }

    /**
     * 创建头像元素
     */
    public createAvatarElement(config: AvatarConfig): HTMLElement {
        const container = document.createElement('div');
        container.className = `relative ${this.sizeClasses[config.size === 'auto' ? 'medium' : config.size]} rounded-full overflow-hidden bg-gray-150 flex items-center justify-center`;

        // 创建图片元素
        const img = document.createElement('img');
        img.src = config.src;
        img.alt = config.fallbackText;
        img.className = 'w-full h-full object-cover hidden';
        img.setAttribute('data-avatar-src', config.src);

        // 绑定加载事件
        img.onload = () => this.handleAvatarLoad(img, config.size);
        img.onerror = () => this.handleAvatarError(img, config.size);

        // 创建fallback元素
        const fallback = document.createElement('div');
        fallback.className = config.size === 'small' ? 'avatar-fallback-small' : 'avatar-fallback-main';
        fallback.setAttribute('data-fallback', 'true');
        fallback.textContent = this.getFallbackText(config.fallbackText);

        container.appendChild(img);
        container.appendChild(fallback);

        // 设置超时检查
        if (config.timeout) {
            this.setAvatarTimeout(img, config.timeout);
        }

        return container;
    }

    /**
     * 获取容器选择器
     */
    private getContainerClass(img: HTMLImageElement, size: 'small' | 'medium' | 'large' | 'auto'): string {
        // 使用更具体的选择器，避免选中图片元素本身
        if (size !== 'auto') {
            const sizeClass = this.sizeClasses[size].replace(' ', '.');
            return `div.${sizeClass}`;
        }

        // 自动检测图片尺寸
        if (img.classList.contains('w-8')) return 'div.w-8.h-8';
        if (img.classList.contains('w-12')) return 'div.w-12.h-12';
        if (img.classList.contains('w-20')) return 'div.w-20.h-20';
        if (img.classList.contains('w-24')) return 'div.w-24.h-24';
        if (img.classList.contains('w-28')) return 'div.w-28.h-28';

        // 默认返回小尺寸
        return 'div.w-8.h-8';
    }

    /**
     * 获取fallback文本（通常是昵称的第一个字符）
     */
    private getFallbackText(text: string): string {
        if (!text) return '?';
        
        // 获取第一个字符，支持中文和英文
        const firstChar = text.charAt(0);
        return firstChar.toUpperCase();
    }
}

// ==================== 全局函数，供模板调用 ====================

/**
 * 处理大头像加载成功（向后兼容）
 * @param img 图片元素
 */
export function handleAvatarLoad(img: HTMLImageElement): void {
    AvatarService.handleLoad(img, 'large');
}

/**
 * 处理大头像加载失败（向后兼容）
 * @param img 图片元素
 */
export function handleAvatarError(img: HTMLImageElement): void {
    AvatarService.handleError(img, 'large');
}

/**
 * 处理小头像加载成功（向后兼容）
 * @param img 图片元素
 */
export function handleSmallAvatarLoad(img: HTMLImageElement): void {
    AvatarService.handleLoad(img, 'small');
}

/**
 * 处理小头像加载失败（向后兼容）
 * @param img 图片元素
 */
export function handleSmallAvatarError(img: HTMLImageElement): void {
    AvatarService.handleError(img, 'small');
}

/**
 * 处理中等头像加载成功（向后兼容）
 * @param img 图片元素
 */
export function handleMediumAvatarLoad(img: HTMLImageElement): void {
    AvatarService.handleLoad(img, 'medium');
}

/**
 * 处理中等头像加载失败（向后兼容）
 * @param img 图片元素
 */
export function handleMediumAvatarError(img: HTMLImageElement): void {
    AvatarService.handleError(img, 'medium');
}

/**
 * 检查头像加载超时（向后兼容）
 * @param img 图片元素
 * @param timeoutMs 超时时间
 */
export function checkAvatarLoadTimeout(img: HTMLImageElement, timeoutMs: number = DEFAULT_CONFIG.AVATAR_TIMEOUT): void {
    AvatarService.setLoadTimeout(img, timeoutMs);
}

// 导出默认实例
export default AvatarService;
