﻿using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using System;
using System.Data;
using System.Text.RegularExpressions;
using Dapper;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Book_View_mod : MyPageWap
    {
        private const long MIN_ADDITIONAL_REWARD = 1000;
        public const int TOKEN_EXPIRE_MINUTES = 60; // token有效期60分钟

        private readonly string a = PubConstant.GetAppString("InstanceName");

        public wap_bbs_Model bbsVo = new wap_bbs_Model();

        public string action = "";

        public string id = "";

        public string lpage = "";

        public string INFO = "";

        public string ERROR = "";

        public string[] facelist;

        public string[] facelistImg;

        public string[] stypelist;

        public string face = "";

        public string stype = "";

        public string titlemax = "2";

        public string contentmax = "2";

        public string logMessage = "";

        public bool isAuthor = false;

        public bool isFreeMoney = false;

        public string additionalRewardMessage = "";

        public string formToken = "";

        public bool hasPermission = false;
        public bool isAdmin = false;

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            lpage = GetRequestValue("lpage");

            // 1. 基础登录验证
            IsLogin(userid, "bbs/book_view_mod.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id + "&amp;lpage=" + lpage);

            // 2. 检查功能是否关闭
            if ("1".Equals(WapTool.GetArryString(classVo.smallimg, '|', 28)) && "|00|01|".IndexOf(userVo.managerlvl) < 0)
            {
                ShowTipInfo("修改帖子功能已关闭！", "wapindex.aspx?siteid=" + siteid + "&amp;classid=" + classVo.childid);
                return;
            }

            try
            {
                // 3. 验证帖子ID
                if (string.IsNullOrEmpty(id) || !long.TryParse(id, out long postId))
                {
                    ERROR = "帖子ID无效！";
                    return;
                }

                // 4. 初始化数据
                InitializeFaceAndTypeList();  // 初始化表情和类型列表
                InitializeBbsData();         // 初始化帖子数据
                ValidateBbsData();           // 验证帖子数据
                RemoveTitlePrefix();         // 移除标题前缀

                // Correctly set titlemax and contentmax based on configuration or defaults for frontend messages and validation
                string configuredTitleMin = WapTool.GetArryString(classVo.smallimg, '|', 24);
                if (WapTool.IsNumeric(configuredTitleMin) && configuredTitleMin != "0")
                {
                    this.titlemax = configuredTitleMin;
                }
                else
                {
                    this.titlemax = "5"; // Default minimum title length
                }

                string configuredContentMin = WapTool.GetArryString(classVo.smallimg, '|', 25);
                if (WapTool.IsNumeric(configuredContentMin) && configuredContentMin != "0")
                {
                    this.contentmax = configuredContentMin;
                }
                else
                {
                    this.contentmax = "15"; // Default minimum content length
                }

                // 5. 统一的权限验证
                isAdmin = CheckManagerLvl("04", classVo.adminusername);

                if (!string.IsNullOrEmpty(userid) &&
                    bbsVo != null &&
                    userid == bbsVo.book_pub.ToString())
                {
                    hasPermission = true;
                    isAuthor = true;
                }
                else if (isAdmin)
                {
                    hasPermission = true;
                }

                if (!hasPermission)
                {
                    ERROR = "您没有权限修改该帖子！";
                    return;
                }

                // 6. Token处理 - 修改这部分
                string tokenKey = "formTokenList_mod_" + id;
                if (action != "gomod" && !IsPostBack) // 只在首次加载时生成token
                {
                    formToken = GenerateFormToken(tokenKey);
                    Session["formTokenExpire"] = DateTime.Now.AddMinutes(TOKEN_EXPIRE_MINUTES);
                }

                // 7. 处理修改操作
                if (action == "gomod")
                {
                    string token = Request["token"];
                    DateTime? tokenExpire = Session["formTokenExpire"] as DateTime?;

                    if (!ValidateFormToken(tokenKey, token))
                    {
                        ERROR = "安全验证失败，请刷新页面重试";
                        return;
                    }

                    if (!tokenExpire.HasValue || tokenExpire.Value < DateTime.Now)
                    {
                        ERROR = "表单已过期，请刷新页面重试";
                        return;
                    }

                    try
                    {
                        string currentTitle = GetRequestValue("book_title").Trim();
                        string currentContent = GetRequestValue("book_content"); // Get raw content for length check and prefill

                        // Updated minimum length validation using this.titlemax and this.contentmax
                        if (currentTitle.Length < long.Parse(this.titlemax) || currentContent.Trim().Length < long.Parse(this.contentmax))
                        {
                            INFO = "NULL";
                            // Preserve user's attempt for pre-filling the form
                            bbsVo.book_title = currentTitle;
                            bbsVo.book_content = currentContent;
                            return;
                        }

                        UpdateBbsContent();
                        if (INFO != "TITLEMAX") // Ensure INFO wasn't set to TITLEMAX by UpdateBbsContent
                        {
                            HandleAdditionalReward();
                            wap_bbs_BLL bll = new wap_bbs_BLL(a);
                            bll.Update(bbsVo);

                            WapTool.ClearDataBBS("bbs" + siteid + classid);
                            Session["formToken"] = null;
                            Session["formTokenExpire"] = null;
                            ClearFormToken(tokenKey);
                            INFO = "OK";
                        }
                    }
                    catch (Exception ex)
                    {
                        ERROR = "修改失败：" + ex.Message;
                    }
                }
            }
            catch (Exception ex)
            {
                ERROR = "系统错误：" + ex.Message;
            }
        }

        private void InitializeBbsData()
        {
            try
            {
                wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(a);
                bbsVo = wap_bbs_BLL.GetModel(long.Parse(id));

                // 验证帖子是否存在
                if (bbsVo == null)
                {
                    throw new Exception("帖子不存在！");
                }

                // 验证作者身份和管理权限
                isAuthor = userid == bbsVo.book_pub.ToString();
                if (!isAuthor)
                {
                    CheckManagerLvl("04", classVo.adminusername, "bbs/book_view_admin.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;lpage=" + lpage + "&amp;id=" + id);
                }

                isFreeMoney = bbsVo.freeMoney > 0;
            }
            catch (Exception ex)
            {
                throw new Exception("初始化帖子数据失败：" + ex.Message);
            }
        }

        private void InitializeFaceAndTypeList()
        {
            if (classVo.bbsFace.IndexOf('_') < 0)
            {
                classVo.bbsFace = "_";
            }
            facelist = classVo.bbsFace.Split('_')[0].Split('|');
            facelistImg = classVo.bbsFace.Split('_')[1].Split('|');
            if (classVo.bbsType.IndexOf('_') < 0)
            {
                classVo.bbsType = "_";
            }
            stypelist = classVo.bbsType.Split('_')[0].Split('|');
        }

        private void ValidateBbsData()
        {
            if (bbsVo == null)
            {
                ShowTipInfo("已删除！或不存在！", "");
            }
            else if (bbsVo.ischeck == 1L)
            {
                ShowTipInfo("正在审核中！", "");
            }
            else if (bbsVo.book_classid.ToString() != classid)
            {
                ShowTipInfo("栏目ID对不上！可能没有传classid值！", "");
            }
            else if (bbsVo.islock == 1L)
            {
                ShowTipInfo("此帖已锁！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bbsVo.book_classid + "&amp;id=" + bbsVo.id + "&amp;lpage=" + lpage);
            }
            else if (bbsVo.islock == 2L)
            {
                ShowTipInfo("此帖已结！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bbsVo.book_classid + "&amp;id=" + bbsVo.id + "&amp;lpage=" + lpage);
            }
            // 新增: 如果是userid=1000的帖子,只有自己可以修改
            else if (long.Parse(bbsVo.book_pub.ToString()) == 1000L && long.Parse(userid) != 1000L)
            {
                ShowTipInfo("此帖仅作者可以修改！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bbsVo.book_classid + "&amp;id=" + bbsVo.id + "&amp;lpage=" + lpage);
            }

            if (bbsVo.userid.ToString() != siteid)
            {
                base.Response.End();
            }
            if (bbsVo.book_classid.ToString() != classid)
            {
                base.Response.End();
            }
        }

        private void RemoveTitlePrefix()
        {
            int num = bbsVo.book_title.LastIndexOf("]");
            if (num > 0)
            {
                bbsVo.book_title = bbsVo.book_title.Substring(num + 1, bbsVo.book_title.Length - num - 1);
            }
        }

        private void UpdateBbsContent()
        {
            // 先获取原始标题，用于长度校验
            string rawTitle = GetRequestValue("book_title").Trim();

            // 长度校验通过后，对标题进行HTML编码
            bbsVo.book_title = System.Web.HttpUtility.HtmlEncode(rawTitle);

            // 修改: 使用 PreserveCodeContent 方法处理 book_content
            bbsVo.book_content = PreserveCodeContent(GetRequestValue("book_content"));
            bbsVo.book_title = bbsVo.book_title.Replace("/", "／").Replace("[", "［").Replace("]", "］");
            bbsVo.book_img = GetSafeRequestValue("book_img");
            face = GetSafeRequestValue("face");
            stype = GetSafeRequestValue("stype");
            bbsVo.book_content = WapTool.URLtoWAP(bbsVo.book_content);
            ValidateSpecialPost();
            AddTypeAndFaceToTitle();
            UpdateBbsMetadata();
        }

        // 新增: PreserveCodeContent 方法，该方法用于保护 [code] 标签内的内容不被 HTML 编码
        private string PreserveCodeContent(string content)
        {
            // 使用正则表达式分割内容，保留 [code] 标签
            string[] parts = Regex.Split(content, @"(\[code\].*?\[/code\])", RegexOptions.Singleline);
            for (int i = 0; i < parts.Length; i++)
            {
                // 只对非 [code] 标签内的内容进行 HTML 编码
                if (i % 2 == 0)
                {
                    parts[i] = System.Web.HttpUtility.HtmlEncode(parts[i]);
                }
            }
            // 将所有部分重新组合成一个字符串
            return string.Join("", parts);
        }

        private string GetSafeRequestValue(string key)
        {
            return System.Web.HttpUtility.HtmlEncode(GetRequestValue(key));
        }

        private void ValidateSpecialPost()
        {
            string arryString = WapTool.GetArryString(classVo.smallimg, '|', 21);
            if (arryString != "")
            {
                arryString = "_" + arryString + "_";
                bool flag = false;
                if (int.Parse(bbsVo.viewtype.ToString()) > 2 || bbsVo.book_content.IndexOf("[/reply]") > 0 || bbsVo.book_content.IndexOf("[/buy]") > 0 || bbsVo.book_content.IndexOf("[/coin]") > 0 || bbsVo.book_content.IndexOf("[/grade]") > 0)
                {
                    flag = true;
                }
                if (flag && !IsCheckManagerLvl("|00|01|03|04|", classVo.adminusername) && arryString.IndexOf("_" + userVo.SessionTimeout + "_") < 0)
                {
                    ShowTipInfo("您当前的身份不允许发特殊帖。", "bbs/book_view_mod.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id);
                }
            }
        }

        private void AddTypeAndFaceToTitle()
        {
            stype = stype.Replace("类别", "");
            face = face.Replace("表情", "");
            if (stype != "")
            {
                bbsVo.book_title = "[" + stype + "]" + bbsVo.book_title;
            }
            if (face.Trim().Length > 3 && face.Substring(face.Length - 3, 3).ToLower() == "gif")
            {
                bbsVo.book_title = "[img]face/" + face + "[/img]" + bbsVo.book_title;
            }
            bbsVo.book_title = bbsVo.book_title.Trim();
            if (bbsVo.book_title.Length > 200)
            {
                bbsVo.book_title = bbsVo.book_title.Substring(0, 200);
            }
            if (WapTool.GetArryString(classVo.smallimg, '|', 41) == "1" && stype.Trim() == "")
            {
                ShowTipInfo("类别不能为空！", "bbs/book_view_mod.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id + "&amp;lpage=" + lpage);
            }
        }

        private void UpdateBbsMetadata()
        {
            // 只有当用户ID不是1000时，才记录修改历史
            if (userVo.userid != 1000L)  // 使用 1000L 表示 long 类型的 1000
            {
                string text3 = "{" + userVo.nickname + "(ID" + userVo.userid + ")修改此帖" + $"{DateTime.Now:MM-dd HH:mm}" + "}<br/>";
                bbsVo.whylock = text3 + bbsVo.whylock;
            }

            // 更新时间的逻辑保持不变
            string arryString2 = WapTool.GetArryString(classVo.smallimg, '|', 43);
            if (arryString2 == "2")
            {
                bbsVo.reDate = DateTime.Now;
            }
        }

        private void HandleAdditionalReward()
        {
            if (!isFreeMoney)
            {
                string additionalReward = GetSafeRequestValue("additionalReward");
                if (!string.IsNullOrEmpty(additionalReward) && long.TryParse(additionalReward, out long additionalRewardAmount))
                {
                    if (additionalRewardAmount >= MIN_ADDITIONAL_REWARD && isAuthor)
                    {
                        if (userVo.money >= additionalRewardAmount)
                        {
                            UpdateRewardAndUserBalance(additionalRewardAmount);
                        }
                        else
                        {
                            additionalRewardMessage = "insufficient_balance";
                        }
                    }
                    else if (additionalRewardAmount > 0 && !isAuthor)
                    {
                        additionalRewardMessage = "not_author";
                    }
                    else if (additionalRewardAmount > 0 && additionalRewardAmount < MIN_ADDITIONAL_REWARD)
                    {
                        additionalRewardMessage = "min_amount";
                    }
                }
            }
        }

        private void UpdateRewardAndUserBalance(long additionalRewardAmount)
        {
            // ✅ 使用TransactionHelper进行安全的事务性资金操作
            string connectionString = PubConstant.GetConnectionString(a);
            long siteIdLong = DapperHelper.SafeParseLong(siteid, "站点ID");
            long userIdLong = DapperHelper.SafeParseLong(userid, "用户ID");

            TransactionHelper.ExecuteMoneyTransaction(connectionString, (connection, transaction) =>
            {
                // 1. 扣除用户金币
                string updateUserSql = "UPDATE [user] SET [money] = [money] - @Amount WHERE siteid = @SiteId AND userid = @UserId";
                connection.Execute(updateUserSql, new {
                    Amount = additionalRewardAmount,
                    SiteId = siteIdLong,
                    UserId = userIdLong
                }, transaction);

                // 2. 更新帖子悬赏金额（在事务内确保一致性）
                bbsVo.sendMoney += additionalRewardAmount;
            });

            // 3. 记录银行日志（在事务外执行，使用SaveBankLogWithBalance避免死锁）
            long newBalance = userVo.money - additionalRewardAmount;
            SaveBankLogWithBalance(userid, "追加悬赏", "-" + additionalRewardAmount.ToString(), userid, userVo.nickname, "追加悬赏[" + id + "]", newBalance);
            additionalRewardMessage = "success," + additionalRewardAmount;
            userVo.money -= additionalRewardAmount;
        }
    }
}