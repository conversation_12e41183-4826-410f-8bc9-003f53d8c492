﻿using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using System;
using System.Collections.Generic;
using System.Text;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.WebSite.BBS.Service;

namespace YaoHuo.Plugin.Games.ChuiNiu
{
    public class Index : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public StringBuilder strhtml = new StringBuilder();

        public string ERROR = "";

        public long showRe = 5L;

        public List<wap2_games_chuiniu_Model> listVo = new List<wap2_games_chuiniu_Model>();

        public wap2_games_config_Model configVo = new wap2_games_config_Model();

        public sys_ad_show_Model adVo = new sys_ad_show_Model();

        public List<wap2_games_chat_Model> relistVo = null;

        protected void Page_Load(object sender, EventArgs e)
        {
            // ✅ 统一获取数据库连接字符串
            string connectionString = PubConstant.GetConnectionString(a);

            if (GetRequestValue("action") == "del")
            {
                IsCheckManagerLvl("|00|01|", classVo.adminusername, GetUrlQueryString());
                string requestValue = GetRequestValue("id");

                // ✅ 使用DapperHelper进行安全的游戏记录删除操作
                string deleteGameSql = "DELETE FROM wap2_games_chuiniu WHERE siteid = @SiteId AND id = @Id";
                DapperHelper.Execute(connectionString, deleteGameSql, new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    Id = DapperHelper.SafeParseLong(requestValue, "游戏记录ID")
                });
            }

            // ✅ 使用AdvertisementService统一广告查询逻辑
            adVo = AdvertisementService.GetGamesAdvertisementSafely(siteid, connectionString);
            wap2_games_config_BLL wap2_games_config_BLL = new wap2_games_config_BLL(a);
            // ⚠️ 安全警告：游戏配置查询仍使用不安全的BLL方法，存在SQL注入风险
            configVo = wap2_games_config_BLL.GetModel("gameen='chuiniu' and siteid=" + siteid);
            if (configVo == null)
            {
                configVo = new wap2_games_config_Model();
                configVo.siteid = siteVo.siteid;
                configVo.gameEn = "chuiniu";
                configVo.gameCn = "吹牛";
                configVo.config = "100|20000|95|10|5";
                configVo.todayTimes = 0L;
                configVo.todayMoney = 0L;
                configVo.updateTime = DateTime.Now;
                configVo.addtime = DateTime.Now;
                wap2_games_config_BLL.Add(configVo);
            }
            wap2_games_chuiniu_BLL wap2_games_chuiniu_BLL = new wap2_games_chuiniu_BLL(a);
            // ⚠️ 安全警告：游戏列表查询仍使用不安全的BLL方法，存在SQL注入风险
            listVo = wap2_games_chuiniu_BLL.GetListVo(100L, 1L, " state=0 and siteid=" + siteid + " ", "*", "id", 100L, 1);
            if (WapTool.GetArryString(configVo.config, '|', 4) != "")
            {
                showRe = long.Parse(WapTool.GetArryString(configVo.config, '|', 4));
            }
            if (showRe > 0L)
            {
                wap2_games_chat_BLL wap2_games_chat_BLL = new wap2_games_chat_BLL(a);
                relistVo = wap2_games_chat_BLL.GetListVo(showRe, 1L, " siteid=" + siteid + " ", "*", "id", 100L, 1);
            }
            string fcountSubMoneyFlag = WapTool.GetFcountSubMoneyFlag(siteid, userid, IP);
            if (fcountSubMoneyFlag.IndexOf("chuiniu") < 0)
            {
                VisiteCount("在玩<a href=\"" + http_start + "games/chuiniu/index.aspx\">吹牛</a>");

                // ✅ 使用DapperHelper进行安全的游戏访问标记操作
                string updateFcountSql = "UPDATE [fcount] SET SubMoneyFlag = @SubMoneyFlag WHERE fip = @IP AND fuserid = @SiteId AND userid = @UserId";
                DapperHelper.Execute(connectionString, updateFcountSql, new {
                    SubMoneyFlag = DapperHelper.LimitLength(fcountSubMoneyFlag + "chuiniu,", 500),
                    IP = DapperHelper.LimitLength(IP, 50),
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    UserId = DapperHelper.SafeParseLong(userid, "用户ID")
                });
            }
        }
    }
}