<?xml version="1.0" encoding="UTF-8"?>
<rules>
	<rule name="YaohuoRefererCheck" stopProcessing="true">
		<match url="^(bbs/upload/|album/upload/).*" />
		<conditions>
			<!-- 只检查非空referer，如果referer存在则必须是yaohuo.me域名 -->
			<add input="{HTTP_REFERER}" pattern="^https?://" />
			<add input="{HTTP_REFERER}" pattern="^https?://(www\.|wap\.)?yaohuo\.me/.*" negate="true" />
		</conditions>
		<action type="CustomResponse" statusCode="403" statusReason="Forbidden" statusDescription="禁止访问 - 仅允许来自yaohuo.me的访问" />
	</rule>
	<rule name="portal_1">
		<match url="^(.*/)*wapindex-([0-9]+)-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/wapindex.aspx?siteid={R:2}&amp;classid={R:3}&amp;{R4}" />
	</rule>
	<rule name="portal_2">
		<match url="^login\.html\?*(.*)$" />
		<action type="Rewrite" url="waplogin.aspx?{R:1}" />
	</rule>
	<rule name="portal_3">
		<match url="^(.*/)*myfile-([0-9]+)-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/myfile.aspx?siteid={R:2}&amp;classid={R:3}&amp;{R4}" />
	</rule>
	<rule name="portal_bbs_1">
		<match url="^(.*/)*bbs-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/bbs/view.aspx?id={R:2}&amp;{R:3}" />
	</rule>
	<rule name="portal_bbs_2">
		<match url="^(.*/)*bbslist-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/bbs/list.aspx?classid={R:2}&amp;{R:3}" />
	</rule>
	<rule name="portal_bbs_3">
		<match url="^(.*/)*bbslist-([0-9]+)-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/bbs/list.aspx?classid={R:2}&amp;page={R:3}&amp;{R:4}" />
	</rule>
	<rule name="portal_bbs_4">
		<match url="^(.*/)*bbsre-([0-9]+)-([0-9]+)-([0-9]+)-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/bbs/book_re.aspx?siteid={R:2}&amp;classid={R:3}&amp;id={R:4}&amp;page={R:5}&amp;{R:6}" />
	</rule>
	<rule name="Redirect_AdLink_NonFriendlyUrls" stopProcessing="true">
		<match url="^adlink/(.*)" />
		<conditions>
			<add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
			<add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
		</conditions>
		<action type="Redirect" url="/Pages/AdLink/{R:1}" redirectType="Permanent" />
	</rule>
	<rule name="portal_adlink_1">
		<match url="^(.*/)*link-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/Pages/AdLink/view.aspx?id={R:2}&amp;{R:3}" />
	</rule>
	<rule name="portal_adlink_2">
		<match url="^(.*/)*adlinklist-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/Pages/AdLink/list.aspx?classid={R:2}&amp;{R:3}" />
	</rule>
	<rule name="portal_adlink_3">
		<match url="^(.*/)*adlinklist-([0-9]+)-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/Pages/AdLink/list.aspx?classid={R:2}&amp;page={R:3}&amp;{R:4}" />
	</rule>
	<rule name="portal_adlink_4">
		<match url="^(.*/)*adlinkre-([0-9]+)-([0-9]+)-([0-9]+)-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/Pages/AdLink/book_re.aspx?siteid={R:2}&amp;classid={R:3}&amp;id={R:4}&amp;page={R:5}&amp;{R:6}" />
	</rule>
	<rule name="portal_album_1">
		<match url="^(.*/)*album-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/album/view.aspx?id={R:2}&amp;{R:3}" />
	</rule>
	<rule name="portal_album_2">
		<match url="^(.*/)*albumlist-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/album/list.aspx?classid={R:2}&amp;{R:3}" />
	</rule>
	<rule name="portal_album_3">
		<match url="^(.*/)*albumlist-([0-9]+)-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/album/list.aspx?classid={R:2}&amp;page={R:3}&amp;{R:4}" />
	</rule>
	<rule name="portal_album_4">
		<match url="^(.*/)*albumre-([0-9]+)-([0-9]+)-([0-9]+)-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/album/book_re.aspx?siteid={R:2}&amp;classid={R:3}&amp;id={R:4}&amp;page={R:5}&amp;{R:6}" />
	</rule>
	<rule name="portal_wml_1">
		<match url="^(.*/)*wml-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/wml/view.aspx?id={R:2}&amp;{R:3}" />
	</rule>
	<rule name="portal_wml_2">
		<match url="^(.*/)*wmllist-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/wml/list.aspx?classid={R:2}&amp;{R:3}" />
	</rule>
	<rule name="portal_wml_3">
		<match url="^(.*/)*wmllist-([0-9]+)-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/wml/list.aspx?classid={R:2}&amp;page={R:3}&amp;{R:4}" />
	</rule>
	<rule name="portal_wml_4">
		<match url="^(.*/)*wmlre-([0-9]+)-([0-9]+)-([0-9]+)-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/wml/book_re.aspx?siteid={R:2}&amp;classid={R:3}&amp;id={R:4}&amp;page={R:5}&amp;{R:6}" />
	</rule>
	<rule name="portal_xinzhang_1">
		<match url="^(.*/)*xinzhang-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/xinzhang/view.aspx?id={R:2}&amp;{R:3}" />
	</rule>
	<rule name="portal_xinzhang_2">
		<match url="^(.*/)*xinzhanglist-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/xinzhang/list.aspx?classid={R:2}&amp;{R:3}" />
	</rule>
	<rule name="portal_xinzhang_3">
		<match url="^(.*/)*xinzhanglist-([0-9]+)-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/xinzhang/list.aspx?classid={R:2}&amp;page={R:3}&amp;{R:4}" />
	</rule>
	<rule name="portal_xinzhang_4">
		<match url="^(.*/)*xinzhangre-([0-9]+)-([0-9]+)-([0-9]+)-([0-9]+).html\?*(.*)$" />
		<action type="Rewrite" url="{R:1}/xinzhang/book_re.aspx?siteid={R:2}&amp;classid={R:3}&amp;id={R:4}&amp;page={R:5}&amp;{R:6}" />
	</rule>
	<rule name="WWW Redirect" stopProcessing="true">
		<match url=".*" />
		<conditions>
			<add input="{HTTP_HOST}" pattern="^yaohw.com$" />
		</conditions>
		<action type="Redirect" url="https://yaohuo.me/{R:0}" redirectType="Permanent" />
	</rule>
	<rule name="RedirectFaceToBbsFace" stopProcessing="true">
		<match url="^face/(.*)" />
		<action type="Rewrite" url="bbs/face/{R:1}" />
	</rule>
	<rule name="RedirectNetImages" stopProcessing="true">
		<match url="^NetImages/(.*)" />
		<action type="Rewrite" url="NetCSS/IMG/NetImages/{R:1}" />
	</rule>
	<rule name="RedirectTupian" stopProcessing="true">
		<match url="^Tupian/(.*)" />
		<action type="Rewrite" url="NetCSS/IMG/Tupian/{R:1}" />
	</rule>
	<rule name="RedirectClan" stopProcessing="true">
		<match url="^Clan/(.*)" />
		<action type="Rewrite" url="Games/Clan/{R:1}" />
	</rule>
	<rule name="RedirectTuChuang" stopProcessing="true">
		<match url="^TuChuang/(.*)" />
		<action type="Rewrite" url="Pages/TuChuang/{R:1}" />
	</rule>
	<rule name="RedirectBlog" stopProcessing="true">
		<match url="^Blog/(.*)" />
		<action type="Rewrite" url="Pages/Blog/{R:1}" />
	</rule>
	<rule name="RedirectChinabankWAP" stopProcessing="true">
		<match url="^Chinabank_WAP/(.*)" />
		<action type="Rewrite" url="Pages/Chinabank_WAP/{R:1}" />
	</rule>
	<rule name="RedirectClass" stopProcessing="true">
		<match url="^Class/(.*)" />
		<action type="Rewrite" url="Admin/Class/{R:1}" />
	</rule>
	<rule name="RedirectVisiteCount" stopProcessing="true">
		<match url="^VisiteCount/(.*)" />
		<action type="Rewrite" url="Admin/VisiteCount/{R:1}" />
	</rule>
	<rule name="RedirectWapStyle" stopProcessing="true">
		<match url="^WapStyle/(.*)" />
		<action type="Rewrite" url="Admin/WapStyle/{R:1}" />
	</rule>
	<rule name="RedirectWEB" stopProcessing="true">
		<match url="^WEB/(.*)" />
		<action type="Rewrite" url="Admin/WEB/{R:1}" />
	</rule>
	<rule name="RedirectHtmlFiles" stopProcessing="true">
		<match url="^(Go|HongBao|KA)\.html$" />
		<action type="Rewrite" url="Pages/{R:1}.html" />
	</rule>
	<rule name="RedirectFavicon" stopProcessing="true">
		<match url="^favicon\.ico$" />
		<action type="Rewrite" url="NetCSS/IMG/Favicon.ico" />
	</rule>
	<rule name="RedirectRobots" stopProcessing="true">
		<match url="^robots\.txt$" />
		<action type="Rewrite" url="Pages/Robots.txt" />
	</rule>
	<rule name="RedirectAds" stopProcessing="true">
		<match url="^ads\.txt$" />
		<action type="Rewrite" url="Pages/Ads.txt" />
	</rule>
	<rule name="RedirectSearch" stopProcessing="true">
		<match url="^Search/(.*)" />
		<action type="Rewrite" url="Pages/Search/{R:1}" />
	</rule>
	<rule name="RedirectIMAX" stopProcessing="true">
		<match url="^IMAX$" />
		<action type="Rewrite" url="Pages/IMAX/IMAX.aspx" />
	</rule>
	<rule name="RedirectTemplateDefault" stopProcessing="true">
		<match url="^Template/default/(.*)" />
		<action type="Rewrite" url="NetCSS/CSS/Index/Default/{R:1}" />
	</rule>
	<rule name="RewriteDefaultAndIndexToWapIndex" stopProcessing="true">
	  <match url="^(Default|Index)\.aspx$|^index\.html$" />
	  <action type="Rewrite" url="wapindex.aspx" appendQueryString="true" />
	</rule>
	<rule name="RewriteBbsIndexToBbsDefault" stopProcessing="true">
	  <match url="^bbs/index\.html$" />
	  <action type="Rewrite" url="bbs/default.aspx" appendQueryString="true" />
	</rule>
</rules>