using System;
using System.Collections.Generic;
using System.Linq;
using YaoHuo.Plugin.WebSite.Services.Config.Models;
using YaoHuo.Plugin.Template.Models;

namespace YaoHuo.Plugin.WebSite.Services.Config
{
    /// <summary>
    /// BBS模块配置服务
    /// 提供BBS相关的配置数据和业务逻辑
    /// </summary>
    public static class BBSConfigService
    {
        #region 身份配置相关

        /// <summary>
        /// 获取所有启用的身份选项
        /// </summary>
        /// <returns>身份选项列表</returns>
        public static List<IdentityOptionModel> GetIdentityOptions()
        {
            try
            {
                var configRoot = ConfigService.GetConfig<IdentityConfigRoot>("IdentityConfigs");

                var enabledIdentities = configRoot.IdentityTypes
                    .Where(option => option.Enabled)
                    .OrderBy(option => option.SortOrder)
                    .ThenBy(option => option.Id)
                    .Select(option => new IdentityOptionModel
                    {
                        Id = option.Id,
                        Name = option.Name,
                        DisplayName = option.DisplayName,
                        Price = option.Price,
                        CoinPrice = option.CoinPrice,
                        Period = option.Period,
                        Type = option.Type,
                        IconUrl = option.IconUrl,
                        NameCssClass = option.NameCssClass,
                        ColorCode = option.ColorCode,
                        IconFileName = option.IconFileName,
                        Privileges = option.Privileges,
                        IsColorNickname = option.IsColorNickname,
                        ColorOptions = option.ColorOptions?.Select(co => new ColorOptionModel
                        {
                            Color = co.Color,
                            Name = co.Name,
                            TargetId = co.TargetId,
                            ColorCode = co.ColorCode,
                            IsDefault = co.IsDefault
                        }).ToList() ?? new List<ColorOptionModel>(),
                        Enabled = option.Enabled,
                        SortOrder = option.SortOrder
                    })
                    .ToList();

                return enabledIdentities;
            }
            catch
            {
                return new List<IdentityOptionModel>();
            }
        }

        /// <summary>
        /// 获取可购买的身份选项（排除管理员身份）
        /// </summary>
        /// <returns>可购买的身份选项列表</returns>
        public static List<IdentityOptionModel> GetPurchasableIdentityOptions()
        {
            try
            {
                var configRoot = ConfigService.GetConfig<IdentityConfigRoot>("IdentityConfigs");

                var purchasableIdentities = configRoot.IdentityTypes
                    .Where(option => option.Enabled && option.Type != "admin") // 排除管理员身份
                    .OrderBy(option => option.SortOrder)
                    .ThenBy(option => option.Id)
                    .Select(option => new IdentityOptionModel
                    {
                        Id = option.Id,
                        Name = option.Name,
                        DisplayName = option.DisplayName,
                        Price = option.Price,
                        CoinPrice = option.CoinPrice,
                        Period = option.Period,
                        Type = option.Type,
                        IconUrl = option.IconUrl,
                        NameCssClass = option.NameCssClass,
                        ColorCode = option.ColorCode,
                        IconFileName = option.IconFileName,
                        Privileges = option.Privileges,
                        IsColorNickname = option.IsColorNickname,
                        ColorOptions = option.ColorOptions?.Select(co => new ColorOptionModel
                        {
                            Color = co.Color,
                            Name = co.Name,
                            TargetId = co.TargetId,
                            ColorCode = co.ColorCode,
                            IsDefault = co.IsDefault
                        }).ToList() ?? new List<ColorOptionModel>(),
                        Enabled = option.Enabled,
                        SortOrder = option.SortOrder
                    })
                    .ToList();

                return purchasableIdentities;
            }
            catch
            {
                return new List<IdentityOptionModel>();
            }
        }

        /// <summary>
        /// 获取特定身份选项
        /// </summary>
        /// <param name="identityId">身份ID</param>
        /// <returns>身份选项</returns>
        public static IdentityOptionModel GetIdentityOption(int identityId)
        {
            try
            {
                var identities = GetIdentityOptions();
                return identities.FirstOrDefault(i => i.Id == identityId);
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 检查身份是否启用
        /// </summary>
        /// <param name="identityId">身份ID</param>
        /// <returns>是否启用</returns>
        public static bool IsIdentityEnabled(int identityId)
        {
            try
            {
                var identity = GetIdentityOption(identityId);
                return identity != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取身份配置信息
        /// </summary>
        /// <returns>配置元数据</returns>
        public static ConfigMetadata GetIdentityConfigInfo()
        {
            try
            {
                var configRoot = ConfigService.GetConfig<IdentityConfigRoot>("IdentityConfigs");
                return configRoot?.Config;
            }
            catch
            {
                return null;
            }
        }

        #endregion

        #region 勋章配置相关

        // 勋章比对性能优化：缓存用户勋章HashSet
        private static readonly Dictionary<string, HashSet<string>> _userMedalCache = new Dictionary<string, HashSet<string>>();
        private static readonly object _medalCacheLock = new object();

        /// <summary>
        /// 获取所有启用的申请勋章
        /// </summary>
        /// <returns>申请勋章列表</returns>
        public static List<ApplyMedalConfig> GetApplyMedals()
        {
            try
            {
                var config = ConfigService.GetConfig<MedalConfigRoot>("MedalConfigs");
                return config.ApplyMedals
                    .Where(m => m.Enabled)
                    .OrderBy(m => m.SortOrder)
                    .ThenBy(m => m.Name)
                    .ToList();
            }
            catch
            {
                return GetFallbackApplyMedals();
            }
        }

        /// <summary>
        /// 获取所有启用的购买勋章
        /// </summary>
        /// <returns>购买勋章列表</returns>
        public static List<PurchaseMedalConfig> GetPurchaseMedals()
        {
            try
            {
                var config = ConfigService.GetConfig<MedalConfigRoot>("MedalConfigs");
                return config.PurchaseMedals
                    .Where(m => m.Enabled)
                    .OrderBy(m => m.SortOrder)
                    .ThenBy(m => m.Name)
                    .ToList();
            }
            catch
            {
                return GetFallbackPurchaseMedals();
            }
        }

        /// <summary>
        /// 检查勋章是否已拥有（处理路径等同性）
        /// 🚀 性能优化：保持向后兼容的单个勋章检查方法
        /// </summary>
        /// <param name="medalFileName">勋章文件名</param>
        /// <param name="userMoneyName">用户勋章数据</param>
        /// <returns>是否已拥有</returns>
        public static bool IsMedalOwned(string medalFileName, string userMoneyName)
        {
            if (string.IsNullOrEmpty(medalFileName) || string.IsNullOrEmpty(userMoneyName))
                return false;

            // 获取用户勋章HashSet（带缓存优化）
            var ownedMedalsSet = GetUserMedalHashSet(userMoneyName);

            // 标准化勋章文件名进行比对
            string normalizedMedalFileName = NormalizeMedalFileName(medalFileName);

            // HashSet查找：O(1)复杂度
            bool isOwned = ownedMedalsSet.Contains(normalizedMedalFileName);

            return isOwned;
        }

        /// <summary>
        /// 🚀 性能优化：批量检查多个勋章的拥有状态
        /// 复杂度从O(n×m)优化到O(n+m)
        /// </summary>
        /// <param name="medalFileNames">勋章文件名列表</param>
        /// <param name="userMoneyName">用户勋章数据</param>
        /// <returns>勋章拥有状态字典</returns>
        public static Dictionary<string, bool> BatchCheckMedalOwnership(IEnumerable<string> medalFileNames, string userMoneyName)
        {
            var result = new Dictionary<string, bool>();

            if (medalFileNames == null || string.IsNullOrEmpty(userMoneyName))
            {
                foreach (var fileName in medalFileNames ?? Enumerable.Empty<string>())
                {
                    result[fileName] = false;
                }
                return result;
            }

            // 获取用户勋章HashSet（带缓存优化）
            var ownedMedalsSet = GetUserMedalHashSet(userMoneyName);

            // 批量检查，每个勋章只需O(1)时间
            foreach (var medalFileName in medalFileNames)
            {
                if (string.IsNullOrEmpty(medalFileName))
                {
                    result[medalFileName] = false;
                    continue;
                }

                string normalizedFileName = NormalizeMedalFileName(medalFileName);
                bool isOwned = ownedMedalsSet.Contains(normalizedFileName);
                result[medalFileName] = isOwned;


            }

            return result;
        }

        /// <summary>
        /// 🚀 性能优化：获取用户勋章HashSet（带缓存）
        /// </summary>
        /// <param name="userMoneyName">用户勋章数据</param>
        /// <returns>标准化后的勋章文件名HashSet</returns>
        public static HashSet<string> GetUserMedalHashSet(string userMoneyName)
        {
            if (string.IsNullOrEmpty(userMoneyName))
                return new HashSet<string>();

            lock (_medalCacheLock)
            {
                // 检查缓存
                if (_userMedalCache.TryGetValue(userMoneyName, out var cachedSet))
                {
                    return cachedSet;
                }

                // 缓存未命中，解析并缓存
                var medalSet = new HashSet<string>();
                var rawMedals = userMoneyName.Split('|')
                    .Where(m => !string.IsNullOrWhiteSpace(m))
                    .Select(m => m.Trim());

                foreach (var medal in rawMedals)
                {
                    string normalized = NormalizeMedalFileName(medal);
                    if (!string.IsNullOrEmpty(normalized))
                    {
                        medalSet.Add(normalized);
                    }
                }

                // 缓存结果（限制缓存大小，避免内存泄漏）
                if (_userMedalCache.Count < 1000) // 最多缓存1000个用户的勋章数据
                {
                    _userMedalCache[userMoneyName] = medalSet;
                }

                return medalSet;
            }
        }

        /// <summary>
        /// 解析用户拥有的勋章列表
        /// </summary>
        /// <param name="userMoneyName">用户勋章数据</param>
        /// <returns>勋章文件名列表</returns>
        public static List<string> ParseUserMedals(string userMoneyName)
        {
            if (string.IsNullOrEmpty(userMoneyName))
                return new List<string>();

            return userMoneyName.Split('|')
                .Where(m => !string.IsNullOrWhiteSpace(m))
                .Select(m => m.Trim())
                .ToList();
        }

        /// <summary>
        /// 标准化勋章文件名（处理路径等同性）
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>标准化后的文件名</returns>
        private static string NormalizeMedalFileName(string fileName)
        {
            if (string.IsNullOrEmpty(fileName))
                return "";

            // 移除 /bbs/medal/ 前缀，使 /bbs/medal/67.gif 等同于 67.gif
            if (fileName.StartsWith("/bbs/medal/"))
            {
                return fileName.Substring("/bbs/medal/".Length);
            }

            // 对于XinZhang路径的勋章，统一移除前导斜杠
            // 配置文件中可能是 /XinZhang/... 而用户数据中是 XinZhang/...
            if (fileName.StartsWith("/XinZhang/"))
            {
                return fileName.Substring(1); // 移除前导斜杠
            }

            // 其他情况保持原样
            return fileName;
        }

        /// <summary>
        /// 获取备用申请勋章配置
        /// </summary>
        private static List<ApplyMedalConfig> GetFallbackApplyMedals()
        {
            return new List<ApplyMedalConfig>
            {
                new ApplyMedalConfig
                {
                    Id = "newbie_progress",
                    Name = "新人进步",
                    FileName = "67.gif",
                    IconUrl = "/bbs/medal/67.gif",
                    Description = "累计发表10篇资源帖，展现新人的积极进步精神",
                    ShortDescription = "累计发表10资源帖",
                    Category = "achievement",
                    SortOrder = 1
                }
            };
        }

        /// <summary>
        /// 获取备用购买勋章配置
        /// </summary>
        private static List<PurchaseMedalConfig> GetFallbackPurchaseMedals()
        {
            return new List<PurchaseMedalConfig>
            {
                new PurchaseMedalConfig
                {
                    Id = "basic_medal",
                    Name = "初级勋章",
                    FileName = "初级勋章.gif",
                    IconUrl = "/bbs/medal/初级勋章.gif",
                    Description = "入门级别的基础勋章，开启收集之旅的第一步",
                    Category = "popular",
                    Price = "10000",
                    BuyUrl = "/XinZhang/book_view_buy.aspx?&id=20&lpage=1&ordertype=1",
                    SortOrder = 1
                }
            };
        }

        #endregion

        #region 派币帖验证码配置相关

        /// <summary>
        /// 检查用户在派币帖回复时是否需要验证码
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="freeMoney">帖子派币金额</param>
        /// <param name="freeLeftMoney">剩余派币金额（可选，-1表示不检查派币完成状态）</param>
        /// <returns>是否需要验证码</returns>
        public static bool RequiresCaptchaForFreeMoneyPost(long userId, long freeMoney, long freeLeftMoney = -1)
        {
            try
            {
                // 如果不是派币帖，不需要验证码
                if (freeMoney <= 0) return false;

                var config = ConfigService.GetConfig<FreeMoneyPostCaptchaConfig>("FreeMoneyPostCaptcha");

                // 配置未启用
                if (config == null || !config.Enabled) return false;

                // 🔧 新增：如果启用了"派币完成后跳过验证码"功能，且派币已完成，则不需要验证码
                if (config.SkipCaptchaWhenCompleted && freeLeftMoney >= 0 && freeLeftMoney <= 0)
                {
                    return false;
                }

                // 检查派币金额是否达到触发阈值
                if (freeMoney < config.MinFreeMoneyAmount) return false;

                // 检查是否在白名单中（免验证码）
                if (config.ExemptUserIds != null && config.ExemptUserIds.Contains(userId)) return false;

                // 检查是否在需要验证码的用户列表中
                return config.UserIds != null && config.UserIds.Contains(userId);
            }
            catch (Exception ex)
            {
                // 配置异常时的降级处理
                System.Diagnostics.Debug.WriteLine($"派币帖验证码配置检查异常: {ex.Message}");
                return false; // 默认不启用验证码，确保系统可用性
            }
        }

        /// <summary>
        /// 获取派币帖验证码配置信息
        /// </summary>
        /// <returns>配置信息</returns>
        public static FreeMoneyPostCaptchaConfig GetFreeMoneyPostCaptchaConfig()
        {
            try
            {
                return ConfigService.GetConfig<FreeMoneyPostCaptchaConfig>("FreeMoneyPostCaptcha");
            }
            catch
            {
                return GetFallbackFreeMoneyPostCaptchaConfig();
            }
        }

        /// <summary>
        /// 获取备用派币帖验证码配置
        /// </summary>
        private static FreeMoneyPostCaptchaConfig GetFallbackFreeMoneyPostCaptchaConfig()
        {
            return new FreeMoneyPostCaptchaConfig
            {
                Enabled = false,
                UserIds = new List<long>(),
                CaptchaProvider = "gocaptcha",
                MinFreeMoneyAmount = 1,
                SkipCaptchaWhenCompleted = true,
                ExemptUserIds = new List<long>(),
                DebugMode = false
            };
        }

        /// <summary>
        /// 刷新派币帖验证码配置缓存
        /// </summary>
        public static void RefreshFreeMoneyPostCaptchaCache()
        {
            ConfigService.RefreshConfig("FreeMoneyPostCaptcha");
        }

        #endregion

        #region 通用方法

        /// <summary>
        /// 刷新身份配置缓存
        /// </summary>
        public static void RefreshIdentityCache()
        {
            ConfigService.RefreshConfig("IdentityConfigs");
        }

        /// <summary>
        /// 刷新勋章配置缓存
        /// </summary>
        public static void RefreshMedalCache()
        {
            ConfigService.RefreshConfig("MedalConfigs");
        }

        /// <summary>
        /// 🚀 性能优化：清空勋章缓存
        /// </summary>
        public static void ClearMedalCache()
        {
            lock (_medalCacheLock)
            {
                _userMedalCache.Clear();
            }
        }

        /// <summary>
        /// 🚀 性能优化：清空特定用户的勋章缓存
        /// </summary>
        /// <param name="userMoneyName">用户勋章数据</param>
        public static void ClearUserMedalCache(string userMoneyName)
        {
            if (string.IsNullOrEmpty(userMoneyName))
                return;

            lock (_medalCacheLock)
            {
                _userMedalCache.Remove(userMoneyName);
            }
        }

        /// <summary>
        /// 🚀 性能优化：获取勋章缓存统计信息
        /// </summary>
        /// <returns>缓存统计信息</returns>
        public static string GetMedalCacheStats()
        {
            lock (_medalCacheLock)
            {
                return $"勋章缓存项数: {_userMedalCache.Count}/1000";
            }
        }

        /// <summary>
        /// 🚀 性能测试：对比新旧勋章比对方法的性能
        /// </summary>
        /// <param name="userMoneyName">用户勋章数据</param>
        /// <param name="testMedalCount">测试勋章数量</param>
        /// <returns>性能测试结果</returns>
        public static string PerformanceBenchmark(string userMoneyName, int testMedalCount = 100)
        {
            if (string.IsNullOrEmpty(userMoneyName))
                return "无效的用户勋章数据";

            var testMedals = new List<string>();
            for (int i = 0; i < testMedalCount; i++)
            {
                testMedals.Add($"test_medal_{i}.gif");
            }

            var sw = System.Diagnostics.Stopwatch.StartNew();

            // 测试旧方法（模拟）：每次都重新解析
            sw.Restart();
            for (int i = 0; i < testMedalCount; i++)
            {
                IsMedalOwned(testMedals[i], userMoneyName);
            }
            sw.Stop();
            long oldMethodTime = sw.ElapsedMilliseconds;

            // 测试新方法：批量检查
            sw.Restart();
            BatchCheckMedalOwnership(testMedals, userMoneyName);
            sw.Stop();
            long newMethodTime = sw.ElapsedMilliseconds;

            // 清空缓存重新测试
            ClearMedalCache();
            sw.Restart();
            BatchCheckMedalOwnership(testMedals, userMoneyName);
            sw.Stop();
            long newMethodTimeNoCache = sw.ElapsedMilliseconds;

            double improvement = oldMethodTime > 0 ? (double)oldMethodTime / newMethodTime : 0;

            return $"性能测试结果 (测试 {testMedalCount} 个勋章):\n" +
                   $"旧方法: {oldMethodTime}ms\n" +
                   $"新方法(无缓存): {newMethodTimeNoCache}ms\n" +
                   $"新方法(有缓存): {newMethodTime}ms\n" +
                   $"性能提升: {improvement:F2}x";
        }

        /// <summary>
        /// 获取BBS配置统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public static string GetBBSConfigStats()
        {
            try
            {
                var identityCount = GetIdentityOptions().Count;
                var applyMedalCount = GetApplyMedals().Count;
                var purchaseMedalCount = GetPurchaseMedals().Count;
                var medalCacheStats = GetMedalCacheStats();

                // 添加派币帖验证码配置统计
                var captchaConfig = GetFreeMoneyPostCaptchaConfig();
                var captchaStats = $"派币帖验证码: {(captchaConfig.Enabled ? "启用" : "禁用")}, 监控用户: {captchaConfig.UserIds.Count}";

                return $"身份选项: {identityCount}, 申请勋章: {applyMedalCount}, 购买勋章: {purchaseMedalCount}, {medalCacheStats}, {captchaStats}";
            }
            catch
            {
                return $"获取BBS配置统计失败";
            }
        }

        #endregion

        #region 动态图标配置服务

        /// <summary>
        /// 获取动态图标配置
        /// </summary>
        /// <returns>动态图标配置</returns>
        public static DynamicIconConfigRoot GetDynamicIconConfig()
        {
            try
            {
                var config = ConfigService.GetConfig<DynamicIconConfigRoot>("DynamicIconConfigs", "StaticData");
                return config;
            }
            catch
            {
                // 返回默认配置
                return GetDefaultDynamicIconConfig();
            }
        }

        /// <summary>
        /// 根据内容匹配动态图标
        /// </summary>
        /// <param name="content">动态内容</param>
        /// <returns>匹配结果</returns>
        public static DynamicIconMatchResult MatchDynamicIcon(string content)
        {
            try
            {
                if (string.IsNullOrEmpty(content))
                {
                    var config = GetDynamicIconConfig();
                    return DynamicIconMatchResult.Default(config.DefaultIcon);
                }

                var iconConfig = GetDynamicIconConfig();

                // 按优先级排序，查找第一个匹配的规则
                var matchedRule = iconConfig.IconRules
                    .Where(rule => rule.Enabled)
                    .OrderBy(rule => rule.Priority)
                    .FirstOrDefault(rule => rule.IsMatch(content));

                if (matchedRule != null)
                {
                    return DynamicIconMatchResult.Success(matchedRule);
                }
                else
                {
                    return DynamicIconMatchResult.Default(iconConfig.DefaultIcon);
                }
            }
            catch
            {
                var defaultConfig = GetDefaultDynamicIconConfig();
                return DynamicIconMatchResult.Default(defaultConfig.DefaultIcon);
            }
        }

        /// <summary>
        /// 获取所有启用的图标规则
        /// </summary>
        /// <returns>图标规则列表</returns>
        public static List<DynamicIconRule> GetEnabledIconRules()
        {
            try
            {
                var config = GetDynamicIconConfig();
                return config.IconRules
                    .Where(rule => rule.Enabled)
                    .OrderBy(rule => rule.Priority)
                    .ToList();
            }
            catch
            {
                return new List<DynamicIconRule>();
            }
        }

        /// <summary>
        /// 刷新动态图标配置缓存
        /// </summary>
        public static void RefreshDynamicIconCache()
        {
            try
            {
                // 对于StaticData模块的配置，使用特殊的缓存键
                ConfigService.RefreshConfig("DynamicIconConfigs");
            }
            catch
            {
                // 静默处理异常
            }
        }

        /// <summary>
        /// 获取默认动态图标配置（fallback）
        /// </summary>
        /// <returns>默认配置</returns>
        private static DynamicIconConfigRoot GetDefaultDynamicIconConfig()
        {
            return new DynamicIconConfigRoot
            {
                Config = new ConfigMetadata
                {
                    Version = "1.0",
                    Description = "默认动态图标配置（fallback）"
                },
                DefaultIcon = new DefaultIconConfig
                {
                    Icon = "fas fa-circle-notch",
                    Color = "text-gray-400",
                    Description = "默认动态图标"
                },
                IconRules = new List<DynamicIconRule>
                {
                    new DynamicIconRule { Id = "search", Pattern = "正在论坛查询", MatchType = "startsWith", Icon = "fas fa-search", Color = "text-primary", Priority = 1 },
                    new DynamicIconRule { Id = "reply", Pattern = "回复了帖子", MatchType = "startsWith", Icon = "fas fa-reply", Color = "text-purple-400", Priority = 2 },
                    new DynamicIconRule { Id = "newpost", Pattern = "发表新帖", MatchType = "startsWith", Icon = "fas fa-feather-alt", Color = "text-emerald-500", Priority = 3 }
                }
            };
        }

        #endregion
    }
}