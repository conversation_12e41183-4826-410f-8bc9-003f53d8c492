namespace YaoHuo.Plugin.OAuth
{
    /// <summary>
    /// OAuth 2.0 常量定义
    /// 消除硬编码，提高代码可维护性
    /// </summary>
    public static class OAuthConstants
    {
        #region OAuth 2.0 错误代码
        public const string INVALID_REQUEST = "invalid_request";
        public const string INVALID_CLIENT = "invalid_client";
        public const string INVALID_GRANT = "invalid_grant";
        public const string UNSUPPORTED_GRANT_TYPE = "unsupported_grant_type";
        public const string UNSUPPORTED_RESPONSE_TYPE = "unsupported_response_type";
        public const string ACCESS_DENIED = "access_denied";
        public const string SERVER_ERROR = "server_error";
        #endregion

        #region 令牌管理模式
        public const string TOKEN_MODE_NORMAL = "normal";
        public const string TOKEN_MODE_SMART = "smart";
        public const string TOKEN_MODE_AGGRESSIVE = "aggressive";
        #endregion

        #region CSRF 令牌名称
        public const string CSRF_ADMIN_FORM = "OAuth_Admin_Form";
        // 移除 CSRF_AUTHORIZE_FORM - 授权端点不再使用 CSRF 验证
        public const string CSRF_INDEX_DELETE = "OAuth_Index_Delete";
        public const string CSRF_INDEX_REVOKE = "OAuth_Index_Revoke";
        public const string CSRF_INDEX_REVOKE_ALL = "OAuth_Index_RevokeAll";
        #endregion

        #region HTTP 状态码
        public const int HTTP_OK = 200;
        public const int HTTP_BAD_REQUEST = 400;
        public const int HTTP_UNAUTHORIZED = 401;
        public const int HTTP_FORBIDDEN = 403;
        public const int HTTP_INTERNAL_SERVER_ERROR = 500;
        #endregion

        #region HTTP 方法
        public const string HTTP_METHOD_GET = "GET";
        public const string HTTP_METHOD_POST = "POST";
        #endregion

        #region OAuth 2.0 标准值
        public const string SCOPE_PROFILE = "profile";
        public const string GRANT_AUTHORIZATION_CODE = "authorization_code";
        public const string RESPONSE_TYPE_CODE = "code";
        public const string PKCE_METHOD_S256 = "S256";
        public const string TOKEN_TYPE_BEARER = "Bearer";
        #endregion

        #region 分隔符
        public const string SEPARATOR_REDIRECT_URI = ";";
        public const string SEPARATOR_SCOPE_PRIMARY = " ";
        public const string SEPARATOR_SCOPE_ALTERNATIVE = ",";
        #endregion

        #region 管理员级别
        public const string MANAGER_SUPER_ADMIN = "|00|";
        #endregion

        #region 内容类型
        public const string CONTENT_TYPE_JSON = "application/json; charset=utf-8";
        public const string CONTENT_TYPE_HTML = "text/html; charset=utf-8";
        #endregion
    }
} 