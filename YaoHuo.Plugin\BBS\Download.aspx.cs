﻿using System;
using Dapper;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class DownLoad : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string id = "";

        public string book_id = "";

        public string RndPath = "";

        public wap2_attachment_Model bookVo = new wap2_attachment_Model();

        public string KL_NotDownAndUpload = PubConstant.GetAppString("KL_DownCheck");

        public string KL_DownCheckReferrer = PubConstant.GetAppString("KL_DownCheckReferrer");

        public string KL_JUMPURL_bbs = PubConstant.GetAppString("KL_JUMPURL_bbs");

        public string RealPath = "";

        public string refer = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            id = GetRequestValue("id");
            book_id = GetRequestValue("book_id");
            RndPath = GetRequestValue("RndPath");
            RealPath = base.Server.MapPath("/");
            string classid = GetRequestValue("classid");
            // 登录检查
            IsLogin(userid, "bbs/book_view.aspx?siteid=" + siteid + "&classid=" + classid + "&id=" + book_id);
            // 参数检查
            if (string.IsNullOrEmpty(classid) || !System.Text.RegularExpressions.Regex.IsMatch(classid, @"^[1-9]\d*$"))
            {
                ShowTipInfo("无效的参数", "bbs/book_view.aspx?siteid=" + siteid + "&classid=" + classid + "&id=" + book_id);
                return;
            }

            wap2_attachment_BLL wap2_attachment_BLL = new wap2_attachment_BLL(string_10);
            bookVo = wap2_attachment_BLL.GetModel(long.Parse(id));

            // 验证附件存在性
            if (bookVo == null)
            {
                ShowTipInfo("文件不存在或已被删除", "bbs/book_view.aspx?siteid=" + siteid + "&classid=" + classid + "&id=" + book_id);
                return;
            }

            // 根据附件类型获取对应的帖子或回复
            if (bookVo.book_type == "bbs")
            {
                // 主题帖附件
                wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(string_10);
                wap_bbs_Model bbsVo = wap_bbs_BLL.GetModel(long.Parse(book_id));

                if (bbsVo == null || bbsVo.book_classid.ToString() != classid)
                {
                    ShowTipInfo("栏目ID不匹配", "bbs/book_view.aspx?siteid=" + siteid + "&classid=" + classid + "&id=" + book_id);
                    return;
                }
            }
            else if (bookVo.book_type == "bbsre")
            {
                // 回复附件
                wap_bbsre_BLL wap_bbsre_BLL = new wap_bbsre_BLL(string_10);
                wap_bbsre_Model bbsReVo = wap_bbsre_BLL.GetModel(long.Parse(book_id));

                if (bbsReVo == null)
                {
                    ShowTipInfo("回复不存在或已被删除", "bbs/book_view.aspx?siteid=" + siteid + "&classid=" + classid + "&id=" + book_id);
                    return;
                }

                // 获取主题帖信息以验证栏目ID
                wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(string_10);
                wap_bbs_Model bbsVo = wap_bbs_BLL.GetModel(bbsReVo.bookid);

                if (bbsVo == null || bbsVo.book_classid.ToString() != classid)
                {
                    ShowTipInfo("栏目ID不匹配", "bbs/book_view.aspx?siteid=" + siteid + "&classid=" + classid + "&id=" + bbsReVo.bookid);
                    return;
                }
            }
            else
            {
                ShowTipInfo("无效的附件类型", "bbs/book_view.aspx?siteid=" + siteid + "&classid=" + classid + "&id=" + book_id);
                return;
            }

            string text = WapTool.GetArryString(classVo.smallimg, '|', 16);
            string text2 = WapTool.GetArryString(classVo.smallimg, '|', 17);
            string text3 = WapTool.GetArryString(classVo.smallimg, '|', 18);
            string text4 = WapTool.GetArryString(classVo.smallimg, '|', 22);
            if (!WapTool.IsNumeric(text))
            {
                text = "0";
            }
            if (!WapTool.IsNumeric(text2))
            {
                text2 = "0";
            }
            if (!WapTool.IsNumeric(text3))
            {
                text3 = "0";
            }
            if (!WapTool.IsNumeric(text4))
            {
                text4 = "0";
            }
            if (IsCheckManagerLvl("|00|01|03|04|", classVo.adminusername))
            {
                text3 = "0";
            }
            if ("1".Equals(WapTool.GetArryString(classVo.smallimg, '|', 10)) && !WapTool.IsAllowUA(UA) && !WapTool.IsAllowIP(IP))
            {
                string error = "此网站设置了只允许手机下载[UA+IP]，如果你是手机访问请将以下信息发给站长：" + UA + "  " + IP;
                ShowTipInfo(error, "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + book_id);
            }
            if (text == "1")
            {
                IsLogin(userid, "bbs/book_view.aspx?siteid=" + siteid + "&classid=" + classid + "&id=" + book_id);
            }
            if (long.Parse(text2) > 0L)
            {
                // 检查用户余额是否满足最低要求(500)和下载费用
                if (userVo.money < 500 || userVo.money < long.Parse(text2))
                {
                    string moneyName = WapTool.GetSiteMoneyName(siteVo.sitemoneyname, this.lang);
                    ShowTipInfo($"抱歉，下载需要{text2}{moneyName}，且需保留至少500{moneyName}。你当前余额{userVo.money}{moneyName}。",
                        "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + book_id);
                    return;
                }

                string fcountSubMoneyFlag = WapTool.GetFcountSubMoneyFlag(siteid, userid, IP);
                if (fcountSubMoneyFlag.IndexOf("BBSDLFL" + id) < 0 && userVo.userid != bookVo.userid)
                {
                    // ✅ 使用TransactionHelper进行安全的事务性资金操作
                    string downloadConnectionString = PubConstant.GetConnectionString(string_10);
                    long downloadCost = long.Parse(text2);
                    long userIdLong = DapperHelper.SafeParseLong(userid, "用户ID");
                    long siteIdLong = DapperHelper.SafeParseLong(siteid, "站点ID");
                    long attachmentIdLong = DapperHelper.SafeParseLong(id, "附件ID");

                    TransactionHelper.ExecuteMoneyTransaction(downloadConnectionString, (connection, transaction) =>
                    {
                        // 1. 更新下载标记
                        fcountSubMoneyFlag = fcountSubMoneyFlag + "BBSDLFL" + id;
                        string updateFcountSql = "UPDATE [fcount] SET SubMoneyFlag = @SubMoneyFlag WHERE fip = @IP AND fuserid = @SiteId AND userid = @UserId";
                        connection.Execute(updateFcountSql, new {
                            SubMoneyFlag = fcountSubMoneyFlag,
                            IP = DapperHelper.LimitLength(IP, 50),
                            SiteId = siteIdLong,
                            UserId = userIdLong
                        }, transaction);

                        // 2. 扣除下载者金币
                        string deductUserSql = "UPDATE [user] SET money = money - @Amount WHERE userid = @UserId";
                        connection.Execute(deductUserSql, new {
                            Amount = downloadCost,
                            UserId = userVo.userid
                        }, transaction);

                        // 3. 如果需要给上传者分成
                        if (text4 == "1")
                        {
                            // 计算税后金额
                            long taxRate = 10; // 10% 的税率
                            long afterTaxAmount = (long)(downloadCost * (100 - taxRate) / 100);

                            // 更新上传者的余额
                            string addUploaderSql = "UPDATE [user] SET money = money + @Amount WHERE userid = @UserId";
                            connection.Execute(addUploaderSql, new {
                                Amount = afterTaxAmount,
                                UserId = bookVo.userid
                            }, transaction);

                            // ✅ 先获取上传者当前余额，避免SaveBankLog中的SELECT操作导致死锁
                            string getUploaderMoneySql = "SELECT money FROM [user] WHERE userid = @UserId AND siteid = @SiteId";
                            long uploaderCurrentMoney = connection.QuerySingle<long>(getUploaderMoneySql, new {
                                UserId = bookVo.userid,
                                SiteId = siteIdLong
                            }, transaction);

                            // ✅ 使用SaveBankLogWithBalance替换SaveBankLog，避免死锁
                            SaveBankLogWithBalance(bookVo.userid.ToString(), "论坛赚币(税后)", afterTaxAmount.ToString(), userid, nickname, "操作人下载您的文件[" + id + "]，税后所得", uploaderCurrentMoney);
                        }
                    });

                    // ✅ 先计算下载者新余额，避免SaveBankLog中的SELECT操作导致死锁
                    long downloaderNewBalance = userVo.money - downloadCost;

                    // ✅ 使用SaveBankLogWithBalance替换SaveBankLog，避免死锁
                    SaveBankLogWithBalance(userid, "论坛下载", "-" + text2.ToString(), userid, nickname, "下载扣币[" + id + "]", downloaderNewBalance);
                }
            }
            if (long.Parse(text3) > 0L)
            {
                string fcountSubMoneyFlag = WapTool.GetFcountSubMoneyFlag(siteid, userid, IP);
                if (fcountSubMoneyFlag.IndexOf("BBSDLAD" + id) < 0)
                {
                    // ✅ 使用TransactionHelper进行安全的事务性资金操作
                    string rewardConnectionString = PubConstant.GetConnectionString(string_10);
                    long rewardAmount = long.Parse(text3);
                    long userIdLong = DapperHelper.SafeParseLong(userid, "用户ID");
                    long siteIdLong = DapperHelper.SafeParseLong(siteid, "站点ID");

                    TransactionHelper.ExecuteMoneyTransaction(rewardConnectionString, (connection, transaction) =>
                    {
                        // 1. 更新下载奖励标记
                        fcountSubMoneyFlag = fcountSubMoneyFlag + "BBSDLAD" + id;
                        string updateFcountSql = "UPDATE [fcount] SET SubMoneyFlag = @SubMoneyFlag WHERE fip = @IP AND fuserid = @SiteId AND userid = @UserId";
                        connection.Execute(updateFcountSql, new {
                            SubMoneyFlag = fcountSubMoneyFlag,
                            IP = DapperHelper.LimitLength(IP, 50),
                            SiteId = siteIdLong,
                            UserId = userIdLong
                        }, transaction);

                        // 2. 给用户增加奖励金币
                        string addRewardSql = "UPDATE [user] SET money = money + @Amount WHERE userid = @UserId";
                        connection.Execute(addRewardSql, new {
                            Amount = rewardAmount,
                            UserId = userVo.userid
                        }, transaction);
                    });

                    // ✅ 先计算用户新余额，避免SaveBankLog中的SELECT操作导致死锁
                    long userNewBalance = userVo.money + rewardAmount;

                    // ✅ 使用SaveBankLogWithBalance替换SaveBankLog，避免死锁
                    SaveBankLogWithBalance(userid.ToString(), "论坛赚币", text3.ToString(), userid, nickname, "下载文件[" + id + "]送币", userNewBalance);
                }
            }
            if (base.Request.UrlReferrer != null)
            {
                refer = base.Request.UrlReferrer.ToString().ToLower();
            }
            // ✅ 使用DapperHelper进行安全的参数化更新操作
            string connectionString = PubConstant.GetConnectionString(string_10);
            string updateClickSql = "UPDATE wap2_attachment SET book_click = book_click + 1 WHERE id = @AttachmentId";
            DapperHelper.Execute(connectionString, updateClickSql, new {
                AttachmentId = DapperHelper.SafeParseLong(id, "附件ID")
            });
        }
    }
}