<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clover的空间 - 用户信息页面演示</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Lucide Icons CDN -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#58b4b0',
                        'primary-dark': '#4a9c98',
                        'primary-light': '#7cd0cb',
                        'primary-alpha-10': 'rgba(88, 180, 176, 0.1)',
                        'primary-alpha-20': 'rgba(88, 180, 176, 0.2)',
                        'secondary': '#9CCCC8',
                        'text-primary': '#1f2937',
                        'text-secondary': '#6b7280',
                        'success': '#10b981',
                        'danger': '#dc2626',
                        'warning': '#d97706',
                        'info': '#3b82f6',
                    },
                    screens: {
                        'max-768': {'max': '768px'},
                        'xs-480': {'max': '480px'},
                        'xs-400': {'max': '400px'},
                        'xs-390': {'max': '390px'},
                        'xs-350': {'max': '350px'},
                        'xs-310': {'max': '310px'},
                    },
                    backgroundImage: {
                        'gradient-posts-s2': 'linear-gradient(to bottom right, #f0f9ff, rgba(219, 234, 254, 0.8))',
                        'gradient-replies-s2': 'linear-gradient(to bottom right, #f0fdfa, rgba(156, 204, 200, 0.2))',
                    }
                }
            }
        }
    </script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #E8E8E8;
        }

        .container {
            max-width: 720px !important;
            margin: 0 auto !important;
            background-color: #f9fafb;
            min-height: 100vh;
        }

        .main-content {
            padding-top: 4rem;
            padding-bottom: 1rem;
            overflow-x: hidden;
        }

        .header {
            max-width: 720px;
            margin: 0 auto;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
        }

        .header-icon {
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 0.25rem;
            transition: all 0.2s;
            position: relative;
        }

        .header-icon:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .header-title {
            font-size: 1.125rem;
            font-weight: 500;
            flex: 1;
            text-align: center;
            margin: 0 0.5rem;
        }

        .header-actions-right {
            min-width: 2rem;
            display: flex;
            justify-content: flex-end;
            align-items: center;
        }

        /* 下拉菜单样式 */
        .dropdown {
            position: relative;
            z-index: 10;
        }

        .dropdown-menu {
            position: absolute;
            left: 50%;
            top: 100%;
            margin-top: 0.5rem;
            background-color: white;
            border-radius: 0.375rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            width: auto;
            min-width: fit-content;
            z-index: 90;
            overflow: hidden;
            opacity: 0;
            visibility: hidden;
            transform: translateX(-50%) translateY(-10px);
            transition: all 0.2s;
            pointer-events: none;
        }

        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(0);
            pointer-events: auto;
        }

        .dropdown-item {
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
            color: #4b5563;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            cursor: pointer;
            transition: all 0.2s;
            text-align: left;
            white-space: nowrap;
        }

        .dropdown-item:hover {
            background-color: #f3f4f6;
        }

        .dropdown-item.active {
            background-color: #f0f9ff;
            color: #0369a1;
        }
        
        .avatar-fallback {
            font-size: 28px;
            font-weight: 500;
            color: rgba(88, 180, 176, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
        }
        
        .avatar-fallback-small {
            font-size: 14px;
            font-weight: 600;
            color: rgba(88, 180, 176, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
        }
        
        .dynamic-icon {
            width: 20px !important;
            height: 20px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            flex-shrink: 0 !important;
            font-size: 14px !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 顶部导航栏 -->
        <header class="header fixed top-0 w-full z-50 shadow-md text-white" style="background: linear-gradient(135deg, #58b4b0 0%, #4a9c98 100%);">
            <div class="header-content">
                <div class="header-icon" id="back-button">
                    <i data-lucide="arrow-left" class="w-6 h-6"></i>
                </div>
                <div class="header-title">Clover的空间</div>
                <div class="header-actions-right">
                    <div class="header-icon dropdown" id="theme-toggle">
                        <i data-lucide="brush" class="w-5 h-5"></i>
                        <div class="dropdown-menu" id="skin-dropdown">
                            <div class="dropdown-item active">
                                <span>新版</span>
                            </div>
                            <div class="dropdown-item" onclick="alert('切换到旧版');">
                                <span>旧版</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <div class="main-content">
            <!-- 个人资料展示区域 -->
            <section class="px-4 mb-4 mt-4 max-768:px-3 max-768:mb-3 max-768:mt-3 xs-400:px-2 xs-400:mb-3 xs-400:mt-3">
                <div class="bg-white rounded-xl shadow-sm p-5 xs-400:p-4 xs-350:p-3 xs-310:p-2">
                    <!-- 头像和基本信息 -->
                    <div class="flex items-start space-x-4 mb-6">
                        <div class="relative">
                            <!-- 头像容器 -->
                            <div class="w-20 h-20 rounded-2xl overflow-hidden bg-gray-50 flex items-center justify-center relative border-2" style="border-color: rgba(88, 180, 176, 0.2);">
                                <!-- 首字母fallback -->
                                <span class="avatar-fallback">C</span>
                            </div>
                        </div>
                        <div class="flex-1">
                            <!-- 昵称行，右侧添加私信按钮 -->
                            <div class="flex items-center justify-between mb-1">
                                <h2 class="text-xl font-bold text-gray-800 flex items-center">
                                    <span style="color: #dc2626;">Clover</span>
                                </h2>
                                <button class="text-primary hover:text-primary-dark transition-all duration-300 flex items-center hover:-translate-y-0.5">
                                    <i class="fas fa-envelope mr-1 text-lg transition-transform duration-300"></i>
                                    <span class="text-sm transition-transform duration-300">私信</span>
                                </button>
                            </div>
                            <p class="text-gray-500 text-sm mb-2">ID: 1000</p>
                            <div class="flex items-center space-x-4 text-sm text-gray-500">
                                <span><i class="fas fa-mars text-blue-500 mr-1"></i>男</span>
                                <span><i class="fas fa-birthday-cake text-pink-400 mr-1"></i>30岁</span>
                                <span class="text-green-500">
                                    <i class="fas fa-circle text-xs mr-1"></i>在线
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- 帖子和回复统计 -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <button class="p-4 rounded-xl transition-all duration-300 border border-transparent cursor-pointer hover:shadow-lg hover:border-primary hover:-translate-y-1" style="background: linear-gradient(to bottom right, #f0f9ff, rgba(219, 234, 254, 0.8));">
                            <div class="flex items-center justify-between">
                                <div class="text-left">
                                    <p class="text-sky-700 text-sm font-medium">帖子</p>
                                    <p class="text-xl font-bold text-sky-500">2,428</p>
                                </div>
                                <i class="fas fa-edit text-2xl text-sky-500 opacity-90"></i>
                            </div>
                        </button>
                        <button class="p-4 rounded-xl transition-all duration-300 border border-transparent cursor-pointer hover:shadow-lg hover:border-primary hover:-translate-y-1" style="background: linear-gradient(to bottom right, #f0fdfa, rgba(156, 204, 200, 0.2));">
                            <div class="flex items-center justify-between">
                                <div class="text-left">
                                    <p class="text-teal-700 text-sm font-medium">回复</p>
                                    <p class="text-xl font-bold text-primary">64,437</p>
                                </div>
                                <i class="fas fa-comments text-2xl text-primary opacity-90"></i>
                            </div>
                        </button>
                    </div>

                    <!-- 数据统计 -->
                    <div class="grid grid-cols-3 gap-3 mb-4">
                        <div class="text-center p-3 bg-gray-50 rounded-lg relative cursor-pointer transition-colors hover:bg-gray-200">
                            <p class="text-xl font-medium text-gray-800">124万</p>
                            <p class="text-sm text-gray-600">妖晶</p>
                        </div>
                        <div class="text-center p-3 bg-gray-50 rounded-lg relative cursor-pointer transition-colors hover:bg-gray-200">
                            <p class="text-xl font-medium text-gray-800">12级</p>
                            <p class="text-sm text-gray-600">等级</p>
                        </div>
                        <div class="text-center p-3 bg-gray-50 rounded-lg relative cursor-pointer transition-colors hover:bg-gray-200">
                            <p class="text-xl font-medium text-gray-800">12年</p>
                            <p class="text-sm text-gray-600">注册时长</p>
                        </div>
                    </div>

                    <!-- 详细资料链接 -->
                    <button class="w-full text-center text-primary font-medium relative inline-flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300 bg-transparent border border-transparent hover:bg-primary-alpha-10 hover:border-primary-alpha-20 hover:-translate-y-0.5">
                        <i class="fas fa-id-card mr-2"></i>查看详细资料
                    </button>
                </div>
            </section>

            <!-- 用户动态区域 -->
            <section class="px-4 mb-4 max-768:px-3 max-768:mb-3 xs-400:px-2 xs-400:mb-3">
                <div class="bg-white rounded-xl shadow-sm p-5 xs-400:p-4 xs-350:p-3 xs-310:p-2">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">最新动态</h3>

                    <div class="space-y-4">
                        <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="dynamic-icon fas fa-search text-emerald-500"></i>
                            <div class="flex-1">
                                <p class="text-sm text-gray-700">正在论坛首页搜索：1000</p>
                                <p class="text-xs text-gray-500 mt-1">39分钟</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="dynamic-icon fas fa-reply text-purple-500"></i>
                            <div class="flex-1">
                                <p class="text-sm text-gray-700">回复了帖子：请问你的在线状况...</p>
                                <p class="text-xs text-gray-500 mt-1">39分钟</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                            <i class="dynamic-icon fas fa-reply text-purple-500"></i>
                            <div class="flex-1">
                                <p class="text-sm text-gray-700">回复了帖子：我要收集情报</p>
                                <p class="text-xs text-gray-500 mt-1">13分钟前</p>
                            </div>
                        </div>
                    </div>

                    <!-- 查看更多动态链接 -->
                    <button class="w-full text-center text-primary font-medium relative inline-flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300 bg-transparent border border-transparent hover:bg-primary-alpha-10 hover:border-primary-alpha-20 hover:-translate-y-0.5 mt-4">
                        <i class="fas fa-stream mr-2"></i>查看更多动态
                    </button>
                </div>
            </section>

            <!-- 留言板区域 -->
            <section class="px-4 mb-4 max-768:px-3 max-768:mb-3 xs-400:px-2 xs-400:mb-3">
                <div class="bg-white rounded-xl shadow-sm p-5 xs-400:p-4 xs-350:p-3 xs-310:p-2">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">空间留言</h3>

                    <!-- 留言输入框 -->
                    <div class="mb-6 relative after:content-[''] after:absolute after:bottom-[-12px] after:left-[-20px] after:right-[-20px] after:h-px after:bg-gradient-to-r after:from-transparent after:via-gray-200 after:to-transparent">
                        <form>
                            <div class="flex space-x-3">
                                <div class="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center overflow-hidden">
                                    <span class="avatar-fallback-small">我</span>
                                </div>
                                <div class="flex-1">
                                    <textarea placeholder="给自己留个言吧..." class="w-full p-3 border border-gray-200 bg-gray-50 rounded-lg resize-none focus:outline-none focus:border-primary focus:bg-white transition-colors" rows="3"></textarea>
                                    <div class="flex justify-between items-center mt-2">
                                        <div></div>
                                        <button type="submit" class="bg-gradient-to-r from-primary to-secondary text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg">
                                            <i class="fas fa-paper-plane mr-2"></i>发送留言
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>

                    <!-- 留言列表 -->
                    <h4 class="text-sm font-medium text-gray-600 mb-2">最新留言</h4>
                    <div class="space-y-0">
                        <div class="py-4 border-b border-gray-100">
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center overflow-hidden relative">
                                    <span class="avatar-fallback-small">友</span>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="font-medium text-sm text-primary">友友</span>
                                        <span class="text-xs text-gray-500">刚刚</span>
                                    </div>
                                    <p class="text-sm text-gray-700">给你留个言，记得回访哦！</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="py-4 border-b border-gray-100">
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center overflow-hidden relative">
                                    <span class="avatar-fallback-small">管</span>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="font-medium text-sm text-primary">管理员</span>
                                        <span class="text-xs text-gray-500">19分钟前</span>
                                    </div>
                                    <p class="text-sm text-gray-700">欢迎来到妖火网，记得回访</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="py-4 border-b border-gray-100">
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 rounded-lg bg-gray-100 flex items-center justify-center overflow-hidden relative">
                                    <span class="avatar-fallback-small">测</span>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="font-medium text-sm text-primary">测试3465</span>
                                        <span class="text-xs text-gray-500">1天前</span>
                                    </div>
                                    <p class="text-sm text-gray-700">打卡，感谢楼主分享1000好帖😍</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 查看更多留言链接 -->
                    <button class="w-full text-center text-primary font-medium relative inline-flex items-center justify-center px-3 py-2 rounded-lg transition-all duration-300 bg-transparent border border-transparent hover:bg-primary-alpha-10 hover:border-primary-alpha-20 hover:-translate-y-0.5 mt-4">
                        <i class="fas fa-comments mr-2"></i>查看更多留言
                    </button>
                </div>
            </section>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化 Lucide 图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }

            // 设置header下拉菜单
            setupHeaderDropdowns();

            // 设置返回按钮
            setupBackButton();

            // 为所有按钮添加点击效果
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('按钮被点击:', this.textContent.trim());
                });
            });
        });

        // 设置header下拉菜单
        function setupHeaderDropdowns() {
            const themeToggle = document.getElementById('theme-toggle');
            const skinDropdown = document.getElementById('skin-dropdown');

            if (themeToggle && skinDropdown) {
                themeToggle.addEventListener('click', function(e) {
                    e.stopPropagation();

                    const isCurrentlyOpen = skinDropdown.classList.contains('show');
                    closeAllDropdowns();

                    if (!isCurrentlyOpen) {
                        skinDropdown.classList.add('show');
                    }
                });

                // 处理新版按钮点击
                const activeItem = skinDropdown.querySelector('.dropdown-item.active');
                if (activeItem) {
                    activeItem.addEventListener('click', function(e) {
                        e.stopPropagation();
                        closeAllDropdowns();
                        showToast('当前已是新版界面');
                    });
                }
            }

            // 点击外部关闭下拉菜单
            document.addEventListener('click', closeAllDropdowns);
        }

        // 设置返回按钮
        function setupBackButton() {
            const backButton = document.getElementById('back-button');
            if (backButton) {
                backButton.addEventListener('click', function() {
                    console.log('返回按钮被点击');
                    // 这里可以添加返回逻辑
                });
            }
        }

        // 显示提示消息
        function showToast(message) {
            const toast = document.createElement('div');
            toast.textContent = message;
            toast.className = 'fixed bottom-5 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 text-white py-2.5 px-5 rounded z-[1000]';
            toast.style.opacity = '0';
            toast.style.transition = 'opacity 0.3s ease';

            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '1';
            }, 10);

            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 2000);
        }

        // 关闭所有下拉菜单
        function closeAllDropdowns() {
            const skinDropdown = document.getElementById('skin-dropdown');
            if (skinDropdown) {
                skinDropdown.classList.remove('show');
            }
        }
    </script>
</body>
</html>
