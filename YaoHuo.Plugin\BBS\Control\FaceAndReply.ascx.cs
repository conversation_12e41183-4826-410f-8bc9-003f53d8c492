﻿using System;
using System.Text;
using System.Web.UI;
using KeLin.ClassManager.Model;

namespace YaoHuo.Plugin.BBS.Control
{
    public partial class FaceAndReply : System.Web.UI.UserControl
    {
        public wap_bbs_Model BookVo { get; set; }
        public string[] FaceList { get; set; }
        public string[] FaceListImg { get; set; }
        public string ReShowInfo { get; set; }
        public string HttpStart { get; set; }
        public string Id { get; set; }
        public string SiteId { get; set; }
        public string LPage { get; set; }
        public string ClassId { get; set; }

        protected override void Render(HtmlTextWriter writer)
        {
            // 使用预分配大小的 StringBuilder 可以减少内存重分配
            using (var sw = new System.IO.StringWriter(new StringBuilder(4096)))
            using (var hw = new HtmlTextWriter(sw))
            {
                base.Render(hw);
                string output = sw.ToString()
                    .Replace(Environment.NewLine, "")  // 移除所有换行
                    .Replace("  ", "");  // 移除多余空格
                ((Book_View)Page).strhtml.Append(output);
            }
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            if (!IsPostBack)
            {
                // 页面首次加载时的初始化逻辑
            }
        }

        public string GetLang(string key)
        {
            // 实现多语言转换逻辑
            return key.Split('|')[0];
        }
    }
}