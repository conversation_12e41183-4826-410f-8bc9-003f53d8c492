(function() {
    // 全局变量声明
    let dialog;
    let imageDialog;
    let resultDialog; 
    let currentDeleteUrl;
    let currentImageId;

    // 主初始化函数
    function initialize() {
        initializeDialog();
        initializeSearch();
        initializePagination();
        initializeImageDialog();
        initializeEventListeners();
    }

    // 初始化所有事件监听器
    function initializeEventListeners() {
        // ESC 键关闭功能
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                if (imageDialog && imageDialog.open) {
                    closeImageDialog();
                }
                if (resultDialog && resultDialog.open) {
                    closeResultDialog();
                }
                if (dialog && dialog.open) {
                    closeDeleteDialog();
                }
            }
        });
    }

    // 初始化删除对话框
    function initializeDialog() {
        dialog = document.getElementById('deleteDialog');
        if (!dialog) {
            console.error('Dialog element not found');
            return;
        }

        dialog.addEventListener('click', function(event) {
            if (event.target.closest('.dialog-delete-content') || 
                event.target.closest('.dialog-delete-header') || 
                event.target.closest('.dialog-delete-footer')) {
                return;
            }
            closeDeleteDialog();
        });
    }

    // 初始化图片对话框
    function initializeImageDialog() {
        imageDialog = document.getElementById('imageDialog');
        resultDialog = document.getElementById('resultDialog');

        if (imageDialog) {
            imageDialog.addEventListener('click', function(event) {
                handleDialogClick(event, imageDialog);
            });
        }

        if (resultDialog) {
            resultDialog.addEventListener('click', function(event) {
                handleDialogClick(event, resultDialog);
            });

            const cancelButton = resultDialog.querySelector('.delete-dialog-cancel');
            if (cancelButton) {
                cancelButton.onclick = closeResultDialog;
            }
        }
    }

    // 初始化搜索功能
    function initializeSearch() {
        const searchToggle = document.getElementById('searchToggle');
        const searchContainer = document.getElementById('searchContainer');
        if (!searchContainer || !searchToggle) return;

        const searchInput = searchContainer.getElementsByTagName('input')[0];
        const searchForm = searchContainer.getElementsByTagName('form')[0];
        const uploadButtonText = document.querySelector('.uploadbutton-text');
        let isSearchOpen = false;

        // 搜索按钮点击事件
        searchToggle.onclick = function() {
            const inputValue = searchInput.value.trim();
            
            if (isSearchOpen && inputValue !== '') {
                searchForm.submit();
                return;
            }
            
            isSearchOpen = !isSearchOpen;
            
            if (isSearchOpen) {
                searchContainer.className = searchContainer.className.replace(/\bhidden\b/g, '');
                searchContainer.offsetHeight; // 触发重排
                searchContainer.className += ' slide-in';
                searchContainer.className = searchContainer.className.replace(/\bslide-out\b/g, '');
                searchInput.focus();

                if (window.innerWidth <= 350) {
                    uploadButtonText.classList.add('short');
                }
            } else {
                searchContainer.className = searchContainer.className.replace(/\bslide-in\b/g, '');
                searchContainer.className += ' slide-out';
                setTimeout(function() {
                    searchContainer.className += ' hidden';
                    uploadButtonText.classList.remove('short');
                }, 300);
            }
        };

        // 添加窗口大小改变事件监听
        window.addEventListener('resize', function() {
            if (isSearchOpen) {
                if (window.innerWidth <= 350) {
                    uploadButtonText.classList.add('short');
                } else {
                    uploadButtonText.classList.remove('short');
                }
            }
        });
    }

    // 初始化分页功能
    function initializePagination() {
        const originalPagination = document.getElementById('originalPagination');
        const modernPagination = document.getElementById('modernPagination');
        
        if (!originalPagination || !modernPagination) return;

        const pageInfoMatch = originalPagination.querySelector('.showpage').textContent.match(/(\d+)\/(\d+)\s*页，共\s*(\d+)\s*条/);
        if (!pageInfoMatch) return;

        const currentPage = parseInt(pageInfoMatch[1]);
        const totalPages = parseInt(pageInfoMatch[2]);
        const totalItems = parseInt(pageInfoMatch[3]);
        
        let prevLink = '#';
        let nextLink = '#';
        const links = originalPagination.getElementsByTagName('a');
        
        for(let i = 0; i < links.length; i++) {
            const href = links[i].href;
            if(href.includes('page=' + (currentPage - 1))) {
                prevLink = href;
            } else if(href.includes('page=' + (currentPage + 1))) {
                nextLink = href;
            }
        }
        
        modernPagination.innerHTML = `
            <div class="relative ${currentPage === 1 ? 'disabled' : ''}">
                <a href="${prevLink}" class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 border border-input bg-background hover:bg-slate-100 h-10 w-10">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4"><polyline points="15 18 9 12 15 6"></polyline></svg>
                    <span class="sr-only">上一页</span>
                </a>
            </div>
            <div class="text-sm text-gray-500">第 ${currentPage}/${totalPages} 页，共 ${totalItems} 条</div>
            <div class="relative ${currentPage === totalPages ? 'disabled' : ''}">
                <a href="${nextLink}" class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 border border-input bg-background hover:bg-slate-100 h-10 w-10">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4"><polyline points="9 18 15 12 9 6"></polyline></svg>
                    <span class="sr-only">下一页</span>
                </a>
            </div>
        `;
    }

    // 对话框点击处理
    function handleDialogClick(event, dialogElement) {
        // 如果点击的是头像按钮，直接返回不处理
        if (event.target.closest('.avatar-button')) {
            return;
        }

        const isMobile = window.innerWidth <= 640;

        if (isMobile) {
            // 在移动设备上，任何点击（除了头像按钮）都关闭弹窗
            if (dialogElement === imageDialog) {
                closeImageDialog();
            } else if (dialogElement === resultDialog) {
                closeResultDialog();
            }
        } else {
            // PC端保持原有的行为：只在点击图片外区域时关闭
            const rect = dialogElement.getBoundingClientRect();
            const isInDialog = (
                event.clientX >= rect.left && 
                event.clientX <= rect.right && 
                event.clientY >= rect.top && 
                event.clientY <= rect.bottom
            );
            
            if (!isInDialog) {
                if (dialogElement === imageDialog) {
                    closeImageDialog();
                } else if (dialogElement === resultDialog) {
                    closeResultDialog();
                }
            }
        }
    }

    // 工具函数
    function addClass(element, className) {
        if (element && !element.className.includes(className)) {
            element.className += ' ' + className;
        }
    }

    function removeClass(element, className) {
        if (element) {
            element.className = element.className.replace(new RegExp('\\b' + className + '\\b', 'g'), '');
        }
    }

    function handleTransitionEnd(element, callback) {
        const transitionEndHandler = () => {
            callback();
            element.removeEventListener('transitionend', transitionEndHandler);
        };
        element.addEventListener('transitionend', transitionEndHandler);
    }

    // 错误处理函数
    function handleError(error, message) {
        console.error('Error:', error);
        showToast(message || '操作失败，请重试', false);
    }

    // 暴露给全局的函数
    window.showImage = function(thumbnailPath, imageId) {
        const thumbnailImg = document.querySelector(`img[src="${thumbnailPath}"]`);
        if (thumbnailImg && thumbnailImg.src.startsWith('data:image/svg+xml')) {
            return;
        }
        
        const fullImagePath = thumbnailPath.replace('/S', '/');
        const dialogImage = imageDialog.querySelector('#fullImage');
        if (dialogImage) {
            dialogImage.src = fullImagePath;
        }
        
        currentImageId = imageId;
        imageDialog.showModal();
    };

    window.closeImageDialog = function() {
        if (imageDialog) {
            imageDialog.close();
            const dialogImage = imageDialog.querySelector('#fullImage');
            if (dialogImage) {
                dialogImage.src = '';
            }
        }
    };

    window.showDeleteDialog = function(deleteUrl) {
        if (!dialog) {
            initializeDialog();
        }
        if (dialog) {
            currentDeleteUrl = deleteUrl;
            const button = event.currentTarget;
            currentImageId = button.getAttribute('data-image-id');
            dialog.showModal();
        }
    };

    window.closeDeleteDialog = function() {
        if (dialog) {
            dialog.close();
        }
    };

    window.confirmDelete = function() {
        const confirmButton = dialog.querySelector('.dialog-delete-button-confirm');
        const originalText = confirmButton.textContent;
        confirmButton.textContent = '删除中...';
        confirmButton.disabled = true;

        const godelUrl = currentDeleteUrl.replace('action=del', 'action=godel');
        
        fetch(godelUrl)
            .then(response => response.text())
            .then(text => {
                if (text.includes('删除成功')) {
                    const imageCard = document.querySelector(`[data-image-id="${currentImageId}"]`).closest('.group.relative');
                    if (imageCard) {
                        imageCard.style.transition = 'all 0.3s ease';
                        imageCard.style.opacity = '0';
                        imageCard.style.transform = 'scale(0.9)';
                        
                        setTimeout(function() {
                            imageCard.remove();
                            
                            // 更新总条数
                            const totalItemsElement = document.querySelector('#modernPagination .text-gray-500');
                            if (totalItemsElement) {
                                const text = totalItemsElement.textContent;
                                const match = text.match(/第\s*(\d+)\/(\d+)\s*页，共\s*(\d+)\s*条/);
                                if (match) {
                                    const currentPage = parseInt(match[1]);
                                    const totalPages = parseInt(match[2]);
                                    const totalItems = parseInt(match[3]);
                                    
                                    totalItemsElement.textContent = `第 ${currentPage}/${totalPages} 页，共 ${totalItems - 1} 条`;
                                }
                            }
                            
                            const remainingCards = document.querySelectorAll('.group.relative');
                            if (remainingCards.length === 0) {
                                location.reload();
                            }
                        }, 300);
                        
                        closeDeleteDialog();
                    } else {
                        showToast('删除失败，请重试', false);
                    }
                }
            })
            .catch(() => {
                showToast('删除失败，请重试', false);
            })
            .finally(() => {
                confirmButton.textContent = originalText;
                confirmButton.disabled = false;
            });
    };

    window.showToast = function(message, isSuccess = true) {
        const toast = document.getElementById('toast');
        const toastMessage = document.getElementById('toastMessage');
        
        toastMessage.textContent = message;
        toast.classList.remove('hidden');
        toast.style.backgroundColor = isSuccess ? 'rgba(21, 128, 61, 0.9)' : 'rgba(220, 38, 38, 0.9)';
        
        setTimeout(function() {
            toast.classList.add('hidden');
        }, 3000);
    };

    window.setAsAvatar = function() {
        const http_start = albumConfig.httpStart;
        const siteid = albumConfig.siteId;
        const classid = albumConfig.classId;
        
        fetch(`${http_start}album/book_view.aspx?siteid=${siteid}&classid=${classid}&id=${currentImageId}&action=addhead`)
            .then(response => response.text())
            .then(text => {
                if (text.includes('设置头像成功')) {
                    imageDialog.close();
                    resultDialog.showModal();
                } else {
                    showToast('设置头像失败，请重试', false);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('设置头像失败，请重试', false);
            });
    };

    window.closeResultDialog = function() {
        if (resultDialog) {
            resultDialog.close();
        }
    };

    // 仅保留一个初始化入口
    if (document.addEventListener) {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        window.onload = initialize;
    }
})();