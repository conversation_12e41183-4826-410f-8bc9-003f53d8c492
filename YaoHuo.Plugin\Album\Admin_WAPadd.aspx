﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="admin_WAPadd.aspx.cs" Inherits="YaoHuo.Plugin.Album.admin_WAPadd" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    wmlVo.mycss += "\r\n<link href=\"/netcss/css/upload-resource.css?v1\" rel=\"stylesheet\" type=\"text/css\"/>";

    Response.Write(WapTool.showTop(this.GetLang("上传相片|上传相片|content add"), wmlVo));
    StringBuilder strhtml = new StringBuilder();
    
    // 错误信息处理
    if (ERROR != "")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append(this.ERROR);
        strhtml.Append("</div>");
    }
    
    // 显示上传表单
    strhtml.Append("<div class=\"upload-container\">");
    
    // 面包屑导航
    strhtml.Append("<div class=\"breadcrumb\">");
    strhtml.Append("<a href=\"/\" class=\"breadcrumb-item\">");
    strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"></path><polyline points=\"9 22 9 12 15 12 15 22\"></polyline></svg>");
    strhtml.Append("<span>首页</span>");
    strhtml.Append("</a>");
    strhtml.Append("<span class=\"breadcrumb-separator\"></span>");
    strhtml.Append("<a href=\"" + this.http_start + "album/albumlist.aspx?siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;smalltypeid=" + this.smalltypeid + "\" class=\"breadcrumb-item\">");
    strhtml.Append("<span>我的相册</span>");
    strhtml.Append("</a>");
    strhtml.Append("<span class=\"breadcrumb-separator\"></span>");
    strhtml.Append("<span class=\"breadcrumb-item active\">上传相片</span>");
    strhtml.Append("</div>");

    // 处理各种状态信息 - 移到面包屑导航后面
    if (this.INFO == "OK")
    {
        strhtml.Append("<meta http-equiv=\"refresh\" content=\"0;url=/album/albumlist.aspx?siteid=1000&classid=0&smalltypeid=0\">");
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append("<b>上传成功！</b> ");
        if (siteVo.isCheck == 1)
        {
            strhtml.Append("<b>审核后显示！</b> ");
        }
        strhtml.Append(" <a class=\"urlbtn\" href=\"" + this.http_start + "album/albumlist.aspx?siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;smalltypeid=" + this.smalltypeid + "\">" + this.GetLang("自动返回相册|返回|Back to list") + "</a>");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "EXTERR")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append("<b>上传文件格式错误，只允许上传：" + siteVo.UpFileType + "</b><br/>");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "NOTSPACE")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append("<b>网站总空间已经大于系统分配给此网站的最大空间了，网站空间：" + siteVo.sitespace + "M；此网站已使用：" + (siteVo.myspace) + "KB</b><br/>");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "MAXFILE")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append("<b>您上传的文件大小超出了最大限制" + siteVo.MaxFileSize + "KB</b><br/>");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "NULL")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append("<b>标题不能小于2个字符！</b><br/>");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "ERR_FORMAT")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append("<b>取到非法值:\"$$\"请更换手机浏览器或重新编辑！</b><br/>");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "LOCK")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append("<b>抱歉，您已经被加入黑名单，请注意发贴规则！</b><br/>");
        strhtml.Append("</div>");
    }

    // 继续显示上传表单（除非是上传成功的情况）
    if (this.INFO != "OK")
    {
        // 上传表单
        strhtml.Append("<form name=\"gt\" action=\"" + http_start + "album/admin_WAPadd.aspx\" enctype=\"multipart/form-data\" method=\"post\">");
        strhtml.Append("<div class=\"form-group\">");
        strhtml.Append("<label>图片标题</label>");
        strhtml.Append("<input type=\"text\" name=\"book_title\" class=\"form-control\" value=\"自定义头像\" required=\"required\"/>");
        strhtml.Append("</div>");

        // 文件选择区域
        strhtml.Append("<div class=\"file-select-area\" id=\"uploadArea\" onclick=\"document.querySelector('input[name=book_file]').click()\">");
        strhtml.Append("<div id=\"defaultUploadUI\">");
        strhtml.Append("<div class=\"big-upload-icon\">+</div>");
        strhtml.Append("<div class=\"upload-text\">点击选择图片</div>");
        strhtml.Append("</div>");
        strhtml.Append("<div id=\"previewArea\" style=\"display:none; width:100%;\">");
        strhtml.Append("<img id=\"imagePreview\" style=\"max-width:100%; max-height:60vh;border-radius:7px;\"/>");
        strhtml.Append("<div class=\"photo-info\">");
        strhtml.Append("<div class=\"photo-name\"><span id=\"fileName\"></span><span style=\"margin:0 2px\"></span><span id=\"fileSize\"></span></div>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");

        // 隐藏的文件输入框
        strhtml.Append("<input type=\"file\" name=\"book_file\" style=\"display:none\" accept=\".jpg,.jpeg,.png,.gif\" onchange=\"handleFileSelect(this)\" required=\"required\"/>");

        // 隐藏字段
        strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"num\" value=\"1\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"smalltypeid\" value=\"" + smalltypeid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"toclassid\" value=\"" + classid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"ishidden\" value=\"1\"/>");

        // 提交按钮
        strhtml.Append("<button type=\"submit\" id=\"submitBtn\" class=\"submit-btn\" style=\"display:none\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"></path><polyline points=\"17 8 12 3 7 8\"></polyline><line x1=\"12\" x2=\"12\" y1=\"3\" y2=\"15\"></line></svg>");
        strhtml.Append("<span>上传图片</span></button>");
        strhtml.Append("</form>");
		strhtml.Append("</div>");

        // 警告提示
        strhtml.Append("<div class=\"triangle-alert\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3\"/><path d=\"M12 9v4\"/><path d=\"M12 17h.01\"/></svg>");
        strhtml.Append("严禁上传色情图片，违者永久封号。");
        strhtml.Append("</div><div style=\"padding-bottom:7px\"></div>");
    }

    // 添加文件处理脚本
strhtml.Append(@"
<script type=""text/javascript"">
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return '<span class=""size-number"">' + parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + '</span><span class=""size-unit"">' + sizes[i] + '</span>';
}

function handleFileSelect(input) {
    if (!input.files || !input.files[0]) {
        return;
    }
    processFile(input.files[0]);
}

function processFile(file) {
    if (!file) return;
    
    var defaultUI = document.getElementById('defaultUploadUI');
    var previewArea = document.getElementById('previewArea');
    var submitBtn = document.getElementById('submitBtn');
    var uploadArea = document.getElementById('uploadArea');
    
    if (!file.type.match('image.*')) {
        showErrorDialog('文件类型错误', '只能上传图片文件！');
        document.querySelector('input[name=book_file]').value = '';
        return;
    }

    // 添加图片真实性验证
    validateImage(file).then(isValid => {
        if (!isValid) {
            document.querySelector('input[name=book_file]').value = '';
            return;
        }

        var reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('imagePreview').src = e.target.result;
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').innerHTML = '(' + formatFileSize(file.size) + ')';
            
            defaultUI.style.display = 'none';
            previewArea.style.display = 'block';
            submitBtn.style.display = 'flex';
            
            uploadArea.style.setProperty('padding-bottom', '1rem', 'important');
            uploadArea.style.setProperty('--after-content', 'none');
        };
        reader.onerror = function() {
            showErrorDialog('文件读取错误', '文件读取失败，请重试。');
            defaultUI.style.display = 'flex';
            previewArea.style.display = 'none';
            submitBtn.style.display = 'none';
            document.querySelector('input[name=book_file]').value = '';
        };
        reader.readAsDataURL(file);
    });
}

// 添加图片验证函数
function validateImage(file) {
    return new Promise((resolve) => {
        // 只对 GIF 格式进行大小限制
        if (file.type === 'image/gif' && file.size > 1 * 1024 * 1024) { // 1MB
            showErrorDialog('文件过大', `GIF文件大小不能超过1MB，当前文件: ${(file.size / (1024 * 1024)).toFixed(2)}MB`);
            resolve(false);
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            const img = new Image();
            img.onload = function() {
                // 验证图片尺寸
                if (this.width < 10 || this.height < 10) {
                    showErrorDialog('无效的图片', '图片尺寸太小，请选择更大的图片。');
                    resolve(false);
                    return;
                }
                resolve(true);
            };
            img.onerror = function() {
                showErrorDialog('无效的图片文件', '所选文件不是有效的图片文件。');
                resolve(false);
            };
            img.src = e.target.result;
        };
        reader.onerror = function() {
            showErrorDialog('文件读取错误', '文件读取失败，请重试。');
            resolve(false);
        };
        reader.readAsDataURL(file);
    });
}

// 添加错误提示对话框函数
function showErrorDialog(title, message) {
    const dialog = document.createElement('dialog');
    dialog.className = 'dialog-url';
    
    dialog.innerHTML = `
        <div class=""dialog-url-header"">
            <svg class=""dialog-url-icon"" xmlns=""http://www.w3.org/2000/svg"" fill=""none"" viewBox=""0 0 24 24"" stroke=""currentColor"">
                <path stroke-linecap=""round"" stroke-linejoin=""round"" stroke-width=""2"" d=""M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"" />
            </svg>
            <h2 class=""dialog-url-title"">${title}</h2>
        </div>
        <p class=""dialog-url-description"">${message}</p>
        <div class=""dialog-url-footer"">
            <button class=""dialog-url-button"" onclick=""this.closest('dialog').close()"">确定</button>
        </div>
    `;
    
    document.body.appendChild(dialog);
    dialog.showModal();
    
    dialog.addEventListener('click', (e) => {
        if (e.target === dialog) dialog.close();
    });
}

function handleDragOver(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById('uploadArea').classList.add('drag-over');
}

function handleDragLeave(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById('uploadArea').classList.remove('drag-over');
}

function handleDrop(e) {
    e.preventDefault();
    e.stopPropagation();
    document.getElementById('uploadArea').classList.remove('drag-over');
    
    const file = e.dataTransfer.files[0];
    if (file) {
        processFile(file);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    var uploadArea = document.getElementById('uploadArea');
    
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);

    document.addEventListener('paste', function(e) {
        const items = (e.clipboardData || e.originalEvent.clipboardData).items;
        for (let i = 0; i < items.length; i++) {
            if (items[i].type.indexOf('image') !== -1) {
                const file = items[i].getAsFile();
                processFile(file);
                break;
            }
        }
    });
});
</script>");

    // 添加新的内联样式
    strhtml.Append(@"
    <style>
    .file-select-area {
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    }
        .photo-name {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
            margin-bottom: 4px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        .photo-size {
            font-size: 12px;
            color: #6B7280;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        .photo-info {
            text-align: center;
            border-radius: 6px;
        }

        .size-number {
            margin-right: 1px;
        }

        .file-select-area[style*='--after-content'] {
            padding-bottom: 1rem !important;
        }
        .file-select-area[style*='--after-content']::after {
            content: none !important;
        }
    </style>");

    string isWebHtml = this.ShowWEB_view(this.classid);
    if (isWebHtml != "")
    {
        string strhtml_list = strhtml.ToString();
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml.Replace("[view]", strhtml_list), wmlVo));
        Response.End();
    }
    Response.Write(strhtml);
    Response.Write(WapTool.showDown(wmlVo));
%>