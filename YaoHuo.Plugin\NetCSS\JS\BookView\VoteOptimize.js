(function () {
    // 全局状态变量
    let hasVoted = false;
    let isViewingResults = false;
    let sortedByVotes = false;
    let originalOrder = [];

    // DOM 选择器工具函数
    const DOM = {
        get: (selector) => document.querySelector(selector),
        getAll: (selector) => document.querySelectorAll(selector),
        create: (tag, className, innerHTML) => {
            const element = document.createElement(tag);
            if (className) element.className = className;
            if (innerHTML) element.innerHTML = innerHTML;
            return element;
        },
        appendStyle: (id, cssText) => {
            if (document.getElementById(id)) return;
            const style = document.createElement('style');
            style.id = id;
            style.textContent = cssText;
            document.head.appendChild(style);
        }
    };

    // UI 工具函数
    const UI = {
        showPopup: (message) => {
            const popup = DOM.create('div', 'ui__alert ui__alert_bg');
            popup.innerHTML = `
      <div class="ui__alert_content">
        <div class="ui__content_body">
          <h4 class="ui__title">${message}</h4>
        </div>
      </div>
    `;
            document.body.appendChild(popup);
            setTimeout(() => {
                popup.style.opacity = '0';
                popup.style.transition = 'opacity 0.2s ease';
                setTimeout(() => popup.remove(), 200);
            }, 800);
        },
        saveScrollPosition: () => window.scrollY || document.documentElement.scrollTop,
        restoreScrollPosition: (position) => window.scrollTo(0, position)
    };

    // 添加核心样式
    function addCoreStyles() {
        DOM.appendStyle('vote-core-style', `
    .vote-container {
      background-color: #fbfbfb;
      border-radius: 4px;
      padding: 15px;
      margin: 15px;
      border: 1px solid #ebebeb;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    .vote-title {
      border-bottom: 1px dashed #ddd;
      padding-bottom: 8px;
      margin-bottom: 15px;
      color: #333;
      user-select: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      cursor: default;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .vote-title::before {
      content: attr(data-title);
      flex: 1;
    }

    /* 投票选项行样式 */
    .vote-option-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 12px;
      border-bottom: 1px solid #f0f0f0;
    }

    .vote-option-text {
      flex: 1;
      font-size: 15px;
      font-weight: 500;
      display: flex;
      align-items: center;
    }
    
    /* 圆形序号样式 */
    .vote-option-number {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 25px;
      height: 25px;
      border-radius: 50%;
      background-color: #528b8c;
      color: white;
      font-size: 16px;
      font-weight: 500;
      margin-right: 10px;
      flex-shrink: 0;
    }

    /* 美化的投票按钮 */
    .vote-button {
      display: inline-block;
      background-color: #378d8d;
      color: white;
      border-radius: 4px;
      text-decoration: none;
      font-size: 14px;
      transition: background-color 0.2s;
      text-align: center;
      min-width: 60px;
    }

    .vote-button:hover {
      background-color: #2c6d6d;
      color: white;
      text-decoration: unset;
    }

    /* 投票后的图表样式 */
    .vote-option-result {
      margin-bottom: 15px;
    }

    .vote-option-label {
      margin-bottom: 5px;
      font-size: 15px;
      font-weight: 500;
    }

    .vote-chart-container {
      position: relative;
      height: 30px;
      background-color: #e9e9e9;
      border-radius: 4px;
      overflow: hidden;
      margin-top: 5px;
    }

    .vote-chart-bar {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      background-color: #378d8d;
      transition: width 1s ease-out;
    }

    .vote-chart-text {
      position: absolute;
      right: 10px;
      top: 0;
      height: 100%;
      display: flex;
      align-items: center;
      color: #333;
      font-size: 14px;
      font-weight: 500;
    }

    .vote-message {
      margin-top: 10px;
      color: #666;
      border-top: 1px dashed #ddd;
      padding-top: 8px;
      text-align: center;
      font-size: 14px;
    }

    /* 弹窗样式 */
    .ui__alert {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }

    .ui__alert_bg {
      background-color: rgba(0, 0, 0, 0.5);
    }

    .ui__alert_content {
      background: white;
      padding: 15px 20px;
      border-radius: 5px;
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
      min-width: 200px;
      text-align: center;
    }

    .ui__title {
      margin: 0;
      font-size: 16px;
      color: #333;
    }

    /* 按钮样式 */
    .vote-sort-button, .vote-view-button {
      float: none;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: background-color 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0.7;
      margin-left: 5px;
    }
    
    .vote-sort-button:hover, .vote-view-button:hover {
      background-color: #f0f0f0;
    }
    
    .vote-sort-button svg, .vote-view-button svg {
      width: 18px;
      height: 18px;
    }
    
    .vote-sort-button.sorted, .vote-view-button.active {
      background-color: #eef7f7;
      color: #378d8d;
    }

    /* 动画相关样式 */
    @keyframes fadeOut {
      from { opacity: 1; }
      to { opacity: 0; }
    }
    
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    
    .fade-out {
      animation: fadeOut 0.5s forwards;
    }
    
    .fade-in {
      animation: fadeIn 0.5s forwards;
    }
  `);
    }

    // 按钮创建和管理
    const Buttons = {
        createViewButton: () => {
            // 获取投票标题元素
            const voteTitle = DOM.get('.vote-title');
            if (!voteTitle) return;

            // 检查是否已存在按钮
            const existingButton = DOM.get('.vote-view-button');
            if (existingButton) {
                // 如果按钮已存在，确保事件监听器正确
                if (!existingButton._hasEventListener) {
                    existingButton.addEventListener('click', toggleResultView);
                    existingButton._hasEventListener = true;
                }
                updateButtonStates();
                return;
            }

            // 检查总票数是否为0
            const data = collectVoteData();
            if (data && data.totalVotes === 0) {
                // 如果总票数为0，不显示预览结果图标
                return;
            }

            // 创建查看结果按钮
            const viewButton = DOM.create('span', 'vote-view-button',
                `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye h-5 w-5"><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"></path><circle cx="12" cy="12" r="3"></circle></svg>`
            );

            // 添加点击事件
            viewButton.addEventListener('click', toggleResultView);
            viewButton._hasEventListener = true;

            // 添加到标题元素中
            voteTitle.appendChild(viewButton);
            updateButtonStates();
        },

        createSortButton: () => {
            // 获取投票标题元素
            const voteTitle = DOM.get('.vote-title');
            if (!voteTitle || DOM.get('.vote-sort-button')) return;

            // 创建排序按钮
            const sortButton = DOM.create('span', 'vote-sort-button',
                `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-up-down h-4 w-4"><path d="m21 16-4 4-4-4"></path><path d="M17 20V4"></path><path d="m3 8 4-4 4 4"></path><path d="M7 4v16"></path></svg>`
            );

            // 添加点击事件
            sortButton.addEventListener('click', () => {
                sortedByVotes = !sortedByVotes;
                sortVoteResults(true);
            });

            // 添加到标题元素中
            voteTitle.appendChild(sortButton);
            updateButtonStates();
        }
    };

    // 按钮状态更新
    function updateButtonStates() {
        // 更新查看按钮状态
        const viewButton = DOM.get('.vote-view-button');
        if (viewButton) {
            isViewingResults ? viewButton.classList.add('active') : viewButton.classList.remove('active');
        }

        // 更新排序按钮状态
        const sortButton = DOM.get('.vote-sort-button');
        if (sortButton) {
            sortedByVotes ? sortButton.classList.add('sorted') : sortButton.classList.remove('sorted');
        }
    }

    // 数据处理和解析
    function collectVoteData() {
        const optionRows = DOM.getAll('.vote-option-row');
        if (!optionRows.length) return null;

        let totalVotes = 0;
        const voteData = [];

        // 收集投票数据
        optionRows.forEach((row, index) => {
            const optionTextElement = row.querySelector('.vote-option-text');
            let optionText = '';

            if (optionTextElement) {
                // 获取完整的文本内容
                const fullText = optionTextElement.textContent.trim();

                // 查找序号元素
                const numberElement = optionTextElement.querySelector('.vote-option-number');
                if (numberElement) {
                    // 如果序号元素存在，获取其文本内容
                    const numberText = numberElement.textContent.trim();
                    // 从完整文本中移除序号文本
                    // 使用 replace 且只替换第一个匹配项来确保只移除开头的序号
                    optionText = fullText.replace(numberText, '').trim();
                } else {
                    // 如果没有找到序号元素，直接使用完整的文本内容
                    optionText = fullText;
                }
            }

            // 移除开头的空格
            optionText = optionText.trim();

            // 从隐藏元素中获取投票数据
            const voteNumElement = DOM.get(`.VON${index + 1}`);
            let votes = 0;

            if (voteNumElement) {
                // 解析投票数字
                const matches = voteNumElement.textContent.match(/\((\d+)\)/);
                if (matches && matches[1]) {
                    votes = parseInt(matches[1]);
                    totalVotes += votes;
                }
            }

            voteData.push({
                text: optionText,
                votes: votes
            });
        });

        return { voteData, totalVotes };
    }

    // 投票处理
    function handleVote(e) {
        // 如果用户已投票，阻止点击
        if (hasVoted) {
            if (e.target.classList.contains('vote-button')) {
                e.preventDefault();
                UI.showPopup('您已经投过票了');
            }
            return;
        }

        // 检查点击的是否是投票按钮
        if (!e.target.classList.contains('vote-button')) return;
        e.preventDefault();

        // 获取URL并发送请求
        const url = e.target.getAttribute('href');
        fetch(url, {
            method: 'GET',
            headers: { 'Content-Type': 'application/json' }
        })
            .then(response => response.text())
            .then(data => {
                if (data.includes('投票成功') || data.includes('投票成功！')) {
                    handleVoteSuccess();
                } else if (data.includes('你投过票了') || data.includes('你已经投过票了')) {
                    UI.showPopup('您已经投过票了');
                    hasVoted = true;
                    document.body.setAttribute('data-has-voted', 'true');
                    setTimeout(() => {
                        if (!DOM.get('.vote-sort-button')) {
                            Buttons.createSortButton();
                        }
                    }, 500);
                } else {
                    UI.showPopup('投票失败，请稍后再试');
                }
            })
            .catch(error => {
                console.error('投票失败', error);
                UI.showPopup('投票失败，请稍后再试');
            });
    }

    // 投票成功后的处理
    function handleVoteSuccess() {
        UI.showPopup('投票成功');
        hasVoted = true;
        document.body.setAttribute('data-has-voted', 'true');
        fetchVoteResults();

        setTimeout(() => {
            if (!DOM.get('.vote-sort-button')) {
                Buttons.createSortButton();
            }
        }, 1500);
    }

    // 获取投票结果并更新页面
    function fetchVoteResults() {
        fetch(window.location.href)
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newVoteContainer = doc.querySelector('.vote-container');

                if (newVoteContainer) {
                    animateVoteTransition(newVoteContainer.innerHTML);
                    setTimeout(() => saveOriginalOrder(), 600);
                }
            })
            .catch(error => {
                console.error('获取投票结果失败', error);
                setTimeout(() => window.location.reload(), 1500);
            });
    }

    // 切换结果视图
    function toggleResultView() {
        isViewingResults = !isViewingResults;
        console.log('Toggle result view, new state:', isViewingResults);
        updateButtonStates();

        if (isViewingResults) {
            showVoteResults();
        } else {
            hideVoteResults();
        }
    }

    // 显示投票结果
    function showVoteResults() {
        // 收集投票数据
        const data = collectVoteData();
        if (!data) return;

        const { voteData, totalVotes } = data;
        const voteContainer = DOM.get('.vote-container');

        // 保存原始内容
        const originalContent = voteContainer.innerHTML;
        voteContainer.setAttribute('data-original-content', originalContent);

        // 清空容器并添加新标题
        voteContainer.innerHTML = '';
        const titleElement = DOM.create('div', 'vote-title');
        titleElement.setAttribute('data-title', `单选投票: 共有${totalVotes}人参与`);
        voteContainer.appendChild(titleElement);

        // 重新添加查看按钮
        Buttons.createViewButton();

        // 为每个选项创建结果元素
        voteData.forEach((data, index) => {
            const percentage = totalVotes > 0 ? (data.votes / totalVotes * 100).toFixed(2) : 0;
            const resultElement = DOM.create('div', 'vote-option-result');

            resultElement.innerHTML = `
      <div class="vote-option-label">
        ${data.text}
      </div>
      <div class="vote-chart-container">
        <div class="vote-chart-bar" style="width: 0%;" data-width="${percentage}%"></div>
        <div class="vote-chart-text">${percentage}% (${data.votes})</div>
      </div>
    `;

            voteContainer.appendChild(resultElement);
        });

        // 添加提示消息
        const msgElement = DOM.create('div', 'vote-message', '这是预览结果，您尚未参与投票');
        voteContainer.appendChild(msgElement);

        // 在预览模式下不添加排序按钮
        // 只有在用户已经投票的情况下才添加排序按钮
        if (hasVoted) {
            Buttons.createSortButton();
        }

        // 保存原始顺序
        saveOriginalOrder();

        // 为结果条添加动画
        setTimeout(() => {
            const bars = DOM.getAll('.vote-chart-bar');
            bars.forEach(bar => {
                const targetWidth = bar.getAttribute('data-width');
                void bar.offsetWidth; // 触发重绘
                bar.style.transition = 'width 1s ease-out';
                bar.style.width = targetWidth;
            });
        }, 100);
    }

    // 隐藏投票结果
    function hideVoteResults() {
        const voteContainer = DOM.get('.vote-container');
        if (!voteContainer) return;

        // 先标记状态变更
        isViewingResults = false;
        updateButtonStates();

        // 恢复原始内容
        const originalContent = voteContainer.getAttribute('data-original-content');
        if (originalContent) {
            // 保存滚动位置
            const scrollTop = UI.saveScrollPosition();

            voteContainer.innerHTML = originalContent;

            // 重新初始化必要的组件
            if (document.body.getAttribute('data-has-voted') === 'true') {
                saveOriginalOrder();
                if (!DOM.get('.vote-sort-button')) {
                    Buttons.createSortButton();
                }
            } else {
                Buttons.createViewButton();
            }

            // 恢复滚动位置
            UI.restoreScrollPosition(scrollTop);
        }
    }

    // 保存原始顺序
    function saveOriginalOrder() {
        originalOrder = [];
        const resultElements = DOM.getAll('.vote-option-result');

        resultElements.forEach(element => {
            const label = element.querySelector('.vote-option-label').textContent;
            originalOrder.push({
                label: label,
                element: element
            });
        });
    }

    // 结果排序
    function sortVoteResults(forceSort = false) {
        const voteContainer = DOM.get('.vote-container');
        if (!voteContainer) return;

        const resultElements = DOM.getAll('.vote-option-result');
        if (resultElements.length === 0) return;

        // 判断是否需要排序
        if (!forceSort && !sortedByVotes) return;

        // 收集排序数据
        const results = [];
        resultElements.forEach(element => {
            const chartText = element.querySelector('.vote-chart-text');
            if (!chartText) return;

            const label = element.querySelector('.vote-option-label').textContent;
            const matches = chartText.textContent.match(/\((\d+)\)/);
            const votes = matches && matches[1] ? parseInt(matches[1]) : 0;

            results.push({
                element: element,
                votes: votes,
                label: label
            });
        });

        // 检查是否有非零票数
        const hasNonZeroVotes = results.some(result => result.votes > 0);

        if (hasNonZeroVotes || forceSort) {
            // 获取父元素并移除所有结果
            const parent = resultElements[0].parentNode;
            resultElements.forEach(el => el.remove());

            // 根据排序状态选择排序方式
            if (sortedByVotes) {
                // 按票数排序
                results.sort((a, b) => b.votes - a.votes);
                results.forEach(result => parent.appendChild(result.element));
            } else {
                // 恢复原始顺序
                originalOrder.forEach(item => {
                    const matchingResult = results.find(r => r.label === item.label);
                    if (matchingResult) {
                        parent.appendChild(matchingResult.element);
                    }
                });
            }

            // 确保消息元素在最后
            const voteMessage = DOM.get('.vote-message');
            if (voteMessage) {
                voteMessage.remove();
                parent.appendChild(voteMessage);
            }

            // 更新按钮状态
            updateButtonStates();
        }
    }

    // 动画过渡
    function animateVoteTransition(newHTML) {
        const voteContainer = DOM.get('.vote-container');
        if (!voteContainer) return;

        // 淡出当前内容
        voteContainer.classList.add('fade-out');

        setTimeout(() => {
            // 更新HTML内容
            voteContainer.innerHTML = newHTML;
            voteContainer.classList.remove('fade-out');
            voteContainer.classList.add('fade-in');

            // 处理标题文本，避免重复显示
            const titleElement = DOM.get('.vote-title');
            if (titleElement) {
                const titleText = titleElement.textContent.trim();
                titleElement.setAttribute('data-title', titleText);
                titleElement.textContent = '';
            }

            // 应用排序和UI更新
            sortVoteResults();
            updateVoteUIAfterVoting();

            // 为结果条添加动画
            setTimeout(() => {
                const bars = DOM.getAll('.vote-chart-bar');
                bars.forEach(bar => {
                    const currentWidth = bar.style.width;
                    bar.style.width = '0%';
                    void bar.offsetWidth; // 触发重绘
                    bar.style.transition = 'width 1s ease-out';
                    bar.style.width = currentWidth;
                });

                // 确保排序按钮存在（仅在已投票状态下添加）
                if (!DOM.get('.vote-sort-button') && hasVoted && !isViewingResults) {
                    Buttons.createSortButton();
                }
            }, 100);
        }, 500);
    }

    // 投票后更新UI
    function updateVoteUIAfterVoting() {
        // 重置排序状态
        sortedByVotes = false;

        // 获取总票数
        let totalVotes = 0;
        try {
            DOM.getAll('.vote-chart-text').forEach(text => {
                const matches = text.textContent.match(/\((\d+)\)/);
                if (matches && matches[1]) {
                    totalVotes += parseInt(matches[1]);
                }
            });
        } catch (e) {
            console.error('解析票数时出错:', e);
        }

        // 更新标题
        const voteTitle = DOM.get('.vote-title');
        if (voteTitle) {
            voteTitle.setAttribute('data-title', `单选投票: 共有${totalVotes}人参与`);
            Buttons.createSortButton();
        }

        // 添加图表淡入效果
        const chartContainers = DOM.getAll('.vote-chart-container');
        chartContainers.forEach(container => {
            container.style.opacity = '0';
            setTimeout(() => {
                container.style.transition = 'opacity 0.8s ease';
                container.style.opacity = '1';
            }, 300);
        });

        // 保存原始顺序
        setTimeout(() => saveOriginalOrder(), 400);
    }

    // 初始化序号
    function initNumbering() {
        const optionRows = DOM.getAll('.vote-option-row');

        optionRows.forEach((row, index) => {
            const optionText = row.querySelector('.vote-option-text');

            // 检查是否已有序号
            if (optionText && !optionText.querySelector('.vote-option-number')) {
                const numberElement = DOM.create('div', 'vote-option-number', (index + 1).toString());
                optionText.insertBefore(numberElement, optionText.firstChild);
            }
        });
    }

    // 初始化投票模块
    function initVoting() {
        // 读取投票状态
        hasVoted = document.body.getAttribute('data-has-voted') === 'true';

        // 设置标题文本属性
        const titleElement = DOM.get('.vote-title');
        if (titleElement && !titleElement.hasAttribute('data-title')) {
            titleElement.setAttribute('data-title', titleElement.textContent);
            titleElement.textContent = '';
        }

        // 添加序号
        initNumbering();

        // 根据投票状态添加相应按钮
        if (hasVoted) {
            saveOriginalOrder();
            Buttons.createSortButton();
            sortedByVotes = false;
        } else {
            Buttons.createViewButton();
        }
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function () {
        // 添加全局样式
        addCoreStyles();

        // 初始化投票组件
        initVoting();

        // 添加投票容器的点击事件监听
        const toupiaoDiv = DOM.get('.vote-container');
        if (toupiaoDiv) {
            toupiaoDiv.addEventListener('click', handleVote);
        }
    });
})();