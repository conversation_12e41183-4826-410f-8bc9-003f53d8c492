using System;
using System.Collections.Generic;
using System.Linq;
using Serilog;
using Serilog.Events;

namespace YaoHuo.Plugin.OAuth
{
    /// <summary>
    /// OAuth 安全事件日志记录服务
    /// 记录关键的 OAuth 操作用于安全审计和问题排查
    /// </summary>
    public static class OAuthLogger
    {
        // 使用全局 Logger 的 OAuth 上下文
        private static ILogger Logger => Log.ForContext("SourceContext", "OAuth");

        // 日志系统初始化标志
        private static bool _isInitialized = false;
        private static readonly object _initLock = new object();
        
        // 错误计数器和最后记录时间
        private static readonly Dictionary<string, (int Count, DateTime LastLogged)> _errorCounts = 
            new Dictionary<string, (int Count, DateTime LastLogged)>();
        private static readonly object _lockObj = new object();
            
        // 默认阈值：相同错误每10分钟最多记录5次
        private static readonly TimeSpan ERROR_WINDOW = TimeSpan.FromMinutes(10);
        private static readonly int ERROR_THRESHOLD = 5;

        /// <summary>
        /// 确保日志系统已正确初始化
        /// </summary>
        private static void EnsureLoggerInitialized()
        {
            if (_isInitialized) return;

            lock (_initLock)
            {
                if (_isInitialized) return;

                try
                {
                    // 检查当前 Logger 是否为 SilentLogger（表示未初始化）
                    if (!Log.Logger.IsEnabled(LogEventLevel.Fatal))
                    {
                        // 重新初始化日志系统
                        YaoHuo.Plugin.WebSite.Tool.LoggingConfigLoader.Initialize();

                        // 记录初始化日志
                        Log.Information("OAuth日志系统延迟初始化完成");
                    }

                    _isInitialized = true;
                }
                catch (Exception ex)
                {
                    // 初始化失败时使用 Debug 输出
                    System.Diagnostics.Debug.WriteLine($"OAuth日志系统初始化失败: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 检查是否应该记录此错误（错误计数机制）
        /// </summary>
        private static bool ShouldLogError(string errorKey)
        {
            lock (_lockObj)
            {
                // 如果是新错误，记录并返回true
                if (!_errorCounts.ContainsKey(errorKey))
                {
                    _errorCounts[errorKey] = (1, DateTime.Now);
                    return true;
                }
                
                var (count, lastLogged) = _errorCounts[errorKey];
                var now = DateTime.Now;
                
                // 如果已经超过时间窗口，重置计数
                if (now - lastLogged > ERROR_WINDOW)
                {
                    _errorCounts[errorKey] = (1, now);
                    return true;
                }
                
                // 增加计数
                _errorCounts[errorKey] = (count + 1, lastLogged);
                
                // 如果未超过阈值，记录错误
                if (count < ERROR_THRESHOLD)
                {
                    return true;
                }
                
                // 超过阈值但是整倍数时，记录一条汇总错误
                if (count % ERROR_THRESHOLD == 0)
                {
                    Logger.Warning(
                        "错误频率过高: {ErrorKey} 在过去{WindowMinutes}分钟内发生了{Count}次 {Timestamp}", 
                        errorKey, ERROR_WINDOW.TotalMinutes, count, DateTime.Now
                    );
                }
                
                // 其他情况不记录
                return false;
            }
        }

        /// <summary>
        /// 清理过期的错误计数
        /// </summary>
        public static void CleanupErrorCounts()
        {
            var cutoff = DateTime.Now - TimeSpan.FromHours(1);
            lock (_lockObj)
            {
                var keysToRemove = _errorCounts
                    .Where(kv => kv.Value.LastLogged < cutoff)
                    .Select(kv => kv.Key)
                    .ToList();

                foreach (var key in keysToRemove)
                {
                    _errorCounts.Remove(key);
                }
            }
        }

        /// <summary>
        /// 清理敏感或危险的日志内容，防止日志注入攻击
        /// </summary>
        /// <param name="input">原始输入</param>
        /// <param name="maxLength">最大长度</param>
        /// <returns>清理后的安全内容</returns>
        private static string SanitizeLogInput(string input, int maxLength = 256)
        {
            if (string.IsNullOrEmpty(input))
                return "empty";
                
            // 1. 长度限制
            if (input.Length > maxLength)
                input = input.Substring(0, maxLength) + "...";
                
            // 2. 移除换行符防止CRLF注入
            input = input.Replace('\r', ' ').Replace('\n', ' ');
            
            // 3. 移除制表符和其他控制字符
            input = input.Replace('\t', ' ');
            
            // 4. 压缩多个空格
            while (input.Contains("  "))
                input = input.Replace("  ", " ");
                
            return input.Trim();
        }
        
        /// <summary>
        /// 清理URI类型的输入，提供更严格的验证和清理
        /// </summary>
        /// <param name="uri">原始URI</param>
        /// <param name="maxLength">最大长度</param>
        /// <returns>清理后的URI</returns>
        private static string SanitizeUri(string uri, int maxLength = 512)
        {
            if (string.IsNullOrEmpty(uri))
                return "empty";
                
            try
            {
                // 验证是否为有效URI
                var parsedUri = new Uri(uri);
                
                // 只记录协议、主机和路径，移除查询参数（可能包含敏感信息）
                var sanitized = $"{parsedUri.Scheme}://{parsedUri.Host}{parsedUri.AbsolutePath}";
                
                return SanitizeLogInput(sanitized, maxLength);
            }
            catch
            {
                // 如果不是有效URI，进行基本清理
                return SanitizeLogInput(uri, maxLength);
            }
        }

        /// <summary>
        /// 记录授权请求事件
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="scope">请求的权限范围</param>
        /// <param name="redirectUri">重定向URI</param>
        /// <param name="ip">客户端IP地址</param>
        /// <param name="userAgent">用户代理字符串</param>
        public static void LogAuthorizeRequest(string clientId, long userId, string scope, string redirectUri, string ip, string userAgent = null)
        {
            EnsureLoggerInitialized();
            Logger.Information(
                "OAuth授权请求 {Event} {ClientId} {UserId} {Scope} {RedirectUri} {IP} {UserAgent} {Timestamp}", 
                "oauth_authorize_request", 
                SanitizeLogInput(clientId, 64), 
                userId, 
                SanitizeLogInput(scope, 128), 
                SanitizeUri(redirectUri), 
                SanitizeLogInput(ip, 45), // IPv6最大长度为39，留一些余量
                SanitizeLogInput(userAgent, 256), 
                DateTime.Now
            );
        }
        
        /// <summary>
        /// 记录授权决定事件（用户同意或拒绝）
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="approved">是否同意授权</param>
        /// <param name="ip">客户端IP地址</param>
        /// <param name="userAgent">用户代理字符串</param>
        public static void LogAuthorizeDecision(string clientId, long userId, bool approved, string ip, string userAgent = null)
        {
            EnsureLoggerInitialized();
            Logger.Information(
                "OAuth授权决定 {Event} {ClientId} {UserId} {Decision} {IP} {UserAgent} {Timestamp}", 
                "oauth_authorize_decision", 
                SanitizeLogInput(clientId, 64), 
                userId, 
                approved ? "approved" : "rejected", 
                SanitizeLogInput(ip, 45), 
                SanitizeLogInput(userAgent, 256), 
                DateTime.Now
            );
        }
        
        /// <summary>
        /// 记录Token交换事件
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="grantType">授权类型</param>
        /// <param name="success">是否成功</param>
        /// <param name="ip">客户端IP地址</param>
        /// <param name="userAgent">用户代理字符串</param>
        public static void LogTokenExchange(string clientId, string grantType, bool success, string ip, string userAgent = null)
        {
            EnsureLoggerInitialized();
            Logger.Information(
                "OAuth Token交换 {Event} {ClientId} {GrantType} {Success} {IP} {UserAgent} {Timestamp}", 
                "oauth_token_exchange", 
                SanitizeLogInput(clientId, 64), 
                SanitizeLogInput(grantType, 64), 
                success, 
                SanitizeLogInput(ip, 45), 
                SanitizeLogInput(userAgent, 256), 
                DateTime.Now
            );
        }
        
        /// <summary>
        /// 记录Token撤销事件
        /// </summary>
        /// <param name="clientId">客户端ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="ip">客户端IP地址</param>
        /// <param name="userAgent">用户代理字符串</param>
        public static void LogTokenRevoke(string clientId, long userId, string ip, string userAgent = null)
        {
            EnsureLoggerInitialized();
            Logger.Information(
                "OAuth令牌撤销 {Event} {ClientId} {UserId} {IP} {UserAgent} {Timestamp}", 
                "oauth_token_revoke", 
                SanitizeLogInput(clientId, 64), 
                userId, 
                SanitizeLogInput(ip, 45), 
                SanitizeLogInput(userAgent, 256), 
                DateTime.Now
            );
        }
        
        /// <summary>
        /// 记录OAuth错误事件
        /// </summary>
        /// <param name="errorType">错误类型</param>
        /// <param name="clientId">客户端ID</param>
        /// <param name="details">错误详情</param>
        /// <param name="ip">客户端IP地址</param>
        /// <param name="userAgent">用户代理字符串</param>
        public static void LogError(string errorType, string clientId, string details, string ip, string userAgent = null)
        {
            // 创建错误键
            string errorKey = $"{SanitizeLogInput(clientId, 32)}:{SanitizeLogInput(errorType, 32)}";
            
            // 检查是否应记录此错误
            if (ShouldLogError(errorKey))
            {
                EnsureLoggerInitialized();
                Logger.Error(
                    "OAuth错误 {Event} {ErrorType} {ClientId} {Details} {IP} {UserAgent} {Timestamp}", 
                    "oauth_error", 
                    SanitizeLogInput(errorType, 64), 
                    SanitizeLogInput(clientId, 64), 
                    SanitizeLogInput(details, 512), // 错误详情可能较长
                    SanitizeLogInput(ip, 45), 
                    SanitizeLogInput(userAgent, 256), 
                    DateTime.Now
                );
            }
        }

        /// <summary>
        /// 记录OAuth管理操作事件
        /// </summary>
        /// <param name="action">操作类型</param>
        /// <param name="clientId">客户端ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="details">操作详情</param>
        /// <param name="ip">客户端IP地址</param>
        /// <param name="userAgent">用户代理字符串</param>
        public static void LogAdminAction(string action, string clientId, long userId, string details, string ip, string userAgent = null)
        {
            EnsureLoggerInitialized();
            Logger.Information(
                "OAuth管理操作 {Event} {Action} {ClientId} {UserId} {Details} {IP} {UserAgent} {Timestamp}", 
                "oauth_admin_action", 
                SanitizeLogInput(action, 64), 
                SanitizeLogInput(clientId, 64), 
                userId, 
                SanitizeLogInput(details, 256), 
                SanitizeLogInput(ip, 45), 
                SanitizeLogInput(userAgent, 256), 
                DateTime.Now
            );
        }

        /// <summary>
        /// 记录详细的异常信息
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="context">上下文信息</param>
        /// <param name="clientId">客户端ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="ip">客户端IP地址</param>
        /// <param name="userAgent">用户代理字符串</param>
        public static void LogException(Exception ex, string context, string clientId = null, long userId = 0, string ip = null, string userAgent = null)
        {
            // 创建错误键
            string errorKey = $"{SanitizeLogInput(clientId ?? "unknown", 32)}:{SanitizeLogInput(context, 32)}:{ex.GetType().Name}";
            
            // 检查是否应记录此错误
            if (ShouldLogError(errorKey))
            {
                EnsureLoggerInitialized();
                Logger.Error(
                    ex,
                    "OAuth异常 {Event} {Context} {ClientId} {UserId} {IP} {UserAgent} {ExceptionType} {ExceptionMessage} {Timestamp}", 
                    "oauth_exception", 
                    SanitizeLogInput(context, 128), 
                    SanitizeLogInput(clientId ?? "unknown", 64), 
                    userId, 
                    SanitizeLogInput(ip ?? "unknown", 45), 
                    SanitizeLogInput(userAgent, 256), 
                    ex.GetType().Name, 
                    SanitizeLogInput(ex.Message, 512), // 异常消息可能较长
                    DateTime.Now
                );
            }
        }

        /// <summary>
        /// 关闭日志系统，确保所有日志都被写入
        /// </summary>
        public static void Close()
        {
            try
            {
                // 记录关闭事件
                Logger.Information("OAuth日志系统关闭");
                
                // 不需要调用 Log.CloseAndFlush()，由 Global.Application_End 统一处理
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"关闭OAuth日志系统异常: {ex.Message}");
            }
        }
    }
}