<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<!-- 应用程序配置 - 引用外部配置文件 -->
	<appSettings configSource="AppSettings.config" />

	<connectionStrings>
		<add name="kelinkWAP_CheckConnectionString1" connectionString="Data Source=ITSERVICE;Initial Catalog=kelinkWAP_Check;Persist Security Info=True;User ID=sa;MultipleActiveResultSets=False;Packet Size=4096;Application Name=&quot;Microsoft SQL Server Management Studio&quot;" providerName="System.Data.SqlClient" />
	</connectionStrings>

	<!-- 程序集绑定重定向 - 解决 NuGet 包版本冲突 -->
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<!-- Serilog 版本重定向 -->
			<dependentAssembly>
				<assemblyIdentity name="Serilog" culture="neutral" publicKeyToken="24c2f752a8e58a10" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<!-- System.Runtime.CompilerServices.Unsafe 版本重定向 -->
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" culture="neutral" publicKeyToken="b03f5f7f11d50a3a" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>

	<system.web>

		<!--允许上传文件大小及连接超时时间，4.0请求验证改成2.0验证方式-->
		<httpRuntime maxRequestLength="102400" executionTimeout="300" requestValidationMode="2.0" />
		<!--是否开启请求验证机制-->
		<pages validateRequest="false" />

		<!-- 是否开启跳转到错误页，改为 Off 则显示详细错误信息 -->
		<customErrors defaultRedirect="/Pages/404.htm" mode="Off"></customErrors>
		<!--控制 ASP.NET 是否在调试模式下运行-->
		<compilation debug="true">
			<assemblies>
				<add assembly="System.Design, Version=2.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A" />
			</assemblies>
		</compilation>

		<!-- 身份验证模式-->
		<authentication mode="Windows" />

		<!-- 指定应用程序的字符编码、请求和响应的编码方式-->
		<!-- <globalization fileEncoding='UTF-8' requestEncoding='UTF-8' responseEncoding='UTF-8' culture='zh-CN'/>-->

		<!-- 底层审核图片/文件的类型，可自行配置 -->
		<!-- <httpHandlers>
      <add verb="*" path="*.jpg,*.jpeg,*.gif,*.png,*.webp" type="KeLink.Com.ImgProtectHadler"/>
    </httpHandlers> -->

	</system.web>


	<!-- 在 Internet 信息服务 7.0 下运行 ASP.NET AJAX 需要 system.webServer -->
	<system.webServer>

		<staticContent>
			<mimeMap fileExtension=".webp" mimeType="image/webp" />
			<mimeMap fileExtension=".py" mimeType="application/x-python" />
			<clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="30.00:00:00" cacheControlCustom="public" />
		</staticContent>

		<defaultDocument>
			<files>
				<clear />
				<add value="wapindex.aspx" />
				<add value="default.aspx" />
				<add value="Default.htm" />
				<add value="index.aspx" />
				<add value="index.html" />
				<add value="index.htm" />
			</files>
		</defaultDocument>

		<rewrite>
			<rules configSource="rewriteRules.config"></rules>
		</rewrite>

		<httpErrors errorMode="DetailedLocalOnly" defaultResponseMode="File">
			<remove statusCode="404" />
			<error statusCode="404" path="/Pages/404.htm" responseMode="ExecuteURL" />
			<remove statusCode="502" />
			<error statusCode="502" path="/Pages/404.htm" responseMode="ExecuteURL" />
		</httpErrors>

		<caching>
			<profiles>
				<add extension=".png" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" />
				<add extension=".gif" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" />
				<add extension=".jpg" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" />
				<add extension=".jpeg" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" />
				<add extension=".webp" policy="CacheUntilChange" kernelCachePolicy="CacheUntilChange" />
			</profiles>
		</caching>

		<httpProtocol>
			<customHeaders>
				<remove name="Vary" />
				<add name="Vary" value="User-Agent" />
				<add name="X-Frame-Options" value="sameorigin" />
				<remove name="Permissions-Policy" />
				<add name="Permissions-Policy" value="run-ad-auction=(), join-ad-interest-group=()" />
			</customHeaders>
		</httpProtocol>

		<urlCompression doStaticCompression="true" doDynamicCompression="true" />

		<security>
			<requestFiltering>
				<!-- 限制上传文件大小为 100MB (104857600 字节) -->
				<requestLimits maxAllowedContentLength="104857600" />

				<!-- 添加 HTTP 方法限制，禁止 OPTIONS 请求 -->
				<verbs allowUnlisted="true">
					<add verb="OPTIONS" allowed="false" />
				</verbs>

				<!-- 隐藏敏感配置目录 -->
				<denyUrlSequences>
					<add sequence="Data/ModuleConfigs" />
					<add sequence="Data/GlobalConfigs" />
					<add sequence="Data/Cache" />
				</denyUrlSequences>
			</requestFiltering>
		</security>

	</system.webServer>

	<!-- 静态数据目录配置 -->
	<location path="Data/StaticData">
		<system.webServer>
			<staticContent>
				<!-- JSON文件缓存1小时 -->
				<clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="1.00:00:00" cacheControlCustom="public" />
			</staticContent>
		</system.webServer>
	</location>

	<system.codedom>
		<compilers>
			<compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
			<compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
		</compilers>
	</system.codedom>

</configuration>