﻿using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using System;
using System.Web;
using Dapper;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class SendMoney_FreeMain : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string reid = "";

        public string page = "";

        public string lpage = "";

        public string ot = "";

        public string INFO = "";

        public string ERROR = "";

        public wap_bbsre_Model bbsReVo = null;

        public wap_bbs_Model bbsVo = null;

        public string touserid = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
            }
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            reid = GetRequestValue("reid");
            page = GetRequestValue("page");
            lpage = GetRequestValue("lpage");
            ot = GetRequestValue("ot");
            touserid = GetRequestValue("touserid");
            if (!WapTool.IsNumeric(touserid))
            {
                touserid = "0";
            }
            //IsCheckManagerLvl("|00|01|03|04|", classVo.adminusername, GetUrlQueryString());
            //needPassWordToAdmin();
            if (!(action == "gomod"))
            {
                return;
            }
            try
            {
                string requestValue = GetRequestValue("sendmoney");
                if (!WapTool.IsNumeric(requestValue) || touserid == "0" || touserid == userVo.userid.ToString())
                {
                    INFO = "ERR";
                    return;
                }
                if (long.Parse(requestValue) < 1L)
                {
                    INFO = "ERR";
                    return;
                }
                if (userVo.money < long.Parse(requestValue))
                {
                    INFO = "ERR";
                    return;
                }
                //判断请求方式
                if (HttpContext.Current.Request.HttpMethod != "POST")
                {
                    INFO = "ERR";
                    return;
                }
                //判断值是否合理
                if (!SendMoneyService.IsReasonable(requestValue))
                {
                    INFO = "ERR";
                    return;
                }
                // ✅ 使用TransactionHelper进行安全的事务性资金操作
                string connectionString = PubConstant.GetConnectionString(a);
                long originalAmount = long.Parse(requestValue);
                long userIdLong = DapperHelper.SafeParseLong(userid, "用户ID");
                long toUserIdLong = DapperHelper.SafeParseLong(touserid, "目标用户ID");
                long postIdLong = DapperHelper.SafeParseLong(id, "帖子ID");
                long siteIdLong = DapperHelper.SafeParseLong(siteid, "站点ID");

                // 计算手续费
                var handlingFee = SendMoneyService.GetHandlingFee(requestValue);
                long finalAmount = originalAmount - handlingFee;

                TransactionHelper.ExecuteMoneyTransaction(connectionString, (connection, transaction) =>
                {
                    // 1. 扣除打赏者金币（原始金额）
                    string deductSql = "UPDATE [user] SET money = money - @Amount WHERE userid = @UserId";
                    connection.Execute(deductSql, new {
                        Amount = originalAmount,
                        UserId = userIdLong
                    }, transaction);

                    // 2. 增加被打赏者金币（扣除手续费后的金额）
                    string addSql = "UPDATE [user] SET money = money + @Amount WHERE userid = @UserId";
                    connection.Execute(addSql, new {
                        Amount = finalAmount,
                        UserId = toUserIdLong
                    }, transaction);

                    // 3. 更新帖子获得金币
                    string updatePostSql = "UPDATE [wap_bbs] SET mygetmoney = mygetmoney + @Amount WHERE id = @PostId AND userid = @SiteId AND book_pub = @AuthorId";
                    connection.Execute(updatePostSql, new {
                        Amount = finalAmount,
                        PostId = postIdLong,
                        SiteId = siteIdLong,
                        AuthorId = touserid
                    }, transaction);

                    // 4. 发送系统消息
                    string messageTitle = "恭喜，" + userVo.nickname + "打赏" + finalAmount + "个妖晶给您！";
                    string remark = GetRequestValue("remark");
                    string remarkText = string.IsNullOrEmpty(remark) ? "您的帖子获得赞赏！" : remark;
                    string messageContent = "备注原因：" + messageTitle + " [[url=" + http_start + "bbs-" + id + ".html]进入帖子查看[/url]]";

                    string insertMessageSql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                                               VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, 1)";
                    connection.Execute(insertMessageSql, new {
                        SiteId = siteIdLong,
                        UserId = userIdLong,
                        Nickname = DapperHelper.LimitLength(userVo.nickname, 50),
                        Title = DapperHelper.LimitLength(messageTitle, 100),
                        Content = DapperHelper.LimitLength(messageContent, 500),
                        ToUserId = toUserIdLong
                    }, transaction);
                });

                // ✅ 先计算新余额，避免SaveBankLog中的SELECT操作导致死锁
                string bankLogConnectionString = PubConstant.GetConnectionString(a);

                // 打赏者扣费后的余额
                long senderNewBalance = userVo.money - originalAmount;

                // 获取被打赏者当前余额（事务已更新）
                string getReceiverMoneySql = "SELECT money FROM [user] WHERE userid = @UserId AND siteid = @SiteId";
                long receiverCurrentMoney = DapperHelper.ExecuteScalar<long>(bankLogConnectionString, getReceiverMoneySql, new {
                    UserId = toUserIdLong,
                    SiteId = siteIdLong
                });

                // ✅ 使用SaveBankLogWithBalance替换SaveBankLog，避免死锁
                SaveBankLogWithBalance(userid, "打赏送币", "-" + originalAmount.ToString(), userid, nickname, "打赏给会员ID(" + touserid + ")", senderNewBalance);
                SaveBankLogWithBalance(touserid, "打赏送币", finalAmount.ToString(), userid, nickname, "发帖获得打赏", receiverCurrentMoney);

                // 更新requestValue为最终金额，供后续使用
                requestValue = finalAmount.ToString();
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}