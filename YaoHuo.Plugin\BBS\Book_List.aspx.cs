﻿using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.WebSite.BBS.Service;


namespace YaoHuo.Plugin.BBS
{
    /// <summary>
    /// 帖子列表类型枚举
    /// </summary>
    public enum PostListType
    {
        Class,    // 版块帖子
        New,      // 最新帖子
        Good,     // 精华帖子
        Hot       // 热门帖子
    }

    public class Book_List : BaseBBSListPage
    {
        public string stypename = "";
        public string stypelink = "";
        public string type = "";
        public string key = "";
        public List<wap_bbs_Model> listVoTop = null;
        public string downLink = "";
        public string titlecolor = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            hots = WapTool.GetSiteDefault(siteVo.Version, 41);
            if (!WapTool.IsNumeric(hots))
            {
                hots = "5000";
            }
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
            }
            action = GetRequestValue("action");
            if (classid == "0")
            {
                classVo.introduce = "";
                classVo.sitedowntip = "";
            }
            if (!IsCheckManagerLvl("|00|01|03|04|", "") && "1".Equals(WapTool.GetArryString(classVo.smallimg, '|', 0)))
            {
                ShowTipInfo("此版块已关闭！", "wapindex.aspx?siteid=" + siteid + "&amp;classid=" + classVo.childid);
            }
            titlecolor = WapTool.GetArryString(classVo.smallimg, '|', 42);
            switch (action)
            {
                case "search":
                    ShowSearch();
                    break;

                case "good":
                    ShowGood();
                    break;

                case "class":
                    ShowClass();
                    break;

                case "hot":
                    ShowHot();
                    break;

                case "new":
                    ShowNew();
                    break;

                default:
                    ShowClass();
                    break;
            }
            downLink = WapTool.GetArryString(classVo.smallimg, '|', 19).Trim().Replace("[stype]", stype);
            LoadUserInfo();
        }
        public void ShowClass()
        {
            if (classid == "0")
            {
                ShowTipInfo("无此版块ID", "");
            }

            // ✅ 使用安全的参数化查询构建条件
            var queryBuilder = new QueryBuilder()
                .Where("userid = @ParamN", DapperHelper.SafeParseLong(siteid, "站点ID"))
                .Where("book_classid = @ParamN", DapperHelper.SafeParseLong(classid, "版块ID"))
                .Where("ischeck = @ParamN", 0);

            stype = GetRequestValue("stype");
            if (WapTool.IsNumeric(stype))
            {
                queryBuilder.Where("topic = @ParamN", DapperHelper.SafeParseLong(stype, "主题类型"));
                stypename = WapTool.GetSmallTypeName(siteid, stype);
                stypelink = "&amp;stype=" + stype;
            }

            // ✅ 添加用户黑名单过滤（修复版块列表黑名单不生效的问题）
            string connectionString = PubConstant.GetConnectionString(a);
            AddUserBlockingFilter(queryBuilder, userid, connectionString);

            try
            {
                // ✅ 使用统一的帖子列表处理逻辑
                ShowPostListCommon(PostListType.Class, queryBuilder,
                    "正在浏览:<a href=\"" + http_start + GetUrlQueryString() + "\">" + classVo.classname + "</a>");
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }

        /// <summary>
        /// 安全地获取帖子列表
        /// </summary>
        private List<wap_bbs_Model> GetPostListSafely(QueryBuilder baseQueryBuilder, string connectionString, string stype, long pageSize, long currentPage)
        {
            // ✅ 使用Clone()方法复制基础条件
            var finalQueryBuilder = baseQueryBuilder.Clone();

            // 添加主题类型条件
            if (WapTool.IsNumeric(stype))
            {
                finalQueryBuilder.Where("topic = @ParamN", DapperHelper.SafeParseLong(stype, "主题类型"));
            }

            if (stype == "")
            {
                // 普通列表，排除置顶帖子
                finalQueryBuilder.Where("book_top = @ParamN", 0);
            }

            // 确定排序方式
            string orderBy = (stype != "" && "1".Equals(WapTool.GetArryString(classVo.smallimg, '|', 14))) ? "ORDER BY id DESC" : "ORDER BY reDate DESC";

            // 构建查询SQL（添加freeleftMoney字段，添加NOLOCK提示避免死锁）
            var (sql, parameters) = finalQueryBuilder.Build("SELECT book_classid,id,book_title,book_date,book_click,book_re,book_author,book_pub,book_top,book_good,topic,islock,ischeck,sendMoney,isvote,reDate,isdown,hangbiaoshi,freeMoney,freeleftMoney,book_img,MarkSixBetID,MarkSixWin FROM wap_bbs WITH (NOLOCK)");

            // 添加排序和分页
            long offset = (currentPage - 1) * pageSize;
            sql += $" {orderBy} OFFSET {offset} ROWS FETCH NEXT {pageSize} ROWS ONLY";

            return DapperHelper.Query<wap_bbs_Model>(connectionString, sql, parameters)?.ToList() ?? new List<wap_bbs_Model>();
        }

        /// <summary>
        /// 安全地获取帖子列表（支持不同列表类型）
        /// </summary>
        private List<wap_bbs_Model> GetPostListSafely(QueryBuilder baseQueryBuilder, string connectionString, PostListType listType, long pageSize, long currentPage)
        {
            // ✅ 使用Clone()方法复制基础条件
            var finalQueryBuilder = baseQueryBuilder.Clone();

            // 根据列表类型确定排序方式
            string orderBy;
            switch (listType)
            {
                case PostListType.New:
                    orderBy = "ORDER BY id DESC";
                    break;
                case PostListType.Good:
                    orderBy = "ORDER BY id DESC";
                    break;
                case PostListType.Class:
                default:
                    orderBy = (stype != "" && "1".Equals(WapTool.GetArryString(classVo.smallimg, '|', 14))) ? "ORDER BY id DESC" : "ORDER BY reDate DESC";
                    break;
            }

            // 构建查询SQL（添加freeleftMoney字段，添加NOLOCK提示避免死锁）
            var (sql, parameters) = finalQueryBuilder.Build("SELECT book_classid,id,book_title,book_date,book_click,book_re,book_author,book_pub,book_top,book_good,topic,islock,ischeck,sendMoney,isvote,reDate,isdown,hangbiaoshi,freeMoney,freeleftMoney,book_img,MarkSixBetID,MarkSixWin FROM wap_bbs WITH (NOLOCK)");

            // 添加排序和分页
            long offset = (currentPage - 1) * pageSize;
            sql += $" {orderBy} OFFSET {offset} ROWS FETCH NEXT {pageSize} ROWS ONLY";

            return DapperHelper.Query<wap_bbs_Model>(connectionString, sql, parameters)?.ToList() ?? new List<wap_bbs_Model>();
        }

        public void ShowNew()
        {
            QueryBuilder queryBuilder;

            if (classid == "0")
            {
                // ✅ 全站新帖 - 使用安全的参数化查询
                queryBuilder = new QueryBuilder()
                    .Where("ischeck = @ParamN", 0)
                    .Where("userid = @ParamN", DapperHelper.SafeParseLong(siteid, "站点ID"));

                // 处理排除的版块ID
                string excludedClassIDsConfig = PubConstant.GetAppString("KL_BBS_ExcludeClassIDs");
                if (!string.IsNullOrEmpty(excludedClassIDsConfig))
                {
                    string[] idsToExclude = excludedClassIDsConfig.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                    var validIds = idsToExclude.Where(id => long.TryParse(id.Trim(), out _))
                                              .Select(id => id.Trim())
                                              .ToList();
                    if (validIds.Count > 0)
                    {
                        // ✅ 使用QueryBuilder的WhereNotIn方法
                        var excludeIds = validIds.Select(id => (object)long.Parse(id)).ToList();
                        queryBuilder.WhereNotIn("book_classid", excludeIds);
                    }
                }

                classVo.classid = 0L;
                classVo.position = "left";
                classVo.classname = "所有最新帖子";
                classVo.siteimg = "NetImages/no.gif";
                classVo.introduce = "";
            }
            else
            {
                // ✅ 特定版块新帖 - 使用安全的参数化查询（简化版，不支持层级查询）
                classVo.classname += "・最新帖子";
                queryBuilder = new QueryBuilder()
                    .Where("ischeck = @ParamN", 0)
                    .Where("userid = @ParamN", DapperHelper.SafeParseLong(siteid, "站点ID"))
                    .Where("book_classid = @ParamN", DapperHelper.SafeParseLong(classid, "版块ID"));
            }

            // ✅ 添加用户黑名单过滤
            string connectionString = PubConstant.GetConnectionString(a);
            AddUserBlockingFilter(queryBuilder, userid, connectionString);

            try
            {
                // ✅ 使用统一的帖子列表处理逻辑
                ShowPostListCommon(PostListType.New, queryBuilder,
                    "正在浏览最新:<a href=\"" + http_start + GetUrlQueryString() + "\">" + classVo.classname + "</a>");
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
        public void ShowHot()
        {
            Response.Redirect(http_start + "bbs/book_list_hot.aspx?siteid=" + siteid + "&classid=" + classid + "&type=" + GetRequestValue("type") + "&days=" + GetRequestValue("days"));
        }

        public void ShowSearch()
        {
            Response.Redirect(http_start + "bbs/book_list_search.aspx?action=search&siteid=" + siteid + "&classid=" + classid + "&type=" + GetRequestValue("type") + "&key=" + HttpUtility.UrlEncode(GetRequestValue("key")));
        }

        public void ShowGood()
        {
            QueryBuilder queryBuilder;

            if (classid == "0")
            {
                // ✅ 全站精华帖 - 使用安全的参数化查询
                queryBuilder = new QueryBuilder()
                    .Where("ischeck = @ParamN", 0)
                    .Where("book_good = @ParamN", 1)
                    .Where("userid = @ParamN", DapperHelper.SafeParseLong(siteid, "站点ID"));

                classVo.classid = 0L;
                classVo.position = "left";
                classVo.classname = "所有精华帖子";
                classVo.siteimg = "NetImages/no.gif";
                classVo.introduce = "";
            }
            else
            {
                // ✅ 特定版块精华帖 - 使用安全的参数化查询（简化版，不支持层级查询）
                classVo.classname += "・精华帖子";
                queryBuilder = new QueryBuilder()
                    .Where("ischeck = @ParamN", 0)
                    .Where("book_good = @ParamN", 1)
                    .Where("userid = @ParamN", DapperHelper.SafeParseLong(siteid, "站点ID"))
                    .Where("book_classid = @ParamN", DapperHelper.SafeParseLong(classid, "版块ID"));
            }

            // ✅ 添加用户黑名单过滤
            string connectionString = PubConstant.GetConnectionString(a);
            AddUserBlockingFilter(queryBuilder, userid, connectionString);

            try
            {
                // ✅ 使用统一的帖子列表处理逻辑
                ShowPostListCommon(PostListType.Good, queryBuilder,
                    "正在浏览精华:<a href=\"" + http_start + GetUrlQueryString() + "\">" + classVo.classname + "</a>");
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }

        private new void LoadUserInfo()
        {
            string siteDefault = WapTool.GetSiteDefault(siteVo.Version, 33);
            if (!(siteDefault != "1") || (listVo == null && listVoTop == null))
            {
                return;
            }

            // ✅ 性能优化：收集所有需要查询的用户ID，去重并批量查询
            var userIds = new HashSet<long>();

            if (listVo != null)
            {
                foreach (var item in listVo.Where(x => !string.IsNullOrEmpty(x.book_pub)))
                {
                    if (long.TryParse(item.book_pub, out long userId) && userId > 0)
                    {
                        userIds.Add(userId);
                    }
                }
            }

            if (listVoTop != null)
            {
                foreach (var item in listVoTop.Where(x => !string.IsNullOrEmpty(x.book_pub)))
                {
                    if (long.TryParse(item.book_pub, out long userId) && userId > 0)
                    {
                        userIds.Add(userId);
                    }
                }
            }

            if (userIds.Count > 0)
            {
                // ✅ 使用优化的批量用户查询
                userListVo_IDName = GetUserListByIdsBatch(userIds.ToList());
            }

            // 注意：派币图标现在直接判断 freeLeftMoney > 0，无需预加载缓存
        }



        /// <summary>
        /// 批量获取用户信息（缓存优化版本）
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <returns>用户信息列表</returns>
        private List<user_Model> GetUserListByIdsBatch(List<long> userIds)
        {
            try
            {
                if (userIds == null || userIds.Count == 0)
                    return new List<user_Model>();

                string connectionString = PubConstant.GetConnectionString(a);

                // ✅ 使用缓存服务获取用户基本信息
                var userBasicInfos = UserInfoCacheService.GetUserBasicInfoBatch(userIds, connectionString);

                // ✅ 转换为user_Model格式（保持兼容性）
                var result = new List<user_Model>();
                foreach (var basicInfo in userBasicInfos)
                {
                    var userModel = new user_Model
                    {
                        userid = basicInfo.UserId,
                        nickname = basicInfo.Nickname,
                        endTime = basicInfo.EndTime,
                        SessionTimeout = basicInfo.SessionTimeout,
                        LastLoginTime = basicInfo.LastLoginTime,
                        headimg = basicInfo.HeadImg,
                        idname = basicInfo.IdName, // ✅ 使用智能生成的idname
                        siteid = long.Parse(siteid) // 设置siteid保持兼容性
                    };
                    result.Add(userModel);
                }

                System.Diagnostics.Debug.WriteLine($"GetUserListByIdsBatch: 查询{userIds.Count}个用户，返回{result.Count}个结果");
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"批量获取用户信息失败: {ex.Message}");
                return new List<user_Model>();
            }
        }

        /// <summary>
        /// 安全获取置顶帖子列表（使用独立缓存）
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="siteid">站点ID</param>
        /// <param name="classid">版块ID</param>
        /// <returns>置顶帖子列表</returns>
        private List<wap_bbs_Model> GetTopPostsSafely(string connectionString, string siteid, string classid)
        {
            try
            {
                // ✅ 只使用独立的置顶帖缓存，不依赖全局缓存开关
                string cacheKey = "topPosts_v2_" + siteid + "_" + classid;

                // 尝试从缓存获取
                if (WapTool.DataBBSArray.TryGetValue(cacheKey, out List<wap_bbs_Model> cachedTops))
                {
                    return cachedTops;
                }

                // 缓存未命中，查询数据库
                var topPosts = QueryTopPostsDirectly(connectionString, siteid, classid);

                // 缓存结果（置顶帖变化少，可以长时间缓存）
                try
                {
                    WapTool.DataBBSArray.Add(cacheKey, topPosts);

                    // ✅ 清理旧的缓存键，确保缓存一致性
                    WapTool.DataBBSArray.Remove("bbsTop" + siteid + classid);
                    WapTool.DataBBSArray.Remove("topPosts_v1_" + siteid + "_" + classid);
                }
                catch (Exception)
                {
                    // 忽略缓存失败，不影响功能
                }

                return topPosts;
            }
            catch (Exception)
            {
                return new List<wap_bbs_Model>();
            }
        }

        /// <summary>
        /// 直接查询置顶帖（不使用缓存）
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="siteid">站点ID</param>
        /// <param name="classid">版块ID</param>
        /// <returns>置顶帖子列表</returns>
        private List<wap_bbs_Model> QueryTopPostsDirectly(string connectionString, string siteid, string classid)
        {
            long siteIdLong = DapperHelper.SafeParseLong(siteid, "站点ID");
            long classIdLong = DapperHelper.SafeParseLong(classid, "版块ID");

            // 查询普通置顶帖 (book_top=1 且 book_classid=当前版块)
            var normalTopQuery = new QueryBuilder()
                .Where("userid = @ParamN", siteIdLong)
                .Where("book_classid = @ParamN", classIdLong)
                .Where("ischeck = @ParamN", 0)
                .Where("book_top = @ParamN", 1);

            var (normalTopSql, normalTopParams) = normalTopQuery.Build(
                "SELECT book_classid,id,book_title,book_date,book_click,book_re,book_author,book_pub,book_top,book_good,topic,islock,ischeck,sendMoney,isvote,reDate,isdown,hangbiaoshi,freeMoney,freeleftMoney,book_img,MarkSixBetID,MarkSixWin FROM wap_bbs WITH (NOLOCK)");

            // 查询全区置顶帖 (book_top=2，不限制版块)
            var globalTopQuery = new QueryBuilder()
                .Where("userid = @ParamN", siteIdLong)
                .Where("ischeck = @ParamN", 0)
                .Where("book_top = @ParamN", 2);

            var (globalTopSql, globalTopParams) = globalTopQuery.Build(
                "SELECT book_classid,id,book_title,book_date,book_click,book_re,book_author,book_pub,book_top,book_good,topic,islock,ischeck,sendMoney,isvote,reDate,isdown,hangbiaoshi,freeMoney,freeleftMoney,book_img,MarkSixBetID,MarkSixWin FROM wap_bbs WITH (NOLOCK)");

            // 执行查询并合并结果
            var normalTopPosts = DapperHelper.Query<wap_bbs_Model>(connectionString, normalTopSql, normalTopParams)?.ToList() ?? new List<wap_bbs_Model>();
            var globalTopPosts = DapperHelper.Query<wap_bbs_Model>(connectionString, globalTopSql, globalTopParams)?.ToList() ?? new List<wap_bbs_Model>();

            // 合并结果并按置顶级别和时间排序
            var allTopPosts = new List<wap_bbs_Model>();
            allTopPosts.AddRange(globalTopPosts); // 全区置顶优先
            allTopPosts.AddRange(normalTopPosts); // 普通置顶其次

            // 按置顶级别降序，然后按回复时间降序排序
            return allTopPosts
                .OrderByDescending(p => p.book_top)
                .ThenByDescending(p => p.reDate)
                .ToList();
        }

        /// <summary>
        /// 清除置顶帖缓存（统一缓存管理）
        /// </summary>
        /// <param name="siteid">站点ID</param>
        /// <param name="classid">版块ID，为空时清除所有版块</param>
        public static void ClearTopPostsCache(string siteid, string classid = null)
        {
            try
            {
                if (string.IsNullOrEmpty(classid))
                {
                    // 清除所有版块的置顶帖缓存
                    var keysToRemove = new List<string>();
                    foreach (var key in WapTool.DataBBSArray.Keys)
                    {
                        // ✅ 清理所有版本的置顶帖缓存
                        if (key.StartsWith("topPosts_v1_" + siteid + "_") ||
                            key.StartsWith("topPosts_v2_" + siteid + "_") ||
                            key.StartsWith("bbsTop" + siteid))
                        {
                            keysToRemove.Add(key);
                        }
                    }
                    foreach (var key in keysToRemove)
                    {
                        WapTool.DataBBSArray.Remove(key);
                    }
                }
                else
                {
                    // 清除特定版块的置顶帖缓存（所有版本）
                    WapTool.DataBBSArray.Remove("topPosts_v2_" + siteid + "_" + classid);
                    WapTool.DataBBSArray.Remove("topPosts_v1_" + siteid + "_" + classid);
                    WapTool.DataBBSArray.Remove("bbsTop" + siteid + classid);
                }
            }
            catch (Exception)
            {
                // 忽略清理失败
            }
        }

        /// <summary>
        /// 添加用户黑名单过滤条件
        /// </summary>
        /// <param name="queryBuilder">查询构建器</param>
        /// <param name="userId">当前用户ID</param>
        /// <param name="connectionString">数据库连接字符串</param>
        private void AddUserBlockingFilter(QueryBuilder queryBuilder, string userId, string connectionString)
        {
            if (string.IsNullOrEmpty(userId) || userId == "0")
            {
                return; // 未登录用户不需要黑名单过滤
            }

            try
            {
                // 获取当前用户拉黑的用户ID列表
                var blockedUserIds = UserBlockingService.GetBlockedUserIds(connectionString, userId);
                if (blockedUserIds.Count > 0)
                {
                    // 排除被当前用户拉黑的用户发布的帖子
                    queryBuilder.WhereNotIn("book_pub", blockedUserIds.Cast<object>().ToList());
                }

                // 获取拉黑当前用户的VIP用户ID列表
                var vipBlockerIds = UserBlockingService.GetVipUsersWhoBlockedMe(connectionString, userId);
                if (vipBlockerIds.Count > 0)
                {
                    // 排除拉黑当前用户的VIP用户发布的帖子
                    queryBuilder.WhereNotIn("book_pub", vipBlockerIds.Cast<object>().ToList());
                }
            }
            catch (Exception ex)
            {
                // 黑名单过滤失败时记录日志，但不影响主要功能
                System.Diagnostics.Debug.WriteLine($"黑名单过滤失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 统一的帖子列表处理逻辑
        /// </summary>
        /// <param name="listType">列表类型</param>
        /// <param name="queryBuilder">查询构建器</param>
        /// <param name="visitMessage">访问统计消息</param>
        private void ShowPostListCommon(PostListType listType, QueryBuilder queryBuilder, string visitMessage)
        {
            // 设置页面大小
            SetupPageSize(listType);

            string connectionString = PubConstant.GetConnectionString(a);

            // 获取总数
            total = GetTotalCountWithCache(queryBuilder, connectionString, listType);

            // 设置分页
            SetupPagination(listType);

            // 获取置顶帖（仅在版块首页显示）
            if (listType == PostListType.Class && CurrentPage == 1L && stype == "")
            {
                listVoTop = GetTopPostsSafely(connectionString, siteid, classid);
            }

            // 获取帖子列表
            listVo = GetPostListWithCache(queryBuilder, connectionString, listType);

            // 后处理
            PostProcessResults(visitMessage);
        }

        /// <summary>
        /// 设置页面大小
        /// </summary>
        private void SetupPageSize(PostListType listType)
        {
            if (listType == PostListType.Class)
            {
                if (classVo.ismodel < 1L)
                {
                    pageSize = siteVo.MaxPerPage_Default;
                }
                else
                {
                    pageSize = classVo.ismodel;
                }
            }
            else
            {
                pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
            }
        }

        /// <summary>
        /// 获取总数（带缓存）
        /// </summary>
        private long GetTotalCountWithCache(QueryBuilder queryBuilder, string connectionString, PostListType listType)
        {
            if (GetRequestValue("getTotal") != "")
            {
                return long.Parse(GetRequestValue("getTotal"));
            }

            // 只有版块列表且无主题类型时才使用缓存
            if (listType == PostListType.Class && "1".Equals(WapTool.KL_OpenCache) && stype == "")
            {
                string value = null;
                WapTool.DataTempArray.TryGetValue("bbsTotal" + siteid + classid, out value);

                if (value == null)
                {
                    var (countSql, parameters) = queryBuilder.Build("SELECT COUNT(*) FROM wap_bbs WITH (NOLOCK)");
                    long count = DapperHelper.ExecuteScalar<long>(connectionString, countSql, parameters);
                    WapTool.DataTempArray.Add("bbsTotal" + siteid + classid, count.ToString());
                    return count;
                }
                else
                {
                    return long.Parse(value);
                }
            }
            else
            {
                var (countSql, parameters) = queryBuilder.Build("SELECT COUNT(*) FROM wap_bbs WITH (NOLOCK)");
                return DapperHelper.ExecuteScalar<long>(connectionString, countSql, parameters);
            }
        }

        /// <summary>
        /// 设置分页信息
        /// </summary>
        private void SetupPagination(PostListType listType)
        {
            if (GetRequestValue("page") != "")
            {
                CurrentPage = long.Parse(GetRequestValue("page"));
            }

            if (listType == PostListType.Class)
            {
                CheckFunction("bbs", CurrentPage);
            }

            CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
            index = pageSize * (CurrentPage - 1L);

            // 构建链接URL
            switch (listType)
            {
                case PostListType.Class:
                    linkURL = http_start + "bbs/book_list.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;getTotal=" + total + stypelink;
                    break;
                case PostListType.New:
                    linkURL = http_start + "bbs/book_list.aspx?action=new&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;getTotal=" + total;
                    break;
                case PostListType.Good:
                    linkURL = http_start + "bbs/book_list.aspx?action=good&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;getTotal=" + total;
                    break;
            }

            linkTOP = WapTool.GetPageLinkShowTOP(ver, lang, total, pageSize, CurrentPage, linkURL);
            linkURL = WapTool.GetPageLink(ver, lang, total, pageSize, CurrentPage, linkURL, WapTool.GetArryString(classVo.smallimg, '|', 40));
        }

        /// <summary>
        /// 获取帖子列表（带缓存）
        /// </summary>
        private List<wap_bbs_Model> GetPostListWithCache(QueryBuilder queryBuilder, string connectionString, PostListType listType)
        {
            List<wap_bbs_Model> result = null;

            // 只有版块列表且无主题类型时才使用缓存
            if (listType == PostListType.Class && "1".Equals(WapTool.KL_OpenCache) && CurrentPage < 30L && stype == "")
            {
                WapTool.DataBBSArray.TryGetValue("bbs" + siteid + classid + CurrentPage, out result);
            }

            if (result == null)
            {
                // ✅ 使用支持列表类型的重载方法
                result = GetPostListSafely(queryBuilder, connectionString, listType, pageSize, CurrentPage);

                // 缓存结果
                if (listType == PostListType.Class && CurrentPage < 30L && stype == "")
                {
                    try
                    {
                        WapTool.DataBBSArray.Add("bbs" + siteid + classid + CurrentPage, result);
                    }
                    catch (Exception)
                    {
                        // 忽略缓存失败
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// 后处理结果
        /// </summary>
        private void PostProcessResults(string visitMessage)
        {
            // 获取BBS广告信息
            string connectionString = PubConstant.GetConnectionString(a);
            adVo = AdvertisementService.GetBBSAdvertisementSafely(siteid, connectionString);

            // 访问统计
            VisiteCount(visitMessage);

            // 加载用户信息和派币状态（派币状态预加载已包含在LoadUserInfo中）
            LoadUserInfo();
        }
    }
}