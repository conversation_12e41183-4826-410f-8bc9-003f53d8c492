﻿using System;
using System.Text;
using System.Threading.Tasks;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using System.Runtime.Caching;
using YaoHuo.Plugin.WebSite.Tool;
using Dapper;

namespace YaoHuo.Plugin.WebSite
{
    public class BBSLikeService
    {
        private readonly MyPageWap _page;
        private readonly string _instanceName;
        private static readonly MemoryCache _likeCache = new MemoryCache("BBSLikeCache");
        private static readonly CacheItemPolicy _cachePolicy = new CacheItemPolicy
        {
            AbsoluteExpiration = DateTimeOffset.Now.AddDays(30)
        };

        private static string GetCacheKey(string siteid, string userid, string postid)
            => $"LIKE_{siteid}_{userid}_{postid}";

        public BBSLikeService(MyPageWap page)
        {
            _page = page;
            _instanceName = PubConstant.GetAppString("InstanceName");
        }

        public void HandleLike(string id, string classid, string userid, string siteid, string IP, wap_bbs_Model bookVo, user_Model userVo)
        {
            StringBuilder result = new StringBuilder();

            try
            {
                bool isAllowed = IsLikeAllowed(classid, id, bookVo);
                if (!isAllowed)
                {
                    result.Append("<div class='tip'>此栏目不允许点赞。</div>");
                    _page.Response.Write(result.ToString());
                    return;
                }

                string fcountSubMoneyFlag = WapTool.GetFcountSubMoneyFlag(siteid, userid, IP);
                bool hasLiked = HasUserLiked(fcountSubMoneyFlag, id);
                if (hasLiked)
                {
                    result.Append("<div class='tip'>您已经给这篇帖子点过赞了。</div>");
                    _page.Response.Write(result.ToString());
                    return;
                }

                bool hasEnoughMoney = HasEnoughMoney(userVo);
                if (!hasEnoughMoney)
                {
                    result.Append("<div class='tip'>您的妖晶不足，无法点赞。</div>");
                    _page.Response.Write(result.ToString());
                    return;
                }

                bool success = ExecuteLike(siteid, id, userid, IP, bookVo, fcountSubMoneyFlag);
                if (success)
                {
                    result.Append("<div class='tip'>点赞成功！</div>");
                }
                else
                {
                    result.Append("<div class='tip'>点赞失败，请稍后再试。</div>");
                }

                string cacheKey = GetCacheKey(siteid, userid, id);
                _likeCache.Set(cacheKey, true, _cachePolicy);
            }
            catch (Exception)
            {
                result.Append("<div class='tip'>点赞过程中出现错误。</div>");
            }

            _page.Response.Write(result.ToString());
        }

        private bool IsLikeAllowed(string classid, string id, wap_bbs_Model bookVo)
        {
            return classid == "204" || id == "1363724";
        }

        private bool HasUserLiked(string fcountSubMoneyFlag, string id)
        {
            return fcountSubMoneyFlag.IndexOf("BBSF" + id) >= 0;
        }

        private bool HasEnoughMoney(user_Model userVo)
        {
            return userVo.money >= 10;
        }

        public bool CheckLikeStatus(string id, string classid, string userid, string siteid, string IP, wap_bbs_Model bookVo)
        {
            string cacheKey = GetCacheKey(siteid, userid, id);

            if (_likeCache.Contains(cacheKey))
            {
                return (bool)_likeCache.Get(cacheKey);
            }

            try
            {
                bool isAllowed = IsLikeAllowed(classid, id, bookVo);
                if (!isAllowed)
                {
                    return false;
                }

                string fcountSubMoneyFlag = WapTool.GetFcountSubMoneyFlag(siteid, userid, IP);
                bool hasLiked = HasUserLiked(fcountSubMoneyFlag, id);

                _likeCache.Add(cacheKey, hasLiked, _cachePolicy);

                return hasLiked;
            }
            catch
            {
                return false;
            }
        }
    }
}