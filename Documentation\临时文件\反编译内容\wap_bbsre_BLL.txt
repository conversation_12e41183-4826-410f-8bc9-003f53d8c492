// KenLin_ClassManager, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// KeLin.ClassManager.DAL.wap_bbsre_DAL
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using KeLin.ClassManager;
using KeLin.ClassManager.ExUtility;
using KeLin.ClassManager.Model;

public class wap_bbsre_DAL
{
	private string a = "kelinkWAP_Check";

	private string b = "";

	public wap_bbsre_DAL(string InstanceName)
	{
		a = InstanceName;
		b = PubConstant.GetConnectionString(a);
	}

	public long Add(wap_bbsre_Model model)
	{
		long result = default(long);
		while (true)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("insert into wap_bbsre(");
			stringBuilder.Append("devid,userid,nickname,classid,bookid,content,redate,myGetMoney,book_top,HangBiaoShi,ischeck,isdown,reply)");
			stringBuilder.Append(" values (");
			stringBuilder.Append("@devid,@userid,@nickname,@classid,@bookid,@content,@redate,@myGetMoney,@book_top,@HangBiaoShi,@ischeck,@isdown,@reply)");
			stringBuilder.Append(";select @@IDENTITY");
			SqlParameter[] array = new SqlParameter[13]
			{
				new SqlParameter("@devid", SqlDbType.NVarChar),
				new SqlParameter("@userid", SqlDbType.BigInt),
				new SqlParameter("@nickname", SqlDbType.NVarChar),
				new SqlParameter("@classid", SqlDbType.BigInt),
				new SqlParameter("@bookid", SqlDbType.BigInt),
				new SqlParameter("@content", SqlDbType.NText),
				new SqlParameter("@redate", SqlDbType.DateTime),
				new SqlParameter("@myGetMoney", SqlDbType.Int),
				new SqlParameter("@book_top", SqlDbType.SmallInt),
				new SqlParameter("@HangBiaoShi", SqlDbType.Int),
				new SqlParameter("@ischeck", SqlDbType.Int),
				new SqlParameter("@isdown", SqlDbType.Int),
				new SqlParameter("@reply", SqlDbType.BigInt)
			};
			array[0].Value = model.devid;
			array[1].Value = model.userid;
			array[2].Value = model.nickname;
			array[3].Value = model.classid;
			array[4].Value = model.bookid;
			array[5].Value = model.content;
			array[6].Value = model.redate;
			array[7].Value = model.myGetMoney;
			array[8].Value = model.book_top;
			array[9].Value = model.HangBiaoShi;
			array[10].Value = model.ischeck;
			array[11].Value = model.isdown;
			array[12].Value = model.reply;
			object obj = DbHelperSQL.ExecuteScalar(b, CommandType.Text, stringBuilder.ToString(), array);
			bool flag = obj != null;
			if (true)
			{
			}
			int num = 3;
			while (true)
			{
				switch (num)
				{
				case 3:
					if (!flag)
					{
						num = 2;
						continue;
					}
					result = Convert.ToInt64(obj);
					num = 0;
					continue;
				case 2:
					result = 1L;
					num = 1;
					continue;
				case 0:
				case 1:
					return result;
				}
				break;
			}
		}
	}

	public void Update(wap_bbsre_Model model)
	{
		if (true)
		{
		}
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("update wap_bbsre set ");
		stringBuilder.Append("devid=@devid,");
		stringBuilder.Append("userid=@userid,");
		stringBuilder.Append("nickname=@nickname,");
		stringBuilder.Append("classid=@classid,");
		stringBuilder.Append("bookid=@bookid,");
		stringBuilder.Append("content=@content,");
		stringBuilder.Append("redate=@redate,");
		stringBuilder.Append("myGetMoney=@myGetMoney,");
		stringBuilder.Append("book_top=@book_top,");
		stringBuilder.Append("HangBiaoShi=@HangBiaoShi");
		stringBuilder.Append(" where id=@id  ");
		SqlParameter[] array = new SqlParameter[11]
		{
			new SqlParameter("@id", SqlDbType.BigInt),
			new SqlParameter("@devid", SqlDbType.NVarChar),
			new SqlParameter("@userid", SqlDbType.BigInt),
			new SqlParameter("@nickname", SqlDbType.NVarChar),
			new SqlParameter("@classid", SqlDbType.BigInt),
			new SqlParameter("@bookid", SqlDbType.BigInt),
			new SqlParameter("@content", SqlDbType.NText),
			new SqlParameter("@redate", SqlDbType.DateTime),
			new SqlParameter("@myGetMoney", SqlDbType.Int),
			new SqlParameter("@book_top", SqlDbType.SmallInt),
			new SqlParameter("@HangBiaoShi", SqlDbType.Int)
		};
		array[0].Value = model.id;
		array[1].Value = model.devid;
		array[2].Value = model.userid;
		array[3].Value = model.nickname;
		array[4].Value = model.classid;
		array[5].Value = model.bookid;
		array[6].Value = model.content;
		array[7].Value = model.redate;
		array[8].Value = model.myGetMoney;
		array[9].Value = model.book_top;
		array[10].Value = model.HangBiaoShi;
		DbHelperSQL.ExecuteNonQuery(b, CommandType.Text, stringBuilder.ToString(), array);
	}

	public void Delete(long id)
	{
		if (true)
		{
		}
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("delete from wap_bbsre ");
		stringBuilder.Append(" where id=@id ");
		SqlParameter[] array = new SqlParameter[1]
		{
			new SqlParameter("@id", SqlDbType.BigInt)
		};
		array[0].Value = id;
		DbHelperSQL.ExecuteNonQuery(b, CommandType.Text, stringBuilder.ToString(), array);
	}

	public void Delete(long siteid, long id)
	{
		if (true)
		{
		}
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("delete from wap_bbsre ");
		stringBuilder.Append(" where devid=@siteid and id=@id ");
		SqlParameter[] array = new SqlParameter[2]
		{
			new SqlParameter("@siteid", SqlDbType.BigInt),
			new SqlParameter("@id", SqlDbType.BigInt)
		};
		array[0].Value = siteid;
		array[1].Value = id;
		DbHelperSQL.ExecuteNonQuery(b, CommandType.Text, stringBuilder.ToString(), array);
	}

	public wap_bbsre_Model GetModel(long id)
	{
		wap_bbsre_Model result = default(wap_bbsre_Model);
		while (true)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("select  top 1 id,devid,userid,nickname,classid,bookid,content,redate,myGetMoney,book_top,HangBiaoShi,isdown,reply from wap_bbsre ");
			stringBuilder.Append(" where id=@id ");
			SqlParameter[] array = new SqlParameter[1]
			{
				new SqlParameter("@id", SqlDbType.BigInt)
			};
			array[0].Value = id;
			wap_bbsre_Model wap_bbsre_Model = new wap_bbsre_Model();
			DataSet dataSet = DbHelperSQL.ExecuteDataset(b, CommandType.Text, stringBuilder.ToString(), array);
			bool flag = dataSet.Tables[0].Rows.Count <= 0;
			int num = 21;
			while (true)
			{
				switch (num)
				{
				case 21:
					if (!flag)
					{
						num = 29;
						continue;
					}
					result = null;
					num = 0;
					continue;
				case 9:
					wap_bbsre_Model.HangBiaoShi = long.Parse(dataSet.Tables[0].Rows[0]["HangBiaoShi"].ToString());
					num = 23;
					continue;
				case 30:
					flag = !(dataSet.Tables[0].Rows[0]["book_top"].ToString() != "");
					num = 27;
					continue;
				case 27:
					if (!flag)
					{
						num = 10;
						continue;
					}
					goto case 6;
				case 1:
					if (!flag)
					{
						num = 13;
						continue;
					}
					goto case 33;
				case 2:
					flag = !(dataSet.Tables[0].Rows[0]["reply"].ToString() != "");
					num = 26;
					continue;
				case 26:
					if (!flag)
					{
						num = 24;
						continue;
					}
					goto case 5;
				case 12:
					wap_bbsre_Model.devid = dataSet.Tables[0].Rows[0]["devid"].ToString();
					flag = !(dataSet.Tables[0].Rows[0]["userid"].ToString() != "");
					num = 19;
					continue;
				case 19:
					if (!flag)
					{
						num = 15;
						continue;
					}
					goto IL_01e2;
				case 6:
					flag = !(dataSet.Tables[0].Rows[0]["HangBiaoShi"].ToString() != "");
					num = 11;
					continue;
				case 11:
					if (!flag)
					{
						num = 9;
						continue;
					}
					goto case 23;
				case 5:
					result = wap_bbsre_Model;
					num = 16;
					continue;
				case 17:
					flag = !(dataSet.Tables[0].Rows[0]["myGetMoney"].ToString() != "");
					num = 3;
					continue;
				case 3:
					if (!flag)
					{
						num = 18;
						continue;
					}
					goto case 30;
				case 13:
					wap_bbsre_Model.classid = long.Parse(dataSet.Tables[0].Rows[0]["classid"].ToString());
					num = 33;
					continue;
				case 24:
					wap_bbsre_Model.reply = long.Parse(dataSet.Tables[0].Rows[0]["reply"].ToString());
					num = 5;
					continue;
				case 28:
					wap_bbsre_Model.content = dataSet.Tables[0].Rows[0]["content"].ToString();
					flag = !(dataSet.Tables[0].Rows[0]["redate"].ToString() != "");
					num = 31;
					continue;
				case 31:
					if (!flag)
					{
						num = 25;
						continue;
					}
					goto case 17;
				case 29:
					flag = !(dataSet.Tables[0].Rows[0]["id"].ToString() != "");
					num = 8;
					continue;
				case 8:
					if (!flag)
					{
						num = 32;
						continue;
					}
					goto case 12;
				case 15:
					wap_bbsre_Model.userid = long.Parse(dataSet.Tables[0].Rows[0]["userid"].ToString());
					num = 14;
					continue;
				case 14:
					if (1 == 0)
					{
					}
					goto IL_01e2;
				case 33:
					flag = !(dataSet.Tables[0].Rows[0]["bookid"].ToString() != "");
					num = 4;
					continue;
				case 4:
					if (!flag)
					{
						num = 20;
						continue;
					}
					goto case 28;
				case 20:
					wap_bbsre_Model.bookid = long.Parse(dataSet.Tables[0].Rows[0]["bookid"].ToString());
					num = 28;
					continue;
				case 22:
					wap_bbsre_Model.isdown = long.Parse(dataSet.Tables[0].Rows[0]["isdown"].ToString());
					num = 2;
					continue;
				case 23:
					flag = !(dataSet.Tables[0].Rows[0]["isdown"].ToString() != "");
					num = 7;
					continue;
				case 7:
					if (!flag)
					{
						num = 22;
						continue;
					}
					goto case 2;
				case 25:
					wap_bbsre_Model.redate = DateTime.Parse(dataSet.Tables[0].Rows[0]["redate"].ToString());
					num = 17;
					continue;
				case 18:
					wap_bbsre_Model.myGetMoney = long.Parse(dataSet.Tables[0].Rows[0]["myGetMoney"].ToString());
					num = 30;
					continue;
				case 10:
					wap_bbsre_Model.book_top = long.Parse(dataSet.Tables[0].Rows[0]["book_top"].ToString());
					num = 6;
					continue;
				case 32:
					wap_bbsre_Model.id = long.Parse(dataSet.Tables[0].Rows[0]["id"].ToString());
					num = 12;
					continue;
				case 0:
				case 16:
					{
						return result;
					}
					IL_01e2:
					wap_bbsre_Model.nickname = dataSet.Tables[0].Rows[0]["nickname"].ToString();
					flag = !(dataSet.Tables[0].Rows[0]["classid"].ToString() != "");
					num = 1;
					continue;
				}
				break;
			}
		}
	}

	public int GetListCount(string strWhere)
	{
		int result = default(int);
		while (true)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("select count(id)");
			stringBuilder.Append(" FROM wap_bbsre ");
			bool flag = !(strWhere.Trim() != "");
			if (true)
			{
			}
			int num = 0;
			while (true)
			{
				switch (num)
				{
				case 0:
					if (!flag)
					{
						num = 2;
						continue;
					}
					goto case 1;
				case 1:
					result = int.Parse(DbHelperSQL.ExecuteScalar(b, CommandType.Text, stringBuilder.ToString()).ToString());
					num = 3;
					continue;
				case 2:
					stringBuilder.Append(" where " + strWhere);
					num = 1;
					continue;
				case 3:
					return result;
				}
				break;
			}
		}
	}

	public DataSet GetList(string strWhere)
	{
		DataSet result = default(DataSet);
		while (true)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("select id,devid,userid,nickname,classid,bookid,content,redate,myGetMoney,book_top,HangBiaoShi,reply ");
			stringBuilder.Append(" FROM wap_bbsre ");
			bool flag = !(strWhere.Trim() != "");
			if (true)
			{
			}
			int num = 0;
			while (true)
			{
				switch (num)
				{
				case 0:
					if (!flag)
					{
						num = 3;
						continue;
					}
					goto case 2;
				case 2:
					result = DbHelperSQL.ExecuteDataset(b, CommandType.Text, stringBuilder.ToString());
					num = 1;
					continue;
				case 3:
					stringBuilder.Append(" where " + strWhere);
					num = 2;
					continue;
				case 1:
					return result;
				}
				break;
			}
		}
	}

	public DataSet GetList(int Top, string strWhere, string filedOrder)
	{
		DataSet result = default(DataSet);
		while (true)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Append("select ");
			bool flag = Top <= 0;
			int num = 1;
			while (true)
			{
				switch (num)
				{
				case 1:
					if (!flag)
					{
						num = 8;
						continue;
					}
					goto case 2;
				case 9:
					stringBuilder.Append(" order by " + filedOrder);
					num = 0;
					continue;
				case 6:
					stringBuilder.Append(" where " + strWhere);
					num = 5;
					continue;
				case 8:
					stringBuilder.Append(" top " + Top);
					num = 2;
					continue;
				case 0:
					result = DbHelperSQL.ExecuteDataset(b, CommandType.Text, stringBuilder.ToString());
					num = 7;
					continue;
				case 7:
					if (true)
					{
					}
					return result;
				case 2:
					stringBuilder.Append(" id,devid,userid,nickname,classid,bookid,content,redate,myGetMoney,book_top,HangBiaoShi,reply ");
					stringBuilder.Append(" FROM wap_bbsre ");
					flag = !(strWhere.Trim() != "");
					num = 3;
					continue;
				case 3:
					if (!flag)
					{
						num = 6;
						continue;
					}
					goto case 5;
				case 5:
					flag = !(filedOrder.Trim() != "");
					num = 4;
					continue;
				case 4:
					if (!flag)
					{
						num = 9;
						continue;
					}
					goto case 0;
				}
				break;
			}
		}
	}

	public DataSet GetList(int PageSize, int PageIndex, int OrderType, string strWhere)
	{
		if (true)
		{
		}
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("exec UP_GetRecordByPage @tblName,@fldName,@PageSize,@PageIndex,@IsReCount,@OrderType,@strWhere");
		SqlParameter[] array = new SqlParameter[7]
		{
			new SqlParameter("@tblName", SqlDbType.VarChar),
			new SqlParameter("@fldName", SqlDbType.VarChar),
			new SqlParameter("@PageSize", SqlDbType.Int),
			new SqlParameter("@PageIndex", SqlDbType.Int),
			new SqlParameter("@IsReCount", SqlDbType.Int),
			new SqlParameter("@OrderType", SqlDbType.Bit),
			new SqlParameter("@strWhere", SqlDbType.VarChar)
		};
		array[0].Value = "wap_bbsre";
		array[1].Value = "hangbiaoshi";
		array[2].Value = PageSize;
		array[3].Value = PageIndex;
		array[4].Value = 0;
		array[5].Value = OrderType;
		array[6].Value = strWhere;
		return DbHelperSQL.ExecuteDataset(b, CommandType.Text, stringBuilder.ToString(), array);
	}

	public DataSet GetList(int PageSize, int PageIndex, string strWhere, string ShowFldName, string OrderfldName, int TotalCount, int OrderType)
	{
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.Append("exec UP_GetRecordByPageOrder @tblName,@fldName,@OrderfldName,@StatfldName,@TotalCount,@PageSize,@PageIndex,@IsReCount,@OrderType,@strWhere");
		SqlParameter[] array = new SqlParameter[10]
		{
			new SqlParameter("@tblName", SqlDbType.VarChar),
			new SqlParameter("@fldName", SqlDbType.VarChar),
			new SqlParameter("@OrderfldName", SqlDbType.VarChar),
			new SqlParameter("@StatfldName", SqlDbType.VarChar),
			new SqlParameter("@TotalCount", SqlDbType.Int),
			new SqlParameter("@PageSize", SqlDbType.Int),
			new SqlParameter("@PageIndex", SqlDbType.Int),
			new SqlParameter("@IsReCount", SqlDbType.Int),
			new SqlParameter("@OrderType", SqlDbType.Bit),
			new SqlParameter("@strWhere", SqlDbType.VarChar)
		};
		array[0].Value = "wap_bbsre";
		array[1].Value = ShowFldName;
		array[2].Value = OrderfldName;
		array[3].Value = "";
		array[4].Value = TotalCount;
		array[5].Value = PageSize;
		array[6].Value = PageIndex;
		array[7].Value = 0;
		array[8].Value = OrderType;
		array[9].Value = strWhere;
		DataSet result = DbHelperSQL.ExecuteDataset(b, CommandType.Text, stringBuilder.ToString(), array);
		if (true)
		{
		}
		return result;
	}

	public List<wap_bbsre_Model> GetListVo(long PageSize, long PageIndex, string strWhere, string ShowFldName, string OrderfldName, long TotalCount, int OrderType)
	{
		wap_bbsre_Model wap_bbsre_Model = default(wap_bbsre_Model);
		DataRow dataRow = default(DataRow);
		bool flag = default(bool);
		IEnumerator enumerator = default(IEnumerator);
		List<wap_bbsre_Model> result = default(List<wap_bbsre_Model>);
		while (true)
		{
			if (true)
			{
			}
			StringBuilder stringBuilder = new StringBuilder();
			List<wap_bbsre_Model> list = new List<wap_bbsre_Model>();
			stringBuilder.Append("exec UP_GetRecordByPageOrder @tblName,@fldName,@OrderfldName,@StatfldName,@TotalCount,@PageSize,@PageIndex,@IsReCount,@OrderType,@strWhere");
			SqlParameter[] array = new SqlParameter[10]
			{
				new SqlParameter("@tblName", SqlDbType.VarChar),
				new SqlParameter("@fldName", SqlDbType.VarChar),
				new SqlParameter("@OrderfldName", SqlDbType.VarChar),
				new SqlParameter("@StatfldName", SqlDbType.VarChar),
				new SqlParameter("@TotalCount", SqlDbType.Int),
				new SqlParameter("@PageSize", SqlDbType.Int),
				new SqlParameter("@PageIndex", SqlDbType.Int),
				new SqlParameter("@IsReCount", SqlDbType.Int),
				new SqlParameter("@OrderType", SqlDbType.Bit),
				new SqlParameter("@strWhere", SqlDbType.VarChar)
			};
			array[0].Value = "wap_bbsre_VIEW";
			array[1].Value = ShowFldName;
			array[2].Value = OrderfldName;
			array[3].Value = "";
			array[4].Value = TotalCount;
			array[5].Value = PageSize;
			array[6].Value = PageIndex;
			array[7].Value = 0;
			array[8].Value = OrderType;
			array[9].Value = strWhere;
			DataSet dataSet = DbHelperSQL.ExecuteDataset(b, CommandType.Text, stringBuilder.ToString(), array);
			int num = 3;
			while (true)
			{
				int num2;
				switch (num)
				{
				case 3:
					if (dataSet != null)
					{
						num = 10;
						continue;
					}
					goto IL_0899;
				case 6:
					try
					{
						num = 23;
						while (true)
						{
							switch (num)
							{
							case 25:
								wap_bbsre_Model.expr = long.Parse(dataRow["expr"].ToString());
								num = 4;
								continue;
							default:
								flag = enumerator.MoveNext();
								num = 15;
								continue;
							case 15:
								if (!flag)
								{
									num = 22;
									continue;
								}
								dataRow = (DataRow)enumerator.Current;
								wap_bbsre_Model = new wap_bbsre_Model();
								wap_bbsre_Model.id = long.Parse(dataRow["id"].ToString());
								wap_bbsre_Model.devid = dataRow["devid"].ToString();
								wap_bbsre_Model.userid = long.Parse(dataRow["userid"].ToString());
								wap_bbsre_Model.nickname = dataRow["nickname"].ToString();
								wap_bbsre_Model.classid = long.Parse(dataRow["classid"].ToString());
								wap_bbsre_Model.bookid = long.Parse(dataRow["bookid"].ToString());
								wap_bbsre_Model.content = dataRow["content"].ToString();
								wap_bbsre_Model.redate = DateTime.Parse(dataRow["redate"].ToString());
								flag = !(dataRow["book_top"].ToString() != "");
								num = 7;
								continue;
							case 17:
								flag = !(dataRow["ischeck"].ToString() != "");
								num = 21;
								continue;
							case 21:
								if (!flag)
								{
									num = 6;
									continue;
								}
								goto case 1;
							case 7:
								if (!flag)
								{
									num = 12;
									continue;
								}
								goto case 11;
							case 14:
								wap_bbsre_Model.myGetMoney = long.Parse(dataRow["myGetMoney"].ToString());
								num = 3;
								continue;
							case 11:
								flag = !(dataRow["myGetMoney"].ToString() != "");
								num = 5;
								continue;
							case 5:
								if (!flag)
								{
									num = 14;
									continue;
								}
								goto case 3;
							case 9:
								wap_bbsre_Model.isdown = long.Parse(dataRow["isdown"].ToString());
								num = 17;
								continue;
							case 4:
								wap_bbsre_Model.headimg = dataRow["headimg"].ToString();
								wap_bbsre_Model.idname = dataRow["idname"].ToString();
								wap_bbsre_Model.isonline = dataRow["isonline"].ToString();
								list.Add(wap_bbsre_Model);
								num = 16;
								continue;
							case 20:
								wap_bbsre_Model.money = long.Parse(dataRow["money"].ToString());
								num = 8;
								continue;
							case 6:
								wap_bbsre_Model.ischeck = long.Parse(dataRow["ischeck"].ToString());
								num = 1;
								continue;
							case 1:
								flag = !(dataRow["reply"].ToString() != "");
								num = 2;
								continue;
							case 2:
								if (!flag)
								{
									num = 19;
									continue;
								}
								goto case 0;
							case 0:
								wap_bbsre_Model.sex = dataRow["sex"].ToString();
								wap_bbsre_Model.city = dataRow["city"].ToString();
								flag = !(dataRow["money"].ToString() != "");
								num = 13;
								continue;
							case 13:
								if (!flag)
								{
									num = 20;
									continue;
								}
								goto case 8;
							case 3:
								flag = !(dataRow["isdown"].ToString() != "");
								num = 10;
								continue;
							case 10:
								if (!flag)
								{
									num = 9;
									continue;
								}
								goto case 17;
							case 12:
								wap_bbsre_Model.book_top = long.Parse(dataRow["book_top"].ToString());
								num = 11;
								continue;
							case 8:
								flag = !(dataRow["expr"].ToString() != "");
								num = 24;
								continue;
							case 24:
								if (!flag)
								{
									num = 25;
									continue;
								}
								goto case 4;
							case 19:
								wap_bbsre_Model.reply = long.Parse(dataRow["reply"].ToString());
								num = 0;
								continue;
							case 22:
								num = 18;
								continue;
							case 18:
								break;
							}
							break;
						}
					}
					finally
					{
						while (true)
						{
							IL_0785:
							IDisposable disposable = enumerator as IDisposable;
							flag = disposable == null;
							num = 0;
							while (true)
							{
								switch (num)
								{
								case 0:
									if (!flag)
									{
										num = 2;
										continue;
									}
									goto end_IL_0770;
								case 2:
									disposable.Dispose();
									num = 1;
									continue;
								case 1:
									goto end_IL_0770;
								}
								goto IL_0785;
								continue;
								end_IL_0770:
								break;
							}
							break;
						}
					}
					result = list;
					num = 2;
					continue;
				case 7:
					result = null;
					num = 4;
					continue;
				case 8:
					num = 5;
					continue;
				case 5:
					num2 = ((dataSet.Tables[0].Rows.Count > 0) ? 1 : 0);
					goto IL_0872;
				case 10:
					num = 9;
					continue;
				case 9:
					if (dataSet.Tables.Count > 0)
					{
						num = 8;
						continue;
					}
					goto IL_0899;
				case 0:
					if (flag)
					{
						enumerator = dataSet.Tables[0].Rows.GetEnumerator();
						num = 6;
					}
					else
					{
						num = 7;
					}
					continue;
				case 1:
					num2 = 0;
					goto IL_0872;
				case 2:
				case 4:
					{
						return result;
					}
					IL_0899:
					num = 1;
					continue;
					IL_0872:
					flag = (byte)num2 != 0;
					num = 0;
					continue;
				}
				break;
			}
		}
	}

	public List<wap_bbsre_Model> GetListTopVo(string strWhere, int order)
	{
		DataSet dataSet = default(DataSet);
		DataRow dataRow = default(DataRow);
		wap_bbsre_Model wap_bbsre_Model = default(wap_bbsre_Model);
		IEnumerator enumerator = default(IEnumerator);
		List<wap_bbsre_Model> result = default(List<wap_bbsre_Model>);
		while (true)
		{
			StringBuilder stringBuilder = new StringBuilder();
			List<wap_bbsre_Model> list = new List<wap_bbsre_Model>();
			stringBuilder.Append("select * from wap_bbsre_view where ");
			stringBuilder.Append(strWhere);
			bool flag = order != 0;
			int num = 4;
			while (true)
			{
				int num2;
				switch (num)
				{
				case 4:
					if (!flag)
					{
						num = 8;
						continue;
					}
					stringBuilder.Append(" order by id asc");
					num = 12;
					continue;
				case 8:
					stringBuilder.Append(" order by id desc");
					num = 5;
					continue;
				case 7:
					num = 6;
					continue;
				case 6:
					if (dataSet.Tables.Count > 0)
					{
						num = 2;
						continue;
					}
					goto IL_0766;
				case 2:
					if (true)
					{
					}
					num = 3;
					continue;
				case 5:
				case 12:
					dataSet = DbHelperSQL.ExecuteDataset(b, CommandType.Text, stringBuilder.ToString());
					num = 1;
					continue;
				case 1:
					if (dataSet != null)
					{
						num = 7;
						continue;
					}
					goto IL_0766;
				case 3:
					num2 = ((dataSet.Tables[0].Rows.Count > 0) ? 1 : 0);
					goto IL_0742;
				case 10:
					try
					{
						num = 10;
						while (true)
						{
							switch (num)
							{
							case 17:
								flag = !(dataRow["isdown"].ToString() != "");
								num = 25;
								continue;
							case 25:
								if (!flag)
								{
									num = 4;
									continue;
								}
								goto case 18;
							case 7:
								wap_bbsre_Model.myGetMoney = long.Parse(dataRow["myGetMoney"].ToString());
								num = 17;
								continue;
							case 4:
								wap_bbsre_Model.isdown = long.Parse(dataRow["isdown"].ToString());
								num = 18;
								continue;
							case 23:
								wap_bbsre_Model.reply = long.Parse(dataRow["reply"].ToString());
								num = 0;
								continue;
							case 14:
								if (!flag)
								{
									num = 8;
									continue;
								}
								goto case 13;
							case 0:
								wap_bbsre_Model.sex = dataRow["sex"].ToString();
								wap_bbsre_Model.city = dataRow["city"].ToString();
								flag = !(dataRow["money"].ToString() != "");
								num = 20;
								continue;
							case 20:
								if (!flag)
								{
									num = 19;
									continue;
								}
								goto case 6;
							case 9:
								wap_bbsre_Model.ischeck = long.Parse(dataRow["ischeck"].ToString());
								num = 5;
								continue;
							case 15:
								wap_bbsre_Model.expr = long.Parse(dataRow["expr"].ToString());
								num = 16;
								continue;
							case 18:
								flag = !(dataRow["ischeck"].ToString() != "");
								num = 11;
								continue;
							case 11:
								if (!flag)
								{
									num = 9;
									continue;
								}
								goto case 5;
							case 16:
								wap_bbsre_Model.headimg = dataRow["headimg"].ToString();
								wap_bbsre_Model.idname = dataRow["idname"].ToString();
								wap_bbsre_Model.isonline = dataRow["isonline"].ToString();
								list.Add(wap_bbsre_Model);
								num = 12;
								continue;
							case 13:
								flag = !(dataRow["myGetMoney"].ToString() != "");
								num = 21;
								continue;
							case 21:
								if (!flag)
								{
									num = 7;
									continue;
								}
								goto case 17;
							case 8:
								wap_bbsre_Model.book_top = long.Parse(dataRow["book_top"].ToString());
								num = 13;
								continue;
							default:
								flag = enumerator.MoveNext();
								num = 1;
								continue;
							case 1:
								if (flag)
								{
									dataRow = (DataRow)enumerator.Current;
									wap_bbsre_Model = new wap_bbsre_Model();
									wap_bbsre_Model.id = long.Parse(dataRow["id"].ToString());
									wap_bbsre_Model.devid = dataRow["devid"].ToString();
									wap_bbsre_Model.userid = long.Parse(dataRow["userid"].ToString());
									wap_bbsre_Model.nickname = dataRow["nickname"].ToString();
									wap_bbsre_Model.classid = long.Parse(dataRow["classid"].ToString());
									wap_bbsre_Model.bookid = long.Parse(dataRow["bookid"].ToString());
									wap_bbsre_Model.content = dataRow["content"].ToString();
									wap_bbsre_Model.redate = DateTime.Parse(dataRow["redate"].ToString());
									flag = !(dataRow["book_top"].ToString() != "");
									num = 14;
								}
								else
								{
									num = 2;
								}
								continue;
							case 5:
								flag = !(dataRow["reply"].ToString() != "");
								num = 3;
								continue;
							case 3:
								if (!flag)
								{
									num = 23;
									continue;
								}
								goto case 0;
							case 19:
								wap_bbsre_Model.money = long.Parse(dataRow["money"].ToString());
								num = 6;
								continue;
							case 6:
								flag = !(dataRow["expr"].ToString() != "");
								num = 22;
								continue;
							case 22:
								if (!flag)
								{
									num = 15;
									continue;
								}
								goto case 16;
							case 2:
								num = 24;
								continue;
							case 24:
								break;
							}
							break;
						}
					}
					finally
					{
						while (true)
						{
							IL_0700:
							IDisposable disposable = enumerator as IDisposable;
							flag = disposable == null;
							num = 0;
							while (true)
							{
								switch (num)
								{
								case 0:
									if (!flag)
									{
										num = 1;
										continue;
									}
									goto end_IL_06eb;
								case 1:
									disposable.Dispose();
									num = 2;
									continue;
								case 2:
									goto end_IL_06eb;
								}
								goto IL_0700;
								continue;
								end_IL_06eb:
								break;
							}
							break;
						}
					}
					result = list;
					num = 11;
					continue;
				case 0:
					if (!flag)
					{
						num = 9;
						continue;
					}
					enumerator = dataSet.Tables[0].Rows.GetEnumerator();
					num = 10;
					continue;
				case 13:
					num2 = 0;
					goto IL_0742;
				case 9:
					result = null;
					num = 14;
					continue;
				case 11:
				case 14:
					{
						return result;
					}
					IL_0766:
					num = 13;
					continue;
					IL_0742:
					flag = (byte)num2 != 0;
					num = 0;
					continue;
				}
				break;
			}
		}
	}
}