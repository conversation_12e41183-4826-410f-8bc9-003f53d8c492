(function () {
    // ========== 配置常量 ==========
    const CONFIG = {
        highlightDuration: 3000,     // 高亮持续时间(ms)
        scrollDelay: 100,           // 滚动延迟(ms)
        headerOffset: 60,           // 头部偏移量(px)
        cookieExpiry: 365           // Cookie保存天数
    };

    // ========== 通用工具函数 ==========
    const DOM = {
        select: (selector) => document.querySelector(selector),
        selectAll: (selector) => document.querySelectorAll(selector),
        on: (element, event, handler) => element && element.addEventListener(event, handler)
    };

    // 提取数据的函数
    function extractData(replyElement) {
        // 提取楼层号
        const floorNumberElement = replyElement.querySelector('[class^="floornumber"]');
        const floorNumber = floorNumberElement ? floorNumberElement.textContent : '';

        const userName = replyElement.querySelector('.renick a').textContent;
        const userId = replyElement.querySelector('.renickid').textContent;
        const postDate = replyElement.querySelector('.retime').textContent;
        const replyTextElement = replyElement.querySelector('.retext');
        const replyText = replyTextElement ? replyTextElement.innerHTML : '';

        // 判断是否存在特定按钮，并提取链接
        const deleteButton = replyElement.querySelector('.delete-myfloor');
        const giveButton = replyElement.querySelector('.giveicon');
        const replyLink = replyElement.querySelector('.replyicon');

        // 提取管理链接
        const adminActions = replyElement.querySelector('.admin-remanage');
        const floorgiveLink = adminActions ? adminActions.querySelector('.floorgive.drop-down') : null;
        const floordeladminLink = adminActions ? adminActions.querySelector('.floordeladmin') : null;
        const floordeleteLink = floordeladminLink ? floordeladminLink.getAttribute('href') : null;
        const floorchangeLink = adminActions ? adminActions.querySelector('.floorchange.drop-down') : null;
        const floortopLink = adminActions ? adminActions.querySelector('.floortop.drop-down') : null;
        const floortopText = floortopLink ? floortopLink.textContent : null;
        const isFloortop = floortopText === '顶';

        // 提取回复楼层
        const replyOtherElement = replyElement.querySelector('.reother');
        let replyOtherText = null;
        let replyOtherHref = null;
        let replyOtherFloorText = null;
        if (replyOtherElement) {
            // 优先提取 <a> 标签
            const replyOtherLink = replyOtherElement.querySelector('a');
            if (replyOtherLink) {
                replyOtherHref = replyOtherLink.getAttribute('href');
                replyOtherFloorText = replyOtherLink.textContent;
                // 提取"回复"前缀
                replyOtherText = replyOtherElement.childNodes[0] ? replyOtherElement.childNodes[0].textContent : '回复';
            } else {
                replyOtherText = replyOtherElement.textContent;
            }
        }

        // 提取得金信息
        const rewardInfoElement = replyElement.querySelector('.remoney');
        const rewardNumberElement = rewardInfoElement ? rewardInfoElement.querySelector('.rewardnumber') : null;
        const rewardNumber = rewardNumberElement ? rewardNumberElement.textContent : null;

        // 提取 reidlink
        const reidlinkElement = replyElement.querySelector('.reidlink');
        const reidlink = reidlinkElement ? reidlinkElement.getAttribute('href') : null;

        // 提取图标信息
        const iconElement = replyElement.querySelector('.renick img');
        const iconSrc = iconElement ? iconElement.src : null;
        const iconAlt = iconElement ? iconElement.alt : null;

        // 提取昵称颜色
        const nickColorElement = replyElement.querySelector('.renick font');
        const nickColor = nickColorElement ? nickColorElement.getAttribute('color') : null;

        // 新增：提取真实楼层号
        const realFloorNumber = replyElement.getAttribute('data-floor');

        return {
            floorNumber,
            realFloorNumber,
            userName,
            userId,
            postDate,
            replyText,
            deleteButton: deleteButton ? deleteButton.href : null,
            giveButton: giveButton ? giveButton.href : null,
            replyLink: replyLink ? replyLink.href : null,
            floorgiveLink: floorgiveLink ? floorgiveLink.href : null,
            floordeleteLink: floordeleteLink ? floordeleteLink : null,
            floorchangeLink: floorchangeLink ? floorchangeLink.href : null,
            floortopLink: floortopLink ? floortopLink.href : null,
            replyOtherText: replyOtherText,
            replyOtherHref: replyOtherHref,
            replyOtherFloorText: replyOtherFloorText,
            rewardNumber: rewardNumber,
            reidlink,
            iconSrc: iconSrc,
            iconAlt: iconAlt,
            nickColor: nickColor,
            floortopLink: floortopLink ? floortopLink.href : null,
            floortopText: isFloortop ? '顶' : '消顶',
        };
    }

    function buildNewLayout(data) {
        // 如果之前没有forum-container，则创建
        let forumContainer = document.querySelector('.forum-container');
        if (!forumContainer) {
            forumContainer = document.createElement('div');
            forumContainer.className = 'forum-container';
        }
        const postElement = document.createElement('div');
        // 检查forum-container的子元素数量，如果是偶数，添加'bgColor'类
        if (forumContainer.children.length % 2 === 1) {
            postElement.className = 'forum-post';
        } else {
            postElement.className = 'forum-post bgColor';
        }
        // 修复：用真实楼层号设置data-floor，保证跳转高亮
        if (data.realFloorNumber) {
            postElement.setAttribute('data-floor', data.realFloorNumber);
        }

        const postHeader = document.createElement('div');
        postHeader.className = 'post-header';

        const userNameElement = document.createElement('span');
        userNameElement.className = 'user-name';

        const userNickElement = document.createElement('span');
        userNickElement.className = 'user-nick';

        const userLink = document.createElement('a');
        userLink.href = `/bbs/userinfo.aspx?touserid=${data.userId}`;

        // 提取图标信息
        if (data.iconSrc) {
            const iconImg = document.createElement('img');
            iconImg.src = data.iconSrc;
            iconImg.alt = data.iconAlt;
            userLink.appendChild(iconImg);
        }

        // 提取昵称颜色
        if (data.nickColor) {
            const coloredNickSpan = document.createElement('span');
            coloredNickSpan.style.color = data.nickColor;
            coloredNickSpan.textContent = data.userName;
            userLink.appendChild(coloredNickSpan);
        } else {
            userLink.textContent = data.userName; // 如果没有颜色信息，则直接使用用户名
        }

        userNickElement.appendChild(userLink);

        const userIdElement = document.createElement('span');
        userIdElement.className = 'user-id';

        // 创建包含 userId 的 a 标签
        const userIdLink = document.createElement('a');
        userIdLink.href = data.reidlink; // 使用提取的 reidlink
        userIdLink.textContent = `(${data.userId})`;

        userIdElement.appendChild(userIdLink);

        userNameElement.appendChild(userNickElement);
        userNameElement.appendChild(userIdElement);

        const postDateElement = document.createElement('span');
        postDateElement.className = 'post-date';
        postDateElement.textContent = data.postDate;

        const floorInfoElement = document.createElement('div');
        floorInfoElement.className = 'floor-info';

        // 添加楼层号
        if (data.floorNumber) {
            const floorNumberElement = document.createElement('span');
            floorNumberElement.className = 'floor-number';
            floorNumberElement.textContent = data.floorNumber;
            floorInfoElement.appendChild(floorNumberElement);

            // 如果有管理链接，显示在 floor-number 前面
            if (data.floorgiveLink || data.floordeleteLink || data.floorchangeLink || data.floortopLink) {
                floorInfoElement.insertBefore(floorNumberElement, floorInfoElement.firstChild);
            }
        }

        // 添加文字楼层
        const textFloorElements = ['沙发', '椅子', '板凳', '顶楼'];
        if (textFloorElements.includes(data.floorNumber)) {
            floorInfoElement.textContent = data.floorNumber;
        } else {
            const floorTextElement = document.createElement('span');
            floorTextElement.textContent = '楼';
            floorInfoElement.appendChild(floorTextElement);
        }

        // 添加管理元素和 dropdown
        const dropdownElement = document.createElement('span');
        dropdownElement.className = 'dropdown';
        dropdownElement.innerHTML = '<svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" class="iconfont" viewBox="0 0 831 1000" style="height:15px;transform:translate(0,15%)" height="15"><path d="M415.3 649.6c81.9 0 149.8-67.9 149.8-149.7s-67.9-149.7-149.8-149.7S265.5 418.1 265.5 499.9s67.9 149.7 149.8 149.7Zm317.7-107.8 89.9 69.9c8 6 10 18 4 27.9l-85.9 147.7c-6 10-16 12-26 8l-105.9-41.9c-22 16-46 31.9-71.9 41.9L521.2 907.1c-2 10-10 18-20 18H329.4c-10 0-18-8-20-18l-16-111.8a260.9 260.9 0 0 1-71.9-41.9l-105.9 41.9c-10 4-20 2-26-8L3.7 639.7c-6-10-4-22 4-27.9l89.9-69.9c-2-14-2-27.9-2-41.9s0-27.9 2-41.9L7.7 388.2c-8-6-10-18-4-27.9l85.9-147.7c6-10 16-12 26-8l105.9 41.9c22-16 46-31.9 71.9-41.9l16-111.8c2-10 10-18 20-18h171.8c10 0 18 8 20 18l16 111.8a260.9 260.9 0 0 1 71.9 41.9l105.9-41.9c10-4 20-2 26 8l85.9 147.7c6 10 4 22-4 27.9l-89.9 69.9c2 14 2 27.9 2 41.9s-.004 27.9-2 41.9Z" fill-rule="evenodd" fill="#afc6cf"></path></svg>';

        const dropdownContentElement = document.createElement('span');
        dropdownContentElement.className = 'dropdown-content';

        if (data.floorgiveLink || data.floordeleteLink || data.floorchangeLink || data.floortopLink) {
            const floorgiveLink = document.createElement('a');
            floorgiveLink.className = 'floor-give';
            floorgiveLink.href = data.floorgiveLink;
            floorgiveLink.textContent = '送币';

            const floordeleteLink = document.createElement('a');
            floordeleteLink.className = 'floor-delete';
            floordeleteLink.href = data.floordeleteLink;
            floordeleteLink.textContent = '删除';

            const floorchangeLink = document.createElement('a');
            floorchangeLink.className = 'floor-change';
            floorchangeLink.href = data.floorchangeLink;
            floorchangeLink.textContent = '审核';

            const floortopLink = document.createElement('a');
            floortopLink.className = 'floor-top';
            floortopLink.href = data.floortopLink;
            floortopLink.textContent = data.floortopText === '顶' ? '置顶' : '消顶';

            dropdownContentElement.appendChild(floorgiveLink);
            dropdownContentElement.appendChild(floordeleteLink);
            dropdownContentElement.appendChild(floorchangeLink);
            dropdownContentElement.appendChild(floortopLink);

            dropdownElement.appendChild(dropdownContentElement);

            if (data.floorNumber) {
                floorInfoElement.insertBefore(dropdownElement, floorInfoElement.firstChild);
            } else {
                floorInfoElement.appendChild(dropdownElement);
            }
        }

        postHeader.appendChild(userNameElement);
        postHeader.appendChild(postDateElement);
        postHeader.appendChild(floorInfoElement);

        const postContentElement = document.createElement('div');
        postContentElement.className = 'post-content';

        // 添加回复楼层
        if (data.replyOtherText || data.replyOtherHref) {
            const replyOtherElement = document.createElement('span');
            replyOtherElement.className = 'replay-other';

            if (data.replyOtherHref && data.replyOtherFloorText) {
                // 优先用后端生成的超链接
                replyOtherElement.textContent = data.replyOtherText || '回复';
                const a = document.createElement('a');
                a.href = data.replyOtherHref;
                a.textContent = data.replyOtherFloorText;
                replyOtherElement.appendChild(a);
            } else if (data.replyOtherText) {
                // 兼容无链接的情况
                var match = data.replyOtherText.match(/回复(\d+)楼/);
                if (match) {
                    var floorNum = match[1];
                    var classid = getUrlParam('classid');
                    var id = getUrlParam('id');
                    var page = getUrlParam('page');
                    var href = `/bbs/book_re.aspx?classid=${classid}&id=${id}&tofloor=${floorNum}&page=${page}#floor-${floorNum}`;
                    var a = document.createElement('a');
                    a.href = href;
                    a.textContent = `${floorNum}楼`;
                    replyOtherElement.textContent = '回复';
                    replyOtherElement.appendChild(a);
                } else {
                    replyOtherElement.textContent = data.replyOtherText;
                }
            }
            postContentElement.appendChild(replyOtherElement);
        }

        // 添加冒号
        if (data.replyOtherText) {
            const reColonElement = document.createElement('span');
            reColonElement.className = 'recolon';
            reColonElement.textContent = ':';
            postContentElement.appendChild(reColonElement);
        }

        const replyTextElement = document.createElement('span');
        replyTextElement.className = 'retext';
        replyTextElement.innerHTML = data.replyText;

        postContentElement.appendChild(replyTextElement);

        const adminActionsElement = document.createElement('div');
        adminActionsElement.className = 'admin-actions';

        // 添加得金信息
        if (data.rewardNumber) {
            const rewardInfoElement = document.createElement('span');
            rewardInfoElement.className = 'reward-info';

            const rewardNumberElement = document.createElement('span');
            rewardNumberElement.className = 'reward-number';
            rewardNumberElement.textContent = `+${data.rewardNumber}`;

            rewardInfoElement.appendChild(rewardNumberElement);
            adminActionsElement.appendChild(rewardInfoElement);
        }

        const operateElement = document.createElement('span');
        operateElement.className = 'operate';

        // 根据是否存在按钮来决定是否添加按钮
        if (data.deleteButton) {
            const deleteButton = document.createElement('a');
            deleteButton.className = 'delete-myfloor';
            deleteButton.href = data.deleteButton;

            const deleteIcon = document.createElement('img');
            deleteIcon.src = '/NetCSS/SVG/删除.svg';
            deleteIcon.alt = '删除';
            deleteIcon.width = 18;
            deleteIcon.height = 18;

            deleteButton.appendChild(deleteIcon);
            operateElement.appendChild(deleteButton);
        }

        if (data.replyLink) {
            const replyLink = document.createElement('a');
            replyLink.className = 'replyicon';
            replyLink.href = data.replyLink;

            const replyIcon = document.createElement('img');
            replyIcon.src = '/NetCSS/SVG/回复.svg';
            replyIcon.alt = '回复';
            replyIcon.width = 18;
            replyIcon.height = 18;

            replyLink.appendChild(replyIcon);
            operateElement.appendChild(replyLink);
        }

        if (data.giveButton) {
            const giveButton = document.createElement('a');
            giveButton.className = 'giveicon';
            giveButton.href = data.giveButton;

            const giveIcon = document.createElement('img');
            giveIcon.src = '/NetCSS/SVG/赏分.svg';
            giveIcon.alt = '赏分';
            giveIcon.width = 18;
            giveIcon.height = 20;

            giveButton.appendChild(giveIcon);
            operateElement.appendChild(giveButton);
        }

        // 添加元素到 forum-post
        adminActionsElement.appendChild(operateElement);
        postElement.appendChild(postHeader);
        postElement.appendChild(postContentElement);
        postElement.appendChild(adminActionsElement);

        // 将 forum-post 添加到 forum-container 中
        forumContainer.appendChild(postElement);

        return forumContainer;
    }

    // 替换内容的函数
    function replaceContent(oldElement, newElement) {
        oldElement.parentNode.replaceChild(newElement, oldElement);
    }

    // 新的代码：添加一个状态变量，用于跟踪当前布局状态
    // let isCustomLayoutEnabled = false; // 将由 window.isCustomLayoutEnabled 替代

    function observePageChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                // 检测子元素的增加
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // 遍历每个新添加的节点
                    mutation.addedNodes.forEach((node) => {
                        // 如果节点是一个元素并且有 'list-reply' 类
                        if (node.nodeType === Node.ELEMENT_NODE && node.classList.contains('list-reply')) {
                            // 仅当新版布局开启时才应用新布局
                            if (window.isCustomLayoutEnabled) {
                                const data = extractData(node);
                                const newElement = buildNewLayout(data);
                                replaceContent(node, newElement);
                            }

                            // 重新运行文本转换脚本
                            const textContentElements = node.querySelectorAll(".retext");
                            textContentElements.forEach((element) => {
                                if (typeof processTextContent === 'function') {
                                    processTextContent(element);
                                }
                            });

                            if (typeof convertJdLinksInATags === 'function') {
                                convertJdLinksInATags();
                            }
                            if (typeof convertTbLinksInATags === 'function') {
                                convertTbLinksInATags();
                            }
                        }
                    });
                }
            });
        });

        // 定义要监控的元素和配置
        const targetNode = document.querySelector('#KL_show_next_list'); // 监控这个容器
        if (targetNode) {
            const config = { childList: true, subtree: true };
            // 开始监控
            observer.observe(targetNode, config);
        }
    }

    // 存储切换前的 padding 值
    let originalPadding;

    function toggleLayout() {
        // 切换状态
        window.isCustomLayoutEnabled = !window.isCustomLayoutEnabled;

        // 获取所有旧布局元素
        const oldLayoutElements = document.querySelectorAll('.list-reply');

        // 遍历每个旧布局元素，提取数据并构建新布局，然后替换内容
        oldLayoutElements.forEach((oldElement) => {
            const data = extractData(oldElement);
            const newElement = buildNewLayout(data);
            replaceContent(oldElement, newElement);
        });

        // 获取 recontent 元素
        const recontentElement = document.querySelector('.recontent');

        // 在切换到新版布局时
        if (window.isCustomLayoutEnabled) {
            // 存储当前 padding 值
            originalPadding = recontentElement.style.padding;

            // 移除 padding
            recontentElement.style.padding = '0';
        } else {
            // 在切回旧版布局时，恢复原始 padding 值
            recontentElement.style.padding = originalPadding;
        }

        // 更新按钮文本
        updateChangeLayoutButtonText();

        callHighlightAndScroll();
    }

    // 在页面加载时检查优先使用服务器端变量，再考虑cookie设置
    document.addEventListener('DOMContentLoaded', () => {
        // 初始化 window.isCustomLayoutEnabled
        if (typeof serverSideNewReplyUIEnabled !== 'undefined') {
            window.isCustomLayoutEnabled = serverSideNewReplyUIEnabled;
        } else {
            window.isCustomLayoutEnabled = true; // 默认开启新版
        }

        updateChangeLayoutButtonText();

        // 如果状态为 true，应用新布局
        if (window.isCustomLayoutEnabled) {
            const oldLayoutElements = document.querySelectorAll('.list-reply');
            oldLayoutElements.forEach((oldElement) => {
                const data = extractData(oldElement);
                const newElement = buildNewLayout(data);
                replaceContent(oldElement, newElement);
            });

            // 重新运行文本转换脚本
            const textContentElements = document.querySelectorAll(".retext");
            textContentElements.forEach((element) => {
                if (typeof processTextContent === 'function') {
                    processTextContent(element);
                }
            });

            if (typeof convertJdLinksInATags === 'function') {
                convertJdLinksInATags();
            }

            // 获取 recontent 元素
            const recontentElement = document.querySelector('.recontent');
            if (recontentElement) {
                // 存储当前 padding 值
                originalPadding = recontentElement.style.padding;

                // 移除 padding
                recontentElement.style.padding = '0';
            }
        }

        // 给切换按钮添加点击事件监听器
        const changeLayoutButton = document.getElementById('changeLayoutButton');
        if (changeLayoutButton) {
            changeLayoutButton.addEventListener('click', toggleLayout);
        }

        // 添加触摸支持
        addDropdownTouchSupport();

        // 启动页面变化监控
        observePageChanges();

        // 新增：高亮目标楼层（新版UI）
        var urlParams = new URLSearchParams(window.location.search);
        var targetFloor = urlParams.get('tofloor');
        if (targetFloor) {
            setTimeout(function () {
                var floorEls = document.querySelectorAll('.forum-post .floor-number');
                for (var i = 0; i < floorEls.length; i++) {
                    if (floorEls[i].textContent == targetFloor) {
                        var post = floorEls[i].closest('.forum-post');
                        if (post) {
                            post.classList.add('floor-highlight');
                            setTimeout(function () {
                                post.classList.remove('floor-highlight');
                            }, 3000);
                        }
                        break;
                    }
                }
            }, 200);
        }

        callHighlightAndScroll();
    });

    // 新的函数：更新切换样式按钮的文本
    function updateChangeLayoutButtonText() {
        const changeLayoutButton = document.getElementById('changeLayoutButton');
        if (changeLayoutButton) {
            changeLayoutButton.textContent = window.isCustomLayoutEnabled ? '切回旧版' : '切换新版';
        }
    }

    // 修改后的函数：给下拉菜单添加点击和鼠标移出事件支持
    function addDropdownTouchSupport() {
        document.querySelectorAll('.dropdown').forEach(function (dropDownElem) {
            let dropdownContent = dropDownElem.querySelector('.dropdown-content');
            if (!dropdownContent) return;

            // 添加点击事件
            dropDownElem.addEventListener('click', function (event) {
                if (dropdownContent.style.display === 'block') {
                    dropdownContent.style.display = 'none';
                } else {
                    dropdownContent.style.display = 'block';
                }
                // 防止事件冒泡
                event.stopPropagation();
            });

            // 添加鼠标移出事件
            dropDownElem.addEventListener('mouseleave', function () {
                dropdownContent.style.display = 'none';
            });

            // 添加鼠标移入事件，重新应用hover效果
            dropDownElem.addEventListener('mouseenter', function () {
                dropdownContent.style.display = 'block';
            });

            // 点击页面其他地方关闭下拉菜单
            window.addEventListener('click', function () {
                dropdownContent.style.display = 'none';
            });
        });
    }

    // 辅助函数：获取URL参数
    function getUrlParam(name) {
        var results = new RegExp('[?&]' + name + '=([^&#]*)').exec(window.location.href);
        return results ? decodeURIComponent(results[1]) : '';
    }

    // 封装：高亮并滚动到目标楼层
    function highlightAndScrollToFloorNum(floorNum, highlightMs = CONFIG.highlightDuration, scrollDelay = CONFIG.scrollDelay) {
        // 先移除所有高亮
        DOM.selectAll('.floor-highlight').forEach(el => el.classList.remove('floor-highlight'));

        // 新版UI：优先通过data-floor查找
        var post = document.querySelector('.forum-post[data-floor="' + floorNum + '"]');
        if (post) {
            // 立即强制触发重绘，确保移除后再添加高亮有动画反馈
            void post.offsetWidth;
            post.classList.add('floor-highlight');
            setTimeout(() => post.classList.remove('floor-highlight'), highlightMs);
            setTimeout(() => {
                var offset = CONFIG.headerOffset;
                var top = post.getBoundingClientRect().top + window.scrollY - offset;
                window.scrollTo({ top, behavior: 'smooth' });
            }, scrollDelay);
            return true;
        }

        // 兼容旧实现：通过楼层号文本查找
        var floorEls = DOM.selectAll('.forum-post .floor-number');
        for (var i = 0; i < floorEls.length; i++) {
            if (floorEls[i].textContent == floorNum) {
                var post2 = floorEls[i].closest('.forum-post');
                if (post2) {
                    void post2.offsetWidth;
                    post2.classList.add('floor-highlight');
                    setTimeout(() => post2.classList.remove('floor-highlight'), highlightMs);
                    setTimeout(() => {
                        var offset = CONFIG.headerOffset;
                        var top = post2.getBoundingClientRect().top + window.scrollY - offset;
                        window.scrollTo({ top, behavior: 'smooth' });
                    }, scrollDelay);
                }
                return true;
            }
        }
        // 旧版UI
        var oldTarget = document.getElementById('floor-' + floorNum);
        if (oldTarget) {
            oldTarget.style.transition = 'background-color 0.5s ease-out';
            oldTarget.style.backgroundColor = '#fff8dc';
            setTimeout(() => { oldTarget.style.backgroundColor = ''; }, highlightMs);
            setTimeout(() => {
                var offset = CONFIG.headerOffset;
                var top = oldTarget.getBoundingClientRect().top + window.scrollY - offset;
                window.scrollTo({ top, behavior: 'smooth' });
            }, scrollDelay);
            return true;
        }
        return false;
    }

    // 页面加载/切换布局/新版渲染后调用
    function highlightAndScrollToUrlFloor() {
        var urlParams = new URLSearchParams(window.location.search);
        var targetFloor = urlParams.get('tofloor');
        if (targetFloor) highlightAndScrollToFloorNum(targetFloor);
    }

    // 替换原有 highlightAndScrollToFloor 相关调用
    function callHighlightAndScroll() {
        setTimeout(highlightAndScrollToUrlFloor, 200);
    }

    // 统一拦截"回复X楼"点击事件，实现同页滚动优化

    document.addEventListener('click', function (e) {
        var a = e.target.closest('a');
        if (!a) return;

        // 1. 先处理锚点跳转
        var hashMatch = a.hash && a.hash.match(/^#floor-(\d+)$/);
        if (hashMatch) {
            var floorNum = hashMatch[1];
            if (highlightAndScrollToFloorNum(floorNum)) {
                e.preventDefault();
                return;
            }
        }

        // 2. 处理带tofloor参数的链接
        var href = a.getAttribute('href');
        if (href) {
            var tofloorMatch = href.match(/[?&]tofloor=(\d+)/);
            if (tofloorMatch) {
                var floorNum2 = tofloorMatch[1];
                if (highlightAndScrollToFloorNum(floorNum2)) {
                    e.preventDefault();
                    return;
                }
            }
        }
    });

    // 将关键函数挂载到window对象上，使其全局可用
    window.extractData = extractData;
    window.buildNewLayout = buildNewLayout;
    window.replaceContent = replaceContent;

    // 简化：将核心布局转换函数全局挂载（用于异步刷新后自动应用新版UI）
    if (!window.applyNewLayoutToNewContent) {
        window.applyNewLayoutToNewContent = function () {
            if (!window.isCustomLayoutEnabled) return;
            document.querySelectorAll('.list-reply').forEach(oldElement => {
                const data = window.extractData(oldElement);
                const newElement = window.buildNewLayout(data);
                window.replaceContent(oldElement, newElement);
            });
            // 处理文本内容
            document.querySelectorAll(".retext").forEach(element => {
                if (typeof processTextContent === 'function') processTextContent(element);
            });
        };
    }

})();