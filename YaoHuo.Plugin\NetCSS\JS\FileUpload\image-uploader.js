function insertImageUploader() {
    // 查找标题label的父元素
    const titleLabel = document.querySelector('.form-group label');
    if (!titleLabel || titleLabel.textContent.trim() !== '标题') return;

    // 创建content-header容器(如果不存在)
    let contentHeader = titleLabel.parentElement;
    if (!contentHeader.classList.contains('content-header')) {
        contentHeader = document.createElement('div');
        contentHeader.className = 'content-header';
        titleLabel.parentElement.insertBefore(contentHeader, titleLabel);
        contentHeader.appendChild(titleLabel);
    }

    // 创建图床上传按钮容器和按钮
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'textarea-actions';

    const uploadBtn = document.createElement('button');
    uploadBtn.type = 'button';
    uploadBtn.className = 'action-btn-small';
    uploadBtn.onclick = showImageUploader;
    uploadBtn.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
            <circle cx="8.5" cy="8.5" r="1.5"/>
            <polyline points="21 15 16 10 5 21"/>
        </svg>
        <span>图床上传</span>
    `;

    // 组装按钮
    actionsDiv.appendChild(uploadBtn);
    contentHeader.appendChild(actionsDiv);

    // 创建modal结构
    const modal = document.createElement('div');
    modal.id = 'imageUploaderModal';
    modal.className = 'modal-tc';
    modal.innerHTML = `
        <div class="modal-tc-content">
            <div class="modal-tc-body">
                <iframe src="/tuchuang/?8" frameborder="0" width="100%" scrolling="no"></iframe>
            </div>
        </div>
    `;

    // 添加modal样式
    const style = document.createElement('style');
    style.textContent = `
        .action-btn-small {
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            cursor: pointer;
        }
        
        .action-btn-small:focus {
            outline: none;
        }

        .modal-tc {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
            overflow: hidden;
            align-items: center;
            justify-content: center;
        }

        .modal-tc-content {
            position: relative;
            background-color: #fff;
            width: 90%;
            max-width: 450px;
            border-radius: 8px;
            overflow: hidden;
            transform: translateY(-2%);
        }

        .modal-tc-body {
            overflow: hidden;
        }

        .modal-tc-body iframe {
            height: 400px;
            max-height: 100%;
            border-radius: 8px;
            overflow: hidden;
        }

        /* 添加媒体查询 */
        @media screen and (max-width: 433px) {
            .modal-tc-body iframe {
                height: 380px;
            }
        }

        @media screen and (max-width: 389px) {
            .modal-tc-body iframe {
                height: 370px;
            }
        }
    `;

    // 插入modal和样式
    document.head.appendChild(style);
    document.body.appendChild(modal);

    // 添加modal点击关闭事件
    modal.addEventListener('click', (event) => {
        if (event.target === modal) {
            closeImageUploader();
        }
    });
}

// 显示图床上传modal
function showImageUploader() {
    const modal = document.getElementById('imageUploaderModal');
    modal.style.display = 'flex';
}

// 关闭图床上传modal
function closeImageUploader() {
    document.getElementById('imageUploaderModal').style.display = 'none';
}

// 初始化
document.addEventListener('DOMContentLoaded', insertImageUploader);