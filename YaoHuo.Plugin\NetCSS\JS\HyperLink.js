const DEBUG = false;

function debugLog(...args) {
  if (DEBUG) {
    console.log(...args);
  }
}

// 1. 所有域名白名单和正则表达式常量
const domainWhitelist = [
  "yaohuo.me",
  "wap.yaohuo.me", 
  "www.yaohuo.me",
  "yaohw.com",
  "www.yaohw.com"
];

const jdLinkRegex = /^(https?:\/\/)?((?:u|item\.m|m\.jd\.com\/product|kpl\.m|item|plus\.m|3\.cn|v\.m|prodev\.m|pro\.m|jingfen)\.jd\.com|3\.cn)/;
const tbLinkRegex = /^https?:\/\/(m\.tb\.cn|e\.tb\.cn|chaoshi\.detail\.tmall\.com\/item\.htm|item\.taobao\.com\/item\.htm|detail\.tmall\.com\/item\.htm|s\.click\.taobao\.com|uland\.taobao\.com\/coupon\/edetail)/;
const pddLinkRegex = /^https?:\/\/(p\.pinduoduo\.com|mobile\.yangkeduo\.com)/;
const mtLinkRegex = /^https?:\/\/(click\.meituan\.com|dpurl\.cn)/;

// 2. 所有判断函数
function isJdLink(url) {
  return jdLinkRegex.test(url);
}

function isTbLink(url) {
  return tbLinkRegex.test(url);
}

function isPddLink(url) {
  return pddLinkRegex.test(url);
}

function isMtLink(url) {
  return mtLinkRegex.test(url);
}

function extractJdSku(url) {
  const skuMatch = url.match(/sku=(\d+)/);
  if (skuMatch) {
    return skuMatch[1];
  }
  const itemMatch = url.match(/\/(\d+)\.html/);
  if (itemMatch) {
    return itemMatch[1];
  }
  return null;
}

const processTextContent = async (node) => {
  if (node.nodeType === 3) {
    const text = node.textContent;
    const urlRegex = /https?:\/\/[a-zA-Z0-9.:/?=&#_%-@]+/g;
    const matches = text.match(urlRegex);
    if (matches) {
      let html = text;
      for (const match of matches) {
        const fullUrl = match.trim();

        html = html.replace(match, `<a href="${fullUrl}" target="_blank">${match}</a>`);

        if (isJdLink(fullUrl)) {
          try {
            const conversionResponse = await convertJdLink(fullUrl);
            if (conversionResponse && conversionResponse.code === 0 && conversionResponse.data && conversionResponse.data.shortUrl) {
              html = html.replace(`href="${fullUrl}"`, `href="${conversionResponse.data.shortUrl}"`);
            }
          } catch (error) {
            debugLog(`京东链接转换失败: ${fullUrl}`, error);
          }
        } else if (isTbLink(fullUrl)) {
          convertTbLink(fullUrl);
        } else if (isPddLink(fullUrl)) {  // 添加这个条件
          try {
            const shortUrl = await convertPddLink(fullUrl);
            html = html.replace(`href="${fullUrl}"`, `href="${shortUrl}"`);
          } catch (error) {
            debugLog(`拼多多链接转换失败: ${fullUrl}`, error);
          }
        } else if (isMtLink(fullUrl)) {  // 添加美团链接处理
          try {
            const shortUrl = await convertMtLink(fullUrl);
            html = html.replace(`href="${fullUrl}"`, `href="${shortUrl}"`);
          } catch (error) {
            debugLog(`美团链接转换失败: ${fullUrl}`, error);
          }
        } else {
          try {
            const urlObject = new URL(fullUrl);
            const urlHost = urlObject.host;
            const isInternal = domainWhitelist.some((el) => urlHost === el);

            if (isInternal) {
              const path = urlObject.pathname + urlObject.search;
              const isHomePage = path === "/" || path === "/wapindex.aspx?siteid=1000";
              const href = isHomePage ? fullUrl : path + urlObject.hash;
              html = html.replace(`href="${fullUrl}"`, `href="${isHomePage ? fullUrl : href}" target="_self"`);
            }
          } catch (error) {
            debugLog(`处理 URL ${fullUrl} 时出错: ${error}`);
          }
        }
      }

      const newElement = document.createElement('span');
      newElement.innerHTML = html;
      node.parentNode.replaceChild(newElement, node);
    }
  } else if (node.nodeType === 1 && node.tagName.toLowerCase() === "a") {
    const href = node.getAttribute('href');
    if (href) {
      try {
        const parsedUrl = new URL(href, window.location.href);
        const path = parsedUrl.pathname + parsedUrl.search;
        const isHomePage = path === "/" || path === "/wapindex.aspx?siteid=1000";
        if (!isHomePage && domainWhitelist.includes(parsedUrl.host)) {
          const relativePath = parsedUrl.pathname + parsedUrl.search + parsedUrl.hash;
          node.setAttribute('href', relativePath);
          node.setAttribute('target', '_self');
        }
      } catch (error) {
        debugLog(`处理 URL ${href} 时出错: ${error}`);
      }
    }
  } else if (node.nodeType === 1 && node.tagName.toLowerCase() !== 'pre' && node.tagName.toLowerCase() !== 'code') {
    const childNodes = node.childNodes;
    for (let i = 0; i < childNodes.length; i++) {
      await processTextContent(childNodes[i]);
    }
  }
};

const convertJdLink = async (originalLink) => {
  try {
    const jdUrl = encodeURIComponent(originalLink);
    const apiUrl = `https://api.yaohuo.me/api/convert?materialId=${jdUrl}`;

    const response = await fetch(apiUrl);
    const data = await response.json();

    if (response.ok) {
      if (data.code === 0 && data.data && data.data.shortUrl) {
        const shortUrl = data.data.shortUrl;
        updateAnchorTags(originalLink, shortUrl);
        return data;
      } else {
        throw new Error(JSON.stringify(data));
      }
    } else {
      throw new Error(`网络错误: ${response.status}`);
    }
  } catch (error) {
    debugLog(`京东链接转换失败: ${originalLink}`, error);
    throw error;
  }
};

function updateAnchorTags(originalLink, newLink) {
  const anchorTags = document.querySelectorAll(`a[href="${originalLink}"]`);
  for (const tag of anchorTags) {
    tag.href = newLink;
  }
}

const convertJdLinksInATags = async () => {
  const aTags = document.querySelectorAll('a');
  for (const aTag of aTags) {
    if (aTag.classList.contains('jdurl')) {
      continue;
    }
    const href = aTag.getAttribute('href');
    if (href && isJdLink(href)) {
      await convertJdLink(href);
    }
  }
};

const convertTbLink = async (originalLink) => {
  try {
    const simplifiedLink = simplifyTaobaoLink(originalLink);
    const tbUrl = encodeURIComponent(simplifiedLink);
    const apiUrl = `https://api.yaohuo.me/api/convert-tb?content=${tbUrl}`;

    const response = await fetch(apiUrl);

    if (response.ok) {
      const data = await response.json();
      if (data.data && data.data.shortUrl) {
        updateAnchorTags(originalLink, data.data.shortUrl);
      } else if (data.data && data.data.longTpwd) {
        updateAnchorTags(originalLink, data.data.longTpwd);
      } else {
        debugLog(`简化链接 ${simplifiedLink} 转换失败: 未获得预期的响应数据`);
      }
    } else {
    }
  } catch (error) {
    debugLog(`处理链接 ${originalLink} 时发生错误`, error);
  }
};

const convertTbLinksInATags = async () => {
  const aTags = document.querySelectorAll('a');
  for (const aTag of aTags) {
    if (aTag.classList.contains('jdurl')) {
      continue;
    }
    const href = aTag.getAttribute('href');
    if (href && isTbLink(href)) {
      convertTbLink(href);
    }
  }
};

const textContentElements = document.querySelectorAll(".bbscontent, .bubble, .bubble-left, .bubble-right, .retext, .retext");

textContentElements.forEach((element) => {
  processTextContent(element);
});

convertJdLinksInATags();

convertTbLinksInATags();

function simplifyTaobaoLink(url) {
  try {
    const parsedUrl = new URL(url);
    const id = parsedUrl.searchParams.get('id');
    if (id) {
      if (url.includes('item.taobao.com')) {
        return `https://item.taobao.com/item.htm?id=${id}`;
      } else if (url.includes('detail.tmall.com')) {
        return `https://detail.tmall.com/item.htm?id=${id}`;
      } else if (url.includes('chaoshi.detail.tmall.com')) {
        return `https://chaoshi.detail.tmall.com/item.htm?id=${id}`;
      }
    }
    return url;
  } catch (error) {
    debugLog('化淘宝链接时出错:', error);
    return url;
  }
}

const convertPddLink = async (originalLink) => {
  try {
    const apiUrl = `https://api.yaohuo.me/api/convert-pdd?materialId=${encodeURIComponent(originalLink)}`;

    const response = await fetch(apiUrl);
    if (response.ok) {
      const data = await response.json();
      if (data.code === 0 && data.data && data.data.shortUrl) {
        const shortUrl = data.data.shortUrl;
        updateAnchorTags(originalLink, shortUrl);
        return shortUrl;
      } else {
        throw new Error(JSON.stringify(data));
      }
    } else {
      throw new Error(`网络错误: ${response.status}`);
    }
  } catch (error) {
    debugLog(`拼多多链接转换失败: ${originalLink}`, error);
    throw error;
  }
};

const convertMtLink = async (originalLink) => {
  try {
    const apiUrl = `https://api.yaohuo.me/api/convert-mt?materialId=${encodeURIComponent(originalLink)}`;

    const response = await fetch(apiUrl);
    if (response.ok) {
      const data = await response.json();
      if (data.code === 0 && data.data && data.data.shortUrl) {
        const shortUrl = data.data.shortUrl;
        updateAnchorTags(originalLink, shortUrl);
        return shortUrl;
      } else {
        throw new Error(JSON.stringify(data));
      }
    } else {
      throw new Error(`网络错误: ${response.status}`);
    }
  } catch (error) {
    debugLog(`美团链接转换失败: ${originalLink}`, error);
    throw error;
  }
};

const convertPddLinksInATags = async () => {
  const aTags = document.querySelectorAll('a');
  for (const aTag of aTags) {
    if (aTag.classList.contains('jdurl')) {
      continue;
    }
    const href = aTag.getAttribute('href');
    if (href && isPddLink(href)) {
      try {
        await convertPddLink(href);
      } catch (error) {
        debugLog(`处理拼多多链接时发生错误: ${href}`, error);
      }
    }
  }
};

const convertMtLinksInATags = async () => {
  const aTags = document.querySelectorAll('a');
  for (const aTag of aTags) {
    if (aTag.classList.contains('jdurl')) {
      continue;
    }
    const href = aTag.getAttribute('href');
    if (href && isMtLink(href)) {
      try {
        await convertMtLink(href);
      } catch (error) {
        debugLog(`处理美团链接时发生错误: ${href}`, error);
      }
    }
  }
};

// 在主执行部分添加：
convertPddLinksInATags();
convertMtLinksInATags();

// 处理需要点击确认的链接
const confirmActionLinks = document.querySelectorAll('a.js-confirm-action');
confirmActionLinks.forEach(link => {
    link.addEventListener('click', function(event) {
        const message = this.getAttribute('data-confirm-message') || '确定要执行此操作吗？'; // 从data属性获取消息，或使用默认消息
        if (!confirm(message)) {
            event.preventDefault(); // 如果用户点击"取消"，则阻止链接的默认行为
        }
    });
});
