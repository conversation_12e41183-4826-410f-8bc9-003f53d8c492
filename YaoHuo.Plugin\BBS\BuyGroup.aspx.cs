using System;
using System.Collections.Generic;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.Template.Models;
using YaoHuo.Plugin.BBS.Models;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite.Services.Config;

namespace YaoHuo.Plugin.BBS
{
    public partial class BuyGroup : MyPageWap
    {
        public string ERROR = "";
        public string INFO = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            // 会员可见判断
            if (!IsCheckManagerLvl("|00|01|02|03|04|", ""))
            {
                Response.Redirect("/");
            }

            // 检查用户UI偏好并处理版本切换
            try
            {
                bool newVersionRendered = CheckAndHandleUIPreference();
                if (newVersionRendered)
                {
                    // 新版渲染成功，直接返回，不再执行后续的旧版代码
                    return;
                }
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }

            // 继续执行旧版逻辑...
        }

        /// <summary>
        /// 检查用户UI偏好并处理版本切换
        /// </summary>
        private bool CheckAndHandleUIPreference()
        {
            string uiPreference = "";
            if (Request.Cookies["ui_preference"] != null)
            {
                uiPreference = Request.Cookies["ui_preference"].Value;
            }
            if (string.IsNullOrEmpty(uiPreference))
            {
                uiPreference = "old";
            }

            if (uiPreference == "new")
            {
                return TryRenderWithHandlebars();
            }
            return false;
        }

        private bool TryRenderWithHandlebars()
        {
            try
            {
                var templateServiceType = Type.GetType("YaoHuo.Plugin.WebSite.Tool.TemplateService, YaoHuo.Plugin");

                if (templateServiceType != null)
                {
                    var getViewModeMethod = templateServiceType.GetMethod("GetViewMode");
                    var renderPageMethod = templateServiceType.GetMethod("RenderPageWithLayout");

                    if (getViewModeMethod != null && renderPageMethod != null)
                    {
                        string viewMode = (string)getViewModeMethod.Invoke(null, null);

                        if (viewMode == "new")
                        {
                            RenderWithHandlebars();
                            return true;
                        }
                    }
                }

                ERROR = "Handlebars模板服务不可用";
                return false;
            }
            catch (System.Threading.ThreadAbortException)
            {
                return true;
            }
            catch (Exception ex)
            {
                ERROR = "新版模板加载失败: " + WapTool.ErrorToString(ex.ToString());
                return false;
            }
        }

        private void RenderWithHandlebars()
        {
            try
            {
                var pageModel = BuildPageModel();
                var templateServiceType = Type.GetType("YaoHuo.Plugin.WebSite.Tool.TemplateService, YaoHuo.Plugin");
                var renderPageMethod = templateServiceType.GetMethod("RenderPageWithLayout");

                string finalHtml = (string)renderPageMethod.Invoke(null, new object[]
                {
                    "~/Template/Pages/BuyGroup.hbs",
                    pageModel,
                    "购买身份",
                    new HeaderOptionsModel { ShowViewModeToggle = false },
                    null,
                    "~/Template/Layouts/MainLayout.hbs"
                });

                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write(finalHtml);
                Response.End();
            }
            catch (System.Threading.ThreadAbortException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}</div>");
                System.Web.HttpContext.Current.ApplicationInstance.CompleteRequest();
            }
        }

        /// <summary>
        /// 构建页面数据模型
        /// </summary>
        private BuyGroupPageModel BuildPageModel()
        {
            var model = new BuyGroupPageModel
            {
                PageTitle = "购买身份"
            };

            // 构建当前身份信息
            BuildCurrentIdentityModel(model);

            // 构建身份选项列表
            BuildIdentityOptionsModel(model);

            // 构建站点信息
            BuildSiteInfoModel(model);

            // 构建消息模型
            BuildMessageModel(model);

            return model;
        }

        /// <summary>
        /// 构建当前身份信息
        /// </summary>
        private void BuildCurrentIdentityModel(BuyGroupPageModel model)
        {
            if (userVo != null && userVo.idname != null)
            {
                model.CurrentIdentity.IdentityHtml = WapTool.GetMyID(userVo.idname, lang, userVo.endTime);
                model.CurrentIdentity.DisplayName = userVo.idname;
                model.CurrentIdentity.IsVip = userVo.idname != "普通会员";

                if (userVo.endTime > DateTime.Now)
                {
                    model.CurrentIdentity.HasEndTime = true;
                    model.CurrentIdentity.EndTime = userVo.endTime.ToString("yyyy-MM-dd");
                }
            }
            else
            {
                model.CurrentIdentity.DisplayName = "普通会员";
                model.CurrentIdentity.IdentityHtml = "普通会员";
                model.CurrentIdentity.IsVip = false;
            }
        }

        /// <summary>
        /// 构建身份选项列表 - 从JSON配置文件加载（排除管理员身份）
        /// </summary>
        private void BuildIdentityOptionsModel(BuyGroupPageModel model)
        {
            try
            {
                // 使用专门的可购买身份选项方法，排除管理员身份
                var identityOptions = BBSConfigService.GetPurchasableIdentityOptions();

                if (identityOptions != null && identityOptions.Count > 0)
                {
                    model.IdentityOptions.AddRange(identityOptions);
                }
                else
                {
                    BuildFallbackIdentityOptions(model);
                }
            }
            catch (Exception)
            {
                ERROR = "身份配置加载失败，请联系管理员";

                try
                {
                    BuildFallbackIdentityOptions(model);
                }
                catch (Exception)
                {
                    ERROR = "系统配置错误，请联系管理员";
                }
            }
        }

        /// <summary>
        /// 构建fallback身份选项（当配置文件加载失败时使用）
        /// </summary>
        private void BuildFallbackIdentityOptions(BuyGroupPageModel model)
        {

            // 彩色昵称选项
            var colorNicknameOption = new IdentityOptionModel
            {
                Id = 0,
                Name = "彩色昵称",
                DisplayName = "彩色昵称",
                Price = 3,
                CoinPrice = 37500,
                Period = "月",
                Type = "basic",
                IsColorNickname = true,
                ColorOptions = new List<ColorOptionModel>
                {
                    new ColorOptionModel { Color = "green", Name = "绿色昵称", TargetId = 120, IsDefault = false },
                    new ColorOptionModel { Color = "red", Name = "红色昵称", TargetId = 340, IsDefault = true },
                    new ColorOptionModel { Color = "blue", Name = "蓝色昵称", TargetId = 341, IsDefault = false },
                    new ColorOptionModel { Color = "purple", Name = "紫色昵称", TargetId = 342, IsDefault = false },
                    new ColorOptionModel { Color = "pink", Name = "粉色昵称", TargetId = 355, IsDefault = false },
                    new ColorOptionModel { Color = "pink-purple", Name = "粉紫昵称", TargetId = 356, IsDefault = false }
                }
            };
            model.IdentityOptions.Add(colorNicknameOption);

            // 红名VIP
            var redVipOption = new IdentityOptionModel
            {
                Id = 101,
                Name = "红名VIP",
                DisplayName = "红名VIP",
                Price = 5,
                CoinPrice = 62500,
                Period = "月",
                Type = "premium",
                NameCssClass = "text-red-vip",
                IconUrl = "/netimages/vip.gif",
                Privileges = new List<string> { "双向拉黑", "黑名单上限+10" },
                PurchaseUrl = $"/bbs/togroupcoinbuy.aspx?toid=101"
            };
            model.IdentityOptions.Add(redVipOption);

            // 金名VIP
            var goldVipOption = new IdentityOptionModel
            {
                Id = 358,
                Name = "金名VIP",
                DisplayName = "金名VIP",
                Price = 6,
                CoinPrice = 75000,
                Period = "月",
                Type = "premium",
                NameCssClass = "text-gold-vip",
                IconUrl = "/netimages/newvip.gif",
                Privileges = new List<string> { "双向拉黑", "黑名单上限+10" },
                PurchaseUrl = $"/bbs/togroupcoinbuy.aspx?toid=358"
            };
            model.IdentityOptions.Add(goldVipOption);
        }

        /// <summary>
        /// 构建站点信息
        /// </summary>
        private void BuildSiteInfoModel(BuyGroupPageModel model)
        {
            model.SiteInfo.SiteId = siteid;
            model.SiteInfo.HttpStart = http_start;
            model.SiteInfo.BackUrl = "/myfile.aspx";
        }

        /// <summary>
        /// 构建消息模型
        /// </summary>
        private void BuildMessageModel(BuyGroupPageModel model)
        {
            if (!string.IsNullOrEmpty(ERROR))
            {
                model.Message.HasMessage = true;
                model.Message.Type = "error";
                model.Message.Content = ERROR;
                model.Message.IsSuccess = false;
            }
            else if (!string.IsNullOrEmpty(INFO))
            {
                model.Message.HasMessage = true;
                model.Message.Type = "success";
                model.Message.Content = INFO;
                model.Message.IsSuccess = true;
            }
        }
    }
}