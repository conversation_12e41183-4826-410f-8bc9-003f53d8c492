﻿using System;
using KeLin.ClassManager;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.Tool
{
    /// <summary>
    /// 用户偏好设置数据访问类
    /// </summary>
    public class UserPreferencesRepository
    {
        private readonly string _connectionString;

        public UserPreferencesRepository(string siteInstanceName)
        {
            _connectionString = PubConstant.GetConnectionString(siteInstanceName);
            if (string.IsNullOrEmpty(_connectionString))
            {
                // 使用 string.Format 替代 string interpolation ($"...")，以兼容 C# 5.0
                throw new InvalidOperationException(string.Format("无法获取站点实例 '{0}' 的数据库连接字符串。", siteInstanceName));
            }
        }

        /// <summary>
        /// 获取用户的新版回帖UI设置，统一处理登录/未登录用户的逻辑
        /// </summary>
        /// <param name="userid">用户ID (字符串形式)</param>
        /// <param name="instanceName">数据库实例名</param>
        /// <returns>新版回帖UI是否启用</returns>
        public static bool GetUserNewReplyUIEnabled(string userid, string instanceName, object _ = null)
        {
            // 未登录用户，始终返回true，不读取cookie，也不允许设置
            if (userid == "0")
            {
                return true;
            }
            if (long.TryParse(userid, out long userIdLong))
            {
                try
                {
                    // 已登录用户，从数据库读取设置
                    var repo = new UserPreferencesRepository(instanceName);
                    return repo.GetNewReplyUIEnabled(userIdLong);
                }
                catch (Exception)
                {
                    // 如果出错，使用默认值
                }
            }
            // 用户ID无效或异常，使用默认值
            return true;
        }

        /// <summary>
        /// 获取用户的偏好设置
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>新版回帖UI是否启用</returns>
        public bool GetNewReplyUIEnabled(long userId)
        {
            // 默认为启用
            bool isEnabled = true;

            try
            {
                string sql = "SELECT NewReplyUIEnabled FROM UserPreferences WHERE UserId = @UserId";

                // ✅ 使用DapperHelper安全查询，避免SQL注入
                var result = DapperHelper.ExecuteScalar<bool?>(_connectionString, sql, new {
                    UserId = userId
                });

                if (result.HasValue)
                {
                    isEnabled = result.Value;
                }
            }
            catch (Exception)
            {
                // 如果出错，使用默认值
            }

            return isEnabled;
        }

        /// <summary>
        /// 保存用户的新版回帖UI启用状态
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="isEnabled">是否启用</param>
        /// <returns>是否保存成功</returns>
        public bool SaveNewReplyUIEnabled(long userId, bool isEnabled)
        {
            try
            {
                // 首先检查表是否存在
                bool tableExists = CheckTableExists("UserPreferences");
                if (!tableExists)
                {
                    if (!CreateUserPreferencesTable())
                    {
                        return false;
                    }
                }

                // ✅ 使用DapperHelper安全执行MERGE操作，避免SQL注入
                string sql = @"MERGE INTO UserPreferences AS target
                              USING (SELECT @UserId AS UserId) AS source
                              ON (target.UserId = source.UserId)
                              WHEN MATCHED THEN
                                  UPDATE SET NewReplyUIEnabled = @IsEnabled
                              WHEN NOT MATCHED THEN
                                  INSERT (UserId, NewReplyUIEnabled)
                                  VALUES (@UserId, @IsEnabled);";

                int affectedRows = DapperHelper.Execute(_connectionString, sql, new {
                    UserId = userId,
                    IsEnabled = isEnabled
                });

                return affectedRows > 0;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 检查数据库中是否存在指定表
        /// </summary>
        private bool CheckTableExists(string tableName)
        {
            try
            {
                string sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @TableName";

                // ✅ 使用DapperHelper安全查询，避免SQL注入
                int count = DapperHelper.ExecuteScalar<int>(_connectionString, sql, new {
                    TableName = tableName
                });

                return count > 0;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// 创建用户偏好设置表
        /// </summary>
        private bool CreateUserPreferencesTable()
        {
            try
            {
                string sql = @"CREATE TABLE UserPreferences (
                              UserId BIGINT NOT NULL PRIMARY KEY,
                              NewReplyUIEnabled BIT NOT NULL DEFAULT 1,
                              CONSTRAINT FK_UserPreferences_user FOREIGN KEY (UserId)
                              REFERENCES [user] (userid)
                              )";

                // ✅ 使用DapperHelper安全执行DDL，避免SQL注入
                DapperHelper.Execute(_connectionString, sql, null);
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}