﻿using System;
using KeLin.ClassManager;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.Admin
{
    public class AddRowInfoWAP : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string string_11 = "";

        public string page = "";

        public string INFO = "";

        public string ERROR = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = base.Request.Form.Get("action");
            page = GetRequestValue("page");
            IsCheckUserManager(userid, userVo.managerlvl, "", "admin/BaseSiteModifyWML.aspx?siteid=" + siteid);
            if (!(action == "gomod"))
            {
                return;
            }
            try
            {
                string requestValue = GetRequestValue("path");
                requestValue = WapTool.URLtoWAP(requestValue);
                if (!(classid == "0"))
                {
                    // ✅ 使用DapperHelper进行安全的栏目配置更新
                    string connectionString = PubConstant.GetConnectionString(string_10);
                    string updateClassSql = "UPDATE [class] SET siterowremark = @SiteRowRemark WHERE userid = @UserId AND classid = @ClassId";
                    DapperHelper.Execute(connectionString, updateClassSql, new {
                        SiteRowRemark = DapperHelper.LimitLength(requestValue, 4000),
                        UserId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        ClassId = DapperHelper.SafeParseLong(classid, "栏目ID")
                    });
                }
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}