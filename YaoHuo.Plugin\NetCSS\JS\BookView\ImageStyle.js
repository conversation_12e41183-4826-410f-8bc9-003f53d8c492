(function() {
    // 图片增加阴影或圆角
    function addShadowOrRoundCorner(element) {
        const images = element.querySelectorAll('img');
        images.forEach(img => {
            if (img.complete) {
                checkAndAddShadowOrRoundCorner(img);
            } else {
                img.addEventListener('load', () => {
                    checkAndAddShadowOrRoundCorner(img);
                });
            }
        });
    }

    function checkAndAddShadowOrRoundCorner(img) {
        const imgWidth = img.naturalWidth;
        const imgHeight = img.naturalHeight;
        const aspectRatio = imgWidth / imgHeight;

        if (imgWidth > 200) {
            if (aspectRatio >= 10) {
                img.style.borderRadius = '3px';
            } else if (aspectRatio >= 3) {
                img.style.borderRadius = '5px';
            } else if (aspectRatio >= 1.2 && aspectRatio < 3) {
                img.style.borderRadius = '7px';
            } else {
                img.style.borderRadius = '9px';
            }
            img.style.boxShadow = 'rgb(0 0 0 / 18%) 0px 0px 4px';
        } else {
            img.style.borderRadius = '3px';
        }
    }

    // 全局图片美化处理器：MutationObserver 监听
    function initImageStyleObserver() {
        const containers = ['.recontent', '.bbscontent'];
        
        // 处理单个img
        function processImg(img) {
            if (img.complete) {
                checkAndAddShadowOrRoundCorner(img);
            } else {
                img.addEventListener('load', function handler() {
                    checkAndAddShadowOrRoundCorner(img);
                    img.removeEventListener('load', handler);
                });
            }
        }
        
        // 处理容器下所有 span.retext 下的 img
        function processAllImgsInReText(element) {
            if (!element) return;
            // 只处理 span.retext 下的图片
            const retextSpans = element.querySelectorAll('span.retext');
            retextSpans.forEach(span => {
                const imgs = span.querySelectorAll('img');
                imgs.forEach(processImg);
            });
        }
        
        // 处理 bbscontent 区域下所有图片
        function processAllImgsInBbsContent(element) {
            if (!element) return;
            const imgs = element.querySelectorAll('img');
            imgs.forEach(processImg);
        }
        
        // 监听函数
        function observeContainer(container) {
            if (!container) return;
            // 首次处理
            if (container.classList.contains('bbscontent')) {
                processAllImgsInBbsContent(container);
            } else {
                processAllImgsInReText(container);
            }
            // 监听后续DOM变化
            const observer = new MutationObserver(mutations => {
                mutations.forEach(mutation => {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === 1) { // 元素节点
                            if (container.classList.contains('bbscontent')) {
                                processAllImgsInBbsContent(node);
                            } else {
                                processAllImgsInReText(node);
                            }
                        }
                    });
                });
            });
            observer.observe(container, { childList: true, subtree: true });
        }
        
        // 启动监听
        containers.forEach(sel => {
            document.querySelectorAll(sel).forEach(observeContainer);
        });
    }

    // 页面加载完成后初始化
    document.addEventListener("DOMContentLoaded", () => {
        const retextElements = document.querySelectorAll('.retext');
        const bbscontentElements = document.querySelectorAll('.bbscontent');
        retextElements.forEach(addShadowOrRoundCorner);
        bbscontentElements.forEach(addShadowOrRoundCorner);
        initImageStyleObserver();
    });
})(); 