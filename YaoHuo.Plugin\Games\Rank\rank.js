// rank.js
// Tab切换与分页链接同步（Rank/Book_List.aspx）
(function () {
    var btnT = document.getElementById('btn-times'),
        btnM = document.getElementById('btn-money'),
        tabT = document.getElementById('tab-times'),
        tabM = document.getElementById('tab-money'),
        pager = document.getElementById('pager'),
        icon = document.getElementById('header-icon'),
        text = document.getElementById('header-text');
    function syncLinks(type) {
        var as = pager.getElementsByTagName('a');
        for (var i = 0; i < as.length; i++) {
            try {
                var u = new URL(as[i].href, location.origin);
                u.searchParams.set('type', type);
                as[i].href = u.toString();
            } catch (e) { }
        }
    }
    function updateHistory(type) {
        // 更新URL参数，不刷新页面
        if (history.pushState) {
            var url = new URL(window.location);
            url.searchParams.set('type', type);
            window.history.pushState({ type: type }, '', url);
        }
    }
    function activate(type) {
        if (type === '1') {
            icon.className = 'fas fa-coins mr-2';
            text.textContent = '赚币排行';
            btnT.classList.remove('text-teal-600', 'border-b-2', 'border-teal-600');
            btnT.classList.add('text-gray-500', 'hover:text-teal-600');
            btnM.classList.remove('text-gray-500', 'hover:text-teal-600');
            btnM.classList.add('text-teal-600', 'border-b-2', 'border-teal-600');
            tabT.classList.remove('active');
            tabM.classList.add('active');
        } else {
            icon.className = 'fas fa-trophy mr-2';
            text.textContent = '净胜排行';
            btnM.classList.remove('text-teal-600', 'border-b-2', 'border-teal-600');
            btnM.classList.add('text-gray-500', 'hover:text-teal-600');
            btnT.classList.remove('text-gray-500', 'hover:text-teal-600');
            btnT.classList.add('text-teal-600', 'border-b-2', 'border-teal-600');
            tabT.classList.add('active');
            tabM.classList.remove('active');
        }
        syncLinks(type);
        updateHistory(type);
    }
    var defaultType = window.rankDefaultType || '0';
    activate(defaultType);
    if (btnT) btnT.onclick = function () { activate('0'); };
    if (btnM) btnM.onclick = function () { activate('1'); };

    // 监听浏览器前进后退
    window.addEventListener('popstate', function (event) {
        if (event.state && event.state.type) {
            activate(event.state.type);
        }
    });
})(); 