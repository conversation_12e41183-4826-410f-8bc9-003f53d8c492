﻿using KeLin.ClassManager;
using System;
using System.Web;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.Tool;

namespace YaoHuo.Plugin.BBS
{
	public class FriendList_del : MyPageWap
    {
		private string string_10 = PubConstant.GetAppString("InstanceName");

		public string action = "";

		public string linkURL = "";

		public string condition = "";

		public string ERROR = "";

		public string string_11 = "";

		public string friendtype = "";

		public string id = "";

		public string backurl = "";

		public string INFO = "";

		public string page = "";

		protected void Page_Load(object sender, EventArgs e)
		{
			action = GetRequestValue("action");
			backurl = base.Request.QueryString.Get("backurl");
			id = base.Request.QueryString.Get("id");
			page = base.Request.QueryString.Get("page");
			friendtype = base.Request.QueryString.Get("friendtype");
			backurl = base.Request.QueryString.Get("backurl");
			if (backurl == null || backurl == "")
			{
				backurl = base.Request.Form.Get("backurl");
			}
			if (backurl == null || backurl == "")
			{
				backurl = "myfile.aspx?siteid=" + siteid;
			}
			backurl = ToHtm(backurl);
			backurl = HttpUtility.UrlDecode(backurl);
			backurl = WapTool.URLtoWAP(backurl);
			IsLogin(userid, backurl);
			switch (action)
			{
				case "godelall":
					godelall();
					break;
				case "godel":
					godel();
					break;
			}
		}

		public void godel()
		{
			// ✅ 使用DapperHelper进行安全的参数化删除操作
			string connectionString = PubConstant.GetConnectionString(string_10);
			string deleteSql = "DELETE FROM wap_friends WHERE siteid = @SiteId AND userid = @UserId AND id = @FriendId";
			int rowsAffected = DapperHelper.Execute(connectionString, deleteSql, new {
				SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
				UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
				FriendId = DapperHelper.SafeParseLong(id, "好友ID")
			});

			// ✅ 删除成功后清除黑名单缓存
			if (rowsAffected > 0)
			{
				UserBlockingService.ClearUserBlockCache(userid);
			}

			INFO = "OK";
		}

		public void godelall()
		{
			// ✅ 使用DapperHelper进行安全的参数化删除操作
			string connectionString = PubConstant.GetConnectionString(string_10);
			string deleteSql = "DELETE FROM wap_friends WHERE siteid = @SiteId AND userid = @UserId AND friendtype = @FriendType";
			int rowsAffected = DapperHelper.Execute(connectionString, deleteSql, new {
				SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
				UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
				FriendType = DapperHelper.SafeParseLong(friendtype, "好友类型")
			});

			// ✅ 删除成功后清除黑名单缓存（如果删除的是黑名单）
			if (rowsAffected > 0 && friendtype == "1")
			{
				UserBlockingService.ClearUserBlockCache(userid);
			}

			INFO = "OK";
		}
	}
}