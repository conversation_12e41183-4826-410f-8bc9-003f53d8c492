/**
 * 筛选服务
 * 统一管理页面中的内容筛选功能
 * 支持多条件筛选、动态显示/隐藏、状态管理
 * 
 * @version 1.0
 * <AUTHOR>
 * @date 2025-01-07
 */

/**
 * 筛选条件接口
 */
export interface FilterCondition {
    key: string;
    value: string | string[];
    operator?: 'equals' | 'includes' | 'contains' | 'custom';
    customMatcher?: (itemValue: string, filterValue: string | string[]) => boolean;
}

/**
 * 筛选项配置接口
 */
export interface FilterItemConfig {
    selector: string;
    dataAttribute: string;
    showClass?: string;
    hideClass?: string;
    animationDuration?: number;
}

/**
 * 筛选器配置接口
 */
export interface FilterConfig {
    filterId: string;
    filterTabsSelector: string;
    itemsConfig: FilterItemConfig;
    activeClass: string;
    defaultFilter?: string;
    onFilterChange?: (activeFilter: string, visibleCount: number) => void;
}

/**
 * 筛选服务类
 * 提供统一的内容筛选、动态显示和状态管理功能
 */
export class FilterService {
    private static instance: FilterService;
    private activeFilters: Map<string, FilterConfig> = new Map();
    private currentFilters: Map<string, string> = new Map(); // filterId -> activeFilter

    /**
     * 获取单例实例
     */
    public static getInstance(): FilterService {
        if (!FilterService.instance) {
            FilterService.instance = new FilterService();
        }
        return FilterService.instance;
    }

    /**
     * 初始化筛选器
     * @param config 筛选器配置
     */
    public static initFilter(config: FilterConfig): void {
        FilterService.getInstance().initializeFilter(config);
    }

    /**
     * 应用筛选
     * @param filterId 筛选器ID
     * @param filterValue 筛选值
     */
    public static applyFilter(filterId: string, filterValue: string): void {
        FilterService.getInstance().performFilter(filterId, filterValue);
    }

    /**
     * 获取当前筛选值
     * @param filterId 筛选器ID
     */
    public static getCurrentFilter(filterId: string): string | undefined {
        return FilterService.getInstance().getCurrentFilterValue(filterId);
    }

    /**
     * 重置筛选器
     * @param filterId 筛选器ID
     */
    public static resetFilter(filterId: string): void {
        FilterService.getInstance().resetFilterToDefault(filterId);
    }

    /**
     * 初始化筛选器
     */
    public initializeFilter(config: FilterConfig): void {
        this.activeFilters.set(config.filterId, config);

        // 绑定筛选标签点击事件
        this.bindFilterEvents(config);

        // 设置初始筛选状态
        this.setInitialFilter(config);
    }

    /**
     * 执行筛选
     */
    public performFilter(filterId: string, filterValue: string): void {
        const config = this.activeFilters.get(filterId);
        if (!config) return;

        // 更新筛选标签状态
        this.updateFilterTabStyles(config, filterValue);

        // 执行内容筛选
        const visibleCount = this.filterItems(config, filterValue);

        // 更新当前状态
        this.currentFilters.set(filterId, filterValue);

        // 执行回调
        if (config.onFilterChange) {
            config.onFilterChange(filterValue, visibleCount);
        }
    }

    /**
     * 获取当前筛选值
     */
    public getCurrentFilterValue(filterId: string): string | undefined {
        return this.currentFilters.get(filterId);
    }

    /**
     * 重置筛选器到默认值
     */
    public resetFilterToDefault(filterId: string): void {
        const config = this.activeFilters.get(filterId);
        if (!config) return;

        const defaultValue = config.defaultFilter || 'all';
        this.performFilter(filterId, defaultValue);
    }

    /**
     * 绑定筛选事件
     */
    private bindFilterEvents(config: FilterConfig): void {
        const filterTabs = document.querySelectorAll(config.filterTabsSelector);
        
        filterTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                
                const target = e.currentTarget as HTMLElement;
                const filterValue = this.getFilterValueFromElement(target);
                
                if (filterValue) {
                    this.performFilter(config.filterId, filterValue);
                }
            });
        });
    }

    /**
     * 从元素获取筛选值
     */
    private getFilterValueFromElement(element: HTMLElement): string | undefined {
        // 尝试从各种data属性获取
        const dataAttributes = ['data-category', 'data-filter', 'data-value', 'data-type'];
        
        for (const attr of dataAttributes) {
            const value = element.getAttribute(attr);
            if (value) {
                return value;
            }
        }

        // 尝试从文本内容获取（转换为小写并去除空格）
        const textContent = element.textContent?.trim().toLowerCase();
        if (textContent) {
            // 常见的筛选值映射
            const filterMap: { [key: string]: string } = {
                '全部': 'all',
                '等级': 'level',
                '活跃': 'active',
                '特殊': 'special',
                '成就': 'achievement',
                '节日': 'festival'
            };
            
            return filterMap[textContent] || textContent;
        }

        return undefined;
    }

    /**
     * 设置初始筛选状态
     */
    private setInitialFilter(config: FilterConfig): void {
        const defaultValue = config.defaultFilter || 'all';
        this.performFilter(config.filterId, defaultValue);
    }

    /**
     * 更新筛选标签样式
     */
    private updateFilterTabStyles(config: FilterConfig, activeFilter: string): void {
        const filterTabs = document.querySelectorAll(config.filterTabsSelector);
        
        filterTabs.forEach(tab => {
            const element = tab as HTMLElement;
            element.classList.remove(config.activeClass);
            
            const tabValue = this.getFilterValueFromElement(element);
            if (tabValue === activeFilter) {
                element.classList.add(config.activeClass);
            }
        });
    }

    /**
     * 筛选项目
     */
    private filterItems(config: FilterConfig, filterValue: string): number {
        const items = document.querySelectorAll(config.itemsConfig.selector);
        let visibleCount = 0;

        items.forEach(item => {
            const element = item as HTMLElement;
            const shouldShow = this.shouldShowItem(element, config.itemsConfig, filterValue);
            
            if (shouldShow) {
                this.showItem(element, config.itemsConfig);
                visibleCount++;
            } else {
                this.hideItem(element, config.itemsConfig);
            }
        });

        return visibleCount;
    }

    /**
     * 判断项目是否应该显示
     */
    private shouldShowItem(element: HTMLElement, itemConfig: FilterItemConfig, filterValue: string): boolean {
        if (filterValue === 'all') {
            return true;
        }

        const itemCategories = element.getAttribute(itemConfig.dataAttribute);
        if (!itemCategories) {
            return false;
        }

        // 支持多个分类（空格分隔）
        const categories = itemCategories.split(' ').map(cat => cat.trim().toLowerCase());
        return categories.includes(filterValue.toLowerCase());
    }

    /**
     * 显示项目
     */
    private showItem(element: HTMLElement, itemConfig: FilterItemConfig): void {
        if (itemConfig.hideClass) {
            element.classList.remove(itemConfig.hideClass);
        }
        
        if (itemConfig.showClass) {
            element.classList.add(itemConfig.showClass);
        } else {
            element.style.display = 'flex'; // 默认显示方式
        }
    }

    /**
     * 隐藏项目
     */
    private hideItem(element: HTMLElement, itemConfig: FilterItemConfig): void {
        if (itemConfig.showClass) {
            element.classList.remove(itemConfig.showClass);
        }
        
        if (itemConfig.hideClass) {
            element.classList.add(itemConfig.hideClass);
        } else {
            element.style.display = 'none'; // 默认隐藏方式
        }
    }

    /**
     * 获取筛选统计信息
     */
    public getFilterStats(filterId: string): { total: number; visible: number; hidden: number } {
        const config = this.activeFilters.get(filterId);
        if (!config) {
            return { total: 0, visible: 0, hidden: 0 };
        }

        const items = document.querySelectorAll(config.itemsConfig.selector);
        let visible = 0;
        let hidden = 0;

        items.forEach(item => {
            const element = item as HTMLElement;
            const isVisible = element.style.display !== 'none' && 
                             !element.classList.contains(config.itemsConfig.hideClass || '');
            
            if (isVisible) {
                visible++;
            } else {
                hidden++;
            }
        });

        return {
            total: items.length,
            visible,
            hidden
        };
    }
}

// ==================== 全局函数，供模板调用 ====================

/**
 * 初始化筛选器（简化接口）
 * @param config 筛选器配置
 */
export function initFilter(config: FilterConfig): void {
    FilterService.initFilter(config);
}

/**
 * 应用筛选（简化接口）
 * @param filterId 筛选器ID
 * @param filterValue 筛选值
 */
export function applyFilter(filterId: string, filterValue: string): void {
    FilterService.applyFilter(filterId, filterValue);
}

/**
 * 重置筛选（简化接口）
 * @param filterId 筛选器ID
 */
export function resetFilter(filterId: string): void {
    FilterService.resetFilter(filterId);
}

// 导出默认实例
export default FilterService;
