/**
 * 分页服务
 * 统一管理所有页面的分页导航功能
 * 消除重复代码，提供一致的分页体验
 * 
 * @version 1.0
 * <AUTHOR>
 * @date 2025-01-07
 */

import { PaginationConfig } from '../types/CommonTypes.js';

/**
 * 分页服务类
 * 提供统一的分页导航、页面跳转和URL管理功能
 */
export class PaginationService {
    private static instance: PaginationService;

    /**
     * 获取单例实例
     */
    public static getInstance(): PaginationService {
        if (!PaginationService.instance) {
            PaginationService.instance = new PaginationService();
        }
        return PaginationService.instance;
    }

    /**
     * 初始化分页功能
     * @param config 分页配置
     */
    public static init(config: PaginationConfig): void {
        PaginationService.getInstance().initializePagination(config);
    }

    /**
     * 导航到指定页面
     * @param page 页码
     * @param baseUrl 基础URL（可选）
     * @param pageParam 页码参数名（可选）
     */
    public static navigateToPage(page: number, baseUrl?: string, pageParam: string = 'page'): void {
        PaginationService.getInstance().navigateTo(page, baseUrl, pageParam);
    }

    /**
     * 获取当前页码
     */
    public static getCurrentPage(): number {
        return PaginationService.getInstance().getCurrentPageFromUrl();
    }

    /**
     * 初始化分页功能
     */
    public initializePagination(config: PaginationConfig): void {
        this.bindPaginationEvents(config);
    }

    /**
     * 绑定分页事件
     */
    private bindPaginationEvents(config: PaginationConfig): void {
        // 绑定上一页按钮
        const prevBtn = document.getElementById('prevPageBtn') as HTMLButtonElement;
        if (prevBtn && !prevBtn.disabled) {
            prevBtn.addEventListener('click', () => {
                if (config.currentPage > 1) {
                    this.navigateTo(config.currentPage - 1, config.baseUrl, config.pageParam);
                }
            });
        }

        // 绑定下一页按钮
        const nextBtn = document.getElementById('nextPageBtn') as HTMLButtonElement;
        if (nextBtn && !nextBtn.disabled) {
            nextBtn.addEventListener('click', () => {
                if (config.currentPage < config.totalPages) {
                    this.navigateTo(config.currentPage + 1, config.baseUrl, config.pageParam);
                }
            });
        }

        // 绑定首页按钮（如果存在）
        if (config.showFirstLast) {
            const firstBtn = document.getElementById('firstPageBtn') as HTMLButtonElement;
            if (firstBtn && !firstBtn.disabled) {
                firstBtn.addEventListener('click', () => {
                    this.navigateTo(1, config.baseUrl, config.pageParam);
                });
            }

            // 绑定末页按钮
            const lastBtn = document.getElementById('lastPageBtn') as HTMLButtonElement;
            if (lastBtn && !lastBtn.disabled) {
                lastBtn.addEventListener('click', () => {
                    this.navigateTo(config.totalPages, config.baseUrl, config.pageParam);
                });
            }
        }

        // 绑定页码输入框（如果存在）
        const pageInput = document.getElementById('pageInput') as HTMLInputElement;
        if (pageInput) {
            pageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const page = parseInt(pageInput.value);
                    if (page >= 1 && page <= config.totalPages) {
                        this.navigateTo(page, config.baseUrl, config.pageParam);
                    }
                }
            });
        }

        // 绑定页码跳转按钮（如果存在）
        const goBtn = document.getElementById('goPageBtn') as HTMLButtonElement;
        if (goBtn && pageInput) {
            goBtn.addEventListener('click', () => {
                const page = parseInt(pageInput.value);
                if (page >= 1 && page <= config.totalPages) {
                    this.navigateTo(page, config.baseUrl, config.pageParam);
                }
            });
        }
    }

    /**
     * 导航到指定页面
     */
    public navigateTo(page: number, baseUrl?: string, pageParam: string = 'page'): void {
        const url = this.buildPageUrl(page, baseUrl, pageParam);
        window.location.href = url;
    }

    /**
     * 构建页面URL
     */
    private buildPageUrl(page: number, baseUrl?: string, pageParam: string = 'page'): string {
        const currentUrl = new URL(baseUrl || window.location.href);
        
        if (page === 1) {
            // 第一页时移除页码参数
            currentUrl.searchParams.delete(pageParam);
        } else {
            // 设置页码参数
            currentUrl.searchParams.set(pageParam, page.toString());
        }
        
        return currentUrl.toString();
    }

    /**
     * 从URL获取当前页码
     */
    public getCurrentPageFromUrl(pageParam: string = 'page'): number {
        const urlParams = new URLSearchParams(window.location.search);
        const page = urlParams.get(pageParam);
        return page ? parseInt(page) : 1;
    }

    /**
     * 创建分页导航HTML
     */
    public createPaginationHtml(config: PaginationConfig): string {
        const { currentPage, totalPages, showFirstLast, showPrevNext } = config;
        
        let html = '<div class="flex items-center justify-center gap-4 mt-4">';

        // 首页按钮
        if (showFirstLast && currentPage > 1) {
            html += `<button class="pagination-btn" id="firstPageBtn" ${currentPage === 1 ? 'disabled' : ''}>
                        <i data-lucide="chevrons-left" class="w-5 h-5"></i>
                     </button>`;
        }

        // 上一页按钮
        if (showPrevNext !== false) {
            html += `<button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed" 
                            id="prevPageBtn" ${currentPage === 1 ? 'disabled' : ''}>
                        <i data-lucide="chevron-left" class="w-5 h-5"></i>
                     </button>`;
        }

        // 页码信息
        html += `<div class="flex-1 text-center text-sm text-text-secondary px-2">
                    第 ${currentPage} / ${totalPages} 页
                 </div>`;

        // 下一页按钮
        if (showPrevNext !== false) {
            html += `<button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed" 
                            id="nextPageBtn" ${currentPage === totalPages ? 'disabled' : ''}>
                        <i data-lucide="chevron-right" class="w-5 h-5"></i>
                     </button>`;
        }

        // 末页按钮
        if (showFirstLast && currentPage < totalPages) {
            html += `<button class="pagination-btn" id="lastPageBtn" ${currentPage === totalPages ? 'disabled' : ''}>
                        <i data-lucide="chevrons-right" class="w-5 h-5"></i>
                     </button>`;
        }

        html += '</div>';
        return html;
    }

    /**
     * 动态插入分页导航
     */
    public insertPagination(containerId: string, config: PaginationConfig): void {
        const container = document.getElementById(containerId);
        if (!container) return;

        container.innerHTML = this.createPaginationHtml(config);
        this.bindPaginationEvents(config);

        // 初始化Lucide图标（如果可用）
        if (typeof (window as any).lucide !== 'undefined') {
            (window as any).lucide.createIcons();
        }
    }
}

// ==================== 全局函数，供模板调用 ====================

/**
 * 初始化分页功能（向后兼容）
 */
export function initPagination(): void {
    // 从页面元素中读取分页配置
    const currentPageElement = document.querySelector('[data-current-page]') as HTMLElement;
    const totalPagesElement = document.querySelector('[data-total-pages]') as HTMLElement;
    
    if (currentPageElement && totalPagesElement) {
        const config: PaginationConfig = {
            currentPage: parseInt(currentPageElement.dataset.currentPage || '1'),
            totalPages: parseInt(totalPagesElement.dataset.totalPages || '1'),
            baseUrl: window.location.href,
            pageParam: 'page',
            showPrevNext: true
        };
        
        PaginationService.init(config);
    }
}

/**
 * 导航到指定页面（向后兼容）
 * @param page 页码
 */
export function navigateToPage(page: number): void {
    PaginationService.navigateToPage(page);
}

/**
 * 获取当前页码（向后兼容）
 */
export function getCurrentPage(): number {
    return PaginationService.getCurrentPage();
}

// 导出默认实例
export default PaginationService;
