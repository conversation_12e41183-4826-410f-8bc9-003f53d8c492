﻿using System;
using KeLin.ClassManager;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class ToBBSType : MyPageWap
    {
        private readonly string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string page = "";

        public string INFO = "";

        public string ERROR = "";

        public string top = "";

        public string down = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            page = GetRequestValue("page");
            CheckManagerLvl("04", classVo.adminusername, "bbs/showadmin.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + page);
            if (classVo.userid.ToString() != siteid)
            {
                base.Response.End();
            }
            if (classVo.classid.ToString() != classid)
            {
                base.Response.End();
            }
            if (classVo.bbsType.IndexOf('_') > 0)
            {
                top = classVo.bbsType.Split('_')[0];
                down = classVo.bbsType.Split('_')[1];
            }
            if (!(action == "gomod"))
            {
                return;
            }
            try
            {
                string requestValue = GetRequestValue("chkall");
                string strWhere = "";
                top = GetRequestValue("top");
                down = GetRequestValue("down");
                top = top.Replace("_", "*");
                down = down.Replace("_", "*");
                if (requestValue == "yes" && (userVo.managerlvl == "00" || userVo.managerlvl == "01"))
                {
                    strWhere = "bbs/index.aspx";
                }
                // ✅ 使用DapperHelper安全更新版块类型，避免SQL注入
                UpdateBbsTypeSafely(top + "_" + down, strWhere);
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }

        /// <summary>
        /// 使用DapperHelper安全更新版块类型，避免SQL注入
        /// </summary>
        /// <param name="bbsType">版块类型</param>
        /// <param name="strWhere">更新条件</param>
        private void UpdateBbsTypeSafely(string bbsType, string strWhere)
        {
            string bbsTypeConnectionString = PubConstant.GetConnectionString(a);
            string sql = "UPDATE [class] SET bbsType = @BbsType WHERE siteid = @SiteId AND classid = @ClassId";

            DapperHelper.Execute(bbsTypeConnectionString, sql, new {
                BbsType = DapperHelper.LimitLength(bbsType ?? "", 255),
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                ClassId = DapperHelper.SafeParseLong(classid, "版块ID")
            });
        }
    }
}