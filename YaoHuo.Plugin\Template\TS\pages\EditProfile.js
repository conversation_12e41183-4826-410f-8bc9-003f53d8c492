import { FormValidationService } from '../services/FormValidationService.js';
import { NavigationService } from '../services/NavigationService.js';
export class EditProfilePage {
    constructor() {
        this.formValidationService = FormValidationService.getInstance();
        this.navigationService = NavigationService.getInstance();
    }
    init() {
        this.setupFormValidation();
        this.setupNavigationService();
        this.setupExpandMoreFields();
        this.initializeLucideIcons();
    }
    setupFormValidation() {
        const form = document.getElementById('profile-form');
        if (!form) {
            console.warn('Profile form not found');
            return;
        }
        const config = FormValidationService.createProfileValidationConfig();
        this.formValidationService.registerForm('profile-form', config);
        this.formValidationService.initializeForm(form);
        this.setupFormSubmit(form);
    }
    setupFormSubmit(form) {
        form.addEventListener('submit', (e) => {
            if (!this.formValidationService.validateForm(form)) {
                e.preventDefault();
                return;
            }
            const submitButton = form.querySelector('.form-submit');
            if (submitButton) {
                submitButton.innerHTML = '<i data-lucide="loader-2" class="icon animate-spin"></i>保存中...';
                submitButton.disabled = true;
                setTimeout(() => {
                    this.initializeLucideIcons();
                }, 10);
            }
        });
    }
    setupNavigationService() {
        this.navigationService.initializeBackButton('#back-button');
    }
    setupExpandMoreFields() {
        const expandBtn = document.getElementById('expand-more-btn');
        const moreFields = document.getElementById('more-fields');
        const expandBtnText = expandBtn?.querySelector('.expand-btn-text');
        let isExpanded = false;
        if (expandBtn && moreFields && expandBtnText) {
            expandBtn.addEventListener('click', () => {
                isExpanded = !isExpanded;
                if (isExpanded) {
                    moreFields.classList.remove('hidden');
                    expandBtn.classList.add('expanded');
                    expandBtnText.textContent = '收起更多';
                    moreFields.classList.add('expanding');
                    setTimeout(() => {
                        moreFields.classList.remove('expanding');
                        this.scrollToElementIfNeeded(moreFields);
                    }, 200);
                }
                else {
                    expandBtn.classList.remove('expanded');
                    expandBtnText.textContent = '展开更多';
                    moreFields.classList.add('collapsing');
                    setTimeout(() => {
                        moreFields.classList.add('hidden');
                        moreFields.classList.remove('collapsing');
                    }, 200);
                }
            });
        }
    }
    scrollToElementIfNeeded(element) {
        const rect = element.getBoundingClientRect();
        const isVisible = rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth);
        if (!isVisible) {
            const headerHeight = 60;
            const scrollPosition = element.offsetTop - headerHeight;
            window.scrollTo({
                top: scrollPosition,
                behavior: 'smooth'
            });
        }
    }
    initializeLucideIcons() {
        if (typeof window.lucide !== 'undefined') {
            window.lucide.createIcons();
        }
    }
    validateField(fieldName) {
        const form = document.getElementById('profile-form');
        if (!form)
            return false;
        const fieldElement = form.querySelector(`[name="${fieldName}"]`);
        if (!fieldElement)
            return false;
        const config = FormValidationService.createProfileValidationConfig();
        const fieldConfig = config.fields[fieldName];
        if (!fieldConfig)
            return false;
        const result = this.formValidationService.validateField(fieldElement, fieldConfig);
        return result.isValid;
    }
    clearFieldError(fieldName) {
        const form = document.getElementById('profile-form');
        if (!form)
            return;
        const fieldElement = form.querySelector(`[name="${fieldName}"]`);
        if (!fieldElement)
            return;
        fieldElement.classList.remove('border-red-500', 'error', 'border-danger');
        const parent = fieldElement.parentNode;
        if (parent) {
            const existingError = parent.querySelector('.form-error, .text-xs.text-red-500');
            if (existingError) {
                existingError.remove();
            }
        }
    }
    getFormData() {
        const form = document.getElementById('profile-form');
        if (!form)
            return {};
        const formData = new FormData(form);
        const data = {};
        formData.forEach((value, key) => {
            data[key] = value.toString();
        });
        return data;
    }
    setFormData(data) {
        const form = document.getElementById('profile-form');
        if (!form)
            return;
        Object.entries(data).forEach(([key, value]) => {
            const fieldElement = form.querySelector(`[name="${key}"]`);
            if (fieldElement) {
                fieldElement.value = value;
            }
        });
    }
    resetForm() {
        const form = document.getElementById('profile-form');
        if (form) {
            form.reset();
            const errorElements = form.querySelectorAll('.form-error, .text-xs.text-red-500');
            errorElements.forEach(element => element.remove());
            const inputElements = form.querySelectorAll('.form-input');
            inputElements.forEach(element => {
                element.classList.remove('border-red-500', 'error', 'border-danger');
            });
        }
    }
    hasFormChanged() {
        const form = document.getElementById('profile-form');
        if (!form)
            return false;
        const inputs = form.querySelectorAll('input, textarea, select');
        for (let i = 0; i < inputs.length; i++) {
            const input = inputs[i];
            const element = input;
            if (element.type === 'checkbox' || element.type === 'radio') {
                if (element.checked !== element.defaultChecked) {
                    return true;
                }
            }
            else {
                if (element.value !== element.defaultValue) {
                    return true;
                }
            }
        }
        return false;
    }
    showSaveSuccess(message = '保存成功') {
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        toast.textContent = message;
        document.body.appendChild(toast);
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
    showSaveError(message = '保存失败') {
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        toast.textContent = message;
        document.body.appendChild(toast);
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}
document.addEventListener('DOMContentLoaded', () => {
    const editProfilePage = new EditProfilePage();
    editProfilePage.init();
    window.editProfilePage = editProfilePage;
});
export default EditProfilePage;
