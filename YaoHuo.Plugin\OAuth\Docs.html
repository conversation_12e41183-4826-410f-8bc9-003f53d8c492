<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YaoHuo 账号登录文档</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="/NetCSS/JS/BBS/Lucide.0.511.0.min.js"></script>
    
    <!-- Prism.js 代码高亮 -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/prism/1.27.0/themes/prism.min.css" rel="stylesheet">
    <script src="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/prism/1.27.0/components/prism-core.min.js"></script>
    <script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/prism/1.27.0/plugins/autoloader/prism-autoloader.min.js"></script>
    
    <style>
        .docs-nav {
            position: sticky;
            top: 80px;
            max-height: calc(100vh - 100px);
            overflow-y: auto;
        }
        
        .code-block {
            position: relative;
        }
        
        .copy-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0,0,0,0.7);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s;
        }
        
        .code-block:hover .copy-btn {
            opacity: 1;
        }
        
        .copy-btn:hover {
            background: rgba(0,0,0,0.9);
        }
        
        .nav-link {
            display: block;
            padding: 8px 16px;
            color: #6b7280;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.2s ease;
            cursor: pointer;
            user-select: none;
        }

        .nav-link:hover {
            background: #f3f4f6;
            color: #374151;
            transform: translateX(2px);
        }

        .nav-link:active {
            background: #e5e7eb;
            transform: translateX(1px);
        }

        .nav-link.active {
            background: #dbeafe;
            color: #1d4ed8;
            font-weight: 500;
            border-left: 3px solid #3b82f6;
            padding-left: 13px;
        }

        .nav-link.active:hover {
            background: #bfdbfe;
            transform: none;
        }

        /* 确保section标题不被遮挡 */
        section[id] {
            scroll-margin-top: 80px;
        }

        /* 为section标题添加适当的上边距 */
        section[id] h2 {
            padding-top: 20px;
        }

        /* 统一修正所有section标题的对齐问题 */
        section .flex.items-center.space-x-3.mb-6,
        section .section-header {
            display: flex !important;
            align-items: center !important;
            gap: 12px !important;
            margin-bottom: 24px !important;
        }

        section .flex.items-center.space-x-3.mb-6 i,
        section .section-header i {
            width: 24px !important;
            height: 24px !important;
            flex-shrink: 0 !important;
            margin-top: 0 !important;
        }

        section .flex.items-center.space-x-3.mb-6 h1,
        section .section-header h1 {
            margin: 0 !important;
            padding: 0 !important;
            line-height: 1.5 !important;
            display: flex !important;
            align-items: center !important;
            font-size: 1.5rem !important;
            font-weight: 700 !important;
            color: #111827 !important;
        }

        section .flex.items-center.space-x-3.mb-6 h2,
        section .section-header h2 {
            margin: 0 !important;
            padding: 0 !important;
            line-height: 1.5 !important;
            display: flex !important;
            align-items: center !important;
            font-size: 1.25rem !important;
            font-weight: 700 !important;
            color: #111827 !important;
        }
        
        .endpoint-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            margin-right: 8px;
        }
        
        .endpoint-get {
            background: #dcfdf7;
            color: #065f46;
        }
        
        .endpoint-post {
            background: #fef3c7;
            color: #92400e;
        }
        
        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
        }
        
        .param-table th,
        .param-table td {
            border: 1px solid #e5e7eb;
            padding: 12px;
            text-align: left;
        }
        
        .param-table th {
            background: #f9fafb;
            font-weight: 600;
        }
        
        .param-required {
            color: #dc2626;
            font-weight: 600;
        }
        
        .param-optional {
            color: #6b7280;
        }
        
        .step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: #3b82f6;
            color: white;
            border-radius: 50%;
            font-size: 14px;
            font-weight: 600;
            margin-right: 12px;
        }
        
        .flow-diagram {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 24px;
            margin: 24px 0;
        }
        
        .flow-step {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding: 12px;
            background: white;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .flow-step:last-child {
            margin-bottom: 0;
        }
        
        .flow-arrow {
            text-align: center;
            color: #6b7280;
            margin: 8px 0;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <!-- 固定顶部导航栏 -->
        <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center space-x-4 ml-1">
                        <h1 class="text-xl font-semibold text-gray-900">YaoHuo 账号登录文档</h1>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button onclick="window.open('https://tools.ietf.org/html/rfc6749', '_blank')" 
                                class="p-2 rounded-lg hover:bg-gray-100" title="OAuth 2.0 RFC">
                            <i data-lucide="external-link" class="w-5 h-5"></i>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                <!-- 左侧导航 -->
                <div class="lg:col-span-1">
                    <nav class="docs-nav bg-white rounded-lg border p-4">
                        <div class="space-y-6">
                            <div>
                                <div class="text-sm font-semibold text-gray-900 mb-2">🚀 开始使用</div>
                                <div class="space-y-1">
                                    <a href="#quickstart" class="nav-link">3步集成</a>
                                    <a href="#api" class="nav-link">API 参考</a>
                                    <a href="#examples" class="nav-link">完整示例</a>
                                    <a href="#faq" class="nav-link">常见问题</a>
                                </div>
                            </div>
                        </div>
                    </nav>
                </div>

                <!-- 右侧内容 -->
                <div class="lg:col-span-3">
                    <div class="space-y-8">

                        <!-- 5分钟集成 -->
                        <section id="quickstart" class="bg-white rounded-lg border p-6">
                            <div class="section-header">
                                <h2>⚡ 3步集成</h2>
                            </div>
                            
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                                <div class="flex items-start space-x-2">
                                    <i data-lucide="check-circle" class="w-5 h-5 text-green-600 mt-0.5"></i>
                                    <div>
                                        <p class="text-sm text-green-800">
                                            <strong>开始前请先联系管理员获取：</strong>Client ID 和 Client Secret
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="space-y-6">
                                <div class="flex items-start space-x-4">
                                    <div class="flex-shrink-0 w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                                    <div class="flex-1">
                                        <h4 class="font-medium mb-2">前端：跳转到授权页面</h4>
                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
                                            <pre><code class="language-javascript">window.location.href = 'https://yaohuo.me/OAuth/Authorize.aspx?' +
    'response_type=code&' +
    'client_id=YOUR_CLIENT_ID&' +
    'redirect_uri=https://yourapp.com/callback&' +
    'scope=profile';</code></pre>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex items-start space-x-4">
                                    <div class="flex-shrink-0 w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                                    <div class="flex-1">
                                        <h4 class="font-medium mb-2">后端：用授权码换取令牌</h4>
                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
                                            <pre><code class="language-javascript">const response = await fetch('https://yaohuo.me/OAuth/Token.aspx', {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams({
        grant_type: 'authorization_code',
        code: 'YOUR_AUTH_CODE',
        client_id: 'YOUR_CLIENT_ID',
        client_secret: 'YOUR_CLIENT_SECRET',
        redirect_uri: 'https://yourapp.com/callback'
    })
});

const data = await response.json();</code></pre>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex items-start space-x-4">
                                    <div class="flex-shrink-0 w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                                    <div class="flex-1">
                                        <h4 class="font-medium mb-2">获取用户信息（已包含在令牌响应中）</h4>
                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyCode(this)">复制</button>
                                            <pre><code class="language-javascript">const userInfo = data.user_info;
console.log(userInfo);
// { userid: 12345, nickname: "张三", level: 8 }</code></pre>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div class="flex items-start space-x-2">
                                        <i data-lucide="info" class="w-5 h-5 text-blue-600 mt-0.5"></i>
                                        <div>
                                            <p class="text-sm text-blue-800">
                                                <strong>就这么简单！</strong> 用户信息直接包含在令牌响应中，无需额外API调用。
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>





                        <!-- API 参考 -->
                        <section id="api" class="bg-white rounded-lg border p-6">
                            <div class="section-header">
                                <h2>📖 API 参考</h2>
                            </div>

                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- 授权端点 -->
                                <div class="border rounded-lg p-4 bg-blue-50">
                                    <div class="mb-3">
                                        <span class="endpoint-badge endpoint-get">GET</span>
                                        <code class="text-sm">/OAuth/Authorize.aspx</code>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-3">🔗 跳转到这里让用户授权</p>
                                    <div class="text-xs space-y-1">
                                        <div><code>client_id</code> = 您的客户端ID</div>
                                        <div><code>redirect_uri</code> = 回调地址</div>
                                        <div><code>response_type</code> = code</div>
                                    </div>
                                </div>

                                <!-- 令牌端点 -->
                                <div class="border rounded-lg p-4 bg-green-50">
                                    <div class="mb-3">
                                        <span class="endpoint-badge endpoint-post">POST</span>
                                        <code class="text-sm">/OAuth/Token.aspx</code>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-3">🎫 在后端用授权码换令牌</p>
                                    <div class="text-xs space-y-1">
                                        <div><code>code</code> = 授权码</div>
                                        <div><code>client_id + client_secret</code> = 您的密钥</div>
                                        <div><code>grant_type</code> = authorization_code</div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-6">
                                <h3 class="text-lg font-semibold mb-3">Token 响应示例</h3>
                                <div class="code-block">
                                    <button class="copy-btn" onclick="copyCode(this)">复制</button>
                                    <pre><code class="language-json">{
  "access_token": "tok_1234567890abcdef",
  "token_type": "Bearer",
  "expires_in": 86400,
  "user_info": {
    "userid": 12345,
    "nickname": "张三",
    "level": 8
  }
}</code></pre>
                                </div>
                            </div>
                        </section>

                        <!-- 代码示例 -->
                        <section id="examples" class="bg-white rounded-lg border p-6">
                            <div class="section-header">
                                <h2>💻 完整示例</h2>
                            </div>

                            <div class="grid grid-cols-1 gap-6">
                                <!-- Node.js -->
                                <div>
                                    <h3 class="text-lg font-semibold mb-3">🟢 Node.js</h3>
                                    <div class="code-block">
                                        <button class="copy-btn" onclick="copyCode(this)">复制</button>
                                        <pre><code class="language-javascript">// 后端 API
app.post('/oauth/exchange', async (req, res) => {
    const { code } = req.body;
    
    const response = await fetch('https://yaohuo.me/OAuth/Token.aspx', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
            grant_type: 'authorization_code',
            code,
            client_id: process.env.CLIENT_ID,
            client_secret: process.env.CLIENT_SECRET,
            redirect_uri: 'https://yourapp.com/callback'
        })
    });
    
    const data = await response.json();
    req.session.user = data.user_info;
    res.json({ success: true, user: data.user_info });
});</code></pre>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mt-6">
                                <div class="flex items-start space-x-2">
                                    <i data-lucide="check-circle" class="w-5 h-5 text-green-600 mt-0.5"></i>
                                    <div>
                                        <p class="text-sm text-green-800">
                                            <strong>记住：</strong>密钥放服务器，HTTPS必须用，用户信息直接拿！
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </section>



                                                <!-- 常见问题 -->
                        <section id="faq" class="bg-white rounded-lg border p-6">
                            <div class="section-header">
                                <h2>❓ 常见问题</h2>
                            </div>

                            <div class="space-y-4">
                                <div class="border-l-4 border-blue-500 pl-4">
                                    <h4 class="font-semibold mb-1">如何获取Client ID和Secret？</h4>
                                    <p class="text-gray-600 text-sm">联系管理员注册您的应用，提供应用名称、描述和回调地址即可。</p>
                                </div>

                                <div class="border-l-4 border-blue-500 pl-4">
                                    <h4 class="font-semibold mb-1">令牌有效期多长？</h4>
                                    <p class="text-gray-600 text-sm">默认24小时。过期后需要重新授权。</p>
                                </div>

                                <div class="border-l-4 border-blue-500 pl-4">
                                    <h4 class="font-semibold mb-1">能获取哪些用户信息？</h4>
                                    <p class="text-gray-600 text-sm">用户ID、昵称、等级信息，直接包含在令牌响应中。</p>
                                </div>

                                <div class="border-l-4 border-blue-500 pl-4">
                                    <h4 class="font-semibold mb-1">支持刷新令牌吗？</h4>
                                    <p class="text-gray-600 text-sm">当前版本暂不支持，令牌过期后需要重新授权。</p>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('OAuth 文档页面加载完成');

            // 初始化 Lucide 图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
                console.log('Lucide 图标初始化完成');
            } else {
                console.warn('Lucide 图标库未加载');
            }

            // 检查导航链接数量
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('section[id]');
            console.log(`找到 ${navLinks.length} 个导航链接，${sections.length} 个内容区块`);

            // 导航高亮
            updateActiveNav();

            // 监听滚动事件
            window.addEventListener('scroll', updateActiveNav);

            // 平滑滚动
            navLinks.forEach((link, index) => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);

                    console.log(`点击导航链接 ${index + 1}: ${targetId}`);

                    if (targetElement) {
                        console.log(`找到目标元素，开始滚动到: ${targetId}`);

                        // 计算目标位置，考虑固定头部的高度
                        const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - 80;

                        window.scrollTo({
                            top: targetPosition,
                            behavior: 'smooth'
                        });

                        // 手动更新高亮状态
                        setTimeout(() => {
                            updateActiveNav();
                        }, 100);
                    } else {
                        console.error(`未找到目标元素: ${targetId}`);
                    }
                });
            });

            // 添加点击效果
            navLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('active')) {
                        this.style.backgroundColor = '#f3f4f6';
                    }
                });

                link.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.backgroundColor = '';
                    }
                });
            });
        });

        // 更新导航高亮
        function updateActiveNav() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link');

            let currentSection = '';
            const scrollOffset = 120; // 考虑固定头部高度的偏移量

            // 检查是否滚动到了页面底部
            const isAtBottom = (window.innerHeight + window.scrollY) >= (document.body.offsetHeight - 50);
            
            if (isAtBottom && sections.length > 0) {
                // 如果在页面底部，选择最后一个section
                currentSection = sections[sections.length - 1].id;
            } else {
                // 正常情况下，检测当前可见的section
                sections.forEach(section => {
                    const rect = section.getBoundingClientRect();
                    // 调整检测区域，使其与滚动偏移量一致
                    if (rect.top <= scrollOffset && rect.bottom >= scrollOffset) {
                        currentSection = section.id;
                    }
                });

                // 如果没有找到当前section，选择最接近顶部的section
                if (!currentSection && sections.length > 0) {
                    let closestSection = null;
                    let closestDistance = Infinity;

                    sections.forEach(section => {
                        const rect = section.getBoundingClientRect();
                        const distance = Math.abs(rect.top - scrollOffset);
                        if (distance < closestDistance) {
                            closestDistance = distance;
                            closestSection = section.id;
                        }
                    });

                    currentSection = closestSection;
                }
            }

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + currentSection) {
                    link.classList.add('active');
                }
            });
        }

        // 复制代码功能
        function copyCode(button) {
            const codeBlock = button.parentElement.querySelector('code');
            const text = codeBlock.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.textContent;
                button.textContent = '已复制';
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            }).catch(() => {
                // 兼容旧浏览器
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                const originalText = button.textContent;
                button.textContent = '已复制';
                setTimeout(() => {
                    button.textContent = originalText;
                }, 2000);
            });
        }
    </script>
</body>
</html>