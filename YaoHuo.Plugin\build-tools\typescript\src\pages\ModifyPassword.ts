import { FormValidationService } from '../services/FormValidationService.js';
import { NavigationService } from '../services/NavigationService.js';

/**
 * 密码修改页面
 * 应用FormValidationService进行实时密码强度验证
 */
export class ModifyPasswordPage {
    private formValidationService: FormValidationService;
    private navigationService: NavigationService;
    private ruleElements: {
        length?: HTMLElement;
        uppercase?: HTMLElement;
        lowercase?: HTMLElement;
        number?: HTMLElement;
        match?: HTMLElement;
    } = {};

    constructor() {
        this.formValidationService = FormValidationService.getInstance();
        this.navigationService = NavigationService.getInstance();
    }

    /**
     * 初始化页面
     */
    public init(): void {
        // 检查是否是成功页面（有倒计时元素）
        const countdownElement = document.getElementById('countdown-text');
        if (countdownElement) {
            // 成功页面，启动倒计时
            this.startCountdown();
            this.initializeLucideIcons();
            return;
        }

        // 正常的密码修改页面
        this.setupFormValidation();
        this.setupPasswordStrengthValidation();
        this.setupNavigationService();
        this.initializeLucideIcons();
    }

    /**
     * 设置表单验证
     */
    private setupFormValidation(): void {
        const form = document.getElementById('password-form') as HTMLFormElement;
        if (!form) {
            console.warn('Password form not found');
            return;
        }

        // 注册表单验证配置
        const config = FormValidationService.createPasswordValidationConfig();
        this.formValidationService.registerForm('password-form', config);

        // 初始化表单验证
        this.formValidationService.initializeForm(form);

        // 设置表单提交处理
        this.setupFormSubmit(form);
    }

    /**
     * 设置密码强度验证
     */
    private setupPasswordStrengthValidation(): void {
        // 获取密码强度规则元素
        this.ruleElements = {
            length: document.getElementById('rule-length') || undefined,
            uppercase: document.getElementById('rule-uppercase') || undefined,
            lowercase: document.getElementById('rule-lowercase') || undefined,
            number: document.getElementById('rule-number') || undefined,
            match: document.getElementById('rule-match') || undefined
        };

        // 获取密码输入框
        const newPasswordInput = document.querySelector('[name="txtnewPW"]') as HTMLInputElement;
        const confirmPasswordInput = document.querySelector('[name="txtrePW"]') as HTMLInputElement;

        if (newPasswordInput && confirmPasswordInput) {
            // 实时验证密码强度
            newPasswordInput.addEventListener('input', () => {
                this.updatePasswordRules(newPasswordInput.value, confirmPasswordInput.value);
            });

            confirmPasswordInput.addEventListener('input', () => {
                this.updatePasswordRules(newPasswordInput.value, confirmPasswordInput.value);
            });
        }
    }

    /**
     * 更新密码强度规则显示
     */
    private updatePasswordRules(newPassword: string, confirmPassword: string): void {
        this.formValidationService.updatePasswordRules(newPassword, confirmPassword, this.ruleElements);
    }

    /**
     * 设置表单提交处理
     */
    private setupFormSubmit(form: HTMLFormElement): void {
        form.addEventListener('submit', (e) => {
            // 验证表单
            if (!this.formValidationService.validateForm(form)) {
                e.preventDefault();
                return;
            }

            // 额外的密码强度检查
            const newPasswordInput = form.querySelector('[name="txtnewPW"]') as HTMLInputElement;
            const oldPasswordInput = form.querySelector('[name="txtoldPW"]') as HTMLInputElement;

            if (newPasswordInput && oldPasswordInput) {
                // 检查新旧密码是否相同
                if (newPasswordInput.value === oldPasswordInput.value) {
                    e.preventDefault();
                    this.showPasswordError('新旧密码不能相同');
                    return;
                }

                // 检查密码强度
                const strength = this.formValidationService.validatePasswordStrength(newPasswordInput.value);
                if (!strength.hasLength || !strength.hasUppercase || !strength.hasLowercase || !strength.hasNumber) {
                    e.preventDefault();
                    this.showPasswordError('密码强度不符合要求');
                    return;
                }
            }

            // 显示提交状态
            const submitButton = form.querySelector('.form-submit') as HTMLButtonElement;
            if (submitButton) {
                submitButton.innerHTML = '<i data-lucide="loader-2" class="w-5 h-5 animate-spin"></i>修改中...';
                submitButton.disabled = true;
                
                // 重新创建图标
                setTimeout(() => {
                    this.initializeLucideIcons();
                }, 10);
            }
        });
    }

    /**
     * 设置导航服务
     */
    private setupNavigationService(): void {
        // 初始化返回按钮（如果页面有的话）
        this.navigationService.initializeBackButton('#back-button');
    }

    /**
     * 初始化Lucide图标
     */
    private initializeLucideIcons(): void {
        if (typeof (window as any).lucide !== 'undefined') {
            (window as any).lucide.createIcons();
        }
    }

    /**
     * 显示密码错误提示
     */
    private showPasswordError(message: string): void {
        // 创建或更新错误提示
        let alertMessage = document.getElementById('alert-message');
        if (!alertMessage) {
            alertMessage = document.createElement('div');
            alertMessage.id = 'alert-message';
            alertMessage.className = 'fixed top-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            document.body.appendChild(alertMessage);
        }

        const alertTitle = alertMessage.querySelector('.alert-title') || alertMessage;
        alertTitle.textContent = message;
        alertMessage.style.display = 'block';

        setTimeout(() => {
            alertMessage.style.display = 'none';
        }, 3000);
    }

    /**
     * 获取密码强度分数
     */
    public getPasswordStrength(password: string): number {
        const strength = this.formValidationService.validatePasswordStrength(password);
        return strength.score;
    }

    /**
     * 检查密码是否符合所有要求
     */
    public isPasswordValid(password: string): boolean {
        const strength = this.formValidationService.validatePasswordStrength(password);
        return strength.hasLength && strength.hasUppercase && strength.hasLowercase && strength.hasNumber;
    }

    /**
     * 重置表单
     */
    public resetForm(): void {
        const form = document.getElementById('password-form') as HTMLFormElement;
        if (form) {
            form.reset();
            
            // 重置密码强度规则显示
            Object.values(this.ruleElements).forEach(element => {
                if (element) {
                    const icon = element.querySelector('i');
                    if (icon) {
                        icon.setAttribute('data-lucide', 'x');
                        element.classList.remove('text-green-600');
                        element.classList.add('text-gray-500');
                    }
                }
            });
            
            // 清除所有错误状态
            const errorElements = form.querySelectorAll('.form-error, .text-xs.text-red-500');
            errorElements.forEach(element => element.remove());
            
            const inputElements = form.querySelectorAll('.form-input');
            inputElements.forEach(element => {
                element.classList.remove('border-red-500', 'error', 'border-danger');
            });

            this.initializeLucideIcons();
        }
    }

    /**
     * 显示修改成功提示
     */
    public showChangeSuccess(message: string = '密码修改成功'): void {
        const toast = document.createElement('div');
        toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    /**
     * 显示修改失败提示
     */
    public showChangeError(message: string = '密码修改失败'): void {
        this.showPasswordError(message);
    }

    /**
     * 启动倒计时功能（成功页面专用）
     */
    private startCountdown(): void {
        let countdown = 10;
        const countdownElement = document.getElementById('countdown-text');

        if (!countdownElement) {
            console.warn('Countdown element not found');
            return;
        }

        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown + '秒后自动返回登录';

            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = '/waplogin.aspx';
            }
        }, 1000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    const modifyPasswordPage = new ModifyPasswordPage();
    modifyPasswordPage.init();
    
    // 全局暴露实例，方便调试和其他脚本使用
    (window as any).modifyPasswordPage = modifyPasswordPage;
});

// 导出类供其他模块使用
export default ModifyPasswordPage;
