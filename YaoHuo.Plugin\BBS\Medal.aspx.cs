using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.Template.Models;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.BBS.Models;
using YaoHuo.Plugin.WebSite.Services.Config;

namespace YaoHuo.Plugin.BBS
{
    public partial class Medal : MyPageWap
    {
        public string ERROR = "";
        public string INFO = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            // 会员可见判断
            if (!IsCheckManagerLvl("|00|01|02|03|04|", ""))
            {
                Response.Redirect("/");
                return;
            }

            try
            {
                // 检查用户UI偏好并处理版本切换
                bool newVersionRendered = CheckAndHandleUIPreference();
                if (newVersionRendered)
                {
                    // 新版渲染成功，直接返回，不再执行后续的旧版代码
                    return;
                }
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
            }

            // 继续执行旧版逻辑...
        }

        /// <summary>
        /// 检查用户UI偏好并处理版本切换
        /// </summary>
        private bool CheckAndHandleUIPreference()
        {
            // 详细调试当前请求信息
            System.Diagnostics.Debug.WriteLine($"[Medal体验模式] 当前请求URL: {Request.Url}");
            System.Diagnostics.Debug.WriteLine($"[Medal体验模式] 原始URL: {Request.RawUrl}");
            System.Diagnostics.Debug.WriteLine($"[Medal体验模式] 查询字符串原始: '{Request.QueryString}'");

            // 调试所有QueryString键值对
            var allParams = new List<string>();
            foreach (string key in Request.QueryString.AllKeys)
            {
                string value = Request.QueryString[key];
                allParams.Add($"'{key ?? "NULL"}'='{value ?? "NULL"}'");
            }
            System.Diagnostics.Debug.WriteLine($"[Medal体验模式] 所有参数详细: [{string.Join(", ", allParams)}]");

            // 多种方式检查体验模式参数
            // 方式1: 标准参数 ?ui=new
            string uiParam = Request.QueryString["ui"];
            bool forceNewByUiParam = uiParam == "new";

            // 方式2: 简单参数 ?new (检查是否在查询字符串中出现)
            bool forceNewBySimpleParam = Request.RawUrl.Contains("?new") || Request.RawUrl.Contains("&new");

            // 方式3: 检查所有可能的键值组合
            bool forceNewByKeyValue = false;
            foreach (string key in Request.QueryString.AllKeys ?? new string[0])
            {
                string value = Request.QueryString[key];
                if ((key == "new" && value != null) ||
                    (string.IsNullOrEmpty(key) && value == "new") ||
                    (key == null && value == "new"))
                {
                    forceNewByKeyValue = true;
                    break;
                }
            }

            bool forceNewVersion = forceNewByUiParam || forceNewBySimpleParam || forceNewByKeyValue;

            System.Diagnostics.Debug.WriteLine($"[Medal体验模式] 参数检查结果:");
            System.Diagnostics.Debug.WriteLine($"  - ui参数: '{uiParam ?? "null"}' -> {forceNewByUiParam}");
            System.Diagnostics.Debug.WriteLine($"  - URL包含new: {forceNewBySimpleParam}");
            System.Diagnostics.Debug.WriteLine($"  - 键值匹配: {forceNewByKeyValue}");
            System.Diagnostics.Debug.WriteLine($"  - 最终结果: {forceNewVersion}");

            if (forceNewVersion)
            {
                System.Diagnostics.Debug.WriteLine("[Medal体验模式] 检测到体验模式参数，强制使用新版UI");
                return TryRenderWithHandlebars(true);
            }

            // 没有强制参数时，按cookie判断
            string uiPreference = "";
            if (Request.Cookies["ui_preference"] != null)
            {
                uiPreference = Request.Cookies["ui_preference"].Value;
            }
            if (string.IsNullOrEmpty(uiPreference))
            {
                uiPreference = "old";
            }

            System.Diagnostics.Debug.WriteLine($"[Medal体验模式] Cookie检查: ui_preference={uiPreference}");

            if (uiPreference == "new")
            {
                System.Diagnostics.Debug.WriteLine("[Medal体验模式] Cookie为新版，使用新版UI");
                return TryRenderWithHandlebars(false);
            }

            System.Diagnostics.Debug.WriteLine("[Medal体验模式] 使用旧版UI");
            return false;
        }

        /// <summary>
        /// 尝试使用Handlebars模板渲染页面
        /// </summary>
        private bool TryRenderWithHandlebars(bool isExperienceMode = false)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[Medal体验模式] 尝试渲染Handlebars模板, isExperienceMode={isExperienceMode}");

                // 使用反射检查TemplateService可用性
                var templateServiceType = Type.GetType("YaoHuo.Plugin.WebSite.Tool.TemplateService, YaoHuo.Plugin");
                if (templateServiceType != null)
                {
                    var getViewModeMethod = templateServiceType.GetMethod("GetViewMode");
                    var renderPageMethod = templateServiceType.GetMethod("RenderPageWithLayout");

                    if (getViewModeMethod != null && renderPageMethod != null)
                    {
                        string viewMode = (string)getViewModeMethod.Invoke(null, null);
                        System.Diagnostics.Debug.WriteLine($"[Medal体验模式] TemplateService.GetViewMode()={viewMode}");

                        // 体验模式下强制渲染新版，不依赖viewMode
                        if (isExperienceMode)
                        {
                            System.Diagnostics.Debug.WriteLine("[Medal体验模式] 体验模式强制渲染新版");
                            RenderWithHandlebars(isExperienceMode);
                            return true;
                        }
                        else if (viewMode == "new")
                        {
                            System.Diagnostics.Debug.WriteLine("[Medal体验模式] viewMode为new，渲染新版");
                            RenderWithHandlebars(isExperienceMode);
                            return true;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"[Medal体验模式] viewMode为{viewMode}，不渲染新版");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("[Medal体验模式] TemplateService方法不可用");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[Medal体验模式] TemplateService类型不可用");
                }

                ERROR = "Handlebars模板服务不可用";
                return false;
            }
            catch (System.Threading.ThreadAbortException)
            {
                System.Diagnostics.Debug.WriteLine("[Medal体验模式] ThreadAbortException - 正常结束");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[Medal体验模式] 渲染异常: {ex.Message}");
                ERROR = "新版模板加载失败: " + WapTool.ErrorToString(ex.ToString());
                return false;
            }
        }

        /// <summary>
        /// 使用Handlebars模板渲染页面
        /// </summary>
        private void RenderWithHandlebars(bool isExperienceMode = false)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[Medal体验模式] 开始渲染页面, isExperienceMode={isExperienceMode}");

                // 构建页面数据模型
                var pageModel = BuildMedalPageModel();

                // 设置体验模式标识，传递给前端JavaScript
                pageModel.IsExperienceMode = isExperienceMode;
                System.Diagnostics.Debug.WriteLine($"[Medal体验模式] 页面模型构建完成, IsExperienceMode={pageModel.IsExperienceMode}");

                // 构建头部选项
                var headerOptions = new HeaderOptionsModel { ShowViewModeToggle = false };

                // 如果是申请勋章页面，添加问号按钮显示申请流程说明
                if (pageModel.IsApplyPage)
                {
                    headerOptions.CustomButtons.Add(new HeaderButtonModel
                    {
                        Id = "help-button",
                        Icon = "help-circle",
                        OnClick = "showHelpModal()",
                        Tooltip = "申请流程说明"
                    });
                }

                System.Diagnostics.Debug.WriteLine("[Medal体验模式] 调用TemplateService.RenderPageWithLayout");

                // 调用新的 RenderPageWithLayout 方法
                string finalHtml = TemplateService.RenderPageWithLayout(
                    "~/Template/Pages/Medal.hbs",
                    pageModel,
                    pageModel.PageTitle,
                    headerOptions
                );

                System.Diagnostics.Debug.WriteLine("[Medal体验模式] 模板渲染成功，输出HTML");

                // 输出渲染结果
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write(finalHtml);
                Response.End();
            }
            catch (System.Threading.ThreadAbortException)
            {
                System.Diagnostics.Debug.WriteLine("[Medal体验模式] Response.End()正常结束");
                // Response.End() 的正常行为，直接重新抛出
                throw;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[Medal体验模式] 渲染页面异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"[Medal体验模式] 异常堆栈: {ex.StackTrace}");
                // 错误处理
                Response.Clear();
                Response.ContentType = "text/html; charset=utf-8";
                Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}</div>");
                HttpContext.Current.ApplicationInstance.CompleteRequest();
            }
        }

        /// <summary>
        /// 构建勋章页面数据模型
        /// </summary>
        private MedalPageModel BuildMedalPageModel()
        {
            var model = new MedalPageModel();

            // 清空当前用户的勋章缓存，确保显示最新的勋章状态
            // 这样用户刚购买的勋章能立即显示为"已拥有"状态
            if (userVo != null && !string.IsNullOrEmpty(userVo.moneyname))
            {
                BBSConfigService.ClearUserMedalCache(userVo.moneyname);
                System.Diagnostics.Debug.WriteLine($"[Medal缓存] 清空用户勋章缓存: {userVo.userid}");
            }

            // 获取页面类型（用于初始显示状态）
            string pageType = Request.QueryString["type"] ?? "apply";
            model.PageType = pageType;

            // 设置页面标题
            model.PageTitle = "勋章中心";

            // 构建消息模型
            BuildMessageModel(model);

            // 构建站点信息
            BuildSiteInfoModel(model);

            // 同时构建两种勋章列表，前端动态切换显示
            BuildApplyMedalList(model);
            BuildPurchaseMedalList(model);

            // 设置问候语和时间
            model.Greeting = WapTool.GetHello();
            model.CurrentTime = DateTime.Now.ToString("H:mm");

            return model;
        }

        /// <summary>
        /// 构建消息模型
        /// </summary>
        private void BuildMessageModel(MedalPageModel model)
        {
            if (!string.IsNullOrEmpty(ERROR))
            {
                model.Message.HasMessage = true;
                model.Message.Type = "error";
                model.Message.Content = ERROR;
                model.Message.IsSuccess = false;
            }
            else if (!string.IsNullOrEmpty(INFO))
            {
                model.Message.HasMessage = true;
                model.Message.Type = "success";
                model.Message.Content = INFO;
                model.Message.IsSuccess = true;
            }
        }

        /// <summary>
        /// 构建站点信息模型
        /// </summary>
        private void BuildSiteInfoModel(MedalPageModel model)
        {
            model.SiteInfo.SiteId = siteid;
            model.SiteInfo.ClassId = classid;
            model.SiteInfo.HttpStart = http_start;
            model.SiteInfo.BackUrl = Request.QueryString["backurl"] ?? "";
        }





        /// <summary>
        /// 构建申请勋章列表
        /// 🚀 性能优化：使用批量检查避免重复解析用户勋章数据
        /// </summary>
        private void BuildApplyMedalList(MedalPageModel model)
        {
            try
            {
                // 使用新的通用配置服务获取申请勋章列表
                var applyMedalConfigs = BBSConfigService.GetApplyMedals();

                // 🚀 性能优化：批量检查勋章拥有状态
                var medalFileNames = applyMedalConfigs.Select(c => c.FileName).ToList();
                var ownershipStatus = BBSConfigService.BatchCheckMedalOwnership(medalFileNames, userVo?.moneyname ?? "");

                model.ApplyMedals = applyMedalConfigs.Select(config => new ApplyMedalModel
                {
                    Name = config.Name,
                    IconUrl = config.IconUrl,
                    Description = config.Description,
                    Category = config.Category,
                    ConditionUrl = config.ConditionUrl,
                    MedalFileName = config.FileName,
                    IsOwned = ownershipStatus.GetValueOrDefault(config.FileName, false)
                }).ToList();

                System.Diagnostics.Debug.WriteLine($"申请勋章列表构建完成，共 {model.ApplyMedals.Count} 个勋章");
            }
            catch (Exception ex)
            {
                // 如果JSON配置加载失败，使用备用硬编码数据
                System.Diagnostics.Debug.WriteLine($"加载申请勋章配置失败，使用备用数据: {ex.Message}");
                BuildFallbackApplyMedalList(model);
            }
        }

        /// <summary>
        /// 构建备用申请勋章列表（硬编码）
        /// </summary>
        private void BuildFallbackApplyMedalList(MedalPageModel model)
        {
            string userMoneyName = userVo?.moneyname ?? "";

            model.ApplyMedals = new List<ApplyMedalModel>
            {
                new ApplyMedalModel
                {
                    Name = "新人进步",
                    IconUrl = "/bbs/medal/67.gif",
                    Description = "累计发表10篇资源帖，展现新人的积极进步精神",
                    Category = "achievement",
                    MedalFileName = "67.gif",
                    IsOwned = BBSConfigService.IsMedalOwned("67.gif", userMoneyName)
                },
                new ApplyMedalModel
                {
                    Name = "认真学习",
                    IconUrl = "/bbs/medal/认真学习.gif",
                    Description = "认真回帖满1000次，体现学习的认真态度",
                    Category = "achievement",
                    MedalFileName = "认真学习.gif",
                    IsOwned = BBSConfigService.IsMedalOwned("认真学习.gif", userMoneyName)
                }
                // 可以继续添加更多备用勋章...
            };
        }

        /// <summary>
        /// 构建购买勋章列表
        /// 🚀 性能优化：使用批量检查避免重复解析用户勋章数据
        /// </summary>
        private void BuildPurchaseMedalList(MedalPageModel model)
        {
            try
            {
                // 使用新的通用配置服务获取购买勋章列表
                var purchaseMedalConfigs = BBSConfigService.GetPurchaseMedals();

                // 🚀 性能优化：批量检查勋章拥有状态
                var medalFileNames = purchaseMedalConfigs.Select(c => c.FileName).ToList();
                var ownershipStatus = BBSConfigService.BatchCheckMedalOwnership(medalFileNames, userVo?.moneyname ?? "");

                model.PurchaseMedals = purchaseMedalConfigs.Select(config => new PurchaseMedalModel
                {
                    Name = config.Name,
                    IconUrl = config.IconUrl,
                    Description = config.Description,
                    Category = config.Category,
                    Price = config.Price,
                    BuyUrl = config.BuyUrl,
                    MedalFileName = config.FileName,
                    IsOwned = ownershipStatus.GetValueOrDefault(config.FileName, false)
                }).ToList();

                System.Diagnostics.Debug.WriteLine($"购买勋章列表构建完成，共 {model.PurchaseMedals.Count} 个勋章");
            }
            catch (Exception ex)
            {
                // 如果JSON配置加载失败，使用备用硬编码数据
                System.Diagnostics.Debug.WriteLine($"加载购买勋章配置失败，使用备用数据: {ex.Message}");
                BuildFallbackPurchaseMedalList(model);
            }
        }

        /// <summary>
        /// 构建备用购买勋章列表（硬编码）
        /// </summary>
        private void BuildFallbackPurchaseMedalList(MedalPageModel model)
        {
            string userMoneyName = userVo?.moneyname ?? "";

            model.PurchaseMedals = new List<PurchaseMedalModel>
            {
                new PurchaseMedalModel
                {
                    Name = "初级勋章",
                    IconUrl = "/bbs/medal/初级勋章.gif",
                    Description = "入门级别的基础勋章，开启收集之旅的第一步",
                    Category = "popular",
                    Price = "10000",
                    BuyUrl = "/XinZhang/book_view_buy.aspx?&id=20&lpage=1&ordertype=1",
                    MedalFileName = "初级勋章.gif",
                    IsOwned = BBSConfigService.IsMedalOwned("初级勋章.gif", userMoneyName)
                }
                // 可以继续添加更多备用勋章...
            };
        }


    }
}