using System;
using System.Collections.Generic;

namespace YaoHuo.Plugin.Template.Models
{
    /// <summary>
    /// 消息详情页面数据模型（聊天界面）
    /// </summary>
    public class MessageDetailPageModel : BasePageModel
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public MessageDetailPageModel()
        {
            PageTitle = "查看消息"; // 默认标题，会动态设置为"与 xxx 的对话"
        }

        /// <summary>
        /// 当前消息的详细信息
        /// </summary>
        public MessageInfoModel MessageInfo { get; set; } = new MessageInfoModel();

        /// <summary>
        /// 聊天记录列表
        /// </summary>
        public List<ChatMessageModel> ChatHistory { get; set; } = new List<ChatMessageModel>();

        /// <summary>
        /// 回复表单相关数据
        /// </summary>
        public ReplyFormModel ReplyForm { get; set; } = new ReplyFormModel();

        /// <summary>
        /// 发送方和接收方用户信息
        /// </summary>
        public ConversationUsersModel UserInfo { get; set; } = new ConversationUsersModel();

        /// <summary>
        /// 对话相关设置
        /// </summary>
        public ConversationSettingsModel ConversationSettings { get; set; } = new ConversationSettingsModel();

        /// <summary>
        /// 对话对象信息（用于动态标题）
        /// </summary>
        public UserInfoModel ConversationPartner { get; set; } = new UserInfoModel();

        /// <summary>
        /// 锚点消息ID，用于前端定位和滚动
        /// </summary>
        public long AnchorMessageId { get; set; }
    }

    /// <summary>
    /// 消息详情模型
    /// </summary>
    public class MessageInfoModel
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 消息标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 原始内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// UBB处理后的内容
        /// </summary>
        public string ProcessedContent { get; set; }

        /// <summary>
        /// 发送时间
        /// </summary>
        public DateTime AddTime { get; set; }

        /// <summary>
        /// 发送者ID
        /// </summary>
        public string SenderId { get; set; }

        /// <summary>
        /// 发送者昵称
        /// </summary>
        public string SenderNickname { get; set; }

        /// <summary>
        /// 消息状态（0=已读，1=未读，2=重发）
        /// </summary>
        public int Status { get; set; }
    }

    /// <summary>
    /// 单条聊天消息模型
    /// </summary>
    public class ChatMessageModel
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 原始内容
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// UBB处理后的内容
        /// </summary>
        public string ProcessedContent { get; set; }

        /// <summary>
        /// 是否显示头像
        /// </summary>
        public bool ShowAvatar { get; set; }

        /// <summary>
        /// 是否显示时间
        /// </summary>
        public bool ShowTime { get; set; }

        /// <summary>
        /// 是否当前用户发送
        /// </summary>
        public bool IsFromCurrentUser { get; set; }

        /// <summary>
        /// 发送时间
        /// </summary>
        public DateTime AddTime { get; set; }

        /// <summary>
        /// 发送者ID
        /// </summary>
        public long SenderId { get; set; }

        /// <summary>
        /// 发送者昵称
        /// </summary>
        public string SenderNickname { get; set; }

        /// <summary>
        /// 发送者头像URL
        /// </summary>
        public string SenderAvatarUrl { get; set; }

        /// <summary>
        /// 发送者是否为默认头像
        /// </summary>
        public bool SenderIsDefaultAvatar { get; set; } = true;

        /// <summary>
        /// 发送者昵称首字母（用于fallback显示）
        /// </summary>
        public string SenderFirstChar { get; set; }

        /// <summary>
        /// 日期分组标识
        /// </summary>
        public string DateGroup { get; set; }

        /// <summary>
        /// 是否新日期组的第一条
        /// </summary>
        public bool IsNewDateGroup { get; set; }

        /// <summary>
        /// 格式化的时间显示（如：14:30）
        /// </summary>
        public string TimeDisplay { get; set; }

        /// <summary>
        /// 消息状态
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 是否与前一条消息紧密相连（连续消息，用于控制间距）
        /// </summary>
        public bool IsCompact { get; set; }

        /// <summary>
        /// 是否是第一条消息
        /// </summary>
        public bool IsFirst { get; set; }

        /// <summary>
        /// 是否为锚点消息（用于前端标识和高亮）
        /// </summary>
        public bool IsAnchor { get; set; }

        /// <summary>
        /// 消息间距CSS类名（简化模板复杂嵌套）
        /// </summary>
        public string SpacingClass => IsFirst ? "" : (IsCompact ? "mt-2" : "mt-4");
    }

    /// <summary>
    /// 对话用户信息模型
    /// </summary>
    public class ConversationUsersModel
    {
        /// <summary>
        /// 发送方用户信息
        /// </summary>
        public UserInfoModel Sender { get; set; } = new UserInfoModel();

        /// <summary>
        /// 接收方用户信息
        /// </summary>
        public UserInfoModel Receiver { get; set; } = new UserInfoModel();

        /// <summary>
        /// 当前用户ID
        /// </summary>
        public string CurrentUserId { get; set; }
    }

    /// <summary>
    /// 用户信息模型
    /// </summary>
    public class UserInfoModel
    {
        /// <summary>
        /// 用户ID
        /// </summary>
        public string UserId { get; set; }

        /// <summary>
        /// 用户昵称
        /// </summary>
        public string Nickname { get; set; }

        /// <summary>
        /// 头像URL
        /// </summary>
        public string AvatarUrl { get; set; }

        /// <summary>
        /// 是否为默认头像
        /// </summary>
        public bool IsDefaultAvatar { get; set; } = true;

        /// <summary>
        /// 昵称首字母（用于fallback显示）
        /// </summary>
        public string FirstChar { get; set; }

        /// <summary>
        /// 用户空间链接
        /// </summary>
        public string UserSpaceUrl { get; set; }
    }

    /// <summary>
    /// 回复表单模型
    /// </summary>
    public class ReplyFormModel
    {
        /// <summary>
        /// 是否需要密码验证
        /// </summary>
        public bool NeedPassword { get; set; }

        /// <summary>
        /// 密码值
        /// </summary>
        public string Password { get; set; }

        /// <summary>
        /// 表单提交URL
        /// </summary>
        public string ActionUrl { get; set; }

        /// <summary>
        /// 目标用户ID列表
        /// </summary>
        public string TargetUserIds { get; set; }

        /// <summary>
        /// 回复目标消息ID
        /// </summary>
        public string TargetMessageId { get; set; }

        /// <summary>
        /// 隐藏字段集合
        /// </summary>
        public Dictionary<string, string> HiddenFields { get; set; } = new Dictionary<string, string>();
    }

    /// <summary>
    /// 对话设置模型
    /// </summary>
    public class ConversationSettingsModel
    {
        /// <summary>
        /// 是否关闭对话（不显示历史记录）
        /// </summary>
        public bool IsConversationClosed { get; set; }

        /// <summary>
        /// 是否显示历史记录
        /// </summary>
        public bool ShowChatHistory { get; set; } = true;

        /// <summary>
        /// 历史记录显示数量
        /// </summary>
        public int HistoryLimit { get; set; } = 50;

        /// <summary>
        /// 是否可以回复
        /// </summary>
        public bool CanReply { get; set; } = true;

        /// <summary>
        /// 是否还有更多消息可以加载（向上，更早的消息）
        /// </summary>
        public bool HasMoreMessages { get; set; } = false;

        /// <summary>
        /// 是否还有更新的消息可以加载（向下，更新的消息）
        /// </summary>
        public bool HasMoreNewerMessages { get; set; } = false;
    }


} 