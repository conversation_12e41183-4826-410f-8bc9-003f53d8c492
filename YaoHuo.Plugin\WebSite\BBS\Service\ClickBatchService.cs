using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading;
using Dapper;
using KeLin.ClassManager;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.WebSite.BBS.Service
{
    /// <summary>
    /// 点击数批量处理服务
    /// 收集点击请求并定时批量更新数据库，减少数据库压力和死锁风险
    /// </summary>
    public static class ClickBatchService
    {
        #region 私有字段

        /// <summary>
        /// 点击请求队列，线程安全
        /// </summary>
        private static readonly ConcurrentQueue<ClickRequest> _clickQueue = new ConcurrentQueue<ClickRequest>();

        /// <summary>
        /// 批量处理定时器，每30秒执行一次
        /// </summary>
        private static readonly Timer _batchTimer;

        /// <summary>
        /// 服务是否已初始化
        /// </summary>
        private static volatile bool _isInitialized = false;

        /// <summary>
        /// 初始化锁对象
        /// </summary>
        private static readonly object _initLock = new object();

        /// <summary>
        /// 批量处理间隔（毫秒）
        /// </summary>
        private const int BATCH_INTERVAL_MS = 30000; // 30秒

        /// <summary>
        /// 最大队列长度，防止内存溢出
        /// </summary>
        private const int MAX_QUEUE_SIZE = 10000;

        #endregion

        #region 静态构造函数

        /// <summary>
        /// 静态构造函数，初始化定时器
        /// </summary>
        static ClickBatchService()
        {
            try
            {
                // 延迟5秒启动，避免应用启动时的资源竞争
                _batchTimer = new Timer(ProcessBatchClicks, null, 5000, BATCH_INTERVAL_MS);
                _isInitialized = true;
            }
            catch (Exception ex)
            {
                _ = ex;
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 将点击请求加入批量处理队列
        /// </summary>
        /// <param name="siteId">站点ID</param>
        /// <param name="postId">帖子ID</param>
        public static void QueueClick(string siteId, string postId)
        {
            try
            {
                // 确保服务已初始化
                EnsureInitialized();

                // 检查队列长度，防止内存溢出
                if (_clickQueue.Count >= MAX_QUEUE_SIZE)
                {
                    return;
                }

                // 验证参数
                if (string.IsNullOrWhiteSpace(siteId) || string.IsNullOrWhiteSpace(postId))
                {
                    return;
                }

                // 创建点击请求并加入队列
                var clickRequest = new ClickRequest
                {
                    SiteId = siteId,
                    PostId = postId,
                    Timestamp = DateTime.Now
                };

                _clickQueue.Enqueue(clickRequest);
            }
            catch (Exception ex)
            {
                _ = ex;
            }
        }

        /// <summary>
        /// 获取当前队列状态信息（用于监控）
        /// </summary>
        /// <returns>队列状态信息</returns>
        public static string GetQueueStatus()
        {
            try
            {
                return $"队列长度: {_clickQueue.Count}, 服务状态: {(_isInitialized ? "运行中" : "未初始化")}";
            }
            catch (Exception ex)
            {
                return $"获取状态失败: {ex.Message}";
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 确保服务已初始化
        /// </summary>
        private static void EnsureInitialized()
        {
            if (!_isInitialized)
            {
                lock (_initLock)
                {
                    if (!_isInitialized)
                    {
                        _isInitialized = true;
                    }
                }
            }
        }

        /// <summary>
        /// 批量处理点击请求的定时器回调方法
        /// </summary>
        /// <param name="state">定时器状态（未使用）</param>
        private static void ProcessBatchClicks(object state)
        {
            try
            {
                // 获取队列中的所有点击请求
                var clickRequests = DequeueAllClicks();

                if (clickRequests.Count == 0)
                {
                    return; // 没有待处理的点击请求
                }

                // 按帖子分组并统计点击数
                var groupedClicks = clickRequests
                    .GroupBy(c => c.PostId)
                    .Select(g => new
                    {
                        PostId = long.Parse(g.Key),
                        ClickCount = g.Count()
                    })
                    .ToList();

                if (groupedClicks.Count == 0)
                {
                    return;
                }

                // 使用TVP批量更新点击数
                UpdatePostClickCountWithTVP(groupedClicks);
            }
            catch (Exception)
            {
                // 记录批处理失败，但不抛出异常以避免影响定时器
            }
        }

        /// <summary>
        /// 从队列中取出所有点击请求
        /// </summary>
        /// <returns>点击请求列表</returns>
        private static List<ClickRequest> DequeueAllClicks()
        {
            var clicks = new List<ClickRequest>();
            
            // 一次性取出队列中的所有元素
            while (_clickQueue.TryDequeue(out ClickRequest clickRequest))
            {
                clicks.Add(clickRequest);
            }
            
            return clicks;
        }

        /// <summary>
        /// 使用表值参数(TVP)批量更新帖子点击数
        /// </summary>
        /// <param name="groupedClicks">分组后的点击数据</param>
        private static void UpdatePostClickCountWithTVP(IEnumerable<dynamic> groupedClicks)
        {
            try
            {
                // 将聚合数据转换为 DataTable，其结构必须与TVP类型完全匹配
                var dt = new System.Data.DataTable();
                dt.Columns.Add("PostId", typeof(long));
                dt.Columns.Add("ClickCount", typeof(int));

                foreach (var item in groupedClicks)
                {
                    dt.Rows.Add(item.PostId, item.ClickCount);
                }

                if (dt.Rows.Count == 0) return;

                // 定义使用TVP的SQL更新语句
                string updateSql = @"
                UPDATE B
                SET B.book_click = B.book_click + T.ClickCount
                FROM dbo.wap_bbs AS B
                INNER JOIN @ClickUpdates AS T ON B.id = T.PostId;";

                // 使用 Dapper 传递 TVP 参数并执行
                string instanceName = PubConstant.GetAppString("InstanceName");
                string connectionString = PubConstant.GetConnectionString(instanceName);

                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand(updateSql, connection))
                    {
                        // 创建表值参数
                        var parameter = command.Parameters.AddWithValue("@ClickUpdates", dt);
                        parameter.SqlDbType = SqlDbType.Structured;
                        parameter.TypeName = "dbo.ClickUpdateType";

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception)
            {
                // 记录批量更新失败的详细日志，这对于排查问题至关重要
                // 例如: Log.Error("TVP批量更新点击数失败，启动降级处理。", ex);

                // 调用独立的降级方法
                HandleUpdateFailureByDegradation(groupedClicks);
            }
        }

        /// <summary>
        /// 批量更新失败后的降级处理，逐个更新帖子点击数
        /// </summary>
        /// <param name="groupedClicks">分组后的点击数据</param>
        private static void HandleUpdateFailureByDegradation(IEnumerable<dynamic> groupedClicks)
        {
            foreach (var item in groupedClicks)
            {
                try
                {
                    // 调用优化后的单个更新方法，直接传递long类型
                    UpdatePostClickCountSingle(item.PostId, item.ClickCount);
                }
                catch (Exception)
                {
                    // 记录单个更新失败的日志，但循环继续
                    // 例如: Log.Warn($"降级更新帖子 {item.PostId} 失败。", singleEx);
                }
            }
        }

        /// <summary>
        /// 更新单个帖子的点击数（降级方案）
        /// </summary>
        /// <param name="postId">帖子ID（long类型，避免重复转换）</param>
        /// <param name="clickCount">要增加的点击数</param>
        private static void UpdatePostClickCountSingle(long postId, int clickCount)
        {
            try
            {
                // 使用DapperHelper进行安全的参数化更新
                string instanceName = PubConstant.GetAppString("InstanceName");
                string connectionString = PubConstant.GetConnectionString(instanceName);
                string updateSql = "UPDATE wap_bbs SET book_click = book_click + @ClickCount WHERE id = @PostId";

                DapperHelper.Execute(connectionString, updateSql, new
                {
                    ClickCount = clickCount,
                    PostId = postId
                });
            }
            catch (Exception ex)
            {
                // 将异常向上抛出，由调用者(HandleUpdateFailureByDegradation)的catch块处理
                throw new Exception($"更新帖子点击数失败: PostId={postId}, ClickCount={clickCount}", ex);
            }
        }

        #endregion
    }

    /// <summary>
    /// 点击请求数据结构
    /// </summary>
    internal class ClickRequest
    {
        /// <summary>
        /// 站点ID
        /// </summary>
        public string SiteId { get; set; }

        /// <summary>
        /// 帖子ID
        /// </summary>
        public string PostId { get; set; }

        /// <summary>
        /// 点击时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
}
