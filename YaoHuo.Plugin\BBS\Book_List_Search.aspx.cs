﻿using KeLin.ClassManager.Model;
using KeLin.ClassManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Book_List_Search : BaseBBSListPage
    {
        // 常量定义
        private const string SEARCH_TYPE_CONTENT = "content";
        private const string SEARCH_TYPE_TITLE = "title";
        private const string SEARCH_TYPE_AUTHOR = "author";
        private const string SEARCH_TYPE_DAYS = "days";
        private const string SEARCH_TYPE_PUB = "pub";

        // 搜索特定属性
        private static bool EnableMultiKeywordSearch = true;
        private static Dictionary<string, DateTime> lastSearchTime = new Dictionary<string, DateTime>();
        private static readonly TimeSpan searchLogInterval = TimeSpan.FromMinutes(2);

        // 添加公共属性
        public string key = "";
        public string type = "";
        public string titlecolor = "";

        public static void ToggleMultiKeywordSearch()
        {
            EnableMultiKeywordSearch = !EnableMultiKeywordSearch;
        }

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            ShowSearch();
        }

        private void ShowSearch()
        {
            try
            {
                var searchParams = GetAndValidateSearchParams();
                if (searchParams == null) return;

                // 设置公共属性，以便前端访问
                this.key = searchParams.Key;
                this.type = searchParams.Type;

                // ✅ 使用QueryBuilder构建安全的查询条件
                var queryBuilder = SetupClassAndCondition(searchParams);
                queryBuilder = AddSearchConditions(searchParams, queryBuilder);
                ProcessSearch(searchParams, queryBuilder);
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }

        private class SearchParameters
        {
            public string Key { get; set; }
            public string Type { get; set; }
            public string Pub { get; set; }
            public string DisplayText { get; set; }
            public string DisplayValue { get; set; }
        }

        private SearchParameters GetAndValidateSearchParams()
        {
            var key = GetRequestValue("key");
            var type = GetRequestValue("type");
            var pub = GetRequestValue("pub");

            // 处理关键词
            if (!string.IsNullOrEmpty(key))
            {
                key = ProcessKeyword(key);
            }

            // 处理用户ID
            if (type == SEARCH_TYPE_PUB || !string.IsNullOrEmpty(pub))
            {
                key = key?.TrimStart('0');
                pub = pub?.TrimStart('0');
            }

            // 验证搜索参数
            if (key.IsNull() && pub.IsNull())
            {
                ShowTipInfo("禁止空白搜索", "");
                return null;
            }

            // 检查登录要求
            if (WapTool.GetSiteDefault(siteVo.Version, 60) == "1")
            {
                IsLogin(userid, GetUrlQueryString());
            }

            return new SearchParameters
            {
                Key = key,
                Type = type,
                Pub = pub,
                DisplayText = GetDisplayText(type, pub),
                DisplayValue = GetDisplayValue(key, pub, type)
            };
        }

        private string ProcessKeyword(string key)
        {
            // 限制关键词数量和长度
            var keywords = key.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            if (keywords.Length > 5)
            {
                keywords = keywords.Take(5).ToArray();
            }
            key = string.Join(" ", keywords);
            if (key.Length > 20)
            {
                key = key.Substring(0, 20);
            }

            // URL解码和特殊字符处理
            key = HttpUtility.UrlDecode(key);
            return key.Replace("[", "［")
                     .Replace("]", "］")
                     .Replace("%", "％")
                     .Replace("_", "——");
        }

        private QueryBuilder SetupClassAndCondition(SearchParameters searchParams)
        {
            if (classid == "0")
            {
                return SetupEmptyClass(searchParams);
            }
            else
            {
                return SetupExistingClass(searchParams);
            }
        }

        private QueryBuilder SetupEmptyClass(SearchParameters searchParams)
        {
            // ✅ 使用安全的参数化查询构建基础条件（全站搜索）
            // 🎯 移除userid条件：wap_bbs表中userid都是1000（站点ID），无选择性，移除以优化索引使用
            var queryBuilder = new QueryBuilder()
                .Where("ischeck = @ParamN", 0);

            classVo.classid = 0L;
            classVo.position = "left";
            classVo.classname = searchParams.Type == SEARCH_TYPE_DAYS
                ? $"查询天数:{searchParams.DisplayValue}"
                : $"{searchParams.DisplayText}:{searchParams.DisplayValue}";
            classVo.siteimg = "NetImages/no.gif";
            classVo.introduce = "";

            return queryBuilder;
        }

        private QueryBuilder SetupExistingClass(SearchParameters searchParams)
        {
            // ✅ 使用安全的参数化查询构建基础条件
            // 🎯 移除userid条件：wap_bbs表中userid都是1000（站点ID），无选择性，移除以优化索引使用
            var queryBuilder = new QueryBuilder()
                .Where("book_classid = @ParamN", DapperHelper.SafeParseLong(classid, "版块ID"))
                .Where("ischeck = @ParamN", 0)
                .Where("book_date >= DATEADD(year, -5, GETDATE())", 1); // 添加一个占位参数

            if (searchParams.Type == SEARCH_TYPE_DAYS)
            {
                classVo.classname = $"{classVo.classname}>查询天数:{searchParams.DisplayValue}";
            }
            else if (searchParams.Type == SEARCH_TYPE_PUB || !string.IsNullOrEmpty(searchParams.Pub))
            {
                classVo.classname = $"{classVo.classname}>查询用户:{searchParams.DisplayValue}";
            }
            else
            {
                classVo.classname = $"{classVo.classname}>{searchParams.DisplayText}:{searchParams.DisplayValue}";
            }

            return queryBuilder;
        }

        private QueryBuilder AddSearchConditions(SearchParameters searchParams, QueryBuilder queryBuilder)
        {
            if (!string.IsNullOrEmpty(searchParams.Key))
            {
                queryBuilder = AddKeywordCondition(searchParams, queryBuilder);
            }

            if (!string.IsNullOrEmpty(searchParams.Pub) && WapTool.IsNumeric(searchParams.Pub))
            {
                // ✅ 使用安全的参数化查询
                queryBuilder.Where("book_pub = @ParamN", DapperHelper.SafeParseLong(searchParams.Pub, "发布用户ID"));
            }

            // 特殊用户搜索限制
            if (IsUser1000Search(searchParams) && userid != "1000")
            {
                queryBuilder.Where("book_date >= DATEADD(year, -2, GETDATE())", 1); // 添加占位参数
            }

            return queryBuilder;
        }

        private QueryBuilder AddKeywordCondition(SearchParameters searchParams, QueryBuilder queryBuilder)
        {
            switch (searchParams.Type)
            {
                case SEARCH_TYPE_TITLE:
                    return AddTitleSearchCondition(searchParams.Key, queryBuilder);
                case SEARCH_TYPE_CONTENT:
                    return AddContentSearchCondition(searchParams.Key, queryBuilder);
                case SEARCH_TYPE_AUTHOR:
                    // ✅ 使用安全的参数化查询
                    return queryBuilder.Where("book_author LIKE @ParamN", $"%{searchParams.Key}%");
                case SEARCH_TYPE_DAYS:
                    return AddDaysSearchCondition(searchParams.Key, queryBuilder);
                case SEARCH_TYPE_PUB:
                    if (WapTool.IsNumeric(searchParams.Key))
                    {
                        // ✅ 使用安全的参数化查询
                        return queryBuilder.Where("book_pub = @ParamN", DapperHelper.SafeParseLong(searchParams.Key, "发布用户ID"));
                    }
                    break;
            }
            return queryBuilder;
        }

        private void ProcessSearch(SearchParameters searchParams, QueryBuilder queryBuilder)
        {
            pageSize = Convert.ToInt32(siteVo.MaxPerPage_Default);
            string connectionString = PubConstant.GetConnectionString(a);

            // ✅ 使用DapperHelper进行安全的分页查询
            HandlePaging(queryBuilder, connectionString);
            UpdateUserBBSCount(searchParams);
            BuildSearchPageLinks(searchParams);

            // ✅ 使用QueryBuilder获取搜索结果（添加NOLOCK避免死锁）
            var (dataSql, dataParameters) = queryBuilder.Build("SELECT book_classid,id,book_title,book_date,book_click,book_re,book_author,book_pub,book_top,book_good,topic,islock,ischeck,sendMoney,isvote,isdown,hangbiaoshi,freeMoney,freeleftMoney,book_img,MarkSixBetID,MarkSixWin FROM wap_bbs WITH (NOLOCK)");

            // 添加排序和分页，确保offset不为负数
            long offset = Math.Max(0, (CurrentPage - 1) * pageSize);

            // 如果没有搜索结果，直接返回空列表，避免执行OFFSET查询
            if (total == 0)
            {
                listVo = new List<wap_bbs_Model>();
            }
            else
            {
                dataSql += $" ORDER BY id DESC OFFSET {offset} ROWS FETCH NEXT {pageSize} ROWS ONLY";
                listVo = DapperHelper.Query<wap_bbs_Model>(connectionString, dataSql, dataParameters)?.ToList() ?? new List<wap_bbs_Model>();
            }

            // 加载广告
            LoadAdvertisement();

            // 获取搜索结果后加载用户信息
            LoadUserInfo();

            // 注意：派币图标现在直接判断 freeLeftMoney > 0，无需预加载缓存

            // 记录搜索日志
            LogSearchActivity(searchParams);
        }

        /// <summary>
        /// 使用QueryBuilder进行安全的分页处理
        /// </summary>
        private void HandlePaging(QueryBuilder queryBuilder, string connectionString)
        {
            if (GetRequestValue("getTotal") != "")
            {
                total = long.Parse(GetRequestValue("getTotal"));
            }
            else
            {
                // ✅ 使用DapperHelper进行安全的计数查询（添加NOLOCK避免死锁）
                var (countSql, countParameters) = queryBuilder.Build("SELECT COUNT(*) FROM wap_bbs WITH (NOLOCK)");
                total = DapperHelper.ExecuteScalar<long>(connectionString, countSql, countParameters);
            }

            if (GetRequestValue("page") != "")
            {
                CurrentPage = long.Parse(GetRequestValue("page"));
            }

            CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);

            // 确保CurrentPage至少为1，避免负数offset
            if (CurrentPage < 1)
            {
                CurrentPage = 1;
            }

            index = pageSize * (CurrentPage - 1L);
        }

        private void UpdateUserBBSCount(SearchParameters searchParams)
        {
            if (CurrentPage == 1L && searchParams.Type == SEARCH_TYPE_PUB && WapTool.IsNumeric(searchParams.Key))
            {
                // ✅ 使用DapperHelper进行安全的参数化更新
                string connectionString = PubConstant.GetConnectionString(a);
                string updateSql = "UPDATE [user] SET bbsCount = @Total WHERE siteid = @SiteId AND userid = @UserId";
                DapperHelper.Execute(connectionString, updateSql, new {
                    Total = total,
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    UserId = DapperHelper.SafeParseLong(searchParams.Key, "用户ID")
                });
            }
        }

        private void BuildSearchPageLinks(SearchParameters searchParams)
        {
            var baseUrl = $"bbs/book_list_search.aspx?action=search&amp;siteid={siteid}&amp;classid={classid}&amp;type={searchParams.Type}&amp;key={HttpUtility.UrlEncode(searchParams.Key)}";
            if (!string.IsNullOrEmpty(searchParams.Pub))
            {
                baseUrl += $"&amp;pub={searchParams.Pub}";
            }
            BuildPageLinks(baseUrl);
        }

        private bool ShouldLogSearch(string searchKey)
        {
            var now = DateTime.Now;
            if (!lastSearchTime.ContainsKey(searchKey) || (now - lastSearchTime[searchKey]) > searchLogInterval)
            {
                lastSearchTime[searchKey] = now;
                return true;
            }
            return false;
        }

        private void LogSearchActivity(SearchParameters searchParams)
        {
            var (logMessage, searchKey) = GetSearchLogInfo(searchParams);
            if (ShouldLogSearch(searchKey))
            {
                VisiteCount(logMessage);
            }
        }

        private (string logMessage, string searchKey) GetSearchLogInfo(SearchParameters searchParams)
        {
            var currentClassName = classVo.classname.Split(':')[0];

            // 处理特殊情况
            if (!string.IsNullOrEmpty(searchParams.Pub) && searchParams.Type == SEARCH_TYPE_TITLE)
            {
                return ($"正在论坛查询{searchParams.Pub}的帖子:{searchParams.Key}",
                        $"title_{searchParams.Pub}_{searchParams.Key}");
            }

            if (searchParams.Type == SEARCH_TYPE_PUB || !string.IsNullOrEmpty(searchParams.Pub))
            {
                string userValue = !string.IsNullOrEmpty(searchParams.Pub) ? searchParams.Pub : searchParams.Key;
                return ($"正在论坛查询用户:{userValue}",
                        $"user_{userValue}");
            }

            // 使用传统的 switch 语句替代 switch expression
            string logMessage;
            string searchKey;

            switch (searchParams.Type)
            {
                case SEARCH_TYPE_TITLE:
                    logMessage = $"正在论坛查询标题:{searchParams.Key}";
                    searchKey = $"title_{searchParams.Key}";
                    break;
                case SEARCH_TYPE_CONTENT:
                    logMessage = $"正在论坛查询内容:{searchParams.Key}";
                    searchKey = $"content_{searchParams.Key}";
                    break;
                case SEARCH_TYPE_AUTHOR:
                    logMessage = $"正在论坛查询作者:{searchParams.Key}";
                    searchKey = $"author_{searchParams.Key}";
                    break;
                case SEARCH_TYPE_DAYS:
                    logMessage = $"正在查看{currentClassName}最近{searchParams.Key}天的帖子";
                    searchKey = $"days_{searchParams.Key}";
                    break;
                default:
                    logMessage = $"正在论坛查询关键字:{searchParams.Key}";
                    searchKey = $"keyword_{searchParams.Key}";
                    break;
            }

            return (logMessage, searchKey);
        }

        private bool IsUser1000Search(SearchParameters searchParams)
        {
            return (searchParams.Type == SEARCH_TYPE_PUB && searchParams.Key == "1000") ||
                   (!string.IsNullOrEmpty(searchParams.Pub) && searchParams.Pub == "1000");
        }

        private string GetDisplayText(string type, string pub)
        {
            if (type == SEARCH_TYPE_PUB || !string.IsNullOrEmpty(pub))
                return "查询用户";
            if (type == SEARCH_TYPE_TITLE)
                return "查询标题";
            return "查询内容";
        }

        private string GetDisplayValue(string key, string pub, string type)
        {
            if (type == SEARCH_TYPE_PUB || !string.IsNullOrEmpty(pub))
                return !string.IsNullOrEmpty(pub) ? pub : key;
            return key;
        }

        private string EscapeForContains(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;
            // 先转义双引号，再转义单引号，确保 CONTAINS 谓词安全
            return input.Replace("\"", "\"\"").Replace("'", "''");
        }

        private QueryBuilder AddTitleSearchCondition(string key, QueryBuilder queryBuilder)
        {
            // 分割关键词，支持多关键词搜索（空格分隔，含全角空格）
            var keywords = key.Split(new[] { ' ', '　' }, StringSplitOptions.RemoveEmptyEntries);

            // 🎯 中文全文搜索支持不佳，回退到LIKE确保搜索准确性
            if (keywords.Length >= 2 && keywords.Length <= 5 && EnableMultiKeywordSearch)
            {
                // 多关键词搜索：所有关键词都必须存在
                foreach (var keyword in keywords)
                {
                    queryBuilder.Where("book_title LIKE @ParamN", $"%{keyword}%");
                }
                return queryBuilder;
            }
            else
            {
                // 单关键词搜索
                string singleKeyword = keywords.Length > 0 ? keywords.First() : key;
                return queryBuilder.Where("book_title LIKE @ParamN", $"%{singleKeyword}%");
            }
        }

        private QueryBuilder AddContentSearchCondition(string key, QueryBuilder queryBuilder)
        {
            var keywords = key.Split(new[] { ' ', '　' }, StringSplitOptions.RemoveEmptyEntries);

            // 🎯 基于测试结果：CONTAINS遗漏太多内容，完全使用LIKE确保搜索准确性
            if (keywords.Length >= 2 && keywords.Length <= 5 && EnableMultiKeywordSearch)
            {
                // 多关键词搜索：所有关键词都必须存在
                foreach (var keyword in keywords)
                {
                    queryBuilder.Where("(book_title LIKE @ParamN OR book_content LIKE @ParamN)", $"%{keyword}%");
                }
                return queryBuilder;
            }
            else
            {
                // 单关键词搜索
                string singleKeyword = keywords.Length > 0 ? keywords.First() : key;
                return queryBuilder.Where("(book_title LIKE @ParamN OR book_content LIKE @ParamN)", $"%{singleKeyword}%");
            }
        }

        private QueryBuilder AddDaysSearchCondition(string key, QueryBuilder queryBuilder)
        {
            if (!WapTool.IsNumeric(key))
            {
                key = "0";
            }
            // ✅ 使用安全的参数化查询（暂时简化）
            return queryBuilder.Where("DATEDIFF(dd, book_date, GETDATE()) < @ParamN", DapperHelper.SafeParseLong(key, "天数"));
        }
    }
}