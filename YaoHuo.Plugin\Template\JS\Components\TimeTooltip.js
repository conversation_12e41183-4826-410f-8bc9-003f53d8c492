/**
 * 统一的时间Tooltip组件
 * 基于JSON配置系统，提供一致的友好时间显示功能
 * 
 * 使用方法：
 * 1. 在HTML元素上添加属性：
 *    data-detail-time="2025-06-12 14:46:18"
 *    onmouseenter="TimeTooltip.show(this)"
 *    onmouseleave="TimeTooltip.hide(this)"
 * 
 * 2. 确保页面加载了此JS文件：
 *    <script src="/Template/JS/Components/TimeTooltip.js"></script>
 */

class TimeTooltip {
    static config = null;
    static currentTooltip = null;

    /**
     * 初始化配置
     */
    static async init() {
        if (this.config) return;
        
        try {
            const response = await fetch('/Data/StaticData/UIComponents.json');
            const data = await response.json();
            this.config = data.timeTooltip;
        } catch (error) {
            this.config = this.getDefaultConfig();
        }
    }

    /**
     * 获取默认配置
     */
    static getDefaultConfig() {
        return {
            enabled: true,
            style: {
                position: "fixed",
                zIndex: 9999,
                backgroundColor: "#1f2937",
                color: "white",
                fontSize: "12px",
                padding: "4px 8px",
                borderRadius: "4px",
                boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
                pointerEvents: "none",
                whiteSpace: "nowrap",
                opacity: 0,
                transform: "translateY(-5px)",
                transition: "opacity 0.2s ease, transform 0.2s ease",
                visibility: "hidden"
            },
            positioning: {
                useRequestAnimationFrame: true,
                preventOverflow: true,
                offsetTop: 8,
                minLeft: 10,
                minTop: 10
            },
            animation: {
                showDelay: 10,
                hideDelay: 200,
                showOpacity: 1,
                showTransform: "translateY(0)",
                hideOpacity: 0,
                hideTransform: "translateY(-5px)"
            }
        };
    }

    /**
     * 显示时间tooltip
     */
    static async show(element) {
        await this.init();
        
        if (!this.config.enabled) return;
        
        const detailTime = element.getAttribute('data-detail-time');
        if (!detailTime) return;

        // 移除已存在的tooltip
        this.hide();

        // 创建tooltip元素
        const tooltip = document.createElement('div');
        tooltip.id = 'timeTooltip';
        
        // 应用样式
        this.applyStyles(tooltip, this.config.style);
        tooltip.textContent = detailTime;

        // 添加到页面
        document.body.appendChild(tooltip);
        this.currentTooltip = tooltip;

        // 定位tooltip
        this.positionTooltip(tooltip, element);
    }

    /**
     * 应用样式到元素
     */
    static applyStyles(element, styles) {
        const styleString = Object.entries(styles)
            .map(([key, value]) => {
                // 转换camelCase到kebab-case
                const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();
                return `${cssKey}: ${value}`;
            })
            .join('; ');
        
        element.style.cssText = styleString;
    }

    /**
     * 定位tooltip
     */
    static positionTooltip(tooltip, element) {
        const config = this.config;
        
        if (config.positioning.useRequestAnimationFrame) {
            // 使用requestAnimationFrame确保DOM完全渲染
            requestAnimationFrame(() => {
                this.calculatePosition(tooltip, element);
                this.showAnimation(tooltip);
            });
        } else {
            // 直接计算位置
            this.calculatePosition(tooltip, element);
            this.showAnimation(tooltip);
        }
    }

    /**
     * 计算tooltip位置
     */
    static calculatePosition(tooltip, element) {
        const config = this.config.positioning;
        
        // 获取触发元素的位置
        const rect = element.getBoundingClientRect();

        if (config.preventOverflow) {
            // 临时显示tooltip以获取准确尺寸
            tooltip.style.visibility = 'visible';
            tooltip.style.opacity = '0';
        }

        // 获取tooltip尺寸
        const tooltipRect = tooltip.getBoundingClientRect();

        // 计算居中位置
        const left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
        const top = rect.top - tooltipRect.height - config.offsetTop;

        // 设置最终位置
        if (config.preventOverflow) {
            tooltip.style.left = `${Math.max(config.minLeft, left)}px`;
            tooltip.style.top = `${Math.max(config.minTop, top)}px`;
        } else {
            tooltip.style.left = `${left}px`;
            tooltip.style.top = `${top}px`;
        }
    }

    /**
     * 显示动画
     */
    static showAnimation(tooltip) {
        const animation = this.config.animation;
        
        setTimeout(() => {
            tooltip.style.opacity = animation.showOpacity;
            tooltip.style.transform = animation.showTransform;
        }, animation.showDelay);
    }

    /**
     * 隐藏时间tooltip
     */
    static hide() {
        if (!this.currentTooltip) return;
        
        const tooltip = this.currentTooltip;
        const animation = this.config?.animation || this.getDefaultConfig().animation;
        
        tooltip.style.opacity = animation.hideOpacity;
        tooltip.style.transform = animation.hideTransform;
        
        setTimeout(() => {
            if (tooltip && tooltip.parentNode) {
                tooltip.parentNode.removeChild(tooltip);
            }
            this.currentTooltip = null;
        }, animation.hideDelay);
    }

    /**
     * 刷新配置
     */
    static async refreshConfig() {
        this.config = null;
        await this.init();
    }
}

// 全局暴露
window.TimeTooltip = TimeTooltip;

// 兼容性：提供全局函数
window.showTimeTooltip = (element) => TimeTooltip.show(element);
window.hideTimeTooltip = () => TimeTooltip.hide();

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', () => {
    TimeTooltip.init();
});
