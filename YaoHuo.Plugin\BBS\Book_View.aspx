﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_View.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Book_View" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%@ Import Namespace="KeLin.ClassManager" %>
<%@ Register Src="~/BBS/Control/GuessingModule.ascx" TagPrefix="uc" TagName="GuessingModule" %>
<%@ Register Src="~/BBS/Control/FaceAndReply.ascx" TagPrefix="uc" TagName="FaceAndReply" %>
<%
    Response.Write(WapTool.showTop(bookVo.book_title, wmlVo));
    if (bookVo.islock == 1)
    {
        content = "";
    }
    string isWebHtml = this.ShowWEB_view(this.classid);
    //显示广告
    if (adVo.threeShowTop != "")
    {
        strhtml.Append(adVo.threeShowTop);
    }
    strhtml.Append("<!--web-->");
    if (bookVo.ischeck == 2)
    {
        strhtml.Append("<div class=\"tip\"><b>此帖已在回收站！</b></div>");
    }
    
    // 输出新版回帖设置变量
    Response.Write(GetNewReplyUIJsVar());
    
    // 添加调试模式控制变量
    Response.Write("<script type=\"text/javascript\">window.debugMode = false;</script>");
    
    // 只在有悬赏或派币时添加通知相关的 CSS 和容器
    if (bookVo.freeMoney > 0 || bookVo.sendMoney > 0)
    {
        strhtml.Append("<link href=\"/NetCSS/BookView/Notification.css\" rel=\"stylesheet\" type=\"text/css\"/>");
        
        // 添加通知容器，默认隐藏
        strhtml.Append("<div class=\"rectangle-container\" style=\"display:none;\"> <div class=\"rectangle\"> <div class=\"notification-text\"> <i class=\"material-icons\"><svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24\" viewBox=\"0 -960 960 960\" width=\"24\"> <path d=\"M440-280h80v-240h-80v240Zm40-320q17 0 28.5-11.5T520-640q0-17-11.5-28.5T480-680q-17 0-28.5 11.5T440-640q0 17 11.5 28.5T480-600Zm0 520q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z\"/> </svg> </i> <span><span style=\"padding-left:3px;\">");

        // 根据情况添加不同的通知文本
        if (bookVo.freeMoney > 0)
        {
            strhtml.Append("派币</span><span style=\"padding:2px;\">" + bookVo.freeMoney + "</span>已结束</span> </div> </div> </div>");
        }
        else // 这里必然是 bookVo.sendMoney > 0
        {
            strhtml.Append("悬赏</span><span style=\"padding:2px;\">" + bookVo.sendMoney + "</span>已结束</span> </div> </div> </div>");
        }
    }

    // 创建链接字符串
    string rewardLink = "<a href=\"" + this.http_start + "bbs/SendMoney_freeMain.aspx?action=sendmoney&amp;classid=" + classid + "&amp;id=" + id + "&amp;touserid=" + bookVo.book_pub + "&amp;siteid=" + this.siteid + "\" class=\"showReward\">打赏</a> ";
    string newPostLink = "<a href=\"/bbs/book_list.aspx?gettotal=2024&action=new" + "\">新帖</a> ";
    string adminLink = "<a href=\"" + this.http_start + "bbs/Book_View_admin.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + this.id + "&amp;lpage=" + this.lpage + "\">管理</a> ";
    string settingsLink = "<a class=\"BBSettings\" href=\"/bbs/settings.aspx\">设置</a>";

    //会员可见
    if (this.IsCheckManagerLvl("02", "") == true)
    {
        strhtml.Append("<div class=\"subtitle2\">");
        strhtml.Append(rewardLink);
        strhtml.Append(newPostLink);
        strhtml.Append(adminLink);
        strhtml.Append(settingsLink);
        strhtml.Append("</div>");
    }
    //管理员可见
    if (this.IsCheckManagerLvl("|00|01|03|04|", "") == true)
    {
        strhtml.Append("<div class=\"subtitle2\">");
        strhtml.Append(rewardLink);
        strhtml.Append(newPostLink);
        strhtml.Append(settingsLink + " ");
        strhtml.Append(adminLink);
        strhtml.Append("</div>");
    }

    strhtml.Append("<div class=\"content\">");
    if (bookVo.sendMoney > 0)
    {
        strhtml.Append("<span class=\"xuanshang\">[悬赏]<span class=\"xuanshangshuzi\" style='padding-left:3px;'>" + bookVo.sendMoney + "</span><span class=\"yishang\" style=\"padding:0 2px 0 12px;\">已赏</span><span class=\"yishangshuzi\">" + bookVo.hasMoney + "</span><br/></span>");
    }
    if (bookVo.freeMoney > 0)
    {
        strhtml.Append("<div class=\"paibi\" style=\"line-height:40px;\"><span class=\"lijin\" style=\"padding-right:0.2em;\">礼金</span><span class=\"lijinshuzi\" style=\"padding-right:0.7em;\">" + bookVo.freeMoney + "</span>");
        //strhtml.Append("方式：");
        string[] freerule = bookVo.freeRule.Split('_');
        string[] free1 = freerule[0].Split('|');
        string[] free2 = freerule[1].Split('|');
        if (free1.Length == 1)
        {
            strhtml.Append("<span class=\"meiren\" style=\"padding-right:0.15em;\">每人</span><span class=\"meirenshuzi\">" + free2[0]);
        }
        else
        {
            for (int y = 0; y < free1.Length; y++)
            {
                strhtml.Append("<br/>楼层:" + free1[y] + "派礼:" + free2[y]);
            }
        }
        strhtml.Append("</span><span class=\"shengyu\" style=\"padding-left:6px;\">(<span class=\"yu\" style=\"padding:.5px;\">余</span><span class=\"yushuzi\" style=\"padding:.5px;\">" + bookVo.freeLeftMoney + "</span>)</span>");
        strhtml.Append("</div>");
    }
    strhtml.Append("<div class=\"Postinfo\"><span class=\"biaotiwenzi\" style='padding-right:3px;'>[标题]</span>" + bookVo.book_title + "<span style='padding-left: 3px;' class=\"yueduliang\">(阅" + bookVo.book_click + ")</span><br/>");
    strhtml.Append("<span class=\"Postime\">[时间]<span class=\"DateAndTime\" style='padding-left:3px;'>" + string.Format("{0:yyyy-MM-dd HH:mm}", bookVo.book_date) + "</span>");
    strhtml.Append("</span>");
    if (bookVo.myGetMoney > 0)
    {
        strhtml.Append("<span class=\"post-badge\" id=\"stamp-badge\" style=\"opacity: 0.8;\">获赏<span class=\"earnbounty\">" + bookVo.myGetMoney + "</span></span>");
    }
    //帖子内容
    strhtml.Append("</div><div class=\"dashed\"></div><div class=\"bbscontent\">");
    strhtml.Append(content + "</div></div></div>");

    //分页显示
    strhtml.Append(linkURL);
    //全局CSS样式（打赏盖章、打赏弹窗、下拉表情、搜索弹窗）
    strhtml.Append("<link href=\"/NetCSS/BookView/ALL.css\" rel=\"stylesheet\" type=\"text/css\"/>");
    strhtml.Append("<link href=\"/NetCSS/BookView/NewReply.css\" rel=\"stylesheet\" type=\"text/css\"/>");
    strhtml.Append("<link href=\"/NetCSS/CSS/BBS/Popup-Base.css\" rel=\"stylesheet\" type=\"text/css\"/>");

    //显示竞猜模块
    if (guessingData != null)
    {
        %><uc:GuessingModule runat="server" ID="GuessingModuleControl" /><%
    }

    //显示投票选项
    if (bookVo.isVote == 1)
    {
        // 计算总票数
        int totalVotes = 0;
        if (vlistVo != null)
        {
            foreach (var vote in vlistVo)
            {
                totalVotes += (int)vote.voteClick;
            }
        }

        strhtml.Append("<div class=\"vote-container\">");
        // 修复类型转换问题 - 使用变量而不是直接在三元表达式中混合类型
        string participantsCount = totalVotes.ToString();
        strhtml.Append("<div class=\"vote-title\">单选投票: 共有" + participantsCount + "人参与</div>");
        
        for (int i = 0; (vlistVo != null && i < vlistVo.Count); i++)
        {
            if (!hasVotedAlready)
            {
                // 投票前的界面 - 使用更小的按钮并隐藏票数
                strhtml.Append("<div class='vote-option-row'>");
                strhtml.Append("<div class='vote-option-text'>" + vlistVo[i].voteTitle + "</div>");
				strhtml.Append("<span class='VON" + (i + 1) + "'style='display: none;'>(" + vlistVo[i].voteClick + ")</span>");
                strhtml.Append("<a href=\"" + http_start + "bbs/book_view_toVote.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;vid=" + vlistVo[i].vid + "&amp;vpage=" + CurrentPage + "&amp;lpage=" + lpage + "&amp;id=" + id + "\" data-vote-index='" + (i + 1) + "' class='vote-button'>投票</a>");
                strhtml.Append("</div>");
            }
            else
            {
                // 计算百分比
                double percentage = totalVotes > 0 ? (double)vlistVo[i].voteClick / totalVotes * 100 : 0;
                percentage = Math.Round(percentage, 2);
                
                // 投票后的界面 - 图表样式
                strhtml.Append("<div class=\"vote-option-result\">");
                strhtml.Append("<div class=\"vote-option-label\">" + vlistVo[i].voteTitle + "</div>");
                strhtml.Append("<div class=\"vote-chart-container\">");
                strhtml.Append("<div class=\"vote-chart-bar\" style=\"width: " + percentage + "%\"></div>");
                strhtml.Append("<div class=\"vote-chart-text\">" + percentage + "% (" + vlistVo[i].voteClick + ")</div>");
                strhtml.Append("</div>");
                strhtml.Append("</div>");
            }
        }
        
        // 投票后的提示信息
        if (hasVotedAlready)
        {
            strhtml.Append("<div class=\"vote-message\">您已经投过票，谢谢您的参与</div>");
        }
        strhtml.Append("</div>");

        // 投票免刷新脚本
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookView/VoteOptimize.js\" defer></script>");
        
        // 给页面添加投票状态标记，供JS使用
        strhtml.Append("<script>document.body.setAttribute('data-has-voted', '" + (hasVotedAlready ? "true" : "false") + "');</script>");
    }
    //显示结束或锁定
    if (bookVo.whylock != "")
    {
        strhtml.Append("<div class=\"tipmini\">");
        bookVo.whylock = bookVo.whylock.Replace("<br/>", "|");
        string[] arry = bookVo.whylock.Split('|');
        bookVo.whylock = arry[0];
        strhtml.Append(bookVo.whylock);
        strhtml.Append("");
        //if (arry.Length > 2)
        //{
            //strhtml.Append("<br/><a href=\"" + this.http_start + "bbs/Book_View_log.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + this.id + "&amp;lpage=" + this.lpage + "\">更多&gt;&gt;</a>");
        //}
        strhtml.Append("</div>");
    }
    //显示楼主信息
    strhtml.Append("<div class=\"louzhuxinxi subtitle\">");
    strhtml.Append("<span class='louzhu'>[楼主]<span style='padding-right:2px;'></span><span class='louzhunicheng'><a href=\"" + this.http_start + "bbs/userinfo.aspx?touserid=" + bookVo.book_pub + "\">" + WapTool.GetColorNickName(toUserVo.idname, toUserVo.nickname, lang, ver) + "</a></span><span class='louzhudengji' style='color:#3d68a8;font-size:15px;padding-right:1px;'>(<span class='dengji'>" + WapTool.GetLevl(siteVo.lvlNumer, toUserVo.expr, toUserVo.money, type) + "</span><span style='display: none' class='touxian'>" + WapTool.GetHandle(siteVo.lvlNumer, toUserVo.expr, toUserVo.money, type) + "</span>)</span><span class='online'>" + WapTool.GetOnline(http_start, toUserVo.isonline, toUserVo.sex.ToString()) + WapTool.GetOLtimePic(this.http_start, siteVo.lvlTimeImg, toUserVo.LoginTimes) + "</span><br/></span>");
    strhtml.Append("<span class='xunzhang'><a style='padding: 0; color: #000000;padding-right:5px;text-decoration: none;' class='rongyuwenzi'>[荣誉]</a><span class='xunzhangtupian'>" + WapTool.GetMedal(toUserVo.userid.ToString(), toUserVo.moneyname, WapTool.GetSiteDefault(siteVo.Version, 47), wmlVo) + "</span><br/></span>");
    //会员可见
    if (this.IsCheckManagerLvl("|00|01|02|03|04|", "") == true)
    {
        strhtml.Append("[操作]<span style='padding-right:2px;'></span><a href=\"" + this.http_start + "bbs/Book_View_admin.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + this.id + "&amp;lpage=" + this.lpage + "\">管理</a> <a href=\"" + this.http_start + "bbs/Report_add.aspx?siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;page=" + this.lpage + "&amp;id=" + this.id + "\">举报</a> <a class=\"js-confirm-action\" data-confirm-message=\"确定要收藏吗？\" href=\"" + this.http_start + "bbs/Share.aspx?action=fav&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;id=" + this.id + "\">收藏</a> ");
        strhtml.Append("<a href=\"" + this.http_start + "bbs/SendMoney_freeMain.aspx?action=sendmoney&amp;classid=" + classid + "&amp;id=" + id + "&amp;touserid=" + bookVo.book_pub + "&amp;siteid=" + this.siteid + "\" class=\"showReward\">打赏</a><br/>");

        //打赏弹窗内容 - 使用新的 popup-base 结构
        strhtml.Append("<div id='RewardCoins' class='popup-overlay'>");
        strhtml.Append("<div class='popup-container'>");
        strhtml.Append("<header class='popup-header'>");
        strhtml.Append("打赏楼主");
        // 添加新的关闭按钮结构
        strhtml.Append("<button class='popup-close-btn'>");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 384 512\" width=\"18\" height=\"18\" fill=\"currentColor\" stroke=\"currentColor\" stroke-width=\"20\"><path d=\"M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7 86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256 41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3 297.4 406.6c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256 342.6 150.6z\"/></svg>");
        strhtml.Append("</button>");
        strhtml.Append("</header>");
        strhtml.Append("<div class='popup-content-area'>");
        strhtml.Append("<div class='aui-recharge-box'><div class='aui-cell-box'><form name='send' action='/bbs/sendmoney_freeMain.aspx' method='post'><div class='tip'></div><p class='info-text'>余额<span id=\"balance\" class=\"space\">" + userVo.money + "</span><span class='space'></span>妖晶</p><div class='info-text' id='type-amount'>请选择打赏数量</div><div class='aui-grids'><button type='button' class='aui-grids-item this-card' value='101'><span>100</span></button><button type='button' class='aui-grids-item' value='303'><span>300</span></button><button type='button' class='aui-grids-item' value='525'><span>520</span></button><button type='button' class='aui-grids-item' value='673'><span>666</span></button><button type='button' class='aui-grids-item' value='2357'><span>2333</span></button><button type='button' class='aui-grids-item' value='5050'><span>5000</span></button><button type='button' class='aui-grids-item' value='6733'><span>6666</span></button><button type='button' class='aui-grids-item' value='8978'><span>8888</span></button><button type='button' class='aui-grids-item' value='10101'><span>10000</span></button></div>");
        strhtml.Append("<input type=\"hidden\" name=\"sendmoney\" value=\"101\" /><input type=\"hidden\" name=\"action\" value=\"gomod\" /><input type=\"hidden\" name=\"id\" value=\"" + id + "\" /><input type=\"hidden\" name=\"classid\" value=\"" + classid + "\" /><input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\" /><input type=\"hidden\" name=\"touserid\" value=\"" + bookVo.book_pub + "\" /><input type=\"hidden\" name=\"myuserid\" value=\"" + userVo.userid + "\" /><button type=\"submit\" name=\"s\" class=\"givebtn\">确定打赏</button>");
        strhtml.Append("</form></div></div>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");

        //显示上一页下一页
        strhtml.Append(preNextTitle);
        //显示最新回复
        strhtml.Append("<div class=\"viewContent\">");
        if (bookVo.islock == 0)
        {
        %><uc:FaceAndReply runat="server" ID="FaceAndReply" /><%
        }
        //全部回复列表
        strhtml.Append("<div style=\"padding: 7px;\" class=\"recontent\">");
        for (int i = 0; (relistVo != null && i < relistVo.Count); i++)
        {
            // 获取原始楼层号
            int originalFloor = relistVo[i].OriginalFloor;
            if (relistVo[i].BookTop == 1)
            {
                strhtml.Append("<div class=\"reline list-reply\" data-floor=\"" + originalFloor + "\">\n<span class='dinglouwenzi'>[<span class=\"floornumber0\" title=\"原" + originalFloor + "楼\">顶楼</span>]</span>");
            }
            else
            {
                strhtml.Append("<div class=\"reline list-reply\" data-floor=\"" + originalFloor + "\">[<span class=\"floornumber\">" + originalFloor + "</span><span>楼</span>]");
            }

            if (relistVo[i].MyGetMoney > 0)
            {
                strhtml.Append("<span class=\"remoney\">[<b>得金<span class=\"rewardnumber\">" + relistVo[i].MyGetMoney + "</span></b>]</span>");
            }

            if (this.userid != relistVo[i].UserId.ToString())
            {
                strhtml.Append("[<a class=\"replyicon\" href=\"" + this.http_start + "bbs/Book_re.aspx?siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;vpage=" + this.CurrentPage + "&amp;reply=" + originalFloor + "&amp;id=" + this.id + "&amp;touserid=" + relistVo[i].UserId + "\">回</a>]");
            }
            // 自己的回复不显示[回]按钮，自己回复自己不太合理

            if (this.userid == bookVo.book_pub && bookVo.sendMoney > 0 && bookVo.sendMoney != bookVo.hasMoney && bookVo.book_pub != relistVo[i].UserId.ToString())
            {
                strhtml.Append("<span class=\"remanage\">[<a class=\"giveicon\" href=\"" + this.http_start + "bbs/SendMoney.aspx?action=sendmoney&amp;classid=" + classid + "&amp;id=" + id + "&amp;reid=" + relistVo[i].Id + "&amp;siteid=" + this.siteid + "\">赏分</a>]</span>");
            }

            if (this.IsCheckManagerLvl("|00|01|03|04|", classVo.adminusername))
            {
                strhtml.Append("<span style='display:none' class=\"admin-remanage\">");
                if (this.userid == relistVo[i].UserId.ToString())
                {
                    strhtml.Append("[<a class='user-remanage delete-myfloor' href=\"" + this.http_start + "bbs/Book_re_del.aspx?action=go&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;page=" + this.CurrentPage + "&amp;reid=" + relistVo[i].Id + "&amp;id=" + this.id + "\">删</a>]");
                    strhtml.Append("[<a class='floordeladmin drop-down' href=\"" + this.http_start + "bbs/Book_re_del.aspx?action=go&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;page=" + this.CurrentPage + "&amp;reid=" + relistVo[i].Id + "&amp;id=" + this.id + "\">删</a>]");
                }
                else
                {
                    strhtml.Append("[<a class='floordeladmin drop-down' href=\"" + this.http_start + "bbs/Book_re_del.aspx?action=go&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;page=" + this.CurrentPage + "&amp;reid=" + relistVo[i].Id + "&amp;id=" + this.id + "\">删</a>]");
                }
                strhtml.Append("[<a class='floorgive drop-down' href=\"" + this.http_start + "bbs/SendMoney_free.aspx?action=sendmoney&amp;classid=" + classid + "&amp;id=" + id + "&amp;reid=" + relistVo[i].Id + "&amp;touserid=" + relistVo[i].UserId + "&amp;siteid=" + this.siteid + "\">送</a>]");
                strhtml.Append("[<a class='floorchange drop-down' href=\"" + this.http_start + "bbs/Book_re_mod.aspx?action=go&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;page=" + this.CurrentPage + "&amp;reid=" + relistVo[i].Id + "&amp;id=" + this.id + "\">审</a>]");
                if (relistVo[i].BookTop == 1)
                {
                    strhtml.Append("[<a class='floortop drop-down' href=\"" + this.http_start + "bbs/Book_re_top.aspx?action=go&amp;tops=0&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;page=" + this.CurrentPage + "&amp;reid=" + relistVo[i].Id + "&amp;id=" + this.id + "\">消顶</a>]</span>");
                }
                else
                {
                    strhtml.Append("[<a class='floortop drop-down' href=\"" + this.http_start + "bbs/Book_re_top.aspx?action=go&amp;tops=1&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;page=" + this.CurrentPage + "&amp;reid=" + relistVo[i].Id + "&amp;id=" + this.id + "\">顶</a>]</span>");
                }
            }
            else if (this.userid == relistVo[i].UserId.ToString())
            {
                strhtml.Append("<span class=\"user-remanage\">[<a class='delete-myfloor' href=\"" + this.http_start + "bbs/Book_re_del.aspx?action=go&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;page=" + this.CurrentPage + "&amp;reid=" + relistVo[i].Id + "&amp;id=" + this.id + "\">删</a>]</span>");
            }

            strhtml.Append("<span class=\"renick\"><a href=\"" + this.http_start + "bbs/userinfo.aspx?touserid=" + relistVo[i].UserId + "\">" + YaoHuo.Plugin.Tool.BBSHelper.FormatColoredNickname(relistVo[i].UserId, relistVo[i].Nickname, this.userListVo_IDName, this.lang, this.ver) + "</a></span><span style='display:none'>(<a class=\"reidlink\" href=\"" + this.http_start + "bbs/book_re.aspx?action=class&id=" + this.id + "&classid=" + this.classid + "&mainuserid=" + relistVo[i].UserId + "\"><span class=\"renickid\">" + relistVo[i].UserId + "</span>)</a></span></span>");

            if (relistVo[i].ReplyToFloor != 0)
            {
                strhtml.Append("<span class=\"reother\">回复<a href=\"/bbs/book_re.aspx?classid=" + this.classid + "&id=" + this.id + "&tofloor=" + relistVo[i].ReplyToFloor + "&page=1#floor-" + relistVo[i].ReplyToFloor + "\">" + relistVo[i].ReplyToFloor + "楼</a></span>");
            }
            strhtml.Append("<span class=\"recolon\">:</span>");
            strhtml.Append("<span class=\"retext\">");
            strhtml.Append(relistVo[i].Content);
            if (relistVo[i].AttachCount > 0)
            {
                strhtml.Append("{<a href=\"" + this.http_start + "bbs/book_re_addfileshow.aspx?siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;id=" + this.id + "&amp;reid=" + relistVo[i].Id + "&amp;lpage=" + this.lpage + "\">查看" + relistVo[i].AttachCount + "个附件</a>}");
            }
            strhtml.Append("</span>");
            strhtml.Append("<span style=\"color: #404040; font-size: 15px; padding-left: 5px;\"><span class=\"redate\">(<span class=\"retime\">" + string.Format("{0:MM-dd HH:mm}", relistVo[i].ReplyDate) + "</span>)</span></span></div>");
        }
        if (relistVo == null)
        {
            strhtml.Append("<span style=\"margin-left: 5px;opacity:0.7;\">暂时木有回复，快抢沙发哦！</span></div>");
        }
        else
        {
            strhtml.Append("</div>");
            strhtml.Append("<div class=\"more\">");
            strhtml.Append("<a class=\"noafter\" href=\"" + http_start + "bbs/book_re.aspx?lpage=" + this.lpage + "&amp;getTotal=" + this.FilteredReplyCount + "&amp;id=" + id + "&amp;classid=" + classid + "&amp;siteid=" + siteid + "\">全部回帖(" + this.FilteredReplyCount + ")</a>");
            strhtml.Append("</div>");
        }
        strhtml.Append("</div>");
        //全部回复结束

        //补充CSS样式
        strhtml.Append("<style>.sticky b {margin-left: 8px; margin-right: -2px; }.line1,.line2{line-height:40px;padding:8px 0px 10px 0;}</style>");

        //显示电脑版
        if (isWebHtml != "")
        {
            string strhtml_list = strhtml.ToString();
            int s = strhtml_list.IndexOf("<!--web-->");
            strhtml_list = strhtml_list.Substring(s, strhtml_list.Length - s);
            Response.Clear();
            Response.Write(WapTool.ToWML(isWebHtml.Replace("[view]", strhtml_list), wmlVo));
            Response.End();
        }
        //显示广告
        if (adVo.threeShowDown != "")
        {
            strhtml.Append(adVo.threeShowDown);
        }
        if (downLink != "")
        {
            strhtml.Append(downLink);
        }
        //页面脚本合集

        // 使用 string.Format 替代内插字符串
        string rewardConfigScript = string.Format("\n<script type=\"text/javascript\">\n    window.rewardPageConfig = {{\n        id: {0},\n        classid: {1},\n        siteid: {2},\n        touserid: {3},\n        myuserid: {4},\n        currentUserBalance: {5},\n        formActionUrl: '{6}'\n    }};\n</script>", 
            id, 
            classid, 
            siteid, 
            bookVo.book_pub, 
            (userVo != null ? userVo.userid.ToString() : "0"), 
            (userVo != null ? userVo.money.ToString() : "0"), 
            "/bbs/sendmoney_freeMain.aspx"
        );
        strhtml.Append(rewardConfigScript);

        // 只有在悬赏或派币存在时才加载 ItemDisplay.js
        if (bookVo.sendMoney > 0 || bookVo.freeMoney > 0)
        {
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookView/ItemDisplay.js\"></script>");
        }
        
        // 🔧 新增：派币帖验证码资源条件性加载
        if (ShouldShowCaptchaForFreeMoneyPost())
        {
            // 设置全局变量
            strhtml.Append("<script>window.REQUIRES_CAPTCHA = true;</script>");
            strhtml.Append("<script>window.CURRENT_USER_ID = " + base.userid + ";</script>");
            strhtml.Append("<script>window.BOOK_FREE_MONEY = " + bookVo.freeMoney + ";</script>");

            // 添加验证码模态框HTML结构
            strhtml.Append("<div id='gocaptcha-wrap' style='width: 100%; margin: 0 auto;'></div>");

            // 🔧 修复：为AJAX提交添加验证码令牌字段（在页面任意位置，JavaScript可以访问）
            strhtml.Append("<input type='hidden' id='gocaptcha-token' name='gocaptchaToken' value='' />");

            // 加载专用验证码模块
            strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BBS/FreeMoneyPostCaptcha.js?v2.2.2\"></script>");

            // 加载GoCaptcha资源（复用现有）
            strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/CSS/Login/Gocaptcha/gocaptcha-init.js?v1.0.2\"></script>");
            strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/CSS/Login/Gocaptcha/gocaptcha.global.js?v1.0.2\"></script>");
            strhtml.Append("<link href=\"/NetCSS/CSS/Login/Gocaptcha/gocaptcha.global.css?v1.0.2\" rel=\"stylesheet\" />");
            strhtml.Append("<link href=\"/NetCSS/CSS/Login/Gocaptcha/gocaptcha-modal.css?v1.0.2\" rel=\"stylesheet\" />");

            // 设置 Turnstile Site Key 全局变量，供备用验证码使用
            strhtml.Append("<script>window.TURNSTILE_SITE_KEY = '" + PubConstant.GetAppString("CloudflareTurnstileSiteKey") + "';</script>");
        }
        else
        {
            // 不需要验证码时设置标识
            strhtml.Append("<script>window.REQUIRES_CAPTCHA = false;</script>");
        }

        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/Shared/ReplyForm.js?v1.0.0\"></script>");
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/Shared/NewReplyUI.js?v1.0.0\"></script>");
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/Shared/QuickReplyAjax.js?v1.0.3\"></script>");
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/Shared/DomHelpers.js?v1.0.0\" defer></script>");
                                                                      
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/KL_Common.js\"></script>");
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/HyperLink.js\" defer></script>");
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookView/Reward.js\" defer></script>");
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookView/HideInfo.js\" defer></script>");
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookView/BookViewScript.js\"></script>");
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookView/IframePopupManager.js?1\" defer></script>");
                                                                      
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookView/Emoji.js\" defer></script>");
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookView/Attach.js\" defer></script>");
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookView/AtUserID.js\" defer></script>");
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookView/ImageStyle.js\" defer></script>");
    }
    //会员可见结束
    strhtml.Append(WapTool.GetVS(wmlVo));
    strhtml.Append(ERROR);
    Response.Write(WapTool.ToWML(strhtml.ToString(), wmlVo));
    Response.Write(WapTool.showDown(wmlVo));
%>