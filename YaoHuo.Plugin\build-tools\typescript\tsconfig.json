{"compilerOptions": {"target": "ES2020", "module": "ES2020", "moduleResolution": "node", "lib": ["DOM", "ES2020"], "outDir": "../../Template/TS", "rootDir": "./src", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": false, "removeComments": true, "sourceMap": false, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": true}, "include": ["src/**/*", "types/**/*"], "exclude": ["node_modules", "../../Template"]}