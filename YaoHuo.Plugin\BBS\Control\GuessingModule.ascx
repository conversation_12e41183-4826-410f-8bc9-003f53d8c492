﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="GuessingModule.ascx.cs" Inherits="YaoHuo.Plugin.BBS.Control.GuessingModule" %>
<div class="Betting-widget">
    <div class="Betting-container bg-white rounded-xl shadow-md overflow-hidden">
        <div class="flex justify-between items-start mb-4">
            <h1 class="Betting-title">猜对即可分妖晶</h1>
            <div class="Betting-points">可用妖晶 <span class="Betting-points-value"><%= UserVo.money %></span><a style="color: black;font-size: 0.7em;margin-left: -1px;" href="/chinabank_wap/RMBtoMoney.aspx"> &gt;</a></div>
        </div>

        <div class="mb-6">
            <div class="flex items-center justify-between mb-2">
                <span class="Betting-pool-label"><span class="pool-text">奖池</span><span class="have-text">共有</span></span>
                <div class="Betting-pool-digits">
                    <% string poolAmount = TotalAmount.ToString("********");
                       foreach (char digit in poolAmount)
                       { %>
                        <span class="Betting-pool-digit"><%= digit %></span>
                    <% } %>
                </div>
                <span class="Betting-pool-label righttext"></span>
            </div>

            <% int option1Percentage = TotalAmount > 0 ? (int)((double)GuessingData.Options[0].Amount / TotalAmount * 100) : 50;
               int option2Percentage = 100 - option1Percentage; %>
            <div class="relative pt-1">
                <div class="flex mb-2 items-center justify-between">
                    <div class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-white bg-red-500"><%= option1Percentage %>%</div>
                    <div class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-white bg-green-500"><%= option2Percentage %>%</div>
                </div>
                <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-gray-200">
                    <div style='<%="width:" + option1Percentage + "%"%>' class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-red-500 to-red-600"></div>
                    <div style='<%="width:" + option2Percentage + "%"%>' class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-green-500 to-green-600"></div>
                </div>
            </div>
        </div>

        <div class="text-xl font-bold mb-4"><%= GuessingData.Title %></div>

        <% if (!GuessingData.IsClosed)
           { %>
            <% if (UserBet == null)
               { %>
                <div class="flex justify-between mb-8">
                    <% for (int i = 0; i < GuessingData.Options.Count; i++)
                       {
                           var option = GuessingData.Options[i];
                           string voteUrl = HttpStart + "bbs/GuessVote.aspx?id=" + GuessingData.Id + "&option=" + HttpUtility.UrlEncode(option.Text); %>
                        <button class="Betting-prediction-button <%= i == 0 ? "rise" : "fall" %>" data-vote-url="<%= voteUrl %>">
                            <%= option.Text %>
                        </button>
                    <% } %>
                </div>
            <% }
               if (UserBet != null)
               { %>
                <div class="user-bet-info mb-4">
                    <button class="Betting-user-bet-button <%= UserBet.OptionId == 1 ? " rise" : "fall" %>" disabled>
                        您已竞猜<span class="mybet-amount"><%= UserBet.Amount.ToString("F0") %></span><%= HttpUtility.HtmlEncode(GuessingData.Options[UserBet.OptionId - 1].Text) %>
                    </button>
                </div>
            <% } %>

            <div class="Betting-countdown text-center text-gray-700">距<span class="Betting-date-time-group"><%= GuessingData.Deadline.Month.ToString("00") %></span>月<span class="Betting-date-time-group"><%= GuessingData.Deadline.Day.ToString("00") %></span>日<span class="Betting-date-time-group"><%= GuessingData.Deadline.Hour.ToString("00") %></span>时截止下注还有 
                <span class="Betting-countdown-digit countdown-days">00</span> 天 
                <span class="Betting-countdown-digit countdown-hours">00</span> 时 
                <span class="Betting-countdown-digit countdown-minutes">00</span> 分 
                <span class="Betting-countdown-digit countdown-seconds">00</span> 秒
            </div>
        <% }
           else
           { %>
            <div class="user-bet-info mb-4">
                <% if (GuessingData != null && GuessingData.IsClosed)
                   {
                       string winningClass = WinningOptionId == 1 ? "rise" : "fall"; %>
                    <button class="Betting-user-bet-button <%= winningClass %>">
                        <%= WinningOptionId.HasValue && !string.IsNullOrEmpty(WinningOptionText) ? "竞猜正确答案：" + WinningOptionText : "竞猜已结束，请等待获胜结果" %>
                    </button>
                <% } %>
            </div>
            <div class="Betting-TipText">奖池将按投注比例分配给预测正确的玩家</div>
        <% } %>
    </div>
</div>

<div id="betting-dialog" class="Betting-dialog-overlay" style="display:none">
    <div class="Betting-dialog-content">
        <div id="dialogTitle" class="Betting-dialog-title"></div>
        <div class="Betting-points-grid">
            <% for (int i = 0; i < 9; i++)
               { %>
                <button class="Betting-points-button"></button>
            <% } %>
        </div>
        <div class="Betting-dialog-footer">可用 <span id="available-points"><%= UserVo.money %></span> 妖晶</div>
        <button id="confirmButton" class="Betting-confirm-button"></button>
    </div>
</div>

<div id="custom-alert" class="Betting-alert-overlay" style="display:none;">
    <div class="Betting-alert-content">
        <div id="alert-message" class="Betting-alert-message"></div>
        <button id="alert-close" class="Betting-alert-close">确定</button>
    </div>
</div>

<link rel="stylesheet" href="/NetCSS/CSS/BBS/Betting.css" type="text/css" />
<script src="/NetCSS/JS/BookView/Guess/Betting.js"></script>
<script src="/NetCSS/JS/BookView/Guess/Confetti.Browser.min.js"></script>
<script src="/NetCSS/JS/BookView/Guess/Celebration.js"></script>
<script type="text/javascript">
(function () {
    var config = {
        guessingTitle: '<%= GuessingData.Title %>',
        option1Text: '<%= GuessingData.Options[0].Text %>',
        option2Text: '<%= GuessingData.Options[1].Text %>',
        deadline: new Date('<%= GuessingData.Deadline.ToString("yyyy-MM-ddTHH:mm:ss") %>'),
        isClosed: <%= GuessingData.IsClosed.ToString().ToLower() %>,
        userBetOption: '<%= UserBet != null ? GuessingData.Options[UserBet.OptionId - 1].Text : "" %>',
        winningOption: '<%= WinningOptionId.HasValue ? GuessingData.Options[(int)(WinningOptionId.Value - 1)].Text : "" %>',
        totalAmount: <%= TotalAmount %>
    };
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function () {
                initBetting(config);
            });
        } else {
            initBetting(config);
        }
    })();
</script>