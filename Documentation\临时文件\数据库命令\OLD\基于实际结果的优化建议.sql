-- 基于您的实际配置结果的优化建议
-- 执行日期：2025-06-15

-- 1. 立即可执行的优化配置
EXEC sp_configure 'show advanced options', 1;
RECONFIGURE;

-- 启用备份压缩（当前为0，建议启用）
EXEC sp_configure 'backup compression default', 1;
RECONFIGURE;

-- 提高并行成本阈值（当前为5，建议25-50）
EXEC sp_configure 'cost threshold for parallelism', 25;
RECONFIGURE;

-- 启用临时查询优化（当前为0，建议启用）
EXEC sp_configure 'optimize for ad hoc workloads', 1;
RECONFIGURE;

-- 启用远程管理连接（当前为0，建议启用）
EXEC sp_configure 'remote admin connections', 1;
RECONFIGURE;

GO

-- 2. 数据库级别优化
USE Clover;
GO

-- 启用异步统计信息更新（当前为0，建议启用）
ALTER DATABASE Clover SET AUTO_UPDATE_STATISTICS_ASYNC ON;

-- 将页面验证从TORN_PAGE_DETECTION升级到CHECKSUM（更强的数据完整性检查）
ALTER DATABASE Clover SET PAGE_VERIFY CHECKSUM;

GO

-- 3. TempDB优化建议
-- 您的TempDB配置已经很好（8个文件），但可以考虑调整大小
USE master;
GO

-- 当前TempDB数据文件只有256MB，建议增加到512MB-1GB
-- 在维护窗口执行以下命令：
/*
ALTER DATABASE tempdb MODIFY FILE (NAME = 'tempdev', SIZE = 1024MB);
ALTER DATABASE tempdb MODIFY FILE (NAME = 'temp2', SIZE = 1024MB);
ALTER DATABASE tempdb MODIFY FILE (NAME = 'temp3', SIZE = 1024MB);
ALTER DATABASE tempdb MODIFY FILE (NAME = 'temp4', SIZE = 1024MB);
ALTER DATABASE tempdb MODIFY FILE (NAME = 'temp5', SIZE = 1024MB);
ALTER DATABASE tempdb MODIFY FILE (NAME = 'temp6', SIZE = 1024MB);
ALTER DATABASE tempdb MODIFY FILE (NAME = 'temp7', SIZE = 1024MB);
ALTER DATABASE tempdb MODIFY FILE (NAME = 'temp8', SIZE = 1024MB);

-- TempDB日志文件从4GB减少到1GB（通常不需要这么大）
ALTER DATABASE tempdb MODIFY FILE (NAME = 'templog', SIZE = 1024MB);
*/

-- 4. 等待统计分析
-- 基于您的等待统计结果，主要等待类型分析：

-- SOS_WORK_DISPATCHER: 工作调度等待，正常
-- HADR_FILESTREAM_IOMGR_IOCOMPLETION: 文件流相关，如果不使用FileStream可以忽略
-- DIRTY_PAGE_POLL: 脏页轮询，正常
-- FT_IFTSHC_MUTEX: 全文搜索相关，您有全文索引所以正常
-- QDS_ASYNC_QUEUE: Query Store异步队列，正常
-- IO_COMPLETION: I/O完成等待，需要关注

-- 检查I/O性能
SELECT 
    DB_NAME(database_id) AS database_name,
    file_id,
    io_stall_read_ms,
    num_of_reads,
    CASE WHEN num_of_reads = 0 THEN 0 ELSE io_stall_read_ms / num_of_reads END AS avg_read_stall_ms,
    io_stall_write_ms,
    num_of_writes,
    CASE WHEN num_of_writes = 0 THEN 0 ELSE io_stall_write_ms / num_of_writes END AS avg_write_stall_ms
FROM sys.dm_io_virtual_file_stats(NULL, NULL)
WHERE database_id > 4  -- 排除系统数据库
ORDER BY avg_read_stall_ms DESC;

-- 5. 性能监控查询
-- 查看当前缓存中最耗CPU的查询
SELECT TOP 10
    qs.execution_count,
    qs.total_worker_time / 1000 AS total_cpu_ms,
    qs.total_worker_time / qs.execution_count / 1000 AS avg_cpu_ms,
    qs.total_elapsed_time / qs.execution_count / 1000 AS avg_elapsed_ms,
    qs.total_logical_reads / qs.execution_count AS avg_logical_reads,
    qs.creation_time,
    qs.last_execution_time,
    SUBSTRING(st.text, (qs.statement_start_offset/2)+1, 
        ((CASE qs.statement_end_offset 
            WHEN -1 THEN DATALENGTH(st.text)
            ELSE qs.statement_end_offset 
        END - qs.statement_start_offset)/2) + 1) AS statement_text
FROM sys.dm_exec_query_stats qs
CROSS APPLY sys.dm_exec_sql_text(qs.sql_handle) st
WHERE qs.execution_count > 10  -- 只看执行次数较多的查询
ORDER BY qs.total_worker_time / qs.execution_count DESC;

-- 6. 内存使用情况检查
SELECT 
    counter_name,
    cntr_value,
    CASE counter_name
        WHEN 'Buffer cache hit ratio' THEN 
            CASE WHEN cntr_value > 95 THEN '优秀' 
                 WHEN cntr_value > 90 THEN '良好' 
                 ELSE '需要关注' END
        WHEN 'Page life expectancy' THEN 
            CASE WHEN cntr_value > 300 THEN '优秀' 
                 WHEN cntr_value > 180 THEN '良好' 
                 ELSE '需要关注' END
        ELSE ''
    END AS 评估
FROM sys.dm_os_performance_counters
WHERE counter_name IN (
    'Buffer cache hit ratio',
    'Page life expectancy',
    'Lazy writes/sec',
    'Checkpoint pages/sec'
)
AND object_name LIKE '%Buffer Manager%';

-- 7. 计划缓存使用情况
SELECT 
    objtype,
    COUNT(*) AS plan_count,
    SUM(size_in_bytes) / 1024 / 1024 AS size_mb,
    SUM(usecounts) AS total_use_count,
    AVG(usecounts) AS avg_use_count
FROM sys.dm_exec_cached_plans
GROUP BY objtype
ORDER BY size_mb DESC;
