# TimeTooltip 统一时间提示组件使用指南

## 📋 概述

TimeTooltip 是一个基于JSON配置系统的统一时间提示组件，用于在 UserInfo.hbs 和 UserGuessBook.hbs 等页面中提供一致的友好时间显示功能。

## 🚀 快速开始

### 1. 引入组件
在页面模板中引入JS文件：
```html
<script src="/Template/JS/Components/TimeTooltip.js"></script>
```

> **注意**: 组件已从 `/NetCSS/JS/Common/` 迁移到 `/Template/JS/Components/`，请使用新路径。

### 2. HTML使用方式
```html
<span class="text-xs text-gray-500 cursor-pointer"
      data-detail-time="{{DetailTime}}"
      onmouseenter="TimeTooltip.show(this)"
      onmouseleave="TimeTooltip.hide()">{{FriendlyTime}}</span>
```

### 3. 兼容性使用方式
为了保持向后兼容，也支持原有的函数调用方式：
```html
<span class="text-xs text-gray-500 cursor-pointer"
      data-detail-time="{{DetailTime}}"
      onmouseenter="showTimeTooltip(this)"
      onmouseleave="hideTimeTooltip()">{{FriendlyTime}}</span>
```

## 🔧 配置说明

### JSON配置文件位置
`/Data/StaticData/UIComponents.json`

### 配置结构
```json
{
  "timeTooltip": {
    "enabled": true,
    "style": {
      "backgroundColor": "#1f2937",
      "color": "white",
      "fontSize": "12px",
      "padding": "4px 8px",
      "borderRadius": "4px"
    },
    "positioning": {
      "useRequestAnimationFrame": true,
      "preventOverflow": true,
      "offsetTop": 8,
      "minLeft": 10,
      "minTop": 10
    },
    "animation": {
      "showDelay": 10,
      "hideDelay": 200
    }
  }
}
```

## 🎯 功能特性

### 1. 统一的样式和行为
- 所有页面使用相同的tooltip样式
- 一致的动画效果和交互体验
- 统一的定位算法

### 2. 智能定位
- 使用 `requestAnimationFrame` 确保DOM完全渲染
- 防止tooltip超出屏幕边界
- 自动居中对齐

### 3. 配置驱动
- 通过JSON配置文件控制所有行为
- 支持运行时配置刷新
- 降级到默认配置确保可用性

### 4. 向后兼容
- 保持原有的函数调用方式
- 无需修改现有页面代码
- 渐进式升级

## 📖 API 文档

### 静态方法

#### `TimeTooltip.show(element)`
显示时间tooltip
- **参数**: `element` - 触发元素，必须包含 `data-detail-time` 属性
- **返回**: `Promise<void>`

#### `TimeTooltip.hide()`
隐藏当前显示的tooltip
- **参数**: 无
- **返回**: `void`

#### `TimeTooltip.init()`
初始化配置（自动调用）
- **参数**: 无
- **返回**: `Promise<void>`

#### `TimeTooltip.refreshConfig()`
刷新配置文件
- **参数**: 无
- **返回**: `Promise<void>`

### 全局函数（兼容性）

#### `showTimeTooltip(element)`
等同于 `TimeTooltip.show(element)`

#### `hideTimeTooltip()`
等同于 `TimeTooltip.hide()`

## 🔄 迁移指南

### 从现有实现迁移

#### 步骤1：引入组件
在页面头部添加：
```html
<script src="/Template/JS/Components/TimeTooltip.js"></script>
```

#### 步骤2：移除重复代码
删除页面中的 `showTimeTooltip` 和 `hideTimeTooltip` 函数定义

#### 步骤3：保持HTML不变
现有的HTML代码无需修改，组件会自动兼容

### UserInfo.hbs 迁移示例

**迁移前**：
```javascript
// 显示时间tooltip
function showTimeTooltip(element) {
    // ... 大量重复代码
}

// 隐藏时间tooltip  
function hideTimeTooltip() {
    // ... 大量重复代码
}
```

**迁移后**：
```html
<!-- 只需引入组件 -->
<script src="/Template/JS/Components/TimeTooltip.js"></script>
<!-- HTML保持不变 -->
<span onmouseenter="showTimeTooltip(this)" onmouseleave="hideTimeTooltip()">{{FriendlyTime}}</span>
```

## ⚙️ 高级配置

### 自定义样式
修改 `UIComponents.json` 中的 `style` 配置：
```json
{
  "timeTooltip": {
    "style": {
      "backgroundColor": "#2563eb",
      "borderRadius": "8px",
      "fontSize": "14px"
    }
  }
}
```

### 调整定位行为
修改 `positioning` 配置：
```json
{
  "timeTooltip": {
    "positioning": {
      "useRequestAnimationFrame": false,
      "preventOverflow": false,
      "offsetTop": 12
    }
  }
}
```

### 自定义动画
修改 `animation` 配置：
```json
{
  "timeTooltip": {
    "animation": {
      "showDelay": 0,
      "hideDelay": 300,
      "showTransform": "translateY(0) scale(1)",
      "hideTransform": "translateY(-10px) scale(0.95)"
    }
  }
}
```

## 🐛 故障排除

### 配置不生效？
1. 检查JSON文件格式是否正确
2. 确认文件路径 `/Data/StaticData/UIComponents.json` 可访问
3. 调用 `TimeTooltip.refreshConfig()` 刷新配置

### Tooltip不显示？
1. 检查元素是否有 `data-detail-time` 属性
2. 确认JS文件已正确加载
3. 查看浏览器控制台是否有错误信息

### 位置不正确？
1. 检查 `positioning` 配置
2. 确认触发元素的位置和尺寸
3. 调整 `offsetTop`、`minLeft`、`minTop` 参数

## 📊 性能优化

### 配置缓存
- 配置文件只在首次使用时加载
- 支持运行时刷新，无需重新加载页面
- 降级机制确保在配置加载失败时仍可正常工作

### DOM操作优化
- 使用 `requestAnimationFrame` 优化渲染性能
- 智能的tooltip复用机制
- 最小化DOM操作次数

## 🔮 未来扩展

### 计划功能
- 支持更多tooltip类型（信息提示、警告等）
- 主题系统支持
- 国际化支持
- 更丰富的动画效果

### 扩展示例
```javascript
// 未来可能的扩展用法
TimeTooltip.showInfo(element, '这是一个信息提示');
TimeTooltip.showWarning(element, '这是一个警告');
TimeTooltip.showError(element, '这是一个错误提示');
```

## 📞 技术支持

### 相关文件
- 配置文件：`/Data/StaticData/UIComponents.json`
- 组件文件：`/Template/JS/Components/TimeTooltip.js`
- 旧版文件：`/NetCSS/JS/Common/TimeTooltip.js` (已标记为迁移)
- 使用示例：`UserInfo.hbs`、`UserGuessBook.hbs`

### 调试方法
```javascript
// 检查配置是否加载
console.log(TimeTooltip.config);

// 手动刷新配置
await TimeTooltip.refreshConfig();

// 检查当前tooltip状态
console.log(TimeTooltip.currentTooltip);
```
