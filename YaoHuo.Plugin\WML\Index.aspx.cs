﻿using System;
using System.Web.UI;
using YaoHuo.Plugin.Tool;

namespace YaoHuo.Plugin.WML
{
    public class index : Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            while (true)
            {
                string text = base.Request.QueryString.Get("siteid");
                string text2 = base.Request.QueryString.Get("classid");
                string text3 = base.Request.QueryString.Get("sid");
                string text4 = base.Request.QueryString.Get("action");
                string text5 = base.Request.Url.Scheme + "://" + base.Request.Url.Host + "/";
                bool flag = !(text4 == "webAdmin");
                int num = 2;
                while (true)
                {
                    switch (num)
                    {
                        case 2:
                            if (!flag)
                            {
                                num = 0;
                                continue;
                            }
                            flag = !(text4 == "webAdmin00");
                            num = 6;
                            continue;
                        case 4:
                            if (!flag)
                            {
                                num = 1;
                                continue;
                            }
                            base.Response.Redirect(text5 + "wml/list.aspx?classid=" + text2);
                            num = 16;
                            continue;
                        case 10:
                            if (flag)
                            {
                                flag = !(WapTool.ISAPI_Rewrite3_Open == "1");
                                num = 4;
                            }
                            else
                            {
                                num = 14;
                            }
                            continue;
                        case 6:
                            if (!flag)
                            {
                                num = 11;
                                continue;
                            }
                            flag = !(text4 == "wapAdmin");
                            num = 15;
                            continue;
                        case 0:
                            base.Response.Redirect("userlist.aspx?classid=" + text2);
                            num = 5;
                            continue;
                        case 5:
                            return;
                        case 1:
                            base.Response.Redirect(text5 + "wmllist-" + text2 + ".html");
                            num = 9;
                            continue;
                        case 9:
                        case 16:
                            num = 12;
                            continue;
                        case 12:
                            return;
                        case 3:
                            base.Response.Redirect(text5 + "wml/admin_userlistWAP.aspx?siteid=" + text + "&classid=" + text2);
                            num = 7;
                            continue;
                        case 7:
                            return;
                        case 11:
                            base.Response.Redirect("admin_userlist.aspx?classid=" + text2);
                            num = 8;
                            continue;
                        case 8:
                            return;
                        case 14:
                            base.Response.Redirect(text5 + "wml/admin_userlistWAP00.aspx?siteid=" + text + "&classid=" + text2);
                            num = 13;
                            continue;
                        case 13:
                            return;
                        case 15:
                            if (flag)
                            {
                                flag = !(text4 == "wapAdmin00");
                                if (true)
                                {
                                }
                                num = 10;
                            }
                            else
                            {
                                num = 3;
                            }
                            continue;
                    }
                    break;
                }
            }
        }
    }
}