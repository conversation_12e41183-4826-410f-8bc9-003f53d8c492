{{#if Message.HasMessage}}
<div class="message {{Message.Type}}">
    {{Message.Content}}
</div>
{{/if}}

<form id="profile-form" action="{{FormData.ActionUrl}}" method="post">
    <!-- 论坛资料 -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">
                <i data-lucide="message-circle" class="card-icon"></i>
                论坛资料
            </h2>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label class="form-label">昵称</label>
                <input type="text" 
                       name="tonickname" 
                       class="form-input" 
                       maxlength="15" 
                       value="{{FormData.ForumInfo.Nickname}}" 
                       placeholder="请输入昵称">
                <div class="form-hint">{{FormData.ForumInfo.NicknameHint}}</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">个性签名</label>
                <input type="text" 
                       name="remark" 
                       class="form-input" 
                       maxlength="15" 
                       value="{{FormData.ForumInfo.Signature}}">
            </div>
        </div>
    </div>

    <!-- 联系方式 -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">
                <i data-lucide="mail" class="card-icon"></i>
                联系方式
            </h2>
        </div>
        <div class="card-body">
            <div class="form-group">
                <label class="form-label">手机</label>
                <input type="tel" 
                       name="mobile" 
                       class="form-input" 
                       maxlength="11" 
                       pattern="[0-9]*" 
                       value="{{FormData.ContactInfo.Mobile}}" 
                       placeholder="非登录手机号">
            </div>
            
            <div class="form-group">
                <label class="form-label">邮箱</label>
                <input type="email" 
                       name="email" 
                       class="form-input" 
                       maxlength="30" 
                       value="{{FormData.ContactInfo.Email}}">
            </div>
            
            <div class="form-group">
                <label class="form-label">QQ号</label>
                <input type="text" 
                       name="qq" 
                       class="form-input" 
                       maxlength="11" 
                       pattern="[0-9]*" 
                       value="{{FormData.ContactInfo.QQ}}">
            </div>
        </div>
    </div>

    <!-- 个人信息 -->
    <div class="card">
        <div class="card-header">
            <h2 class="card-title">
                <i data-lucide="user-round" class="card-icon"></i>
                个人信息
            </h2>
        </div>
        <div class="card-body">
            <!-- 基本信息（默认显示） -->
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">年龄</label>
                    <input type="number" 
                           name="age" 
                           class="form-input" 
                           min="10" 
                           max="99" 
                           value="{{#if FormData.PersonalInfo.Age}}{{FormData.PersonalInfo.Age}}{{/if}}">
                </div>
                
                <div class="form-group">
                    <label class="form-label">爱好</label>
                    <input type="text" 
                           name="aihao" 
                           class="form-input" 
                           maxlength="10" 
                           value="{{FormData.PersonalInfo.Hobby}}">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">城市</label>
                    <input type="text" 
                           name="city" 
                           class="form-input" 
                           maxlength="8" 
                           value="{{FormData.PersonalInfo.City}}">
                </div>
                
                <div class="form-group">
                    <label class="form-label">职业</label>
                    <input type="text" 
                           name="zhiye" 
                           class="form-input" 
                           maxlength="5" 
                           value="{{FormData.PersonalInfo.Occupation}}">
                </div>
            </div>
            
            <!-- 展开更多按钮 -->
            <div class="expand-toggle">
                <button type="button" class="expand-btn" id="expand-more-btn">
                    <i data-lucide="chevron-down" class="icon mr-2"></i>
                    <span class="expand-btn-text">展开更多</span>
                </button>
            </div>
            
            <!-- 详细信息（默认隐藏） -->
            <div class="more-fields hidden" id="more-fields">
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">身高 (cm)</label>
                        <input type="number" 
                               name="shenggao" 
                               class="form-input" 
                               min="100" 
                               max="250" 
                               value="{{FormData.PersonalInfo.Height}}">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">体重 (kg)</label>
                        <input type="number" 
                               name="tizhong" 
                               class="form-input" 
                               min="30" 
                               max="300" 
                               value="{{FormData.PersonalInfo.Weight}}">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label class="form-label">星座</label>
                        <select name="xingzuo" class="form-select">
                            {{#each OptionLists.ZodiacOptions}}
                            <option value="{{Value}}"{{#if Selected}} selected{{/if}}>{{Text}}</option>
                            {{/each}}
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">婚否</label>
                        <select name="fenfuo" class="form-select">
                            {{#each OptionLists.MaritalStatusOptions}}
                            <option value="{{Value}}"{{#if Selected}} selected{{/if}}>{{Text}}</option>
                            {{/each}}
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 隐藏字段 -->
    <input type="hidden" name="action" value="{{FormData.HiddenFields.Action}}">
    <input type="hidden" name="siteid" value="{{FormData.HiddenFields.SiteId}}">
    <input type="hidden" name="classid" value="{{FormData.HiddenFields.ClassId}}">
    <input type="hidden" name="backurl" value="{{FormData.HiddenFields.BackUrl}}">
    <input type="hidden" name="sex" value="{{FormData.PersonalInfo.Gender}}">

    <!-- 保存按钮 -->
    <div class="form-actions">
        <button type="submit" class="form-submit">
            <i data-lucide="save" class="icon"></i>
            保存修改
        </button>
    </div>
</form>

<!-- 引入TypeScript编译后的EditProfile模块 -->
<script type="module" src="/Template/TS/pages/EditProfile.js?v=1751889158386"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化图标
        lucide.createIcons();
        
        // 展开更多字段功能
        const expandBtn = document.getElementById('expand-more-btn');
        const moreFields = document.getElementById('more-fields');
        const expandBtnText = expandBtn ? expandBtn.querySelector('.expand-btn-text') : null;
        let isExpanded = false;
        
        if (expandBtn && moreFields && expandBtnText) {
            expandBtn.addEventListener('click', function() {
                isExpanded = !isExpanded;
                
                if (isExpanded) {
                    // 展开
                    moreFields.classList.remove('hidden');
                    expandBtn.classList.add('expanded');
                    expandBtnText.textContent = '收起更多'; // 只更新文本内容
                    // CSS类将处理图标的旋转动画
                    
                    // 动画辅助类，平滑展开
                    moreFields.classList.add('expanding');
                    setTimeout(() => {
                        moreFields.classList.remove('expanding');
                        const rect = moreFields.getBoundingClientRect();
                        const isVisible = rect.top >= 0 && 
                                          rect.left >= 0 && 
                                          rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) && 
                                          rect.right <= (window.innerWidth || document.documentElement.clientWidth);
                        
                        if (!isVisible) {
                            const headerHeight = 60;
                            const scrollPosition = moreFields.offsetTop - headerHeight;
                            window.scrollTo({
                                top: scrollPosition,
                                behavior: 'smooth'
                            });
                        }
                    }, 200); // 动画持续时间
                } else {
                    // 收起
                    expandBtn.classList.remove('expanded');
                    expandBtnText.textContent = '展开更多'; // 只更新文本内容
                    // CSS类将处理图标的旋转动画
                    
                    // 动画辅助类，平滑收起
                    moreFields.classList.add('collapsing');
                    setTimeout(() => {
                        moreFields.classList.add('hidden');
                        moreFields.classList.remove('collapsing');
                    }, 200); // 动画持续时间
                }
            });
        }
    });
</script> 