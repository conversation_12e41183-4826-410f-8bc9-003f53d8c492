using System;
using System.Collections.Generic;
using YaoHuo.Plugin.WebSite.Services.Config.Models;

namespace YaoHuo.Plugin.WebSite.Services.Config
{
    /// <summary>
    /// OAuth 2.0 配置服务
    /// 提供统一的 OAuth 配置管理接口
    /// </summary>
    public static class OAuthConfigService
    {
        private const string CONFIG_NAME = "OAuthConfig";
        private const string MODULE_NAME = "OAuth";

        /// <summary>
        /// 获取 OAuth 配置
        /// </summary>
        /// <returns>OAuth 配置对象</returns>
        public static OAuthConfigRoot GetConfig()
        {
            try
            {
                return ConfigService.GetConfig<OAuthConfigRoot>(CONFIG_NAME, MODULE_NAME);
            }
            catch (Exception)
            {
                return GetDefaultConfig();
            }
        }

        /// <summary>
        /// 获取令牌设置
        /// </summary>
        /// <returns>令牌设置</returns>
        public static TokenSettings GetTokenSettings()
        {
            return GetConfig()?.tokenSettings ?? new TokenSettings();
        }

        /// <summary>
        /// 获取安全设置
        /// </summary>
        /// <returns>安全设置</returns>
        public static SecuritySettings GetSecuritySettings()
        {
            return GetConfig()?.securitySettings ?? new SecuritySettings();
        }

        /// <summary>
        /// 获取端点设置
        /// </summary>
        /// <returns>端点设置</returns>
        public static EndpointSettings GetEndpointSettings()
        {
            return GetConfig()?.endpointSettings ?? new EndpointSettings();
        }

        /// <summary>
        /// 获取 CORS 设置
        /// </summary>
        /// <returns>CORS 设置</returns>
        public static CorsSettings GetCorsSettings()
        {
            return GetConfig()?.corsSettings ?? new CorsSettings();
        }

        /// <summary>
        /// 获取日志记录设置
        /// </summary>
        /// <returns>日志记录设置</returns>
        public static LoggingSettings GetLoggingSettings()
        {
            return GetConfig()?.loggingSettings ?? new LoggingSettings();
        }



        /// <summary>
        /// 获取令牌管理模式
        /// </summary>
        /// <returns>令牌管理模式（normal/smart/aggressive）</returns>
        public static string GetTokenManagementMode()
        {
            try
            {
                var config = GetConfig();
                return config?.tokenManagementSettings?.mode ?? "smart";
            }
            catch (Exception)
            {
                return "smart"; // 默认使用智能模式
            }
        }

        /// <summary>
        /// 是否启用令牌管理活动日志
        /// </summary>
        /// <returns>是否启用</returns>
        public static bool IsTokenActivityLogEnabled()
        {
            try
            {
                var config = GetConfig();
                return config?.tokenManagementSettings?.enableActivityLog ?? true;
            }
            catch (Exception)
            {
                return true; // 默认启用日志
            }
        }

        /// <summary>
        /// 获取授权码有效期（分钟）
        /// </summary>
        /// <returns>授权码有效期</returns>
        public static int GetAuthorizationCodeLifetimeMinutes()
        {
            return GetTokenSettings().authorizationCodeLifetimeMinutes;
        }

        /// <summary>
        /// 获取访问令牌有效期（小时）
        /// </summary>
        /// <returns>访问令牌有效期</returns>
        public static int GetAccessTokenLifetimeHours()
        {
            return GetTokenSettings().accessTokenLifetimeHours;
        }

        /// <summary>
        /// 获取默认权限范围
        /// </summary>
        /// <returns>默认权限范围</returns>
        public static string GetDefaultScope()
        {
            return GetTokenSettings().defaultScope;
        }

        /// <summary>
        /// 获取支持的权限范围
        /// </summary>
        /// <returns>支持的权限范围列表</returns>
        public static List<string> GetSupportedScopes()
        {
            return GetTokenSettings().supportedScopes ?? new List<string> { "profile" };
        }

        /// <summary>
        /// 检查是否支持指定的权限范围
        /// </summary>
        /// <param name="scope">权限范围</param>
        /// <returns>是否支持</returns>
        public static bool IsScopeSupported(string scope)
        {
            if (string.IsNullOrEmpty(scope))
                return false;

            var supportedScopes = GetSupportedScopes();
            return supportedScopes.Contains(scope);
        }

        /// <summary>
        /// 获取 AppKey 哈希盐值
        /// </summary>
        /// <returns>盐值</returns>
        public static string GetAppKeySalt()
        {
            return GetSecuritySettings().appKeySalt;
        }

        /// <summary>
        /// 检查是否要求 HTTPS
        /// </summary>
        /// <returns>是否要求 HTTPS</returns>
        public static bool RequireHttps()
        {
            return GetSecuritySettings().requireHttps;
        }

        /// <summary>
        /// 检查是否允许 localhost 使用 HTTP
        /// </summary>
        /// <returns>是否允许</returns>
        public static bool AllowLocalhostHttp()
        {
            return GetSecuritySettings().allowLocalhostHttp;
        }

        /// <summary>
        /// 检查是否要求 PKCE
        /// </summary>
        /// <returns>是否要求 PKCE</returns>
        public static bool RequirePKCE()
        {
            return GetTokenSettings().requirePKCE;
        }

        /// <summary>
        /// 刷新配置缓存
        /// </summary>
        public static void RefreshConfig()
        {
            ConfigService.RefreshConfig(CONFIG_NAME);
        }

        /// <summary>
        /// 获取默认配置（配置文件加载失败时使用）
        /// </summary>
        /// <returns>默认配置</returns>
        private static OAuthConfigRoot GetDefaultConfig()
        {
            return new OAuthConfigRoot
            {
                config = new ConfigInfo
                {
                    version = "2.0",
                    lastUpdated = DateTime.UtcNow,
                    description = "OAuth 2.0 默认配置",
                    author = "系统",
                    environment = "default"
                },
                tokenSettings = new TokenSettings(),
                securitySettings = new SecuritySettings(),
                endpointSettings = new EndpointSettings(),
                corsSettings = new CorsSettings()
            };
        }

        /// <summary>
        /// 验证配置完整性
        /// </summary>
        /// <returns>验证结果</returns>
        public static (bool IsValid, List<string> Errors) ValidateConfig()
        {
            var errors = new List<string>();
            var config = GetConfig();

            if (config == null)
            {
                errors.Add("配置文件无法加载");
                return (false, errors);
            }

            // 验证令牌设置
            if (config.tokenSettings != null)
            {
                if (config.tokenSettings.authorizationCodeLifetimeMinutes <= 0)
                    errors.Add("授权码有效期必须大于0");

                if (config.tokenSettings.accessTokenLifetimeHours <= 0)
                    errors.Add("访问令牌有效期必须大于0");

                if (string.IsNullOrEmpty(config.tokenSettings.defaultScope))
                    errors.Add("默认权限范围不能为空");
            }

            // 验证安全设置
            if (config.securitySettings != null)
            {
                if (string.IsNullOrEmpty(config.securitySettings.appKeySalt))
                    errors.Add("AppKey 盐值不能为空");

                if (config.securitySettings.maxRedirectUris <= 0)
                    errors.Add("最大重定向URI数量必须大于0");
            }

            return (errors.Count == 0, errors);
        }
    }
}