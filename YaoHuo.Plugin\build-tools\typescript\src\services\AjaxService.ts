/**
 * AJAX服务
 * 统一管理所有页面的异步请求功能
 * 提供加载状态管理、错误处理和响应数据处理
 * 
 * @version 1.0
 * <AUTHOR>
 * @date 2025-01-07
 */

import { DEFAULT_CONFIG } from '../types/CommonTypes.js';
import { ToastService } from './ToastService.js';

/**
 * 请求配置接口
 */
export interface RequestConfig {
    url: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    data?: any;
    headers?: { [key: string]: string };
    timeout?: number;
    showLoading?: boolean;
    loadingElement?: HTMLElement;
    loadingText?: string;
    showToast?: boolean;
    successMessage?: string;
    errorMessage?: string;
    onSuccess?: (response: any) => void;
    onError?: (error: any) => void;
    onComplete?: () => void;
}

/**
 * 加载状态管理接口
 */
export interface LoadingState {
    element: HTMLElement;
    originalContent: string;
    originalDisabled: boolean;
}

/**
 * AJAX服务类
 * 提供统一的异步请求、加载状态管理和错误处理功能
 */
export class AjaxService {
    private static instance: AjaxService;
    private activeRequests: Map<string, AbortController> = new Map();
    private loadingStates: Map<HTMLElement, LoadingState> = new Map();

    /**
     * 获取单例实例
     */
    public static getInstance(): AjaxService {
        if (!AjaxService.instance) {
            AjaxService.instance = new AjaxService();
        }
        return AjaxService.instance;
    }

    /**
     * 发送GET请求
     */
    public static get(url: string, config?: Partial<RequestConfig>): Promise<any> {
        return AjaxService.getInstance().request({ ...config, url, method: 'GET' });
    }

    /**
     * 发送POST请求
     */
    public static post(url: string, data?: any, config?: Partial<RequestConfig>): Promise<any> {
        return AjaxService.getInstance().request({ ...config, url, method: 'POST', data });
    }

    /**
     * 发送DELETE请求
     */
    public static delete(url: string, config?: Partial<RequestConfig>): Promise<any> {
        return AjaxService.getInstance().request({ ...config, url, method: 'DELETE' });
    }

    /**
     * 取消所有请求
     */
    public static cancelAll(): void {
        AjaxService.getInstance().cancelAllRequests();
    }

    /**
     * 发送请求
     */
    public async request(config: RequestConfig): Promise<any> {
        const requestId = this.generateRequestId();
        const controller = new AbortController();
        this.activeRequests.set(requestId, controller);

        try {
            // 显示加载状态
            if (config.showLoading && config.loadingElement) {
                this.showLoading(config.loadingElement, config.loadingText);
            }

            // 构建请求选项
            const requestOptions: RequestInit = {
                method: config.method || 'GET',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    ...config.headers
                },
                signal: controller.signal,
                credentials: 'same-origin'
            };

            // 添加请求体
            if (config.data && (config.method === 'POST' || config.method === 'PUT')) {
                if (typeof config.data === 'string') {
                    requestOptions.body = config.data;
                } else {
                    requestOptions.body = new URLSearchParams(config.data).toString();
                }
            }

            // 设置超时
            const timeout = config.timeout || DEFAULT_CONFIG.AJAX_TIMEOUT;
            const timeoutId = setTimeout(() => {
                controller.abort();
            }, timeout);

            // 发送请求
            const response = await fetch(config.url, requestOptions);
            clearTimeout(timeoutId);

            // 处理响应
            const result = await this.handleResponse(response, config);

            // 成功回调
            if (config.onSuccess) {
                config.onSuccess(result);
            }

            // 显示成功Toast
            if (config.showToast && config.successMessage) {
                ToastService.showSuccess(config.successMessage);
            }

            return result;

        } catch (error) {
            // 错误处理
            return this.handleError(error, config);
        } finally {
            // 清理
            this.activeRequests.delete(requestId);
            
            if (config.showLoading && config.loadingElement) {
                this.hideLoading(config.loadingElement);
            }

            if (config.onComplete) {
                config.onComplete();
            }
        }
    }

    /**
     * 处理响应
     */
    private async handleResponse(response: Response, _config: RequestConfig): Promise<any> {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        } else {
            const text = await response.text();
            
            // 尝试解析为JSON
            try {
                return JSON.parse(text);
            } catch {
                // 如果不是JSON，检查是否包含成功/失败标识
                return this.parseTextResponse(text);
            }
        }
    }

    /**
     * 解析文本响应
     */
    private parseTextResponse(text: string): any {
        // 检查常见的成功/失败标识
        const successKeywords = ['删除成功', '清空成功', '操作成功', 'success', 'deleted successfully'];
        const errorKeywords = ['删除失败', '清空失败', '操作失败', 'error', 'failed'];

        const lowerText = text.toLowerCase();
        
        for (const keyword of successKeywords) {
            if (lowerText.includes(keyword.toLowerCase())) {
                return { success: true, message: '操作成功', data: text };
            }
        }

        for (const keyword of errorKeywords) {
            if (lowerText.includes(keyword.toLowerCase())) {
                return { success: false, message: '操作失败', data: text };
            }
        }

        // 默认认为是成功的
        return { success: true, message: '操作完成', data: text };
    }

    /**
     * 处理错误
     */
    private handleError(error: any, config: RequestConfig): Promise<never> {
        console.error('AJAX请求错误:', error);

        let errorMessage = config.errorMessage || '请求失败，请重试';

        if (error.name === 'AbortError') {
            errorMessage = '请求已取消';
        } else if (error.message) {
            errorMessage = error.message;
        }

        // 错误回调
        if (config.onError) {
            config.onError(error);
        }

        // 显示错误Toast
        if (config.showToast !== false) {
            ToastService.showError(errorMessage);
        }

        return Promise.reject(error);
    }

    /**
     * 显示加载状态
     */
    private showLoading(element: HTMLElement, loadingText?: string): void {
        if (this.loadingStates.has(element)) {
            return; // 已经在加载中
        }

        // 保存原始状态
        const originalContent = element.innerHTML;
        const originalDisabled = (element as any).disabled || false;

        this.loadingStates.set(element, {
            element,
            originalContent,
            originalDisabled
        });

        // 设置加载状态
        const text = loadingText || '加载中...';
        element.innerHTML = `<i data-lucide="loader-2" class="w-4 h-4 animate-spin mr-2"></i>${text}`;
        (element as any).disabled = true;

        // 初始化图标
        if (typeof (window as any).lucide !== 'undefined') {
            (window as any).lucide.createIcons();
        }
    }

    /**
     * 隐藏加载状态
     */
    private hideLoading(element: HTMLElement): void {
        const loadingState = this.loadingStates.get(element);
        if (!loadingState) {
            return;
        }

        // 恢复原始状态
        element.innerHTML = loadingState.originalContent;
        (element as any).disabled = loadingState.originalDisabled;

        this.loadingStates.delete(element);

        // 重新初始化图标
        if (typeof (window as any).lucide !== 'undefined') {
            (window as any).lucide.createIcons();
        }
    }

    /**
     * 取消所有请求
     */
    public cancelAllRequests(): void {
        for (const controller of this.activeRequests.values()) {
            controller.abort();
        }
        this.activeRequests.clear();
    }

    /**
     * 生成请求ID
     */
    private generateRequestId(): string {
        return `request-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 获取活动请求数量
     */
    public getActiveRequestCount(): number {
        return this.activeRequests.size;
    }
}

// ==================== 全局函数，供模板调用 ====================

/**
 * 发送GET请求（简化接口）
 */
export function ajaxGet(url: string, config?: Partial<RequestConfig>): Promise<any> {
    return AjaxService.get(url, config);
}

/**
 * 发送POST请求（简化接口）
 */
export function ajaxPost(url: string, data?: any, config?: Partial<RequestConfig>): Promise<any> {
    return AjaxService.post(url, data, config);
}

/**
 * 发送DELETE请求（简化接口）
 */
export function ajaxDelete(url: string, config?: Partial<RequestConfig>): Promise<any> {
    return AjaxService.delete(url, config);
}

// 导出默认实例
export default AjaxService;
