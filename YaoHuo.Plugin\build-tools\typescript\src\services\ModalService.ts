/**
 * 模态框服务
 * 统一管理所有页面的确认弹窗和模态框
 * 消除重复代码，提供一致的用户体验
 * 
 * @version 1.0
 * <AUTHOR>
 * @date 2025-01-07
 */

import type { ModalConfig } from '../types/CommonTypes.js';

// 内联CSS类名常量，避免导入问题
const CSS_CLASSES = {
    MODAL_OVERLAY: 'confirm-overlay',
    MODAL_CONTENT: 'confirm-content',
    MODAL_TITLE: 'confirm-title',
    MODAL_MESSAGE: 'confirm-message',
    MODAL_ACTIONS: 'confirm-actions',
    BTN_PRIMARY: 'btn btn-primary',
    BTN_OUTLINE: 'btn btn-outline',
    BTN_DESTRUCTIVE: 'btn btn-destructive',
    CONFIRM_DELETE: 'custom-confirm-btn custom-confirm-delete',
    CONFIRM_CANCEL: 'custom-confirm-btn custom-confirm-cancel',
    HIDDEN: 'hidden'
} as const;

/**
 * 自定义模态框配置接口
 */
export interface CustomModalConfig {
    title?: string;
    content: string;
    showCloseButton?: boolean;
    customClass?: string;
    contentClass?: string; // 新增：自定义内容容器样式类
    onClose?: () => void;
}

/**
 * 模态框服务类
 * 提供统一的确认弹窗、警告框和自定义模态框功能
 */
export class ModalService {
    private static instance: ModalService;
    private activeModals: Map<string, HTMLElement> = new Map();
    private modalCounter: number = 0;

    /**
     * 获取单例实例
     */
    public static getInstance(): ModalService {
        if (!ModalService.instance) {
            ModalService.instance = new ModalService();
        }
        return ModalService.instance;
    }

    /**
     * 显示确认对话框
     */
    public static confirm(message: string, onConfirm?: () => void, onCancel?: () => void): Promise<boolean> {
        return ModalService.getInstance().showConfirm({
            title: '确认操作',
            message,
            type: 'confirm',
            confirmText: '确定',
            cancelText: '取消',
            onConfirm,
            onCancel
        });
    }

    /**
     * 显示警告对话框
     */
    public static alert(message: string, onConfirm?: () => void): Promise<boolean> {
        return ModalService.getInstance().showConfirm({
            message,
            type: 'alert',
            confirmText: '确定',
            onConfirm
        });
    }

    /**
     * 显示删除确认对话框
     */
    public static confirmDelete(message: string, onConfirm?: () => void, onCancel?: () => void): Promise<boolean> {
        return ModalService.getInstance().showConfirm({
            title: '确认操作',
            message,
            type: 'confirm',
            confirmText: '确定',
            cancelText: '取消',
            onConfirm,
            onCancel
        });
    }

    /**
     * 关闭所有模态框
     */
    public static closeAll(): void {
        ModalService.getInstance().closeAllModals();
    }

    /**
     * 显示确认对话框
     */
    public showConfirm(config: ModalConfig): Promise<boolean> {
        return new Promise((resolve) => {
            const modalId = this.generateModalId();
            
            // 创建模态框元素
            const modalElement = this.createModalElement(modalId, config, resolve);
            
            // 添加到页面
            document.body.appendChild(modalElement);
            this.activeModals.set(modalId, modalElement);

            // 显示模态框
            requestAnimationFrame(() => {
                modalElement.style.display = 'flex';
            });

            // 绑定ESC键关闭
            this.bindEscapeKey(modalId, () => {
                resolve(false);
                if (config.onCancel) {
                    config.onCancel();
                }
            });
        });
    }

    /**
     * 关闭指定模态框
     */
    public closeModal(modalId: string): void {
        const modalElement = this.activeModals.get(modalId);
        if (!modalElement) return;

        // 移除元素
        if (modalElement.parentNode) {
            modalElement.parentNode.removeChild(modalElement);
        }
        this.activeModals.delete(modalId);

        // 解绑ESC键
        this.unbindEscapeKey(modalId);
    }

    /**
     * 关闭所有模态框
     */
    public closeAllModals(): void {
        for (const modalId of this.activeModals.keys()) {
            this.closeModal(modalId);
        }
    }

    /**
     * 创建模态框元素
     */
    private createModalElement(modalId: string, config: ModalConfig, resolve: (value: boolean) => void): HTMLElement {
        // 创建遮罩层
        const overlay = document.createElement('div');
        overlay.id = modalId;
        overlay.className = CSS_CLASSES.MODAL_OVERLAY;
        overlay.style.display = 'none';

        // 点击遮罩层关闭
        overlay.onclick = (e) => {
            if (e.target === overlay) {
                this.closeModal(modalId);
                resolve(false);
                if (config.onCancel) {
                    config.onCancel();
                }
            }
        };

        // 创建内容容器
        const content = document.createElement('div');
        content.className = CSS_CLASSES.MODAL_CONTENT;

        // 添加标题（如果有）
        if (config.title) {
            const title = document.createElement('h3');
            title.className = CSS_CLASSES.MODAL_TITLE;
            title.textContent = config.title;
            content.appendChild(title);
        }

        // 添加消息
        const message = document.createElement('p');
        message.className = CSS_CLASSES.MODAL_MESSAGE;
        message.textContent = config.message;
        content.appendChild(message);

        // 添加按钮组
        const actions = document.createElement('div');
        actions.className = CSS_CLASSES.MODAL_ACTIONS;

        // 确定按钮
        const confirmBtn = document.createElement('button');
        confirmBtn.className = CSS_CLASSES.CONFIRM_DELETE;
        confirmBtn.textContent = config.confirmText || '确定';
        confirmBtn.onclick = () => {
            this.closeModal(modalId);
            resolve(true);
            if (config.onConfirm) {
                config.onConfirm();
            }
        };
        actions.appendChild(confirmBtn);

        // 取消按钮（仅在确认对话框中显示）
        if (config.type === 'confirm') {
            const cancelBtn = document.createElement('button');
            cancelBtn.className = CSS_CLASSES.CONFIRM_CANCEL;
            cancelBtn.textContent = config.cancelText || '取消';
            cancelBtn.onclick = () => {
                this.closeModal(modalId);
                resolve(false);
                if (config.onCancel) {
                    config.onCancel();
                }
            };
            actions.appendChild(cancelBtn);
        }

        content.appendChild(actions);
        overlay.appendChild(content);

        return overlay;
    }

    /**
     * 绑定ESC键关闭
     */
    private bindEscapeKey(modalId: string, onEscape: () => void): void {
        const handler = (e: KeyboardEvent) => {
            if (e.key === 'Escape') {
                this.closeModal(modalId);
                onEscape();
            }
        };

        document.addEventListener('keydown', handler);
        // 存储处理器以便后续移除
        (this.activeModals.get(modalId) as any).__escapeHandler = handler;
    }

    /**
     * 解绑ESC键
     */
    private unbindEscapeKey(modalId: string): void {
        const modalElement = this.activeModals.get(modalId);
        if (modalElement && (modalElement as any).__escapeHandler) {
            document.removeEventListener('keydown', (modalElement as any).__escapeHandler);
            delete (modalElement as any).__escapeHandler;
        }
    }

    /**
     * 显示自定义模态框
     */
    public showCustomModal(config: CustomModalConfig): string {
        const modalId = this.generateModalId();
        const modal = this.createCustomModal(modalId, config);

        this.activeModals.set(modalId, modal);
        document.body.appendChild(modal);

        // 显示动画
        requestAnimationFrame(() => {
            modal.style.opacity = '1';
            // 查找内容容器（可能是confirm-content或其他自定义类）
            const contentSelector = config.contentClass ? `.${config.contentClass.split(' ')[0]}` : '.confirm-content';
            const content = modal.querySelector(contentSelector) as HTMLElement;
            if (content) {
                content.style.transform = 'scale(1)';
            }
        });

        return modalId;
    }

    /**
     * 创建自定义模态框
     */
    private createCustomModal(modalId: string, config: CustomModalConfig): HTMLElement {
        const modal = document.createElement('div');
        modal.id = modalId;
        modal.className = `${CSS_CLASSES.MODAL_OVERLAY} ${config.customClass || ''}`;
        modal.style.opacity = '0';

        const content = document.createElement('div');
        // 使用自定义容器样式类，如果没有指定则使用默认的confirm-content
        content.className = config.contentClass || CSS_CLASSES.MODAL_CONTENT;
        content.style.transform = 'scale(0.95)';
        content.style.transition = 'transform 0.2s ease-out';

        // 如果有标题，添加标题
        if (config.title) {
            const title = document.createElement('h3');
            title.className = CSS_CLASSES.MODAL_TITLE;
            title.textContent = config.title;
            content.appendChild(title);
        }

        // 添加自定义内容
        const contentDiv = document.createElement('div');
        contentDiv.innerHTML = config.content;
        content.appendChild(contentDiv);

        modal.appendChild(content);

        // 绑定关闭事件
        if (config.showCloseButton !== false) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal(modalId);
                    if (config.onClose) {
                        config.onClose();
                    }
                }
            });
        }

        return modal;
    }



    /**
     * 生成唯一的模态框ID
     */
    private generateModalId(): string {
        return `modal-${++this.modalCounter}-${Date.now()}`;
    }
}

// ==================== 全局函数，供模板调用 ====================

/**
 * 显示自定义确认对话框（向后兼容）
 * @param message 消息内容
 * @param onConfirm 确认回调
 * @param onCancel 取消回调
 */
export function showCustomConfirm(message: string, onConfirm?: () => void, onCancel?: () => void): Promise<boolean> {
    return ModalService.confirm(message, onConfirm, onCancel);
}

/**
 * 显示确认对话框（简化接口）
 * @param message 消息内容
 */
export function confirm(message: string): Promise<boolean> {
    return ModalService.confirm(message);
}

/**
 * 显示警告对话框（简化接口）
 * @param message 消息内容
 */
export function alert(message: string): Promise<boolean> {
    return ModalService.alert(message);
}

/**
 * 显示删除确认对话框（简化接口）
 * @param itemName 要删除的项目名称
 */
export function confirmDelete(itemName: string): Promise<boolean> {
    return ModalService.confirmDelete(`确定要删除"${itemName}"吗？`);
}

// 导出默认实例
export default ModalService;
