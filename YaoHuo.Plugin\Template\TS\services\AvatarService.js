import { DEFAULT_CONFIG } from '../types/CommonTypes.js';
export class AvatarService {
    constructor() {
        this.sizeClasses = {
            small: 'w-8 h-8',
            medium: 'w-12 h-12',
            large: 'w-20 h-20'
        };
    }
    static getInstance() {
        if (!AvatarService.instance) {
            AvatarService.instance = new AvatarService();
        }
        return AvatarService.instance;
    }
    static handleLoad(img, size = 'auto') {
        AvatarService.getInstance().handleAvatarLoad(img, size);
    }
    static handleError(img, size = 'auto') {
        AvatarService.getInstance().handleAvatarError(img, size);
    }
    static setLoadTimeout(img, timeout) {
        AvatarService.getInstance().setAvatarTimeout(img, timeout);
    }
    static initPageAvatars() {
        AvatarService.getInstance().initializeAllAvatars();
    }
    static createAvatar(config) {
        return AvatarService.getInstance().createAvatarElement(config);
    }
    handleAvatarLoad(img, size = 'auto') {
        const containerClass = this.getContainerClass(img, size);
        const container = img.closest(containerClass);
        const fallback = container?.querySelector('[data-fallback="true"]');
        img.classList.remove('hidden');
        if (fallback) {
            fallback.classList.add('hidden');
        }
    }
    handleAvatarError(img, size = 'auto') {
        const containerClass = this.getContainerClass(img, size);
        const container = img.closest(containerClass);
        const fallback = container?.querySelector('[data-fallback="true"]');
        img.classList.add('hidden');
        img.style.display = 'none';
        if (fallback) {
            fallback.classList.remove('hidden');
        }
    }
    setAvatarTimeout(img, timeout) {
        const timeoutMs = timeout || DEFAULT_CONFIG.AVATAR_TIMEOUT;
        setTimeout(() => {
            if (img.classList.contains('hidden') && !img.complete) {
                this.handleAvatarError(img);
            }
        }, timeoutMs);
    }
    initializeAllAvatars() {
        document.querySelectorAll('img[data-avatar-src]').forEach(img => {
            const imgElement = img;
            if (imgElement.complete) {
                if (imgElement.naturalWidth > 0) {
                    this.handleAvatarLoad(imgElement);
                }
                else {
                    this.handleAvatarError(imgElement);
                }
            }
            else {
                this.setAvatarTimeout(imgElement);
            }
        });
    }
    createAvatarElement(config) {
        const container = document.createElement('div');
        container.className = `relative ${this.sizeClasses[config.size === 'auto' ? 'medium' : config.size]} rounded-full overflow-hidden bg-gray-150 flex items-center justify-center`;
        const img = document.createElement('img');
        img.src = config.src;
        img.alt = config.fallbackText;
        img.className = 'w-full h-full object-cover hidden';
        img.setAttribute('data-avatar-src', config.src);
        img.onload = () => this.handleAvatarLoad(img, config.size);
        img.onerror = () => this.handleAvatarError(img, config.size);
        const fallback = document.createElement('div');
        fallback.className = config.size === 'small' ? 'avatar-fallback-small' : 'avatar-fallback-main';
        fallback.setAttribute('data-fallback', 'true');
        fallback.textContent = this.getFallbackText(config.fallbackText);
        container.appendChild(img);
        container.appendChild(fallback);
        if (config.timeout) {
            this.setAvatarTimeout(img, config.timeout);
        }
        return container;
    }
    getContainerClass(img, size) {
        if (size !== 'auto') {
            const sizeClass = this.sizeClasses[size].replace(' ', '.');
            return `div.${sizeClass}`;
        }
        if (img.classList.contains('w-8'))
            return 'div.w-8.h-8';
        if (img.classList.contains('w-12'))
            return 'div.w-12.h-12';
        if (img.classList.contains('w-20'))
            return 'div.w-20.h-20';
        if (img.classList.contains('w-24'))
            return 'div.w-24.h-24';
        if (img.classList.contains('w-28'))
            return 'div.w-28.h-28';
        return 'div.w-8.h-8';
    }
    getFallbackText(text) {
        if (!text)
            return '?';
        const firstChar = text.charAt(0);
        return firstChar.toUpperCase();
    }
}
export function handleAvatarLoad(img) {
    AvatarService.handleLoad(img, 'large');
}
export function handleAvatarError(img) {
    AvatarService.handleError(img, 'large');
}
export function handleSmallAvatarLoad(img) {
    AvatarService.handleLoad(img, 'small');
}
export function handleSmallAvatarError(img) {
    AvatarService.handleError(img, 'small');
}
export function handleMediumAvatarLoad(img) {
    AvatarService.handleLoad(img, 'medium');
}
export function handleMediumAvatarError(img) {
    AvatarService.handleError(img, 'medium');
}
export function checkAvatarLoadTimeout(img, timeoutMs = DEFAULT_CONFIG.AVATAR_TIMEOUT) {
    AvatarService.setLoadTimeout(img, timeoutMs);
}
export default AvatarService;
