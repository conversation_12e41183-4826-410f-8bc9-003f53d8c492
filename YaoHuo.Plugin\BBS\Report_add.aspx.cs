﻿using System;
using System.Collections.Generic;
using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Report_add : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public List<class_Model> classList = new List<class_Model>();

        public string action = "";

        public string id = "";

        public string page = "";

        public string INFO = "";

        public string ERROR = "";

        public string toclassid = "";

        public int int_0 = 1;

        // 数据库连接字符串
        private string ConnectionString => PubConstant.GetConnectionString(string_10);

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            page = GetRequestValue("page");
            id = GetRequestValue("id");
            if (action == "gomod")
            {
                try
                {
                    // ✅ 使用DapperHelper进行安全的参数化查询检查是否已经举报过
                    string checkSql = "SELECT COUNT(1) FROM wap2_bbs_report WHERE bbsid = @BbsId AND userid = @UserId";
                    int reportCount = DapperHelper.ExecuteScalar<int>(ConnectionString, checkSql, new {
                        BbsId = DapperHelper.SafeParseLong(id, "帖子ID"),
                        UserId = DapperHelper.SafeParseLong(userid, "用户ID")
                    });

                    if (reportCount > 0)
                    {
                        INFO = "ALREADY_REPORTED";
                    }
                    else
                    {
                        // ✅ 使用DapperHelper进行安全的参数化插入操作
                        string reportType = GetRequestValue("reporttype");
                        string reportWhy = GetRequestValue("reportwhy");

                        string insertSql = @"INSERT INTO wap2_bbs_report
                            (siteid, classid, bbsid, userid, nickname, ReportType, ReportWhy, addtime, types)
                            VALUES (@SiteId, @ClassId, @BbsId, @UserId, @Nickname, @ReportType, @ReportWhy, @AddTime, @Types)";

                        DapperHelper.Execute(ConnectionString, insertSql, new {
                            SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                            ClassId = DapperHelper.SafeParseLong(classid, "栏目ID"),
                            BbsId = DapperHelper.SafeParseLong(id, "帖子ID"),
                            UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                            Nickname = DapperHelper.LimitLength(nickname, 20),
                            ReportType = DapperHelper.LimitLength(reportType, 20),
                            ReportWhy = DapperHelper.LimitLength(reportWhy, 100),
                            AddTime = DateTime.Now,
                            Types = 0L
                        });

                        INFO = "OK";
                    }
                }
                catch (Exception ex)
                {
                    ERROR = ex.ToString();
                }
            }
        }
    }
}