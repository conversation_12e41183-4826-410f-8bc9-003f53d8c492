using System;
using System.IO;
using Serilog;
using Serilog.Events;
using Serilog.Formatting.Compact;
using YaoHuo.Plugin.WebSite.Services.Config;

namespace YaoHuo.Plugin.WebSite.Tool
{
    /// <summary>
    /// 统一日志配置加载器
    /// </summary>
    public static class LoggingConfigLoader
    {
        /// <summary>
        /// 初始化全局日志系统
        /// </summary>
        public static void Initialize()
        {
            try
            {
                // 1. 读取配置
                var config = LoadConfiguration();
                
                // 2. 创建日志目录
                EnsureDirectoryExists(config.LogPath);
                
                // 3. 配置Serilog
                ConfigureSerilog(config);
                
                // 4. 记录初始化成功日志
                Log.Information("日志系统初始化成功: {LogPath}, {LogLevel}, {RollingInterval}", 
                    config.LogPath, config.MinimumLevel, config.RollingInterval);
            }
            catch (Exception ex)
            {
                // 配置失败时使用默认配置
                ConfigureFallbackLogging();
                Log.Error(ex, "日志系统初始化异常，已启用默认配置");
            }
        }

        /// <summary>
        /// 加载日志配置
        /// </summary>
        private static LoggingConfig LoadConfiguration()
        {
            try
            {
                // 尝试从OAuth配置中读取
                var loggingSettings = OAuthConfigService.GetLoggingSettings();
                if (loggingSettings != null)
                {
                    return new LoggingConfig
                    {
                        Enabled = loggingSettings.enabled,
                        LogPath = ResolveLogPath(loggingSettings.logPath),
                        MinimumLevel = ParseLogLevel(loggingSettings.logLevel),
                        RollingInterval = ParseRollingInterval(loggingSettings.rollingInterval)
                    };
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"读取OAuth日志配置失败: {ex.Message}");
                // 失败时继续使用默认配置
            }

            // 返回默认配置
            return new LoggingConfig
            {
                Enabled = true,
                LogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "App_Data", "Logs"),
                MinimumLevel = LogEventLevel.Information,
                RollingInterval = RollingInterval.Day
            };
        }

        /// <summary>
        /// 确保日志目录存在
        /// </summary>
        private static void EnsureDirectoryExists(string path)
        {
            if (!Directory.Exists(path))
            {
                Directory.CreateDirectory(path);
            }
        }

        /// <summary>
        /// 配置Serilog
        /// </summary>
        private static void ConfigureSerilog(LoggingConfig config)
        {
            var loggerConfig = new LoggerConfiguration()
                .MinimumLevel.Is(config.MinimumLevel);

            if (config.Enabled)
            {
                // 统一日志文件 - 通过 SourceContext 区分不同模块
                loggerConfig.WriteTo.Async(a => a.File(
                    formatter: new CompactJsonFormatter(),
                    path: Path.Combine(config.LogPath, "global-.log"),
                    rollingInterval: config.RollingInterval,
                    shared: true,
                    fileSizeLimitBytes: 10 * 1024 * 1024, // 10MB 文件大小限制
                    retainedFileCountLimit: 31            // 保留31个文件
                ));
            }

            // 设置为全局Logger
            Log.Logger = loggerConfig.CreateLogger();
        }

        /// <summary>
        /// 配置备用日志
        /// </summary>
        private static void ConfigureFallbackLogging()
        {
            try
            {
                var logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "App_Data", "Logs");
                if (!Directory.Exists(logPath))
                {
                    Directory.CreateDirectory(logPath);
                }

                Log.Logger = new LoggerConfiguration()
                    .MinimumLevel.Warning()
                    .WriteTo.File(
                        formatter: new CompactJsonFormatter(),
                        path: Path.Combine(logPath, "fallback-.log"),
                        rollingInterval: RollingInterval.Day
                    )
                    .CreateLogger();
            }
            catch
            {
                // 如果连备用日志都失败，使用最简单的配置
                Log.Logger = new LoggerConfiguration()
                    .MinimumLevel.Error()
                    .WriteTo.File(
                        path: Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "App_Data", "error.log"),
                        rollingInterval: RollingInterval.Day
                    )
                    .CreateLogger();
            }
        }

        /// <summary>
        /// 解析日志路径
        /// </summary>
        private static string ResolveLogPath(string configPath)
        {
            if (string.IsNullOrEmpty(configPath))
            {
                return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "App_Data", "Logs");
            }

            // 处理相对路径
            if (configPath.StartsWith("~/") || configPath.StartsWith("../"))
            {
                return Path.GetFullPath(Path.Combine(
                    AppDomain.CurrentDomain.BaseDirectory,
                    configPath.Replace("~/", "")
                ));
            }

            // 处理绝对路径
            if (Path.IsPathRooted(configPath))
            {
                return configPath;
            }

            // 默认相对于应用根目录
            return Path.Combine(AppDomain.CurrentDomain.BaseDirectory, configPath);
        }

        /// <summary>
        /// 解析日志级别
        /// </summary>
        private static LogEventLevel ParseLogLevel(string level)
        {
            if (string.IsNullOrEmpty(level)) return LogEventLevel.Information;

            switch (level.ToLower())
            {
                case "verbose": return LogEventLevel.Verbose;
                case "debug": return LogEventLevel.Debug;
                case "information": return LogEventLevel.Information;
                case "warning": return LogEventLevel.Warning;
                case "error": return LogEventLevel.Error;
                case "fatal": return LogEventLevel.Fatal;
                default: return LogEventLevel.Information;
            }
        }

        /// <summary>
        /// 解析日志滚动间隔
        /// </summary>
        private static RollingInterval ParseRollingInterval(string interval)
        {
            if (string.IsNullOrEmpty(interval)) return RollingInterval.Day;

            switch (interval.ToLower())
            {
                case "minute": return RollingInterval.Minute;
                case "hour": return RollingInterval.Hour;
                case "day": return RollingInterval.Day;
                case "month": return RollingInterval.Month;
                case "year": return RollingInterval.Year;
                case "infinite": return RollingInterval.Infinite;
                default: return RollingInterval.Day;
            }
        }
    }

    /// <summary>
    /// 日志配置模型
    /// </summary>
    internal class LoggingConfig
    {
        public bool Enabled { get; set; }
        public string LogPath { get; set; }
        public LogEventLevel MinimumLevel { get; set; }
        public RollingInterval RollingInterval { get; set; }
    }
} 