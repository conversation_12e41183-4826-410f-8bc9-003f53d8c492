﻿using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using System;
using System.Data.SqlClient;
using System.Text;
using Dapper;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Book_View_add : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public string KL_CheckBBSCount = PubConstant.GetAppString("KL_CheckBBSCount");

        public string KL_BBS_Anonymous_Open = PubConstant.GetAppString("KL_BBS_Anonymous_Open");

        public wap_bbs_Model bbsVo = new wap_bbs_Model();

        public string action = "";

        public string page = "";

        public string INFO = "";

        public string ERROR = "";

        public string book_title = "";

        public string book_content = "";

        public string sendmoney = "";

        public bool isadmin = false;

        public long getid;

        public string getmoney = "0";

        public string getexpr = "0";

        public string needpwFlag = "";

        public string needpw = "";

        public string titlemax = "0";

        public string contentmax = "0";

        public string title_max = "0";

        public string content_max = "0";

        public string getmoney2 = "";

        public bool isNeedSecret = false;

        public StringBuilder strhtml = new StringBuilder();

        protected void Page_Load(object sender, EventArgs e)
        {
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID对应非论坛模块。", "");
            }
            action = base.Request.Form.Get("action");
            page = GetRequestValue("page");
            needpwFlag = WapTool.GetArryString(siteVo.Version, '|', 31);
            if (classVo.topicID != "" && classVo.topicID != "0" && IsCheckManagerLvl("|00|01|03|04|", ""))
            {
                isNeedSecret = true;
            }
            if ("1".Equals(WapTool.GetArryString(classVo.smallimg, '|', 2)) && !CheckManagerLvl("04", classVo.adminusername))
            {
                ShowTipInfo("发帖功能已关闭！", "wapindex.aspx?siteid=" + siteid + "&amp;classid=" + classVo.childid);
            }
            string value = WapTool.GetArryString(classVo.smallimg, '|', 11);
            if (KL_BBS_Anonymous_Open != "1")
            {
                value = "0";
            }
            if (!"1".Equals(value))
            {
                IsLogin(userid, "bbs/book_view_add.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + page);
            }
            else
            {
                userVo.password = "";
            }
            string arryString = WapTool.GetArryString(classVo.smallimg, '|', 27);
            if (arryString.Trim() != "")
            {
                arryString = arryString.Replace("_", "|");
                arryString = "|" + arryString + "|";
                if (!IsCheckManagerLvl("|00|01|03|04|", classVo.adminusername) && arryString.IndexOf("|" + userVo.SessionTimeout + "|") < 0)
                {
                    ShowTipInfo("我当前的用户级别：" + WapTool.GetMyID(userVo.idname, lang, userVo.endTime) + " 不允许发帖。<br/>允许发帖用户级别为：" + WapTool.GetCardIDNameFormID_multiple(siteid, arryString, lang), "bbs/book_list.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + page);
                }
            }
            string text = WapTool.GetSiteDefault(siteVo.Version, 14);
            if (!WapTool.IsNumeric(text))
            {
                text = "0";
            }
            long num = Convert.ToInt64(text);
            if (num > 0L)
            {
                long num2 = WapTool.DateDiff(DateTime.Now, userVo.RegTime, "MM");
                if (num2 < num)
                {
                    ShowTipInfo("请再过:" + (num - num2) + "分钟才能发帖！", "bbs/book_list.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + page);
                }
            }
            if (classid == "0")
            {
                ShowTipInfo("无此栏目ID", "");
            }
            if (userVo.managerlvl == "01" || userVo.managerlvl == "00")
            {
                isadmin = true;
            }
            if (action == "gomod")
            {
                try
                {
                    book_title = GetRequestValue("book_title").Trim(); // 移除标题前后的空格
                    book_content = GetRequestValue("book_content");
                    book_title = book_title.Replace("/", "／").Replace("[", "［").Replace("]", "］");
                    titlemax = WapTool.GetArryString(classVo.smallimg, '|', 24);
                    contentmax = WapTool.GetArryString(classVo.smallimg, '|', 25);
                    if (!WapTool.IsNumeric(titlemax) || titlemax == "0")
                    {
                        titlemax = "2";
                    }
                    if (!WapTool.IsNumeric(contentmax) || contentmax == "0")
                    {
                        contentmax = "2";
                    }
                    title_max = WapTool.GetArryString(classVo.smallimg, '|', 30);
                    content_max = WapTool.GetArryString(classVo.smallimg, '|', 31);
                    if (!WapTool.IsNumeric(title_max))
                    {
                        title_max = "0";
                    }
                    if (!WapTool.IsNumeric(content_max))
                    {
                        content_max = "0";
                    }
                    needpw = GetRequestValue("needpw");
                    sendmoney = GetRequestValue("sendmoney");
                    string text2 = WapTool.GetArryString(siteVo.Version, '|', 22);
                    if (!WapTool.IsNumeric(sendmoney))
                    {
                        sendmoney = "0";
                    }
                    if (!WapTool.IsNumeric(text2))
                    {
                        text2 = "0";
                    }
                    if (long.Parse(text2) < 2L)
                    {
                        text2 = "1000";
                    }
                    if (long.Parse(sendmoney) > long.Parse(text2))
                    {
                        sendmoney = text2;
                    }
                    string arryString2 = WapTool.GetArryString(classVo.smallimg, '|', 21);
                    if (arryString2.Trim() != "")
                    {
                        arryString2 = arryString2.Replace("_", "|");
                        arryString2 = "|" + arryString2 + "|";
                        bool flag = false;
                        if (book_content.IndexOf("[/reply]") > 0 || book_content.IndexOf("[/buy]") > 0 || book_content.IndexOf("[/coin]") > 0 || book_content.IndexOf("[/grade]") > 0)
                        {
                            flag = true;
                        }
                        if (flag && !IsCheckManagerLvl("|00|01|03|04|", classVo.adminusername) && arryString2.IndexOf("|" + userVo.SessionTimeout + "|") < 0)
                        {
                            ShowTipInfo("我当前的用户级别：" + WapTool.GetMyID(userVo.idname, lang, userVo.endTime) + " 不允许发特殊帖。<br/>允许发特殊帖用户级别为：" + WapTool.GetCardIDNameFormID_multiple(siteid, arryString2, lang), "bbs/book_list.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + page);
                        }
                    }
                    string[] array = (WapTool.GetArryString(classVo.smallimg, '|', 34) + ",").Split(',');
                    getmoney2 = array[0];
                    if (needpwFlag == "1" && PubConstant.md5(needpw).ToLower() != userVo.password.ToLower())
                    {
                        INFO = "PWERROR";
                    }
                    else if (isNeedSecret && base.Request.Form.Get("secret").ToString() != classVo.topicID)
                    {
                        INFO = "ERROR_Secret";
                    }
                    else if (book_title.Trim().Length < long.Parse(titlemax) || book_content.Trim().Length < long.Parse(contentmax))
                    {
                        INFO = "NULL";
                    }
                    else if ((title_max != "0" && book_title.Trim().Length > long.Parse(title_max)) || (content_max != "0" && book_content.Trim().Length > long.Parse(content_max)))
                    {
                        INFO = "TITLEMAX";
                    }
                    //else if (book_title.IndexOf("$(") >= 0 || book_content.IndexOf("$(") >= 0)
                    //{
                    //    INFO = "ERR_FORMAT";
                    //}
                    else if (book_title.Equals(Session["content"]))
                    {
                        INFO = "REPEAT";
                    }
                    else if (!WapTool.CheckUserBBSCount(siteid, userid, KL_CheckBBSCount, "bbs"))
                    {
                        INFO = "MAX";
                    }
                    else if (userVo.money < long.Parse(sendmoney))
                    {
                        INFO = "SENDMONEY";
                    }
                    else if (WapTool.IsLockuser(siteid, userid, classid) > -1L)
                    {
                        INFO = "LOCK";
                    }
                    else if (getmoney2.IndexOf('-') == 0 && userVo.money + long.Parse(getmoney2) < 0L)
                    {
                        INFO = "NOMONEY";
                    }
                    else
                    {
                        // 🔧 使用优化的事务版本执行发帖操作
                        ExecutePostingWithOptimizedTransaction();
                    }
                }
                catch (Exception ex)
                {
                    ERROR = WapTool.ErrorToString(ex.ToString());
                }
            }
            if (INFO == "WAITING")
            {
                VisiteCount("发表新帖。");
            }
        }

        /// <summary>
        /// 🔧 优化的事务版本：执行发帖操作
        /// 使用统一事务和固定锁顺序，避免死锁
        /// </summary>
        private void ExecutePostingWithOptimizedTransaction()
        {
            Session["content"] = book_title;
            if (book_title.Length > 200)
            {
                book_title = book_title.Substring(0, 200);
            }

            // 预处理数据
            long currentUserId = DapperHelper.SafeParseLong(userid, "用户ID");
            long currentSiteId = DapperHelper.SafeParseLong(siteid, "站点ID");
            long currentClassId = DapperHelper.SafeParseLong(classid, "栏目ID");

            getmoney = WapTool.GetSiteDefault(siteVo.moneyregular, 0);
            if (!WapTool.IsNumeric(getmoney))
            {
                getmoney = "0";
            }
            getexpr = WapTool.GetSiteDefault(siteVo.lvlRegular, 0);
            if (!WapTool.IsNumeric(getexpr))
            {
                getexpr = "0";
            }
            string[] array2 = (WapTool.GetArryString(classVo.smallimg, '|', 34) + ",").Split(',');
            if (WapTool.IsNumeric(array2[0].Replace("-", "")))
            {
                getmoney = array2[0];
            }
            if (WapTool.IsNumeric(array2[1].Replace("-", "")))
            {
                getexpr = array2[1];
            }

            long earnedMoney = DapperHelper.SafeParseLong(getmoney, "获得金币");
            long spentMoney = DapperHelper.SafeParseLong(sendmoney, "悬赏金额");
            long earnedExp = DapperHelper.SafeParseLong(getexpr, "获得经验");
            long netMoneyChange = earnedMoney - spentMoney;

            string connectionString = PubConstant.GetConnectionString(a);

            // 🔒 使用纯本地事务执行核心数据库操作
            using (var connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction(System.Data.IsolationLevel.ReadCommitted))
                {
                    try
                    {
                        // 📋 步骤1: 插入帖子记录
                        string insertPostSql = @"INSERT INTO wap_bbs (ischeck, userid, book_classid, book_title, book_author, book_pub, book_content, book_date, reDate, sendMoney)
                                                VALUES (@IsCheck, @UserId, @ClassId, @Title, @Author, @PubUserId, @Content, @BookDate, @ReDate, @SendMoney);
                                                SELECT CAST(SCOPE_IDENTITY() AS BIGINT);";

                        getid = connection.QuerySingle<long>(insertPostSql, new {
                            IsCheck = siteVo.isCheck,
                            UserId = currentSiteId,
                            ClassId = currentClassId,
                            Title = book_title,
                            Author = userVo.nickname,
                            PubUserId = currentUserId,
                            Content = book_content,
                            BookDate = DateTime.Now,
                            ReDate = DateTime.Now,
                            SendMoney = spentMoney
                        }, transaction);

                        // 📋 步骤2: 更新用户统计
                        string updateUserSql = @"UPDATE [user] SET money = money + @GetMoney - @SendMoney,
                                                expR = expR + @GetExp, bbscount = bbscount + 1
                                                WHERE siteid = @SiteId AND userid = @UserId";

                        connection.Execute(updateUserSql, new {
                            GetMoney = earnedMoney,
                            SendMoney = spentMoney,
                            GetExp = earnedExp,
                            SiteId = currentSiteId,
                            UserId = currentUserId
                        }, transaction);

                        // 提交核心事务
                        transaction.Commit();
                    }
                    catch (SqlException ex) when (ex.Number == 1205) // 死锁检测
                    {
                        transaction.Rollback();
                        System.Diagnostics.Debug.WriteLine($"发帖时检测到死锁: {ex.Message}");
                        INFO = "DEADLOCK";
                        return;
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        System.Diagnostics.Debug.WriteLine($"发帖事务异常: {ex.Message}");
                        throw;
                    }
                }
            }

            // 📋 步骤3: 事务外操作 - 异步记录银行日志（非关键操作）
            RecordPostingBankLogAsync(connectionString, currentSiteId, currentUserId, earnedMoney, spentMoney, getid);

            // 📋 步骤4: 事务外操作 - 清理缓存和记录日志
            VisiteCount("发表新帖:<a href=\"" + http_start + "bbs-" + getid + ".html\">" + WapTool.GetShowImg(book_title, "200", "bbs") + "</a>");
            INFO = "OK";
            WapTool.ClearDataBBS("bbs" + siteid + classid);
            WapTool.ClearDataTemp("bbsTotal" + siteid + classid);
            Action_user_doit(1);
        }

        /// <summary>
        /// 💰 异步记录发帖银行日志（不阻塞主流程）
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <param name="siteId">站点ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="earnedMoney">获得金币</param>
        /// <param name="spentMoney">花费金币</param>
        /// <param name="postId">帖子ID</param>
        private void RecordPostingBankLogAsync(string connectionString, long siteId, long userId, long earnedMoney, long spentMoney, long postId)
        {
            // 捕获当前上下文的必要信息
            string currentNickname = nickname;
            string currentIP = IP;
            long currentUserMoney = userVo.money;

            // 异步执行银行日志记录，不阻塞主流程
            System.Threading.Tasks.Task.Run(async () =>
            {
                try
                {
                    string insertBankLogSql = @"INSERT INTO wap_bankLog (siteid, userid, actionName, money, leftMoney, opera_userid, opera_nickname, remark, ip, addtime)
                                               VALUES (@SiteId, @UserId, @ActionName, @Money, @LeftMoney, @OperaUserId, @OperaNickname, @Remark, @IP, @AddTime)";

                    // 发帖奖励日志
                    await System.Threading.Tasks.Task.Run(() => DapperHelper.Execute(connectionString, insertBankLogSql, new {
                        SiteId = siteId,
                        UserId = userId,
                        ActionName = "论坛发帖",
                        Money = getmoney,
                        LeftMoney = currentUserMoney + earnedMoney,
                        OperaUserId = userId,
                        OperaNickname = currentNickname,
                        Remark = "发新帖[" + postId + "]",
                        IP = currentIP,
                        AddTime = DateTime.Now
                    }));

                    // 悬赏帖扣费日志
                    if (spentMoney > 0L)
                    {
                        long finalBalance = currentUserMoney + earnedMoney - spentMoney;
                        await System.Threading.Tasks.Task.Run(() => DapperHelper.Execute(connectionString, insertBankLogSql, new {
                            SiteId = siteId,
                            UserId = userId,
                            ActionName = "发布赏帖",
                            Money = "-" + sendmoney,
                            LeftMoney = finalBalance,
                            OperaUserId = userId,
                            OperaNickname = currentNickname,
                            Remark = "发赏帖[" + postId + "]",
                            IP = currentIP,
                            AddTime = DateTime.Now
                        }));
                    }

                    System.Diagnostics.Debug.WriteLine($"异步发帖银行日志记录成功: 用户{userId}, 奖励{earnedMoney}, 花费{spentMoney}, 帖子{postId}");
                }
                catch (Exception ex)
                {
                    // 异步银行日志失败不影响发帖成功，只记录错误
                    System.Diagnostics.Debug.WriteLine($"异步记录发帖银行日志失败: {ex.Message}");
                }
            });
        }
    }
}