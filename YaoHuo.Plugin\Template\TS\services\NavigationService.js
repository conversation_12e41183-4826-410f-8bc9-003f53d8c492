export class NavigationService {
    constructor() { }
    static getInstance() {
        if (!NavigationService.instance) {
            NavigationService.instance = new NavigationService();
        }
        return NavigationService.instance;
    }
    smartBack() {
        try {
            const currentUrl = window.location.href;
            const currentPath = window.location.pathname;
            const referrer = document.referrer;
            const isMyFilePage = currentPath.includes('myfile.aspx');
            const isUserInfoPage = currentPath.includes('userinfo.aspx');
            const isUserSpaceSubPage = this.isUserSpaceSubPage(currentPath);
            if (isMyFilePage) {
                window.location.href = '/';
                return;
            }
            if (isUserInfoPage) {
                window.location.href = '/';
                return;
            }
            if (isUserSpaceSubPage && (!referrer || !this.isValidReferrer(referrer) || this.isSamePageNavigation(referrer, currentUrl))) {
                const userInfoUrl = this.buildUserInfoUrl(currentUrl);
                window.location.href = userInfoUrl;
                return;
            }
            if (referrer && referrer !== currentUrl && this.isValidReferrer(referrer) && !this.isSamePageNavigation(referrer, currentUrl)) {
                window.history.back();
                setTimeout(() => {
                    if (window.location.href === currentUrl) {
                        const fallbackUrl = this.getFallbackUrl(currentPath, currentUrl);
                        window.location.href = fallbackUrl;
                    }
                }, 500);
            }
            else {
                const fallbackUrl = this.getFallbackUrl(currentPath, currentUrl);
                window.location.href = fallbackUrl;
            }
        }
        catch (error) {
            console.log('Back button error:', error);
            const fallbackUrl = this.getFallbackUrl(window.location.pathname, window.location.href);
            window.location.href = fallbackUrl;
        }
    }
    isValidReferrer(referrer) {
        const currentDomain = window.location.origin;
        return referrer.startsWith(currentDomain) || referrer.startsWith('http');
    }
    isUserSpaceSubPage(currentPath) {
        const userSpaceSubPages = [
            'book_list_log.aspx',
            'userguessbook.aspx',
            'userinfomore.aspx',
            'book_re_my.aspx'
        ];
        return userSpaceSubPages.some(page => currentPath.includes(page));
    }
    buildUserInfoUrl(currentUrl) {
        try {
            const urlObj = new URL(currentUrl);
            const params = new URLSearchParams(urlObj.search);
            const touserid = params.get('touserid');
            if (touserid) {
                return `/bbs/userinfo.aspx?touserid=${touserid}`;
            }
            else {
                return '/';
            }
        }
        catch (error) {
            console.log('[SmartBack] Build userinfo URL error:', error);
            return '/';
        }
    }
    getFallbackUrl(currentPath, currentUrl) {
        const isMyFilePage = currentPath.includes('myfile.aspx');
        const isUserInfoPage = currentPath.includes('userinfo.aspx');
        const isUserSpaceSubPage = this.isUserSpaceSubPage(currentPath);
        if (isMyFilePage) {
            return '/';
        }
        else if (isUserInfoPage) {
            return '/';
        }
        else if (isUserSpaceSubPage) {
            return this.buildUserInfoUrl(currentUrl);
        }
        else {
            return '/myfile.aspx';
        }
    }
    isSamePageNavigation(referrer, currentUrl) {
        try {
            const referrerUrl = new URL(referrer);
            const currentUrlObj = new URL(currentUrl);
            if (referrerUrl.pathname === currentUrlObj.pathname) {
                const referrerParams = new URLSearchParams(referrerUrl.search);
                const currentParams = new URLSearchParams(currentUrlObj.search);
                const pageParams = ['page', 'lpage', 'CurrentPage', 'ot'];
                for (const param of pageParams) {
                    if (referrerParams.has(param) || currentParams.has(param)) {
                        return true;
                    }
                }
                if (currentUrlObj.pathname.includes('messagelist.aspx')) {
                    const tabParams = ['types'];
                    for (const param of tabParams) {
                        if (referrerParams.has(param) || currentParams.has(param)) {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
        catch (error) {
            return false;
        }
    }
    initializeBackButton(buttonSelector = '#back-button') {
        const backButton = document.querySelector(buttonSelector);
        if (backButton) {
            backButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.smartBack();
            });
        }
    }
    simpleBack() {
        if (window.history.length > 1) {
            window.history.back();
        }
        else {
            window.location.href = '/';
        }
    }
    backTo(url) {
        window.location.href = url;
    }
    backToHome() {
        window.location.href = '/';
    }
    backToMyFile() {
        window.location.href = '/myfile.aspx';
    }
    getRecommendedBackUrl() {
        const currentPath = window.location.pathname;
        const currentUrl = window.location.href;
        return this.getFallbackUrl(currentPath, currentUrl);
    }
}
