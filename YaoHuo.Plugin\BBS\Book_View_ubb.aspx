﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="book_view_ubb.aspx.cs" Inherits="YaoHuo.Plugin.BBS.book_view_ubb" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%=this.GetLang("UBB使用方法|UBB使用方法|bank list")%></title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 15px;
            background-color: #f5f5f5;
        }

        h1 {
            color: #1a1a1a;
            font-size: 1.8em;
            margin-bottom: 1.5em;
            text-align: center;
        }

        .tabs {
            display: flex;
            gap: 0;
            margin-bottom: 25px;
            background: #f5f5f5;
            padding: 4px;
            border-radius: 8px;
            width: fit-content;
            margin: 0 auto 25px;
			user-select: none;
        }

        .tab {
            padding: 8px 20px;
            background: transparent;
            border: none;
            cursor: pointer;
            color: #999;
            font-weight: normal;
            position: relative;
            transition: all 0.3s ease;
        }

            .tab.active {
                background: #fff;
                color: #000;
                border-radius: 6px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

        .tab-content {
            display: none;
        }

            .tab-content.active {
                display: block;
                animation: fadeIn 0.3s ease;
            }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        .card {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            margin-bottom: 20px;
            padding: 20px;
            position: relative;
            border: 1px solid #eaeaea;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

            .card::after {
                content: '';
                display: table;
                clear: both;
            }

            .card h3 {
                margin-top: 0;
                color: #1a1a1a;
                font-size: 1.2em;
                font-weight: 600;
                margin-bottom: 15px;
            }

            .card p {
                color: #666;
                font-size: 0.9em;
                margin-bottom: 15px;
            }

            .card a {
                color: #404040;
                text-decoration: none;
                border-bottom: 1px solid #ddd;
            }

                .card a:hover {
                    color: #000;
                    border-bottom-color: #000;
                }

        code {
            background-color: #f8f8f8;
            padding: 8px 12px;
            padding-right: 70px;
            border-radius: 6px;
            font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: #333;
            border: 1px solid #eee;
            display: block;
            margin: 10px 0;
            word-break: break-all;
            word-wrap: break-word;
            overflow-x: auto;
        }

        .code-container {
            position: relative;
            margin-bottom: 15px;
        }

        .copy-btn {
            background-color: #2c2c2c;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            transition: background-color 0.3s ease;
            z-index: 1;
        }

            .copy-btn:hover {
                background-color: #404040;
            }

            .copy-btn:active {
            }

        @media (max-width: 600px) {
            body {
                padding: 10px;
            }

            .tabs {
                width: calc(100% - 20px);
            }

            .tab {
                flex: 1;
                padding: 8px 10px;
                font-size: 14px;
            }

            .card {
                padding: 15px;
            }

            h1 {
                font-size: 1.5em;
                margin-bottom: 1em;
            }

            code {
                padding-right: 60px;
                font-size: 0.85em;
                padding-top: 8px;
                padding-bottom: 8px;
            }

            .copy-btn {
                padding: 4px 8px;
                font-size: 0.75em;
            }
        }

        .nav-buttons {
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            padding: 1rem 0 1rem;
        }

        .nav-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            color: #374151;
            font-size: 0.875rem;
            text-decoration: none;
            transition: all 0.2s;
            text-align: center;
        }

            .nav-btn svg {
                width: 16px;
                height: 16px;
            }

            .nav-btn:hover {
                background: #f9f9f9;
                transform: translateY(-1px);
                color: #374151;
                text-decoration: none;
            }
    </style>
</head>
<body>
    <%=WapTool.showTop(this.GetLang("UBB使用方法|UBB使用方法|bank list"), wmlVo)%>

    <h1><%=this.GetLang("UBB使用方法|UBB使用方法|bank list")%></h1>

    <div class="tabs">
        <button class="tab active" data-tab="basic">基础格式</button>
        <button class="tab" data-tab="media">媒体</button>
        <button class="tab" data-tab="advanced">高级功能</button>
    </div>

    <div id="basic" class="tab-content active">
        <div class="card">
            <h3>加粗</h3>
            <div class="code-container">
                <code>[b]加粗[/b]</code>
                <button class="copy-btn" data-code="[b]加粗[/b]">复制</button>
            </div>
        </div>
        <div class="card">
            <h3>斜体</h3>
            <div class="code-container">
                <code>[i]斜体[/i]</code>
                <button class="copy-btn" data-code="[i]斜体[/i]">复制</button>
            </div>
        </div>
        <div class="card">
            <h3>下划线</h3>
            <div class="code-container">
                <code>[u]下划线[/u]</code>
                <button class="copy-btn" data-code="[u]下划线[/u]">复制</button>
            </div>
        </div>
        <div class="card">
            <h3>字体颜色</h3>
            <div class="code-container">
                <code>[forecolor=red]红色文字[/forecolor]</code>
                <button class="copy-btn" data-code="[forecolor=red]红色文字[/forecolor]">复制</button>
            </div>
        </div>
        <div class="card">
            <h3>文本超链接</h3>
            <div class="code-container">
                <code>[url=https://yaohuo.me]妖火网[/url]</code>
                <button class="copy-btn" data-code="[url=https://yaohuo.me]妖火网[/url]">复制</button>
            </div>
        </div>
    </div>

    <div id="media" class="tab-content">
        <div class="card">
            <h3>插入图片</h3>
            <div class="code-container">
                <code>[img]图片链接[/img]</code>
                <button class="copy-btn" data-code="[img]图片链接[/img]">复制</button>
            </div>
        </div>
        <div class="card">
            <h3>音频播放器</h3>
            <div class="code-container">
                <code>[audio]音频直链地址[/audio]</code>
                <button class="copy-btn" data-code="[audio]音频直链地址[/audio]">复制</button>
            </div>
        </div>
        <div class="card">
            <h3>视频播放器</h3>
            <div class="code-container">
                <code>[movie]视频直链地址[/movie]</code>
                <button class="copy-btn" data-code="[movie]视频直链地址[/movie]">复制</button>
            </div>
        </div>
        <div class="card">
            <h3><a href="/bbs-1341662.html">QQ音乐</a></h3>
            <div class="code-container">
                <code>[qqmusic]歌曲ID[/qqmusic]</code>
                <button class="copy-btn" data-code="[qqmusic]歌曲ID[/qqmusic]">复制</button>
            </div>
        </div>
        <div class="card">
            <h3><a href="/bbs-1341702.html">网易音乐</a></h3>
            <div class="code-container">
                <code>[wymusic]歌曲ID[/wymusic]</code>
                <button class="copy-btn" data-code="[wymusic]歌曲ID[/wymusic]">复制</button>
            </div>
        </div>
    </div>

    <div id="advanced" class="tab-content">
        <div class="card">
            <h3><a href="/bbs-1349958.html">可复制的代码块</a></h3>
            <div class="code-container">
                <code>[code]代码内容[/code]</code>
                <button class="copy-btn" data-code="[code]代码内容[/code]">复制</button>
            </div>
        </div>
        <div class="card">
            <h3>全角符号转半角</h3>
            <div class="code-container">
                <code>[text]文本内容[/text]</code>
                <button class="copy-btn" data-code="[text]文本内容[/text]">复制</button>
            </div>
        </div>
        <div class="card">
            <h3>当前时间</h3>
            <div class="code-container">
                <code>[now]</code>
                <button class="copy-btn" data-code="[now]">复制</button>
            </div>
        </div>
        <div class="card">
            <h3>倒计时</h3>
            <div class="code-container">
                <code>[codo]2030-01-01[/codo]</code>
                <button class="copy-btn" data-code="[codo]2030-01-01[/codo]">复制</button>
            </div>
        </div>
    </div>

    <%=WapTool.showDown(wmlVo)%>

    <div class="nav-buttons">
        <a class="nav-btn" href="javascript:history.back()">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left">
                <path d="m12 19-7-7 7-7" />
                <path d="M19 12H5" />
            </svg>
            返回上级
        </a>
        <a class="nav-btn" href="/">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-house">
                <path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8" />
                <path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
            </svg>
            返回首页
        </a>
    </div>

</body>
</html>

<script>
    document.querySelectorAll('.tab').forEach(tab => {
        tab.addEventListener('click', () => {
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
            tab.classList.add('active');
            document.getElementById(tab.dataset.tab).classList.add('active');
        });
    });

    document.querySelectorAll('.copy-btn').forEach(btn => {
        btn.addEventListener('click', async () => {
            try {
                // 使用现代的 Clipboard API
                await navigator.clipboard.writeText(btn.dataset.code);
                
                const originalText = btn.textContent;
                btn.textContent = '已复制!';
                setTimeout(() => {
                    btn.textContent = originalText;
                }, 2000);
            } catch (err) {
                // 如果 Clipboard API 不可用，使用备用方案
                const textArea = document.createElement('textarea');
                textArea.value = btn.dataset.code;
                
                // 将 textarea 放在视图之外
                Object.assign(textArea.style, {
                    position: 'absolute',
                    left: '-9999px',
                    top: '-9999px'
                });
                
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                
                try {
                    document.execCommand('copy');
                    btn.textContent = '已复制!';
                } catch (err) {
                    btn.textContent = '复制失败';
                }
                
                document.body.removeChild(textArea);
                
                setTimeout(() => {
                    btn.textContent = '复制';
                }, 2000);
            }
        });
    });
</script>
</body>
</html>