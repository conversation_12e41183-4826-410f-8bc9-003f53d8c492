using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;

// ⚠️ 安全警告：本文件使用的BLL类存在SQL注入风险
// 这些BLL类位于编译后的DLL文件中，无法直接修复
// 当前的部分修复只是临时措施，核心风险依然存在
using System;
using System.Data;
using System.Linq;
using System.Text;
using System.Collections.Generic;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.WebSite.BBS.Service;
using System.Runtime.Caching;
using YaoHuo.Plugin.BBS.Control;

namespace YaoHuo.Plugin.BBS
{
    public partial class Book_View : MyPageWap
    {
        // 声明竞猜模块控件
        protected GuessingModule GuessingModuleControl;

        private readonly string a = PubConstant.GetAppString("InstanceName");

        public string KL_ShowPreNextTitle_bbs = PubConstant.GetAppString("KL_ShowPreNextTitle_bbs");

        public string id = "0";

        public sys_ad_show_Model adVo = new sys_ad_show_Model();

        public wap_bbs_Model bookVo = new wap_bbs_Model();

        public List<ReplyData> relistVo = null;

        public List<ReplyData> relistVoTop = null;

        public List<wap_bbs_vote_Model> vlistVo = null;

        public List<user_Model> userListVo_IDName = null;

        public string[] facelist;

        public string[] facelistImg;

        public string reShowInfo = "";

        public StringBuilder strhtml = new StringBuilder();

        public string lpage = "";

        public string content = "";

        public string view = "";

        public string viewLeave = "";

        public StringBuilder preNextTitle = new StringBuilder();

        public string ERROR = "";

        public int k = 0;

        public string stype = "";

        public string stypelink = "";

        //public string threePageType = "";

        public string linkURL = "";

        public string http_start_url = "";

        public int totalPage = 0;

        public int pageSize = 1000;

        public int CurrentPage = 1;

        public bool isAdmin = false;

        public string type = "";

        public string showhead = "0";

        public user_Model toUserVo = null;

        public string downLink = "";

        public bool isNeedSecret = false;

        protected GuessData guessingData;

        // 新增：用于存储过滤后的回复总数
        public int FilteredReplyCount = 0;

        protected BetInfo userBet;

        public long? WinningOptionId { get; set; }
        public string WinningOptionText { get; set; }

        public bool hasVotedAlready = false;

        // 添加广告缓存
        private static readonly MemoryCache _adCache = new MemoryCache("BBSAdCache");

        // 修改缓存策略的创建方式
        private static CacheItemPolicy GetAdCachePolicy() => new CacheItemPolicy
        {
            AbsoluteExpiration = DateTimeOffset.Now.AddMinutes(10)
        };

        private static string GetAdCacheKey(string siteid) => $"AD_BBS_{siteid}";



        protected FaceAndReply FaceAndReply;

        // 新增：新版回帖开关，默认开启
        public bool IsNewReplyUIEnabled { get; private set; } = true;

        protected void Page_Load(object sender, EventArgs e)
        {
            InitAndValidateParameters();
            LoadMainPostAndUserInfo();
            LoadAndFilterReplies();
            ProcessFaceAndReplyInfo();
            LoadVoteInfoAndStatus();
            LoadModulesAndInitControls();
            try
            {
                // ✅ 点击数更新已移至LoadMainPostAndUserInfo方法中，避免重复调用
                if (relistVo == null)
                {
                    try
                    {
                        var replyRepository = new ReplyRepository(this.a);
                        long bookIdParsed = long.Parse(this.id);
                        int previewCount = 30;

                        long currentUserIdForFiltering = 0;
                        if (base.userid != "0" && long.TryParse(base.userid, out var tempUserId))
                        {
                            currentUserIdForFiltering = tempUserId;
                        }

                        bool hideUselessCookie = Request.Cookies["hideUseless"]?.Value == "1";
                        bool isFreeMoneyPost = this.bookVo != null && this.bookVo.freeMoney > 0;
                        bool shouldApplyContentFilter = hideUselessCookie && isFreeMoneyPost;

                        this.relistVoTop = new List<ReplyData>();
                        string sortOrderForTop = "DESC";
                        List<wap_bbsre_Model> topBbsReModels = ReplyHelper.GetTopReplies(base.siteid, bookIdParsed, this.a, sortOrderForTop);

                        if (topBbsReModels != null && topBbsReModels.Any())
                        {
                            List<long> topReplyIds = topBbsReModels.Select(m => m.id).ToList();
                            Dictionary<long, int> topOriginalFloorsMap = replyRepository.GetOriginalFloorsByIds(base.siteid, bookIdParsed, topReplyIds);

                            foreach (var model in topBbsReModels)
                            {
                                this.relistVoTop.Add(new ReplyData
                                {
                                    Id = model.id,
                                    SiteId = model.devid,
                                    UserId = model.userid,
                                    Nickname = model.nickname,
                                    ClassId = model.classid,
                                    BookId = model.bookid,
                                    Content = model.content,
                                    ReplyDate = model.redate,
                                    MyGetMoney = (int)model.myGetMoney,
                                    BookTop = (short)model.book_top,
                                    AttachCount = model.isdown,
                                    ReplyToFloor = model.reply,
                                    OriginalFloor = topOriginalFloorsMap.ContainsKey(model.id) ? topOriginalFloorsMap[model.id] : 0
                                });
                            }
                        }

                        int pageSizeToFetch = previewCount;
                        if (shouldApplyContentFilter)
                        {
                            pageSizeToFetch = previewCount * 3;
                        }

                        List<ReplyData> fetchedNonTopRepliesData = replyRepository.GetRepliesPaged(
                            bookIdParsed,
                            siteid,
                            pageNumber: 1,
                            pageSize: pageSizeToFetch,
                            sortOrder: "DESC",
                            mainUserId: null,
                            enableSqlFilter: false,
                            currentUserId: currentUserIdForFiltering
                        );

                        List<ReplyData> repliesToDisplayAfterContentFilter = fetchedNonTopRepliesData;
                        if (shouldApplyContentFilter)
                        {
                            repliesToDisplayAfterContentFilter = ReplyHelper.ApplySecondaryFilter(fetchedNonTopRepliesData, shouldApplyContentFilter, currentUserIdForFiltering);
                        }

                        List<ReplyData> regularRepliesPreview = repliesToDisplayAfterContentFilter.Take(previewCount).ToList();

                        List<ReplyData> finalRepliesForView = new List<ReplyData>();
                        if (this.relistVoTop.Any())
                        {
                            finalRepliesForView.AddRange(this.relistVoTop);
                        }
                        if (regularRepliesPreview.Any())
                        {
                            finalRepliesForView.AddRange(regularRepliesPreview);
                        }
                        this.relistVo = finalRepliesForView;

                        // 加载已删除的回复
                        LoadDeletedReplies(bookIdParsed, siteid);

                        int nonTopFilteredCount = replyRepository.GetTotalReplyCount(
                            bookIdParsed,
                            siteid,
                            null,
                            false,
                            false,
                            null,
                            currentUserId: currentUserIdForFiltering
                        );
                        this.FilteredReplyCount = nonTopFilteredCount;
                    }
                    catch (Exception)
                    {
                        // 问题已修复，移除网页上的详细错误输出。可选：此处可记录后端日志。
                    }
                }
                string siteDefault = WapTool.GetSiteDefault(siteVo.Version, 33);
                if (siteDefault != "1" && this.relistVo != null && this.relistVo.Any())
                {
                    // ✅ 使用UserInfoCacheService获取回复用户信息（与其他页面保持一致）
                    var userIds = this.relistVo.Where(r => r.UserId > 0).Select(r => r.UserId).Distinct().ToList();
                    string connectionString = PubConstant.GetConnectionString(a);
                    var userBasicInfos = UserInfoCacheService.GetUserBasicInfoBatch(userIds, connectionString);

                    // 转换为user_Model格式（保持兼容性）
                    this.userListVo_IDName = new List<user_Model>();
                    foreach (var basicInfo in userBasicInfos)
                    {
                        var userModel = new user_Model
                        {
                            userid = basicInfo.UserId,
                            nickname = basicInfo.Nickname,
                            endTime = basicInfo.EndTime,
                            SessionTimeout = basicInfo.SessionTimeout,
                            LastLoginTime = basicInfo.LastLoginTime,
                            headimg = basicInfo.HeadImg,
                            idname = basicInfo.IdName,
                            siteid = long.Parse(siteid)
                        };
                        this.userListVo_IDName.Add(userModel);
                    }
                }
                else
                {
                    this.userListVo_IDName = new List<user_Model>();
                }

                // 访问统计
                VisiteCount("正在浏览贴子:<a href=\"" + http_start + "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id + "\">" + bookVo.book_title + "</a>");

                // 用户行为统计
                Action_user_doit(3);

                // 新增：从数据库读取新版回帖开关状态，使用共享方法
                this.IsNewReplyUIEnabled = UserPreferencesRepository.GetUserNewReplyUIEnabled(userid, a, Request);
            }
            catch (Exception)
            {
                // 问题已修复，移除网页上的详细错误输出。可选：此处可记录后端日志。
            }
        }

        /// <summary>
        /// 初始化并校验页面参数，包括栏目ID、帖子ID、分页、视图类型、栏目关闭、权限等。
        /// Initializes and validates page parameters, including class ID, post ID, paging, view type, column closure, permissions, etc.
        /// </summary>
        /// <remarks>
        /// 拆分自 Page_Load，确保参数获取、校验、权限检查等逻辑集中，便于维护和测试。
        /// Extracted from Page_Load to centralize parameter acquisition, validation, and permission checks for maintainability and testability.
        /// </remarks>
        private void InitAndValidateParameters()
        {
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID非论模块。", "");
            }
            id = GetRequestValue("id");
            lpage = GetRequestValue("lpage");
            if (lpage == "")
            {
                lpage = "1";
            }
            view = GetRequestValue("view");
            viewLeave = GetRequestValue("viewleave");
            stype = GetRequestValue("stype");
            type = WapTool.GetSiteDefault(siteVo.Version, 27);
            showhead = WapTool.GetArryString(classVo.smallimg, '|', 38);
            downLink = WapTool.GetArryString(classVo.smallimg, '|', 20).Trim().Replace("[stype]", stype);
            if (WapTool.IsNumeric(stype))
            {
                stypelink = "&amp;stype=" + stype;
            }
            if (!WapTool.IsNumeric(id))
            {
                ShowTipInfo("帖子ID参数错误！", "bbs/book_list.aspx?siteid=" + siteid + "&amp;classid=" + classid + stypelink);
            }
            if ("1".Equals(WapTool.GetArryString(classVo.smallimg, '|', 0)))
            {
                ShowTipInfo("此栏目已关闭！", "wapindex.aspx?siteid=" + siteid + "&amp;classid=" + classVo.childid);
            }
            if (classVo.topicID != "" && classVo.topicID != "0" && IsCheckManagerLvl("|00|01|03|04|", ""))
            {
                isNeedSecret = true;
            }
            isAdmin = IsCheckManagerLvl("|00|01|03|04|", classVo.adminusername);
            CheckUserViewSubMoney("BBS" + id, GetUrlQueryString(), "bbs/book_list.aspx?siteid=" + siteid + "&amp;classid=" + classid + stypelink);
            pageSize = Convert.ToInt32(userVo.MaxPerPage_Content);
            if (pageSize < 100)
            {
                pageSize = Convert.ToInt32(siteVo.MaxPerPage_Content);
            }
            if (pageSize < 100)
            {
                pageSize = 100;
            }
            if (GetRequestValue("vpage") != "")
            {
                CurrentPage = int.Parse(GetRequestValue("vpage"));
                if (CurrentPage < 1)
                {
                    CurrentPage = 1;
                }
            }
            CheckFunction("bbs", CurrentPage);
        }

        /// <summary>
        /// 加载主帖和用户信息，包括主帖内容、用户信息、广告、内容处理、分页链接等。
        /// Loads main post and user info, including post content, user info, ads, content processing, and paging links.
        /// </summary>
        /// <remarks>
        /// 拆分自 Page_Load，集中主帖、用户、广告、内容处理等相关逻辑，便于维护。
        /// Extracted from Page_Load to centralize main post, user, ad, and content processing logic for maintainability.
        /// </remarks>
        private void LoadMainPostAndUserInfo()
        {
            string backurl = "bbs/book_list.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + lpage + stypelink;
            wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(a);
            if (CurrentPage == 1)
            {
                // ✅ 使用批量处理服务更新点击数，减少数据库压力和死锁风险
                try
                {
                    ClickBatchService.QueueClick(siteid, id);
                }
                catch (Exception ex)
                {
                    // 记录日志，但不影响页面加载
                    System.Diagnostics.Debug.WriteLine($"批量点击数队列添加失败 (LoadMainPostAndUserInfo): PostId={id}, Error={ex.Message}");
                }
            }
            bookVo = wap_bbs_BLL.GetModel(long.Parse(id));
            if (bookVo == null)
            {
                ShowTipInfo("帖子已删除，或不存在。", backurl);
            }
            else if (bookVo.ischeck == 1L)
            {
                ShowTipInfo("正在审核中！", backurl);
            }
            else if (bookVo.book_classid.ToString() != classid)
            {
                ShowTipInfo("栏目ID不正确！", "");
            }
            else if (bookVo.ischeck == 2L)
            {
                CheckManagerLvl("04", classVo.adminusername, "bbs/book_list.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;lpage=" + lpage + "&amp;id=" + id);
            }
            bookVo.book_title = WapTool.GetShowImg(bookVo.book_title, "200", "bbs");
            wmlVo.title = bookVo.book_title;
            wmlVo.id = bookVo.id;
            user_BLL user_BLL = new user_BLL(a);
            toUserVo = user_BLL.getUserInfo(bookVo.book_pub, siteid);
            if (toUserVo == null)
            {
                toUserVo = user_BLL.getUserInfo(siteid, siteid);
                toUserVo.nickname = "游客";
                toUserVo.city = "火星";
                toUserVo.userid = 0L;
                toUserVo.remark = "";
            }
            http_start_url = http_start + "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;lpage=" + lpage + stypelink;

            // ✅ 使用AdvertisementService获取广告信息
            string connectionString = PubConstant.GetConnectionString(a);
            adVo = AdvertisementService.GetBBSAdvertisementSafely(siteid, connectionString);
            content = bookVo.book_content;
            content = content.Replace("[id]", id);
            content = ProcessCodeTags(content);
            if (view != "all")
            {
                content = BookViewHelper.ProcessContent(content, ref totalPage, CurrentPage, pageSize, viewLeave);
                if (content != "")
                {
                    content = "<!--listS-->" + content + "<!--listE-->";
                }
                // 添加用于"加载更多"功能的容器元素，修复 KL_common.js 中的 null 引用错误
                content += "<div id=\"KL_show_next_list\" style=\"display:none\"></div>";
                linkURL = WapTool.GetPageContentLink(ver, lang, totalPage, pageSize, CurrentPage, http_start_url + "&amp;id=" + id);
            }
            wap2_attachment_BLL attachmentBLL = new wap2_attachment_BLL(a);
            string attachmentContent = AttachmentService.ProcessAttachments(
                bookVo.isdown,
                classVo.smallimg,
                siteid,
                classid,
                id,
                lpage,
                stypelink,
                siteVo.SaveUpFilesPath,
                siteVo.sitemoneyname,
                http_start,
                attachmentBLL
            );
            content += attachmentContent;
            KL_ShowPreNextTitle_bbs = WapTool.GetSystemAndMyConfig(KL_ShowPreNextTitle_bbs, WapTool.GetArryString(classVo.smallimg, '|', 15));
            if (!"1".Equals(KL_ShowPreNextTitle_bbs))
            {
                string text2 = stype == "" ? classid : (classid + " and topic=" + stype);
                // ⚠️ 安全警告：此处仍使用不安全的BLL方法，存在SQL注入风险
                preNextTitle = wap_bbs_BLL.GetPreNextTitle(ver, lang, http_start_url, siteid, text2, id, "desc");
            }
            else
            {
                preNextTitle.Append("");
            }
        }


        // 新增：在页面输出js变量，便于前端感知新版回帖开关
        public string GetNewReplyUIJsVar()
        {
            return string.Format("<script type=\"text/javascript\">var serverSideNewReplyUIEnabled = {0};</script>", this.IsNewReplyUIEnabled.ToString().ToLower());
        }

        /// <summary>
        /// 加载并过滤回复列表，包括置顶回复、普通回复、内容过滤、已删除回复处理及总数统计。
        /// Loads and filters reply list, including top replies, normal replies, content filtering, deleted reply handling, and total count statistics.
        /// </summary>
        /// <remarks>
        /// 拆分自 Page_Load，集中回复数据加载、过滤、已删除回复处理等逻辑，便于维护。
        /// Extracted from Page_Load to centralize reply data loading, filtering, and deleted reply handling for maintainability.
        /// </remarks>
        private void LoadAndFilterReplies()
        {
            if (relistVo == null)
            {
                try
                {
                    var replyRepository = new ReplyRepository(this.a);
                    long bookIdParsed = long.Parse(this.id);
                    int previewCount = 30;

                    long currentUserIdForFiltering = 0;
                    if (base.userid != "0" && long.TryParse(base.userid, out var tempUserId))
                    {
                        currentUserIdForFiltering = tempUserId;
                    }

                    bool shouldApplyFullFilter = ReplyHelper.ShouldApplySqlFilter(Request, this.bookVo);

                    List<ReplyData> allRepliesIncludingTop = replyRepository.GetRepliesPaged(
                        bookIdParsed,
                        siteid,
                        1,
                        99999,
                        "DESC",
                        null,
                        false,
                        currentUserIdForFiltering,
                        true
                    );

                    // 2. 应用过滤（包含置顶的完整数据）
                    List<ReplyData> allFilteredRepliesIncludingTop = ReplyHelper.ApplySecondaryFilter(
                        allRepliesIncludingTop,
                        shouldApplyFullFilter,
                        currentUserIdForFiltering
                    );

                    // 3. 获取置顶回复ID列表（使用与Book_Re相同的方法）
                    List<wap_bbsre_Model> topBbsReModels = ReplyHelper.GetTopReplies(base.siteid, bookIdParsed, this.a, "DESC");
                    List<long> topReplyIds = topBbsReModels?.Select(m => m.id).ToList() ?? new List<long>();
                    var topIdsSet = new HashSet<long>(topReplyIds);

                    // 4. 从过滤后的数据中分离置顶和普通回复（🔧 修复：使用ID匹配，与Book_Re逻辑一致）
                    List<ReplyData> topRepliesFromFiltered = allFilteredRepliesIncludingTop.Where(r => topIdsSet.Contains(r.Id)).ToList();
                    List<ReplyData> normalRepliesFromFiltered = allFilteredRepliesIncludingTop.Where(r => !topIdsSet.Contains(r.Id)).ToList();

                    this.FilteredReplyCount = normalRepliesFromFiltered.Count; // 只计算普通回复数量

                    this.relistVoTop = topRepliesFromFiltered;

                    int skipCount = (this.CurrentPage - 1) * previewCount;
                    int takeCount = previewCount;

                    if (this.CurrentPage == 1)
                    {
                        takeCount = previewCount - this.relistVoTop.Count;
                    }

                    List<ReplyData> pagedNormalReplies = normalRepliesFromFiltered.Skip(skipCount).Take(takeCount).ToList();

                    List<ReplyData> finalRepliesForView = new List<ReplyData>();

                    if (this.CurrentPage == 1 && this.relistVoTop.Any())
                    {
                        finalRepliesForView.AddRange(this.relistVoTop);
                    }

                    finalRepliesForView.AddRange(pagedNormalReplies);
                    this.relistVo = finalRepliesForView;

                    LoadDeletedReplies(bookIdParsed, siteid);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error in LoadAndFilterReplies (Book_View.aspx.cs): {ex.Message}");
                    this.ERROR = "加载回复时出错，请稍后重试。";
                    if (this.FilteredReplyCount == 0 && this.relistVo != null)
                    {
                        this.FilteredReplyCount = this.relistVo.Count(r => r.BookTop == 0);
                    }
                    else if (this.FilteredReplyCount == 0)
                    {
                        this.FilteredReplyCount = 0;
                    }
                }
            }
        }

        /// <summary>
        /// 加载已删除的回复(用于Book_View页面)
        /// </summary>
        /// <param name="bookId">主帖ID</param>
        /// <param name="siteId">站点ID</param>
        /// <remarks>
        /// 此方法的主要目的：
        /// 1. 获取当前帖子下所有已删除的回复(isCheck=2/3)
        /// 2. 处理这些已删除回复的显示方式，在页面上以占位符形式展示
        /// 3. 确保"回复X楼"等引用链接仍然有效，即使被引用的楼层已被删除
        /// 4. 对于用户删除(isCheck=2)的回复显示"此回复已被用户删除"
        /// 5. 对于管理员删除(isCheck=3)的回复显示"此回复已被管理员删除"
        /// 
        /// 这种机制确保了帖子楼层结构的完整性，使楼层号保持稳定不变
        /// 即使内容被删除，但楼层的"位置"和"编号"依然存在
        /// </remarks>
        /// <summary>
        /// 加载已删除回复的楼层映射信息，仅用于保持楼层编号连续性，不在前端显示
        /// 注意：此方法不会将已删除回复添加到显示列表中，只是为了确保楼层编号的正确计算
        /// </summary>
        private void LoadDeletedReplies(long bookId, string siteId)
        {
            // ✅ 根据用户需求，Book_View 页面不需要显示已删除回复的占位符
            // 楼层编号的连续性已经通过 ReplyRepository 中的 CTE 查询保证
            // 此方法保留为空实现，避免影响现有调用逻辑

            // 如果将来需要处理已删除回复的特殊逻辑，可以在这里添加
            // 但不应该将已删除回复添加到 this.relistVo 显示列表中
        }

        /// <summary>
        /// 处理表情列表与回帖信息，包括表情数据、回帖展示信息等。
        /// Processes face list and reply info, including face data and reply display info.
        /// </summary>
        /// <remarks>
        /// 拆分自 Page_Load，集中表情与回帖信息处理逻辑，便于维护。
        /// Extracted from Page_Load to centralize face and reply info processing logic for maintainability.
        /// </remarks>
        private void ProcessFaceAndReplyInfo()
        {
            try
            {
                var processedFaceInfo = BBSHelper.GetProcessedFaceAndReShowInfo(this.classVo);
                this.facelist = processedFaceInfo.FaceList;
                this.facelistImg = processedFaceInfo.FaceListImg;
                this.reShowInfo = processedFaceInfo.ReShowInfo;
            }
            catch (Exception)
            {
                this.facelist = Array.Empty<string>();
                this.facelistImg = Array.Empty<string>();
                this.reShowInfo = "";
            }
        }

        /// <summary>
        /// 加载投票信息及投票状态，包括投票选项、用户是否已投票等。
        /// Loads vote info and status, including vote options and whether the user has already voted.
        /// </summary>
        /// <remarks>
        /// 拆分自 Page_Load，集中投票信息加载与投票状态判断逻辑，便于维护。
        /// Extracted from Page_Load to centralize vote info loading and status checking logic for maintainability.
        /// </remarks>
        private void LoadVoteInfoAndStatus()
        {
            if (bookVo.isVote == 1L)
            {
                // ✅ 使用DapperHelper安全查询投票信息
                string connectionString = PubConstant.GetConnectionString(a);
                string voteSql = @"SELECT TOP 100 * FROM wap_bbs_vote
                                   WHERE siteid = @SiteId AND id = @PostId
                                   ORDER BY vid";
                vlistVo = DapperHelper.Query<wap_bbs_vote_Model>(connectionString, voteSql, new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    PostId = DapperHelper.SafeParseLong(id, "帖子ID")
                })?.ToList();

                // 保持原有的投票状态检查逻辑
                wap_bbs_vote_BLL wap_bbs_vote_BLL = new wap_bbs_vote_BLL(a);
                string whoVote = wap_bbs_vote_BLL.GetWhoVote(long.Parse(id));
                hasVotedAlready = (whoVote.IndexOf(userid + ",") >= 0);
            }
        }

        /// <summary>
        /// 加载竞猜、点赞等模块状态，并初始化相关控件属性。
        /// Loads modules such as guessing and like status, and initializes related control properties.
        /// </summary>
        /// <remarks>
        /// 拆分自 Page_Load，集中竞猜、点赞、控件初始化等模块状态加载逻辑，便于维护。
        /// Extracted from Page_Load to centralize guessing, like, and control initialization logic for maintainability.
        /// </remarks>
        private void LoadModulesAndInitControls()
        {
            BBSGuessService guessHandler = new BBSGuessService(this);
            guessingData = guessHandler.GetGuessingData(long.Parse(id));
            if (guessingData != null)
            {
                userBet = guessHandler.ProcessGuessing(id, userid, guessingData);
            }

            if (guessingData != null && GuessingModuleControl != null)
            {
                GuessingModuleControl.GuessingData = guessingData;
                GuessingModuleControl.UserVo = userVo;
                GuessingModuleControl.UserBet = userBet;
                GuessingModuleControl.WinningOptionId = WinningOptionId.HasValue ? (int?)WinningOptionId.Value : null;
                GuessingModuleControl.WinningOptionText = WinningOptionText;
                GuessingModuleControl.HttpStart = this.http_start;
            }

            if (FaceAndReply != null)
            {
                FaceAndReply.BookVo = bookVo;
                FaceAndReply.FaceList = facelist;
                FaceAndReply.FaceListImg = facelistImg;
                FaceAndReply.ReShowInfo = reShowInfo;
                FaceAndReply.HttpStart = http_start;
                FaceAndReply.Id = id;
                FaceAndReply.SiteId = siteid;
                FaceAndReply.LPage = lpage;
                FaceAndReply.ClassId = classid;
            }
        }

        /// <summary>
        /// 判断当前用户在当前派币帖是否需要显示验证码
        /// </summary>
        /// <returns>是否需要显示验证码</returns>
        protected bool ShouldShowCaptchaForFreeMoneyPost()
        {
            try
            {
                // 检查帖子是否为派币帖
                if (bookVo == null || bookVo.freeMoney <= 0) return false;

                // 检查用户ID是否有效
                long currentUserId;
                if (!long.TryParse(base.userid, out currentUserId)) return false;

                // 调用配置服务检查（传递剩余派币金额以支持派币完成后跳过验证码）
                return YaoHuo.Plugin.WebSite.Services.Config.BBSConfigService
                    .RequiresCaptchaForFreeMoneyPost(currentUserId, bookVo.freeMoney, bookVo.freeLeftMoney);
            }
            catch
            {
                // 异常时默认不显示验证码
                return false;
            }
        }


    }
}