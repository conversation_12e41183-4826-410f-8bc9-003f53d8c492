﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_View.aspx.cs" Inherits="YaoHuo.Plugin.Album.Book_View" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
Response.Write(WapTool.showTop(bookVo.book_title, wmlVo));
    strhtml.Append("<!--web-->");
    if (adVo.threeShowTop != "")
    {
        strhtml.Append(adVo.threeShowTop );
    }
    strhtml.Append("<div class=\"title\">" + bookVo.book_title +"</div>" );
    if (this.INFO == "OK")
    {
        strhtml.Append("<div class=\"tip\">设置头像成功，进入查看:<a style=\"font-size:unset;margin-left:2px;\" href=\"" + this.http_start + "bbs/userinfo.aspx?touserid="+this.userid+"\">我的空间</a></div>");
    }
    strhtml.Append("<div class=\"content\">");
    //strhtml.Append("<a class=\"urlbtn\" href=\"" + this.http_start + "bbs/userinfo.aspx?siteid=" + this.siteid + "&amp;touserid=" + bookVo.makerid + "&amp;backurl=" + HttpUtility.UrlEncode("album/book_view.aspx?siteid=" + this.siteid + "&classid=" + this.classid + "&id=" + this.id) + "\">" + bookVo.book_author + "</a> 上传于" + string.Format("{0:MM-dd HH:mm}", bookVo.book_date) + " ");
    //strhtml.Append("&nbsp;&nbsp; (" + bookVo.book_click + ")<span class=\"right\"><a class=\"urlbtn\" href=\"" + this.http_start + "album/myalbum.aspx?siteid=" + this.siteid + "&amp;classid=0&amp;touserid=" + bookVo.makerid + "&amp;backurl=" + HttpUtility.UrlEncode("album/book_view.aspx?siteid=" + this.siteid + "&classid=" + this.classid + "&amp;id=" + this.id + "") + "\">TA的相册</a></span><br/>");
    //strhtml.Append("" + content + "<br/>");
    strhtml.Append(linkURL);
    strhtml.Append("<div id=\"KL_margin\"style=\"margin:8px;\"></div>");
    for (int i = 0; (filelist != null && i < filelist.Count); i++)
    {
        if (i == 0 && this.userid == bookVo.makerid.ToString())
        {
            strhtml.Append("<div class=\"btBox\"><div class=\"bt1\">");
            strhtml.Append("<a href=\"" + this.http_start + "album/book_view.aspx?siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;id=" + this.id + "&amp;action=addhead\">设为我的头像</a>");
            strhtml.Append("</div></div>");
        }
        strhtml.Append("<img src=\"" + this.http_start + "album/" + filelist[i].book_file + "\"/><br/>");
    }
    strhtml.Append("</div>");
    string isWebHtml = this.ShowWEB_view(this.classid);
    if (isWebHtml != "")
    {
        string strhtml_list = strhtml.ToString();
        int s = strhtml_list.IndexOf("<!--web-->");
        strhtml_list = strhtml_list.Substring(s, strhtml_list.Length - s);
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml.Replace("[view]", strhtml_list), wmlVo));
        Response.End();
    }
    if (adVo.threeShowDown != "")
    {
        strhtml.Append(adVo.threeShowDown);
    }
    strhtml.Append("<div class=\"btBox\"><div class=\"bt2\">");
    strhtml.Append("<a href=\"" + this.http_start + "album/albumlist.aspx?touserid="+this.userid+"\">返回列表</a>");
    strhtml.Append("<a href=\"/\">返回首页</a>");
    strhtml.Append("</div></div>");
    strhtml.Append(ERROR);
    Response.Write(WapTool.ToWML(strhtml.ToString(), wmlVo));
Response.Write(WapTool.showDown(wmlVo));
%>