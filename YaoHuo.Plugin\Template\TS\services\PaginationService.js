export class PaginationService {
    static getInstance() {
        if (!PaginationService.instance) {
            PaginationService.instance = new PaginationService();
        }
        return PaginationService.instance;
    }
    static init(config) {
        PaginationService.getInstance().initializePagination(config);
    }
    static navigateToPage(page, baseUrl, pageParam = 'page') {
        PaginationService.getInstance().navigateTo(page, baseUrl, pageParam);
    }
    static getCurrentPage() {
        return PaginationService.getInstance().getCurrentPageFromUrl();
    }
    initializePagination(config) {
        this.bindPaginationEvents(config);
    }
    bindPaginationEvents(config) {
        const prevBtn = document.getElementById('prevPageBtn');
        if (prevBtn && !prevBtn.disabled) {
            prevBtn.addEventListener('click', () => {
                if (config.currentPage > 1) {
                    this.navigateTo(config.currentPage - 1, config.baseUrl, config.pageParam);
                }
            });
        }
        const nextBtn = document.getElementById('nextPageBtn');
        if (nextBtn && !nextBtn.disabled) {
            nextBtn.addEventListener('click', () => {
                if (config.currentPage < config.totalPages) {
                    this.navigateTo(config.currentPage + 1, config.baseUrl, config.pageParam);
                }
            });
        }
        if (config.showFirstLast) {
            const firstBtn = document.getElementById('firstPageBtn');
            if (firstBtn && !firstBtn.disabled) {
                firstBtn.addEventListener('click', () => {
                    this.navigateTo(1, config.baseUrl, config.pageParam);
                });
            }
            const lastBtn = document.getElementById('lastPageBtn');
            if (lastBtn && !lastBtn.disabled) {
                lastBtn.addEventListener('click', () => {
                    this.navigateTo(config.totalPages, config.baseUrl, config.pageParam);
                });
            }
        }
        const pageInput = document.getElementById('pageInput');
        if (pageInput) {
            pageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const page = parseInt(pageInput.value);
                    if (page >= 1 && page <= config.totalPages) {
                        this.navigateTo(page, config.baseUrl, config.pageParam);
                    }
                }
            });
        }
        const goBtn = document.getElementById('goPageBtn');
        if (goBtn && pageInput) {
            goBtn.addEventListener('click', () => {
                const page = parseInt(pageInput.value);
                if (page >= 1 && page <= config.totalPages) {
                    this.navigateTo(page, config.baseUrl, config.pageParam);
                }
            });
        }
    }
    navigateTo(page, baseUrl, pageParam = 'page') {
        const url = this.buildPageUrl(page, baseUrl, pageParam);
        window.location.href = url;
    }
    buildPageUrl(page, baseUrl, pageParam = 'page') {
        const currentUrl = new URL(baseUrl || window.location.href);
        if (page === 1) {
            currentUrl.searchParams.delete(pageParam);
        }
        else {
            currentUrl.searchParams.set(pageParam, page.toString());
        }
        return currentUrl.toString();
    }
    getCurrentPageFromUrl(pageParam = 'page') {
        const urlParams = new URLSearchParams(window.location.search);
        const page = urlParams.get(pageParam);
        return page ? parseInt(page) : 1;
    }
    createPaginationHtml(config) {
        const { currentPage, totalPages, showFirstLast, showPrevNext } = config;
        let html = '<div class="flex items-center justify-center gap-4 mt-4">';
        if (showFirstLast && currentPage > 1) {
            html += `<button class="pagination-btn" id="firstPageBtn" ${currentPage === 1 ? 'disabled' : ''}>
                        <i data-lucide="chevrons-left" class="w-5 h-5"></i>
                     </button>`;
        }
        if (showPrevNext !== false) {
            html += `<button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed" 
                            id="prevPageBtn" ${currentPage === 1 ? 'disabled' : ''}>
                        <i data-lucide="chevron-left" class="w-5 h-5"></i>
                     </button>`;
        }
        html += `<div class="flex-1 text-center text-sm text-text-secondary px-2">
                    第 ${currentPage} / ${totalPages} 页
                 </div>`;
        if (showPrevNext !== false) {
            html += `<button class="w-10 h-10 rounded-full bg-white border border-border-normal flex items-center justify-center text-text-secondary transition-all duration-200 hover:bg-primary hover:text-white hover:border-primary disabled:bg-bg-gray-100 disabled:text-text-light disabled:border-border-light disabled:cursor-not-allowed" 
                            id="nextPageBtn" ${currentPage === totalPages ? 'disabled' : ''}>
                        <i data-lucide="chevron-right" class="w-5 h-5"></i>
                     </button>`;
        }
        if (showFirstLast && currentPage < totalPages) {
            html += `<button class="pagination-btn" id="lastPageBtn" ${currentPage === totalPages ? 'disabled' : ''}>
                        <i data-lucide="chevrons-right" class="w-5 h-5"></i>
                     </button>`;
        }
        html += '</div>';
        return html;
    }
    insertPagination(containerId, config) {
        const container = document.getElementById(containerId);
        if (!container)
            return;
        container.innerHTML = this.createPaginationHtml(config);
        this.bindPaginationEvents(config);
        if (typeof window.lucide !== 'undefined') {
            window.lucide.createIcons();
        }
    }
}
export function initPagination() {
    const currentPageElement = document.querySelector('[data-current-page]');
    const totalPagesElement = document.querySelector('[data-total-pages]');
    if (currentPageElement && totalPagesElement) {
        const config = {
            currentPage: parseInt(currentPageElement.dataset.currentPage || '1'),
            totalPages: parseInt(totalPagesElement.dataset.totalPages || '1'),
            baseUrl: window.location.href,
            pageParam: 'page',
            showPrevNext: true
        };
        PaginationService.init(config);
    }
}
export function navigateToPage(page) {
    PaginationService.navigateToPage(page);
}
export function getCurrentPage() {
    return PaginationService.getCurrentPage();
}
export default PaginationService;
