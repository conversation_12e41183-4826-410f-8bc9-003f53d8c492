import { TabSwitchService } from '../services/TabSwitchService.js';
import { FilterService } from '../services/FilterService.js';
export class MedalPage {
    constructor() {
        this.currentFunction = 'apply';
        this.isExperienceMode = false;
    }
    static getInstance() {
        if (!MedalPage.instance) {
            MedalPage.instance = new MedalPage();
        }
        return MedalPage.instance;
    }
    static init() {
        MedalPage.getInstance().initialize();
    }
    static showHelp() {
        MedalPage.getInstance().showHelpModal();
    }
    static hideHelp() {
        MedalPage.getInstance().hideHelpModal();
    }
    initialize() {
        console.log('Medal页面初始化开始');
        this.loadPageConfig();
        this.initLucideIcons();
        this.initFunctionTabs();
        this.initCategoryFilters();
        this.bindHelpModalEvents();
        console.log('Medal页面初始化完成');
    }
    loadPageConfig() {
        const configElement = document.getElementById('page-config');
        if (configElement) {
            this.currentFunction = configElement.dataset.pageType || 'apply';
            this.isExperienceMode = configElement.dataset.isExperienceMode === 'true';
        }
        else {
            const urlParams = new URLSearchParams(window.location.search);
            this.currentFunction = urlParams.get('type') || 'apply';
            this.isExperienceMode = urlParams.has('ui');
        }
        console.log(`Medal页面配置: 功能=${this.currentFunction}, 体验模式=${this.isExperienceMode}`);
    }
    initLucideIcons() {
        if (typeof window.lucide !== 'undefined') {
            window.lucide.createIcons();
            console.log('Medal页面: Lucide图标初始化完成');
        }
    }
    initFunctionTabs() {
        const functionTabConfig = {
            groupSelector: '.function-tab',
            activeClass: 'active',
            updateUrl: true,
            preserveParams: this.isExperienceMode ? ['ui'] : [],
            tabs: [
                {
                    id: 'apply',
                    selector: '[data-function="apply"]',
                    contentSelector: '#applyMedals',
                    urlParam: 'type',
                    urlValue: 'apply',
                    onActivate: () => {
                        this.onApplyTabActivate();
                    }
                },
                {
                    id: 'purchase',
                    selector: '[data-function="purchase"]',
                    contentSelector: '#purchaseMedals',
                    urlParam: 'type',
                    urlValue: 'buy',
                    onActivate: () => {
                        this.onPurchaseTabActivate();
                    }
                }
            ],
            onChange: (activeTab) => {
                this.currentFunction = activeTab.id;
                console.log(`功能切换到: ${activeTab.id}`);
                this.resetCategoryFilter();
            }
        };
        TabSwitchService.initTabGroup('function-tabs', functionTabConfig);
    }
    initCategoryFilters() {
        const applyFilterConfig = {
            filterId: 'apply-filter',
            filterTabsSelector: '#applyFilterTabs .filter-tab',
            itemsConfig: {
                selector: '#applyMedals .medal-item',
                dataAttribute: 'data-category'
            },
            activeClass: 'active',
            defaultFilter: 'all',
            onFilterChange: (filter, count) => {
                console.log(`申请勋章筛选: ${filter}, 显示数量: ${count}`);
            }
        };
        const purchaseFilterConfig = {
            filterId: 'purchase-filter',
            filterTabsSelector: '#purchaseFilterTabs .filter-tab',
            itemsConfig: {
                selector: '#purchaseMedals .medal-item',
                dataAttribute: 'data-category'
            },
            activeClass: 'active',
            defaultFilter: 'all',
            onFilterChange: (filter, count) => {
                console.log(`购买勋章筛选: ${filter}, 显示数量: ${count}`);
            }
        };
        FilterService.initFilter(applyFilterConfig);
        FilterService.initFilter(purchaseFilterConfig);
    }
    onApplyTabActivate() {
        const applyFilterTabs = document.getElementById('applyFilterTabs');
        const purchaseFilterTabs = document.getElementById('purchaseFilterTabs');
        if (applyFilterTabs)
            applyFilterTabs.style.display = 'flex';
        if (purchaseFilterTabs)
            purchaseFilterTabs.style.display = 'none';
    }
    onPurchaseTabActivate() {
        const applyFilterTabs = document.getElementById('applyFilterTabs');
        const purchaseFilterTabs = document.getElementById('purchaseFilterTabs');
        if (applyFilterTabs)
            applyFilterTabs.style.display = 'none';
        if (purchaseFilterTabs)
            purchaseFilterTabs.style.display = 'flex';
    }
    resetCategoryFilter() {
        if (this.currentFunction === 'apply') {
            FilterService.resetFilter('apply-filter');
        }
        else {
            FilterService.resetFilter('purchase-filter');
        }
    }
    bindHelpModalEvents() {
        window.showHelpModal = () => this.showHelpModal();
        window.hideHelpModal = () => this.hideHelpModal();
    }
    showHelpModal() {
        const modal = document.getElementById('helpModal');
        if (modal) {
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            console.log('显示申请流程说明弹窗');
        }
    }
    hideHelpModal() {
        const modal = document.getElementById('helpModal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
            console.log('隐藏申请流程说明弹窗');
        }
    }
    getCurrentFunction() {
        return this.currentFunction;
    }
    getCurrentFilter() {
        const filterId = this.currentFunction === 'apply' ? 'apply-filter' : 'purchase-filter';
        return FilterService.getCurrentFilter(filterId);
    }
    getPageStats() {
        const applyStats = FilterService.getInstance().getFilterStats('apply-filter');
        const purchaseStats = FilterService.getInstance().getFilterStats('purchase-filter');
        return {
            function: this.currentFunction,
            filter: this.getCurrentFilter() || 'all',
            applyCount: applyStats.visible,
            purchaseCount: purchaseStats.visible
        };
    }
}
document.addEventListener('DOMContentLoaded', () => {
    MedalPage.init();
});
export function showHelpModal() {
    MedalPage.showHelp();
}
export function hideHelpModal() {
    MedalPage.hideHelp();
}
export function getMedalPage() {
    return MedalPage.getInstance();
}
export default MedalPage;
