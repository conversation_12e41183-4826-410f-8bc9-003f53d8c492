﻿using System;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Book_View_down : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string lpage = "";

        public string INFO = "";

        public string ERROR = "";

        public string tops = "";

        public wap_bbs_Model bookVo = null;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
            }
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            lpage = GetRequestValue("lpage");
            tops = GetRequestValue("tops");
            CheckManagerLvl("04", classVo.adminusername, "bbs/book_view_admin.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;lpage=" + lpage + "&amp;id=" + id);
            wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(a);
            bookVo = wap_bbs_BLL.GetModel(long.Parse(id));
            if (bookVo == null)
            {
                ShowTipInfo("已删除！或不存在！", "");
            }
            else if (bookVo.ischeck == 1L)
            {
                ShowTipInfo("正在审核中！", "");
            }
            else if (bookVo.book_classid.ToString() != classid)
            {
                ShowTipInfo("栏目ID对不上！可能没有传classid值！", "");
            }
            else if (bookVo.islock == 1L)
            {
                ShowTipInfo("此帖已锁！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bookVo.book_classid + "&amp;id=" + bookVo.id + "&amp;lpage=" + lpage);
            }
            else if (bookVo.islock == 2L)
            {
                ShowTipInfo("此帖已结！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bookVo.book_classid + "&amp;id=" + bookVo.id + "&amp;lpage=" + lpage);
            }
            else if (bookVo.isdown == 1L)
            {
                INFO = "NOTDOWN";
            }
            if (!(action == "gomod"))
            {
                return;
            }
            WapTool.ClearDataBBS("bbs" + siteid + classid);
            WapTool.ClearDataBBS("bbsTop" + siteid + classid);
            try
            {
                if (tops == "1")
                {
                    if (bookVo.isdown == 2L)
                    {
                        INFO = "ERR";
                        return;
                    }
                    if (bookVo.isdown == 1L)
                    {
                        INFO = "NOTDOWN";
                        return;
                    }
                    // ✅ 使用DapperHelper进行安全的参数化更新操作
                    string connectionString = PubConstant.GetConnectionString(a);
                    string lockReason = "{" + userVo.nickname + "(ID" + userVo.userid + ")设沉帖子" + $"{DateTime.Now:MM-dd HH:mm}" + "}<br/>";
                    lockReason += bookVo.whylock;

                    string updateSql = "UPDATE wap_bbs SET isdown = 2, whylock = @WhyLock WHERE userid = @SiteId AND id = @PostId";
                    DapperHelper.Execute(connectionString, updateSql, new {
                        WhyLock = DapperHelper.LimitLength(lockReason, 1000),
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        PostId = DapperHelper.SafeParseLong(id, "帖子ID")
                    });

                    // ✅ 使用DapperHelper进行安全的参数化插入日志
                    string insertLogSql = @"INSERT INTO wap_log(siteid,oper_userid,oper_nickname,oper_type,log_info,oper_ip)
                                          VALUES (@SiteId, @OperUserId, @OperNickname, 0, @LogInfo, @OperIp)";
                    DapperHelper.Execute(connectionString, insertLogSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        OperUserId = DapperHelper.SafeParseLong(userid, "操作用户ID"),
                        OperNickname = DapperHelper.LimitLength(nickname, 50),
                        LogInfo = DapperHelper.LimitLength("用户ID:" + userid + "设沉用户ID:" + bookVo.book_pub + "发表的ID=" + id + "主题:" + bookVo.book_title, 500),
                        OperIp = DapperHelper.LimitLength(IP, 50)
                    });
                    INFO = "OK";
                }
                else if (bookVo.isdown == 0L)
                {
                    INFO = "ERR";
                }
                else if (bookVo.isdown == 1L)
                {
                    INFO = "NOTDOWN";
                }
                else
                {
                    // ✅ 使用DapperHelper进行安全的参数化更新操作
                    string connectionString = PubConstant.GetConnectionString(a);
                    string lockReason = "{" + userVo.nickname + "(ID" + userVo.userid + ")解除沉帖" + $"{DateTime.Now:MM-dd HH:mm}" + "}<br/>";
                    lockReason += bookVo.whylock;

                    string updateSql = "UPDATE wap_bbs SET isdown = 0, whylock = @WhyLock WHERE userid = @SiteId AND id = @PostId";
                    DapperHelper.Execute(connectionString, updateSql, new {
                        WhyLock = DapperHelper.LimitLength(lockReason, 1000),
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        PostId = DapperHelper.SafeParseLong(id, "帖子ID")
                    });

                    // ✅ 使用DapperHelper进行安全的参数化插入消息
                    string book_title = bookVo.book_title.Replace("[", "［").Replace("]", "］");
                    string messageContent = "设置时间：" + DateTime.Now + "[br]论坛主题:[url=/bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bookVo.book_classid + "&amp;id=" + id + "]" + book_title + "[/url]";

                    string insertMessageSql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                                              VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, 1)";
                    DapperHelper.Execute(connectionString, insertMessageSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                        Nickname = DapperHelper.LimitLength(nickname, 50),
                        Title = "您的一个主题取消沉帖!",
                        Content = DapperHelper.LimitLength(messageContent, 500),
                        ToUserId = bookVo.book_pub
                    });

                    // ✅ 使用DapperHelper进行安全的参数化插入日志
                    string insertLogSql = @"INSERT INTO wap_log(siteid,oper_userid,oper_nickname,oper_type,log_info,oper_ip)
                                          VALUES (@SiteId, @OperUserId, @OperNickname, 0, @LogInfo, @OperIp)";
                    DapperHelper.Execute(connectionString, insertLogSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        OperUserId = DapperHelper.SafeParseLong(userid, "操作用户ID"),
                        OperNickname = DapperHelper.LimitLength(nickname, 50),
                        LogInfo = DapperHelper.LimitLength("用户ID:" + userid + "取消设沉用户ID:" + bookVo.book_pub + "发表的ID=" + id + "主题:" + book_title, 500),
                        OperIp = DapperHelper.LimitLength(IP, 50)
                    });
                    INFO = "OK";
                }
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}