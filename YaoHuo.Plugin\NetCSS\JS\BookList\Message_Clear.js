// 获取清理消息提示的链接元素
const cleanMessageLink = document.querySelector('.newMessage a[href="/bbs/Message_Clear.htm"]');

// 添加点击事件监听器
cleanMessageLink.addEventListener('click', function (e) {
    e.preventDefault(); // 阻止默认链接跳转行为

    // 发送异步请求
    fetch('/bbs/messagelist_clear.aspx?action=godelall&siteid=1000&classid=0&id=0&page=1&types=0&issystem=3&backurl=myfile.aspx%3fsiteid%3d1000')
        .then(response => {
            if (response.ok) {
                // 请求成功，移除class="newMessage"元素
                const newMessageElement = document.querySelector('.newMessage');
                if (newMessageElement) {
                    newMessageElement.remove();
                } else {
                    console.error('未找到class="newMessage"元素');
                }
            } else {
                console.error('清理消息提示请求失败');
            }
        })
        .catch(error => {
            console.error('发生错误：', error);
        });
});