import { ValidationRule, ValidationResult, FormFieldConfig, FormValidationConfig } from '../types/CommonTypes.js';

/**
 * 统一的表单验证服务
 * 提供实时验证、错误提示、验证规则配置等功能
 */
export class FormValidationService {
    private static instance: FormValidationService;
    private validationConfigs: Map<string, FormValidationConfig> = new Map();
    private activeValidators: Map<HTMLFormElement, Map<string, HTMLElement>> = new Map();

    private constructor() {}

    public static getInstance(): FormValidationService {
        if (!FormValidationService.instance) {
            FormValidationService.instance = new FormValidationService();
        }
        return FormValidationService.instance;
    }

    /**
     * 注册表单验证配置
     */
    public registerForm(formId: string, config: FormValidationConfig): void {
        this.validationConfigs.set(formId, config);
    }

    /**
     * 初始化表单验证
     */
    public initializeForm(formElement: HTMLFormElement): void {
        const formId = formElement.id;
        const config = this.validationConfigs.get(formId);
        
        if (!config) {
            console.warn(`No validation config found for form: ${formId}`);
            return;
        }

        // 初始化字段验证器映射
        this.activeValidators.set(formElement, new Map());

        // 为每个字段设置验证
        Object.entries(config.fields).forEach(([fieldName, fieldConfig]) => {
            const fieldElement = formElement.querySelector(`[name="${fieldName}"]`) as HTMLElement;
            if (fieldElement) {
                this.setupFieldValidation(fieldElement, fieldConfig, formElement);
            }
        });

        // 设置表单提交验证
        this.setupFormSubmitValidation(formElement, config);
    }

    /**
     * 设置字段验证
     */
    private setupFieldValidation(
        fieldElement: HTMLElement, 
        fieldConfig: FormFieldConfig, 
        formElement: HTMLFormElement
    ): void {
        const inputElement = fieldElement as HTMLInputElement;
        
        // 实时验证（输入时）
        if (fieldConfig.validateOnInput) {
            inputElement.addEventListener('input', () => {
                this.clearFieldError(inputElement);
            });
        }

        // 失焦验证
        if (fieldConfig.validateOnBlur !== false) {
            inputElement.addEventListener('blur', () => {
                this.validateField(inputElement, fieldConfig);
            });
        }

        // 存储字段元素引用
        const validators = this.activeValidators.get(formElement);
        if (validators) {
            validators.set(inputElement.name, inputElement);
        }
    }

    /**
     * 设置表单提交验证
     */
    private setupFormSubmitValidation(formElement: HTMLFormElement, config: FormValidationConfig): void {
        formElement.addEventListener('submit', (e) => {
            if (!this.validateForm(formElement)) {
                e.preventDefault();
                return;
            }

            // 显示提交状态
            if (config.submitButton) {
                this.showSubmitLoading(config.submitButton);
            }
        });
    }

    /**
     * 验证单个字段
     */
    public validateField(fieldElement: HTMLInputElement, fieldConfig: FormFieldConfig): ValidationResult {
        const value = fieldElement.value.trim();
        const result: ValidationResult = { isValid: true, errorMessage: '' };

        // 执行所有验证规则
        for (const rule of fieldConfig.rules) {
            const ruleResult = this.executeValidationRule(value, rule, fieldElement);
            if (!ruleResult.isValid) {
                result.isValid = false;
                result.errorMessage = ruleResult.errorMessage;
                break;
            }
        }

        // 显示验证结果
        if (!result.isValid) {
            this.showFieldError(fieldElement, result.errorMessage || '验证失败');
        } else {
            this.clearFieldError(fieldElement);
        }

        return result;
    }

    /**
     * 验证整个表单
     */
    public validateForm(formElement: HTMLFormElement): boolean {
        const formId = formElement.id;
        const config = this.validationConfigs.get(formId);
        
        if (!config) {
            return true;
        }

        let isFormValid = true;
        const validators = this.activeValidators.get(formElement);

        if (validators) {
            validators.forEach((fieldElement, fieldName) => {
                const fieldConfig = config.fields[fieldName];
                if (fieldConfig) {
                    const result = this.validateField(fieldElement as HTMLInputElement, fieldConfig);
                    if (!result.isValid) {
                        isFormValid = false;
                    }
                }
            });
        }

        return isFormValid;
    }

    /**
     * 执行验证规则
     */
    private executeValidationRule(value: string, rule: ValidationRule, fieldElement: HTMLInputElement): ValidationResult {
        const result: ValidationResult = { isValid: true, errorMessage: '' };

        switch (rule.type) {
            case 'required':
                if (value.length === 0) {
                    result.isValid = false;
                    result.errorMessage = rule.message || '此字段不能为空';
                }
                break;

            case 'minLength':
                if (value.length > 0 && value.length < (rule.value as number)) {
                    result.isValid = false;
                    result.errorMessage = rule.message || `最少需要${rule.value}个字符`;
                }
                break;

            case 'maxLength':
                if (value.length > (rule.value as number)) {
                    result.isValid = false;
                    result.errorMessage = rule.message || `最多允许${rule.value}个字符`;
                }
                break;

            case 'pattern':
                if (value.length > 0 && !(rule.value as RegExp).test(value)) {
                    result.isValid = false;
                    result.errorMessage = rule.message || '格式不正确';
                }
                break;

            case 'email':
                if (value.length > 0 && !/^.+@.+\..+$/.test(value)) {
                    result.isValid = false;
                    result.errorMessage = rule.message || '邮箱格式不正确';
                }
                break;

            case 'mobile':
                if (value.length > 0 && !/^\d{11}$/.test(value)) {
                    result.isValid = false;
                    result.errorMessage = rule.message || '手机号必须为11位数字';
                }
                break;

            case 'qq':
                if (value.length > 0 && !/^\d{5,11}$/.test(value)) {
                    result.isValid = false;
                    result.errorMessage = rule.message || 'QQ号必须为5-11位数字';
                }
                break;

            case 'number':
                if (value.length > 0 && !/^\d+$/.test(value)) {
                    result.isValid = false;
                    result.errorMessage = rule.message || '必须为数字';
                }
                break;

            case 'range':
                const numValue = parseFloat(value);
                const range = rule.value as { min: number; max: number };
                if (value.length > 0 && (isNaN(numValue) || numValue < range.min || numValue > range.max)) {
                    result.isValid = false;
                    result.errorMessage = rule.message || `必须为${range.min}-${range.max}之间的数字`;
                }
                break;

            case 'custom':
                if (rule.validator) {
                    const customResult = rule.validator(value, fieldElement);
                    if (!customResult.isValid) {
                        result.isValid = false;
                        result.errorMessage = customResult.errorMessage;
                    }
                }
                break;
        }

        return result;
    }

    /**
     * 显示字段错误
     */
    private showFieldError(fieldElement: HTMLInputElement, message: string): void {
        this.clearFieldError(fieldElement);
        
        // 添加错误样式
        fieldElement.classList.add('border-red-500', 'error');
        
        // 创建错误消息元素
        const errorDiv = document.createElement('div');
        errorDiv.className = 'form-error text-xs text-red-500 mt-1';
        errorDiv.textContent = message;
        
        // 插入错误消息
        const parent = fieldElement.parentNode;
        if (parent) {
            parent.appendChild(errorDiv);
        }
    }

    /**
     * 清除字段错误
     */
    private clearFieldError(fieldElement: HTMLInputElement): void {
        // 移除错误样式
        fieldElement.classList.remove('border-red-500', 'error', 'border-danger');
        
        // 移除错误消息
        const parent = fieldElement.parentNode;
        if (parent) {
            const existingError = parent.querySelector('.form-error, .text-xs.text-red-500');
            if (existingError) {
                existingError.remove();
            }
        }
    }

    /**
     * 显示提交加载状态
     */
    private showSubmitLoading(buttonConfig: { selector: string; loadingText: string; iconClass?: string }): void {
        const button = document.querySelector(buttonConfig.selector) as HTMLButtonElement;
        if (button) {
            const iconClass = buttonConfig.iconClass || 'loader-2';
            button.innerHTML = `<i data-lucide="${iconClass}" class="w-5 h-5 animate-spin mr-2"></i>${buttonConfig.loadingText}`;
            button.disabled = true;
            
            // 重新创建图标
            setTimeout(() => {
                if (typeof (window as any).lucide !== 'undefined') {
                    (window as any).lucide.createIcons();
                }
            }, 10);
        }
    }

    /**
     * 密码强度验证（专用于密码字段）
     */
    public validatePasswordStrength(password: string): {
        hasLength: boolean;
        hasUppercase: boolean;
        hasLowercase: boolean;
        hasNumber: boolean;
        score: number;
    } {
        return {
            hasLength: password.length >= 6,
            hasUppercase: /[A-Z]/.test(password),
            hasLowercase: /[a-z]/.test(password),
            hasNumber: /\d/.test(password),
            score: this.calculatePasswordScore(password)
        };
    }

    /**
     * 计算密码强度分数
     */
    private calculatePasswordScore(password: string): number {
        let score = 0;
        if (password.length >= 6) score += 25;
        if (/[A-Z]/.test(password)) score += 25;
        if (/[a-z]/.test(password)) score += 25;
        if (/\d/.test(password)) score += 25;
        return score;
    }

    /**
     * 更新密码强度规则显示
     */
    public updatePasswordRules(password: string, confirmPassword: string, ruleElements: {
        length?: HTMLElement;
        uppercase?: HTMLElement;
        lowercase?: HTMLElement;
        number?: HTMLElement;
        match?: HTMLElement;
    }): void {
        const strength = this.validatePasswordStrength(password);

        if (ruleElements.length) {
            this.updateRuleStatus(ruleElements.length, strength.hasLength);
        }
        if (ruleElements.uppercase) {
            this.updateRuleStatus(ruleElements.uppercase, strength.hasUppercase);
        }
        if (ruleElements.lowercase) {
            this.updateRuleStatus(ruleElements.lowercase, strength.hasLowercase);
        }
        if (ruleElements.number) {
            this.updateRuleStatus(ruleElements.number, strength.hasNumber);
        }
        if (ruleElements.match) {
            const passwordsMatch = password.length > 0 && password === confirmPassword;
            this.updateRuleStatus(ruleElements.match, passwordsMatch);
        }
    }

    /**
     * 更新规则状态显示
     */
    private updateRuleStatus(element: HTMLElement, isValid: boolean): void {
        // 检查是否是密码页面的规则元素（通过查找特定的图标类）
        const icon = element.querySelector('.w-4.h-4') || element.querySelector('i');

        if (icon) {
            if (isValid) {
                // 密码页面样式
                if (element.querySelector('.w-4.h-4')) {
                    element.classList.add('text-success');
                    element.classList.remove('text-text-light');
                    icon.classList.remove('opacity-30');
                    icon.classList.add('opacity-100');
                    icon.classList.add('text-success');
                } else {
                    // 通用样式
                    icon.setAttribute('data-lucide', 'check');
                    element.classList.remove('text-gray-500');
                    element.classList.add('text-green-600');
                }
            } else {
                // 密码页面样式
                if (element.querySelector('.w-4.h-4')) {
                    element.classList.remove('text-success');
                    element.classList.add('text-text-light');
                    icon.classList.add('opacity-30');
                    icon.classList.remove('opacity-100');
                    icon.classList.remove('text-success');
                } else {
                    // 通用样式
                    icon.setAttribute('data-lucide', 'x');
                    element.classList.remove('text-green-600');
                    element.classList.add('text-gray-500');
                }
            }

            // 重新创建图标（仅对通用样式）
            if (!element.querySelector('.w-4.h-4')) {
                setTimeout(() => {
                    if (typeof (window as any).lucide !== 'undefined') {
                        (window as any).lucide.createIcons();
                    }
                }, 10);
            }
        }
    }

    /**
     * 创建预定义的验证配置
     */
    public static createProfileValidationConfig(): FormValidationConfig {
        return {
            fields: {
                'tonickname': {
                    rules: [
                        { type: 'required', message: '昵称不能为空' },
                        { type: 'maxLength', value: 15, message: '昵称不能超过15个字符' }
                    ],
                    validateOnBlur: true,
                    validateOnInput: true
                },
                'mobile': {
                    rules: [
                        { type: 'mobile', message: '手机号必须为11位数字' }
                    ],
                    validateOnBlur: true
                },
                'email': {
                    rules: [
                        { type: 'email', message: '邮箱格式不正确' }
                    ],
                    validateOnBlur: true
                },
                'qq': {
                    rules: [
                        { type: 'qq', message: 'QQ号必须为5-11位数字' }
                    ],
                    validateOnBlur: true
                }
            },
            submitButton: {
                selector: '.form-submit',
                loadingText: '保存中...'
            }
        };
    }

    /**
     * 创建密码修改验证配置
     */
    public static createPasswordValidationConfig(): FormValidationConfig {
        return {
            fields: {
                'txtoldPW': {
                    rules: [
                        { type: 'required', message: '原密码不能为空' }
                    ],
                    validateOnBlur: true
                },
                'txtnewPW': {
                    rules: [
                        { type: 'required', message: '新密码不能为空' },
                        { type: 'minLength', value: 6, message: '密码至少需要6个字符' }
                    ],
                    validateOnBlur: true,
                    validateOnInput: true
                },
                'txtrePW': {
                    rules: [
                        { type: 'required', message: '确认密码不能为空' },
                        {
                            type: 'custom',
                            message: '两次密码输入不一致',
                            validator: (value: string, element: HTMLInputElement) => {
                                const newPasswordElement = element.form?.querySelector('[name="txtnewPW"]') as HTMLInputElement;
                                const isValid = newPasswordElement ? value === newPasswordElement.value : false;
                                return { isValid, errorMessage: '两次密码输入不一致' };
                            }
                        }
                    ],
                    validateOnBlur: true,
                    validateOnInput: true
                }
            },
            submitButton: {
                selector: '.form-submit',
                loadingText: '修改中...'
            }
        };
    }

    /**
     * 创建充值表单验证配置
     */
    public static createRMBtoMoneyValidationConfig(): FormValidationConfig {
        return {
            fields: {
                'exchangeAmount': {
                    rules: [
                        { type: 'required', message: '请输入兑换金额' },
                        { type: 'number', message: '金额必须为数字' },
                        { type: 'range', value: { min: 0.01, max: 1000 }, message: '金额必须在0.01-1000之间' }
                    ],
                    validateOnBlur: true,
                    validateOnInput: true
                },
                'password': {
                    rules: [
                        { type: 'required', message: '请输入密码' }
                    ],
                    validateOnBlur: true
                }
            },
            submitButton: {
                selector: '#exchangeBtn',
                loadingText: '兑换中...'
            }
        };
    }
}
