using KeLin.ClassManager.Model;
using System.Text;
using YaoHuo.Plugin.WebSite.BBS.Service;

namespace YaoHuo.Plugin.BBS.Components
{
    /// <summary>
    /// 帖子图标渲染器 - 统一处理所有帖子图标的渲染逻辑
    /// </summary>
    public static class PostIconRenderer
    {
        /// <summary>
        /// 渲染所有帖子图标（包括派币帖变灰功能）
        /// </summary>
        /// <param name="post">帖子模型</param>
        /// <param name="httpStart">HTTP起始路径</param>
        /// <param name="hots">热门阈值</param>
        /// <param name="showTopIcon">是否显示置顶图标</param>
        /// <param name="showHotIcon">是否显示热门图标</param>
        /// <param name="showIndex">是否显示序号</param>
        /// <param name="index">序号</param>
        /// <returns>所有图标的HTML字符串</returns>
        public static string RenderAllIcons(wap_bbs_Model post, string httpStart, string hots, 
            bool showTopIcon = false, bool showHotIcon = true, bool showIndex = false, int index = 0)
        {
            var icons = new StringBuilder();
            
            // 序号显示
            if (showIndex)
            {
                icons.Append($"{index}.");
            }
            
            // 置顶图标（只在版块列表的置顶帖中显示）
            if (showTopIcon)
            {
                if (post.book_top == 1)
                {
                    icons.Append($"<img src=\"{httpStart}NetImages/ding.gif\" alt=\"顶\"/>");
                }
                else if (post.book_top == 2)
                {
                    icons.Append($"<img src=\"{httpStart}NetImages/top.gif\" alt=\"总顶\"/>");
                }
            }
            
            // 热门图标
            if (showHotIcon && post.book_click >= long.Parse(hots))
            {
                icons.Append($"<img src=\"{httpStart}NetImages/huo.gif\" alt=\"火\"/>");
            }
            
            // 精华图标
            if (post.book_good == 1)
            {
                icons.Append($"<img src=\"{httpStart}NetImages/jing.gif\" alt=\"精\"/>");
            }
            
            // 锁定图标
            if (post.islock == 1)
            {
                icons.Append($"<img src=\"{httpStart}NetImages/suo.gif\" alt=\"锁\"/>");
            }
            
            if (post.islock == 2)
            {
                icons.Append($"<img src=\"{httpStart}NetImages/jie.gif\" alt=\"结\"/>");
            }
            
            // 悬赏图标
            if (post.sendMoney > 0)
            {
                icons.Append($"<img src=\"{httpStart}NetImages/shang.gif\" alt=\"赏\"/>");
            }
            
            // 投票图标
            if (post.isVote > 0)
            {
                icons.Append($"<img src=\"{httpStart}NetImages/tuo.gif\" alt=\"投\"/>");
            }
            
            // 附件图标
            if (post.isdown == 1)
            {
                icons.Append($"<img src=\"{httpStart}NetImages/file.gif\" alt=\"附\"/>");
            }
            
            if (post.isdown == 2)
            {
                icons.Append($"<img src=\"{httpStart}NetImages/d.gif\" alt=\"沉\"/>");
            }
            
            // 派币图标（剩余派币为0时不显示）
            // 如果帖子已结束（islock == 2），则不显示派币图标，因为无法回复领取
            // 如果剩余派币为0，则不显示图标（更直观，避免用户反感灰色图标）
            if (post.freeMoney > 0 && post.islock != 2 && post.freeLeftMoney > 0)
            {
                icons.Append($"<img src=\"{httpStart}NetImages/li.gif\" alt=\"礼\"/>");
            }
            
            return icons.ToString();
        }
        
        /// <summary>
        /// 渲染置顶帖图标（专用于版块列表页面）
        /// </summary>
        /// <param name="post">帖子模型</param>
        /// <param name="httpStart">HTTP起始路径</param>
        /// <param name="hots">热门阈值</param>
        /// <returns>置顶帖图标HTML字符串</returns>
        public static string RenderTopPostIcons(wap_bbs_Model post, string httpStart, string hots)
        {
            return RenderAllIcons(post, httpStart, hots, showTopIcon: true, showHotIcon: true, showIndex: false);
        }
        
        /// <summary>
        /// 渲染普通帖子图标（带序号）
        /// </summary>
        /// <param name="post">帖子模型</param>
        /// <param name="httpStart">HTTP起始路径</param>
        /// <param name="hots">热门阈值</param>
        /// <param name="index">序号</param>
        /// <returns>普通帖子图标HTML字符串</returns>
        public static string RenderNormalPostIcons(wap_bbs_Model post, string httpStart, string hots, int index)
        {
            return RenderAllIcons(post, httpStart, hots, showTopIcon: false, showHotIcon: false, showIndex: true, index: index);
        }
        
        /// <summary>
        /// 渲染热门帖子图标（带序号，不显示热门图标避免重复）
        /// </summary>
        /// <param name="post">帖子模型</param>
        /// <param name="httpStart">HTTP起始路径</param>
        /// <param name="hots">热门阈值</param>
        /// <param name="index">序号</param>
        /// <returns>热门帖子图标HTML字符串</returns>
        public static string RenderHotPostIcons(wap_bbs_Model post, string httpStart, string hots, int index)
        {
            return RenderAllIcons(post, httpStart, hots, showTopIcon: false, showHotIcon: false, showIndex: true, index: index);
        }
        
        /// <summary>
        /// 渲染搜索结果帖子图标（带序号，不显示热门图标）
        /// </summary>
        /// <param name="post">帖子模型</param>
        /// <param name="httpStart">HTTP起始路径</param>
        /// <param name="hots">热门阈值</param>
        /// <param name="index">序号</param>
        /// <returns>搜索结果帖子图标HTML字符串</returns>
        public static string RenderSearchPostIcons(wap_bbs_Model post, string httpStart, string hots, int index)
        {
            return RenderAllIcons(post, httpStart, hots, showTopIcon: false, showHotIcon: false, showIndex: true, index: index);
        }
    }
}