using System;
using System.Collections.Generic;

namespace YaoHuo.Plugin.Template.Models
{
    /// <summary>
    /// 消息列表页面数据模型
    /// </summary>
    public class MessageListPageModel : BasePageModelWithPagination
    {
        public MessageListPageModel()
        {
            PageTitle = "消息中心";
        }

        /// <summary>
        /// 消息类型（0=收件箱，2=发件箱）
        /// </summary>
        public string MessageType { get; set; } = "0";

        /// <summary>
        /// 筛选类型（空=所有，0=聊天消息，1=系统消息，2=收藏消息）
        /// </summary>
        public string FilterType { get; set; } = "";

        /// <summary>
        /// 搜索关键字
        /// </summary>
        public string SearchKey { get; set; } = "";

        /// <summary>
        /// 消息列表数据
        /// </summary>
        public List<MessageItemModel> MessagesList { get; set; } = new List<MessageItemModel>();

        /// <summary>
        /// 未读消息数量
        /// </summary>
        public int UnreadCount { get; set; } = 0;

        /// <summary>
        /// 是否显示搜索框
        /// </summary>
        public bool ShowSearchBox { get; set; } = true;

        /// <summary>
        /// 是否显示筛选操作
        /// </summary>
        public bool ShowFilterActions { get; set; } = true;

        /// <summary>
        /// 收件箱是否完全为空（用于控制搜索框和筛选按钮的显示）
        /// </summary>
        public bool IsInboxCompletelyEmpty { get; set; } = false;

        /// <summary>
        /// 返回URL
        /// </summary>
        public string BackUrl { get; set; } = "";

        /// <summary>
        /// 站点ID
        /// </summary>
        public string SiteId { get; set; } = "";

        /// <summary>
        /// 分类ID
        /// </summary>
        public string ClassId { get; set; } = "";

        /// <summary>
        /// 筛选选项数据
        /// </summary>
        public MessageFilterModel FilterOptions { get; set; } = new MessageFilterModel();
    }

    /// <summary>
    /// 单条消息数据模型
    /// </summary>
    public class MessageItemModel
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 消息标题
        /// </summary>
        public string Title { get; set; } = "";

        /// <summary>
        /// 消息标题（截断处理后）
        /// </summary>
        public string TruncatedTitle { get; set; } = "";

        /// <summary>
        /// 发送人昵称
        /// </summary>
        public string SenderName { get; set; } = "";

        /// <summary>
        /// 发送人ID
        /// </summary>
        public long SenderId { get; set; }

        /// <summary>
        /// 收信人昵称（发件箱时使用）
        /// </summary>
        public string ReceiverName { get; set; } = "";

        /// <summary>
        /// 是否为新消息（未读）
        /// </summary>
        public bool IsNew { get; set; } = false;

        /// <summary>
        /// 是否为系统消息
        /// </summary>
        public bool IsSystem { get; set; } = false;

        /// <summary>
        /// 发送时间
        /// </summary>
        public DateTime AddTime { get; set; }

        /// <summary>
        /// 格式化时间显示
        /// </summary>
        public string FormattedTime { get; set; } = "";

        /// <summary>
        /// 友好时间显示（如：5分钟前、昨天）
        /// </summary>
        public string FriendlyTime { get; set; } = "";

        /// <summary>
        /// 查看消息链接
        /// </summary>
        public string ViewUrl { get; set; } = "";

        /// <summary>
        /// 删除消息链接
        /// </summary>
        public string DeleteUrl { get; set; } = "";

        /// <summary>
        /// 收藏消息链接（收件箱时使用）
        /// </summary>
        public string FavoriteUrl { get; set; } = "";

        /// <summary>
        /// 是否可以收藏（收件箱且非收藏消息时为true）
        /// </summary>
        public bool CanFavorite { get; set; } = false;

        /// <summary>
        /// 消息类型标识（用于CSS类名）
        /// </summary>
        public string MessageTypeClass { get; set; } = "chat-message";

        /// <summary>
        /// 发送人头像URL
        /// </summary>
        public string SenderAvatarUrl { get; set; } = "";

        /// <summary>
        /// 发送人是否为默认头像
        /// </summary>
        public bool SenderIsDefaultAvatar { get; set; } = true;

        /// <summary>
        /// 发送人首字母（用于头像fallback显示）
        /// </summary>
        public string SenderFirstChar { get; set; } = "";

        /// <summary>
        /// 收信人头像URL（发件箱时使用）
        /// </summary>
        public string ReceiverAvatarUrl { get; set; } = "";

        /// <summary>
        /// 收信人是否为默认头像（发件箱时使用）
        /// </summary>
        public bool ReceiverIsDefaultAvatar { get; set; } = true;

        /// <summary>
        /// 收信人首字母（发件箱时用于头像fallback显示）
        /// </summary>
        public string ReceiverFirstChar { get; set; } = "";
    }

    /// <summary>
    /// 消息筛选选项模型
    /// </summary>
    public class MessageFilterModel
    {
        /// <summary>
        /// 当前选中的筛选类型
        /// </summary>
        public string CurrentFilter { get; set; } = "";

        /// <summary>
        /// 筛选选项列表
        /// </summary>
        public List<FilterOptionModel> FilterOptions { get; set; } = new List<FilterOptionModel>();

        /// <summary>
        /// 是否显示收藏筛选（收件箱时显示）
        /// </summary>
        public bool ShowFavoriteFilter { get; set; } = true;
    }

    /// <summary>
    /// 筛选选项模型
    /// </summary>
    public class FilterOptionModel
    {
        /// <summary>
        /// 选项值
        /// </summary>
        public string Value { get; set; } = "";

        /// <summary>
        /// 选项显示文本
        /// </summary>
        public string Text { get; set; } = "";

        /// <summary>
        /// 是否为当前选中项
        /// </summary>
        public bool IsActive { get; set; } = false;

        /// <summary>
        /// 选项链接URL
        /// </summary>
        public string Url { get; set; } = "";
    }
}
