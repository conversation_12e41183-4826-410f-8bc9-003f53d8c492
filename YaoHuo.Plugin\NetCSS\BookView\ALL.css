/* 搜索弹窗CSS样式 */
.search-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    display: none;
    z-index: 97;
}

.search-popup {
    max-width: 95%;
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    width: 290px;
    height: 290px;
    border: 2px solid #d4d4d4;
    border-radius: 15px;
    z-index: 98;
}

    .search-popup iframe {
        border-radius: 15px;
        max-width: 100%;
        max-height: 100%;
        border: none;
    }

/* 打赏盖章CSS样式 */
.earnbounty {
    padding-left: 1px;
}

.post-badge {
    font-family: Arial,sans-serif;
    line-height: 20px;
    border: solid 0.1em #d00;
    border-radius: 0.2em;
    color: #d00;
    font-weight: bold;
    padding: 0.1em 0.2em;
    display: inline-block;
    text-transform: uppercase;
    transform-origin: 100% 100%;
    transform: rotate(2deg);
    margin-left: 5px;
    font-size: 14px;
}

.animated-stamp {
    animation: stampAnimation 0.5s ease-in-out;
    animation-delay: 0.5s;
    animation-fill-mode: both;
    animation-iteration-count: 1;
    transform-origin: 100% 100%;
    transform: rotate(2deg);
}

@keyframes stampAnimation {
    0% {
        opacity: 0;
        transform: rotate(2deg) scale(5);
    }

    20% {
        opacity: 0.4;
        transform: rotate(-2deg) scale(0.9);
    }

    40% {
        opacity: 0.6;
        transform: rotate(2deg) scale(1.1);
    }

    60% {
        opacity: 0.7;
        transform: rotate(-2deg) scale(0.95);
    }

    80% {
        opacity: 0.8;
        transform: rotate(2deg) scale(1.03);
    }

    100% {
        opacity: 0.9;
        transform: rotate(2deg) scale(1);
    }
}

/* 打赏弹窗CSS样式 - 移除基础布局部分，保留内部元素样式 */
/* 保留内部 aui-* 样式 */

.aui-grids {
    position: relative;
    overflow: hidden;
    margin-top: 1rem;
    margin-left: 3%;
    text-align: center;
}

.aui-grids-item {
    cursor: pointer;
    font-family: Arvo,serif;
    width: 30.33333%;
    float: left;
    position: relative;
    padding: .7rem 0;
    font-size: .22rem;
    border: 1px solid #1abc9c;
    border-radius: 5px;
    color: #1abc9c;
    text-align: center;
    margin-right: 3%;
    margin-bottom: 10px;
    height: 4rem;
    display: flex;
    background: rgba(255,255,255,.9);
    outline: none;
}

    .aui-grids-item span {
        font-size: 1.45rem;
        width: 100%;
        display: block;
        align-self: center;
        cursor: pointer;
    }

        .aui-grids-item span:last-child {
            letter-spacing: -0.05rem;
        }

.this-card {
    background: #1abc9c;
    border: 1px solid #1abc9c;
    color: #fff;
}

.aui-cell-box {
    margin: 0 auto;
    background: rgba(255,255,255,.9);
    padding: 2%;
    box-shadow: 0 0 20px rgba(0,0,0,.3);
    text-align: center;
}

.info-text {
    font-family: Arvo,serif;
    font-size: 1.1rem;
    color: #666;
    margin-top: .5rem;
    line-height: 15px;
    border: none;
}

.givebtn {
    margin-bottom: 8px !important;
    display: block;
    margin: 0 auto;
    margin-top: .2rem;
    background: #1abc9c;
    color: #fff;
    padding: 8px 10px;
    font-size: 1.2rem;
    border-radius: 5px;
    cursor: pointer;
    outline: none;
    border: none;
}

    .givebtn:hover {
        background: #20ab8e;
    }

.space {
    margin-left: 2px;
    margin-right: 2px;
}

.aui-recharge-box {
    position: relative;
    overflow: hidden;
    background: #fff;
}

button, input {
    -webkit-tap-highlight-color: transparent;
}

/*表情容器*/
.emoticon-popup {
    cursor: pointer;
    touch-action: manipulation;
    margin-top: -3px;
    border-radius: 5px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    position: absolute;
    z-index: 91;
    background-color: white;
    width: 100%;
    max-width: 720px;
}

select {
    background-color: transparent;
    outline: none;
}

    select::-ms-expand, select::-webkit-inner-spin-button, select::-webkit-outer-spin-button {
        display: none;
    }

table {
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
}

table, th, td {
    border: none;
}

/*下拉选择表情*/
.centered-container, .kuaisuhuifu {
    padding-left: 7px;
}

.centered-container {
    margin-top: -2px;
    padding-right: 7px;
}

.newselect * {
    position: relative;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-weight: 300;
}

.newselect button, .newselect input, .newselect optgroup, .newselect select, .newselect textarea {
    color: inherit;
    font: inherit;
    margin: 0;
    background: transparent;
    outline: none;
    border: none;
    border-radius: 0;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.newselect ul, .newselect ol, .newselect menu {
    list-style: none;
}

.ulselect {
    margin-left: 8px;
    margin-bottom: -4px;
    display: inline-block;
    width: 75px;
    height: 18px;
    cursor: pointer;
    background-color: white;
    border-radius: 7px;
}

.select_expand {
    width: 0;
    height: 18px;
    position: absolute;
    top: 0;
    right: 0;
}

    .select_expand::after {
        font-family: sans-serif;
        content: '\003E';
        position: absolute;
        top: 50%;
        right: 0;
        transform: translate(-50%, -50%) rotate(90deg) scaleY(1.75);
        color: #3e3e3e;
        font-size: 12px;
        pointer-events: none;
        z-index: 2;
        opacity: 0.7;
    }

    .select_expand:hover::after {
        opacity: 1;
    }

    .select_expand:checked::after {
        transform: translate(-50%, -50%) rotate(90deg) scaleX(-1) scaleY(1.75);
    }

.select_expandLabel {
    display: block;
    width: 100%;
    height: 18px;
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
    border-radius: 7px;
}

.select_close {
    display: none;
}

.select_closeLabel {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    display: none;
}

.select_items {
    z-index: 90;
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 7px;
    padding-top: 18px;
    border: 1px solid #d4d4d4;
}

.select_input {
    display: none;
}

.select_label {
    display: block;
    height: 0;
    font-size: 12px;
    line-height: 18px;
    overflow: hidden;
    color: #3e3e3e;
    background-color: #fff;
    cursor: pointer;
    padding-left: 8px;
}

.select_option .select_label {
    border-radius: 0;
}

.select_option:last-child .select_label {
    border-radius: 0 0 7px 7px;
}

.select_label-placeholder {
    height: 18px;
    vertical-align: middle;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0.7;
    background-color: transparent;
}

.select_expand:checked + .select_closeLabel {
    display: block;
}

    .select_expand:checked + .select_closeLabel + .select_options .select_label {
        height: 18px;
    }

.select_options {
    margin-top: 0px;
}

.select_expand:checked + .select_closeLabel + .select_options .select_label:hover {
    background-color: #f7f7f7;
}

.select_expand:checked + .select_closeLabel + .select_options + .select_expandLabel {
    display: none;
}

.select_input:checked + .select_label {
    padding-top: 1px;
    height: 18px;
    margin-top: -18px;
    border-radius: 7px;
}

.select_label-placeholder {
    padding-top: 1.5px;
}

.select_option:first-child .select_label {
    border-top-left-radius: 7px;
    border-top-right-radius: 7px;
}

/*复选框通知楼主*/
.cbx {
    margin: auto;
    margin-left: 6px;
    -webkit-user-select: none;
    user-select: none;
    cursor: pointer
}

    .cbx span {
        display: inline-block;
        vertical-align: middle;
        transform: translate3d(0,0,0)
    }

        .cbx span:first-child {
            position: relative;
            width: 18px;
            height: 18px;
            border-radius: 4px;
            transform: scale(1);
            vertical-align: middle;
            border: 1px solid #c7c7c7;
            transition: all .2s ease
        }

            .cbx span:first-child svg {
                position: absolute;
                top: 3px;
                left: 2px;
                fill: none;
                stroke: #fff;
                stroke-width: 2;
                stroke-linecap: round;
                stroke-linejoin: round;
                stroke-dasharray: 16px;
                stroke-dashoffset: 16px;
                transition: all .3s ease;
                transition-delay: .1s;
                transform: translate3d(0,0,0)
            }

            .cbx span:first-child:before {
                content: "";
                width: 100%;
                height: 100%;
                background: #1abc9c;
                display: block;
                transform: scale(0);
                opacity: 1;
                border-radius: 50%
            }

        .cbx span:last-child {
            font-size: 0.85rem;
            opacity: 0.6;
            padding-left: 2px
        }

    .cbx:hover span:first-child {
        border-color: #1abc9c
    }

.inp-cbx:checked + .cbx span:first-child {
    background: #1abc9c;
    border-color: #1abc9c;
    animation: wave .4s ease
}

    .inp-cbx:checked + .cbx span:first-child svg {
        stroke-dashoffset: 0
    }

:root {
    --blue: #007bff;
    --indigo: #6610f2;
    --purple: #6f42c1;
    --pink: #e83e8c;
    --red: #dc3545;
    --orange: #fd7e14;
    --yellow: #ffc107;
    --green: #28a745;
    --teal: #20c997;
    --cyan: #17a2b8;
    --white: #fff;
    --gray: #6c757d;
    --gray-dark: #343a40;
    --primary: #007bff;
    --secondary: #6c757d;
    --success: #28a745;
    --info: #17a2b8;
    --warning: #ffc107;
    --danger: #dc3545;
    --light: #f8f9fa;
    --dark: #343a40;
    --breakpoint-xs: 0;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --font-family-sans-serif: -apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";
    --font-family-monospace: SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace
}

svg:not(:root) {
    overflow: hidden
}

input {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit
}

input {
    overflow: visible
}

    input[type=checkbox] {
        box-sizing: border-box;
        padding: 0
    }

::-webkit-file-upload-button {
    font: inherit;
    -webkit-appearance: button
}

@media print {
    *, ::after, ::before {
        text-shadow: none !important;
        box-shadow: none !important
    }

    @page {
        size: a3
    }

    .tongzhi {
        z-index: 92;
        display: flex;
        align-items: center;
    }
}
