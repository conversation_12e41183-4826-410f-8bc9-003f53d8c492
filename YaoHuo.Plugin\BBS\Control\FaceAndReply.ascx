﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="FaceAndReply.ascx.cs" Inherits="YaoHuo.Plugin.BBS.Control.FaceAndReply" %>
<% if (BookVo != null && BookVo.islock == 0) { %>
    <form name="f" class="custom-form" action="<%= HttpStart %>bbs/book_re.aspx" method="post">
        <%-- 表情下拉开始 --%>
        <span class='newselect'>
            <ul id='faceselect' class='ulselect'>
                <li>
                    <input class='select_close' type='radio' name='face' id='emotion-close' value='' />
                    <span class='select_label select_label-placeholder'>表情</span>
                </li>
                <li class='select_items'>
                    <input class='select_expand' type='radio' name='face' id='emotion-opener' />
                    <label class='select_closeLabel' for='emotion-close'></label>
                    <ul class='select_options'>
                        <% for (int i = 0; (FaceListImg != null && i < FaceListImg.Length); i++) { %>
                            <li class='select_option'>
                                <input class='select_input' type='radio' name='face' id='emotion-<%= i %>' value='<%= FaceListImg[i] %>' />
                                <label class='select_label' for='emotion-<%= i %>'><%= (FaceList != null && i < FaceList.Length ? FaceList[i] : "") %></label>
                            </li>
                        <% } %>
                    </ul>
                    <label class='select_expandLabel' for='emotion-opener'></label>
                </li>
            </ul>
            <span class='tongzhi'>
                <input class="inp-cbx" id="cbx" type="checkbox" name="sendmsg" value="1" style="display: none"/>
                <label class="cbx" for="cbx">
                    <span>
                        <svg width="12px" height="10px" viewbox="0 0 12 10">
                            <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                        </svg>
                    </span>
                    <span>通知楼主</span>
                </label>
            </span>
            <div id="emoticon-container" class="emoticon-popup" style="display: none;"></div>
        </span>
        <%-- 表情下拉结束 --%>

        <%-- 回复文本框 --%>
        <div class='centered-container'>
            <textarea class="retextarea" name="content" minlength="1" maxlength="5000" required="required" 
                placeholder="请不要乱打字回复，以免被加黑" rows="5" style="width:98.6%;margin-bottom:5px;"><%= ReShowInfo %></textarea>
        </div>

        <%-- 隐藏字段 --%>
        <input type="hidden" name="action" value="add"/>
        <input type="hidden" name="id" value="<%= Id %>"/>
        <input type="hidden" name="siteid" value="<%= SiteId %>"/>
        <input type="hidden" name="lpage" value="<%= LPage %>"/>
        <input type="hidden" name="classid" value="<%= ClassId %>"/>

        <%-- 提交按钮 --%>
        <span class='kuaisuhuifu'>
            <input type="submit" name="g" style="transform: translateY(-5%); margin-right: 2px;" value="快速回复"/>
            <a href="<%= HttpStart %>bbs/book_re_addfile.aspx?action=class&amp;siteid=<%= SiteId %>&amp;classid=<%= ClassId %>&amp;id=<%= Id %>&amp;lpage=<%= LPage %>" 
               style="font-size:13px;margin-left:5px;"><%= GetLang("文件回帖|文件回帖|upload file") %></a>
        </span>
        <br/>
    </form>
<% } %>