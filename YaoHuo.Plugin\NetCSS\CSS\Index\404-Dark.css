@font-face{font-family:'Gilda Display';font-style:normal;font-weight:regular;src:url(//lib.baomitu.com/fonts/gilda-display/gilda-display-regular.eot);src:local('Gilda Display'),local('GildaDisplay-Normal'),url(//lib.baomitu.com/fonts/gilda-display/gilda-display-regular.eot?#iefix) format('embedded-opentype'),url(//lib.baomitu.com/fonts/gilda-display/gilda-display-regular.woff2) format('woff2'),url(//lib.baomitu.com/fonts/gilda-display/gilda-display-regular.woff) format('woff'),url(//lib.baomitu.com/fonts/gilda-display/gilda-display-regular.ttf) format('truetype'),url(//lib.baomitu.com/fonts/gilda-display/gilda-display-regular.svg#GildaDisplay) format('svg')}
html{background:#000;color:#ebebeb;overflow:hidden;height:100%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
body{font:unset;color:unset;background:unset}
.static{width:100%;height:100%;position:relative;margin:0;padding:0;top:-100px;opacity:.05;z-index:230;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;user-drag:none}
.error{text-align:center;font-family:'Gilda Display',serif;font-size:95px;font-style:italic;text-align:center;width:100px;height:60px;line-height:60px;margin:auto;position:absolute;top:0;bottom:0;left:-60px;right:0;-webkit-animation:noise 2s linear infinite;animation:noise 2s linear infinite;overflow:default}
.error:after{content:'404';font-family:'Gilda Display',serif;font-size:100px;font-style:italic;text-align:center;width:150px;height:60px;line-height:60px;margin:auto;position:absolute;top:0;bottom:0;left:0;right:0;opacity:0;color:#00f;-webkit-animation:noise-1 .2s linear infinite;animation:noise-1 .2s linear infinite}
.error,.info,.static{display:block}
.info{text-align:center;font-family:'Gilda Display',serif;font-size:15px;font-style:italic;text-align:center;width:200px;height:60px;line-height:60px;margin:auto;position:absolute;top:140px;bottom:0;left:0;right:0;-webkit-animation:noise-3 1s linear infinite;animation:noise-3 1s linear infinite}
.error:before{content:'404';font-family:'Gilda Display',serif;font-size:100px;font-style:italic;text-align:center;width:100px;height:60px;line-height:60px;margin:auto;position:absolute;top:0;bottom:0;left:0;right:0;opacity:0;color:red;-webkit-animation:noise-2 .2s linear infinite;animation:noise-2 .2s linear infinite}
@-webkit-keyframes noise-1{0%,20%,40%,60%,70%,90%{opacity:0}
10%{opacity:.1}
50%{opacity:.5;left:-6px}
80%{opacity:.3}
100%{opacity:.6;left:2px}
}
@keyframes noise-1{0%,20%,40%,60%,70%,90%{opacity:0}
10%{opacity:.1}
50%{opacity:.5;left:-6px}
80%{opacity:.3}
100%{opacity:.6;left:2px}
}
.notFound{display:none}
@-webkit-keyframes noise-2{0%,20%,40%,60%,70%,90%{opacity:0}
10%{opacity:.1}
50%{opacity:.5;left:6px}
80%{opacity:.3}
100%{opacity:.6;left:-2px}
}
@keyframes noise-2{0%,20%,40%,60%,70%,90%{opacity:0}
10%{opacity:.1}
50%{opacity:.5;left:6px}
80%{opacity:.3}
100%{opacity:.6;left:-2px}
}
@-webkit-keyframes noise{0%,100%,3%,42%,44%,5%{opacity:1;transform:scaleY(1)}
4.3%{opacity:1;transform:scaleY(1.7)}
43%{opacity:1;transform:scaleX(1.5)}
}
@keyframes noise{0%,100%,3%,42%,44%,5%{opacity:1;transform:scaleY(1)}
4.3%{opacity:1;transform:scaleY(1.7)}
43%{opacity:1;transform:scaleX(1.5)}
}
@-webkit-keyframes noise-3{0%,100%,3%,42%,44%,5%{opacity:1;transform:scaleY(1)}
4.3%{opacity:1;transform:scaleY(4)}
43%{opacity:1;transform:scaleX(10) rotate(60deg)}
}
@keyframes noise-3{0%,100%,3%,42%,44%,5%{opacity:1;transform:scaleY(1)}
4.3%{opacity:1;transform:scaleY(4)}
43%{opacity:1;transform:scaleX(10) rotate(60deg)}
}