﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Admin_WAPdel.aspx.cs" Inherits="YaoHuo.Plugin.Games.Chat.Admin_WAPdel" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    Response.Write(WapTool.showTop(this.GetLang("聊天内容|聊天內容|Content of the guangbo"), wmlVo));
    Response.Write("<div class=\"subtitle\">" + this.GetLang("删除操作|刪除操作|delete") + "</div>");
    if (this.INFO == "")
    {
        Response.Write("<div class=\"content\"><a href=\"" + this.http_start + "games/chat/admin_WAPdel.aspx?action=godel&amp;id=" + this.id + "&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;page=" + this.page + "\">" + this.GetLang("确定要删除此条记录吗？是！|確定要刪除此條記錄嗎？是！|Are you sure? YES") + "</a></div>");
    }
    else if (this.INFO == "OK")
    {
        Response.Write("<div class=\"tip\"><b>" + this.GetLang("删除成功！|刪除成功！|Deleted successfully!") + "</b></div>");
    }
    else
    {
        Response.Write("<div class=\"tip\">" + this.INFO + "</div>");
    }
    Response.Write("<div class=\"bt1\"><a href=\"" + this.http_start + "games/chat/admin_userlistWAP.aspx?siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;page=" + this.page + "\">" + this.GetLang("返回列表|返回列表|Back to list") + "</a>");
    Response.Write(WapTool.GetVS(wmlVo));
    Response.Write("</div>");
    Response.Write(WapTool.showDown(wmlVo));
%>