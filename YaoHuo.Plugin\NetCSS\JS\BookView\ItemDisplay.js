window.addEventListener('DOMContentLoaded', (event) => {
    // 初始检查 - 如果既没有悬赏也没有派币元素，直接退出
    const hasXuanShang = document.querySelector('.xuanshang') !== null;
    const hasPaiBi = document.querySelector('.paibi') !== null;

    // 如果页面上既没有悬赏元素也没有派币元素，直接退出
    if (!hasXuanShang && !hasPaiBi) {
        if (window.debugMode) {
            console.log('ItemDisplay.js: 页面上没有悬赏或派币元素，不执行任何操作');
        }
        return;
    }

    /**
     * 通用函数，用于处理元素的显示/隐藏和通知逻辑
     * @param {Object} config - 配置对象
     * @returns {boolean} - 是否成功处理了目标元素
     */
    function handleItemDisplay(config) {
        // 查找目标元素
        const targetElements = document.querySelectorAll(config.targetSelector);
        if (targetElements.length === 0) {
            // 静默退出，不输出错误
            return false; // 返回 false 表示未处理成功
        }

        // 获取发帖时间元素
        const dateAndTimeElement = document.querySelector('.DateAndTime');
        if (!dateAndTimeElement) {
            if (window.debugMode) {
                console.log('未找到发帖时间元素');
            }
            return false;
        }

        // 替换日期字符串中的短横线为斜杠，提高兼容性
        const dateTimeString = dateAndTimeElement.textContent.trim().replace(/-/g, '/');

        // 创建一个兼容性更强的日期对象
        const postTime = new Date(dateTimeString);

        // 确保日期解析正确
        if (isNaN(postTime.getTime())) {
            if (window.debugMode) {
                console.log('日期解析错误: 无效的日期格式');
            }
            return false;
        }

        // 获取当前时间并计算时间差
        const currentTime = new Date();
        const timeDifference = currentTime - postTime;

        // 找到提示 HTML 元素
        const notificationElement = document.querySelector(config.notificationSelector);
        if (!notificationElement) {
            if (window.debugMode) {
                console.log(`未找到通知元素: ${config.notificationSelector}`);
            }
            return false;
        }

        // 找到所有标题元素（用于切换显示状态）
        const titleElements = document.querySelectorAll(config.titleSelector);

        // 遍历目标元素
        targetElements.forEach((targetElement, index) => {
            // 提取数字
            const numbers = config.numberSelectors.map(selector => {
                const element = targetElement.querySelector(selector);
                // 简单的错误处理和数字解析
                return element ? parseInt(element.textContent) || 0 : 0;
            });

            // 执行指定的数字处理逻辑（如果有）
            if (config.numberProcessing) {
                config.numberProcessing(targetElement, numbers);
            }

            // 执行特定额外逻辑（如果有）
            if (config.additionalLogic) {
                config.additionalLogic(targetElement);
            }

            // 判断是否隐藏目标元素
            if (config.hideCondition(numbers, timeDifference)) {
                targetElement.style.display = 'none';

                // 判断是否显示通知
                if (config.notificationCondition(numbers, timeDifference)) {
                    notificationElement.style.display = 'block';
                    // 设置指定时间后自动隐藏通知
                    setTimeout(() => {
                        notificationElement.style.display = 'none';
                    }, config.notificationDuration);
                }
            }

            // 添加点击事件切换显示
            if (index < titleElements.length) {
                titleElements[index].addEventListener('click', () => {
                    targetElement.style.display = targetElement.style.display === 'none' ? 'block' : 'none';
                });
            }
        });

        return true; // 返回 true 表示处理成功
    }

    // 定义时间常量
    const twelveHoursInMilliseconds = 12 * 60 * 60 * 1000;
    const thirtySixHoursInMilliseconds = 36 * 60 * 60 * 1000;
    const seventyTwoHoursInMilliseconds = 72 * 60 * 60 * 1000;

    // 处理悬赏逻辑（只在元素存在时）
    if (hasXuanShang) {
        const xuanshangConfig = {
            targetSelector: '.xuanshang',
            numberSelectors: ['.xuanshangshuzi', '.yishangshuzi'],
            hideCondition: (numbers, timeDiff) => numbers[0] === numbers[1], // 悬赏数字 === 已赏数字
            notificationCondition: (numbers, timeDiff) => timeDiff <= seventyTwoHoursInMilliseconds,
            titleSelector: '.biaotiwenzi',
            notificationSelector: '.rectangle-container',
            notificationDuration: 3500
        };
        handleItemDisplay(xuanshangConfig);
    }

    // 处理派币逻辑（只在元素存在时）
    if (hasPaiBi) {
        const paibiConfig = {
            targetSelector: '.paibi',
            numberSelectors: ['.yushuzi'],
            hideCondition: (numbers, timeDiff) => timeDiff >= twelveHoursInMilliseconds && numbers[0] <= 0,
            notificationCondition: (numbers, timeDiff) => timeDiff >= twelveHoursInMilliseconds && timeDiff < thirtySixHoursInMilliseconds && numbers[0] <= 0,
            numberProcessing: (targetElement, numbers) => {
                // 确保 yushuzi 不为负数
                const yushuziElement = targetElement.querySelector('.yushuzi');
                if (yushuziElement && numbers[0] < 0) {
                    yushuziElement.textContent = '0';
                }
            },
            additionalLogic: (targetElement) => {
                // 处理 meirenshuzi 元素去前导零
                const meirenshuziElement = targetElement.querySelector('.meirenshuzi');
                if (meirenshuziElement) {
                    const numberText = meirenshuziElement.textContent;
                    const trimmedNumberText = numberText.replace(/^0+/, '');
                    meirenshuziElement.textContent = trimmedNumberText;
                }
            },
            titleSelector: '.biaotiwenzi',
            notificationSelector: '.rectangle-container',
            notificationDuration: 3500
        };
        handleItemDisplay(paibiConfig);
    }

    // 处理所有 meirenshuzi 元素去前导零（只在派币存在时执行）
    if (hasPaiBi) {
        const meirenshuziElements = document.querySelectorAll('.meirenshuzi');
        meirenshuziElements.forEach(meirenshuziElement => {
            const numberText = meirenshuziElement.textContent;
            const trimmedNumberText = numberText.replace(/^0+/, '');
            meirenshuziElement.textContent = trimmedNumberText;
        });
    }

    // 输出调试信息（如果在调试模式中）
    if (window.debugMode) {
        console.log('ItemDisplay.js 执行结果：');
        console.log('- 悬赏元素存在：' + hasXuanShang);
        console.log('- 派币元素存在：' + hasPaiBi);
    }
}); 