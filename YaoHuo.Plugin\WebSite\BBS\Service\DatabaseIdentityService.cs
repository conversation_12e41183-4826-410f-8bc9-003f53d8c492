using System;
using System.Collections.Generic;
using System.Runtime.Caching;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    /// <summary>
    /// 数据库身份信息服务（用于混合方案的降级查询）
    /// </summary>
    public static class DatabaseIdentityService
    {
        private static readonly MemoryCache _dbIdentityCache = MemoryCache.Default;
        private static readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(30); // 30分钟过期
        
        /// <summary>
        /// 数据库身份配置模型
        /// </summary>
        public class DatabaseIdentityConfig
        {
            public long Id { get; set; }
            public string Name { get; set; }
            public string ColorCode { get; set; }
            public string IconFileName { get; set; }
            /// <summary>
            /// 完整的图标URL路径（与JSON配置保持一致）
            /// </summary>
            public string IconUrl { get; set; }
        }
        
        /// <summary>
        /// 从数据库获取身份配置（带缓存）
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <returns>身份配置字典</returns>
        public static Dictionary<long, DatabaseIdentityConfig> GetIdentityConfigs(string connectionString)
        {
            const string cacheKey = "db_identity_configs";

            // 检查连接字符串
            if (string.IsNullOrEmpty(connectionString))
            {
                System.Diagnostics.Debug.WriteLine("数据库连接字符串为空，返回空配置");
                return new Dictionary<long, DatabaseIdentityConfig>();
            }

            // 先查缓存
            if (_dbIdentityCache.Get(cacheKey) is Dictionary<long, DatabaseIdentityConfig> cached)
            {
                return cached;
            }

            // 缓存未命中，查询数据库
            var configs = QueryIdentityFromDatabase(connectionString);

            // 存入缓存
            var policy = new CacheItemPolicy
            {
                AbsoluteExpiration = DateTimeOffset.Now.Add(_cacheExpiry)
            };
            _dbIdentityCache.Set(cacheKey, configs, policy);

            System.Diagnostics.Debug.WriteLine($"DatabaseIdentityService: 从数据库加载了 {configs.Count} 个身份配置");

            return configs;
        }
        
        /// <summary>
        /// 获取特定身份配置
        /// </summary>
        /// <param name="sessionTimeout">身份ID</param>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <returns>身份配置</returns>
        public static DatabaseIdentityConfig GetIdentityConfig(long sessionTimeout, string connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
            {
                System.Diagnostics.Debug.WriteLine("数据库连接字符串为空，跳过数据库身份查询");
                return null;
            }

            try
            {
                var configs = GetIdentityConfigs(connectionString);
                return configs.TryGetValue(sessionTimeout, out var config) ? config : null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取数据库身份配置失败: {ex.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// 从数据库查询身份信息
        /// </summary>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <returns>身份配置字典</returns>
        private static Dictionary<long, DatabaseIdentityConfig> QueryIdentityFromDatabase(string connectionString)
        {
            try
            {
                // 查询wap2_smallType表中的身份配置
                // ✅ 优化查询条件：只获取有颜色代码的身份配置，排除专题分类和已删除身份
                string sql = @"
                    SELECT
                        id,
                        subclassName,
                        CASE
                            WHEN CHARINDEX('#', subclassName) > 0
                            THEN LEFT(subclassName, CHARINDEX('#', subclassName) - 1)
                            ELSE subclassName
                        END AS identityName,
                        CASE
                            WHEN CHARINDEX('#', subclassName) > 0
                            THEN RIGHT(subclassName, LEN(subclassName) - CHARINDEX('#', subclassName))
                            ELSE ''
                        END AS colorCode
                    FROM wap2_smallType
                    WHERE siteid = 1000
                        AND subclassName IS NOT NULL
                        AND subclassName != ''
                        AND CHARINDEX('#', subclassName) > 0  -- 确保有颜色代码
                        AND id IN (117, 118, 120, 340, 341, 342, 355, 356, 101, 105, 358, 140, 180)  -- 只查询有效身份
                    ORDER BY
                        CASE
                            WHEN id IN (120, 340, 341, 342, 355, 356) THEN 1  -- 彩色昵称
                            WHEN id IN (101, 358, 105, 140, 180) THEN 2        -- VIP身份
                            WHEN id IN (117, 118) THEN 3                       -- 管理员身份
                            ELSE 4
                        END,
                        rank ASC,
                        id ASC";
                
                var results = DapperHelper.Query<dynamic>(connectionString, sql);
                
                var configs = new Dictionary<long, DatabaseIdentityConfig>();
                
                foreach (var row in results)
                {
                    string identityName = row.identityName?.ToString() ?? "";
                    var config = new DatabaseIdentityConfig
                    {
                        Id = row.id,
                        Name = ExtractNameFromSubclassName(identityName),
                        ColorCode = row.colorCode?.ToString() ?? "",
                        IconFileName = ExtractIconFileName(identityName),
                        IconUrl = ExtractIconUrl(identityName) // ✅ 添加完整URL路径
                    };

                    configs[config.Id] = config;
                }
                
                return configs;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查询数据库身份配置失败: {ex.Message}");
                return new Dictionary<long, DatabaseIdentityConfig>();
            }
        }
        
        /// <summary>
        /// 从subclassName中提取身份名称
        /// </summary>
        /// <param name="identityName">身份名称部分</param>
        /// <returns>处理后的名称</returns>
        private static string ExtractNameFromSubclassName(string identityName)
        {
            if (string.IsNullOrEmpty(identityName))
                return "";
                
            // 如果是图标路径，提取文件名
            if (identityName.StartsWith("/") && identityName.Contains(".gif"))
            {
                int lastSlashIndex = identityName.LastIndexOf('/');
                if (lastSlashIndex >= 0 && lastSlashIndex < identityName.Length - 1)
                {
                    return identityName.Substring(lastSlashIndex + 1).Replace(".gif", "");
                }
            }
            
            return identityName;
        }
        
        /// <summary>
        /// 从身份名称中提取图标文件名
        /// </summary>
        /// <param name="identityName">身份名称部分</param>
        /// <returns>图标文件名</returns>
        private static string ExtractIconFileName(string identityName)
        {
            if (string.IsNullOrEmpty(identityName))
                return "";

            // 如果是图标路径，提取文件名
            if (identityName.StartsWith("/") && identityName.Contains(".gif"))
            {
                int lastSlashIndex = identityName.LastIndexOf('/');
                if (lastSlashIndex >= 0 && lastSlashIndex < identityName.Length - 1)
                {
                    return identityName.Substring(lastSlashIndex + 1);
                }
            }

            return ""; // 彩色昵称没有图标
        }

        /// <summary>
        /// 从身份名称中提取完整的图标URL路径
        /// </summary>
        /// <param name="identityName">身份名称部分</param>
        /// <returns>完整的图标URL路径</returns>
        private static string ExtractIconUrl(string identityName)
        {
            if (string.IsNullOrEmpty(identityName))
                return "";

            // 如果是图标路径，直接返回完整路径
            if (identityName.StartsWith("/") && identityName.Contains(".gif"))
            {
                return identityName;
            }

            return ""; // 彩色昵称没有图标
        }
        
        /// <summary>
        /// 清除数据库身份配置缓存
        /// </summary>
        public static void ClearCache()
        {
            _dbIdentityCache.Remove("db_identity_configs");
            System.Diagnostics.Debug.WriteLine("DatabaseIdentityService: 已清除数据库身份配置缓存");
        }
        
        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        /// <returns>缓存统计信息</returns>
        public static string GetCacheStats()
        {
            try
            {
                var cached = _dbIdentityCache.Get("db_identity_configs") as Dictionary<long, DatabaseIdentityConfig>;
                int configCount = cached?.Count ?? 0;
                return $"数据库身份配置缓存: 当前缓存 {configCount} 个身份配置";
            }
            catch (Exception ex)
            {
                return $"获取数据库身份配置缓存统计失败: {ex.Message}";
            }
        }
    }
}