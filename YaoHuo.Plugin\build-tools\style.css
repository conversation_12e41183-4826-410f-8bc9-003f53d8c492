/* YaoHuo.Plugin/Template/CSS/style.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
    /* 头像图片填充模式 - 确保 object-fill 类被生成 */
    .object-fill {
        object-fit: fill;
    }
}

@layer base {
    body {
        @apply font-sans bg-[#E8E8E8] leading-normal;
    }
}

@layer components {
    /* 基础布局组件 */
    .container {
        @apply max-w-[720px] mx-auto bg-bg-primary min-h-screen;
    }
    
    .main-content {
        @apply pt-16 pb-4;
    }

    @media (max-width: 400px) {
        .main-content {
            padding-top: 3.7rem;
        }
    }

    @media (max-width: 350px) {
        .main-content {
            padding-top: 3.1rem;
        }
    }



    /* 昵称字号优化：>=8个字在不同屏幕尺寸下的精细调整 */
    /* 使用更高优先级的选择器来覆盖Tailwind的.text-2xl */

    /* >=8个字且≤400px：1.2rem (19.2px) */
    @media (max-width: 400px) {
        .nickname-display.nickname-small {
            font-size: 1.2rem;
        }
    }

    /* >=8个字且≤350px：1.0rem (16px) */
    @media (max-width: 350px) {
        .nickname-display.nickname-small {
            font-size: 1.0rem;
        }
    }



    /* 昵称字号优化：<8个字在不同屏幕尺寸下的调整 */
    /* 使用更高优先级的选择器来覆盖Tailwind的.text-2xl */

    /* <8个字且≤350px：1.4rem (22.4px) */
    @media (max-width: 350px) {
        .nickname-display:not(.nickname-small) {
            font-size: 1.3rem;
        }
    }


    
    /* 卡片组件 */
    .card {
        @apply bg-white rounded-md shadow mb-4 overflow-hidden mx-4 mt-4;
    }
    
    .card-header {
        @apply p-4 pb-2 border-b border-border-light;
    }
    
    .card-title {
        @apply text-base font-medium text-text-primary flex items-center;
    }
    
    .card-icon {
        @apply text-primary mr-2;
    }
    
    .card-body {
        @apply p-4;
    }
    
    /* 按钮组件 */
    .btn {
        @apply inline-flex items-center justify-center px-4 py-2 rounded text-sm font-medium transition cursor-pointer select-none border border-transparent;
    }
    
    .btn-primary {
        @apply bg-primary text-white border-primary hover:bg-primary-dark hover:border-primary-dark;
    }
    
    .btn-outline {
        @apply bg-transparent text-text-tertiary border-border-normal hover:bg-bg-primary hover:border-border-dark hover:text-text-primary;
    }
    
    .btn-destructive {
        @apply bg-danger text-white border-danger hover:bg-danger-dark hover:border-danger-dark;
    }
    
    .btn-ghost {
        @apply bg-transparent text-primary border-none px-2 py-1 hover:bg-primary-alpha-10 inline-flex items-center justify-center rounded;
        line-height: 1.2;
        min-height: 24px;
    }
    
    /* 网格布局 */
    .grid-2 {
        @apply grid grid-cols-2 gap-3;
    }
    
    .grid-3 {
        @apply grid grid-cols-3 gap-3;
    }
    
    .grid-4 {
        @apply grid grid-cols-4 gap-3;
    }
    
    /* 统计项组件 */
    .stats-item {
        @apply flex flex-col cursor-pointer py-2 transition-all duration-300 relative overflow-hidden rounded-sm hover:-translate-y-0.5 hover:bg-primary-alpha-05;
    }
    
    .stats-grid {
        @apply grid grid-cols-4 py-3 text-center border-b border-border-light;
    }
    
    /* 好友列表项组件 */
    .friend-item {
        @apply flex items-start p-4 bg-white border border-border-light rounded-md transition-all duration-200 hover:border-primary-alpha-30 relative overflow-visible;
    }
    
    /* Toast 提示组件 - 现代简约风格 */
    .toast-base {
        @apply fixed top-20 left-1/2 transform -translate-x-1/2 z-toast min-w-[320px] max-w-[90%] rounded-lg px-4 py-3 shadow-lg flex items-center gap-3 text-sm font-medium opacity-100 transition-all duration-300 border;
    }

    .toast-success {
        @apply toast-base bg-white text-green-800 border-green-200;
    }

    .toast-success .toast-icon {
        @apply text-green-600;
    }

    .toast-error {
        @apply toast-base bg-white text-red-800 border-red-200;
    }

    .toast-error .toast-icon {
        @apply text-red-600;
    }

    .toast-warning {
        @apply toast-base bg-white text-orange-800 border-orange-200;
    }

    .toast-warning .toast-icon {
        @apply text-orange-600;
    }

    .toast-info {
        @apply toast-base bg-white text-blue-800 border-blue-200;
    }

    .toast-info .toast-icon {
        @apply text-blue-600;
    }

    /* MessageDetail风格Toast - 简洁优雅 */
    .toast-messagedetail {
        @apply fixed top-20 left-1/2 transform -translate-x-1/2 z-toast bg-black bg-opacity-70 text-white py-2 px-4 rounded-lg shadow-lg text-sm font-normal transition-all duration-300;
        min-width: auto;
        max-width: 90%;
    }

    /* JavaScript 动态 Toast 组件 - 替代内联样式 */
    .toast-container {
        @apply fixed top-5 right-5 z-toast pointer-events-none;
    }

    .toast-dynamic {
        @apply flex items-center gap-2 min-w-[280px] max-w-[400px] pointer-events-auto transform translate-x-full transition-transform duration-300 ease-out mb-2 px-4 py-3 rounded-lg shadow-[0_4px_12px_rgba(0,0,0,0.15)] text-white;
    }

    .toast-dynamic.show {
        @apply translate-x-0;
    }

    .toast-dynamic-success {
        @apply toast-dynamic bg-success;
    }

    .toast-dynamic-error {
        @apply toast-dynamic bg-error;
    }

    .toast-dynamic-warning {
        @apply toast-dynamic bg-warning;
    }

    .toast-dynamic-info {
        @apply toast-dynamic bg-info;
    }

    /* ========== MessageList 页面专属样式 ========== */
    
    /* 标签和筛选器hover效果 */
    .tab-item:not(.active):hover, 
    .filter-btn:hover:not(.active) { 
        @apply text-text-tertiary bg-bg-primary; 
    }
    
    .btn-outline:hover, 
    .message-item:not(.unread):hover { 
        @apply bg-primary-alpha-05; 
    }
    
    .btn-outline:hover { 
        border-color: rgba(88, 180, 176, 0.2); 
        color: rgba(88, 180, 176, 0.8); 
    }
    
    .message-item.unread:hover { 
        background-color: rgba(88, 180, 176, 0.08); 
    }
    
    /* 未读消息样式 */
    .message-item.unread .user-avatar, 
    .message-item.unread .system-icon { 
        box-shadow: 0 0 0 1px rgba(88, 180, 176, 0.2); 
    }
    
    .message-item.unread .message-time { 
        @apply text-primary font-semibold; 
    }
    
    .message-item.unread .message-preview { 
        @apply text-text-primary; 
    }
    
    .message-item.unread { 
        border-bottom-color: rgb(107 114 128 / 7%); 
    }
    
    /* 已读消息样式 */
    .message-item:not(.unread) .message-time, 
    .message-item:not(.unread) .message-preview { 
        @apply text-text-secondary; 
    }
    
    /* 下拉菜单智能定位 */
    #clean-actions-menu {
        @apply transition-all duration-150 ease-linear;
        transform-origin: top right;
    }
    
    #clean-actions-menu[style*="bottom: 100%"] {
        transform-origin: bottom right;
    }
    
    /* 350px以下响应式优化 */
    @media (max-width: 350px) {
        .message-list .message-item { 
            @apply p-3; 
        }
        .message-list .message-item .unread-dot,
        .message-list .message-item .read-dot-placeholder { 
            @apply mr-2; 
        }
        .message-list .message-item .user-avatar,
        .message-list .message-item .system-icon { 
            @apply w-8 h-8; 
        }
    }

    /* 确认对话框组件 - 替代内联样式 */
    .confirm-overlay {
        @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-modal;
    }

    .confirm-content {
        @apply bg-white rounded-xl p-5 w-[85%] max-w-[360px] shadow-dialog text-center;
    }

    .confirm-title {
        @apply text-lg font-semibold text-text-primary mb-3;
    }

    .confirm-message {
        @apply text-gray-500 mb-6 leading-normal;
        font-size: 15px;
    }

    .confirm-actions {
        @apply flex justify-center gap-3;
    }

    /* 详情模态框组件 - 用于信息展示类模态框 */
    .detail-modal-container {
        @apply bg-white rounded-xl w-[85%] max-w-[400px] shadow-dialog;
    }

    .detail-modal-header {
        @apply px-5 py-4 border-b border-gray-100 flex items-center justify-between;
    }

    .detail-modal-content {
        @apply px-5 py-4 space-y-3;
    }

    .detail-modal-footer {
        @apply px-5 py-4 border-t border-gray-100 flex justify-end;
    }

    /* Tooltip 组件 - UserGuessBook 时间提示 */
    .tooltip-container {
        @apply relative;
    }

    .tooltip-container::after {
        content: attr(data-tooltip);
        @apply absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2.5 py-1.5 bg-black bg-opacity-90 text-white text-xs font-medium rounded-md whitespace-nowrap opacity-0 invisible transition-all duration-300 pointer-events-none z-[9999];
    }

    /* UserInfo 页面智能tooltip样式 */
    .userinfo-tooltip {
        @apply absolute z-50 bg-gray-800 text-white text-sm px-3 py-1 rounded-md shadow-lg pointer-events-none transition-all duration-200 ease-in-out whitespace-nowrap;
        /* 确保在移动端有足够的可读性 */
        min-height: 28px;
        display: flex;
        align-items: center;
    }

    /* 移动端优化 - 768px以下 */
    @media (max-width: 768px) {
        /* tooltip优化 */
        .userinfo-tooltip {
            @apply text-xs px-2 py-1;
            min-height: 24px;
        }

        /* Medal响应式设计 */
        .medal-item {
            @apply px-3;
        }

        .medal-icon {
            @apply mr-2;
        }

        /* 表格响应式 */
        .table-responsive table {
            @apply w-full;
            table-layout: fixed;
        }

        .table-responsive th,
        .table-responsive td {
            @apply px-2 py-1 text-sm;
        }
        /* 移除固定列宽，允许内容自动适应 */
    }



    .tooltip-container::before {
        content: '';
        @apply absolute bottom-full left-1/2 transform -translate-x-1/2 mb-0.5 border-4 border-transparent opacity-0 invisible transition-all duration-300 pointer-events-none z-[9999];
        border-top-color: rgba(0, 0, 0, 0.9);
    }

    .tooltip-container:hover::after,
    .tooltip-container:hover::before {
        @apply opacity-100 visible;
    }
    
    /* 顶部导航栏 */
    .header {
        @apply fixed top-0 w-full max-w-[720px] z-[100] shadow-md text-white;
        background: linear-gradient(135deg, theme('colors.primary') 0%, theme('colors.primary-dark') 100%);
    }
    
    .header-content {
        @apply flex items-center p-4 relative;
    }

    .header-icon {
        @apply w-8 h-8 flex items-center justify-center cursor-pointer rounded transition-colors relative z-20;
    }

    .header-icon:hover {
        @apply bg-white bg-opacity-10;
    }

    .header-title {
        @apply absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2;
        @apply text-lg font-medium z-10 pointer-events-none;
        /* 文本截断优化 */
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 60%; /* 避免与左右按钮重叠 */
    }

    /* Header 响应式优化 */
    @media (max-width: 400px) {
        .header-content {
            padding: 1.1rem;
        }

        .header-title {
            @apply text-lg; /* 保持18px与默认一致 */
            max-width: 65%; /* 小屏幕下适当增加最大宽度 */
        }

        .header-icon {
            @apply w-7 h-7; /* 从32px缩小到28px */
        }
    }

    @media (max-width: 350px) {
        .header-content {
            padding: 1.05rem;
        }

        .header-title {
            @apply text-base; /* 16px，调整为更易读的尺寸 */
            max-width: 70%; /* 更小屏幕下进一步增加最大宽度 */
        }

        .header-icon {
            width: 1.125rem; /* 18px，调整为18px */
            height: 1.125rem;
        }
    }


    
    .header-actions-right {
        @apply ml-auto flex items-center relative z-20;
    }
    
    /* 消息提示 */
    .message {
        @apply p-3 rounded-md mb-0 mx-4 text-sm mt-4;
    }
    
    .message.success {
        @apply text-[#15803d];
    }
    
    .message.error {
        @apply bg-bg-error border border-[#fecaca] text-danger;
    }
    
    .message.warning {
        @apply bg-[#fefce8] border border-[#fde047] text-[#a16207];
    }
    
    .message.info {
        @apply bg-bg-info border border-[#7dd3fc] text-[#0369a1];
    }
    
    /* 下拉菜单 */
    .dropdown {
        @apply relative z-dropdown;
    }
    
    .dropdown-menu {
        @apply absolute left-1/2 top-full mt-2 bg-white rounded shadow-md w-auto min-w-fit z-[90] overflow-hidden opacity-0 invisible -translate-x-1/2 -translate-y-2.5 transition-all pointer-events-none;
    }
    
    .dropdown-menu.show {
        @apply opacity-100 visible translate-y-0 pointer-events-auto;
    }
    
    .dropdown-item {
        @apply py-2 px-3 text-sm text-text-tertiary flex items-center justify-start cursor-pointer transition-colors text-left whitespace-nowrap;
    }
    
    .dropdown-item:hover {
        @apply bg-border-light;
    }
    
    .dropdown-item:first-child {
        @apply rounded-t;
    }
    
    .dropdown-item:last-child {
        @apply rounded-b;
    }
    
    .dropdown-item.active {
        @apply bg-primary-alpha-05 text-primary-dark font-medium;
    }

    .dropdown-divider {
        @apply border-t border-border-light my-1;
    }



    /* 头部右侧下拉菜单右对齐，防止溢出 */
    .header-actions-right .dropdown-menu {
        @apply left-auto right-0 translate-x-0;
    }


    
    /* 表单组件 */
    .form-group {
        @apply mb-4;
    }
    
    .form-group:last-child {
        @apply mb-0;
    }
    
    .form-label {
        @apply block text-sm font-medium text-text-secondary mb-2;
    }
    
    .form-label.required::after {
        @apply text-danger;
        content: " *";
    }
    
    .form-input {
        @apply w-full border border-border-normal rounded text-base bg-white transition-colors outline-none h-11 min-h-11 leading-normal box-border flex items-center px-3 py-0;
        -webkit-appearance: none;
        appearance: none;
    }
    
    .form-input:focus {
        @apply border-primary;
        box-shadow: 0 0 0 2px theme('colors.primary-alpha-20');
    }
    
    .form-input::placeholder {
        @apply text-text-light;
    }
    
    .form-input:disabled {
        @apply bg-bg-primary cursor-not-allowed opacity-60;
    }
    
    .form-input.error {
        @apply border-danger;
    }
    
    .form-input.error:focus {
        @apply border-danger;
        box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
    }
    
    /* 表单选择器 */
    .form-select {
        @apply w-full border border-border-normal rounded text-base bg-white transition-colors outline-none cursor-pointer h-11 min-h-11 leading-normal box-border flex items-center pl-3 pr-10 py-0;
        -webkit-appearance: none;
        appearance: none;
        background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%236b7280' d='M6 8.5L2.5 5h7L6 8.5z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 0.75rem center;
        background-size: 12px 12px;
    }
    
    .form-select:focus {
        @apply border-primary;
        box-shadow: 0 0 0 2px theme('colors.primary-alpha-20');
    }
    
    .form-select:disabled {
        @apply bg-bg-primary cursor-not-allowed opacity-60;
    }
    
    /* 表单提示和错误 */
    .form-hint {
        @apply text-xs text-text-secondary mt-1;
    }
    
    .form-error {
        @apply text-xs text-danger mt-1;
    }
    
    /* 搜索框组件 */
    .search-input {
        @apply w-full py-2.5 pr-10 pl-3 border border-border-normal rounded-md text-sm bg-white transition-all duration-200 focus:outline-none focus:border-primary focus:shadow-[0_0_0_3px_rgba(88,180,176,0.1)] placeholder:text-text-light;
    }
    
    .search-button {
        @apply absolute right-1 top-1/2 transform -translate-y-1/2 bg-transparent border-none p-2 rounded-md cursor-pointer flex items-center justify-center text-text-light transition-all duration-200 hover:bg-primary-alpha-10 hover:text-primary;
    }
    
    /* FriendList 页面特定样式 */
    
    /* 好友类型图标颜色覆盖 */
    .friend-type-0 .text-primary {
        color: theme('colors.primary'); /* 好友 - 青色 */
    }
    
    .friend-type-1 .text-primary {
        color: theme('colors.danger'); /* 黑名单 - 红色 */
    }
    
    .friend-type-2 .text-primary,
    .friend-type-4 .text-primary {
        color: theme('colors.warning'); /* 追求相关 - 橙色 */
    }
    
    .friend-type-5 .text-primary {
        color: theme('colors.violet.600'); /* 推荐 - 紫色 */
    }
    
    /* 下拉菜单智能定位 */
    .dropdown-menu-smart {
        /* 默认向下显示，缩短距离 */
        top: 100%;
        bottom: auto;
        margin-top: 0.125rem;
        margin-bottom: 0;
    }
    
    /* 当好友列表容器中的最后一个条目时，下拉菜单向上显示 */
    .friend-item:last-child .dropdown-menu-smart {
        top: auto;
        bottom: 100%;
        margin-top: 0;
        margin-bottom: 0.125rem;
    }
    
    /* Toast 淡出动画 */
    .fade-out {
        @apply opacity-0 -translate-y-2.5;
    }

    /* UserGuessBook 留言删除动画 */
    .message-delete-fade {
        @apply opacity-0 translate-x-5 transition-all duration-300 ease-out;
    }
    
    /* 自定义确认对话框按钮样式 */
    .custom-confirm-btn {
        @apply border-none rounded-md text-base font-medium cursor-pointer transition-all duration-200 h-11 flex items-center justify-center w-[45%];
    }
    
    .custom-confirm-cancel {
        @apply bg-bg-gray-100 text-text-secondary hover:bg-border-normal hover:text-text-primary;
    }
    
    .custom-confirm-delete {
        @apply bg-danger text-white hover:bg-danger-dark;
    }
    
    /* 表单行布局 */
    .form-row {
        @apply flex gap-3;
    }
    
    .form-row .form-group {
        @apply flex-1;
    }
    
    /* 展开更多功能 */
    .expand-toggle {
        @apply text-center mt-3 mb-2 pt-2 border-t border-border-light;
    }
    
    .expand-btn {
        @apply text-sm text-text-secondary bg-transparent border-none p-2 rounded-md transition-all cursor-pointer inline-flex items-center justify-center hover:bg-primary-alpha-05 hover:text-primary;
    }
    
    .expand-btn .icon {
        @apply transition-transform duration-200;
    }
    
    .expand-btn.expanded .icon {
        @apply rotate-180;
    }
    
    .more-fields {
        @apply mt-3 pt-2 overflow-hidden transition-all duration-200 ease-in-out;
    }
    
    .more-fields.expanding {
        animation: expandFields 0.2s ease-in-out;
    }
    
    .more-fields.collapsing {
        animation: collapseFields 0.2s ease-in-out;
    }
    
    /* 表单操作区域 */
    .form-actions {
        @apply mt-4 px-4 mb-2;
    }
    
    .form-submit {
        @apply w-full py-4 px-4 text-base font-medium max-w-[400px] mx-auto flex items-center justify-center gap-2 bg-gradient-to-br from-primary to-primary-dark text-white rounded shadow-md transition-all hover:-translate-y-0.5 hover:shadow-lg active:translate-y-0 active:shadow-sm;
    }
    
    .form-submit .icon {
        @apply w-5 h-5 flex-shrink-0;
    }
    
    /* 图标尺寸 */
    .icon {
        @apply w-4 h-4;
    }
    
    /* 个人资料编辑按钮 */
    .edit-profile {
        @apply bg-white bg-opacity-20 rounded-md py-2 px-3 cursor-pointer flex items-center transition-colors select-none relative z-50;
    }

    /* 编辑资料按钮响应式padding微调 */
    @media (max-width: 400px) {
        .edit-profile {
            @apply py-1.5 px-2.5;
        }
    }

    @media (max-width: 350px) {
        .edit-profile {
            @apply py-1 px-2;
        }
    }

    .edit-profile:hover {
        @apply bg-white bg-opacity-30;
    }
    
    /* 勋章图片样式 */
    .card-body img {
        @apply inline-block;
    }

    /* 动态链接样式 - 方案5：波浪下划线样式 */
    .dynamic-link {
        color: theme('colors.primary');
        text-decoration: none;
        position: relative;
        transition: color 0.3s ease;
        margin: 0 2px;
    }

    .dynamic-link::before {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 0;
        height: 1px;
        background: linear-gradient(135deg, theme('colors.primary'), theme('colors.secondary'));
        border-radius: 1px;
        transition: width 0.3s ease;
    }

    .dynamic-link:hover {
        color: #4a9d99;
    }

    .dynamic-link:hover::before {
        width: 100%;
    }

    @screen max-768 {
        .container {
            @apply max-w-full;
        }

        .header {
            @apply max-w-full;
        }

        .card {
            @apply mx-3 mb-3 mt-3;
        }

        .card-header {
            @apply p-3 pb-2;
        }

        .card-body {
            @apply p-3;
        }

        .form-actions {
            @apply px-3;
        }

        .form-submit {
            @apply max-w-none;
        }

        /* BuyGroup 页面网格容器对齐 */
        .grid.mx-4 {
            @apply mx-3;
        }
    }

    @screen xs-400 {
        .card {
            @apply mx-2 mb-3 mt-3;
        }

        .card-header {
            @apply p-2 pb-1;
        }

        .card-body {
            @apply p-2;
        }

        .form-actions {
            @apply px-2;
        }

        /* BuyGroup 页面网格容器对齐 */
        .grid.mx-4 {
            @apply mx-2;
        }

        /* 表格小屏幕优化 */
        .table-responsive th,
        .table-responsive td {
            @apply py-2 px-1 text-center align-middle;
        }

        /* 项目列文本不换行 */
        .table-responsive td:first-child div {
            white-space: nowrap;
            font-size: 0.8rem;
        }

        /* 金额和余额列数字不换行 */
        .table-responsive td:nth-child(2) span,
        .table-responsive td:nth-child(3) span {
            white-space: nowrap;
            font-size: 0.8rem;
        }

        /* 时间列保持当前换行方式 */
        .table-responsive td:nth-child(5) span {
            white-space: nowrap;
            font-size: 0.8rem;
        }
    }
    

    
    /* 自定义绿色圆点列表标记 - 在 FriendList.hbs 中使用 */
    .custom-bullet {
        @apply relative pl-4;
    }
    
    .custom-bullet::before {
        content: '●';
        color: theme('colors.primary');
        margin-right: 0.5rem;
    }

    /* 移动端Safari特殊处理 */
    @media screen and (-webkit-min-device-pixel-ratio: 0) {
        .form-input,
        .form-select {
            -webkit-appearance: none;
            border-radius: 0.375rem;
        }
    }

    /* 昵称显示样式 - 从 tailwind.config.js 迁移过来 */
    .nickname-display {
        @apply flex items-center flex-nowrap whitespace-nowrap;
    }

    .nickname-display img {
        @apply inline-block align-middle mr-1 xs-350:mr-0.5 xs-310:mr-px;
    }

    .nickname-display * {
        @apply inline align-middle;
    }

    /* 留言板图片内联显示 - 在 UBB 代码处理和 BookReMy.hbs 中使用 */
    .ubbimg {
        display: inline;
        vertical-align: baseline;
        max-width: 100%;
        height: auto;
    }

    /* BookReMy 回复内容样式 - 修复图片和文本对齐问题，兼容脚本处理 */
    .reply-content, .retext {
        @apply leading-relaxed;
    }

    .reply-content img, .retext img {
        @apply max-w-full h-auto;
        vertical-align: bottom;
    }

    .reply-content .ubbimg, .retext .ubbimg {
        @apply max-w-full h-auto inline-block;
        vertical-align: bottom;
    }

    /* 确保内联元素底对齐 */
    .reply-content *, .retext * {
        vertical-align: bottom;
    }

    /* 特殊处理换行标签 */
    .reply-content br, .retext br {
        line-height: 1.6;
    }

    /* 兼容HyperLink.js生成的链接样式 */
    .reply-content a, .retext a {
        @apply text-primary no-underline hover:underline;
    }



    /* Medal 勋章页面样式 */
    /* 功能切换标签样式 */
    .function-tab {
        @apply flex-1 px-4 py-3 rounded-lg bg-gray-100 text-gray-500 text-base font-medium cursor-pointer transition-all text-center border border-transparent;
    }

    .function-tab:hover {
        @apply bg-primary-alpha-10 text-primary;
    }

    .function-tab.active {
        @apply bg-primary text-white border-primary;
    }

    /* 筛选标签样式 */
    .filter-tabs {
        @apply flex gap-2 p-4 overflow-x-auto;
    }

    .filter-tab {
        @apply px-4 py-2 rounded-full bg-gray-100 text-gray-500 text-sm font-medium cursor-pointer transition-all whitespace-nowrap border border-transparent;
    }

    .filter-tab:hover {
        @apply bg-primary-alpha-10 text-primary;
    }

    .filter-tab.active {
        @apply bg-primary text-white border-primary;
    }

    /* 勋章列表样式 */
    .medal-item {
        @apply p-4 border-b border-gray-100 transition-all flex items-start;
    }

    .medal-item:last-child {
        @apply border-b-0;
    }

    .medal-item:nth-child(even) {
        background-color: #fafbfc;
    }

    .medal-item:hover {
        background-color: rgba(88, 180, 176, 0.05);
    }

    /* 勋章图标样式 */
    .medal-icon {
        @apply w-12 h-12 mr-3 flex-shrink-0 flex items-center justify-center overflow-hidden;
    }

    .medal-icon img {
        @apply max-w-12 max-h-12 w-auto h-auto object-contain;
    }

    /* 勋章信息样式 */
    .medal-info {
        @apply flex-1;
    }

    .medal-header {
        @apply mb-1;
    }

    .medal-title {
        @apply text-base font-semibold text-text-primary;
    }

    .medal-description {
        @apply text-sm text-text-secondary leading-relaxed mb-2;
    }

    .medal-price {
        @apply text-sm font-semibold text-warning bg-warning-alpha-20 px-2 py-1 rounded whitespace-nowrap inline-block;
    }

    /* 购买按钮样式 */
    .medal-actions {
        @apply ml-3 flex-shrink-0 flex items-center self-center;
    }

    .buy-button {
        @apply bg-success text-white border-none px-4 py-2 rounded-lg text-sm font-medium cursor-pointer transition-all min-w-20 w-20 text-center no-underline inline-block;
    }

    .buy-button:hover {
        background-color: #059669;
        @apply transform -translate-y-0.5 shadow-md;
    }

    .buy-button:active {
        @apply transform translate-y-0;
    }

    .buy-button.disabled {
        @apply bg-gray-300 text-gray-500 cursor-not-allowed transform-none shadow-none;
    }

    .buy-button.disabled:hover {
        @apply bg-gray-300 transform-none shadow-none;
    }

    /* 已拥有标签样式 */
    .owned-badge {
        @apply inline-block bg-success text-white text-xs font-medium px-2 py-1 rounded-md ml-2 align-middle;
    }

    /* 勋章链接图标样式 */
    .medal-link {
        @apply inline-flex items-center ml-1 text-primary no-underline opacity-70 transition-all align-middle;
    }

    .medal-link:hover {
        @apply opacity-100 text-primary-dark transform -translate-y-0.5;
    }

    /* Medal 弹窗样式 - 复用现有的确认对话框样式但添加特定样式 */
    .modal-overlay {
        @apply fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-modal p-4;
    }

    .modal-content {
        @apply bg-white rounded-xl shadow-dialog max-w-lg w-full max-h-[90vh] overflow-y-auto;
        animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .modal-header {
        @apply flex items-center justify-between p-4 border-b border-border-light;
    }

    .modal-title {
        @apply m-0 text-lg font-semibold text-text-primary flex items-center gap-2;
    }

    .modal-close {
        @apply bg-transparent border-none text-text-secondary cursor-pointer p-1 rounded transition-all;
    }

    .modal-close:hover {
        @apply bg-bg-primary text-text-primary;
    }

    .modal-body {
        @apply p-4;
    }

    .modal-body.pb-0 {
        padding-bottom: 0;
    }

    .modal-footer {
        @apply flex gap-3 justify-end p-4 border-t border-border-light;
    }

    /* 申请流程说明样式 */
    .help-modal {
        @apply max-w-2xl w-[90%];
    }

    .help-content {
        @apply py-4;
    }

    .help-step {
        @apply flex items-start mb-6 pb-4 border-b border-border-light;
    }

    .help-step:last-child {
        @apply mb-0 border-b-0;
    }

    .step-number {
        @apply w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-bold text-sm mr-4 flex-shrink-0;
    }

    .step-content h4 {
        @apply m-0 mb-2 text-base font-semibold text-text-primary;
    }

    .step-content p {
        @apply m-0 mb-1 text-text-secondary leading-relaxed;
    }

    .help-note {
        @apply text-sm text-text-light italic;
    }



    @media (max-width: 480px) {
        .filter-tabs {
            @apply px-2 py-4;
        }

        .medal-item {
            @apply flex-row;
        }

        .medal-icon {
            @apply mr-2 mb-0;
        }

        .medal-actions {
            @apply ml-2 mt-0 w-auto;
        }

        .buy-button {
            @apply w-20 min-w-20;
        }
    }

    /* UserGuessBook 留言内容图片样式 - 只影响留言内容，不影响头像 */
    #messageList .text-sm.text-gray-700 img {
        @apply max-w-full h-auto rounded-lg my-2;
    }

    /* 确保留言容器正确包含内容 */
    #messageList .relative {
        @apply overflow-hidden;
    }
    
    /* 表格响应式组件 */
    .table-responsive {
        @apply overflow-x-auto;
    }

    /* ========== 消息通知组件样式 ========== */
    
    /* 标准图标尺寸类 - 用于返回图标和功能图标 */
    .header-icon-standard {
        @apply w-6 h-6 xs-400:w-[22px] xs-400:h-[22px] xs-350:w-[18px] xs-350:h-[18px];
    }
    
    /* 小号图标尺寸类 - 专用于笔刷图标 */
    .header-icon-size {
        @apply w-5 h-5 xs-400:w-[19px] xs-400:h-[19px] xs-350:w-4 xs-350:h-4;
    }
    
    /* 其他图标（非消息通知）的通用类 */
    .header-other-icon {
        /* 默认显示，由JavaScript控制隐藏 */
    }
    
    /* 消息通知图标容器 */
    .notification-icon {
        @apply relative transition-all duration-300 ease-in-out;
    }
    
    /* 消息通知徽章 */
    .notification-badge {
        @apply absolute -top-1.5 -right-1.5 flex items-center justify-center;
        @apply min-w-4 h-4 px-1 text-xs font-semibold text-white;
        @apply bg-gradient-notification-badge rounded-full border-2 border-white;
        @apply select-none;
        box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
    }
    
    /* 消息通知下拉面板 */
    .notification-dropdown {
        @apply absolute right-0 top-full mt-2 w-80 max-w-[90vw];
        @apply bg-white rounded-xl shadow-xl z-[90] overflow-hidden;
        @apply opacity-0 invisible -translate-y-2.5 scale-95 pointer-events-none;
        @apply transition-all duration-200 ease-out;
    }
    
    /* 消息通知下拉面板显示状态 */
    .notification-dropdown.show {
        @apply opacity-100 visible translate-y-0 scale-100 pointer-events-auto;
    }
    
    /* 消息通知列表项 */
    .notification-item {
        @apply p-3.5 border-b border-gray-100 cursor-pointer transition-all duration-200 relative;
    }
    
    .notification-item:hover {
        @apply bg-gray-50;
    }
    
    .notification-item:last-child {
        @apply border-b-0;
    }
    
    /* 未读消息项样式 */
    .notification-item.unread {
        @apply bg-primary-alpha-05;
    }
    
    /* 未读消息小绿点 - 放在圆形图标右下角 */
    .notification-item.unread .icon-container::after {
        content: '';
        @apply absolute -right-0.5 -bottom-0.5 w-2.5 h-2.5;
        @apply bg-primary rounded-full border border-white;
        @apply animate-breathe;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }
    
    /* 圆形图标容器相对定位，用于小绿点定位 */
    .notification-item .icon-container {
        @apply relative;
    }
    
    /* 消息通知响应式适配 */
    @media (max-width: 480px) {
        .notification-dropdown {
            width: 280px;
            @apply -right-2;
        }
        
        .notification-item {
            @apply p-3;
        }
    }

}

/* 移动端优化 - 400px以下 */
@media (max-width: 400px) {
    /* tooltip优化 */
    .userinfo-tooltip {
        @apply text-xs px-2 py-0.5;
        min-height: 20px;
        font-size: 0.7rem;
    }

    /* 用户资料头部布局优化 - 减少space-x-4的间距 */
    .flex.items-start.space-x-4.mb-6 > * + * {
        margin-left: 0.5rem; /* 从16px减少到8px，为昵称预留更多空间 */
    }

    /* 头像尺寸优化 - 已转换为Tailwind响应式类 */

    /* 私信按钮优化 - 已转换为Tailwind响应式类 */

    /* 昵称字体优化 - 已转换为Tailwind响应式类 */

    /* 用户信息行间距优化 - 减少space-x-4的间距 */
    .flex.items-center.space-x-4.text-sm > * + * {
        margin-left: 0.75rem; /* 从16px减少到12px */
    }
}

/* 移动端优化 - 350px以下 */
@media (max-width: 350px) {
    /* 头像尺寸优化 - 已转换为Tailwind响应式类 */

    /* 私信按钮优化 - 已转换为Tailwind响应式类 */

    /* 昵称字体优化 - 已转换为Tailwind响应式类 */

    /* 用户信息进一步紧凑 - 保留复杂选择器 */
    .flex.items-center.space-x-4.text-sm > * + * {
        margin-left: 0.5rem; /* 从16px减少到8px */
    }

    /* 统计卡片数字显示优化 */
    .stats-grid {
        padding-top: 0.625rem;    /* 从0.75rem减少 */
        padding-bottom: 0.625rem;
        gap: 0.5rem;              /* 减少列间距 */
    }

    /* 大数字字号优化 */
    .stats-grid .text-3xl {
        font-size: 1.625rem;      /* 从1.875rem减小 */
        line-height: 1.2;         /* 调整行高 */
    }

    /* 标签文字也略微调整 */
    .stats-grid .text-sm {
        font-size: 0.8125rem;     /* 从0.875rem减小 */
    }
}

/* 个人中心弹窗精细化响应式调整 - 在 @layer 外部防止被 Tailwind purge 移除 */

/* 390px 以下屏幕优化 - 对应旧版第一级优化 */
@screen xs-390 {
    /* 论坛互动网格容器 */
    #yaojing-rule-modal .grid.grid-cols-3 {
        gap: 6px;
    }

    /* 论坛互动卡片项（派币帖、悬赏帖、小游戏） */
    #yaojing-rule-modal .grid.grid-cols-3 > div {
        padding: 6px 8px;
        font-size: 13px;
    }

    /* 图标间距调整 */
    #yaojing-rule-modal .grid.grid-cols-3 > div i {
        margin-right: 4px;
    }
}

/* 350px 以下屏幕进一步优化 - 对应旧版第二级优化 (已从 345px 调整) */
@screen xs-350 {
    /* 模态框内容内边距优化 */
    #yaojing-rule-modal > div,
    #level-rule-modal > div,
    #time-rule-modal > div {
        padding: 16px 16px 0 16px;
    }

    /* 论坛互动网格容器 */
    #yaojing-rule-modal .grid.grid-cols-3 {
        gap: 4px;
    }

    /* 论坛互动卡片项 */
    #yaojing-rule-modal .grid.grid-cols-3 > div {
        padding: 4px 6px;
        font-size: 12px;
    }

    /* 图标间距调整 */
    #yaojing-rule-modal .grid.grid-cols-3 > div i {
        margin-right: 2px;
    }
}

/* 310px 以下屏幕（极小屏幕）优化 - 对应旧版第三级优化 */
@screen xs-310 {
    /* 所有弹窗的内容区域内边距优化 */
    #yaojing-rule-modal > div,
    #level-rule-modal > div,
    #time-rule-modal > div {
        padding: 12px 12px 0 12px;
    }

    /* 论坛互动网格容器 */
    #yaojing-rule-modal .grid.grid-cols-3 {
        gap: 2px;
    }

    /* 论坛互动卡片项 */
    #yaojing-rule-modal .grid.grid-cols-3 > div {
        padding: 2px 4px;
        font-size: 11px;
    }

    /* 图标间距调整 */
    #yaojing-rule-modal .grid.grid-cols-3 > div i {
        margin-right: 2px;
    }

    /* 锚点消息高亮效果 */
    .highlight-anchor {
        @apply transition-all duration-500 ease-in-out;
        background-color: rgba(253, 224, 71, 0.4); /* Tailwind yellow-300 with opacity */
    }
}

