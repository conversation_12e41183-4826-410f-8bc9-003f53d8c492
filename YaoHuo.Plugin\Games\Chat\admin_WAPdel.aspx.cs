﻿using System;
using KeLin.ClassManager;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.Games.Chat
{
    public class Admin_WAPdel : MyPageWap
    {
        private readonly string string_0 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string page = "";

        public string INFO = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            page = GetRequestValue("page");
            IsCheckManagerLvl("|00|01|03|", classVo.adminusername, GetUrlQueryString());
            if (action == "godel")
            {
                try
                {
                    // ✅ 使用DapperHelper进行安全的聊天记录删除操作
                    string connectionString = PubConstant.GetConnectionString(string_0);
                    string deleteChatSql = "DELETE FROM [wap2_games_chat] WHERE id = @Id AND siteid = @SiteId";
                    DapperHelper.Execute(connectionString, deleteChatSql, new {
                        Id = DapperHelper.SafeParseLong(id, "聊天记录ID"),
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID")
                    });
                    INFO = "OK";
                }
                catch (Exception ex)
                {
                    INFO = ex.ToString();
                }
            }
        }
    }
}