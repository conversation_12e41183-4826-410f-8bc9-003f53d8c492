

// KenLin_ClassManager, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
// KeLin.ClassManager.Model.wap_bbs_Model
using System;

[Serializable]
public class wap_bbs_Model
{
	private long long_0;

	private long long_1;

	private long long_2;

	private string string_0;

	private string string_1;

	private string string_2;

	private string string_3;

	private long long_3;

	private long long_4;

	private DateTime dateTime_0;

	private long long_5;

	private long long_6;

	private long long_7;

	private DateTime dateTime_1;

	private long long_8;

	private long long_9;

	private long long_10;

	private long long_11;

	private long long_12;

	private long long_13;

	private long long_14;

	private string string_4;

	private long long_15;

	private long long_16;

	private long long_17;

	private long long_18;

	private long long_19;

	private string string_5;

	private long long_20;

	private long long_21;

	private long long_22;

	private long long_23;

	private string string_6;

	private long long_24;

	private string string_7;

	private long long_25;

	private long long_26;

	public long MarkSixWin
	{
		get
		{
			return long_26;
		}
		set
		{
			long_26 = value;
		}
	}

	public long MarkSixBetID
	{
		get
		{
			return long_25;
		}
		set
		{
			long_25 = value;
		}
	}

	public string book_img
	{
		get
		{
			return string_7;
		}
		set
		{
			string_7 = value;
		}
	}

	public long myGetMoney
	{
		get
		{
			return long_24;
		}
		set
		{
			long_24 = value;
		}
	}

	public string freeRule
	{
		get
		{
			return string_6;
		}
		set
		{
			string_6 = value;
		}
	}

	public long freeLeftMoney
	{
		get
		{
			return long_23;
		}
		set
		{
			long_23 = value;
		}
	}

	public long freeMoney
	{
		get
		{
			return long_22;
		}
		set
		{
			long_22 = value;
		}
	}

	public long viewtype
	{
		get
		{
			return long_20;
		}
		set
		{
			long_20 = value;
		}
	}

	public long viewmoney
	{
		get
		{
			return long_21;
		}
		set
		{
			long_21 = value;
		}
	}

	public string classname
	{
		get
		{
			return string_5;
		}
		set
		{
			string_5 = value;
		}
	}

	public long Int64_0
	{
		get
		{
			return long_0;
		}
		set
		{
			long_0 = value;
		}
	}

	public long userid
	{
		get
		{
			return long_1;
		}
		set
		{
			long_1 = value;
		}
	}

	public long book_classid
	{
		get
		{
			return long_2;
		}
		set
		{
			long_2 = value;
		}
	}

	public string book_title
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	public string book_author
	{
		get
		{
			return string_1.Replace("&", "＆").Replace("/", "｜").Replace("[", "［")
				.Replace("]", "］")
				.Replace("\u3000", "空格");
		}
		set
		{
			string_1 = value;
		}
	}

	public string book_pub
	{
		get
		{
			return string_2;
		}
		set
		{
			string_2 = value;
		}
	}

	public string book_content
	{
		get
		{
			return string_3;
		}
		set
		{
			string_3 = value;
		}
	}

	public long book_re
	{
		get
		{
			return long_3;
		}
		set
		{
			long_3 = value;
		}
	}

	public long book_click
	{
		get
		{
			return long_4;
		}
		set
		{
			long_4 = value;
		}
	}

	public DateTime book_date
	{
		get
		{
			return dateTime_0;
		}
		set
		{
			dateTime_0 = value;
		}
	}

	public long book_good
	{
		get
		{
			return long_5;
		}
		set
		{
			long_5 = value;
		}
	}

	public long book_top
	{
		get
		{
			return long_6;
		}
		set
		{
			long_6 = value;
		}
	}

	public long sysid
	{
		get
		{
			return long_7;
		}
		set
		{
			long_7 = value;
		}
	}

	public DateTime reDate
	{
		get
		{
			return dateTime_1;
		}
		set
		{
			dateTime_1 = value;
		}
	}

	public long reShow
	{
		get
		{
			return long_8;
		}
		set
		{
			long_8 = value;
		}
	}

	public long suport
	{
		get
		{
			return long_9;
		}
		set
		{
			long_9 = value;
		}
	}

	public long oppose
	{
		get
		{
			return long_10;
		}
		set
		{
			long_10 = value;
		}
	}

	public long topic
	{
		get
		{
			return long_11;
		}
		set
		{
			long_11 = value;
		}
	}

	public long islock
	{
		get
		{
			return long_12;
		}
		set
		{
			long_12 = value;
		}
	}

	public long ischeck
	{
		get
		{
			return long_13;
		}
		set
		{
			long_13 = value;
		}
	}

	public long isVote
	{
		get
		{
			return long_14;
		}
		set
		{
			long_14 = value;
		}
	}

	public string whylock
	{
		get
		{
			return string_4;
		}
		set
		{
			string_4 = value;
		}
	}

	public long sendMoney
	{
		get
		{
			return long_15;
		}
		set
		{
			long_15 = value;
		}
	}

	public long hasMoney
	{
		get
		{
			return long_16;
		}
		set
		{
			long_16 = value;
		}
	}

	public long isdown
	{
		get
		{
			return long_17;
		}
		set
		{
			long_17 = value;
		}
	}

	public long HangBiaoShi
	{
		get
		{
			return long_18;
		}
		set
		{
			long_18 = value;
		}
	}

	public long smalltype
	{
		get
		{
			return long_19;
		}
		set
		{
			long_19 = value;
		}
	}
}
