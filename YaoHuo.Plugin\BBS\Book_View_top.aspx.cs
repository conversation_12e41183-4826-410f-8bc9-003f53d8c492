﻿using System;
using System.Data;
using Dapper;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Book_View_top : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string lpage = "";

        public string INFO = "";

        public string ERROR = "";

        public string tops = "";

        public string topType = ""; // 新增：用于区分普通置顶和全局置顶

        public wap_bbs_Model bookVo = null;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
            }
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            lpage = GetRequestValue("lpage");
            tops = GetRequestValue("tops");
            topType = GetRequestValue("topType"); // 获取置顶类型

            // 根据置顶类型检查权限
            if (topType == "all")
            {
                CheckManagerLvl("04", "", "bbs/book_view_admin.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;lpage=" + lpage + "&amp;id=" + id);
            }
            else
            {
                CheckManagerLvl("04", classVo.adminusername, "bbs/book_view_admin.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;lpage=" + lpage + "&amp;id=" + id);
            }

            wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(a);
            bookVo = wap_bbs_BLL.GetModel(long.Parse(id));
            if (bookVo == null)
            {
                ShowTipInfo("已删除！或不存在！", "");
            }
            else if (bookVo.ischeck == 1L)
            {
                ShowTipInfo("正在审核中！", "");
            }
            else if (bookVo.book_classid.ToString() != classid)
            {
                ShowTipInfo("栏目ID对不上！可能没有传classid值！", "");
            }
            else if (bookVo.islock == 1L)
            {
                ShowTipInfo("此帖已锁！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bookVo.book_classid + "&amp;id=" + bookVo.id + "&amp;lpage=" + lpage);
            }
            else if (bookVo.islock == 2L)
            {
                ShowTipInfo("此帖已结！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bookVo.book_classid + "&amp;id=" + bookVo.id + "&amp;lpage=" + lpage);
            }
            if (!(action == "gomod"))
            {
                return;
            }

            // 根据置顶类型清理缓存
            if (topType == "all" || bookVo.book_top == 2)
            {
                WapTool.ClearDataBBS("bbs" + siteid);
                WapTool.ClearDataBBS("bbsTop" + siteid);
                // ✅ 清理独立置顶帖缓存（全区置顶影响所有版块）
                YaoHuo.Plugin.BBS.Book_List.ClearTopPostsCache(siteid);
            }
            else
            {
                WapTool.ClearDataBBS("bbs" + siteid + classid);
                WapTool.ClearDataTemp("bbsTop" + siteid + classid);
                WapTool.ClearDataBBS("bbsTop" + siteid + classid);
                // ✅ 清理独立置顶帖缓存（普通置顶只影响当前版块）
                YaoHuo.Plugin.BBS.Book_List.ClearTopPostsCache(siteid, classid);
            }

            try
            {
                if (tops == "1")
                {
                    long targetTopValue = (topType == "all") ? 2L : 1L;
                    if (bookVo.book_top == targetTopValue)
                    {
                        INFO = "ERR";
                        return;
                    }
                    string fcountSubMoneyFlag = WapTool.GetFcountSubMoneyFlag(siteid, userid, IP);
                    if (fcountSubMoneyFlag.IndexOf("BBSTOP" + id) < 0)
                    {
                        // ✅ 使用TransactionHelper进行安全的事务性置顶操作
                        string connectionString = PubConstant.GetConnectionString(a);
                        string topTypeText = (topType == "all") ? "全区置顶" : "置顶";
                        string lockReason = "{" + userVo.nickname + "(ID" + userVo.userid + ")" + topTypeText + "此帖" + $"{DateTime.Now:MM-dd HH:mm}" + "}<br/>";
                        lockReason += bookVo.whylock;

                        long moneyRegular = WapTool.GetMoneyRegular(siteVo.moneyregular, 3);
                        long lvLRegular = WapTool.GetLvLRegular(siteVo.lvlRegular, 3);
                        long siteIdLong = DapperHelper.SafeParseLong(siteid, "站点ID");
                        long userIdLong = DapperHelper.SafeParseLong(userid, "用户ID");
                        long postIdLong = DapperHelper.SafeParseLong(id, "帖子ID");

                        TransactionHelper.ExecuteMoneyTransaction(connectionString, (connection, transaction) =>
                        {
                            // 1. 更新帖子置顶状态
                            string updatePostSql = "UPDATE wap_bbs SET book_top = @TopValue, whylock = @WhyLock WHERE userid = @SiteId AND id = @PostId";
                            connection.Execute(updatePostSql, new {
                                TopValue = targetTopValue,
                                WhyLock = DapperHelper.LimitLength(lockReason, 1000),
                                SiteId = siteIdLong,
                                PostId = postIdLong
                            }, transaction);

                            // 2. 奖励作者金币和经验（如果有奖励）
                            if (moneyRegular > 0L || lvLRegular > 0L)
                            {
                                string updateUserSql = "UPDATE [user] SET money = money + @Money, expR = expR + @Exp WHERE userid = @UserId";
                                connection.Execute(updateUserSql, new {
                                    Money = moneyRegular,
                                    Exp = lvLRegular,
                                    UserId = bookVo.book_pub
                                }, transaction);
                            }

                            // 3. 更新操作标记
                            string updateFcountSql = "UPDATE [fcount] SET SubMoneyFlag = @SubMoneyFlag WHERE fip = @IP AND fuserid = @SiteId AND userid = @UserId";
                            connection.Execute(updateFcountSql, new {
                                SubMoneyFlag = fcountSubMoneyFlag + "BBSTOP" + id,
                                IP = DapperHelper.LimitLength(IP, 50),
                                SiteId = siteIdLong,
                                UserId = userIdLong
                            }, transaction);
                        });

                        // 4. 记录银行日志（在事务外执行）
                        if (moneyRegular > 0L)
                        {
                            // ✅ 先获取帖子作者的当前余额，避免SaveBankLog中的SELECT操作导致死锁
                            string getAuthorMoneySql = "SELECT money FROM [user] WHERE userid = @UserId AND siteid = @SiteId";
                            long authorCurrentMoney = DapperHelper.ExecuteScalar<long>(connectionString, getAuthorMoneySql, new {
                                UserId = bookVo.book_pub,
                                SiteId = siteIdLong
                            });
                            long authorNewBalance = authorCurrentMoney + moneyRegular;

                            // ✅ 使用SaveBankLogWithBalance替换SaveBankLog，避免死锁
                            SaveBankLogWithBalance(bookVo.book_pub.ToString(), "帖子" + topTypeText, moneyRegular.ToString(), userid, nickname, "帖子[ID:" + bookVo.id + "]", authorNewBalance);
                        }

                        // 5. 发送系统消息（如果有奖励）
                        if (moneyRegular > 0L || lvLRegular > 0L)
                        {
                            string book_title = bookVo.book_title.Replace("[", "［").Replace("]", "］");
                            string messageTitle = $"您的一个主题设为{topTypeText}，奖励:{moneyRegular}个{siteVo.sitemoneyname}，奖励经验：{lvLRegular}";
                            string messageContent = $"设置时间：{DateTime.Now}[br]论坛主题:[url=/bbs/book_view.aspx?siteid={siteid}&amp;classid={bookVo.book_classid}&amp;id={id}]{book_title}[/url]";

                            string insertMessageSql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                                                       VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, 1)";
                            DapperHelper.Execute(connectionString, insertMessageSql, new {
                                SiteId = siteIdLong,
                                UserId = userIdLong,
                                Nickname = DapperHelper.LimitLength(nickname, 50),
                                Title = DapperHelper.LimitLength(messageTitle, 100),
                                Content = DapperHelper.LimitLength(messageContent, 500),
                                ToUserId = bookVo.book_pub
                            });
                        }

                        // 6. 记录操作日志
                        string book_title_log = bookVo.book_title.Replace("[", "［").Replace("]", "］");
                        string logInfo = $"用户ID:{userid}{topTypeText}用户ID:{bookVo.book_pub}发表的ID={id}主题:{book_title_log}";
                        string insertLogSql = @"INSERT INTO wap_log(siteid,oper_userid,oper_nickname,oper_type,log_info,oper_ip)
                                               VALUES (@SiteId, @UserId, @Nickname, 0, @LogInfo, @IP)";
                        DapperHelper.Execute(connectionString, insertLogSql, new {
                            SiteId = siteIdLong,
                            UserId = userIdLong,
                            Nickname = DapperHelper.LimitLength(nickname, 50),
                            LogInfo = DapperHelper.LimitLength(logInfo, 500),
                            IP = DapperHelper.LimitLength(IP, 50)
                        });

                        // ✅ 置顶操作成功后清理缓存
                        if (topType == "all" || targetTopValue == 2L)
                        {
                            // 全区置顶影响所有版块
                            YaoHuo.Plugin.BBS.Book_List.ClearTopPostsCache(siteid);
                        }
                        else
                        {
                            // 普通置顶只影响当前版块
                            YaoHuo.Plugin.BBS.Book_List.ClearTopPostsCache(siteid, classid);
                        }

                        INFO = "OK";
                    }
                    else
                    {
                        ShowTipInfo("今天您已操作过一次，请明天再来！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bookVo.book_classid + "&amp;id=" + bookVo.id + "&amp;lpage=" + lpage);
                    }
                }
                else if (bookVo.book_top == 0L)
                {
                    INFO = "ERR";
                }
                else
                {
                    // ✅ 使用DapperHelper进行安全的取消置顶操作
                    string connectionString = PubConstant.GetConnectionString(a);
                    string topTypeText = (bookVo.book_top == 2L) ? "全区置顶" : "置顶";
                    string lockReason = "{" + userVo.nickname + "(ID" + userVo.userid + ")取消" + topTypeText + "此帖" + $"{DateTime.Now:MM-dd HH:mm}" + "}<br/>";
                    lockReason += bookVo.whylock;

                    long siteIdLong = DapperHelper.SafeParseLong(siteid, "站点ID");
                    long userIdLong = DapperHelper.SafeParseLong(userid, "用户ID");
                    long postIdLong = DapperHelper.SafeParseLong(id, "帖子ID");

                    // 1. 更新帖子取消置顶状态
                    string updatePostSql = "UPDATE wap_bbs SET book_top = 0, whylock = @WhyLock WHERE userid = @SiteId AND id = @PostId";
                    DapperHelper.Execute(connectionString, updatePostSql, new {
                        WhyLock = DapperHelper.LimitLength(lockReason, 1000),
                        SiteId = siteIdLong,
                        PostId = postIdLong
                    });

                    // 2. 发送系统消息
                    string book_title = bookVo.book_title.Replace("[", "［").Replace("]", "］");
                    string messageTitle = $"您的一个主题取消{topTypeText}!";
                    string messageContent = $"设置时间：{DateTime.Now}[br]论坛主题:[url=/bbs/book_view.aspx?siteid={siteid}&amp;classid={bookVo.book_classid}&amp;id={id}]{book_title}[/url]";

                    string insertMessageSql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                                               VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, 1)";
                    DapperHelper.Execute(connectionString, insertMessageSql, new {
                        SiteId = siteIdLong,
                        UserId = userIdLong,
                        Nickname = DapperHelper.LimitLength(nickname, 50),
                        Title = DapperHelper.LimitLength(messageTitle, 100),
                        Content = DapperHelper.LimitLength(messageContent, 500),
                        ToUserId = bookVo.book_pub
                    });

                    // 3. 记录操作日志
                    string logInfo = $"用户ID:{userid}取消{topTypeText}用户ID:{bookVo.book_pub}发表的ID={id}主题:{book_title}";
                    string insertLogSql = @"INSERT INTO wap_log(siteid,oper_userid,oper_nickname,oper_type,log_info,oper_ip)
                                           VALUES (@SiteId, @UserId, @Nickname, 0, @LogInfo, @IP)";
                    DapperHelper.Execute(connectionString, insertLogSql, new {
                        SiteId = siteIdLong,
                        UserId = userIdLong,
                        Nickname = DapperHelper.LimitLength(nickname, 50),
                        LogInfo = DapperHelper.LimitLength(logInfo, 500),
                        IP = DapperHelper.LimitLength(IP, 50)
                    });

                    // ✅ 取消置顶操作成功后清理缓存
                    if (bookVo.book_top == 2L)
                    {
                        // 取消全区置顶影响所有版块
                        YaoHuo.Plugin.BBS.Book_List.ClearTopPostsCache(siteid);
                    }
                    else
                    {
                        // 取消普通置顶只影响当前版块
                        YaoHuo.Plugin.BBS.Book_List.ClearTopPostsCache(siteid, classid);
                    }

                    INFO = "OK";
                    if (topType == "all")
                    {
                        base.Application.Clear();
                    }
                }
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}