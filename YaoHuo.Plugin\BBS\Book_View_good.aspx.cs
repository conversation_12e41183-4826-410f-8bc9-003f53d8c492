﻿using System;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Book_View_good : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string lpage = "";

        public string INFO = "";

        public string ERROR = "";

        public string tops = "";

        public wap_bbs_Model bookVo = null;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
            }
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            lpage = GetRequestValue("lpage");
            tops = GetRequestValue("tops");
            CheckManagerLvl("04", classVo.adminusername, "bbs/book_view_admin.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;lpage=" + lpage + "&amp;id=" + id);
            wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(a);
            bookVo = wap_bbs_BLL.GetModel(long.Parse(id));
            if (bookVo == null)
            {
                ShowTipInfo("已删除！或不存在！", "");
            }
            else if (bookVo.ischeck == 1L)
            {
                ShowTipInfo("正在审核中！", "");
            }
            else if (bookVo.book_classid.ToString() != classid)
            {
                ShowTipInfo("栏目ID对不上！可能没有传classid值！", "");
            }
            else if (bookVo.islock == 1L)
            {
                ShowTipInfo("此帖已锁！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bookVo.book_classid + "&amp;id=" + bookVo.id + "&amp;lpage=" + lpage);
            }
            else if (bookVo.islock == 2L)
            {
                ShowTipInfo("此帖已结！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bookVo.book_classid + "&amp;id=" + bookVo.id + "&amp;lpage=" + lpage);
            }
            if (!(action == "gomod"))
            {
                return;
            }
            WapTool.ClearDataBBS("bbs" + siteid + classid);
            WapTool.ClearDataBBS("bbsTop" + siteid + classid);
            try
            {
                if (tops == "1")
                {
                    if (bookVo.book_good == 1L)
                    {
                        INFO = "ERR";
                        return;
                    }
                    string fcountSubMoneyFlag = WapTool.GetFcountSubMoneyFlag(siteid, userid, IP);
                    if (fcountSubMoneyFlag.IndexOf("BBSGOOD" + id) < 0)
                    {
                        // ✅ 使用DapperHelper进行安全的参数化更新操作
                        string connectionString = PubConstant.GetConnectionString(a);
                        string lockReason = "{" + userVo.nickname + "(ID" + userVo.userid + ")加精此帖" + $"{DateTime.Now:MM-dd HH:mm}" + "}<br/>";
                        lockReason += bookVo.whylock;

                        string updatePostSql = "UPDATE wap_bbs SET book_good = 1, whylock = @WhyLock WHERE userid = @SiteId AND id = @PostId";
                        DapperHelper.Execute(connectionString, updatePostSql, new {
                            WhyLock = DapperHelper.LimitLength(lockReason, 1000),
                            SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                            PostId = DapperHelper.SafeParseLong(id, "帖子ID")
                        });

                        long moneyRegular = WapTool.GetMoneyRegular(siteVo.moneyregular, 2);
                        long lvLRegular = WapTool.GetLvLRegular(siteVo.lvlRegular, 2);
                        string book_title = bookVo.book_title.Replace("[", "［").Replace("]", "］");

                        if (moneyRegular > 0L || lvLRegular > 0L)
                        {
                            // ✅ 使用DapperHelper更新用户金币和经验
                            string updateUserSql = "UPDATE [user] SET money = money + @Money, expR = expR + @Exp WHERE userid = @UserId";
                            DapperHelper.Execute(connectionString, updateUserSql, new {
                                Money = moneyRegular,
                                Exp = lvLRegular,
                                UserId = bookVo.book_pub
                            });

                            // ✅ 使用DapperHelper插入消息
                            string messageTitle = "您的一个主题设为精华，奖励:" + moneyRegular + "个" + siteVo.sitemoneyname + "，奖励经验：" + lvLRegular;
                            string messageContent = "设置时间：" + DateTime.Now + "[br]论坛主题:[url=/bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bookVo.book_classid + "&amp;id=" + id + "]" + book_title + "[/url]";

                            string insertMessageSql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                                                      VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, 1)";
                            DapperHelper.Execute(connectionString, insertMessageSql, new {
                                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                                UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                                Nickname = DapperHelper.LimitLength(nickname, 50),
                                Title = DapperHelper.LimitLength(messageTitle, 100),
                                Content = DapperHelper.LimitLength(messageContent, 500),
                                ToUserId = bookVo.book_pub
                            });
                        }

                        // ✅ 使用DapperHelper插入日志
                        string insertLogSql = @"INSERT INTO wap_log(siteid,oper_userid,oper_nickname,oper_type,log_info,oper_ip)
                                              VALUES (@SiteId, @OperUserId, @OperNickname, 0, @LogInfo, @OperIp)";
                        DapperHelper.Execute(connectionString, insertLogSql, new {
                            SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                            OperUserId = DapperHelper.SafeParseLong(userid, "操作用户ID"),
                            OperNickname = DapperHelper.LimitLength(nickname, 50),
                            LogInfo = DapperHelper.LimitLength("用户ID:" + userid + "加精用户ID:" + bookVo.book_pub + "发表的ID=" + id + "主题:" + book_title, 500),
                            OperIp = DapperHelper.LimitLength(IP, 50)
                        });

                        // ✅ 使用DapperHelper更新fcount标记
                        string updateFcountSql = "UPDATE [fcount] SET SubMoneyFlag = @SubMoneyFlag WHERE fip = @Fip AND fuserid = @FUserId AND userid = @UserId";
                        DapperHelper.Execute(connectionString, updateFcountSql, new {
                            SubMoneyFlag = fcountSubMoneyFlag + "BBSGOOD" + id + ",",
                            Fip = DapperHelper.LimitLength(IP, 50),
                            FUserId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                            UserId = DapperHelper.SafeParseLong(userid, "用户ID")
                        });
                        INFO = "OK";
                    }
                    else
                    {
                        ShowTipInfo("今天您已操作过一次，请明天再来！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bookVo.book_classid + "&amp;id=" + bookVo.id + "&amp;lpage=" + lpage);
                    }
                }
                else if (bookVo.book_good == 0L)
                {
                    INFO = "ERR";
                }
                else
                {
                    // ✅ 使用DapperHelper进行安全的参数化更新操作
                    string connectionString = PubConstant.GetConnectionString(a);
                    string lockReason = "{" + userVo.nickname + "(ID" + userVo.userid + ")取消加精此帖" + $"{DateTime.Now:MM-dd HH:mm}" + "}<br/>";
                    lockReason += bookVo.whylock;

                    string updatePostSql = "UPDATE wap_bbs SET book_good = 0, whylock = @WhyLock WHERE userid = @SiteId AND id = @PostId";
                    DapperHelper.Execute(connectionString, updatePostSql, new {
                        WhyLock = DapperHelper.LimitLength(lockReason, 1000),
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        PostId = DapperHelper.SafeParseLong(id, "帖子ID")
                    });

                    string book_title = bookVo.book_title.Replace("[", "［").Replace("]", "］");
                    string messageContent = "设置时间：" + DateTime.Now + "[br]论坛主题:[url=/bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bookVo.book_classid + "&amp;id=" + id + "]" + book_title + "[/url]";

                    // ✅ 使用DapperHelper插入消息
                    string insertMessageSql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                                              VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, 1)";
                    DapperHelper.Execute(connectionString, insertMessageSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                        Nickname = DapperHelper.LimitLength(nickname, 50),
                        Title = "您的一个主题取消精华!",
                        Content = DapperHelper.LimitLength(messageContent, 500),
                        ToUserId = bookVo.book_pub
                    });

                    // ✅ 使用DapperHelper插入日志
                    string insertLogSql = @"INSERT INTO wap_log(siteid,oper_userid,oper_nickname,oper_type,log_info,oper_ip)
                                          VALUES (@SiteId, @OperUserId, @OperNickname, 0, @LogInfo, @OperIp)";
                    DapperHelper.Execute(connectionString, insertLogSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        OperUserId = DapperHelper.SafeParseLong(userid, "操作用户ID"),
                        OperNickname = DapperHelper.LimitLength(nickname, 50),
                        LogInfo = DapperHelper.LimitLength("用户ID:" + userid + "取消加精用户ID:" + bookVo.book_pub + "发表的ID=" + id + "主题:" + book_title, 500),
                        OperIp = DapperHelper.LimitLength(IP, 50)
                    });
                    INFO = "OK";
                }
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}