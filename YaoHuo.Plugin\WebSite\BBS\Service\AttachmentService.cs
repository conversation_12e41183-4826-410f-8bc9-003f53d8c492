﻿using System.Collections.Generic;
using System.Text;
using System.Web;
using KeLin.ClassManager.Model;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite.Tool;
using System.Linq;

namespace YaoHuo.Plugin.WebSite
{
    public class AttachmentService
    {
        public static string ProcessAttachments(long isdown, string smallimg, string siteid, string classid, string id, string lpage, string stypelink, string saveUpFilesPath, string sitemoneyname, string http_start, wap2_attachment_BLL attachmentBLL)
        {
            if (isdown <= 0L)
            {
                return string.Empty;
            }

            StringBuilder stringBuilder = new StringBuilder();
            string chargeInfo = GetAttachmentChargeInfo(smallimg, sitemoneyname);
            List<wap2_attachment_Model> attachmentList = GetAttachmentListSafely(id);

            if (attachmentList != null)
            {
                AppendAttachmentSummary(stringBuilder, attachmentList.Count, chargeInfo);
            }

            int maxAttachments = GetMaxAttachments(smallimg);
            AppendAttachmentInfo(stringBuilder, attachmentList, maxAttachments, siteid, classid, id, http_start, saveUpFilesPath);

            if (attachmentList != null && attachmentList.Count > maxAttachments - 1)
            {
                AppendViewAllAttachmentsLink(stringBuilder, http_start, siteid, classid, id, lpage, stypelink);
            }

            return stringBuilder.ToString();
        }

        private static string GetAttachmentChargeInfo(string smallimg, string sitemoneyname)
        {
            string text4 = "";
            string text5 = WapTool.GetArryString(smallimg, '|', 17);
            string text6 = WapTool.GetArryString(smallimg, '|', 18);
            if (!WapTool.IsNumeric(text5)) text5 = "0";
            if (!WapTool.IsNumeric(text6)) text6 = "0";

            if (long.Parse(text5) > 0L)
            {
                text4 = "扣" + text5 + "个" + sitemoneyname;
            }
            if (long.Parse(text6) > 0L)
            {
                if (text4 != "") text4 += "/";
                text4 = text4 + "送" + text6 + "个" + sitemoneyname;
            }
            if (text4 != "") text4 = "(" + text4 + ")";

            return text4;
        }

        /// <summary>
        /// 安全获取附件列表，使用DapperHelper避免SQL注入
        /// </summary>
        /// <param name="id">帖子ID</param>
        /// <returns>附件列表</returns>
        private static List<wap2_attachment_Model> GetAttachmentListSafely(string id)
        {
            try
            {
                // 获取连接字符串 - 使用标准方式，与项目其他地方保持一致
                string instanceName = PubConstant.GetAppString("InstanceName");
                string connectionString = PubConstant.GetConnectionString(instanceName);

                // ✅ 使用DapperHelper进行安全的参数化查询
                string sql = "SELECT * FROM wap2_attachment WHERE book_type = @BookType AND book_id = @BookId ORDER BY ID";

                var result = DapperHelper.Query<wap2_attachment_Model>(connectionString, sql, new {
                    BookType = "bbs",
                    BookId = DapperHelper.SafeParseLong(id, "帖子ID")
                });

                return result?.ToList() ?? new List<wap2_attachment_Model>();
            }
            catch (System.Exception ex)
            {
                // 记录错误但不抛出异常，确保页面正常显示
                System.Diagnostics.Debug.WriteLine($"获取附件列表失败: {ex.Message}");
                return new List<wap2_attachment_Model>();
            }
        }

        /// <summary>
        /// 保留原方法以保持向后兼容性（已弃用）
        /// </summary>
        /// <param name="attachmentBLL">附件BLL对象</param>
        /// <param name="id">帖子ID</param>
        /// <returns>附件列表</returns>
        [System.Obsolete("此方法存在SQL注入风险，请使用GetAttachmentListSafely方法")]
        private static List<wap2_attachment_Model> GetAttachmentList(wap2_attachment_BLL attachmentBLL, string id)
        {
            // 直接调用安全方法
            return GetAttachmentListSafely(id);
        }

        private static void AppendAttachmentSummary(StringBuilder stringBuilder, int attachmentCount, string chargeInfo)
        {
            stringBuilder.Append("<div class='attachment'>");
            stringBuilder.Append("<span class='attachmenSum'>");
            stringBuilder.Append("<span class='attachmentext'>共有</span>");
            stringBuilder.Append("<span class='attachmentlistnum'>" + attachmentCount + "</span>");
            stringBuilder.Append("<span class='attachmentext'>个附件</span>");
            stringBuilder.Append("<span class='attachmentCharge'>" + chargeInfo + "</span>");
            stringBuilder.Append("</span>");
        }

        private static int GetMaxAttachments(string smallimg)
        {
            string text7 = WapTool.GetArryString(smallimg, '|', 33);
            if (!WapTool.IsNumeric(text7)) text7 = "2";
            if (text7 == "0") text7 = "1000";
            return int.Parse(text7);
        }

        private static void AppendAttachmentInfo(StringBuilder stringBuilder, List<wap2_attachment_Model> attachmentList, int maxAttachments, string siteid, string classid, string id, string http_start, string saveUpFilesPath)
        {
            if (attachmentList == null) return;

            for (int i = 0; i < attachmentList.Count && i < maxAttachments; i++)
            {
                var attachment = attachmentList[i];
                stringBuilder.Append("<div class='attachmentinfo'><span class=\"downloadname\"><span class=\"attachmentnumber\">");
                stringBuilder.Append(i + 1 + ".");
                stringBuilder.Append("</span><span class='attachmentname'><span class='attachmentitle'>");
                stringBuilder.Append(attachment.book_title);
                stringBuilder.Append("</span>");

                AppendFileExtension(stringBuilder, attachment);
                AppendFileSize(stringBuilder, attachment);
                AppendFileContent(stringBuilder, attachment, http_start, siteid, classid, id, saveUpFilesPath);

                stringBuilder.Append("<span class=\"attachmentNote\">");
                stringBuilder.Append(attachment.book_content + "");
                stringBuilder.Append("</span>");
                stringBuilder.Append("</div>");
            }
        }

        private static void AppendFileExtension(StringBuilder stringBuilder, wap2_attachment_Model attachment)
        {
            if (attachment.book_ext.Trim() != "" && attachment.book_ext.Trim() != "mov")
            {
                stringBuilder.Append("<span class=\"FileExtension\">");
                stringBuilder.Append("." + attachment.book_ext);
                stringBuilder.Append("</span></span>");
            }
        }

        private static void AppendFileSize(StringBuilder stringBuilder, wap2_attachment_Model attachment)
        {
            if (attachment.book_size.Trim() != "")
            {
                stringBuilder.Append("<span class=\"attachmentsize\">");
                stringBuilder.Append("(" + attachment.book_size + ")");
                stringBuilder.Append("</span></span>");
            }
        }

        private static void AppendFileContent(StringBuilder stringBuilder, wap2_attachment_Model attachment, string http_start, string siteid, string classid, string id, string saveUpFilesPath)
        {
            if (attachment.book_ext.Trim() != "" && ".jpg|.jpeg|.png|.gif|.webp".IndexOf(attachment.book_ext.ToLower()) >= 0)
            {
                AppendImageContent(stringBuilder, attachment, http_start);
            }
            else if (attachment.book_ext.Trim() != "" && ("mov|flv|m3u8|mp4").IndexOf(attachment.book_ext.ToLower()) >= 0)
            {
                AppendVideoContent(stringBuilder, attachment, http_start, siteid, classid, id, saveUpFilesPath);
            }
            else
            {
                AppendDownloadLink(stringBuilder, attachment, http_start, siteid, classid, id, saveUpFilesPath);
            }
        }

        private static void AppendImageContent(StringBuilder stringBuilder, wap2_attachment_Model attachment, string http_start)
        {
            string imageUrl = http_start + "bbs/" + attachment.book_file;
            if (attachment.book_file.ToLower().StartsWith("http"))
            {
                imageUrl = attachment.book_file;
            }
            stringBuilder.Append("<span class=\"attachmentimage\">");
            stringBuilder.Append("<a href='" + http_start + "bbs/" + HttpUtility.UrlDecode(attachment.book_file) + "'>");
            stringBuilder.Append("<img src='" + imageUrl + "' referrerpolicy='no-referrer'/></a>");
            stringBuilder.Append("</span>");
        }

        private static void AppendVideoContent(StringBuilder stringBuilder, wap2_attachment_Model attachment, string http_start, string siteid, string classid, string id, string saveUpFilesPath)
        {
            string fileExt = WapTool.Right(attachment.book_file.ToLower(), 3);
            if (("mov|flv|m3u8|mp4").IndexOf(fileExt) >= 0)
            {
                stringBuilder.Append("<span class=\"videoplay\"><video src='" + attachment.book_file + "' autobuffer='true' width='100%' height='100%' poster='/NetImages/play.gif' controls>{不支持在线播放，请更换浏览器}</video>");
            }
            else
            {
                AppendDownloadLink(stringBuilder, attachment, http_start, siteid, classid, id, saveUpFilesPath);
            }
        }

        private static void AppendDownloadLink(StringBuilder stringBuilder, wap2_attachment_Model attachment, string http_start, string siteid, string classid, string id, string saveUpFilesPath)
        {
            // 获取下载按钮文本
            string buttonText = GetDownloadButtonText(attachment.book_file);

            stringBuilder.Append("</span><span class=\"downloadlink\"><span class=\"downloadurl\">");
            stringBuilder.Append("<a class='urlbtn' href='" + http_start + "bbs/download.aspx?siteid=" + siteid +
                "&amp;classid=" + classid +
                "&amp;book_id=" + id +
                "&amp;id=" + attachment.ID +
                "&amp;RndPath=" + saveUpFilesPath +
                "&amp;n=" + HttpUtility.UrlEncode(attachment.book_title) + "." + attachment.book_ext +
                "'>" + buttonText + "</a>");
            stringBuilder.Append("</span><span class=\"downloadcount\">(" + attachment.book_click + "次)</span></span>");
        }

        private static string GetDownloadButtonText(string fileUrl)
        {
            if (string.IsNullOrEmpty(fileUrl)) return "点击下载";

            fileUrl = fileUrl.ToLower();

            // 先检查蓝奏云的各种域名变体 - 使用更灵活的匹配模式
            if (System.Text.RegularExpressions.Regex.IsMatch(fileUrl, @"lanzou[a-z]?\.com"))
            {
                return "蓝奏云下载";
            }

            // 定义其他网盘类型及其对应的关键字
            var netdiskMap = new Dictionary<string, string[]>
            {
              { "夸克网盘下载", new[] { "pan.quark.cn" } },
              { "百度网盘下载", new[] { "pan.baidu.com", "eyun.baidu.com" } },
              { "阿里云盘下载", new[] { "aliyundrive.com", "alipan.com" } },
              { "天翼云盘下载", new[] { "cloud.189.cn" } },
              { "迅雷云盘下载", new[] { "pan.xunlei.com" } },
              { "腾讯微云下载", new[] { "share.weiyun.com", "weiyun.com" } },
              { "123云盘下载", new[] { "123pan.com", "123pan.cn", "123865.com", "123684.com", "123912.com" } },
              { "小飞机网盘", new[] { "share.feijipan.com" } },
              { "和彩云下载", new[] { "caiyun.139.com" } },
              { "GitHub链接", new[] { "github.com" } },
              { "115网盘下载", new[] { "115.com" } },
              { "UC网盘下载", new[] { "drive.uc.cn", "broccoli.uc.cn" } }
            };

            // 检查是否匹配任何其他网盘类型
            foreach (var netdisk in netdiskMap)
            {
                if (netdisk.Value.Any(keyword => fileUrl.Contains(keyword)))
                {
                    return netdisk.Key;
                }
            }

            // 如果都不匹配，返回默认文本
            return "点击下载";
        }

        private static void AppendViewAllAttachmentsLink(StringBuilder stringBuilder, string http_start, string siteid, string classid, string id, string lpage, string stypelink)
        {
            stringBuilder.Append("<div class='btBox'><div class='bt1'>");
            stringBuilder.Append("<a href='" + http_start + "bbs/book_view_showfile.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id + "&amp;lpage=" + lpage + stypelink + "'>{查看所有附件}</a> ");
            stringBuilder.Append("</div></div>");
        }
    }
}