// 获取具有 showReward 类的元素 (这些可以认为是此弹窗脚本模块的全局变量)
const rewardButtons = document.querySelectorAll('.showReward');
// 与弹窗本身相关的元素，将在 DOMContentLoaded 或弹窗显示时查询
let rewardOverlayElement;
let rewardContainerElement;
let closeButton;
let confirmRewardButton;

// 显示打赏弹窗
function showRewardPopup() {
    if (!rewardOverlayElement) rewardOverlayElement = document.getElementById('RewardCoins'); // 如果遮罩层元素未缓存，则获取它
    if (!rewardContainerElement && rewardOverlayElement) rewardContainerElement = rewardOverlayElement.querySelector('.popup-container'); // 如果弹窗容器元素未缓存，则在遮罩层内获取它

    if (rewardOverlayElement && rewardContainerElement) {
        rewardOverlayElement.classList.add('active');
        rewardContainerElement.classList.add('active');
        // console.log('显示打赏弹窗 (遮罩层和容器已激活)');
    }
}

// 隐藏打赏弹窗
function hideRewardPopup() {
    if (rewardOverlayElement && rewardContainerElement) {
        rewardOverlayElement.classList.remove('active');
        rewardContainerElement.classList.remove('active');
        // console.log('关闭打赏弹窗 (遮罩层和容器已取消激活)');
    }
}

// 导出公共函数到window对象，使其可全局访问
window.Reward = {
    showRewardPopup: showRewardPopup,
    hideRewardPopup: hideRewardPopup
};

document.addEventListener('DOMContentLoaded', function () {
    // 确保 ASPX 页面定义了 rewardPageConfig
    if (typeof window.rewardPageConfig === 'undefined') {
        console.error('[Reward.js] rewardPageConfig 未定义。打赏功能可能会受限。');
        // 如果配置的关键部分缺失，设置监听器没有意义。
        // return;
    }

    rewardOverlayElement = document.getElementById('RewardCoins'); // 获取打赏弹窗的遮罩层元素
    if (!rewardOverlayElement) {
        // 如果 #RewardCoins div 元素本身不在页面上 (例如，用户没有打赏权限)，
        // 则不需要为打赏交互进行进一步设置。
        // console.log('[Reward.js] 未找到 RewardCoins 元素。跳过打赏特定UI设置。');
        return;
    }
    rewardContainerElement = rewardOverlayElement.querySelector('.popup-container'); // 获取弹窗容器
    closeButton = rewardContainerElement ? rewardContainerElement.querySelector('.popup-close-btn') : null; // 获取关闭按钮
    confirmRewardButton = rewardOverlayElement.querySelector(".givebtn"); // 确认打赏按钮可能在容器外部但在遮罩层内部

    // 点击遮罩层关闭弹窗
    rewardOverlayElement.addEventListener('click', function (event) {
        if (event.target === rewardOverlayElement) { // 关键判断：确保点击的是遮罩层本身
            hideRewardPopup();
            // console.log('[Reward.js] 点击遮罩层，关闭打赏弹窗');
        }
    });

    // 点击关闭按钮，关闭打赏弹窗
    if (closeButton) {
        closeButton.addEventListener("click", function (e) {
            e.preventDefault(); // 阻止默认行为
            e.stopPropagation(); // 停止事件冒泡
            hideRewardPopup();
        });
    }

    // 打赏按钮点击事件 (触发弹窗显示的按钮)
    // 这些按钮在弹窗外部，所以它们的监听器可以一次性设置。
    const staticRewardButtons = document.querySelectorAll('.showReward'); // 如果它们是动态的，则重新查询；如果总是静态的，则使用全局变量
    staticRewardButtons.forEach(function (rewardButton) {
        rewardButton.addEventListener("click", function (e) {
            e.preventDefault(); // 阻止默认行为
            showRewardPopup();
            e.stopPropagation(); // 停止事件冒泡
        });
    });

    // --- 从内联 ASPX 脚本移动过来的逻辑 --- 
    const gridButtons = rewardOverlayElement.querySelectorAll('.aui-grids-item'); // 获取九宫格金额按钮
    const typeAmountDisplay = rewardOverlayElement.querySelector('#type-amount'); // 获取显示选择金额的元素
    const sendMoneyHiddenInput = rewardOverlayElement.querySelector("input[name='sendmoney']"); // 获取隐藏的存储发送金额的 input
    const balanceSpan = rewardOverlayElement.querySelector('#balance'); // 获取显示用户余额的元素

    // 如果配置存在且余额元素存在，则设置用户余额显示
    if (window.rewardPageConfig && balanceSpan && window.rewardPageConfig.currentUserBalance !== undefined) {
        balanceSpan.textContent = window.rewardPageConfig.currentUserBalance;
    }

    // 初始化九宫格打赏金额选择功能
    if (gridButtons.length > 0 && typeAmountDisplay && sendMoneyHiddenInput) {
        // console.log('[Reward.js] 打赏九宫格已初始化。找到 ' + gridButtons.length + ' 个金额按钮。');
        gridButtons.forEach(function (button) {
            button.addEventListener('click', function (e) {
                // e.stopPropagation(); // 此处不一定需要停止冒泡，因为点击是在模态框内的按钮，由遮罩层点击处理
                gridButtons.forEach(function (b) { b.classList.remove('this-card'); }); // 移除其他按钮的选中状态
                this.classList.add('this-card'); // 给当前点击的按钮添加选中状态
                var displayAmount = this.querySelector('span').textContent; // 获取显示的金额文本
                var sendAmountValue = this.getAttribute('value'); // 获取实际发送的金额值
                typeAmountDisplay.innerHTML = '打赏<span id="bounty" class="space">' + displayAmount + '</span><span class="space"></span>妖晶'; // 更新显示的打赏金额
                sendMoneyHiddenInput.value = sendAmountValue; // 更新隐藏input的值
                // console.log('[Reward.js] 已选择金额: ' + displayAmount + ', 值: ' + sendAmountValue);
            });
        });
    } else {
        console.warn('[Reward.js] 未能找到打赏九宫格所需的所有元素: gridButtons, typeAmountDisplay, 或 sendMoneyHiddenInput。');
    }
    // --- 移动逻辑结束 ---

    // 确认打赏按钮点击事件
    if (confirmRewardButton) {
        confirmRewardButton.addEventListener("click", function (e) {
            e.preventDefault(); // 阻止默认行为
            e.stopPropagation(); // 停止事件冒泡

            const formElement = rewardOverlayElement.querySelector('form[name=send]'); // 获取表单元素
            if (!formElement) {
                console.error('[Reward.js] 找不到打赏表单');
                return;
            }
            const formData = new FormData(formElement); // 创建 FormData 对象
            const sendmoneyValue = formData.get('sendmoney'); // 获取要发送的金额

            // 校验是否选择了金额
            if (!sendmoneyValue) {
                console.error('[Reward.js] 请选择打赏妖晶数量');
                showSelectAmountTip(); // 显示提示信息
                return;
            }

            // 校验页面配置是否存在
            if (typeof window.rewardPageConfig === 'undefined') {
                console.error('[Reward.js] rewardPageConfig 缺失，无法处理打赏。');
                showFailureTip('配置错误，无法打赏');
                return;
            }

            const sendmoney = parseInt(sendmoneyValue); // 将金额转为整数
            const touserid = window.rewardPageConfig.touserid; // 从配置中获取接收者ID
            const myuserid = window.rewardPageConfig.myuserid; // 从配置中获取当前用户ID
            const formActionUrl = window.rewardPageConfig.formActionUrl; // 从配置中获取表单提交URL
            
            // 确保关键字段从rewardPageConfig中获取最新值
            // 创建新的FormData对象，因为原始FormData不能直接修改
            const updatedFormData = new FormData();
            
            // 先复制原始表单的值
            for (const [key, value] of formData.entries()) {
                updatedFormData.append(key, value);
            }
            
            // 从rewardPageConfig中更新或添加关键字段
            if (window.rewardPageConfig.id) updatedFormData.set('id', window.rewardPageConfig.id);
            if (window.rewardPageConfig.classid) updatedFormData.set('classid', window.rewardPageConfig.classid);
            if (window.rewardPageConfig.siteid) updatedFormData.set('siteid', window.rewardPageConfig.siteid);
            if (window.rewardPageConfig.touserid) updatedFormData.set('touserid', window.rewardPageConfig.touserid);
            if (window.rewardPageConfig.myuserid) updatedFormData.set('myuserid', window.rewardPageConfig.myuserid);
            
            // 确保action参数
            updatedFormData.set('action', 'gomod');

            // 发起打赏请求
            fetch(formActionUrl, {
                method: 'POST',
                body: updatedFormData // 使用更新后的FormData
            })
                .then(res => res.text()) // 将响应转为文本
                .then(html => handleRewardResponse(html, sendmoney, touserid, myuserid)) // 处理响应
                .catch(error => handleError(error)); // 处理错误
        });
    }
});

// 辅助函数 (showSelectAmountTip, handleRewardResponse 等) 应在下面定义或在此作用域内可访问
// ... (其余辅助函数: showSelectAmountTip, handleRewardResponse, showSuccessTip, updateEarnBounty, simulateCloseButtonClick, handleFailureReward, showFailureTip, handleError)
// 确保这些函数已根据您现有的 Reward.js 文件完整且正确地定义。

// (如果之前的片段中未包含，请确保在此处粘贴辅助函数的完整定义)
// 例如:
function showSelectAmountTip() {
    const selectAmountTipId = 'selectAmountTip' + Date.now();
    const selectAmountTip = '<div class="ui__alert" id="' + selectAmountTipId + '" style="z-index: 9999;position: relative;"><div class="ui__alert_bg ui__alert_border in"></div> <div class="ui__alert_content ui__alert_border in"> <div class="ui__content_body"><h4 class="ui__title">请选择打赏妖晶数量</h4></div></div></div>';
    document.body.insertAdjacentHTML('beforeend', selectAmountTip);
    setTimeout(() => {
        const selectAmountTipElement = document.getElementById(selectAmountTipId);
        if (selectAmountTipElement) {
            selectAmountTipElement.style.display = 'none';
            selectAmountTipElement.remove();
        }
    }, 800);
}

function handleRewardResponse(html, sendmoney, touserid, myuserid) {
    const tipDiv = document.createElement("div");
    tipDiv.innerHTML = html;
    
    const successMessage = tipDiv.querySelector(".tip b");
    
    if (successMessage && successMessage.textContent.includes("打赏送币成功")) {
        handleSuccessReward(sendmoney);
    } else {
        handleFailureReward(sendmoney, touserid, myuserid);
    }
}

function handleSuccessReward(sendmoney) {
    // console.log("[Reward.js] 打赏送币成功！");
    const actualBounty = Math.round(sendmoney * 0.99); // 假设此逻辑正确
    showSuccessTip();
    updateEarnBounty(actualBounty);
    simulateCloseButtonClick();
    const postRewardElement = document.querySelector(".post-badge");
    if (!postRewardElement) {
        const postInfoElement = document.querySelector(".Postinfo");
        if (postInfoElement) {
            const postRewardHtml = '<span class="post-badge animated-stamp" id="stamp-badge" style="opacity: 1;">获赏<span class="earnbounty">0</span></span>';
            postInfoElement.insertAdjacentHTML('beforeend', postRewardHtml);
            const initialEarnBountyElement = document.querySelector(".post-badge .earnbounty");
            if (initialEarnBountyElement) {
                let currentDisplayBounty = 0;
                const increment = Math.max(1, Math.floor(actualBounty / 50)); // 最多50步动画或逐1增加
                const intervalTime = Math.max(10, 3000 / (actualBounty / increment)); // 大约3秒总动画时间
                const initialTimer = setInterval(() => {
                    currentDisplayBounty += increment;
                    if (currentDisplayBounty >= actualBounty) {
                        initialEarnBountyElement.textContent = actualBounty;
                        clearInterval(initialTimer);
                    } else {
                        initialEarnBountyElement.textContent = currentDisplayBounty;
                    }
                }, intervalTime);
            }
        }
    }
}

function showSuccessTip() {
    const successTipId = 'successTip' + Date.now();
    const successTip = '<div class="ui__alert" id="' + successTipId + '" style="z-index: 9999;position: relative;"><div class="ui__alert_bg ui__alert_border in"></div> <div class="ui__alert_content ui__alert_border in"> <div class="ui__content_body"><h4 class="ui__title">打赏成功！感谢您的慷慨！</h4></div></div></div>';
    document.body.insertAdjacentHTML('beforeend', successTip);
    setTimeout(() => {
        const successTipElement = document.getElementById(successTipId);
        if (successTipElement) {
            successTipElement.style.display = 'none';
            successTipElement.remove();
        }
    }, 800);
}

function updateEarnBounty(actualBounty) {
    const earnBountyElement = document.querySelector(".earnbounty");
    if (earnBountyElement) {
        const currentBountyText = earnBountyElement.textContent.trim();
        const currentBounty = currentBountyText === '' ? 0 : parseInt(currentBountyText);
        const newBounty = currentBounty + actualBounty;

        let currentDisplayBounty = currentBounty;
        const increment = Math.max(1, Math.floor(actualBounty / 50));
        const intervalTime = Math.max(10, Math.min(100, 1500 / (actualBounty / increment))); // 限制间隔以获得更平滑的动画

        const timer = setInterval(() => {
            currentDisplayBounty += increment;
            if (currentDisplayBounty >= newBounty) {
                earnBountyElement.textContent = newBounty;
                clearInterval(timer);
            } else {
                earnBountyElement.textContent = currentDisplayBounty;
            }
        }, intervalTime);
    }
}

function simulateCloseButtonClick() {
    // 重新查询关闭按钮，以防DOM被重新渲染或最初不可用
    const currentRewardContainer = document.getElementById('RewardCoins')?.querySelector('.popup-container');
    const currentCloseBtn = currentRewardContainer?.querySelector('.popup-close-btn');
    if (currentCloseBtn) {
        currentCloseBtn.click();
    }
}

function handleFailureReward(sendmoney, touserid, myuserid) {
    let failureMessage = '';
    // 如果可用，则访问配置数据，否则使用合理的默认值或错误
    const configTouserid = window.rewardPageConfig ? window.rewardPageConfig.touserid : null;
    const configMyuserid = window.rewardPageConfig ? window.rewardPageConfig.myuserid : null;

    if (sendmoney < 101) {
        failureMessage = '打赏失败，低于下限';
    } else if (sendmoney > 20200) {
        failureMessage = '打赏失败，超过上限';
    } else if (configTouserid === configMyuserid && configTouserid !== null) { // 检查它们是否相同
        failureMessage = '打赏失败，不能赏自己';
        simulateCloseButtonClick();
    } else {
        failureMessage = '打赏失败，余额不足或其他原因'; // 更通用的消息
    }
    console.error('[Reward.js] ' + failureMessage);
    showFailureTip(failureMessage);
}

function showFailureTip(message) {
    const failureTipId = 'failureTip' + Date.now();
    const failureTip = '<div class="ui__alert" id="' + failureTipId + '" style="z-index: 9999;position: relative;"><div class="ui__alert_bg ui__alert_border in"></div> <div class="ui__alert_content ui__alert_border in"> <div class="ui__content_body"><h4 class="ui__title">' + message + '</h4></div></div></div>';
    document.body.insertAdjacentHTML('beforeend', failureTip);
    setTimeout(() => {
        const failureTipElement = document.getElementById(failureTipId);
        if (failureTipElement) {
            failureTipElement.style.display = 'none';
            failureTipElement.remove();
        }
    }, 800);
}

function handleError(error) {
    console.error('[Reward.js] 发生错误：', error);
    showFailureTip('操作失败，请稍后再试');
}