﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_View_addfileAdd.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Book_View_addfileAdd" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    wmlVo.mycss += "\r\n<link href=\"/netcss/css/upload-resource.css?X11\" rel=\"stylesheet\" type=\"text/css\"/>";
    StringBuilder strhtml = new StringBuilder();
    Response.Write(WapTool.showTop(this.GetLang("续传文件|续传文件|add subject"), wmlVo));
    if (num > 9) num = 9;
    if (num < 1) num = 1;
    strhtml.Append("<div class=\"upload-container\">");
    strhtml.Append("<div class=\"tab-header\">");
    strhtml.Append("<a class=\"tab-btn active\"  href=\"#\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242\"></path><path d=\"M12 21v-9\"></path><path d=\"m8 16 4-4 4 4\"></path></svg>本地文件</a> ");
    strhtml.Append("<a class=\"tab-btn\" href=\"" + this.http_start + "bbs/book_view_addfileaddURL.aspx?action=class&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;page=" + this.lpage + "&amp;id=" + this.id + "&amp;num=1\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path d=\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\"></path><path d=\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\"></path></svg>外站资源</a> ");
    strhtml.Append("</div>");
    strhtml.Append(this.ERROR);
    if (this.INFO == "OK")
    {
        strhtml.Append("<div class=\"upload-success\">");
        strhtml.Append("<div class=\"upload-success-header\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-upload h-4 w-4 text-green-600 dark:text-green-400\" data-id=\"7\"><path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"></path><polyline points=\"17 8 12 3 7 8\"></polyline><line x1=\"12\" x2=\"12\" y1=\"3\" y2=\"15\"></line></svg>");
        strhtml.Append("<div class=\"upload-success-title\">续传文件成功！</div>");
        strhtml.Append("</div>");
        strhtml.Append("<div class=\"upload-success-subtitle\">附件已成功添加到帖子</div>");
        strhtml.Append("</div>");
        if (siteVo.isCheck == 1)
        {
            strhtml.Append("<b>审核后显示！</b>");
        }
    }
    else if (!string.IsNullOrEmpty(this.INFO))
    {
        strhtml.Append("<div class=\"tip\">");
        if (this.INFO == "EXTERR")
        {
            strhtml.Append("<b>上传文件格式错误，只允许上传：" + siteVo.UpFileType + "</b><br/>");
        }
        else if (this.INFO == "NOTSPACE")
        {
            strhtml.Append("<b>网站总空间已经大于系统分配给此网站的最大空间了，网站空间：" + siteVo.sitespace + "M；此网站已使用：" + (siteVo.myspace) + "KB</b><br/>");
        }
        else if (this.INFO == "MAXFILE")
        {
            strhtml.Append("<b>你上传的单个文件超出了最大限制" + siteVo.MaxFileSize + "KB</b><br/>");
        }
        else if (this.INFO == "LOCK")
        {
            strhtml.Append("<b>抱歉，您已经被加入黑名单，请注意发帖规则！</b><br/>");
        }
        strhtml.Append("</div>");
    }
    strhtml.Append("<div class=\"content\">");
    if (this.INFO != "OK")
    {
        strhtml.Append("<form name=\"f\" action=\"" + http_start + "bbs/book_view_addfileadd.aspx\" enctype=\"multipart/form-data\" method=\"post\">");
        strhtml.Append("<input type=\"file\" id=\"multiFileSelect\" style=\"display:none\" multiple accept=\".txt,.zip,.rar,.7z,.apk,.jpg,.jpeg,.png,.gif,.webp,.torrent,.mp3,.wma,.wav,.pdf,.xls,.doc,.docx\" />");
        strhtml.Append("<div id=\"fileSelectArea\" class=\"file-select-area\" onclick=\"document.getElementById('multiFileSelect').click()\" ondrop=\"handleDrop(event)\" ondragover=\"handleDragOver(event)\" ondragleave=\"handleDragLeave(event)\">");
        strhtml.Append("<div class=\"big-upload-icon\">+</div>");
        strhtml.Append("<div class=\"upload-text\">点击此处上传文件</div>");
        strhtml.Append("</div>");
        strhtml.Append("<div id=\"fileUploadContainer\"></div><script type=\"text/javascript\" src=\"/netcss/js/fileupload/file-common.js\"></script>");
        strhtml.Append(@"<script>document.addEventListener('DOMContentLoaded',function(){updateFileDisplay({isAppend:true})});</script>");
        strhtml.Append("<input type=\"hidden\" id=\"numInput\" name=\"num\" value=\"1\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"lpage\" value=\"" + lpage + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"id\" value=\"" + id + "\"/>");
        strhtml.Append("<button type=\"submit\" name=\"g\" id=\"submitBtn\">");
        strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-upload h-4 w-4\" data-id=\"36\"><path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"></path><polyline points=\"17 8 12 3 7 8\"></polyline><line x1=\"12\" x2=\"12\" y1=\"3\" y2=\"15\"></line></svg>");
        strhtml.Append(this.GetLang("确认上传|上传文件|upload new subject") + "</button>");
        strhtml.Append("</form>");
    }
    strhtml.Append("</div>");
    strhtml.Append("<div class=\"triangle-alert\">");
    strhtml.Append("<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-triangle-alert\"><path d=\"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3\"/><path d=\"M12 9v4\"/><path d=\"M12 17h.01\"/></svg>严禁上传色情文件、病毒文件和恶意软件。");
    strhtml.Append("</div>");
    string isWebHtml = this.ShowWEB_view(this.classid);
    if (isWebHtml != "")
    {
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml, wmlVo).Replace("[view]", strhtml.ToString()));
        Response.End();
    }
    strhtml.Append("<div class=\"nav-buttons\">");
    strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs/Book_View_admin.aspx?action=class&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;id=" + this.id + "\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-arrow-left\"><path d=\"m12 19-7-7 7-7\"/><path d=\"M19 12H5\"/></svg>" + this.GetLang("返回管理|回管理|add content") + "</a>");
    strhtml.Append("<a class=\"nav-btn\" href=\"" + this.http_start + "bbs-" + id + ".html\"><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\" class=\"lucide lucide-house\"><path d=\"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8\"/><path d=\"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"/></svg>返回主题</a>");
    strhtml.Append("</div>");
    strhtml.Append(@"<style>#submitBtn{display:none;}</style>");
    Response.Write(strhtml);
    Response.Write(WapTool.showDown(wmlVo));
%>