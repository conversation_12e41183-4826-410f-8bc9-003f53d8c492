let currentMode = 'message'; // 'message' 或 'reply'
let originalPostParams = null; // 用于存储从"到帖子中查看"链接解析的参数

function parseThreadLink() {
    const retextSpan = document.querySelector('span.retext');
    if (retextSpan) {
        const threadLink = retextSpan.querySelector('a[href*="bbs/book_re.aspx"]');
        if (threadLink) {
            try {
                const url = new URL(threadLink.href, window.location.origin);
                const params = {
                    siteid: url.searchParams.get('siteid'),
                    classid: url.searchParams.get('classid'),
                    id: url.searchParams.get('id'), // 这是主帖 bookid
                    tofloor: url.searchParams.get('tofloor'), // 这是目标楼层的原始楼层号
                    fromuserid: url.searchParams.get('fromuserid') || url.searchParams.get('touserid') // 兼容老消息
                };
                if (params.siteid && params.classid && params.id && params.tofloor && params.fromuserid) {
                    originalPostParams = params;
                    return true;
                }
            } catch (e) {
                console.error("解析帖子链接失败:", e);
            }
        }
    }
    return false;
}

function addToggleModeButton() {
    const submitBtn = document.getElementsByName('g')[0];
    if (submitBtn && originalPostParams) {
        const toggleBtn = document.createElement('a');
        toggleBtn.href = 'javascript:void(0);';
        toggleBtn.textContent = '切换回帖';
        toggleBtn.id = 'toggleModeBtn';
        toggleBtn.style.marginLeft = '10px';
        toggleBtn.style.textDecoration = 'underline';

        toggleBtn.addEventListener('click', function () {
            if (currentMode === 'message') {
                currentMode = 'reply';
                submitBtn.value = '发送回帖';
                toggleBtn.textContent = '切换私信';
            } else {
                currentMode = 'message';
                submitBtn.value = '发送消息';
                toggleBtn.textContent = '切换回帖';
            }
        });
        submitBtn.parentNode.insertBefore(toggleBtn, submitBtn.nextSibling);
    }
}

async function getReplyIdByToFloor(params) {
    // params should include siteid, id (bookid), tofloor
    const apiUrl = `/bbs/api/GetReplyInfo.ashx?siteid=${params.siteid}&id=${params.id}&tofloor=${params.tofloor}`;

    try {
        const response = await fetch(apiUrl);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status} when fetching from ${apiUrl}`);
        }
        const data = await response.json();

        if (data.replyid) {
            return data.replyid;
        }
        if (data.error) {
            console.warn(`API Error from GetReplyInfo.ashx (tofloor: ${params.tofloor}): ${data.error}`);
        }
        return null;

    } catch (error) {
        console.error(`获取replyid失败 (AJAX to GetReplyInfo.ashx, tofloor: ${params.tofloor}):`, error);
        return null;
    }
}

function asyncSubmit() { // Renamed from asyncComment to avoid conflict if this script is loaded with BookRe/FastC.js
    const form = document.forms[0]; // Assuming there's only one form
    if (!form) return;
    const submitBtn = form.querySelector('[name="g"]');
    if (!submitBtn) return;

    submitBtn.addEventListener('click', async function (e) {
        e.preventDefault(); // Always prevent default first

        const formData = new FormData(form);
        const entries = formData.entries();
        const data = Object.fromEntries(entries);
        data.content = data.content.replace(/\n/g, '[br]'); // Ensure this is the correct newline replacement

        if (data.content.trim().length === 0) {
            showCustomTip("内容不能为空！", "failure", 1500);
            return;
        }

        if (currentMode === 'message') {
            data.g = '发送消息'; // Keep original button value for message
            fetch('/bbs/messagelist_add.aspx', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8;'
                },
                body: new URLSearchParams(data)
            })
                .then(res => {
                    if (!res.ok) {
                        throw new Error(`HTTP error! status: ${res.status}`);
                    }
                    return res.text();
                })
                .then(html => {
                    const success = /发送信息成功/.test(html);
                    if (success) {
                        showSuccessTip(); // Existing function
                        updateMessages(); // Existing function
                        form.content.value = ''; // Clear textarea
                    } else {
                        // Try to get a more specific error from the response HTML if possible
                        let errorMsg = "发送消息失败，请不要发送重复内容！其它原因：私信数量已达当日上限，或处于对方黑名单。";
                        // Example: const errorMatch = html.match(/<div class="tip"><b>(.*?)<\/b><\/div>/);
                        // if (errorMatch && errorMatch[1]) errorMsg = errorMatch[1];
                        showCustomTip(errorMsg, "failure"); // Use a more generic failure tip or parse specific error
                    }
                })
                .catch(error => {
                    console.error('发送消息错误：', error);
                    showCustomTip("发送消息时发生网络错误。", "failure");
                });
        } else if (currentMode === 'reply' && originalPostParams) {
            submitBtn.disabled = true;
            submitBtn.value = "提交中...";

            const replyFloor = originalPostParams.tofloor;
            if (!replyFloor) {
                showCustomTip("无法获取目标楼层号，无法回帖。请确保目标楼层存在且未被删除。", "failure");
                submitBtn.disabled = false;
                submitBtn.value = "发送回帖";
                return;
            }

            const replyData = {
                content: data.content,
                reply: replyFloor,
                fromuserid: originalPostParams.fromuserid,
                siteid: originalPostParams.siteid,
                classid: originalPostParams.classid,
                id: originalPostParams.id,
                action: 'add',
                ajax: '1',
                sendmsg2: '1',
            };

            fetch(`/bbs/book_re.aspx?ajax=1&siteid=${replyData.siteid}&classid=${replyData.classid}&id=${replyData.id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8;'
                },
                body: new URLSearchParams(replyData)
            })
                .then(res => res.text())
                .then(html => {
                    // Parse Book_Re.aspx AJAX response (similar to FastC.js)
                    const tipRegex = new RegExp('class="tip">([\\s\\S]*?)<\\/div>');
                    let recontenttipMatch = tipRegex.exec(html);
                    let recontenttip = recontenttipMatch ? recontenttipMatch[1] : '';
                    let tipMessage = recontenttip; // Default to raw tip
                    let tipType = "info";
                    let timeoutDuration = 3000;

                    if (recontenttip.includes('回复成功')) {
                        tipType = "success";
                        timeoutDuration = 1500;
                        const successRegex = /获得妖晶:(\d+)，获得经验:(\d+)/;
                        let successMatch = successRegex.exec(recontenttip);
                        if (successMatch) {
                            tipMessage = `回复成功！获得妖晶 ${successMatch[1]}，经验 ${successMatch[2]}`;
                        } else {
                            tipMessage = "回复成功！";
                        }
                        form.content.value = ''; // Clear textarea
                    } else if (recontenttip.includes('回复内容最少')) {
                        tipType = "failure";
                        const minLengthRegex = /回复内容最少(.*?)字/;
                        tipMessage = `回复内容最少 ${(minLengthRegex.exec(recontenttip) || ['', ''])[1]} 字`;
                    } else if (recontenttip.includes('回复内容最多')) {
                        tipType = "failure";
                        const maxLengthRegex = /回复内容最多(.*?)字/;
                        tipMessage = `回复内容最多 ${(maxLengthRegex.exec(recontenttip) || ['', ''])[1]} 字`;
                    } else if (recontenttip.includes('请不要发重复内容')) {
                        tipType = "failure";
                        tipMessage = "请不要发重复内容";
                    } else if (recontenttip.includes('请再过')) {
                        tipType = "failure";
                        const waitTimeRegex = /请再过(.*?)秒后操作/;
                        tipMessage = `操作过于频繁，请再过 ${(waitTimeRegex.exec(recontenttip) || ['', ''])[1]} 秒`;
                    } else if (recontenttip.includes('今天已达回帖上限')) {
                        tipType = "failure";
                        const limitRegex = /今天已达回帖上限 (.*?) 条/;
                        tipMessage = `今天已达回帖上限 ${(limitRegex.exec(recontenttip) || ['', ''])[1]} 条`;
                    } else if (recontenttip.includes('您已被加入黑名单') || recontenttip.includes('您已被禁止发言')) {
                        tipType = "failure";
                        tipMessage = "抱歉，您可能已被禁止发言或加入对方黑名单。";
                    } else if (recontenttip.includes('此帖已锁定') || recontenttip.includes('此帖已结束')) {
                        tipType = "failure";
                        tipMessage = "帖子已锁定或结束，无法回复。";
                    }
                    // Add more specific error parsing if needed

                    showCustomTip(tipMessage, tipType, timeoutDuration);
                })
                .catch(error => {
                    console.error('发送回帖错误：', error);
                    showCustomTip("发送回帖时发生网络错误。", "failure");
                })
                .finally(() => {
                    submitBtn.disabled = false;
                    if (currentMode === 'reply') submitBtn.value = "发送回帖";
                });
        }
    });
}

document.addEventListener("DOMContentLoaded", function () {
    const textareas = document.getElementsByTagName("textarea");
    for (let i = 0; i < textareas.length; i++) {
        textareas[i].addEventListener("input", function () {
            this.style.height = "auto";
            this.style.height = `${this.scrollHeight}px`;
        });
        // Initialize height
        textareas[i].style.overflowY = "hidden"; // Hide scrollbar to correctly calculate scrollHeight
        textareas[i].style.height = "auto"; // Reset height to auto to get natural scrollHeight
        textareas[i].style.height = `${textareas[i].scrollHeight}px`; // Set height
    }

    if (parseThreadLink()) {
        addToggleModeButton();
    }
    asyncSubmit(); // Call the renamed main submit handler
});

function updateMessages() {
    const mmscontent = document.getElementsByClassName('mmscontent')[0];
    if (!mmscontent) return;
    const currentURL = window.location.pathname + window.location.search;
    fetch(currentURL, { method: 'GET' })
        .then(res => res.text())
        .then(html => {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const newMmsContent = doc.querySelector('.mmscontent');
            if (newMmsContent) {
                mmscontent.innerHTML = newMmsContent.innerHTML;
            }
        })
        .catch(e => console.error("Failed to update messages:", e));
}

// Simplified and combined tip function
function showCustomTip(message, type = "info", duration = 3000) { // type can be 'success', 'failure', 'info'
    const oldTip = document.getElementById('dynamicTip');
    if (oldTip) {
        oldTip.remove();
    }

    let tipClasses = "ui__alert";
    let title = "提示";
    if (type === "success") {
        title = "成功";
        // tipClasses += " ui__alert_success"; // Add classes for styling if you have them
    } else if (type === "failure") {
        title = "失败";
        // tipClasses += " ui__alert_failure";
    }

    const tipHtml = `
        <div class="${tipClasses}" id="dynamicTip" style="position: fixed; top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; background-color: #fff; padding: 10px 20px; border-radius: 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.2); border: 1px solid #ddd;">
            <div class="ui__alert_content in">
                <div class="ui__content_body">
                    <h4 class="ui__title" style="margin: 0 0 5px 0; font-size: 16px;">${title}</h4>
                    <div style="font-size: 14px;">${message}</div>
                </div>
            </div>
        </div>`;

    document.body.insertAdjacentHTML('beforeend', tipHtml);
    const dynamicTip = document.getElementById('dynamicTip');

    setTimeout(() => {
        if (dynamicTip) {
            dynamicTip.remove();
        }
    }, duration);

    dynamicTip.addEventListener('click', () => { // Allow click to dismiss
        if (dynamicTip) {
            dynamicTip.remove();
        }
    });
}

// Remove existing tip functions if showCustomTip covers all needs
function showSuccessTip() {
    showCustomTip("发送信息成功！", "success", 1500);
    // const oldTip = document.getElementById('updateTip');
    // if (oldTip) {
    //     oldTip.remove();
    // }
    // const tip = '<div class="ui__alert" id="updateTip"><div class="ui__alert_bg ui__alert_border in"></div> <div class="ui__alert_content ui__alert_border in"> <div class="ui__content_body"><h4 class="ui__title">发送信息成功！</h4></div></div></div>';
    // const mmscontent = document.getElementsByClassName('mmscontent')[0];
    // if (mmscontent) mmscontent.insertAdjacentHTML('beforebegin', tip);
    // const successTip = document.getElementById('updateTip');
    // if(successTip){
    //     setTimeout(() => {
    //         if (successTip) successTip.style.display = 'none';
    //     }, 300);
    // }
}

function showFailureTip() {
    showCustomTip("发送信息失败，请检查内容或网络。", "failure", 3000);
    // const oldTip = document.getElementById('updateTip');
    // if (oldTip) {
    //     oldTip.remove();
    // }
    // const failureTip = '<div class="ui__alert" id="updateTip"><div class="ui__alert_bg ui__alert_border in"></div> <div class="ui__alert_content ui__alert_border in"> <div class="ui__content_body"><h4 class="ui__title">发送信息失败，请不要发送重复内容！其它原因：私信数量已达当日上限，或处于对方黑名单。</h4> </div></div></div>';
    // const mmscontent = document.getElementsByClassName('mmscontent')[0];
    // if (mmscontent) mmscontent.insertAdjacentHTML('beforebegin', failureTip);
    // const failTip = document.getElementById('updateTip');
    // if(failTip){
    //     setTimeout(() => {
    //         if (failTip) failTip.style.display = 'none';
    //     }, 1500);
    // }
}

// Remove the generic click listener that hides 'updateTip' if 'dynamicTip' is used everywhere
// document.addEventListener('click', function() {
//     const tip = document.getElementById('updateTip');
//     if (tip && tip.style.display !== 'none') {
//         tip.style.display = 'none';
//     }
// });

// window.addEventListener('load', asyncComment); // Changed to asyncSubmit and called in DOMContentLoaded