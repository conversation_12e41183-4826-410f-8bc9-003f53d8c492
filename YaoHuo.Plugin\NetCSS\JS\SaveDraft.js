﻿// 获取表单元素
const form = document.forms['f'];
const titleInput = form.elements['book_title'];
const contentTextarea = form.elements['book_content'];
const saveDraftButton = document.getElementById('saveDraftButton');
const clearDraftButton = document.getElementById('clearDraftButton');
const notificationContainer = document.querySelector('.notification-container');
let autoSaveTimer; // 定时器用于延迟自动保存
const autoSaveInterval = 2000; // 设置自动保存的时间间隔，如2秒

// 添加一个变量来跟踪上次显示通知的时间
let lastNotificationTime = 0;

// 检查localStorage是否可用
function storageAvailable(type) {
  var storage;
  try {
    storage = window[type];
    var x = '__storage_test__';
    storage.setItem(x, x);
    storage.removeItem(x);
    return true;
  } catch (e) {
    return e instanceof DOMException && (
      // everything except Firefox
      e.code === 22 ||
      // Firefox
      e.code === 1014 ||
      // test name field too, because code might not be present
      // everything except Firefox
      e.name === 'QuotaExceededError' ||
      // Firefox
      e.name === 'NS_ERROR_DOM_QUOTA_REACHED') &&
      // acknowledge QuotaExceededError only if there's something already stored
      (storage && storage.length !== 0);
  }
}

// 尝试恢复草稿
function attemptToRestoreDraft() {
  if (storageAvailable('localStorage')) {
    const savedTitle = localStorage.getItem('draft_title');
    const savedContent = localStorage.getItem('draft_content');

    if (savedTitle || savedContent) {
      titleInput.value = savedTitle || '';
      contentTextarea.value = savedContent || '';
      showClearDraftButton();
    }
  } else {
    showNotification('本地存储不可用，无法恢复草稿。');
  }
}

// 页面加载时尝试恢复草稿
window.addEventListener('load', attemptToRestoreDraft);

// 节流函数
function throttle(func, wait, immediate) {
  var timeout;
  return function() {
    var context = this, args = arguments;
    var later = function() {
      timeout = null;
      if (!immediate) func.apply(context, args);
    };
    var callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func.apply(context, args);
  };
}

// 自动保存草稿
function autoSaveDraft() {
  const currentTitle = titleInput.value;
  const currentContent = contentTextarea.value;

  if (currentTitle.trim() !== '' || currentContent.trim() !== '') {
    if (storageAvailable('localStorage')) {
      localStorage.setItem('draft_title', currentTitle);
      localStorage.setItem('draft_content', currentContent);
      
      // 检查是否应该显示通知
      const now = Date.now();
      if (now - lastNotificationTime >= 30000) { // 30秒
        showNotification('草稿保存成功!');
        lastNotificationTime = now;
      }
    } else {
      showNotification('本地存储不可用，草稿未保存。');
    }
  }
}

// 监听内容输入，启动自动保存定时器
contentTextarea.addEventListener('input', () => {
  clearTimeout(autoSaveTimer);
  if (contentTextarea.value.trim() !== '') {
    autoSaveTimer = setTimeout(autoSaveDraft, autoSaveInterval);
  }
});

// 保存草稿的函数
saveDraftButton.addEventListener('click', () => {
  console.log('点击了保存草稿按钮');
  
  // 执行保存操作但不使用 autoSaveDraft
  const currentTitle = titleInput.value;
  const currentContent = contentTextarea.value;

  if (storageAvailable('localStorage')) {
    localStorage.setItem('draft_title', currentTitle);
    localStorage.setItem('draft_content', currentContent);
    showNotification('草稿保存成功!');  // 手动保存时始终显示通知
  } else {
    showNotification('本地存储不可用，草稿未保存。');
  }
});

// 清除草稿的函数
clearDraftButton.addEventListener('click', () => {
  console.log('点击了清除草稿按钮');

  const confirmClear = confirm('您确定要清除草稿吗?');

  if (confirmClear) {
    // 清除本地存储中的草稿
    localStorage.removeItem('draft_title');
    localStorage.removeItem('draft_content');

    // 清空输入框
    titleInput.value = '';
    contentTextarea.value = '';

    hideClearDraftButton();
    showNotification('草稿箱已清除!');
  }
});

// 显示"清除草稿"按钮
function showClearDraftButton() {
  clearDraftButton.style.display = 'inline-block';
}

// 隐藏"清除草稿"按钮
function hideClearDraftButton() {
  clearDraftButton.style.display = 'none';
}

// 显示通知
function showNotification(message) {
  const notificationContent = notificationContainer.querySelector('.custom-notification-content');
  notificationContent.textContent = message;
  notificationContainer.style.display = 'block';

  // 设置定时器，1秒后自动隐藏通知
  setTimeout(() => {
    notificationContainer.style.display = 'none';
  }, 1000);
}