﻿using System;
using Dapper;
using KeLin.ClassManager;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Admin_guestlistWAPdel00 : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string page = "";

        public string INFO = "";

        public string tositeid = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            while (true)
            {
                IsCheckSuperAdmin(userid, userVo.managerlvl, GetUrlQueryString());
                action = GetRequestValue("action");
                id = GetRequestValue("id");
                page = GetRequestValue("page");
                tositeid = GetRequestValue("tositeid");
                bool flag = !(action == "godel");
                int num = 3;
                while (true)
                {
                    switch (num)
                    {
                        case 3:
                            if (true)
                            {
                            }
                            if (!flag)
                            {
                                num = 1;
                                continue;
                            }
                            return;
                        case 2:
                            try
                            {
                                // ✅ 使用DapperHelper安全删除回帖，避免SQL注入
                                DeleteReplyAndUpdateCountSafely();
                                INFO = "OK";
                            }
                            catch (Exception ex)
                            {
                                INFO = ex.ToString();
                            }
                            num = 0;
                            continue;
                        case 1:
                            num = 2;
                            continue;
                        case 0:
                            return;
                    }
                    break;
                }
            }
        }

        /// <summary>
        /// 使用DapperHelper安全删除回帖并更新计数，避免SQL注入
        /// </summary>
        private void DeleteReplyAndUpdateCountSafely()
        {
            string delConnectionString = PubConstant.GetConnectionString(a);

            // 使用事务确保数据一致性
            DapperHelper.ExecuteInTransaction(delConnectionString, (connection, transaction) =>
            {
                // 先更新主帖回复数
                string updateSql = @"UPDATE wap_bbs SET book_re = book_re - 1
                                   WHERE id = (SELECT TOP 1 bookid FROM wap_bbsre WHERE id = @ReplyId)";
                connection.Execute(updateSql, new {
                    ReplyId = DapperHelper.SafeParseLong(id, "回帖ID")
                }, transaction);

                // 再删除回帖
                string deleteSql = "DELETE FROM wap_bbsre WHERE id = @ReplyId";
                connection.Execute(deleteSql, new {
                    ReplyId = DapperHelper.SafeParseLong(id, "回帖ID")
                }, transaction);
            });
        }
    }
}