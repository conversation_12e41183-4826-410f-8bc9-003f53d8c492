
@media screen and (min-width: 1441px) {
:root {
    background-color:#cfd9e7!important;
}
body { 
    box-shadow: none!important;
}
}
:root {
    background-color: rgb(232, 232, 232);
}
body { 
    font-family: SimSun; 
    margin: 0 auto; 
    max-width: 720px; 
    box-shadow: rgba(0, 0, 0, 0.2) 0 2px 1px -1px, rgba(0, 0, 0, 0.14) 0 1px 1px 0, rgba(0, 0, 0, 0.12) 0 1px 3px 0; 
    line-height: 32px; 
    background: #cfd9e7;
}
*,::after,::before{transition:transform .5s cubic-bezier(.5,0,.5,1),opacity,background-color,border-color}
[data-state~=idle]{animation:1s cubic-bezier(.5,0,.5,1) 0s 1 normal both running reset}
[data-state~=idle] .ui-icon{--bg:#E3E6F9;--color:#1abc9c}
[data-state~=idle] .ui-password::before{background-color:var(--color-primary);transform:translateX(-100%)}
[data-state~=idle] .ui-password:focus-within::before{transform:none}
.ui-modal{--color-primary:#1abc9c;--color-error:#E0294C;--color-success:#0DBE65;background-color:#fff;padding:2rem 4rem;border-radius:.5rem;display:flex;flex-direction:column;justify-content:flex-start;align-items:center;box-shadow:rgba(0,0,0,.1) 0 1rem 2rem;position:relative;max-width: 90%;}
.ui-icon{height:3rem;width:3rem;border-radius:50%;margin-bottom:1rem}
.ui-icon::before{content:"";position:absolute;display:block;top:0;left:0;width:100%;height:100%;border-radius:inherit;background:#e5f3ee;will-change:transform}
.ui-icon>.ui-lock{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center}
.ui-icon>.ui-lock::after,.ui-icon>.ui-lock::before{content:"";position:absolute}
.ui-icon>.ui-lock::after{background-color:var(--color);height:25%;width:45%;transform:translateY(50%);border-radius:2px}
.ui-icon>.ui-lock::before{width:30%;height:50%;border-radius:1rem;border:4px solid var(--color)}
.ui-title{font-size:1rem;line-height:2rem;white-space: nowrap;}
.ui-subtitle{font-size:.75rem;height:1rem;margin-bottom:1rem;display:flex;justify-content:center;align-items:center;color:#9a9ca2}
.ui-password{appearance:none;background:0 0;border:none;padding-bottom:2px;margin-bottom:2rem;overflow:hidden}
.ui-password::after,.ui-password::before{content:"";position:absolute;height:2px;width:100%;bottom:0;left:0;z-index:1}
.ui-password::after{background-color:#e8e9f0;z-index:0}
.ui-password-input{appearance:none;background:0 0;border:none;height:2rem;width:14rem}
.ui-password-input:focus{outline:0}
.ui-submit,.ui-reset{cursor:pointer}
.ui-submit{appearance:none;padding:0 1.2rem;height:2rem;border-radius:.5rem;font-size:.75rem;color:#fff;background-color:#1abc9c;border:1px solid #13b292;letter-spacing:2px;}
.ui-submit:active{transform:scale(.9);transition-duration:.2s}
.ui-submit:focus{outline:0}
.ui-submit:hover{background:#16a085}
.ui-reset{appearance:none;background:0 0;border:none;position:absolute;top:0;left:0;padding:.5rem}
.ui-reset .arrow{position:relative;float:left;border:4px solid #fff;border-radius:100%;width:40px;height:40px}
.ui-reset .arrow .arrow-mask{position:relative;top:-33px;left:-33px;border:21px solid transparent}
.ui-reset .arrow .arrow-mask:before{content:"";position:absolute;top:25px;left:25px;width:4px;height:16px;background-color:#e8e9f0;transform:rotate(135deg);transition:all .3s}
.ui-reset .arrow .arrow-mask:after{content:"";position:absolute;top:15px;left:26px;width:4px;height:16px;background-color:#e8e9f0;transform:rotate(-135deg);transition:all .3s}
.ui-reset:hover .arrow-mask:before{background-color:#5db99d}
.ui-reset:hover .arrow-mask:after{background-color:#5db99d}
body{display:flex;justify-content:center;align-items:center;background-color:#cfd9e7;user-select: none;}
body,html{width:100%;height:100%;padding:0;font-size:18px}
*,::after,::before{box-sizing:border-box;position:relative}