using System;
using System.Collections.Generic;
using YaoHuo.Plugin.Template.Models;

namespace YaoHuo.Plugin.BBS.Models
{
    /// <summary>
    /// 账目明细页面数据模型
    /// </summary>
    public class BankListPageModel : BasePageModelWithPagination
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public BankListPageModel()
        {
            PageTitle = "账目明细";
        }

        /// <summary>
        /// 过滤条件模型
        /// </summary>
        public FilterModel Filter { get; set; } = new FilterModel();

        /// <summary>
        /// 账目记录列表
        /// </summary>
        public List<BankLogItemModel> BankLogList { get; set; } = new List<BankLogItemModel>();

        /// <summary>
        /// 是否为管理员
        /// </summary>
        public bool IsAdmin { get; set; }

        /// <summary>
        /// 统计信息模型
        /// </summary>
        public StatisticsModel Statistics { get; set; } = new StatisticsModel();

        /// <summary>
        /// 是否显示统计功能（目前为占位符）
        /// </summary>
        public bool ShowStats { get; set; } = true;
    }

    /// <summary>
    /// 过滤条件模型
    /// </summary>
    public class FilterModel
    {
        /// <summary>
        /// 年份
        /// </summary>
        public string ToYear { get; set; } = DateTime.Now.Year.ToString();

        /// <summary>
        /// 月份
        /// </summary>
        public string ToMonth { get; set; } = DateTime.Now.Month.ToString();

        /// <summary>
        /// 用户ID（管理员专用）
        /// </summary>
        public string Key { get; set; } = "";

        /// <summary>
        /// 搜索类型
        /// </summary>
        public string TypeId { get; set; } = "";

        /// <summary>
        /// 搜索关键字
        /// </summary>
        public string TypeKey { get; set; } = "";

        /// <summary>
        /// 搜索类型选项列表
        /// </summary>
        public List<OptionItem> SearchTypeOptions { get; set; } = new List<OptionItem>
        {
            new OptionItem { Value = "", Text = "全部", Selected = true },
            new OptionItem { Value = "1", Text = "项目名称" },
            new OptionItem { Value = "2", Text = "操作人ID" },
            new OptionItem { Value = "3", Text = "操作人昵称" },
            new OptionItem { Value = "4", Text = "备注" }
            // new OptionItem { Value = "5", Text = "记录ID" }
        };

        /// <summary>
        /// 年份选项列表
        /// </summary>
        public List<OptionItem> YearOptions { get; set; } = new List<OptionItem>();

        /// <summary>
        /// 月份选项列表
        /// </summary>
        public List<OptionItem> MonthOptions { get; set; } = new List<OptionItem>();
    }

    /// <summary>
    /// 单个账目记录的数据模型
    /// </summary>
    public class BankLogItemModel
    {
        /// <summary>
        /// 记录ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 项目名称
        /// </summary>
        public string ActionName { get; set; } = "";

        /// <summary>
        /// 变动金额
        /// </summary>
        public string Money { get; set; } = "";

        /// <summary>
        /// 余额
        /// </summary>
        public string LeftMoney { get; set; } = "";

        /// <summary>
        /// 操作人ID
        /// </summary>
        public string OperaUserId { get; set; } = "";

        /// <summary>
        /// 操作人昵称
        /// </summary>
        public string OperaNickname { get; set; } = "";

        /// <summary>
        /// 添加时间
        /// </summary>
        public DateTime AddTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark { get; set; } = "";

        /// <summary>
        /// 金额是否为正数（用于前端样式判断）
        /// </summary>
        public bool IsPositiveAmount
        {
            get
            {
                if (long.TryParse(Money, out long moneyValue))
                {
                    return moneyValue > 0;
                }
                return !Money.StartsWith("-");
            }
        }

        /// <summary>
        /// 金额是否为负数（用于前端样式判断）
        /// </summary>
        public bool IsNegativeAmount
        {
            get
            {
                if (long.TryParse(Money, out long moneyValue))
                {
                    return moneyValue < 0;
                }
                return Money.StartsWith("-");
            }
        }

        /// <summary>
        /// 格式化的金额显示
        /// </summary>
        public string FormattedMoney
        {
            get
            {
                if (long.TryParse(Money, out long moneyValue))
                {
                    return moneyValue.ToString("N0");
                }
                return Money;
            }
        }

        /// <summary>
        /// 格式化的余额显示
        /// </summary>
        public string FormattedLeftMoney
        {
            get
            {
                if (long.TryParse(LeftMoney, out long leftMoneyValue))
                {
                    return leftMoneyValue.ToString("N0");
                }
                return LeftMoney;
            }
        }

        /// <summary>
        /// 格式化的时间显示
        /// </summary>
        public string FormattedAddTime => AddTime.ToString("yyyy-MM-dd HH:mm");

        /// <summary>
        /// 短格式的时间显示（不显示年份）
        /// </summary>
        public string ShortFormattedAddTime => AddTime.ToString("MM-dd HH:mm");
    }

    /// <summary>
    /// 统计信息模型（目前为占位符）
    /// </summary>
    public class StatisticsModel
    {
        /// <summary>
        /// 本月收入（占位符）
        /// </summary>
        public long MonthlyIncome { get; set; } = 0;

        /// <summary>
        /// 本月支出（占位符）
        /// </summary>
        public long MonthlyExpense { get; set; } = 0;

        /// <summary>
        /// 本月净收入（占位符）
        /// </summary>
        public long MonthlyNet => MonthlyIncome - MonthlyExpense;

        /// <summary>
        /// 格式化的本月收入显示
        /// </summary>
        public string FormattedMonthlyIncome => MonthlyIncome.ToString("N0");

        /// <summary>
        /// 格式化的本月支出显示
        /// </summary>
        public string FormattedMonthlyExpense => MonthlyExpense.ToString("N0");

        /// <summary>
        /// 格式化的本月净收入显示
        /// </summary>
        public string FormattedMonthlyNet => MonthlyNet.ToString("N0");
    }
} 