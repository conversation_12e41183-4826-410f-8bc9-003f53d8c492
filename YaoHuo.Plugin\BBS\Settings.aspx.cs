using KeLin.ClassManager;
using System;
using System.Collections.Generic;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.BBS
{
    /// <summary>
    /// 论坛设置页面
    /// </summary>
    public partial class Settings : MyPageWap
    {
        // 用户当前设置
        public bool IsNewReplyUIEnabled;

        // 数据库实例名
        private string a = PubConstant.GetAppString("InstanceName");

        // 用于记录用户最后保存时间的静态字典
        private static Dictionary<long, DateTime> lastSubmitTimes = new Dictionary<long, DateTime>();
        private const int MinSubmitIntervalSeconds = 3; // 设置3秒的提交间隔

        protected void Page_Load(object sender, EventArgs e)
        {
            // 会员可见判断
            if (!IsCheckManagerLvl("|00|01|02|03|04|", ""))
            {
                Response.Redirect("/");
                return;
            }

            // 初始化默认值
            IsNewReplyUIEnabled = true;

            // 获取用户当前设置
            if (userid != "0")
            {
                long userIdLong;
                if (long.TryParse(userid, out userIdLong))
                {
                    try
                    {
                        var repo = new UserPreferencesRepository(a);
                        IsNewReplyUIEnabled = repo.GetNewReplyUIEnabled(userIdLong);
                    }
                    catch (Exception)
                    {
                        // 异常处理，继续使用默认值
                    }
                }
            }

            // 处理保存设置的POST请求
            if (Request.HttpMethod == "POST" && !string.IsNullOrEmpty(Request.Form["action"]))
            {
                string action = Request.Form["action"];

                if (action == "saveSettings")
                {
                    bool newReplyUIEnabled;
                    bool.TryParse(Request.Form["newReplyUIEnabled"], out newReplyUIEnabled);
                    string result = SaveUserSettings(newReplyUIEnabled);

                    // 返回JSON响应
                    Response.Clear();
                    Response.ContentType = "application/json";
                    Response.Write("{ \"d\": \"" + result + "\" }");
                    Response.End();
                }
            }
        }

        /// <summary>
        /// 检查是否为iframe模式
        /// </summary>
        /// <returns>true表示在iframe中加载</returns>
        protected bool IsIframeMode()
        {
            return Request.QueryString["iframe"] != null;
        }

        /// <summary>
        /// 保存用户设置 - 实例方法替代静态WebMethod
        /// </summary>
        /// <param name="newReplyUIEnabled">是否启用新版回帖UI</param>
        /// <returns>操作结果</returns>
        public string SaveUserSettings(bool newReplyUIEnabled)
        {
            try
            {
                // 验证用户是否登录
                long userIdLong;
                if (!long.TryParse(userid, out userIdLong) || userIdLong <= 0)
                {
                    return "用户未登录";
                }

                // 检查提交频率
                lock (lastSubmitTimes)
                {
                    if (lastSubmitTimes.ContainsKey(userIdLong))
                    {
                        TimeSpan timeSinceLastSubmit = DateTime.Now - lastSubmitTimes[userIdLong];
                        if (timeSinceLastSubmit.TotalSeconds < MinSubmitIntervalSeconds)
                        {
                            return "请求过于频繁，请稍后再试";
                        }
                    }
                    lastSubmitTimes[userIdLong] = DateTime.Now;
                }

                // 创建仓储实例
                var repo = new UserPreferencesRepository(a);

                // 首先获取当前设置，检查是否有变化
                bool currentSetting = repo.GetNewReplyUIEnabled(userIdLong);

                // 如果设置没有变化，则不执行保存操作
                if (currentSetting == newReplyUIEnabled)
                {
                    return "success";
                }

                bool result = repo.SaveNewReplyUIEnabled(userIdLong, newReplyUIEnabled);

                return result ? "success" : "failed";
            }
            catch (Exception ex)
            {
                return "error: " + ex.Message;
            }
        }

        /// <summary>
        /// 获取用户ID的JavaScript变量
        /// </summary>
        public string GetUserIdJsVar()
        {
            long userIdLong = 0;
            if (userid != "0")
            {
                long.TryParse(userid, out userIdLong);
            }
            return "<script>var currentUserId = " + userIdLong.ToString() + ";</script>";
        }

        /// <summary>
        /// 获取当前设置状态的JavaScript变量
        /// </summary>
        public string GetSettingsJsVars()
        {
            return "<script>var serverSideNewReplyUIEnabled = " + IsNewReplyUIEnabled.ToString().ToLower() + ";</script>";
        }
    }
}