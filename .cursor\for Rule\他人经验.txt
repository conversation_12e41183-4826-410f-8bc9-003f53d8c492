我的流程是：
1.一个想法
2.和ai进行讨论，有个大概的思路，做成什么样子
3.细化方案，产出详细方案、prd、todolist等各类文档支撑开发任务。详细信息见https://github.com/bmadcode/BMAD-METHOD博主给出了解决方案，通过自定义gemini的gem（提示词定制ai），将想法细化为可执行的方案，后续直接将最终输出的文档转入cursor，让其开发
4.如果有关于前端页面的内容，会在v0里实现前端页面效果，下载成文件
5.在cursor内转入3中生成的文档和4中产出的前端代码文件，创建项目基本环境（rules、mdc文件等）准备开发
6.让cursor熟悉项目情况，一步步完成开发任务。基本如果3执行的好，在cursor内就很顺利。中间有临时的修改，就让cursor执行（先让他给方案，然后写入todolist，最后修改代码执行任务），更新原有的文档内容
注意事项及常见问题
使用cursor时，最常见的问题就是cursor丢失、记不住上下文；执行任务时不遵守已定/写入rules的内容、修改功能时经常影响到其他正常功能，乱创建文件导致项目结构越来越混乱等。目前我的解决方案如下：
1.最好先用gemini（充足上下文）产出可执行的todolist，这样cursor只需要专注执行，开发中会省很多时间和麻烦
2.每个项目开始前，先准备好这个项目需要的rules和各类mdc文件，作为项目全局指导。如web项目就用开发web的rules，然后生成各类mdc文件，工作流程、前端规范、代码规范、文件规范等，详细见https://cursor.directory/rules（官方提供的热门rules）和https://github.com/bmadcode/cursor-custom-agents-rules-generator可生成大概的项目rules和mdc文件，后续随着项目开发，也可以随时更新
3.自定义cursor的agent，详细见https://github.com/vanzan01/cursor-memory-bank。通过自定义的各类agent，在各类场景下灵活切换，执行各类任务。如需要讨论、设计方案、理解项目等需要上下文理解时，用plan模式的gemini；需要执行开发任务修改代码时，用bulid模式的claude
4.让cursor执行任务时，最好每次他给出答案都看一下这次对话完整的文字信息和他修改的代码文件，知道他执行的任务是怎么做的，做了什么。这样当cursor在一个任务上陷入循环重复修改（往往是一个很小的问题）时，给他有用的建议和思路。也可以避免他乱修改/创建文件导致项目混乱成为屎山
5.当一个问题出现了重复修改也解决不了，或者一个对话内上下文太长，执行的任务太多时（尽量一个大任务一个对话窗口），ctrl+k创建新的对话窗口，@之前的一个历史对话避免失去记忆

最后推荐一下二次开发的https://github.com/pawaovo/interactive-feedback-mcp，启用这个mcp服务后，能以更大的ui窗口和cursor进行对话，而且遇到疑问点时会主动进行提问让你指导，可以一定程度上避免cursor自由发挥出现问题。