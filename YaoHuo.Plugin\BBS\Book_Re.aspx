﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_Re.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Book_Re" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%@ Import Namespace="System.Collections.Generic" %>
<%@ Import Namespace="KeLin.ClassManager" %>
<%
    // 验证帖子是否存在
    if (this.bookVo == null)
    {
        Response.Write("<div class='tip'>帖子不存在或参数错误！</div>");
        Response.End();
    }
    // 处理回复成功后的跳转设置
    if (this.INFO == "OK")
    {
        if ("1".Equals(WapTool.GetArryString(classVo.smallimg, '|', 1)))
        {
            wmlVo.timer = "0";
            wmlVo.strUrl = "bbs/book_re.aspx?classid=" + this.classid + "&amp;page=" + this.CurrentPage + "&amp;id=" + this.id + "";
        }
        else
        {
            wmlVo.timer = "0";
            wmlVo.strUrl = "bbs/book_re.aspx?classid=" + this.classid + "&amp;page=" + this.CurrentPage + "&amp;id=" + this.id + "";
        }
    }
    // 输出页面头部
    Response.Write(WapTool.showTop(this.GetLang("查看回复|查看回複|View Reply"), wmlVo));
    string isWebHtml = this.ShowWEB_view(this.classid);

    // 引入页面样式文件
    strhtml.Append("<link href=\"/NetCSS/CSS/BBS/bookre-emoji.css\" rel=\"stylesheet\" type=\"text/css\"/>");
    strhtml.Append("<link href=\"/NetCSS/BookView/NewReply.css\" rel=\"stylesheet\" type=\"text/css\"/>");
    strhtml.Append("<!--web-->");
    strhtml.Append(ERROR);

    // 处理过滤/删除提示信息
    // 显示楼层过滤/删除状态提示
    if (ViewState["FilteredOutFloor"] != null)
    {
        string reason = (ViewState["FilteredOutFloorReason"] ?? "").ToString();
        string msg = "";
        // 根据不同的过滤原因显示相应提示信息
        if (reason == "userdeleted")
            msg = "您查看的楼层（" + ViewState["FilteredOutFloor"] + "楼）已被用户删除。";
        else if (reason == "admindeleted")
            msg = "您查看的楼层（" + ViewState["FilteredOutFloor"] + "楼）已被管理员删除。";
        else if (reason == "pending")
            msg = "您查看的楼层（" + ViewState["FilteredOutFloor"] + "楼）正在审核中。";
        else if (reason == "filtered")
            msg = "您查看的楼层（" + ViewState["FilteredOutFloor"] + "楼）因过滤设置被隐藏。";
        else if (reason == "notfound")
            msg = "您查看的楼层不存在。";
        else
            msg = "您查看的楼层不可见。";
        strhtml.Append("<div class='tip' style='background:#fffbe6;color:#b36a00;border:1px solid #ffe58f;padding:8px 12px;margin-bottom:10px;'>" + msg + "</div>");
    }
    // 显示回复成功信息
    if (this.INFO == "OK")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append("<b>回复成功！</b> ");
        if (siteVo.isCheck == 1)
        {
            strhtml.Append("<b>审核后显示！</b> ");
        }
        strhtml.Append("获得" + WapTool.GetSiteMoneyName(siteVo.sitemoneyname, this.lang) + ":" + allMoney + "，获得经验:" + getexpr + "<br/> ");
        strhtml.Append("跳转中...<a href=\"" + this.http_start + wmlVo.strUrl + "\"" + "\">返回</a><br/>");
        strhtml.Append("</div>");
    }
    // 显示各种错误提示信息
    else if (this.INFO == "NULL")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append("<b>回复内容最少" + contentmax + "字！</b><br/>");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "TITLEMAX")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append("<b>内容最大" + this.content_max + "字。</b></div>");
    }
    else if (this.INFO == "ERR_FORMAT")
    {
        strhtml.Append("<div class=\"tip\"><b>取到非法值: $$( 请更换手机浏览器或重新编辑！</b><br/></div>");
    }
    else if (this.INFO == "REPEAT")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append("<b>请不要发重复内容！</b><br/>");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "ERROR_Secret")
    {
        strhtml.Append("<div class=\"tip\"><b>暗号错误，如果忘记联系站长索取！</b><br/></div>");
    }
    else if (this.INFO == "WAITING")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append("<b>请再过" + this.KL_CheckIPTime + "秒后操作！</b><br/>");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "MAX")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append("<b>今天你已超过回帖限制：" + this.KL_CheckBBSreCount + " 个回帖了，请明天再来！</b><br/>");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "LOCK")
    {
        strhtml.Append("<div class=\"tip\">");
        strhtml.Append("<b>抱歉，您已经被加入黑名单，请注意发帖规则！</b><br/>");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "NOMONEY")
    {
        strhtml.Append("<div class=\"tip\"><b>你当前的只有:" + userVo.money + "个，发帖需要扣掉：" + getmoney2 + "个</b></div>");
    }
    // 显示主要内容区域（当没有错误信息时）
    if (this.INFO == "")
    {
        // 显示排序和导航按钮
        strhtml.Append("<div class=\"btBox\"><div class=\"bt3\">");
        if (this.ot == "1")
        {
            strhtml.Append("<a href=\"" + this.http_start + "bbs/book_re.aspx?action=class&amp;id=" + this.id + "&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;page=" + this.CurrentPage + "&amp;lpage=" + this.lpage + "&amp;ot=0&amp;go=" + this.r + "\">最新回复</a> ");
        }
        else
        {
            strhtml.Append("<a href=\"" + this.http_start + "bbs/book_re.aspx?action=class&amp;id=" + this.id + "&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;page=" + this.CurrentPage + "&amp;lpage=" + this.lpage + "&amp;ot=1&amp;go=" + this.r + "\">最早回复</a> ");
        }
        strhtml.Append("<a href=\"" + this.http_start + "bbs-" + id + ".html\">返回主题</a>");
        strhtml.Append("<a href=\"" + this.http_start + "bbs/book_re.aspx?action=class&amp;id=" + this.id + "&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;page=" + this.CurrentPage + "&amp;lpage=" + this.lpage + "&amp;ot=&amp;mainuserid=" + bookVo.book_pub + "&amp;go=" + this.r + "\">楼主回复</a>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        // 显示回复表单区域
        strhtml.Append("<div class=\"viewContent\">");
        if (bookVo.islock == 0)
        {
            strhtml.Append("<form name=\"f\" action=\"" + http_start + "bbs/book_re.aspx\" method=\"post\">");
            // 显示回复目标楼层信息
            if (this.reply != "")
            {
                strhtml.Append("<span style=\"margin-right: -3px;\" class='replyother'><b>回复" + this.reply + "楼</b>");
                strhtml.Append("<select style=\"display: none;\" name=\"sendmsg2\">");
                strhtml.Append("<option value=\"1\">通知对方</option>");
                strhtml.Append("<option value=\"0\">不予通知</option>");
                strhtml.Append("</select></span>");
            }
            // 显示表情选择和输入框
            strhtml.Append("<span class='newselect'>");
            strhtml.Append("<ul id='faceselect' class='ulselect'>");
            strhtml.Append("<li>");
            strhtml.Append("<input class='select_close' type='radio' name='face' id='emotion-close' value='' />");
            strhtml.Append("<span class='select_label select_label-placeholder'>表情</span>");
            strhtml.Append("</li>");
            strhtml.Append("<li class='select_items'>");
            strhtml.Append("<input class='select_expand' type='radio' name='face' id='emotion-opener' />");
            strhtml.Append("<label class='select_closeLabel' for='emotion-close'></label>");
            strhtml.Append("<ul class='select_options'>");
            for (int i = 0; (facelistImg != null && i < facelistImg.Length); i++)
            {
                strhtml.Append("<li class='select_option'>");
                strhtml.Append("<input class='select_input' type='radio' name='face' id='emotion-" + i + "' value='" + facelistImg[i] + "' />");
                strhtml.Append("<label class='select_label' for='emotion-" + i + "'>" + facelist[i] + "</label>");
                strhtml.Append("</li>");
            }
            strhtml.Append("</ul>");
            strhtml.Append("<label class='select_expandLabel' for='emotion-opener'></label>");
            strhtml.Append("</li>");
            strhtml.Append("</ul>");
            strhtml.Append("<div id=\"emoticon-container\" class=\"emoticon-popup\" style=\"display: none;\"></div>");
            strhtml.Append("<span class='tongzhi'><input class=\"inp-cbx\" id=\"cbx\" type=\"checkbox\" name=\"sendmsg\" value=\"1\" style=\"display: none\"/><label class=\"cbx\" for=\"cbx\"><span><svg width=\"12px\" height=\"10px\" viewbox=\"0 0 12 10\"><polyline points=\"1.5 6 4.5 9 10.5 1\"></polyline></svg></span><span>通知楼主</span></label></span>");
            strhtml.Append("</span>");
            strhtml.Append("<style>.sticky b {margin-left: 7px; }</style>");
            strhtml.Append("<script> function adjustTextareaHeight(textarea) { if (textarea.scrollHeight > textarea.offsetHeight) { textarea.style.height = textarea.scrollHeight + 'px'; } } </script>");
            strhtml.Append("<div class='centered-container'>");
            strhtml.Append("<textarea class='retextarea' oninput='adjustTextareaHeight(this)' name='content' minlength='1' maxlength='5000' required='required' placeholder='请不要乱打字回复，以免被加黑。' rows='5' class='KL_textarea' style='width:98.6%;margin-bottom: 5px;'>" + this.reShowInfo + "</textarea><br/>");
            strhtml.Append("</div>");
            if (this.isNeedSecret == true)
            {
                //strhtml.Append("本版暗号:<input type=\"text\" name=\"secret\" value=\"\" size=\"10\" /><br/>");
            }
            strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"add\"/>");
            strhtml.Append("<input type=\"hidden\" name=\"id\" value=\"" + id + "\"/>");
            strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
            strhtml.Append("<input type=\"hidden\" name=\"lpage\" value=\"" + lpage + "\"/>");
            strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
            strhtml.Append("<input type=\"hidden\" name=\"reply\" value=\"" + reply + "\"/>");
            strhtml.Append("<input type=\"hidden\" name=\"touserid\" value=\"" + this.GetRequestValue("touserid") + "\"/>");

            // 🔧 修复：在表单内部添加验证码令牌字段（仅在需要验证码时）
            if (ShouldShowCaptchaForFreeMoneyPost())
            {
                strhtml.Append("<input type=\"hidden\" id=\"gocaptcha-token\" name=\"gocaptchaToken\" value=\"\" />");
            }

            strhtml.Append("<span class='kuaisuhuifu'><input type=\"submit\" name=\"g\" class=\"btn\" value=\"发表回复\"/></span>");
            strhtml.Append(" <a href=\"" + this.http_start + "bbs/book_re_addfile.aspx?action=class&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;id=" + this.id + "&amp;lpage=" + this.lpage + "\" style=\"font-size:14px;\">" + this.GetLang("文件回帖|文件回帖|upload file") + "</a>");
            strhtml.Append("</form>");
        }
        // 显示回复列表
        strhtml.Append("<!--listS-->");
        strhtml.Append("<div class=\"recontent\">");
        for (int i = 0; (listVo != null && i < listVo.Count); i++)
        {
            // 获取当前回帖的真实原始楼层号
            int currentReplyOriginalFloor = 0; // 默认值，如果未找到映射
            if (this.ReplyIdToOriginalFloorMap != null && this.ReplyIdToOriginalFloorMap.ContainsKey(listVo[i].id))
            {
                currentReplyOriginalFloor = this.ReplyIdToOriginalFloorMap[listVo[i].id];
            }

            // 为每个楼层生成唯一ID，奇偶行使用不同样式
            if (i % 2 == 0)
            {
                strhtml.Append("<div class=\"list-reply line1\" id=\"floor-" + currentReplyOriginalFloor + "\" data-floor=\"" + currentReplyOriginalFloor + "\">");
            }
            else
            {
                strhtml.Append("<div class=\"list-reply line2\" id=\"floor-" + currentReplyOriginalFloor + "\" data-floor=\"" + currentReplyOriginalFloor + "\">");
            }

            // 显示楼层号信息（顶楼或普通楼层）
            if (listVo[i].book_top == 1 && this.CurrentPage == 1) // 只在第一页显示"顶楼"标识
            {
                strhtml.Append(string.Format(HtmlTemplates.TopFloor, CssClasses.FloorNumber0, currentReplyOriginalFloor));
            }
            else
            {
                strhtml.Append(string.Format(HtmlTemplates.FloorNumber, CssClasses.FloorNumber, currentReplyOriginalFloor));
            }

            // 显示得金信息
            if (listVo[i].myGetMoney > 0)
            {
                strhtml.Append(string.Format(HtmlTemplates.MoneyReward, listVo[i].myGetMoney));
            }

            // 显示回复按钮（只对他人的回复显示）
            if (this.userid != listVo[i].userid.ToString())
            {
                strhtml.Append("[<a class=\"" + CssClasses.ReplyIcon + "\" href=\"" + this.http_start + "bbs/book_re.aspx?siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;page=" + this.CurrentPage + "&amp;reply=" + currentReplyOriginalFloor + "&amp;id=" + this.id + "&amp;touserid=" + listVo[i].userid + "&amp;ot=" + this.ot + "\">回</a>]");
            }
            // 自己的回复不显示[回]按钮，自己回复自己不太合理

            // 显示楼主赏分按钮（仅楼主可见，且有剩余赏分）
            if (this.userid == bookVo.book_pub && bookVo.sendMoney > 0 && bookVo.sendMoney != bookVo.hasMoney && bookVo.book_pub != listVo[i].userid.ToString())
            {
                strhtml.Append("<span class=\"" + CssClasses.UserRemanage + "\">[<a class=\"giveicon\" href=\"" + this.http_start + "bbs/SendMoney.aspx?action=sendmoney&amp;classid=" + classid + "&amp;id=" + id + "&amp;reid=" + listVo[i].id + "&amp;siteid=" + this.siteid + "\">赏分</a>]</span>");
            }

            // 管理员操作按钮区域
            if (this.IsCheckManagerLvl(AdminLevels, classVo.adminusername))
            {
                strhtml.Append("<span class=\"" + CssClasses.AdminRemanage + "\">");
                // 根据是否为回复作者显示不同的删除链接
                if (this.userid == listVo[i].userid.ToString())
                {
                    strhtml.Append("[<a class='" + CssClasses.UserRemanage + " " + CssClasses.DeleteMyFloor + "' href=\"" + this.http_start + "bbs/Book_re_del.aspx?action=go&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;page=" + this.CurrentPage + "&amp;reid=" + listVo[i].id + "&amp;id=" + this.id + "&amp;ot=" + this.ot + "\">删</a>]");
                    strhtml.Append("<span style='display:none' >[<a class='floordeladmin drop-down' href=\"" + this.http_start + "bbs/Book_re_del.aspx?action=go&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;page=" + this.CurrentPage + "&amp;reid=" + listVo[i].id + "&amp;id=" + this.id + "&amp;ot=" + this.ot + "\">删</a>]</span>");
                }
                else
                {
                    strhtml.Append("[<a class='floordeladmin drop-down' href=\"" + this.http_start + "bbs/Book_re_del.aspx?action=go&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;page=" + this.CurrentPage + "&amp;reid=" + listVo[i].id + "&amp;id=" + this.id + "&amp;ot=" + this.ot + "\">删</a>]");
                }
                // 管理员其他操作链接
                strhtml.Append("[<a class='floorgive drop-down' href=\"" + this.http_start + "bbs/SendMoney_free.aspx?action=sendmoney&amp;classid=" + classid + "&amp;id=" + id + "&amp;reid=" + listVo[i].id + "&amp;touserid=" + listVo[i].userid + "&amp;siteid=" + this.siteid + "\">送</a>]");
                strhtml.Append("[<a class='floorchange drop-down' href=\"" + this.http_start + "bbs/Book_re_mod.aspx?action=go&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;page=" + this.CurrentPage + "&amp;reid=" + listVo[i].id + "&amp;id=" + this.id + "&amp;ot=" + this.ot + "\">审</a>]");

                // 顶楼/消顶操作
                if (listVo[i].book_top == 1)
                {
                    strhtml.Append("[<a class='floortop drop-down' href=\"" + this.http_start + "bbs/Book_re_top.aspx?action=go&amp;tops=0&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;page=" + this.CurrentPage + "&amp;reid=" + listVo[i].id + "&amp;id=" + this.id + "&amp;ot=" + this.ot + "\">消顶</a>]</span>");
                }
                else
                {
                    strhtml.Append("[<a class='floortop drop-down' href=\"" + this.http_start + "bbs/Book_re_top.aspx?action=go&amp;tops=1&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;page=" + this.CurrentPage + "&amp;reid=" + listVo[i].id + "&amp;id=" + this.id + "&amp;ot=" + this.ot + "\">顶</a>]</span>");
                }
            }
            // 普通用户删除自己的回复
            else if (this.userid == listVo[i].userid.ToString())
            {
                strhtml.Append("<span class=\"" + CssClasses.UserRemanage + "\">[<a class='" + CssClasses.DeleteMyFloor + "' href=\"" + this.http_start + "bbs/Book_re_del.aspx?action=go&amp;siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;lpage=" + this.lpage + "&amp;page=" + this.CurrentPage + "&amp;reid=" + listVo[i].id + "&amp;id=" + this.id + "&amp;ot=" + this.ot + "\">删</a>]</span>");
            }
            // 显示回复内容区域
            if (listVo[i].reply != 0)
            {
                // 生成绝对路径的跳楼层链接，避免/bbs/bbs/bug
                strhtml.Append("<span class=\"reother\">回复<a href=\"/bbs/book_re.aspx?classid=" + this.classid + "&id=" + this.id + "&tofloor=" + listVo[i].reply + "&page=" + this.CurrentPage + "#floor-" + listVo[i].reply + "\">" + listVo[i].reply + "楼</a></span><span class=\"recolon\">:</span>");
            }
            strhtml.Append("<span class=\"retext\">");
            strhtml.Append(listVo[i].content);
            // 显示附件链接
            if (listVo[i].isdown > 0)
            {
                strhtml.Append("{<a href=\"" + this.http_start + "bbs/book_re_addfileshow.aspx?siteid=" + this.siteid + "&amp;classid=" + this.classid + "&amp;id=" + this.id + "&amp;reid=" + listVo[i].id + "&amp;lpage=" + this.lpage + "\">查看" + listVo[i].isdown + "个附件</a>}");
            }
            // 显示用户信息和时间
            strhtml.Append("</span><br/><span class=\"renick\"><a href=\"" + this.http_start + "bbs/userinfo.aspx?touserid=" + listVo[i].userid + "\">" + YaoHuo.Plugin.Tool.BBSHelper.FormatColoredNickname(listVo[i].userid, listVo[i].nickname, this.userListVo_IDName, this.lang, this.ver) + "</a></span>(<a class=\"reidlink\" href=\"" + this.http_start + "bbs/book_re.aspx?action=class&id=" + this.id + "&classid=" + this.classid + "&mainuserid=" + listVo[i].userid + "\"><span class=\"renickid\">" + listVo[i].userid + "</span>)</a></span><span class=\"retime\">" + string.Format("{0:MM-dd HH:mm}", listVo[i].redate) + "</span></div>");
        }
        // 检查是否有回复数据
        if (listVo == null || listVo.Count == 0)
        {
            // 暂时不显示"暂无回复记录"提示
            //strhtml.Append("<div class=\"tip\">暂无回复记录！</div>");
        }
        strhtml.Append("</div>");
        strhtml.Append("<!--listE-->");
        strhtml.Append(linkURL);
    }

    // 处理Web版本显示
    if (isWebHtml != "")
    {
        string strhtml_list = strhtml.ToString();
        int s = strhtml_list.IndexOf("<!--web-->");
        strhtml_list = strhtml_list.Substring(s, strhtml_list.Length - s);
        Response.Clear();
        Response.Write(WapTool.ToWML(isWebHtml.Replace("[view]", strhtml_list), wmlVo));
        Response.End();
    }

    // 显示底部导航按钮
    strhtml.Append("<div class=\"btBox\"><div class=\"bt2\">");
    strhtml.Append("<a href=\"" + this.http_start + "bbs-" + id + ".html\">返回主题</a>");
    strhtml.Append("<a href=\"" + this.http_start + "bbs/book_list.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + lpage + "" + "\">返回列表</a> ");
    strhtml.Append("</div></div>");

    // 引入页面脚本文件

    // 🔧 新增：派币帖验证码资源条件性加载
    if (ShouldShowCaptchaForFreeMoneyPost())
    {
        // 设置全局变量
        strhtml.Append("<script>window.REQUIRES_CAPTCHA = true;</script>");
        strhtml.Append("<script>window.CURRENT_USER_ID = " + base.userid + ";</script>");
        strhtml.Append("<script>window.BOOK_FREE_MONEY = " + bookVo.freeMoney + ";</script>");

        // 添加验证码模态框HTML结构
        strhtml.Append("<div id='gocaptcha-wrap' style='width: 100%; margin: 0 auto;'></div>");

        // 加载专用验证码模块
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BBS/FreeMoneyPostCaptcha.js?v2.2.2\"></script>");

        // 加载GoCaptcha资源（复用现有）
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/CSS/Login/Gocaptcha/gocaptcha-init.js?v1.0.2\"></script>");
        strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/CSS/Login/Gocaptcha/gocaptcha.global.js?v1.0.2\"></script>");
        strhtml.Append("<link href=\"/NetCSS/CSS/Login/Gocaptcha/gocaptcha.global.css?v1.0.2\" rel=\"stylesheet\" />");
        strhtml.Append("<link href=\"/NetCSS/CSS/Login/Gocaptcha/gocaptcha-modal.css?v1.0.2\" rel=\"stylesheet\" />");

        // 设置 Turnstile Site Key 全局变量，供备用验证码使用
        strhtml.Append("<script>window.TURNSTILE_SITE_KEY = '" + PubConstant.GetAppString("CloudflareTurnstileSiteKey") + "';</script>");
    }
    else
    {
        // 不需要验证码时设置标识
        strhtml.Append("<script>window.REQUIRES_CAPTCHA = false;</script>");
    }

    // 引入共享脚本文件
    strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/Shared/ReplyForm.js?v1.0.0\"></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/Shared/NewReplyUI.js?v1.0.0\"></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/Shared/QuickReplyAjax.js?v1.0.3\"></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/Shared/DomHelpers.js?v1.0.0\" defer></script>");

    // 引入功能脚本文件
    strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/HyperLink.js\" defer></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookView/Emoji.js\" defer></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookView/AtUserID.js\" defer></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookView/ImageStyle.js\" defer></script>");
    strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookRe/BookReScript.js?v1.0.3\"></script>");

    // 添加服务器端JavaScript变量配置
    strhtml.Append(this.GetNewReplyUIJsVar());

    // 输出最终页面内容
    Response.Write(WapTool.ToWML(strhtml.ToString(), wmlVo));
    Response.Write(WapTool.showDown(wmlVo));
%>