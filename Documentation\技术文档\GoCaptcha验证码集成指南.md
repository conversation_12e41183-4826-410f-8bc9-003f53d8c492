# GoCaptcha 验证码集成指南

## 项目概述

本文档记录了在 ASP.NET Web Forms 项目中集成 GoCaptcha Service 验证码系统的完整过程，包括技术架构、配置要点、问题解决方案以及验证方式切换指南。

## 技术架构

### 整体架构图
```
前端 (JavaScript) 
    ↓ HTTP 请求
C# 代理服务 (GoCaptchaProxy.ashx) 
    ↓ 转发请求
GoCaptcha Service (go-captcha-service-windows-amd64.exe)
    ↓ 返回验证数据
后端验证 (WapLogin.aspx.cs)
```

### 核心组件

1. **GoCaptcha Service**：独立的验证码生成和验证服务
   - 监听端口：8080 (HTTP)、50051 (gRPC)
   - 配置文件：`gocaptcha.json`、`config.json`

2. **C# HTTP 代理**：`GoCaptchaProxy.ashx`
   - 负责转发前端请求到 GoCaptcha Service
   - 处理跨域问题和请求路由

3. **前端集成**：`gocaptcha-init.js`
   - 初始化验证码组件
   - 处理用户交互和数据提交
   - **弹窗模式**：点击登录时显示验证码弹窗

4. **后端验证**：`WapLogin.aspx.cs`
   - 验证前端提交的验证码 token

## 最终实现：优化后的弹窗式滑动验证码

### 弹窗模式特性（v1.3）

#### 用户体验流程
1. 用户输入账号密码，点击"登录"按钮
2. 系统弹出验证码遮罩窗口（居中显示）
3. 用户完成滑动验证
4. **验证成功** → 显示"✓ 验证成功，正在登录..."，1.5秒后自动提交表单
5. **验证失败** → 在验证码图片区域底部显示红色"验证错误，请重试"提示，同时整个验证码组件左右抖动，1.5秒后自动刷新验证码
6. 如果账号密码正确且验证成功，直接完成登录

#### 弹窗设计原则
- **极简设计**：移除多余的关闭按钮，使用组件内置的关闭功能
- **优雅的错误反馈**：验证失败时在图片底部显示红色半透明提示，配合抖动动画
- **单一遮罩层**：修复重试时遮罩层叠加问题
- **流畅交互**：支持ESC键和点击遮罩关闭弹窗

### 验证失败反馈机制

#### 错误提示设计
- **位置**：验证码图片区域（`.gc-body`）底部
- **样式**：红色半透明背景（rgba(220, 53, 69, 0.9)），白色文字
- **内容**："验证错误，请重试"
- **动画**：从底部滑入效果，1.5秒后滑出

#### 抖动动画效果
- **作用对象**：整个GoCaptcha组件（`.go-captcha`）
- **动画类型**：左右抖动，8px幅度
- **持续时间**：0.6秒
- **触发时机**：与错误提示同时显示

#### CSS实现
```css
/* 隐藏原始容器，在弹窗中显示 */
#gocaptcha-wrap {
    display: none !important;
}

.gocaptcha-modal-wrapper #gocaptcha-wrap {
    display: block !important;
    width: auto !important;
    margin: 0 !important;
}

/* 遮罩层 - 单一实例 */
.gocaptcha-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    display: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    align-items: center;
    justify-content: center;
}

/* 验证码容器 - 最小化包装 */
.gocaptcha-modal-wrapper {
    position: relative;
    transform: scale(0.9);
    transition: transform 0.3s ease;
    max-width: 90vw;
    max-height: 90vh;
}

/* 为GoCaptcha组件添加弹窗效果 */
.gocaptcha-modal-wrapper .go-captcha {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4) !important;
    overflow: hidden !important;
    border: none !important;
}

/* 验证失败错误提示覆盖层 */
.gocaptcha-error-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(220, 53, 69, 0.9);
    color: white;
    text-align: center;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    z-index: 1000;
    opacity: 0;
    transform: translateY(100%);
    transition: all 0.3s ease;
    pointer-events: none;
}

.gocaptcha-error-overlay.show {
    opacity: 1;
    transform: translateY(0);
}

/* 验证失败抖动动画 */
@keyframes gocaptcha-shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-8px); }
    20%, 40%, 60%, 80% { transform: translateX(8px); }
}

.gocaptcha-shake-animation {
    animation: gocaptcha-shake 0.6s ease-in-out;
}

/* 确保gc-body容器支持绝对定位的子元素 */
.gocaptcha-modal-wrapper .gc-body {
    position: relative !important;
}
```

### 配置文件设置

#### gocaptcha.json 关键配置
```json
{
  "builder": {
    "slide_config_maps": {
      "slide-default": {
        "version": "0.0.1",
        "master": {
          "image_size": { "width": 300, "height": 220 },
          "image_alpha": 1
        },
        "thumb": {
          "range_graph_size": { "min": 60, "max": 70 },
          "range_graph_angles": [
            { "min": 20, "max": 35 }
          ],
          "generate_graph_number": 1,
          "enable_graph_vertical_random": false,
          "range_dead_zone_directions": ["left", "right"]
        }
      }
    }
  }
}
```

#### Web.config 配置
```xml
<appSettings>
  <add key="CaptchaProvider" value="gocaptcha" />
  <add key="GoCaptchaEnabled" value="1" />
  <add key="GoCaptchaServiceUrl" value="http://localhost:8080" />
  <add key="GoCaptchaApiKey" value="" />
</appSettings>
```

### 前端实现要点

#### 优化后的弹窗管理核心代码
```javascript
// 创建弹窗（确保单一实例）
function createModal() {
    if (modalOverlay) return; // 避免重复创建
    
    modalOverlay = document.createElement('div');
    modalOverlay.id = 'gocaptcha-modal-overlay';
    modalOverlay.className = 'gocaptcha-modal-overlay';
    
    modalWrapper = document.createElement('div');
    modalWrapper.className = 'gocaptcha-modal-wrapper';
    
    modalOverlay.appendChild(modalWrapper);
    document.body.appendChild(modalOverlay);
    
    // 支持点击遮罩和ESC键关闭
    modalOverlay.addEventListener('click', function(event) {
        if (event.target === modalOverlay) {
            hideModal();
        }
    });
    
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && modalOverlay.classList.contains('show')) {
            hideModal();
        }
    });
}

// 显示验证失败错误提示
function showCaptchaError() {
    const gcBody = modalWrapper.querySelector('.gc-body');
    const goCaptcha = modalWrapper.querySelector('.go-captcha');
    
    if (!gcBody || !goCaptcha) return;

    // 创建错误提示覆盖层
    let errorOverlay = gcBody.querySelector('.gocaptcha-error-overlay');
    if (!errorOverlay) {
        errorOverlay = document.createElement('div');
        errorOverlay.className = 'gocaptcha-error-overlay';
        errorOverlay.textContent = '验证错误，请重试';
        gcBody.appendChild(errorOverlay);
    }

    // 显示错误提示
    errorOverlay.classList.add('show');

    // 添加抖动动画
    goCaptcha.classList.add('gocaptcha-shake-animation');

    // 1.5秒后隐藏提示并移除动画，然后重新加载验证码
    setTimeout(() => {
        errorOverlay.classList.remove('show');
        goCaptcha.classList.remove('gocaptcha-shake-animation');
        
        setTimeout(() => {
            initGoCaptchaInModal();
        }, 300);
    }, 1500);
}

// 验证失败处理（移除弹窗提示）
confirm: function(point, reset) {
    // ... 验证逻辑 ...
    
    if (verificationResult.data === 'ok') {
        // 验证成功
        gocaptchaTokenInput.value = 'verified';
        showSuccess();
        setTimeout(() => {
            hideModal();
            isSubmittingForm = true;
            loginForm.submit();
        }, 1500);
    } else {
        // 验证失败：显示错误提示和抖动动画
        showCaptchaError();
    }
}
```

#### 数据字段映射（重要！）
```javascript
// 映射后端数据到前端组件
const frontendData = {
    image: data.data.master_image_base64,
    thumb: data.data.thumb_image_base64,
    thumbWidth: data.data.thumb_width || 60,
    thumbHeight: data.data.thumb_height || 60,
    thumbX: data.data.display_x || 0,  // 注意：是 display_x 不是 thumb_x
    thumbY: data.data.display_y || 0,  // 注意：是 display_y 不是 thumb_y
};

// 验证请求格式
const verifyBody = {
    id: 'slide-default',
    captchaKey: currentCaptchaKey,
    value: `${point.x},${point.y}`  // 格式：x,y
};
```

### 关键问题与解决方案

#### 问题1：样式冲突和布局错乱 ✅
**原因**：多层容器包装导致 GoCaptcha 组件样式被干扰
**解决方案**：
- 使用极简弹窗结构，不添加多余容器
- 只为 `.go-captcha` 组件添加白色背景和阴影效果
- 移除所有不必要的自定义关闭按钮

#### 问题2：验证码图形对不上 ✅
**原因**：字段名映射错误
- 错误：使用 `thumb_x`, `thumb_y`
- 正确：使用 `display_x`, `display_y`

#### 问题3：验证成功但前端认为失败 ✅
**原因**：响应格式判断错误
- GoCaptcha Service 返回：`{code: 200, data: 'ok'}`
- 前端期望：`{code: 200, data: {token: '...'}}`
- **解决**：修改判断条件为 `verificationResult.data === 'ok'`

#### 问题4：后端验证失败 ✅
**原因**：token 值不匹配
- 前端设置：动态生成的时间戳
- 后端期望：固定值 `"verified"`
- **解决**：前端设置 `gocaptchaTokenInput.value = 'verified'`

#### 问题5：多余的关闭按钮 ✅
**原因**：添加了额外的右上角关闭按钮
**解决方案**：
- 移除自定义关闭按钮的CSS和JavaScript
- 使用GoCaptcha组件内置的关闭功能
- 保留点击遮罩和ESC键关闭功能

#### 问题6：验证失败弹窗打扰 ✅
**原因**：验证失败时弹出alert提示
**解决方案**：
- 移除所有 `alert()` 弹窗提示
- 验证失败时直接重新加载验证码
- 用户可以立即重试，无需额外操作

#### 问题7：遮罩层叠加问题 ✅
**原因**：重试时重复创建遮罩层
**解决方案**：
- 在 `createModal()` 函数中添加重复创建检查
- 确保全局只有一个遮罩层实例
- 每次初始化前正确清理之前的实例

## 验证方式切换指南

### 1. 切换到拖拽式验证码 (SlideRegion)

#### 步骤1：确认配置文件支持
检查 `gocaptcha.json` 中是否存在 `drag_config_maps` 配置：
```json
{
  "builder": {
    "drag_config_maps": {
      "drag-default": {
        "version": "0.0.1",
        "master": {
          "image_size": { "width": 300, "height": 220 },
          "image_alpha": 1
        },
        "thumb": {
          "range_graph_size": { "min": 60, "max": 70 },
          "range_graph_angles": [{ "min": 0, "max": 0 }],
          "generate_graph_number": 2,
          "enable_graph_vertical_random": true,
          "range_dead_zone_directions": ["left", "right", "top", "bottom"]
        }
      }
    }
  }
}
```

#### 步骤2：修改前端代码
```javascript
// 1. 更改验证码类型
captchaInstance = new GoCaptcha.SlideRegion({
    width: 300,
    height: 220,
});

// 2. 更改请求 ID
fetch('/GoCaptchaProxy.ashx?path=get-data&id=drag-default')

// 3. 更改验证请求
const verifyBody = {
    id: 'drag-default',
    captchaKey: currentCaptchaKey,
    value: `${point.x},${point.y}`
};
```

### 2. 切换到点击式验证码 (Click)

#### 步骤1：使用现有点击配置
`gocaptcha.json` 中的 `click_config_maps`：
```json
{
  "builder": {
    "click_config_maps": {
      "click-default-ch": {
        // 点击验证码配置
      }
    }
  }
}
```

#### 步骤2：修改前端代码
```javascript
// 1. 更改验证码类型
captchaInstance = new GoCaptcha.Click({
    width: 300,
    height: 220,
});

// 2. 更改事件处理
captchaInstance.setEvents({
    confirm: function(dots, reset) {  // 注意：是 dots 数组，不是 point 对象
        console.log('Captcha confirmed. Dots:', dots);
        
        // 格式化点击坐标为字符串
        const dotValue = dots.map(dot => `${dot.x},${dot.y}`).join(',');
        
        const verifyBody = {
            id: 'click-default-ch',
            captchaKey: currentCaptchaKey,
            value: dotValue  // 格式：x1,y1,x2,y2,x3,y3
        };
    }
});

// 3. 更改数据请求
fetch('/GoCaptchaProxy.ashx?path=get-data&id=click-default-ch')

// 4. 数据映射（点击模式通常只需要 image 和 thumb）
const frontendData = {
    image: data.data.master_image_base64,
    thumb: data.data.thumb_image_base64,
};
```

### 3. 切换到旋转式验证码 (Rotate)

#### 步骤1：确认配置支持
检查 `rotate_config_maps` 配置：
```json
{
  "builder": {
    "rotate_config_maps": {
      "rotate-default": {
        "version": "0.0.1",
        "master": {
          "image_square_size": 220
        },
        "thumb": {
          "range_angles": [{ "min": 30, "max": 330 }],
          "range_image_square_sizes": [140, 150, 160, 170],
          "image_alpha": 1
        }
      }
    }
  }
}
```

#### 步骤2：修改前端代码
```javascript
// 1. 更改验证码类型
captchaInstance = new GoCaptcha.Rotate({
    width: 300,
    height: 220,
});

// 2. 更改事件处理
captchaInstance.setEvents({
    confirm: function(angle, reset) {  // 注意：是 angle 角度值
        console.log('Captcha confirmed. Angle:', angle);
        
        const verifyBody = {
            id: 'rotate-default',
            captchaKey: currentCaptchaKey,
            value: angle.toString()  // 角度值转字符串
        };
    }
});

// 3. 更改请求 ID
fetch('/GoCaptchaProxy.ashx?path=get-data&id=rotate-default')
```

## 调试与故障排除

### 常用调试步骤

1. **检查 GoCaptcha Service 状态**
```powershell
Get-Process | Where-Object {$_.ProcessName -like "*captcha*"}
```

2. **测试 API 可用性**
```powershell
Invoke-WebRequest -Uri "http://localhost:8080/api/v1/public/get-data?id=slide-default" -Method GET
```

3. **查看浏览器控制台日志**
- 网络请求状态
- JavaScript 错误信息
- 数据格式验证

4. **检查 C# 代理日志**
```csharp
Debug.WriteLine("GoCaptchaProxy: 相关调试信息");
```

### 常见问题

#### 问题：验证码无法显示
**检查项**：
- GoCaptcha Service 是否运行
- 配置文件路径是否正确
- 资源文件是否存在

#### 问题：弹窗样式错乱
**检查项**：
- 是否有多余的容器样式干扰
- GoCaptcha 组件本身的 CSS 是否正确加载
- 浏览器缓存是否已清理

#### 问题：重试时弹窗越来越黑
**解决方案**：
- 确保 `createModal()` 函数有重复创建检查
- 验证失败时不要重复创建遮罩层
- 使用单一全局遮罩层实例

#### 问题：验证失败用户体验差
**解决方案**：
- 移除所有 `alert()` 弹窗提示
- 让验证码自动刷新，用户可直接重试
- 确保错误处理不会打断用户操作流程

## 文件清单

### 核心文件
- `GoCaptchaProxy.ashx` - C# HTTP 代理
- `gocaptcha-init.js` - 前端集成脚本（优化后的弹窗模式）
- `gocaptcha-modal.css` - 极简弹窗遮罩样式
- `WapLogin.aspx` - 登录页面（简化弹窗结构）
- `WapLogin.aspx.cs` - 后端验证逻辑
- `gocaptcha.json` - GoCaptcha 配置文件
- `Web.config` - ASP.NET 配置

### 依赖文件
- `gocaptcha.global.js` - GoCaptcha 前端库
- `gocaptcha.global.css` - GoCaptcha 样式文件
- `go-captcha-service-windows-amd64.exe` - 服务程序

## 性能优化建议

1. **缓存策略**：合理设置验证码缓存时间
2. **资源优化**：压缩图片和字体资源
3. **网络优化**：使用 CDN 加速静态资源
4. **监控告警**：监控 GoCaptcha Service 运行状态
5. **弹窗优化**：最小化容器层级，避免样式冲突
6. **内存管理**：确保验证码实例正确销毁，避免内存泄漏

## 安全注意事项

1. **API 密钥管理**：妥善保管 GoCaptcha API 密钥
2. **HTTPS 部署**：生产环境启用 HTTPS
3. **频率限制**：防止验证码接口被恶意调用
4. **日志安全**：避免在日志中记录敏感信息

## 版本更新记录

### v1.5 - 最终完善版本 (2025-01-03)
- **修复**：解决弹窗模式下token被意外清空的关键问题
- **安全**：重新实现防爆破安全机制，验证码验证失败时立即中断
- **优化**：移除所有调试日志，代码更清洁
- **完善**：修复`hideModal()`函数逻辑，验证成功时保留token
- **稳定**：所有功能测试通过，可投入生产使用

### v1.4 - 安全修复与代码优化 (2025-01-03)
- **修复**：后端验证逻辑错误，确保验证码验证失败时立即中断
- **安全**：防止验证码被绕过进行密码爆破攻击
- **优化**：移除前端脚本中的所有 console.log 和冗余注释
- **改进**：代码更简洁，生产环境更安全

### v1.3 - 验证失败反馈优化 (2025-01-03)
- **新增**：验证失败时在图片底部显示红色半透明错误提示
- **新增**：验证失败时整个验证码组件左右抖动动画效果
- **优化**：错误提示持续1.5秒后自动消失并重新加载验证码
- **改进**：提供更直观的用户反馈，让用户明确知道操作结果

### v1.2 - 用户体验优化 (2025-01-03)
- **移除**：多余的右上角关闭按钮，使用组件内置关闭功能
- **优化**：验证失败时无弹窗打扰，验证码自动刷新
- **修复**：重试时遮罩层叠加问题，确保单一遮罩层实例
- **改进**：极简弹窗设计，减少用户操作步骤

### v1.1 - 弹窗模式实现 (2025-01-03)
- **新增**：弹窗遮罩显示模式，替代直接页面嵌入
- **优化**：简化 HTML 结构，避免容器样式冲突
- **改进**：用户体验流程优化，验证成功后自动提交
- **修复**：移除多余的按钮区域，使用浮动关闭按钮

### v1.0 - 基础集成 (2025-01-02)
- **实现**：滑动验证码基础功能
- **支持**：多种验证方式切换
- **完成**：C# 代理和后端验证

## 总结

GoCaptcha 弹窗模式的最终优化版本提供了极佳的用户体验：

### 主要优势

1. **极简设计**：移除所有不必要的UI元素和操作步骤
2. **无打扰重试**：验证失败时不弹出额外提示，用户可直接重试
3. **单一遮罩层**：解决了重试时遮罩层叠加的问题
4. **流畅交互**：支持多种关闭方式（组件内置、ESC键、点击遮罩）
5. **响应式适配**：完美适配PC端和移动端

### 技术亮点

1. **容器克隆策略**：避免DOM操作复杂性
2. **单实例模式**：确保全局只有一个弹窗实例
3. **CSS优先级管理**：完美保护GoCaptcha组件原有样式
4. **异常安全设计**：所有关键操作都有容错处理
5. **用户友好的错误处理**：优先自动恢复而非打断用户

### 关键成功要素

1. **正确的数据字段映射**
2. **匹配的验证码 ID 和配置**
3. **一致的数据格式处理**
4. **完善的错误处理机制**
5. **极简的弹窗结构设计**
6. **无打扰的用户体验优化**

通过本指南，可以轻松在不同验证方式之间切换，同时提供最优的用户体验和安全性。

---

**最后更新**：2025年1月3日  
**版本**：1.5  
**作者**：YaoHuo 开发团队 