/**
 * 标签切换服务
 * 统一管理页面中的标签切换功能
 * 支持内容显示/隐藏、URL状态管理、事件回调
 * 
 * @version 1.0
 * <AUTHOR>
 * @date 2025-01-07
 */

// import { EventHandler } from '../types/CommonTypes.js'; // 暂时未使用

/**
 * 标签配置接口
 */
export interface TabConfig {
    id: string;
    selector: string;
    contentSelector?: string;
    urlParam?: string;
    urlValue?: string;
    onActivate?: () => void;
    onDeactivate?: () => void;
}

/**
 * 标签组配置接口
 */
export interface TabGroupConfig {
    groupSelector: string;
    activeClass: string;
    tabs: TabConfig[];
    updateUrl?: boolean;
    baseUrl?: string;
    preserveParams?: string[];
    onChange?: (activeTab: TabConfig) => void;
}

/**
 * 标签切换服务类
 * 提供统一的标签切换、内容管理和URL状态同步功能
 */
export class TabSwitchService {
    private static instance: TabSwitchService;
    private activeGroups: Map<string, TabGroupConfig> = new Map();
    private currentTabs: Map<string, string> = new Map(); // groupId -> activeTabId

    /**
     * 获取单例实例
     */
    public static getInstance(): TabSwitchService {
        if (!TabSwitchService.instance) {
            TabSwitchService.instance = new TabSwitchService();
        }
        return TabSwitchService.instance;
    }

    /**
     * 初始化标签组
     * @param groupId 标签组ID
     * @param config 标签组配置
     */
    public static initTabGroup(groupId: string, config: TabGroupConfig): void {
        TabSwitchService.getInstance().initializeTabGroup(groupId, config);
    }

    /**
     * 切换到指定标签
     * @param groupId 标签组ID
     * @param tabId 标签ID
     */
    public static switchTab(groupId: string, tabId: string): void {
        TabSwitchService.getInstance().switchToTab(groupId, tabId);
    }

    /**
     * 获取当前激活的标签
     * @param groupId 标签组ID
     */
    public static getCurrentTab(groupId: string): string | undefined {
        return TabSwitchService.getInstance().getCurrentActiveTab(groupId);
    }

    /**
     * 初始化标签组
     */
    public initializeTabGroup(groupId: string, config: TabGroupConfig): void {
        this.activeGroups.set(groupId, config);

        // 绑定点击事件
        this.bindTabEvents(groupId, config);

        // 设置初始状态
        this.setInitialTab(groupId, config);
    }

    /**
     * 切换到指定标签
     */
    public switchToTab(groupId: string, tabId: string): void {
        const config = this.activeGroups.get(groupId);
        if (!config) return;

        const targetTab = config.tabs.find(tab => tab.id === tabId);
        if (!targetTab) return;

        const currentTabId = this.currentTabs.get(groupId);

        // 执行切换
        this.performTabSwitch(groupId, config, targetTab, currentTabId);
    }

    /**
     * 获取当前激活的标签
     */
    public getCurrentActiveTab(groupId: string): string | undefined {
        return this.currentTabs.get(groupId);
    }

    /**
     * 绑定标签点击事件
     */
    private bindTabEvents(groupId: string, config: TabGroupConfig): void {
        const tabElements = document.querySelectorAll(config.groupSelector);
        
        tabElements.forEach(tabElement => {
            tabElement.addEventListener('click', (e) => {
                e.preventDefault();
                
                const target = e.currentTarget as HTMLElement;
                const tabId = this.getTabIdFromElement(target, config);
                
                if (tabId) {
                    this.switchToTab(groupId, tabId);
                }
            });
        });
    }

    /**
     * 从元素获取标签ID
     */
    private getTabIdFromElement(element: HTMLElement, config: TabGroupConfig): string | undefined {
        // 尝试从data属性获取
        const dataAttributes = ['data-function', 'data-tab', 'data-id', 'data-category'];
        
        for (const attr of dataAttributes) {
            const value = element.getAttribute(attr);
            if (value && config.tabs.some(tab => tab.id === value)) {
                return value;
            }
        }

        // 尝试从选择器匹配
        for (const tab of config.tabs) {
            if (element.matches(tab.selector)) {
                return tab.id;
            }
        }

        return undefined;
    }

    /**
     * 设置初始标签状态
     */
    private setInitialTab(groupId: string, config: TabGroupConfig): void {
        // 从URL参数获取初始状态
        const urlParams = new URLSearchParams(window.location.search);
        let initialTabId: string | undefined;

        // 查找URL中指定的标签
        for (const tab of config.tabs) {
            if (tab.urlParam && tab.urlValue) {
                const paramValue = urlParams.get(tab.urlParam);
                if (paramValue === tab.urlValue) {
                    initialTabId = tab.id;
                    break;
                }
            }
        }

        // 如果URL中没有指定，使用第一个标签
        if (!initialTabId && config.tabs.length > 0) {
            initialTabId = config.tabs[0].id;
        }

        if (initialTabId) {
            this.switchToTab(groupId, initialTabId);
        }
    }

    /**
     * 执行标签切换
     */
    private performTabSwitch(groupId: string, config: TabGroupConfig, targetTab: TabConfig, currentTabId?: string): void {
        // 执行当前标签的deactivate回调
        if (currentTabId) {
            const currentTab = config.tabs.find(tab => tab.id === currentTabId);
            if (currentTab && currentTab.onDeactivate) {
                currentTab.onDeactivate();
            }
        }

        // 更新标签样式
        this.updateTabStyles(config, targetTab);

        // 更新内容显示
        this.updateContentDisplay(config, targetTab);

        // 更新URL
        if (config.updateUrl) {
            this.updateUrl(config, targetTab);
        }

        // 更新当前状态
        this.currentTabs.set(groupId, targetTab.id);

        // 执行目标标签的activate回调
        if (targetTab.onActivate) {
            targetTab.onActivate();
        }

        // 执行组级别的onChange回调
        if (config.onChange) {
            config.onChange(targetTab);
        }
    }

    /**
     * 更新标签样式
     */
    private updateTabStyles(config: TabGroupConfig, activeTab: TabConfig): void {
        const tabElements = document.querySelectorAll(config.groupSelector);
        
        tabElements.forEach(element => {
            element.classList.remove(config.activeClass);
            
            // 检查是否是激活的标签
            if (element.matches(activeTab.selector) || 
                this.getTabIdFromElement(element as HTMLElement, config) === activeTab.id) {
                element.classList.add(config.activeClass);
            }
        });
    }

    /**
     * 更新内容显示
     */
    private updateContentDisplay(config: TabGroupConfig, activeTab: TabConfig): void {
        // 隐藏所有内容
        config.tabs.forEach(tab => {
            if (tab.contentSelector) {
                const contentElement = document.querySelector(tab.contentSelector);
                if (contentElement) {
                    (contentElement as HTMLElement).style.display = 'none';
                }
            }
        });

        // 显示激活标签的内容
        if (activeTab.contentSelector) {
            const activeContent = document.querySelector(activeTab.contentSelector);
            if (activeContent) {
                (activeContent as HTMLElement).style.display = 'block';
            }
        }
    }

    /**
     * 更新URL状态
     */
    private updateUrl(config: TabGroupConfig, activeTab: TabConfig): void {
        if (!activeTab.urlParam || !activeTab.urlValue) return;

        const url = new URL(window.location.href);
        
        // 设置新的参数值
        url.searchParams.set(activeTab.urlParam, activeTab.urlValue);

        // 保留指定的参数
        if (config.preserveParams) {
            const currentParams = new URLSearchParams(window.location.search);
            config.preserveParams.forEach(param => {
                const value = currentParams.get(param);
                if (value) {
                    url.searchParams.set(param, value);
                }
            });
        }

        // 更新URL但不刷新页面
        history.replaceState(null, '', url.toString());
    }
}

// ==================== 全局函数，供模板调用 ====================

/**
 * 初始化标签组（简化接口）
 * @param groupId 标签组ID
 * @param config 标签组配置
 */
export function initTabGroup(groupId: string, config: TabGroupConfig): void {
    TabSwitchService.initTabGroup(groupId, config);
}

/**
 * 切换标签（简化接口）
 * @param groupId 标签组ID
 * @param tabId 标签ID
 */
export function switchTab(groupId: string, tabId: string): void {
    TabSwitchService.switchTab(groupId, tabId);
}

// 导出默认实例
export default TabSwitchService;
