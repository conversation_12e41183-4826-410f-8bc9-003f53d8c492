using Newtonsoft.Json;
using System;

namespace YaoHuo.Plugin.OAuth
{
    /// <summary>
    /// OAuth 2.0 标准令牌端点
    /// 实现授权码换取访问令牌
    /// </summary>
    public partial class Token : OAuthBasePage
    {

        /// <summary>
        /// JSON 响应内容
        /// </summary>
        public string jsonResponse { get; private set; }

        /// <summary>
        /// 页面加载事件
        /// </summary>
        protected void Page_Load(object sender, EventArgs e)
        {
            // 使用基类方法设置JSON响应格式和CORS头
            SetJsonResponse();
            SetCorsHeaders();

            try
            {
                if (Request.HttpMethod.ToUpper() != OAuthConstants.HTTP_METHOD_POST)
                {
                    throw new OAuth2Exception(OAuthConstants.INVALID_REQUEST, "仅支持 POST 请求");
                }

                ProcessTokenRequest();
            }
            catch (OAuth2Exception ex)
            {
                // 记录OAuth错误
                var clientId = GetRequestValue("client_id");
                var grantType = GetRequestValue("grant_type");
                OAuthLogger.LogError(ex.Error, clientId, ex.ErrorDescription, Request.UserHostAddress, Request.UserAgent);
                OAuthLogger.LogTokenExchange(clientId, grantType, false, Request.UserHostAddress, Request.UserAgent);

                HandleOAuth2Error(ex);
            }
            catch (Exception ex)
            {
                // 记录系统错误
                var clientId = GetRequestValue("client_id");
                var grantType = GetRequestValue("grant_type");
                OAuthLogger.LogError("server_error", clientId, ex.Message, Request.UserHostAddress, Request.UserAgent);
                OAuthLogger.LogTokenExchange(clientId, grantType, false, Request.UserHostAddress, Request.UserAgent);

                LogSecurityEvent($"令牌端点异常: {ex.Message}");
                HandleOAuth2Error(new OAuth2Exception(OAuthConstants.SERVER_ERROR, "服务器内部错误"));
            }

            // 输出JSON响应并结束请求
            Response.Write(jsonResponse);
            Response.Flush();
            Context.ApplicationInstance.CompleteRequest();
        }

        /// <summary>
        /// 处理令牌请求
        /// </summary>
        private void ProcessTokenRequest()
        {
            // 获取标准 OAuth 2.0 参数
            var grantType = GetRequestValue("grant_type");
            var code = GetRequestValue("code");
            var redirectUri = GetRequestValue("redirect_uri");
            var clientId = GetRequestValue("client_id");
            var clientSecret = GetRequestValue("client_secret");
            var codeVerifier = GetRequestValue("code_verifier");

            // 验证必需参数
            if (string.IsNullOrEmpty(grantType))
                throw new OAuth2Exception(OAuthConstants.INVALID_REQUEST, "缺少 grant_type 参数");

            if (grantType != OAuthConstants.GRANT_AUTHORIZATION_CODE)
                throw new OAuth2Exception(OAuthConstants.UNSUPPORTED_GRANT_TYPE, "仅支持 authorization_code 授权类型");

            if (string.IsNullOrEmpty(code))
                throw new OAuth2Exception(OAuthConstants.INVALID_REQUEST, "缺少 code 参数");

            if (string.IsNullOrEmpty(redirectUri))
                throw new OAuth2Exception(OAuthConstants.INVALID_REQUEST, "缺少 redirect_uri 参数");

            if (string.IsNullOrEmpty(clientId))
                throw new OAuth2Exception(OAuthConstants.INVALID_REQUEST, "缺少 client_id 参数");

            // 验证客户端凭据
            if (!string.IsNullOrEmpty(clientSecret))
            {
                if (!OAuthService.VerifyClientCredentials(clientId, clientSecret))
                    throw new OAuth2Exception(OAuthConstants.INVALID_CLIENT, "客户端凭据无效");
            }
            else
            {
                // 验证客户端是否存在
                var client = OAuthService.GetClient(clientId);
                if (client == null || !client.IsActive())
                    throw new OAuth2Exception(OAuthConstants.INVALID_CLIENT, "客户端无效");
            }

            // 交换授权码为访问令牌（完整业务流程）
            var accessToken = OAuthService.ExchangeCodeForToken(code, clientId, redirectUri, codeVerifier);

            // 获取用户信息
            var userInfo = OAuthService.GetUserInfo(accessToken.TokenId);

            // 构建令牌响应
            var tokenResponse = accessToken.ToTokenResponseWithUserInfo(userInfo);

            // 记录Token交换成功日志
            OAuthLogger.LogTokenExchange(clientId, grantType, true, Request.UserHostAddress, Request.UserAgent);

            // 记录成功日志
            LogSecurityEvent($"令牌交换成功: ClientId={clientId}, UserId={accessToken.UserId}");

            // 返回 JSON 响应
            jsonResponse = JsonConvert.SerializeObject(tokenResponse, Formatting.Indented);
        }

        /// <summary>
        /// 处理 OAuth 2.0 错误
        /// </summary>
        private void HandleOAuth2Error(OAuth2Exception ex)
        {
            Response.StatusCode = OAuthConstants.HTTP_BAD_REQUEST;

            var errorResponse = new
            {
                error = ex.Error,
                error_description = ex.ErrorDescription
            };

            jsonResponse = JsonConvert.SerializeObject(errorResponse, Formatting.Indented);
            LogSecurityEvent($"令牌端点错误: {ex.Error} - {ex.ErrorDescription}");
        }

    }
}