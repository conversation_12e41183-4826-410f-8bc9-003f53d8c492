﻿using System;
using System.Collections.Generic;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Book_View_change : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public wap_book_Model bookVo = new wap_book_Model();

        public List<class_Model> classList = new List<class_Model>();

        public string action = "";

        public string id = "";

        public string toclassid = "";

        public string lpage = "";

        public string INFO = "";

        public string ERROR = "";

        public wap_bbs_Model bbsVo = null;

        protected void Page_Load(object sender, EventArgs e)
        {
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
            }
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            lpage = GetRequestValue("lpage");
            toclassid = classid;
            CheckManagerLvl("04", classVo.adminusername, "bbs/book_view_admin.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;lpage=" + lpage + "&amp;id=" + id);
            class_BLL class_BLL = new class_BLL(a);
            classList = class_BLL.GetFromPathList(long.Parse(siteid), "bbs/index.aspx");
            if (!(action == "gomod"))
            {
                return;
            }
            try
            {
                toclassid = GetRequestValue("toclassid");
                wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(a);
                bbsVo = wap_bbs_BLL.GetModel(long.Parse(id));
                if (bbsVo == null)
                {
                    ShowTipInfo("已删除！或不存在！", "");
                }
                else if (bbsVo.ischeck == 1L)
                {
                    ShowTipInfo("正在审核中！", "");
                }
                else if (bbsVo.book_classid.ToString() != classid)
                {
                    ShowTipInfo("栏目ID对不上！可能没有传classid值！", "");
                }
                else if (bbsVo.islock == 1L)
                {
                    ShowTipInfo("此帖已锁！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bbsVo.book_classid + "&amp;id=" + bbsVo.id + "&amp;lpage=" + lpage);
                }
                else if (bbsVo.islock == 2L)
                {
                    ShowTipInfo("此帖已结！", "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + bbsVo.book_classid + "&amp;id=" + bbsVo.id + "&amp;lpage=" + lpage);
                }
                if (toclassid == "")
                {
                    INFO = "SELECT";
                    return;
                }
                // ✅ 使用DapperHelper进行安全的参数化更新操作
                string connectionString = PubConstant.GetConnectionString(a);
                string lockReason = "{" + userVo.nickname + "(ID" + userVo.userid + ")转移(" + classid + "→" + toclassid + ")" + $"{DateTime.Now:MM-dd HH:mm}" + "}<br/>";
                lockReason += bbsVo.whylock;

                string updateSql = "UPDATE wap_bbs SET book_classid = @ToClassId, whylock = @WhyLock WHERE userid = @SiteId AND id = @PostId";
                DapperHelper.Execute(connectionString, updateSql, new {
                    ToClassId = DapperHelper.SafeParseLong(toclassid, "目标栏目ID"),
                    WhyLock = DapperHelper.LimitLength(lockReason, 1000),
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    PostId = DapperHelper.SafeParseLong(id, "帖子ID")
                });

                // ✅ 使用DapperHelper进行安全的参数化插入日志
                string insertLogSql = @"INSERT INTO wap_log(siteid,oper_userid,oper_nickname,oper_type,log_info,oper_ip)
                                      VALUES (@SiteId, @OperUserId, @OperNickname, 0, @LogInfo, @OperIp)";
                DapperHelper.Execute(connectionString, insertLogSql, new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    OperUserId = DapperHelper.SafeParseLong(userid, "操作用户ID"),
                    OperNickname = DapperHelper.LimitLength(nickname, 50),
                    LogInfo = DapperHelper.LimitLength("用户ID:" + userid + "转移用户ID:" + bbsVo.book_pub + "发表的ID=" + id + "主题:" + bbsVo.book_title + " (原栏目ID:" + classid + ")至栏目ID:" + toclassid, 500),
                    OperIp = DapperHelper.LimitLength(IP, 50)
                });
                INFO = "OK";
                WapTool.ClearDataBBS("bbs" + siteid + classid);
                WapTool.ClearDataBBS("bbsTop" + siteid + classid);
                WapTool.ClearDataTemp("bbsTotal" + siteid + classid);
                WapTool.ClearDataBBS("bbs" + siteid + toclassid);
                WapTool.ClearDataBBS("bbsTop" + siteid + toclassid);
                WapTool.ClearDataTemp("bbsTotal" + siteid + toclassid);
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}