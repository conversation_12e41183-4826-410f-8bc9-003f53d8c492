// main.js
// 字符计数功能（Add.aspx）
function initQuestionCharCount() {
    const questionInput = document.getElementById('question');
    const questionLength = document.getElementById('questionLength');
    if (questionInput && questionLength) {
        questionLength.textContent = questionInput.value.length;
        questionInput.addEventListener('input', function () {
            questionLength.textContent = this.value.length;
        });
    }
}
document.addEventListener('DOMContentLoaded', initQuestionCharCount);

// 选项选择功能（Doit.aspx）
function selectOption(selected, other, value) {
    // Toggle selected class
    selected.classList.add('selected');
    if (other) other.classList.remove('selected');

    // Show/hide check indicator
    const selectedCheck = selected.querySelector('.option-check');
    const otherCheck = other ? other.querySelector('.option-check') : null;

    if (selectedCheck) selectedCheck.classList.remove('hidden');
    if (otherCheck) otherCheck.classList.add('hidden');

    // Update hidden input value
    const selectedAnswerInput = document.getElementById('selected_answer');
    if (selectedAnswerInput) {
        selectedAnswerInput.value = value;
    }

    // Enable submit button
    const submitBtn = document.getElementById('submit-btn');
    if (submitBtn) {
        submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        submitBtn.disabled = false;
    }
}

// 聊天区域自动滚动到底部（Index.aspx）
function scrollChatToBottom() {
    var chatContainer = document.getElementById('chat-messages-container');
    if (chatContainer) {
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }
}
document.addEventListener('DOMContentLoaded', scrollChatToBottom);

// 挑战列表“查看更多”按钮功能（Index.aspx）
function initLoadMoreChallenges() {
    var challengesContainer = document.getElementById('challenges-list-container');
    if (challengesContainer) {
        var challengeItems = challengesContainer.querySelectorAll('.challenge-item');
        var loadMoreButton = document.getElementById('load-more-challenges');
        var challengesToShowInitially = 5;
        if (challengeItems.length > challengesToShowInitially && loadMoreButton) {
            for (var i = challengesToShowInitially; i < challengeItems.length; i++) {
                challengeItems[i].style.display = 'none';
                challengeItems[i].classList.add('initially-hidden');
            }
            loadMoreButton.style.display = 'inline-flex';
            loadMoreButton.addEventListener('click', function (e) {
                e.preventDefault();
                var hiddenItems = challengesContainer.querySelectorAll('.initially-hidden');
                for (var j = 0; j < hiddenItems.length; j++) {
                    hiddenItems[j].style.display = '';
                    hiddenItems[j].classList.remove('initially-hidden');
                }
                loadMoreButton.style.display = 'none';
            });
        } else if (loadMoreButton) {
            loadMoreButton.style.display = 'none';
        }
    }
}
document.addEventListener('DOMContentLoaded', initLoadMoreChallenges); 