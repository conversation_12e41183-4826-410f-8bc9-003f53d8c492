﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="BuyGroup.aspx.cs" Inherits="YaoHuo.Plugin.BBS.BuyGroup" EnableViewState="false" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%@ Import Namespace="System.Web" %>
<%
string currentTime = DateTime.Now.ToString("H:mm");
string greeting = WapTool.GetHello();

// 获取新消息信息（实现[msg]UBB功能）
string newMessageContent = "";
if (userVo != null && userid != "0")
{
    string messageCount = WapTool.GetMessage(ver, userid, siteid, http_start, sid, classid, "1");
    if (messageCount != "0")
    {
        // 构建消息内容，替换模板中的x为实际消息数量
        string messageTemplate = "[img]/tupian/news.gif[/img]收到x封飞鸽传书 <a href=\"/bbs/Message_Clear.htm\" style=\"font-size:80%;\"><img width=\"20\" height=\"20\" style=\"vertical-align: middle;margin-top:-1.5%;\" src=\"/tupian/clean.png\"></a><script src=\"/NetCSS/JS/BookList/Message_Clear.js\"></script>";
        newMessageContent = messageTemplate.Replace("x", messageCount);
        // 处理[img]标签
        newMessageContent = newMessageContent.Replace("[img]", "<img src=\"" + http_start).Replace("[/img]", "\" />");
        // 包装链接
        newMessageContent = "<a href=\"" + http_start + "bbs/messagelist.aspx?siteid=" + siteid + "&amp;backurl=" + HttpUtility.UrlEncode("bbs/buygroup.aspx") + "\">" + newMessageContent + "</a>";
    }
}

// 获取用户当前身份信息（实现[myvip]UBB功能）
string currentIdentity = "";
if (userVo != null && userVo.idname != null)
{
    currentIdentity = WapTool.GetMyID(userVo.idname, lang, userVo.endTime);
}
else
{
    currentIdentity = "普通会员";
}

Response.Write(@"<!DOCTYPE html>
<html>
<head>
<meta charset=""UTF-8"">
<meta name=""referrer"" content=""same-origin"" />
<meta name=""Format-Detection"" content=""Telephone=NO"">
<meta name=""viewport"" content=""width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"">
<link rel=""icon"" href=""/favicon.ico"" />
<link rel=""stylesheet"" href=""/NetCSS/CSS/Index/Default/xqx.css"" type=""text/css"" />
<link rel=""stylesheet"" href=""/Template/default/default.css"" type=""text/css"" />
<title>付费购买身份 - 妖火网</title>
</head>
<body>
<div class=""newMessage"">" + newMessageContent + @"</div>
<div class=""title""><a href=""/"">首页</a>&gt;<a href=""/myfile.aspx"">我的地盘</a>&gt;购买身份</div>
<div class=""content"" style=""border-bottom: 1px solid #eee;"">我当前身份:<b><span class=""current-identity"">" + currentIdentity + @"</span></b></div>
<script>
    document.addEventListener(""DOMContentLoaded"", function() {
        const colorMap = {
            '绿色昵称': '#25a444',
            '红色昵称': '#FF0000',
            '蓝色昵称': '#228aff',
            '紫色昵称': '#c000ff',
            '粉色昵称': '#ff6363',
            '粉色昵称': '#ff00c0',
            '灰色昵称': '#BDBDBD',
            '/netimages/vip.gif': { name: '红名VIP', color: '#FF0000' },
            '/netimages/年费vip.gif': { name: '年费VIP', color: '#c000ff' },
            '/netimages/靓号.gif': { name: '靓', color: '#FF7F00' },
            '/netimages/帅.gif': { name: '帅', color: '#228aff' },
            '/netimages/newvip.gif': { name: '金名VIP', color: '#fa6700' }
        };

        function processIdentityElement(identityElement) {
            if (identityElement) {
                const identityText = identityElement.innerHTML;
                const regex = /^(绿色昵称|红色昵称|蓝色昵称|紫色昵称|粉色昵称|灰色昵称)/;
                const match = identityText.match(regex);
                
                if (match && colorMap[match[0]]) {
                    const coloredText = `<span style=""color: ${colorMap[match[0]]}"">${match[0]}</span>`;
                    identityElement.innerHTML = identityText.replace(match[0], coloredText);
                }

                const imgElement = identityElement.querySelector('img');
                if (imgElement) {
                    const imgSrc = imgElement.getAttribute('src').toLowerCase();
                    if (colorMap[imgSrc]) {
                        const { name, color } = colorMap[imgSrc];
                        imgElement.insertAdjacentHTML('afterend', `<span style=""color: ${color}; margin-left: 3px;"">${name}</span>`);
                    }
                }
            }
        }

        const currentIdentityElement = document.querySelector('.current-identity');
        processIdentityElement(currentIdentityElement);
    });
</script>
<div class=""line2"">[<a href=""/bbs/togroupcoinbuy.aspx?toid=120"">购买</a>] <font color='#25a444'>绿色昵称</font><br/>[<a href=""/bbs/togroupcoinbuy.aspx?toid=340"">购买</a>] <font color='#FF0000'>红色昵称</font><br/>[<a href=""/bbs/togroupcoinbuy.aspx?toid=341"">购买</a>] <font color='#228aff'>蓝色昵称</font><br/>[<a href=""/bbs/togroupcoinbuy.aspx?toid=342"">购买</a>] <font color='#9c63ce'>紫色昵称</font><br/>[<a href=""/bbs/togroupcoinbuy.aspx?toid=355"">购买</a>] <font color='#ff6363'>粉色昵称</font><br/>[<a href=""/bbs/togroupcoinbuy.aspx?toid=356"">购买</a>] <font color='#ff00c0'>粉紫昵称</font><br/>3元 或 37500妖晶/月</div>
<div class=""line1"">[<a href=""/bbs/togroupcoinbuy.aspx?toid=101"">购买</a>] <font color='#FF0000'><b>红名VIP</b></font> <img src=""/netimages/vip.gif""/><br/>5元 或 62500妖晶/月 <br/><b>特权：</b><font color='#FF0000'>双向拉黑、黑名单上限+10</font></div>
<div class=""line2"">[<a href=""/bbs/togroupcoinbuy.aspx?toid=358"">购买</a>] <font color='#fa6700'><b>金名VIP</b></font> <img src=""/netimages/newvip.gif""/><br/>6元 或 75000妖晶/月<br/><b>特权：</b><font color='#fa6700'>双向拉黑、黑名单上限+10</font></div>
<div class=""line1"">[<a href=""/bbs/togroupcoinbuy.aspx?toid=105"">购买</a>]	<font color='#c000ff'><b>紫名年费VIP</b></font> <img src=""/netimages/年费vip.gif""/><br/> 60元 或 750000妖晶/年<br/><b>特权：</b><font color='#c000ff'>双向拉黑、黑名单上限+20</font></div>
<div class=""line2"">[<a href=""/bbs/togroupcoinbuy.aspx?toid=140"">购买</a>] <font color='#FF7F00'><b>金名靓</b></font> <img src=""/netimages/靓号.gif""/> <br/>8元 或 100000妖晶/月<br/><b>特权：</b><font color='#FF7F00'>双向拉黑、黑名单上限+20</font></div>
<div class=""line1"">[<a href=""/bbs/togroupcoinbuy.aspx?toid=180"">购买</a>] <font color='#228aff'><b>蓝名帅</b></font> <img src=""/netimages/帅.gif""/> <br/>8元 或 100000妖晶/月<br/><b>特权：</b><font color='#228aff'>双向拉黑、黑名单上限+20</font></div>
<div class=""btBox""><div class=""bt2""><a href=""/myfile.aspx"">返回上级</a> <a href=""/"">返回首页</a></div></div> 
<script type=""text/javascript"" src=""/NetCSS/JS/LocalStorage.js""></script>
</body>
</html>");
%>