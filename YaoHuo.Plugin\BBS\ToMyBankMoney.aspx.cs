﻿using KeLin.ClassManager;
using System;
using System.Data;
using System.Web;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using Dapper;

namespace YaoHuo.Plugin.BBS
{
    public class ToMyBankMoney : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string tomoney = "";

        public string backurl = "";

        public string INFO = "";

        public string ERROR = "";

        public long STATE = 0L;

        public string touserid = "";

        public string remark = "";

        public string type = "";

        public long mainmoney = 0L;

        public long lvlmoney = 0L;

        public long allmoney = 0L;

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            tomoney = GetRequestValue("tomoney");
            type = GetRequestValue("type");
            backurl = base.Request.QueryString.Get("backurl");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            IsLogin(userid, backurl);
            mainmoney = WapTool.GetMoneyRegular(siteVo.moneyregular, 7);
            lvlmoney = WapTool.GetLvLRegular(siteVo.lvlRegular, 7);

            // 移除了手续费为0时关闭功能的代码

            addMoneyToMyBank();
            needPassWordToAdmin();
            switch (action)
            {
                case "sub":
                    subMoney();
                    break;
                case "add":
                    addMoney();
                    break;
            }
        }

        public void addMoney()
        {
            if (!WapTool.IsNumeric(tomoney) || tomoney.IndexOf('-') >= 0)
            {
                INFO = "NUM";
                return;
            }
            if (long.Parse(tomoney) < 1L || userVo.money < 1L)
            {
                INFO = "MAX";
                return;
            }
            if (userVo.money < long.Parse(tomoney))
            {
                INFO = "NOMONEY";
                return;
            }

            // ✅ 使用事务确保银行存币操作的原子性
            try
            {
                ExecuteBankDepositTransaction();
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = "存币失败: " + ex.Message;
            }
        }

        /// <summary>
        /// 执行银行存币的事务操作
        /// </summary>
        private void ExecuteBankDepositTransaction()
        {
            string connectionString = PubConstant.GetConnectionString(string_10);
            long moneyAmount = long.Parse(tomoney);
            long userIdLong = long.Parse(userid);
            long siteIdLong = long.Parse(siteid);

            TransactionHelper.ExecuteMoneyTransaction(connectionString, (connection, transaction) =>
            {
                // 1. 锁定用户账户并检查余额
                var currentBalance = TransactionHelper.GetUserBalanceWithLock(connection, transaction, userIdLong, siteIdLong);
                if (currentBalance < moneyAmount)
                {
                    throw new InvalidOperationException($"余额不足，当前余额：{currentBalance}，需要：{moneyAmount}");
                }

                // 2. 检查是否首次存币（决定是否更新mybanktime）
                var currentBankMoney = TransactionHelper.GetUserBankBalanceWithLock(connection, transaction, userIdLong, siteIdLong);

                string updateSql;
                if (currentBankMoney < 1L)
                {
                    // 首次存币，更新时间
                    updateSql = @"UPDATE [user]
                                 SET money = money - @Amount,
                                     mybankmoney = mybankmoney + @Amount,
                                     mybanktime = GETDATE()
                                 WHERE siteid = @SiteId AND userid = @UserId";
                }
                else
                {
                    // 非首次存币，不更新时间
                    updateSql = @"UPDATE [user]
                                 SET money = money - @Amount,
                                     mybankmoney = mybankmoney + @Amount
                                 WHERE siteid = @SiteId AND userid = @UserId";
                }

                // 3. 执行存币操作
                connection.Execute(updateSql, new {
                    Amount = moneyAmount,
                    SiteId = siteIdLong,
                    UserId = userIdLong
                }, transaction);

                // 4. 发送系统消息
                string messageTitle = "恭喜您将" + tomoney + "个存入银行！";
                string messageContent = "操作时间:" + DateTime.Now;
                TransactionHelper.SendSystemMessage(connection, transaction, siteIdLong, siteIdLong,
                    siteVo.nickname, userIdLong, messageTitle, messageContent);

                // 5. 记录银行日志（使用安全方法，传入计算后的余额）
                long newBalance = currentBalance - moneyAmount;
                TransactionHelper.SaveBankLogWithBalance(connection, transaction, siteIdLong, userIdLong, "存入银行",
                    -moneyAmount, userIdLong, nickname, "将币存入银行", IP, newBalance);
            });
        }

        public void subMoney()
        {
            if (!WapTool.IsNumeric(tomoney) || tomoney.IndexOf('-') >= 0)
            {
                INFO = "NUM";
                return;
            }
            if (long.Parse(tomoney) < 0L || userVo.myBankMoney < 1L)
            {
                INFO = "MAX";
                return;
            }
            long num = long.Parse(tomoney) * mainmoney / 100L;
            allmoney = long.Parse(tomoney) + num;
            if (userVo.myBankMoney < allmoney)
            {
                INFO = "NOBANKMONEY";
                return;
            }

            // ✅ 使用事务确保银行取币操作的原子性
            try
            {
                ExecuteBankWithdrawTransaction();
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = "取币失败: " + ex.Message;
            }
        }

        /// <summary>
        /// 执行银行取币的事务操作
        /// </summary>
        private void ExecuteBankWithdrawTransaction()
        {
            string connectionString = PubConstant.GetConnectionString(string_10);
            long withdrawAmount = long.Parse(tomoney);
            long handlingFee = withdrawAmount * mainmoney / 100L;
            long totalDeduct = withdrawAmount + handlingFee;
            long userIdLong = long.Parse(userid);
            long siteIdLong = long.Parse(siteid);

            TransactionHelper.ExecuteMoneyTransaction(connectionString, (connection, transaction) =>
            {
                // 1. 锁定用户银行账户并检查余额
                var currentBankBalance = TransactionHelper.GetUserBankBalanceWithLock(connection, transaction, userIdLong, siteIdLong);
                if (currentBankBalance < totalDeduct)
                {
                    throw new InvalidOperationException($"银行余额不足，当前余额：{currentBankBalance}，需要：{totalDeduct}（含手续费{handlingFee}）");
                }

                // 2. 获取用户当前金币余额（用于银行日志记录）
                var currentBalance = TransactionHelper.GetUserBalanceWithLock(connection, transaction, userIdLong, siteIdLong);

                // 3. 执行取币操作
                string updateSql = @"UPDATE [user]
                                    SET money = money + @WithdrawAmount,
                                        mybankmoney = mybankmoney - @TotalDeduct
                                    WHERE siteid = @SiteId AND userid = @UserId";
                connection.Execute(updateSql, new {
                    WithdrawAmount = withdrawAmount,
                    TotalDeduct = totalDeduct,
                    SiteId = siteIdLong,
                    UserId = userIdLong
                }, transaction);

                // 4. 发送系统消息
                var handlingFeeMsg = handlingFee != 0 ? $"，手续费{handlingFee}妖晶" : "";
                string messageTitle = $"您从银行取出{withdrawAmount}妖晶{handlingFeeMsg}";
                string messageContent = "操作时间:" + DateTime.Now;
                TransactionHelper.SendSystemMessage(connection, transaction, siteIdLong, siteIdLong,
                    siteVo.nickname, userIdLong, messageTitle, messageContent);

                // 5. 记录银行日志（使用安全方法，传入计算后的余额）
                long newBalance = currentBalance + withdrawAmount;
                TransactionHelper.SaveBankLogWithBalance(connection, transaction, siteIdLong, userIdLong, "取出银行",
                    withdrawAmount, userIdLong, nickname, $"从银行取币{handlingFeeMsg}", IP, newBalance);
            });
        }


    }
}