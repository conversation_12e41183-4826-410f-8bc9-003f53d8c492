﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Book_List_hot.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Book_List_hot" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%@ Import Namespace="System.Data" %>
<%
    Response.Write(WapTool.showTop(classVo.classname, wmlVo));
    StringBuilder strhtml_list = new StringBuilder();

    // 添加热门页面样式（包含派币帖变灰功能）
    strhtml.Append(GetHotPageStyles());

    if (adVo.secondShowTop != "")
    {
        strhtml.Append(adVo.secondShowTop);
    }

    // 添加热门天数切换按钮
    int currentDays = 0;
    if (int.TryParse(GetRequestValue("days"), out currentDays))
    {

        // 构建按钮组
        strhtml.Append("<div class='days-filter'>");

        // 构建选项按钮
        strhtml.Append("<a href='" + this.http_start + "bbs/book_list_hot.aspx?classid=" + this.classid + "&days=1' class='days-btn" +
            (currentDays == 1 ? " active" : "") + "'>一天内</a>");
            
        strhtml.Append("<a href='" + this.http_start + "bbs/book_list_hot.aspx?classid=" + this.classid + "&days=3' class='days-btn" +
            (currentDays == 3 ? " active" : "") + "'>三天内</a>");

        strhtml.Append("<a href='" + this.http_start + "bbs/book_list_hot.aspx?classid=" + this.classid + "&days=7' class='days-btn" +
            (currentDays == 7 ? " active" : "") + "'>一周内</a>");

        strhtml.Append("<a href='" + this.http_start + "bbs/book_list_hot.aspx?classid=" + this.classid + "&days=30' class='days-btn" +
            (currentDays == 30 ? " active" : "") + "'>一月内</a>");

        strhtml.Append("</div>");
    }

    // 显示列表
    string lpagetemp = "";
    if (this.CurrentPage > 1)
    {
        if (WapTool.ISAPI_Rewrite3_Open == "1")
        {
            lpagetemp = "?lpage=" + CurrentPage;
        }
        else
        {
            lpagetemp = "&amp;lpage=" + CurrentPage;
        }
    }
    strhtml_list.Append("<!--listS-->");
    //所有列表
    for (int i = 0; (listVo != null && i < listVo.Count); i++)
    {
        if (i % 2 == 0)
        {
            strhtml_list.Append("<div class=\"listdata line2\">");
        }
        else
        {
            strhtml_list.Append("<div class=\"listdata line1\">");
        }
        index = index + kk;
        // 使用统一的热门帖子图标渲染器（包含派币帖变灰功能）
        strhtml_list.Append(RenderHotPostIcons(listVo[i], (int)index));
        if (WapTool.ISAPI_Rewrite3_Open == "1")
        {
            strhtml_list.Append("<a class=\"topic-link\" href=\"" + http_start + "bbs-" + listVo[i].id + ".html" + lpagetemp + "" + "\">" + listVo[i].book_title + "</a><br/><span class=\"louzhunicheng\">" + ShowNickName_color(long.Parse(listVo[i].book_pub), listVo[i].book_author) + "</span>/<a class=\"topic-link\" href=\"" + this.http_start + "bbs/book_re.aspx?actoin=class&amp;siteid=" + this.siteid + "&amp;classid=" + listVo[i].book_classid + "&amp;id=" + listVo[i].id + "&amp;getTotal=" + listVo[i].book_re + "&amp;lpage=" + this.CurrentPage + "\">" + listVo[i].book_re + "</a>回/" + listVo[i].book_click + "阅 <span class=\"right\">" + WapTool.ShowTime(listVo[i].book_date) + "<span></div>");
        }
        else
        {
            strhtml_list.Append("<a class=\"topic-link\" href=\"" + http_start + "bbs/view.aspx?id=" + listVo[i].id + lpagetemp + "" + "\">" + listVo[i].book_title + "</a><br/><span class=\"louzhunicheng\">" + ShowNickName_color(long.Parse(listVo[i].book_pub), listVo[i].book_author) + "</span>/<a class=\"topic-link\" href=\"" + this.http_start + "bbs/book_re.aspx?actoin=class&amp;siteid=" + this.siteid + "&amp;classid=" + listVo[i].book_classid + "&amp;id=" + listVo[i].id + "&amp;getTotal=" + listVo[i].book_re + "&amp;lpage=" + this.CurrentPage + "\">" + listVo[i].book_re + "</a>回/" + listVo[i].book_click + "阅 <span class=\"right\">" + WapTool.ShowTime(listVo[i].book_date) + "<span></div>");
        }
    }
    if (listVo == null)
    {
        strhtml_list.Append("<div class=\"tip\">暂无记录！</div>");
    }
    strhtml_list.Append("<!--listE-->");

    //显示导航分页
    strhtml_list.Append(linkURL);
    strhtml.Append(strhtml_list);

    if (adVo.secondShowDown != "")
    {
        strhtml.Append(adVo.secondShowDown);
    }
    strhtml.Append("<script type=\"text/javascript\" src=\"/NetCSS/JS/BookList/KL_common.js\"></script>");
    strhtml.Append(WapTool.GetVS(wmlVo));
    strhtml.Append(classVo.sitedowntip);
    Response.Write(WapTool.ToWML(strhtml.ToString(), wmlVo));
    Response.Write(ERROR);
    Response.Write(WapTool.showDown(wmlVo));
%>