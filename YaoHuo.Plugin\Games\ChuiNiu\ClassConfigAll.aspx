﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="ClassConfigAll.aspx.cs" Inherits="YaoHuo.Plugin.Games.ChuiNiu.ClassConfigAll" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    Response.Write(WapTool.showTop(this.GetLang("疯狂吹牛配置|疯狂吹牛配置|site setup"), wmlVo));
    Response.Write("<div class=\"title\">" + this.GetLang("疯狂吹牛配置|疯狂吹牛配置|site setup") + "</div>");
    if (this.INFO == "OK")
    {
        Response.Write("<div class=\"tip\"><b>");
        Response.Write(this.GetLang("更新成功！|更新成功！|Successfully Update"));
        Response.Write("</b></div>");
    }
    else if (this.INFO == "NUMBER")
    {
        Response.Write("<div class=\"tip\"><b>");
        Response.Write("只能数值！");
        Response.Write("</b></div>");
    }
    Response.Write("<div class=\"content\">");
    Response.Write("<form name=\"f\" action=\"" + http_start + "games/chuiniu/ClassConfigAll.aspx\" method=\"post\">");
    Response.Write("[0]最小币:<br/>");
    Response.Write("<input type=\"text\" name=\"par0\" size=\"8\" value=\"" + this.par0 + "\"/><br/>");
    Response.Write("[1]最大币<br/>");
    Response.Write("<input type=\"text\" name=\"par1\" size=\"8\" value=\"" + this.par1 + "\"/><br/>");
    Response.Write("[2]获胜得到币的比率:<br/>");
    Response.Write("<input type=\"text\" name=\"par2\" size=\"8\" value=\"" + this.par2 + "\"/>%<br/>");
    Response.Write("[3]一天只能发布挑战:<br/>");
    Response.Write("<input type=\"text\" name=\"par3\" size=\"8\" value=\"" + this.par3 + "\"/>次<br/>");
    Response.Write("[4]显示聊天列表:");
    Response.Write("<input type=\"text\" name=\"par4\" size=\"5\" value=\"" + this.par4 + "\"/><br/>");
    Response.Write("(0-100行)<br/>");
    Response.Write("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
    Response.Write("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
    Response.Write("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
    Response.Write("<input type=\"hidden\" name=\"backtype\" value=\"" + backtype + "\"/>");
    Response.Write("<input type=\"submit\" name=\"g\" value=\"" + this.GetLang("保 存|保 存|save") + "\"/>");
    Response.Write("</form>");
    Response.Write("</form></div>");
    Response.Write(WapTool.GetVS(wmlVo));
    Response.Write(WapTool.showDown(wmlVo));
%>