﻿using System;
using System.Collections.Generic;
using System.Text;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin.Games.Rank
{
    public class Book_List : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public string KL_CheckIPTime = PubConstant.GetAppString("KL_CheckIPTime");

        public string action = "";

        public string linkURL = "";

        public string condition = "";

        public string ERROR = "";

        public string INFO = "";

        public string id = "0";

        public string type = "0";

        public List<wap2_games_rank_Model> listVo = null;
        public List<wap2_games_rank_Model> listRankTimes = null;
        public List<wap2_games_rank_Model> listRankMoney = null;
        public StringBuilder strhtml = new StringBuilder();

        public long kk = 1L;

        public long index = 0L;

        public long total = 0L;

        public long pageSize = 10L;

        public long CurrentPage = 1L;

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            id = GetRequestValue("nid");
            type = GetRequestValue("type");
            if (string.IsNullOrEmpty(type)) type = "0";
            condition = " siteid=" + siteid;
            if (id != "")
            {
                condition = condition + " and gameen='" + id + "' ";
            }
            try
            {
                // 强制每页输出 10 条
                pageSize = 10;
                wap2_games_rank_BLL wap2_games_rank_BLL = new wap2_games_rank_BLL(a);
                if (GetRequestValue("getTotal") != "" && GetRequestValue("getTotal") != "0")
                {
                    total = Convert.ToInt32(GetRequestValue("getTotal"));
                }
                else
                {
                    total = wap2_games_rank_BLL.GetListCount(condition);
                }
                if (total > 100L)
                {
                    total = 100L;
                }
                if (GetRequestValue("page") != "")
                {
                    CurrentPage = long.Parse(GetRequestValue("page"));
                }
                CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
                index = pageSize * (CurrentPage - 1L);
                linkURL = http_start + "games/rank/book_list.aspx?nid=" + id + "&amp;type=" + type + "&amp;getTotal=" + total;
                linkURL = WapTool.GetPageLink(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL);
                if (type == "0")
                {
                    listVo = wap2_games_rank_BLL.GetListVo(pageSize, CurrentPage, condition, "*", "rankTimes", total, 1);
                }
                else
                {
                    listVo = wap2_games_rank_BLL.GetListVo(pageSize, CurrentPage, condition, "*", "rankMoney", total, 1);
                }
                // 限制只输出前 100 名次：裁剪 listVo 超出部分
                if (listVo != null)
                {
                    long startIndex = pageSize * (CurrentPage - 1L);
                    long remain = 100L - startIndex;
                    if (remain < listVo.Count)
                    {
                        listVo = listVo.GetRange(0, (int)Math.Max(0, remain));
                    }
                }
                // 同时查询并裁剪净胜排行
                listRankTimes = wap2_games_rank_BLL.GetListVo(pageSize, CurrentPage, condition, "*", "rankTimes", total, 1);
                if (listRankTimes != null)
                {
                    long startIndex2 = pageSize * (CurrentPage - 1L);
                    long remain2 = 100L - startIndex2;
                    if (remain2 < listRankTimes.Count)
                    {
                        listRankTimes = listRankTimes.GetRange(0, (int)Math.Max(0, remain2));
                    }
                }
                // 同时查询并裁剪赚币排行
                listRankMoney = wap2_games_rank_BLL.GetListVo(pageSize, CurrentPage, condition, "*", "rankMoney", total, 1);
                if (listRankMoney != null)
                {
                    long startIndex3 = pageSize * (CurrentPage - 1L);
                    long remain3 = 100L - startIndex3;
                    if (remain3 < listRankMoney.Count)
                    {
                        listRankMoney = listRankMoney.GetRange(0, (int)Math.Max(0, remain3));
                    }
                }
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}
