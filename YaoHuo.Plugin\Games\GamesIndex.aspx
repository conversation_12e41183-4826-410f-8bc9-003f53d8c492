﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="GamesIndex.aspx.cs" Inherits="YaoHuo.Plugin.Games.GamesIndex" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    Response.Write(WapTool.showTop(classVo.classname, wmlVo));
    //会员可见
    if (this.IsCheckManagerLvl("|00|01|02|03|04|", "") == true)
    {
        if (classVo.siteimg != "UploadFiles/no.gif" && classVo.siteimg != "NetImages/no.gif")
        {
            strhtml.Append("<div class=\"logo\"><img src=\"" + http_start + classVo.siteimg + "\" alt=\"LOGO\"/></div>");
        }
        if (classVo.introduce != "")
        {
            strhtml.Append(classVo.introduce);
        }
        //显示广告
        if (adVo.secondShowTop != "")
        {
            strhtml.Append(adVo.secondShowTop);
        }
        //显示内容
        strhtml.Append("<div class=\"line2\">");
        strhtml.Append("<img src=\"chuiniu/dh.gif\" alt=\"PIC\"/><a href=\"" + this.http_start + "games/chuiniu/index.aspx\">疯狂吹牛</a><span class=\"separate\"></span><a href=\"" + this.http_start + "games/chuiniu/add.aspx\">放狠话</a> <br/>");
        strhtml.Append("今日大话总数 " + this.GetTodayState("chuiniu", "1") + " 次 <br/>");
        strhtml.Append("今日大话总量 " + this.GetTodayState("chuiniu", "2") + " " + siteVo.sitemoneyname + "<br/>");
        strhtml.Append("</div>");
        //strhtml.Append("<div class=\"line2\">");
        //strhtml.Append("<img src=\"stone/st.gif\" alt=\"PIC\"/><a href=\"" + this.http_start + "games/stone/index.aspx\">石头剪刀布</a><span class=\"separate\"></span><a href=\"" + this.http_start + "games/stone/add.aspx\">公开叫板</a><br/>");
        //strhtml.Append("今日疯狂总数 " + this.GetTodayState ("stone","1")+ "次 <br/>");
        //strhtml.Append("今日石头总量 " + this.GetTodayState("stone", "2") + " " + siteVo.sitemoneyname +"<br/>");
        //strhtml.Append("</div>");
        strhtml.Append("<div class=\"line1\">");
        strhtml.Append("<img src=\"touzi/sz.gif\" alt=\"PIC\"/><a href=\"" + this.http_start + "games/touzi/index.aspx\">激情骰子</a><span class=\"separate\"></span><a href=\"" + this.http_start + "games/touzi/book_list.aspx\">激情的岁月</a><br/>");
        strhtml.Append("今日掷骰总数 " + this.GetTodayState("touzi", "1") + " 次 <br/>");
        strhtml.Append("今日掷骰总量 " + this.GetTodayState("touzi", "2") + " " + siteVo.sitemoneyname + " <br/>");
        strhtml.Append("</div>");
        strhtml.Append("<div class=\"line2\">");
        strhtml.Append("<img src=\"apple/apple.gif\" alt=\"PIC\"/><a href=\"" + this.http_start + "games/apple/index.aspx\">苹果机</a><span class=\"separate\"></span><a href=\"" + this.http_start + "games/apple/info.aspx\">玩法</a> <br/>");
        strhtml.Append("今日投注总数 " + this.GetTodayState("apple", "1") + " 次 <br/>");
        strhtml.Append("今日投注总量 " + this.GetTodayState("apple", "2") + " " + siteVo.sitemoneyname + "  <br/>");
        strhtml.Append("</div>");
        strhtml.Append("<div class=\"line1\">");
        strhtml.Append("<img src=\"quankun/qkb.gif\" alt=\"PIC\"/><a href=\"" + this.http_start + "games/quankun/index.aspx\">乾坤宝</a><span class=\"separate\"></span><a href=\"" + this.http_start + "games/quankun/info.aspx\">规则</a><br/>");
        strhtml.Append("今日压注总数 " + this.GetTodayState("quankun", "1") + " 次 <br/>");
        strhtml.Append("今日压注总量 " + this.GetTodayState("quankun", "2") + " " + siteVo.sitemoneyname + "  <br/>");
        strhtml.Append("</div>");
        strhtml.Append("<div class=\"line2\">");
        strhtml.Append("<img src=\"wabao/wabao.gif\" alt=\"PIC\"/><a href=\"" + this.http_start + "games/waBao/index.aspx\">挖宝竟猜</a><span class=\"separate\"></span><a href=\"" + this.http_start + "games/wabao/book_list.aspx\">历史开奖</a><br/>");
        strhtml.Append("今日下注总数 " + this.GetTodayState("waBao", "1") + " 次 <br/>");
        strhtml.Append("今日下注总量 " + this.GetTodayState("waBao", "2") + " " + siteVo.sitemoneyname + "<br/>");
        strhtml.Append("</div>");
        strhtml.Append("<div class=\"line1\">");
        strhtml.Append("<img src=\"lucky28/luck28.gif\" alt=\"PIC\"/><a href=\"" + this.http_start + "games/lucky28/index.aspx\">幸运28</a> <br/>");
        //strhtml.Append("看看今天谁是幸运大赢家！ <br/>");
        strhtml.Append("今日投注总数 " + this.GetTodayState("lucky28", "1") + " 次 <br/>");
        strhtml.Append("今日投注总量 " + this.GetTodayState("lucky28", "2") + " " + siteVo.sitemoneyname + " <br/>");
        strhtml.Append("</div>");
        strhtml.Append("<div class=\"line2\">");
        strhtml.Append("<img src=\"horse/2.gif\" alt=\"PIC\"/><a href=\"" + this.http_start + "games/horse/index.aspx\">赛马场</a><br/>");
        //strhtml.Append("疯狂刺激，爽，赚币多多！ <br/>");
        strhtml.Append("今日投注总数 " + this.GetTodayState("horse", "1") + " 次 <br/>");
        strhtml.Append("今日投注总量 " + this.GetTodayState("horse", "2") + " " + siteVo.sitemoneyname + " <br/>");
        strhtml.Append("</div>");
        //strhtml.Append("<div class=\"line1\">");
        //strhtml.Append("<img src=\"egg/egg.gif\" alt=\"PIC\"/><a href=\"" + this.http_start + "games/egg/index.aspx\">砸金蛋</a> <br/>");
        //strhtml.Append("高赔率，快速致富的好途径！<br/>");
        //strhtml.Append("今日玩家总次数:" + this.GetTodayState("egg", "1") + " 次 <br/>");
        //strhtml.Append("今日砸金总金额:" + this.GetTodayState("egg", "2") + siteVo.sitemoneyname + " <br/>");
        //strhtml.Append("</div>");
        strhtml.Append("<div class=\"line2\">");
        strhtml.Append("<img src=\"/netImages/jz.gif\" alt=\"PIC\"/><a href=\"" + this.http_start + "clan/main.aspx\">家族争霸</a> <br/>");
        strhtml.Append("让自己的家族雄起来");
        strhtml.Append("</div>");
        //显示广告
        if (adVo.secondShowDown != "")
        {
            strhtml.Append(adVo.secondShowDown);
        }
        //导航按钮
        strhtml.Append("<div class=\"btBox\"><div class=\"bt2\">");
        strhtml.Append("<a href=\"" + this.http_start + "wapindex.aspx?siteid=" + siteid + "&amp;classid=" + classVo.childid + "" + "\">返回上级</a> ");
        strhtml.Append("<a href=\"" + this.http_start + "wapindex.aspx?siteid=" + siteid + "&amp;classid=0" + "\">返回首页</a>");
        strhtml.Append("</div></div>");
        if (classVo.sitedowntip != "")
        {
            strhtml.Append(classVo.sitedowntip);
        }
        if (base.IsCheckManagerLvl("|00|01|", ""))
        {
            //strhtml.Append("<div class=\"tip\">");
            //strhtml.Append("提示:在WEB/WAP【页面综合排版】，点击此栏目进去，点击【顶】【底】可录入UBB内容！【插入广告】也可以。有专门的游戏UBB调用。");
            //strhtml.Append("</div>");
        }
        Response.Write(WapTool.ToWML(strhtml.ToString(), wmlVo));
        //会员可见结束
    }
    Response.Write(ERROR);
    Response.Write(WapTool.showDown(wmlVo));
%>