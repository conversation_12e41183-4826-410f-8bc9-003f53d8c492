/* ====================================================== */
/*          通用弹窗基础样式 (Popup Base Styles)        */
/* ====================================================== */

/* 遮罩层 (Overlay) */
.popup-overlay {
    display: none; /* 默认隐藏 */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    z-index: 999;
    opacity: 0;
    transition: opacity 0.3s ease;
}

    .popup-overlay.active {
        display: block; /* 激活时显示 */
        opacity: 1;
    }

/* 弹窗容器 (Container) */
.popup-container {
    display: none; /* 默认隐藏 */
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.95);
    width: 290px; /* 默认宽度，可根据需要覆盖 */
    max-width: 92%; /* 最大宽度 */
    height: auto; /* 高度自适应 */
    z-index: 1000;
    border-radius: 8px; /* 默认圆角 */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15); /* 添加阴影效果 */
    overflow: hidden; /* 隐藏溢出内容，特别是当包含头部和内容区域时 */
    transition: transform 0.3s cubic-bezier(0.2, 0, 0.2, 1), opacity 0.3s cubic-bezier(0.2, 0, 0.2, 1);
    opacity: 0;
    display: flex; /* 使用flex布局方便内部结构组织 */
    flex-direction: column; /* 垂直排列头部和内容 */
}

    .popup-container.active {
        display: flex; /* 激活时显示 */
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }

/* 弹窗头部 (Header) - 可选 */
.popup-header {
    background: #303030; /* 默认头部背景 */
    color: #efeff2; /* 默认头部文字颜色 */
    font-size: 1.1rem; /* 字体大小 */
    font-weight: 600; /* 字体粗细 */
    height: 44px; /* 头部高度 */
    display: flex;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    flex-shrink: 0; /* 防止头部被压缩 */
    padding: 0 15px; /* 左右内边距 */
    border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* 头部底部分隔线 */
    position: relative; /* 用于关闭按钮定位 */
}

/* 弹窗内容区域 (Content Area) - 用于包裹主要内容 */
.popup-content-area {
    overflow-y: auto; /* 内容过多时可滚动 */
    flex-grow: 1; /* 占据剩余空间 */
    background-color: #fff; /* 内容区域背景色 */
}

/* 关闭按钮 (Close Button) */
.popup-close-btn {
    position: absolute;
    top: 50%; /* 垂直居中于头部 */
    right: 10px; /* 距离右侧距离 */
    transform: translateY(-50%); /* 精确垂直居中 */
    width: 28px; /* 调整按钮大小 */
    height: 28px; /* 调整按钮大小 */
    padding: 0;
    margin: 0;
    background: none;
    color: #efeff2;
    border: none;
    cursor: pointer;
    z-index: 1001;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: none;
    border-radius: 50%; /* 圆形按钮 */
    transition: background-color 0.2s ease, opacity 0.2s ease;
}

    .popup-close-btn:hover {
        /* background-color: rgba(255, 255, 255, 0.15); 悬停背景 */
        opacity: 0.5;
    }

    .popup-close-btn svg {
        width: 18px; /* SVG 图标大小 */
        height: 18px; /* SVG 图标大小 */
        stroke: currentColor; /* 描边颜色继承按钮 color */
        stroke-width: 20; /* SVG 描边宽度 */
        fill: currentColor; /* 填充颜色继承按钮 color */
    }

/* 适应没有头部的弹窗的关闭按钮定位 */
.popup-container:not(:has(.popup-header)) .popup-close-btn {
    top: 10px; /* 没有头部时，距离顶部距离 */
    right: 10px; /* 距离右侧距离 */
    transform: none; /* 移除垂直居中变换 */
    color: #555; /* 在白色背景上使用深色图标 */
}

    .popup-container:not(:has(.popup-header)) .popup-close-btn:hover {
        background-color: rgba(0, 0, 0, 0.1); /* 悬停背景 */
    }