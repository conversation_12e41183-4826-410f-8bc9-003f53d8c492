/**
 * QuickReplyAjax.js - 快速回复的核心AJAX逻辑
 * 提供异步提交回复和处理服务器响应的通用功能
 */
(function() {
    'use strict';

    /**
     * 快速回复的Ajax功能
     * @param {Object} options 配置选项
     * @param {HTMLFormElement} options.form 表单元素
     * @param {HTMLElement} options.submitButton 提交按钮
     * @param {String} options.ajaxUrl Ajax请求URL (可选，默认从表单推断)
     * @param {Function} options.onSuccess 成功回调
     * @param {Function} options.onError 错误回调
     */
    function initAsyncComment(options) {
        if (!options.form) {
            return;
        }

        const form = options.form;
        const submitBtn = options.submitButton || form.querySelector('[name="g"]');

        if (!submitBtn) {
            return;
        }

        submitBtn.onclick = function(e) {
            // 🔧 修复：无论是否需要验证码，都阻止默认表单提交，避免重复提交
            e.preventDefault();

            // 🔧 新增：派币帖验证码检查
            if (window.FreeMoneyPostCaptcha && window.REQUIRES_CAPTCHA) {
                // 防抖处理
                if (window.FreeMoneyPostCaptcha.isProcessing) {
                    return;
                }

                window.FreeMoneyPostCaptcha.ensureCaptchaToken(function() {
                    // 验证通过后继续原有逻辑
                    proceedWithOriginalSubmit();
                });
                return;
            }

            function proceedWithOriginalSubmit() {
                const formData = new FormData(form);
                const entries = formData.entries();
                const data = Object.fromEntries(entries);
                data.g = '快速回复';

                // 🔧 新增：确保验证码令牌被包含在提交数据中
                const tokenInput = document.getElementById('gocaptcha-token');
                if (tokenInput && tokenInput.value) {
                    data.gocaptchaToken = tokenInput.value;
                }

                if (data.content && data.content.length > 0) {
                    // 注意：这里不再需要preventDefault，因为已经在上面处理了

                    try {
                    // 处理换行和特殊字符
                    data.content = data.content.replace(/\n/g, '[br]');
                    data.content = data.content.replace(/\$\(/g, '$ (');
                    // 如果存在sendFriendMsg函数，则调用它
                    if (typeof sendFriendMsg === 'function') {
                        sendFriendMsg();
                    }
                } catch (error) {
                    // 忽略可能的错误
                }

                // 获取必要参数
                let siteid = data.siteid || window.siteid || (new URLSearchParams(window.location.search).get('siteid')) || '1';
                let classid = data.classid || window.classid || (new URLSearchParams(window.location.search).get('classid')) || '1';
                let bookid = data.id || window.bookid || (new URLSearchParams(window.location.search).get('id'));

                if (!bookid) {
                    return;
                }

                // 构建AJAX URL
                const ajaxUrl = options.ajaxUrl || 
                                `/bbs/book_re.aspx?ajax=1&siteid=${siteid}&classid=${classid}&id=${bookid}`;

                // 发送请求
                fetch(ajaxUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8;'
                    },
                    body: new URLSearchParams(data)
                })
                .then(res => res.text())
                .then(html => {
                    let recontenttipMatch = /class="tip">([\s\S]*?)<\/div>/.exec(html);
                    let recontenttip = recontenttipMatch ? recontenttipMatch[1] : '';
                    let result = {
                        isSuccess: false,
                        message: '',
                        yaogem: 0,
                        exp: 0,
                        timeoutDuration: 800
                    };

                    // 解析服务器响应
                    if (recontenttip.includes('回复成功')) {
                        let successMatch = /获得妖晶:(\d+)，获得经验:(\d+)/.exec(recontenttip);
                        if (successMatch) {
                            result.isSuccess = true;
                            result.message = '回复成功';
                            result.yaogem = successMatch[1];
                            result.exp = successMatch[2];
                            result.timeoutDuration = 300;
                            
                            // 重置表情选项
                            if (typeof emoticonContainer !== 'undefined') emoticonContainer.style.display = "none";
                            if (typeof selectOptions !== 'undefined') selectOptions.style.display = "block";
                            const emoticonInputs = document.querySelectorAll(".select_option input.select_input");
                            emoticonInputs.forEach(input => {
                                input.checked = false;
                            });
                            
                            // 清空文本框内容
                            if (form.querySelector('[name="content"]')) {
                                form.querySelector('[name="content"]').value = '';
                            }
                        }
                    } else if (recontenttip.includes('回复内容最少')) {
                        result.message = '回复内容最少' + /回复内容最少(.*?)字/.exec(recontenttip)[1] + '字';
                        result.timeoutDuration = 1200;
                    } else if (recontenttip.includes('回复内容最多')) {
                        result.message = '回复内容最多' + /回复内容最多(.*?)字/.exec(recontenttip)[1] + '字';
                        result.timeoutDuration = 1200;
                    } else if (recontenttip.includes('请不要发重复内容')) {
                        result.message = '请不要发重复内容';
                        result.timeoutDuration = 1200;
                    } else if (recontenttip.includes('请再过')) {
                        result.message = '请再过' + /请再过(.*?)秒后操作/.exec(recontenttip)[1] + '秒后操作';
                        result.timeoutDuration = 2000;
                    } else if (recontenttip.includes('今天已达回帖上限')) {
                        result.message = '今天已达回帖上限' + /今天已达回帖上限 (.*?) 条/.exec(recontenttip)[1] + '条';
                        result.timeoutDuration = 2000;
                    } else if (recontenttip.includes('取到非法值')) {
                        result.message = '禁止提交 $(，请在字符间增加空格';
                        result.timeoutDuration = 5000;
                    } else if (recontenttip.includes('您已被加入黑名单')) {
                        result.message = '您已被加入黑名单，请注意发帖规则';
                        result.timeoutDuration = 5000;
                    } else if (recontenttip.includes('禁止发言')) {
                        // 兼容服务端返回的“您已被禁止发言”或类似提示
                        // 这里不用完整句子匹配，避免因细微文案调整导致失配
                        result.message = '您已被禁止发言';
                        result.timeoutDuration = 5000;
                    }

                    // 处理响应结果
                    if (options.onSuccess) {
                        options.onSuccess(result, html);
                    }
                })
                .catch(error => {
                    if (options.onError) {
                        options.onError(error);
                    }
                });
                }
            }

            // 🔧 修复：不需要验证码时，直接执行AJAX提交（已在上面处理）
            proceedWithOriginalSubmit();
        };
    }

    /**
     * 创建提示UI
     * @param {String} message 提示消息
     * @param {String} title 提示标题
     * @param {Number} timeoutDuration 自动关闭时间(毫秒)
     * @returns {HTMLElement} 提示DOM元素
     */
    function createTipUI(message, title, timeoutDuration) {
        var tipHTML = '<div class="ui__alert"><div class="ui__alert_bg in"></div>';
        tipHTML += '<div class="ui__alert_content in"><div class="ui__content_body">';
        tipHTML += '<h4 class="ui__title">' + (title || '提示') + '</h4>';
        if (message) {
            tipHTML += '<div>' + message + '</div>';
        }
        tipHTML += '</div></div></div>';
        
        var tipElement = document.createElement('div');
        tipElement.id = 'retip';
        tipElement.innerHTML = tipHTML;
        
        // 添加关闭事件
        const clearTip = () => {
            if (hideTipTimeout) {
                tipElement.style.display = 'none';
                clearTimeout(hideTipTimeout);
            }
        };
        
        var hideTipTimeout = setTimeout(clearTip, timeoutDuration || 800);
        
        setTimeout(() => {
            const alertBg = tipElement.querySelector('.ui__alert_bg');
            const closeBtn = tipElement.querySelector('.ui__title');
            if (alertBg) alertBg.addEventListener('click', clearTip);
            if (closeBtn) closeBtn.addEventListener('click', clearTip);
        }, 10);
        
        return tipElement;
    }

    // 导出公共函数
    window.QuickReplyAjax = {
        initAsyncComment: initAsyncComment,
        createTipUI: createTipUI
    };
})(); 