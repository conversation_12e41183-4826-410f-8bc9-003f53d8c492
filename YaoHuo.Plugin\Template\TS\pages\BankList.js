import { ModalService } from '../services/ModalService.js';
import { PaginationService } from '../services/PaginationService.js';
import { AjaxService } from '../services/AjaxService.js';
export class BankListPage {
    static getInstance() {
        if (!BankListPage.instance) {
            BankListPage.instance = new BankListPage();
        }
        return BankListPage.instance;
    }
    static init() {
        BankListPage.getInstance().initialize();
    }
    initialize() {
        console.log('BankList页面初始化开始');
        this.initLucideIcons();
        this.initializeFilter();
        this.initPagination();
        this.bindTransactionDetailEvents();
        this.bindClearRecordsEvents();
        this.bindOtherEvents();
        console.log('BankList页面初始化完成');
    }
    initLucideIcons() {
        if (typeof window.lucide !== 'undefined') {
            window.lucide.createIcons();
            console.log('BankList页面: Lucide图标初始化完成');
        }
    }
    initializeFilter() {
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1;
        const yearSelect = document.getElementById('toyear');
        const monthSelect = document.getElementById('tomonth');
        if (!yearSelect || !monthSelect)
            return;
        for (let year = currentYear; year >= currentYear - 5; year--) {
            const option = document.createElement('option');
            option.value = year.toString();
            option.textContent = year + '年';
            yearSelect.appendChild(option);
        }
        this.updateMonthOptions(monthSelect, currentMonth);
        yearSelect.value = currentYear.toString();
        monthSelect.value = currentMonth.toString();
        yearSelect.addEventListener('change', () => {
            const selectedYear = parseInt(yearSelect.value);
            const maxMonth = (selectedYear === currentYear) ? currentMonth : 12;
            this.updateMonthOptions(monthSelect, maxMonth);
        });
        const filterForm = document.getElementById('filter-form');
        if (filterForm) {
            filterForm.addEventListener('submit', (e) => this.handleSearchSubmit(e));
        }
        console.log('BankList页面: 筛选表单初始化完成');
    }
    updateMonthOptions(monthSelect, maxMonth) {
        monthSelect.innerHTML = '';
        for (let month = 1; month <= maxMonth; month++) {
            const option = document.createElement('option');
            option.value = month.toString();
            option.textContent = month + '月';
            monthSelect.appendChild(option);
        }
    }
    initPagination() {
        const paginationInfo = this.extractPaginationInfo();
        if (paginationInfo) {
            const config = {
                currentPage: paginationInfo.currentPage,
                totalPages: paginationInfo.totalPages,
                baseUrl: window.location.href,
                pageParam: 'page',
                showPrevNext: true
            };
            PaginationService.init(config);
            console.log(`BankList页面: 分页初始化完成 (${paginationInfo.currentPage}/${paginationInfo.totalPages})`);
        }
    }
    extractPaginationInfo() {
        const paginationText = document.querySelector('.flex-1.text-center.text-sm.text-text-secondary.px-2');
        if (paginationText && paginationText.textContent) {
            const match = paginationText.textContent.match(/第\s*(\d+)\s*\/\s*(\d+)\s*页/);
            if (match) {
                return {
                    currentPage: parseInt(match[1]),
                    totalPages: parseInt(match[2])
                };
            }
        }
        return null;
    }
    bindTransactionDetailEvents() {
        document.querySelectorAll('tr[onclick*="showTransactionDetail"]').forEach(row => {
            row.removeAttribute('onclick');
            row.addEventListener('click', (e) => {
                e.preventDefault();
                this.showTransactionDetail(row);
            });
        });
    }
    bindClearRecordsEvents() {
        const clearRecordsBtn = document.getElementById('clear-records-btn');
        if (clearRecordsBtn) {
            clearRecordsBtn.addEventListener('click', () => {
                this.clearAllRecords();
            });
        }
    }
    bindOtherEvents() {
        window.resetFilter = () => this.resetFilter();
        window.navigatePage = (page) => this.navigatePage(page);
        window.toggleStats = () => this.toggleStats();
        window.goBack = () => this.goBack();
        window.showTransactionDetail = (row) => this.showTransactionDetail(row);
        window.closeTransactionDetail = () => this.closeTransactionDetail();
        window.handleSearchSubmit = (event) => this.handleSearchSubmit(event);
        console.log('BankList页面: 全局函数已暴露到window对象');
    }
    handleSearchSubmit(event) {
        event.preventDefault();
        const form = event.target;
        const formData = new FormData(form);
        const params = new URLSearchParams();
        params.set('action', 'search');
        const year = formData.get('year');
        const month = formData.get('month');
        if (year)
            params.set('year', year);
        if (month)
            params.set('month', month);
        const currentUrl = new URL(window.location.href);
        const siteid = currentUrl.searchParams.get('siteid');
        if (siteid)
            params.set('siteid', siteid);
        const newUrl = `${window.location.pathname}?${params.toString()}`;
        window.location.href = newUrl;
    }
    resetFilter() {
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1;
        const yearSelect = document.getElementById('toyear');
        const monthSelect = document.getElementById('tomonth');
        if (yearSelect)
            yearSelect.value = currentYear.toString();
        if (monthSelect) {
            this.updateMonthOptions(monthSelect, currentMonth);
            monthSelect.value = currentMonth.toString();
        }
        const currentUrl = new URL(window.location.href);
        const siteid = currentUrl.searchParams.get('siteid');
        const newUrl = siteid ? `${window.location.pathname}?siteid=${siteid}` : window.location.pathname;
        window.location.href = newUrl;
    }
    navigatePage(page) {
        if (page < 1)
            return;
        const currentUrl = new URL(window.location.href);
        currentUrl.searchParams.set('page', page.toString());
        window.location.href = currentUrl.toString();
    }
    toggleStats() {
        const statsPanel = document.getElementById('stats-panel');
        if (statsPanel) {
            statsPanel.classList.toggle('hidden');
        }
    }
    goBack() {
        history.back();
    }
    showTransactionDetail(row) {
        const actionName = row.getAttribute('data-action-name') || '';
        const remark = row.getAttribute('data-remark') || '无';
        const money = row.getAttribute('data-money') || '';
        const isPositive = row.getAttribute('data-is-positive') === 'True';
        const leftMoney = row.getAttribute('data-left-money') || '';
        const operaNickname = row.getAttribute('data-opera-nickname') || '';
        const operaUserId = row.getAttribute('data-opera-userid') || '';
        const addTime = row.getAttribute('data-add-time') || '';
        const modalContent = `
            <div class="detail-modal-header">
                <h3 class="font-medium text-lg">交易详情</h3>
                <button class="w-6 h-6 flex items-center justify-center text-gray-500 hover:text-gray-700" onclick="closeTransactionDetail()">
                    <i data-lucide="x" class="w-4 h-4"></i>
                </button>
            </div>
            <div class="detail-modal-content">
                <div class="flex justify-between items-start">
                    <span class="text-gray-500 text-sm">交易类型</span>
                    <span class="text-sm font-medium text-right">${actionName}</span>
                </div>
                <div class="flex justify-between items-start">
                    <span class="text-gray-500 text-sm">交易金额</span>
                    <span class="text-sm font-medium ${isPositive ? 'text-success' : 'text-danger'}">${isPositive ? '+' : ''}${money}</span>
                </div>
                <div class="flex justify-between items-start">
                    <span class="text-gray-500 text-sm">账户余额</span>
                    <span class="text-sm">${parseFloat(leftMoney).toLocaleString()}</span>
                </div>
                <div class="flex justify-between items-start">
                    <span class="text-gray-500 text-sm">操作人</span>
                    <span class="text-sm text-right">${operaNickname}</span>
                </div>
                <div class="flex justify-between items-start">
                    <span class="text-gray-500 text-sm">操作人ID</span>
                    <span class="text-sm text-right">${operaUserId}</span>
                </div>
                <div class="flex justify-between items-start">
                    <span class="text-gray-500 text-sm">交易时间</span>
                    <span class="text-sm text-right">${addTime}</span>
                </div>
                <div class="flex justify-between items-start">
                    <span class="text-gray-500 text-sm">备注</span>
                    <span class="text-sm text-right break-words max-w-48">${remark}</span>
                </div>
            </div>
            <div class="detail-modal-footer">
                <button class="h-9 px-4 bg-primary text-white rounded-md text-sm font-medium hover:bg-primary-dark transition-colors" onclick="closeTransactionDetail()">确定</button>
            </div>
        `;
        ModalService.getInstance().showCustomModal({
            title: '',
            content: modalContent,
            showCloseButton: false,
            customClass: 'transaction-detail-modal',
            contentClass: 'detail-modal-container'
        });
    }
    closeTransactionDetail() {
        const modals = document.querySelectorAll('.confirm-overlay');
        const latestModal = modals[modals.length - 1];
        if (latestModal) {
            latestModal.remove();
        }
    }
    async clearAllRecords() {
        try {
            const confirmed = await ModalService.confirmDelete('确定要删除所有会员的历史交易记录吗？<br/>此操作将保留本月记录，删除本月之前的所有记录。<br/>此操作不可恢复！');
            if (confirmed) {
                const currentUrl = new URL(window.location.href);
                const siteid = currentUrl.searchParams.get('siteid') || '';
                const clearUrl = `/bbs/banklist.aspx?action=deleteall&siteid=${siteid}`;
                await AjaxService.post(clearUrl, null, {
                    showToast: true,
                    successMessage: '清空成功',
                    errorMessage: '清空失败，请重试',
                    onSuccess: () => {
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    }
                });
            }
        }
        catch (error) {
            console.error('清空记录失败:', error);
        }
    }
    getPageStats() {
        const records = document.querySelectorAll('tbody tr');
        const paginationInfo = this.extractPaginationInfo();
        return {
            totalRecords: records.length,
            currentPage: paginationInfo?.currentPage || 1,
            totalPages: paginationInfo?.totalPages || 1
        };
    }
}
document.addEventListener('DOMContentLoaded', () => {
    BankListPage.init();
});
export function getBankListPage() {
    return BankListPage.getInstance();
}
export default BankListPage;
