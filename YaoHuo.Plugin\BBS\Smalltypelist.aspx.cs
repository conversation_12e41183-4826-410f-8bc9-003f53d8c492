﻿using System;
using System.Collections.Generic;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.Tool;

namespace YaoHuo.Plugin.BBS
{
	public class Smalltypelist : MyPageWap
	{
		private string string_10 = PubConstant.GetAppString("InstanceName");

		public string action = "";

		public string linkURL = "";

		public string condition = "";

		public string ERROR = "";

		public string INFO = "";

		public string string_11 = "";

		public string friendtype = "";

		public string backurl = "";

		public string linkTOP = "";

		public string favtypeid = "";

		public string subjectname = "";

		public string ordernum = "";

		public string color = "";

		public string content = "";

		public List<wap2_smallType_Model> sublistVo = null;

		protected void Page_Load(object sender, EventArgs e)
		{
			action = GetRequestValue("action");
			backurl = base.Request.QueryString.Get("backurl");
			if (backurl == null || backurl == "")
			{
				backurl = base.Request.Form.Get("backurl");
			}
			if (backurl == null || backurl == "")
			{
				backurl = "admin/basesitemodifywml.aspx?siteid=" + siteid;
			}
			backurl = ToHtm(backurl);
			backurl = HttpUtility.UrlDecode(backurl);
			backurl = WapTool.URLtoWAP(backurl);
			IsCheckUserManager(userid, userVo.managerlvl, "", "admin/basesitemodifywml.aspx?siteid=" + siteid);
			switch (action)
			{
				case "goaddinfo":
					goAddInfo();
					break;
				case "goadd":
					goAdd();
					break;
				case "class":
					showclass();
					break;
				default:
					showclass();
					break;
			}
		}

		public void showclass()
		{
			condition = " siteid = " + siteid + " and systype='card'";
			try
			{
				wap2_smallType_BLL wap2_smallType_BLL = new wap2_smallType_BLL(string_10);
				sublistVo = wap2_smallType_BLL.GetListVo(100L, 1L, condition, "*", "id", 100L, 0);
				wap2_smallType_Model model = wap2_smallType_BLL.GetModel("siteid=" + siteid + " and systype='card_info' ");
				if (model != null)
				{
					content = model.subclassName;
				}
			}
			catch (Exception ex)
			{
				ERROR = WapTool.ErrorToString(ex.ToString());
			}
		}

		public void goAdd()
		{
			subjectname = GetRequestValue("subjectname");
			color = GetRequestValue("color");
			ordernum = GetRequestValue("ordernum");
			if (!WapTool.IsNumeric(ordernum))
			{
				ordernum = "0";
			}
			wap2_smallType_Model wap2_smallType_Model = new wap2_smallType_Model();
			wap2_smallType_Model.siteid = long.Parse(siteid);
			wap2_smallType_Model.subclassName = subjectname + "#" + color;
			wap2_smallType_Model.maker = long.Parse(userid);
			wap2_smallType_Model.rank = long.Parse(ordernum);
			wap2_smallType_Model.systype = "card";
			try
			{
				if (subjectname.Trim() == "")
				{
					INFO = "NULL";
				}
				else
				{
					wap2_smallType_BLL wap2_smallType_BLL = new wap2_smallType_BLL(string_10);
					wap2_smallType_BLL.Add(wap2_smallType_Model);
					INFO = "ADDOK";
				}
			}
			catch (Exception ex)
			{
				ERROR = WapTool.ErrorToString(ex.ToString());
			}
			showclass();
		}

		public void goAddInfo()
		{
			content = GetRequestValue("content");
			try
			{
				wap2_smallType_BLL wap2_smallType_BLL = new wap2_smallType_BLL(string_10);
				wap2_smallType_Model model = wap2_smallType_BLL.GetModel("siteid=" + siteid + " and systype='card_info' ");
				if (model != null)
				{
					model.subclassName = content;
					wap2_smallType_BLL.Update(model);
				}
				else
				{
					model = new wap2_smallType_Model();
					model.siteid = long.Parse(siteid);
					model.subclassName = content;
					model.maker = long.Parse(userid);
					model.rank = 0L;
					model.systype = "card_info";
					wap2_smallType_BLL.Add(model);
				}
				INFO = "ADDOK";
			}
			catch (Exception ex)
			{
				ERROR = WapTool.ErrorToString(ex.ToString());
			}
			showclass();
		}
	}
}