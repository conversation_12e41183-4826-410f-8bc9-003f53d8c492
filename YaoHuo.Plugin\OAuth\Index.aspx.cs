﻿using System;
using System.Collections.Generic;
using System.Text;

namespace YaoHuo.Plugin.OAuth
{
    /// <summary>
    /// 用户授权记录管理
    /// </summary>
    public partial class Index : OAuthBasePage
    {

        /// <summary>
        /// 执行动作
        /// </summary>
        public string Action
        {
            get
            {
                var action = GetRequestValue("action") ?? "default";
                return action.ToLower();
            }
        }

        /// <summary>
        /// 输出网页字符
        /// </summary>
        public StringBuilder strhtml { get; } = new StringBuilder();

        public List<dynamic> listVo { get; set; } = null;

        public long kk { get; set; } = 1L;

        public long index { get; set; } = 0L;

        /// <summary>
        /// 加载时
        /// </summary>
        protected void Page_Load(object sender, EventArgs e)
        {
            // 使用基类方法验证用户登录状态
            var userInfo = EnsureUserAuthenticated();
            if (userInfo == null) return;

            // 添加用户信息调试
            LogSecurityEvent($"当前用户信息: UserID={userInfo.userid}, ManagerLvl={userInfo.managerlvl}");
            //执行动作
            // 移除标题输出，避免重复显示
            if (Action == "add")
            {
                //限制请求类型
                if (base.Request.HttpMethod.ToLower() != "post")
                {
                    strhtml.Append("<div class='tip'>请求无效</div>");
                    return;
                }
                //读取参数
                var dateTimeNow = DateTime.Now;
                var appID = GetRequestValue("aid");
                // 新架构中不再需要手动添加授权记录，授权记录通过访问令牌表管理
                strhtml.Append("<div class='tip'>新架构中授权记录通过令牌自动管理</div>");
            }
            else if (Action == "del")
            {
                // 添加 CSRF 保护
                var csrfToken = GetRequestValue("token");
                if (!ValidateFormToken(OAuthConstants.CSRF_INDEX_DELETE, csrfToken))
                {
                    strhtml.Append("<div class='tip'>安全验证失败</div>");
                    LogSecurityEvent($"删除授权记录CSRF验证失败: UserID={userInfo.userid}, IP={Request.UserHostAddress}");
                    return;
                }

                // 新架构中删除授权记录等同于撤销该应用的所有令牌
                var appId = GetRequestValue("aid");
                if (!string.IsNullOrEmpty(appId))
                {
                    try
                    {
                        var revokedCount = OAuthService.RevokeUserTokensForClient(userInfo.userid, appId);
                        strhtml.Append($"<div class='tip'>删除授权成功，已撤销 {revokedCount} 个访问令牌</div>");
                        LogSecurityEvent($"删除授权成功: AppID={appId}, UserID={userInfo.userid}, 撤销令牌数={revokedCount}");
                    }
                    catch (Exception ex)
                    {
                        strhtml.Append("<div class='tip'>删除授权失败，请稍后再试</div>");
                        LogSecurityEvent($"删除授权失败: AppID={appId}, UserID={userInfo.userid}, 错误={ex.Message}");
                    }
                }
                else
                {
                    strhtml.Append("<div class='tip'>参数错误</div>");
                }
            }
            else if (Action == "revoke")
            {
                // 撤销应用的所有令牌
                var csrfToken = GetRequestValue("token");
                if (!ValidateFormToken(OAuthConstants.CSRF_INDEX_REVOKE, csrfToken))
                {
                    strhtml.Append("<div class='tip'>安全验证失败</div>");
                    LogSecurityEvent($"撤销令牌CSRF验证失败: UserID={userInfo.userid}, IP={Request.UserHostAddress}");
                    return;
                }

                var appId = GetRequestValue("aid");
                if (!string.IsNullOrEmpty(appId))
                {
                    try
                    {
                        var revokedCount = OAuthService.RevokeUserTokensForClient(userInfo.userid, appId);
                        strhtml.Append($"<div class='tip'>撤销令牌成功，已撤销 {revokedCount} 个访问令牌</div>");
                        LogSecurityEvent($"撤销应用令牌成功: AppID={appId}, UserID={userInfo.userid}, 撤销令牌数={revokedCount}");
                    }
                    catch (Exception ex)
                    {
                        strhtml.Append("<div class='tip'>撤销令牌失败，请稍后再试</div>");
                        LogSecurityEvent($"撤销应用令牌失败: AppID={appId}, UserID={userInfo.userid}, 错误={ex.Message}");
                    }
                }
                else
                {
                    strhtml.Append("<div class='tip'>参数错误</div>");
                }
            }
            else if (Action == "revokeall")
            {
                // 撤销用户的所有令牌
                var csrfToken = GetRequestValue("token");
                if (!ValidateFormToken(OAuthConstants.CSRF_INDEX_REVOKE_ALL, csrfToken))
                {
                    strhtml.Append("<div class='tip'>安全验证失败</div>");
                    LogSecurityEvent($"撤销所有令牌CSRF验证失败: UserID={userInfo.userid}, IP={Request.UserHostAddress}");
                    return;
                }

                try
                {
                    var revokedCount = OAuthService.RevokeAllUserTokens(userInfo.userid);
                    strhtml.Append($"<div class='tip'>撤销所有令牌成功，已撤销 {revokedCount} 个访问令牌</div>");
                    LogSecurityEvent($"撤销所有令牌成功: UserID={userInfo.userid}, 撤销令牌数={revokedCount}");
                }
                catch (Exception ex)
                {
                    strhtml.Append("<div class='tip'>撤销所有令牌失败，请稍后再试</div>");
                    LogSecurityEvent($"撤销所有令牌失败: UserID={userInfo.userid}, 错误={ex.Message}");
                }
            }
            else
            {
                //默认操作 - 获取当前用户的授权记录
                listVo = OAuthService.GetUserAuthorizations(userInfo.userid);
            }
        }

    }
}