﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Settings.aspx.cs" Inherits="YaoHuo.Plugin.BBS.Settings" %><!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <title>论坛设置</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="/NetCSS/CSS/Setting.css" type="text/css" />
    <script type="text/javascript" src="/NetCSS/JS/BBSetting.js"></script>
    <%= GetUserIdJsVar() %>
    <%= GetSettingsJsVars() %>
    <style>
        html {display:flex;min-height:100%;justify-content:center;align-items:center;flex-direction:column;} 
        body{background: #e8e8e8;max-width: 720px;}
    </style>
</head>
<body class="<%= IsIframeMode() ? "iframe-mode" : "" %>">
    <div class="settings-popup-styled-container">
        <div class="settings-popup-content-area">
            <form runat="server">
            <div class="toggle-container">
                <ul class="tg-list">
                    <li class="tg-list-item">
                        <h4>新版回帖</h4>
                        <input class="tgl tgl-ios" id="cb1" type="checkbox" />
                        <label class="tgl-btn" for="cb1"></label>
                    </li>
                    <li class="tg-list-item">
                        <h4>隐藏吃肉</h4>
                        <input class="tgl tgl-ios" id="cbHideUselessReplies" type="checkbox" />
                        <label class="tgl-btn" for="cbHideUselessReplies"></label>
                    </li>
                    <li class="tg-list-item">
                        <h4>字体切换</h4>
                        <div class="font-choice">
                            <input type="radio" id="song" name="font" />
                            <label for="song">默认</label>
                            <input type="radio" id="other" name="font" />
                            <label for="other">系统</label>
                        </div>
                    </li>
                    <li class="tg-list-item">
                        <h4>勋章显示</h4>
                        <div class="font-choice Medal-Images-choice">
                            <input type="radio" id="medalPartial" name="medalDisplay" />
                            <label for="medalPartial">部分</label>
                            <input type="radio" id="medalAll" name="medalDisplay" />
                            <label for="medalAll">全部</label>
                        </div>
                    </li>
                </ul>
                    <div style="text-align: center;" class="save-btn-container">
                        <button id="saveSettingsBtn" class="save-btn" type="button">保存设置</button>
                        <div id="statusMessage" class="status-message" style="display:none;"></div>
                    </div>
                    <div id="debug-info" style="display:none;"></div>
            </div>
            </form>
        </div>
    </div>
</body>
</html>