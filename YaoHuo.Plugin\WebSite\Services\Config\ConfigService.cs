using System;
using System.Collections.Generic;
using System.IO;
using System.Web;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.WebSite.Services.Config
{
    /// <summary>
    /// 通用配置服务
    /// 统一管理所有JSON配置文件的加载、缓存和访问
    /// </summary>
    public static class ConfigService
    {
        private const string CONFIG_BASE_PATH = "~/Data/ModuleConfigs/";
        private const int DEFAULT_CACHE_HOURS = 24;

        /// <summary>
        /// 获取配置数据
        /// </summary>
        /// <typeparam name="T">配置数据类型</typeparam>
        /// <param name="configName">配置名称（用作缓存键）</param>
        /// <param name="relativePath">相对于ModuleConfigs的路径，如"BBS/IdentityConfigs.json"</param>
        /// <param name="cacheHours">缓存小时数，默认24小时</param>
        /// <returns>配置数据</returns>
        public static T GetConfig<T>(string configName, string relativePath, int cacheHours = DEFAULT_CACHE_HOURS) where T : class
        {
            try
            {
                string fullPath = CONFIG_BASE_PATH + relativePath;
                return ConfigCacheService.GetConfig<T>(configName, fullPath, cacheHours);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"通用配置服务加载失败: {configName}, 错误: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取配置数据（使用默认路径约定）
        /// </summary>
        /// <typeparam name="T">配置数据类型</typeparam>
        /// <param name="configName">配置名称，如"IdentityConfigs"</param>
        /// <param name="module">模块名称，如"BBS"，默认为"BBS"</param>
        /// <returns>配置数据</returns>
        public static T GetConfig<T>(string configName, string module = "BBS") where T : class
        {
            string relativePath = $"{module}/{configName}.json";
            return GetConfig<T>(configName, relativePath, DEFAULT_CACHE_HOURS);
        }

        /// <summary>
        /// 刷新特定配置的缓存
        /// </summary>
        /// <param name="configName">配置名称</param>
        public static void RefreshConfig(string configName)
        {
            ConfigCacheService.RefreshConfig(configName);
            System.Diagnostics.Debug.WriteLine($"通用配置服务刷新缓存: {configName}");
        }

        /// <summary>
        /// 清空所有配置缓存
        /// </summary>
        public static void ClearAllConfigs()
        {
            ConfigCacheService.ClearAllConfigs();
            System.Diagnostics.Debug.WriteLine("通用配置服务清空所有缓存");
        }

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        /// <returns>缓存统计信息</returns>
        public static string GetCacheStats()
        {
            return ConfigCacheService.GetCacheStats();
        }

        /// <summary>
        /// 检查配置文件是否存在
        /// </summary>
        /// <param name="configName">配置名称</param>
        /// <param name="module">模块名称</param>
        /// <returns>文件是否存在</returns>
        public static bool ConfigExists(string configName, string module = "BBS")
        {
            try
            {
                string relativePath = $"{module}/{configName}.json";
                string fullPath = HttpContext.Current.Server.MapPath(CONFIG_BASE_PATH + relativePath);
                return File.Exists(fullPath);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取配置文件的最后修改时间
        /// </summary>
        /// <param name="configName">配置名称</param>
        /// <param name="module">模块名称</param>
        /// <returns>最后修改时间</returns>
        public static DateTime? GetConfigLastModified(string configName, string module = "BBS")
        {
            try
            {
                string relativePath = $"{module}/{configName}.json";
                string fullPath = HttpContext.Current.Server.MapPath(CONFIG_BASE_PATH + relativePath);
                
                if (File.Exists(fullPath))
                {
                    return new FileInfo(fullPath).LastWriteTime;
                }
                
                return null;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 获取所有已加载的配置统计信息
        /// </summary>
        /// <returns>配置统计信息</returns>
        public static string GetAllConfigStats()
        {
            try
            {
                var stats = new List<string>();
                
                // 检查常用配置
                var commonConfigs = new[] { "IdentityConfigs", "MedalConfigs" };
                
                foreach (var configName in commonConfigs)
                {
                    bool exists = ConfigExists(configName);
                    var lastModified = GetConfigLastModified(configName);
                    string modifiedStr = lastModified?.ToString("yyyy-MM-dd HH:mm:ss") ?? "未知";
                    
                    stats.Add($"{configName}: {(exists ? "存在" : "不存在")}, 修改时间: {modifiedStr}");
                }
                
                stats.Add($"缓存统计: {GetCacheStats()}");
                
                return string.Join("\n", stats);
            }
            catch (Exception ex)
            {
                return $"获取配置统计失败: {ex.Message}";
            }
        }
    }
}