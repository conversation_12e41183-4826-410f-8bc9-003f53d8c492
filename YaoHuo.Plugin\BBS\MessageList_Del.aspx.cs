﻿using System;
using System.Linq;
using System.Web;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class MessageList_Del : MyPageWap
    {
        private string string_10 = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string linkURL = "";

        public string condition = "";

        public string ERROR = "";

        public string key = "";

        public string types = "";

        public string id = "";

        public string backurl = "";

        public string INFO = "";

        public string page = "";

        public string issystem = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            backurl = base.Request.QueryString.Get("backurl");
            id = base.Request.QueryString.Get("id");
            page = base.Request.QueryString.Get("page");
            types = base.Request.QueryString.Get("types");
            backurl = base.Request.QueryString.Get("backurl");
            issystem = base.Request.QueryString.Get("issystem");
            if (backurl == null || backurl == "")
            {
                backurl = base.Request.Form.Get("backurl");
            }
            if (backurl == null || backurl == "")
            {
                backurl = "myfile.aspx?siteid=" + siteid;
            }
            backurl = ToHtm(backurl);
            backurl = HttpUtility.UrlDecode(backurl);
            backurl = WapTool.URLtoWAP(backurl);
            if (!WapTool.IsNumeric(id))
            {
                id = "0";
            }
            IsLogin(userid, backurl);
            if (WapTool.GetArryString(siteVo.Version, '|', 53) == "1")
            {
                needPassWordToAdmin();
            }
            switch (action)
            {
                case "godelall":
                    godelall();
                    break;
                case "godelother":
                    godelother();
                    break;
                case "godel":
                    godel();
                    break;
            }
        }

        public void godelother()
        {
            // ✅ 使用DapperHelper安全获取消息发送者ID
            string connectionString = PubConstant.GetConnectionString(string_10);
            long messageId = DapperHelper.SafeParseLong(id, "消息ID");

            string selectSql = "SELECT userid FROM wap_message WHERE id = @MessageId";
            var userIdResult = DapperHelper.Query<long>(connectionString, selectSql, new {
                MessageId = messageId
            });
            long modelUserId = userIdResult.FirstOrDefault();

            // ✅ 使用DapperHelper进行安全的参数化删除操作
            string deleteSql = @"DELETE FROM wap_message
                                WHERE siteid = @SiteId
                                AND issystem <> 2
                                AND touserid = @UserId
                                AND userid   = @ModelUserId";
            DapperHelper.Execute(connectionString, deleteSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                ModelUserId = modelUserId
            });
            INFO = "OK";
        }

        public void godel()
        {
            // ✅ 使用DapperHelper进行安全的参数化删除操作
            string connectionString = PubConstant.GetConnectionString(string_10);
            string deleteSql = "DELETE FROM wap_message WHERE siteid = @SiteId AND touserid = @UserId AND id = @MessageId";
            DapperHelper.Execute(connectionString, deleteSql, new {
                SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                MessageId = DapperHelper.SafeParseLong(id, "消息ID")
            });
            INFO = "OK";
        }

        public void godelall()
        {
            try
            {
                string connectionString = PubConstant.GetConnectionString(string_10);
                long siteIdLong = DapperHelper.SafeParseLong(siteid, "站点ID");
                long userIdLong = DapperHelper.SafeParseLong(userid, "用户ID");

                if (types == "2")
                {
                    // ✅ 使用DapperHelper安全删除已发送消息
                    string deleteSql = "DELETE FROM wap_message WHERE siteid = @SiteId AND isnew = 2 AND issystem <> 2 AND touserid = @UserId";
                    DapperHelper.Execute(connectionString, deleteSql, new {
                        SiteId = siteIdLong,
                        UserId = userIdLong
                    });
                }
                else if (issystem == "")
                {
                    // ✅ 使用DapperHelper安全删除所有非系统消息
                    string deleteSql = "DELETE FROM wap_message WHERE siteid = @SiteId AND isnew < 2 AND issystem <> 2 AND touserid = @UserId";
                    DapperHelper.Execute(connectionString, deleteSql, new {
                        SiteId = siteIdLong,
                        UserId = userIdLong
                    });
                }
                else if (issystem == "0")
                {
                    // ✅ 使用DapperHelper安全删除用户消息
                    string deleteSql = "DELETE FROM wap_message WHERE siteid = @SiteId AND isnew < 2 AND issystem = 0 AND touserid = @UserId";
                    DapperHelper.Execute(connectionString, deleteSql, new {
                        SiteId = siteIdLong,
                        UserId = userIdLong
                    });
                }
                else if (issystem == "1")
                {
                    // ✅ 使用DapperHelper安全删除系统消息
                    string deleteSql = "DELETE FROM wap_message WHERE siteid = @SiteId AND isnew < 2 AND issystem = 1 AND touserid = @UserId";
                    DapperHelper.Execute(connectionString, deleteSql, new {
                        SiteId = siteIdLong,
                        UserId = userIdLong
                    });
                }
                else if (issystem == "2")
                {
                    // ✅ 使用DapperHelper安全删除管理消息
                    string deleteSql = "DELETE FROM wap_message WHERE siteid = @SiteId AND isnew < 2 AND issystem = 2 AND touserid = @UserId";
                    DapperHelper.Execute(connectionString, deleteSql, new {
                        SiteId = siteIdLong,
                        UserId = userIdLong
                    });
                }
                else if (issystem == "3")
                {
                    // 1. ✅ 使用DapperHelper安全标记消息为已读
                    string updateReadSql = "UPDATE wap_message SET isnew = 0 WHERE isnew = 1 AND siteid = @SiteId AND touserid = @UserId";
                    DapperHelper.Execute(connectionString, updateReadSql, new {
                        SiteId = siteIdLong,
                        UserId = userIdLong
                    });

                    // 2. ✅ 使用DapperHelper安全清理系统消息
                    string deleteSql = "DELETE FROM wap_message WHERE siteid = @SiteId AND isnew < 2 AND issystem = 1 AND touserid = @UserId";
                    DapperHelper.Execute(connectionString, deleteSql, new {
                        SiteId = siteIdLong,
                        UserId = userIdLong
                    });
                }
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = WapTool.ErrorToString(ex.ToString());
                INFO = "ERROR";
            }
        }
    }
}