﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Authorize.aspx.cs" Inherits="YaoHuo.Plugin.OAuth.Authorize" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权登录</title>

    <!-- 本地 Tailwind CSS -->
    <link href="/Template/CSS/output.css" rel="stylesheet">

    <!-- 本地 Lucide Icons -->
    <script src="/NetCSS/JS/BBS/Lucide.0.511.0.min.js"></script>

    <!-- 自定义样式 -->
    <style>
        .btn-danger-outline {
            border-color: #dc2626;
            color: #dc2626;
            background-color: transparent;
        }

        .btn-danger-outline:hover {
            border-color: #b91c1c;
            background-color: #fef2f2;
            color: #b91c1c;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 固定顶部导航栏 -->
        <header class="header">
            <div class="header-content">
                <div class="header-icon" onclick="history.back()">
                    <i data-lucide="arrow-left" class="w-6 h-6"></i>
                </div>
                <div class="header-title">授权登录</div>
                <div class="header-actions-right">
                    <!-- 可以添加帮助按钮等 -->
                </div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <div class="main-content">
            <%
            string htmlContent = strhtml.ToString();

            // 检查是否是错误页面
            if (htmlContent.Contains("授权错误"))
            {
                %>
                <!-- 错误页面 -->
                <div class="card">
                    <div class="card-body text-center py-8">
                        <i data-lucide="shield-x" class="w-16 h-16 text-danger mx-auto mb-4"></i>
                        <h2 class="text-xl font-semibold text-text-primary mb-3">授权失败</h2>

                        <%
                        // 提取错误信息
                        string errorMsg = htmlContent;
                        if (errorMsg.Contains("错误："))
                        {
                            int startIndex = errorMsg.IndexOf("错误：") + 3;
                            int endIndex = errorMsg.IndexOf("</div>", startIndex);
                            if (endIndex > startIndex)
                            {
                                errorMsg = errorMsg.Substring(startIndex, endIndex - startIndex).Trim();
                            }
                        }
                        %>

                        <div class="message error mb-6">
                            <i data-lucide="alert-circle" class="w-4 h-4 inline mr-2"></i>
                            <%=errorMsg%>
                        </div>

                        <%
                        // 检查是否有返回应用的链接
                        if (htmlContent.Contains("返回应用"))
                        {
                            // 提取返回链接
                            int linkStart = htmlContent.IndexOf("href='") + 6;
                            int linkEnd = htmlContent.IndexOf("'", linkStart);
                            if (linkEnd > linkStart)
                            {
                                string returnUrl = htmlContent.Substring(linkStart, linkEnd - linkStart);
                                %>
                                <div class="grid-2">
                                    <button type="button" class="btn btn-outline" onclick="history.back()">
                                        <i data-lucide="arrow-left" class="w-4 h-4 mr-1"></i>
                                        返回
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="window.location.href='<%=HttpUtility.HtmlEncode(returnUrl)%>'">
                                        <i data-lucide="external-link" class="w-4 h-4 mr-1"></i>
                                        返回应用
                                    </button>
                                </div>
                                <%
                            }
                        }
                        else
                        {
                            %>
                            <button type="button" class="btn btn-outline" onclick="history.back()">
                                <i data-lucide="arrow-left" class="w-4 h-4 mr-1"></i>
                                返回
                            </button>
                            <%
                        }
                        %>
                    </div>
                </div>
                <%
            }
            else if (htmlContent.Contains("OAuth 2.0 授权确认"))
            {
                // 授权确认页面
                // 从后端HTML中提取应用信息
                string appName = "";
                string appDesc = "";
                string scope = "";
                string redirectUri = "";

                // 提取应用名称
                int appNameStart = htmlContent.IndexOf("应用名称：");
                if (appNameStart > -1) {
                    appNameStart += 5;
                    int appNameEnd = htmlContent.IndexOf("</p>", appNameStart);
                    if (appNameEnd > appNameStart) {
                        appName = htmlContent.Substring(appNameStart, appNameEnd - appNameStart);
                        appName = System.Text.RegularExpressions.Regex.Replace(appName, "<[^>]*>", "");
                        appName = HttpUtility.HtmlDecode(appName).Trim();
                    }
                }

                // 提取应用说明
                int appDescStart = htmlContent.IndexOf("应用说明：");
                if (appDescStart > -1) {
                    appDescStart += 5;
                    int appDescEnd = htmlContent.IndexOf("</p>", appDescStart);
                    if (appDescEnd > appDescStart) {
                        appDesc = htmlContent.Substring(appDescStart, appDescEnd - appDescStart);
                        appDesc = System.Text.RegularExpressions.Regex.Replace(appDesc, "<[^>]*>", "");
                        appDesc = HttpUtility.HtmlDecode(appDesc).Trim();
                    }
                }

                // 提取权限范围
                int scopeStart = htmlContent.IndexOf("请求权限：");
                if (scopeStart > -1) {
                    scopeStart += 5;
                    int scopeEnd = htmlContent.IndexOf("</p>", scopeStart);
                    if (scopeEnd > scopeStart) {
                        scope = htmlContent.Substring(scopeStart, scopeEnd - scopeStart);
                        scope = System.Text.RegularExpressions.Regex.Replace(scope, "<[^>]*>", "");
                        scope = HttpUtility.HtmlDecode(scope).Trim();
                    }
                }

                // 提取重定向地址
                int redirectStart = htmlContent.IndexOf("重定向地址：");
                if (redirectStart > -1) {
                    redirectStart += 6;
                    int redirectEnd = htmlContent.IndexOf("</p>", redirectStart);
                    if (redirectEnd > redirectStart) {
                        redirectUri = htmlContent.Substring(redirectStart, redirectEnd - redirectStart);
                        redirectUri = System.Text.RegularExpressions.Regex.Replace(redirectUri, "<[^>]*>", "");
                        redirectUri = HttpUtility.HtmlDecode(redirectUri).Trim();
                    }
                }

                // 提取表单的隐藏字段值
                string clientId = "";
                string responseType = "";
                string state = "";
                string codeChallenge = "";
                string codeChallengeMethod = "";
                // 移除 csrfToken 变量 - 不再需要 CSRF 验证

                // 提取 client_id
                var clientIdMatch = System.Text.RegularExpressions.Regex.Match(htmlContent, "name='client_id'\\s+value='([^']*)'");
                if (clientIdMatch.Success) {
                    clientId = HttpUtility.HtmlDecode(clientIdMatch.Groups[1].Value);
                }

                // 提取 response_type
                var responseTypeMatch = System.Text.RegularExpressions.Regex.Match(htmlContent, "name='response_type'\\s+value='([^']*)'");
                if (responseTypeMatch.Success) {
                    responseType = HttpUtility.HtmlDecode(responseTypeMatch.Groups[1].Value);
                }

                // 提取 state
                var stateMatch = System.Text.RegularExpressions.Regex.Match(htmlContent, "name='state'\\s+value='([^']*)'");
                if (stateMatch.Success) {
                    state = HttpUtility.HtmlDecode(stateMatch.Groups[1].Value);
                }

                // 提取 code_challenge
                var codeChallengeMatch = System.Text.RegularExpressions.Regex.Match(htmlContent, "name='code_challenge'\\s+value='([^']*)'");
                if (codeChallengeMatch.Success) {
                    codeChallenge = HttpUtility.HtmlDecode(codeChallengeMatch.Groups[1].Value);
                }

                // 提取 code_challenge_method
                var codeChallengeMethodMatch = System.Text.RegularExpressions.Regex.Match(htmlContent, "name='code_challenge_method'\\s+value='([^']*)'");
                if (codeChallengeMethodMatch.Success) {
                    codeChallengeMethod = HttpUtility.HtmlDecode(codeChallengeMethodMatch.Groups[1].Value);
                }

                // 移除 csrf_token 提取 - 不再需要 CSRF 验证

                // 计算重定向域名，供友好显示（协议+域名）
                string redirectDisplay = string.Empty;
                try
                {
                    if (!string.IsNullOrEmpty(redirectUri))
                    {
                        Uri uri = new Uri(redirectUri);
                        redirectDisplay = uri.Scheme + "://" + uri.Host;
                        if (!uri.IsDefaultPort)
                        {
                            redirectDisplay += ":" + uri.Port;
                        }
                    }
                }
                catch
                {
                    // ignore parsing errors and keep original
                    redirectDisplay = redirectUri;
                }
                %>

                <!-- 授权登录卡片 -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <i data-lucide="shield-check" class="card-icon"></i>
                            授权登录
                        </h2>
                    </div>
                    <div class="card-body">
                        <!-- 应用基本信息 -->
                        <div class="flex items-start space-x-2 mb-6">
                            <div class="w-12 h-12 bg-primary-alpha-10 rounded-xl flex items-center justify-center">
                                <i data-lucide="app-window" class="w-6 h-6 text-primary"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-text-primary"><%=HttpUtility.HtmlEncode(appName)%></h3>
                                <% if (!string.IsNullOrEmpty(appDesc) && appDesc != "无") { %>
                                <p class="text-sm text-text-secondary"><%=HttpUtility.HtmlEncode(appDesc)%></p>
                                <% } %>
                            </div>
                        </div>

                        <!-- 登录后跳转信息 -->
                        <div class="space-y-3 mb-6">
                            <div class="flex items-start text-sm">
                                <i data-lucide="link" class="w-4 h-4 text-text-secondary mr-1 mt-0.5"></i>
                                <span class="text-text-secondary w-24">登录后跳转至:</span>
                                <a href="<%=HttpUtility.HtmlEncode(redirectUri)%>" class="dynamic-link break-all"><%=HttpUtility.HtmlEncode(redirectDisplay)%></a>
                            </div>
                        </div>

                        <!-- 权限说明 -->
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                            <div class="flex items-start space-x-3">
                                <i data-lucide="info" class="w-5 h-5 text-blue-600 mt-0.5"></i>
                                <div>
                                    <h4 class="text-sm font-medium text-blue-900 mb-2">该应用将获得以下权限：</h4>
                                    <div class="text-sm text-blue-800">
                                        查看您的ID、昵称、等级
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 授权决定表单 -->
                        <form method="post" action="Authorize.aspx" id="authForm">
                            <!-- 隐藏字段 -->
                            <input type="hidden" name="response_type" value="<%=HttpUtility.HtmlEncode(responseType)%>" />
                            <input type="hidden" name="client_id" value="<%=HttpUtility.HtmlEncode(clientId)%>" />
                            <input type="hidden" name="redirect_uri" value="<%=HttpUtility.HtmlEncode(redirectUri)%>" />
                            <input type="hidden" name="scope" value="<%=HttpUtility.HtmlEncode(scope)%>" />
                            <input type="hidden" name="state" value="<%=HttpUtility.HtmlEncode(state)%>" />
                            <input type="hidden" name="code_challenge" value="<%=HttpUtility.HtmlEncode(codeChallenge)%>" />
                            <input type="hidden" name="code_challenge_method" value="<%=HttpUtility.HtmlEncode(codeChallengeMethod)%>" />
                            <input type="hidden" name="action" value="authorize" />
                            <input type="hidden" name="decision" id="decisionInput" value="" />

                            <div class="grid-2">
                                <button type="button" class="btn btn-outline btn-danger-outline"
                                        onclick="submitDecision('deny')">
                                    <i data-lucide="x" class="w-4 h-4 mr-1"></i>
                                    拒绝授权
                                </button>
                                <button type="button" class="btn btn-primary"
                                        onclick="submitDecision('allow')">
                                    <i data-lucide="user-check" class="w-4 h-4 mr-1"></i>
                                    同意授权
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                <%
            }
            %>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化 Lucide 图标
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });

        // 提交授权决定
        function submitDecision(decision) {
            const decisionInput = document.getElementById('decisionInput');
            const authForm = document.getElementById('authForm');

            if (decisionInput && authForm) {
                decisionInput.value = decision;

                // 添加加载状态
                const submitBtn = event.target;
                const originalText = submitBtn.innerHTML;
                submitBtn.disabled = true;

                if (decision === 'allow') {
                    submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-1 animate-spin"></i>授权中...';
                } else {
                    submitBtn.innerHTML = '<i data-lucide="loader-2" class="w-4 h-4 mr-1 animate-spin"></i>处理中...';
                }

                // 重新初始化图标
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }

                // 提交表单
                authForm.submit();
            }
        }
    </script>
</body>
</html>