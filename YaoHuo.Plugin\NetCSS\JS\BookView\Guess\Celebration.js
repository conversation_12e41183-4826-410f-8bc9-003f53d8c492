// 确保 confetti 函数存在
if (typeof window.confetti !== 'function') {
    console.error('Confetti 库未加载，请检查本地文件路径。');
}

function celebrate() {
    if (typeof window.confetti === 'function') {
        var end = Date.now() + (4 * 1000);

        (function frame() {
            confetti({
                particleCount: 2,
                angle: 60,
                spread: 55,
                origin: { x: 0 },
                colors: ['#ff0000', '#ff7f00', '#ffff00', '#00ff00', '#0000ff', '#8b00ff']
            });
            confetti({
                particleCount: 2,
                angle: 120,
                spread: 55,
                origin: { x: 1 },
                colors: ['#ff0000', '#ff7f00', '#ffff00', '#00ff00', '#0000ff', '#8b00ff']
            });

            if (Date.now() < end) {
                requestAnimationFrame(frame);
            }
        }());
    } else {
        console.info('Confetti 函数不可用，跳过');
    }
}

function rainbow() {
    if (typeof window.confetti === 'function') {
        var end = Date.now() + (7 * 1000);
        var colors = ['#ff0000', '#ff7f00', '#ffff00', '#00ff00', '#0000ff', '#8b00ff', '#ff69b4', '#00ffff', '#ff1493', '#7fff00'];

        (function frame() {
            confetti({
                particleCount: 3, // 增加每次喷出的纸屑数量
                angle: 60,
                spread: 55,
                origin: { x: 0 },
                colors: [colors[Math.floor(Math.random() * colors.length)]]
            });
            confetti({
                particleCount: 3, // 增加每次喷出的纸屑数量
                angle: 120,
                spread: 55,
                origin: { x: 1 },
                colors: [colors[Math.floor(Math.random() * colors.length)]]
            });

            if (Date.now() < end) {
                requestAnimationFrame(frame);
            }
        }());
    } else {
        console.info('Confetti 函数不可用，跳过');
    }
}