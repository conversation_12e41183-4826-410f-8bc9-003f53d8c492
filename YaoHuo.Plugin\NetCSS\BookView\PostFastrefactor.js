//asyncComment函数
function asyncComment() {
    let form = document.getElementsByName('f')[0];
    if (!form) return;

    let submitBtn = form.querySelector('[name="g"]');
    if (!submitBtn) return;

    submitBtn.onclick = function (e) {
        e.preventDefault();
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        data.g = '快速回复';

        if (data.content.length > 0) {
            processContent(data);
            postComment('/bbs/book_re_QQ.aspx', data);
        }
    }
}

function processContent(data) {
    data.content = data.content.replace(/\n/g, '[br]').replace(/\$\(/g, '$ (');
    try {
        sendFriendMsg();
    } catch (error) {
        console.error('Error in sendFriendMsg:', error);
    }
}

function postComment(url, data) {
    fetch(url, {
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8;'},
        body: new URLSearchParams(data)
    })
    .then(res => res.text())
    .then(handleCommentResponse)
    .catch(error => console.error('Error during fetch:', error));
}

function handleCommentResponse(html) {
    let recontenttip = /class="tip">(.*?)<\/div>/.exec(html)[1];
    let timeoutDuration = 800;
    let tip = '';

    if (recontenttip.includes('回复成功')) {
        let [yaogem, exp] = extractRewardDetails(recontenttip);
        tip = createSuccessAlert(yaogem, exp);
        resetEmoticonOptions();
        document.getElementsByName("content")[0].value = '';
    } else {
        [tip, timeoutDuration] = createFailureAlert(recontenttip);
    }

    updateRecontentWithTip(tip, timeoutDuration);
}

function extractRewardDetails(recontenttip) {
    let successMatch = /获得妖晶:(\d+)，获得经验:(\d+)/.exec(recontenttip);
    if (successMatch) {
        return [successMatch[1], successMatch[2]];
    }
    return [0, 0];
}

function createSuccessAlert(yaogem, exp) {
    return `<div class="ui__alert"><div class="ui__alert_bg in"></div> <div class="ui__alert_content in"> <div class="ui__content_body"><h4 class="ui__title">回复成功</h4><div>获得妖晶${yaogem}，经验${exp}</div> </div></div></div>`;
}

function resetEmoticonOptions() {
    emoticonContainer.style.display = "none";
    selectOptions.style.display = "block";
    document.querySelectorAll(".select_option input.select_input").forEach(input => input.checked = false);
}

function createFailureAlert(recontenttip) {
    if (recontenttip.includes('请不要发重复内容')) {
        return [createAlertTip('请不要发重复内容', 1200), 1200];
    } else if (recontenttip.includes('请再过')) {
        let detail = /请再过(.+?)秒后操作/.exec(recontenttip)[1];
        return [`<div class="ui__alert"><div class="ui__alert_bg in"></div> <div class="ui__alert_content in"> <div class="ui__content_body"><h4 class="ui__title">请再过</h4><div>${detail}</div> </div></div></div>`, 2000];
    } else if (recontenttip.includes('今天已达回帖上限')) {
        let detail = /今天已达回帖上限 (.+?) 条/.exec(recontenttip)[1];
        return [`<div class="ui__alert"><div class="ui__alert_bg in"></div> <div class="ui__alert_content in"> <div class="ui__content_body"><h4 class="ui__title">今天已达回帖上限</h4><div>${detail}</div> </div></div></div>`, 2000];
    } else if (recontenttip.includes('取到非法值')) {
        return [createAlertTip('禁止提交 $(', 5000), 5000];
    } else if (recontenttip.includes('您已被加入黑名单')) {
        return [createAlertTip('您已被加入黑名单', 5000), 5000];
    }
    return ['', 800];
}


function createAlertTip(recontenttip, message, timeoutDuration) {
    let detail = /(.+?)字/.exec(recontenttip)[1];
    return `<div class="ui__alert"><div class="ui__alert_bg in"></div> <div class="ui__alert_content in"> <div class="ui__content_body"><h4 class="ui__title">${message}</h4><div>${detail}</div> </div></div></div>`;
}

function updateRecontentWithTip(tip, timeoutDuration) {
    let recontent = document.getElementsByClassName('recontent')[0];
    const data = Object.fromEntries(new FormData(document.getElementsByName('f')[0]).entries());
    fetch(`/bbs-${data.id}.html`)
    .then(res => res.text())
    .then(html => {
        let newcontent = /recontent">([\s\S]*?)<div class="more"/.exec(html)[1];
        recontent.innerHTML = `<div id="retip">${tip}</div>${newcontent}`;
        applyNewContentUpdates(timeoutDuration);
    });
}

function applyNewContentUpdates(timeoutDuration) {
    if (isCustomLayoutEnabled) applyNewLayoutToNewContent();
    replyAny();
    updateTextElements();
    clearTipAfterTimeout(timeoutDuration);
}

// 更新文本元素处理
function updateTextElements() {
    document.querySelectorAll(".retext").forEach(element => processTextContent(element));
}

// 在一定时间后清除提示
function clearTipAfterTimeout(timeoutDuration) {
    const retip = document.getElementById('retip');
    let hideTipTimeout = setTimeout(() => {
        if (hideTipTimeout) {
            retip.style.display = 'none';
            clearTimeout(hideTipTimeout);
        }
    }, timeoutDuration);
    retip.querySelector('.ui__alert_bg').addEventListener('click', () => retip.style.display = 'none');
    retip.querySelector('.ui__title').addEventListener('click', () => retip.style.display = 'none');
}

//sticky函数
function sticky() {
    let content = document.querySelector(".viewContent");
    let form = document.querySelector("form[name='f']");
    if (content && form) {
        moveFormToTop(content, form);
    }
}

function moveFormToTop(content, form) {
    // 确保表单未被粘性定位
    if (!form.parentElement || !form.parentElement.classList.contains("sticky")) {
        let stickyDiv = document.createElement("div");
        stickyDiv.classList.add("sticky");
        content.insertBefore(stickyDiv, content.firstChild);
        stickyDiv.appendChild(form);
        addReplyListener(stickyDiv, form);
    }
}

function addReplyListener(stickyDiv, form) {
    document.getElementsByClassName('recontent')[0].addEventListener('click', function (event) {
        let replyLinkElement = findParentReplyLink(event.target);
        if (replyLinkElement) {
            event.preventDefault();
            processReplyLink(replyLinkElement, stickyDiv, form);
        }
    });
}

function findParentReplyLink(element) {
    while (element) {
        if (element.tagName.toLowerCase() === 'a' && (element.classList.contains('replyicon') || element.classList.contains('replyme'))) {
            return element;
        }
        element = element.parentElement;
    }
    return null;
}

function processReplyLink(replyLinkElement, stickyDiv, form) {
    let replyLink = replyLinkElement.href;
    let reply = /reply=\d+/.exec(replyLink)[0].slice(6);
    let touserid = /touserid=\d+/.exec(replyLink)[0].slice(9);

    toggleStickyForm(stickyDiv, form, reply, touserid);
}

function toggleStickyForm(stickyDiv, form, reply, touserid) {
    if (!stickyDiv.classList.contains('active')) {
        stickyDiv.classList.add('active');
        stickyDiv.style.cssText = "position: sticky; top: 0;";
        if (form.firstChild.tagName === "B") {
            clearFormFields(form);
        }
        prependReplyInfo(form, reply, touserid);
    } else {
        stickyDiv.classList.remove('active');
        clearFormFields(form);
        stickyDiv.style.cssText = "";
    }
}

function clearFormFields(form) {
    while (form.firstChild.tagName === "B" || form.firstChild.tagName === "SELECT") {
        form.removeChild(form.firstChild);
    }
    let hiddenInputs = form.querySelectorAll('input[type="hidden"]');
    hiddenInputs.forEach(input => form.removeChild(input));
}

function prependReplyInfo(form, reply, touserid) {
    form.insertAdjacentHTML("afterbegin", `<b>回复${reply}楼</b><select style="display: none;" name="sendmsg2"><option value="1">通知对方</option><option value="0">不予通知</option></select>`);
    form.insertAdjacentHTML("beforeend", `<input type="hidden" name="reply" value="${reply}"><input type="hidden" name="touserid" value="${touserid}">`);
}

//asyncReply函数
function asyncReply() {
    let more = document.querySelector(".more");
    if (more) {
        setupPagination(more);
    }
}

function setupPagination(more) {
    let moreLink = more.querySelector("a");
    let totalpage = Math.ceil(/getTotal=\d+/.exec(moreLink.href)[0].slice(9) / 15);
    let currpage = 1;

    let newMoreLink = createPaginationLink("加载更多", currpage, totalpage, moreLink.href);
    more.appendChild(newMoreLink);

    newMoreLink.addEventListener("click", function() {
        YH_show_next(totalpage, 15, currpage, moreLink.href, "page");
    });
}

function createPaginationLink(text, currpage, totalpage, tourl) {
    let newMoreLink = document.createElement("a");
    newMoreLink.innerHTML = `<span id="YH_show_tip">${text}(${currpage}/${totalpage})</span>`;
    newMoreLink.href = "javascript:void(0);";
    newMoreLink.style.width = "100%";
    newMoreLink.style.textAlign = "center";
    return newMoreLink;
}

function YH_show_next(total, pagesize, currpage, tourl, pagetype) {
    let nextPage = currpage + 1;
    if (nextPage <= total) {
        tourl = tourl.replace("&" + pagetype + "=" + currpage, "&" + pagetype + "=" + nextPage);
        LoadXML_Async(tourl);
    }
}

function LoadXML_Async(tourl) {
    let xmlhttp;
    if (window.XMLHttpRequest) {
        xmlhttp = new XMLHttpRequest();
    } else if (window.ActiveXObject) {
        xmlhttp = new ActiveXObject("Microsoft.XMLHTTP");
    }

    if (xmlhttp) {
        xmlhttp.onreadystatechange = function() {
            if (xmlhttp.readyState === 4 && xmlhttp.status === 200) {
                updateContent(xmlhttp.responseText);
            }
        };
        xmlhttp.open("GET", tourl, true);
        xmlhttp.send();
    }
}

function updateContent(responseText) {
    let start = responseText.indexOf("<!--listS-->");
    let end = responseText.indexOf("<!--listE-->");
    if (start >= 0 && end >= 0) {
        responseText = responseText.substring(start + 12, end);
        let recontentElement = document.getElementsByClassName("recontent")[0];
        recontentElement.innerHTML += responseText;

        if (isCustomLayoutEnabled) applyNewLayoutToNewContent();
        replyAny();
        processTextContentInNewContent(recontentElement);
    }
}

function processTextContentInNewContent(container) {
    let textElements = container.querySelectorAll(".retext");
    textElements.forEach(element => processTextContent(element));
}



//replyAny函数
let sticked = false; // 标记是否已粘贴
let replyAnyBound = false; // 标记是否已绑定回复事件

function replyAny() {
    if (!replyAnyBound) {
        let sticky = document.getElementsByClassName("sticky")[0];
        let recontent = document.getElementsByClassName("recontent")[0];
        recontent.addEventListener('click', function (event) {
            let target = event.target;
            let replyLinkElement = findParentReplyLink(target);
            if (replyLinkElement) {
                event.preventDefault();
                handleReplyLink(replyLinkElement, sticky);
            }
        });
        replyAnyBound = true;
    }
}

function findParentReplyLink(element) {
    while (element) {
        if (element.tagName.toLowerCase() === 'a' && (element.classList.contains('replyicon') || element.classList.contains('replyme'))) {
            return element;
        }
        element = element.parentElement;
    }
    return null;
}

function handleReplyLink(replyLinkElement, stickyDiv) {
    let reply = /reply=\d+/.exec(replyLinkElement.href)[0].slice(6);
    let touserid = /touserid=\d+/.exec(replyLinkElement.href)[0].slice(9);
    toggleStickyForm(stickyDiv, reply, touserid);
}

function toggleStickyForm(stickyDiv, reply, touserid) {
    if (!sticked) {
        stickyDiv.style.cssText = "position: sticky; top: 0;";
        sticked = true;
        let form = document.querySelector("form[name='f']");
        clearFormFields(form);
        prependReplyInfo(form, reply, touserid);
    } else {
        stickyDiv.style.cssText = "";
        sticked = false;
    }
}

function clearFormFields(form) {
    // 移除所有B标签和隐藏的输入字段
    while (form.firstChild.tagName === "B" || form.firstChild.tagName === "SELECT" || form.firstChild.tagName === "INPUT") {
        form.removeChild(form.firstChild);
    }
}

function prependReplyInfo(form, reply, touserid) {
    let replyInfoHtml = `<b>回复${reply}楼</b><select name="sendmsg2" style="display: none;"><option value="1">通知对方</option><option value="0">不予通知</option></select>`;
    let hiddenFieldsHtml = `<input type="hidden" name="reply" value="${reply}"><input type="hidden" name="touserid" value="${touserid}">`;
    form.insertAdjacentHTML("afterbegin", replyInfoHtml + hiddenFieldsHtml);
}

//加载更多部分
var YH_now_currpage = 0;
var YH_page_total = 0;
var YH_scoll_downEnd = 0;

function YH_show_next(YH_total, YH_pagesize, YH_currpage, tourl, pagetype) {
    var total = parseInt(YH_total);
    YH_page_total = total;
    var pagesize = parseInt(YH_pagesize);
    var currpage = parseInt(YH_currpage);

    if (YH_now_currpage === 0) {
        YH_now_currpage = currpage + 1;
    } else {
        YH_now_currpage += 1;
    }

    document.getElementById("YH_show_loadimg").innerHTML = '<span id="loadimg"></span>';
    document.getElementById("YH_show_tip").innerHTML = "正在加载(" + YH_now_currpage + "/" + total + ")";

    if (YH_now_currpage > total) {
        document.getElementById("YH_show_loadimg").innerHTML = "";
        document.getElementById("YH_show_tip").innerHTML = "没有更多了";
    } else {
        tourl = tourl + "&" + pagetype + "=" + YH_now_currpage;
        LoadXML_Async(tourl);
    }
}

function LoadXML_Async(tourl) {
    xmlhttp = new XMLHttpRequest();
    xmlhttp.onreadystatechange = YH_CallBack;
    xmlhttp.open("GET", tourl, true);
    xmlhttp.send(null);
}

function YH_CallBack() {
    if (xmlhttp.readyState == 4) {
        if (xmlhttp.status == 200) {
            var responseText = xmlhttp.responseText;
            var start = responseText.indexOf("<!--listS-->");
            var end = responseText.indexOf("<!--listE-->");

            if (start >= 0 && end >= 0) {
                responseText = responseText.substring(start + 12, end);
                var recontentElement = document.getElementsByClassName("recontent")[0];
                recontentElement.innerHTML += responseText;

                if (isCustomLayoutEnabled) {
                    applyNewLayoutToNewContent();
                }
                replyAny();
                updateTextElements(recontentElement);
            }

            updatePaginationInfo();
        }
    }
}

function updatePaginationInfo() {
    if (YH_now_currpage == YH_page_total) {
        document.getElementById("YH_show_loadimg").innerHTML = "";
        document.getElementById("YH_show_tip").innerHTML = "没有更多了";
    } else {
        document.getElementById("YH_show_loadimg").innerHTML = "";
        document.getElementById("YH_show_tip").innerHTML = "加载更多(" + YH_now_currpage + "/" + YH_page_total + ")";
    }
}

function updateTextElements(container) {
    let textElements = container.querySelectorAll(".retext");
    textElements.forEach(element => {
        processTextContent(element);
    });
}

//新布局相关的函数
function applyNewLayoutToNewContent() {
    const newElements = document.querySelectorAll('.list-reply');
    newElements.forEach(newElement => {
        const data = extractData(newElement); // 此函数在其他脚本中定义
        const newLayout = buildNewLayout(data); // 此函数在其他脚本中定义
        replaceContent(newElement, newLayout); // 此函数在其他脚本中定义
    });

    const textContentElements = document.querySelectorAll(".bbscontent, .bubble, .retext");
    textContentElements.forEach(element => {
        processTextContent(element); // 此函数在其他脚本中定义
    });
    addDropdownTouchSupport(); // 此函数在其他脚本中定义
    replyAny(); // 此函数在其他脚本中定义
}

function applyHoverEffectToNewContent() {
    document.querySelectorAll('.dropdown').forEach(dropDownElem => {
        var dropdownContent = dropDownElem.querySelector('.dropdown-content');
        if (dropdownContent) {
            dropDownElem.addEventListener('mouseover', () => {
                dropdownContent.style.display = 'block';
            });
            dropDownElem.addEventListener('mouseout', () => {
                dropdownContent.style.display = 'none';
            });
        }
    });
}

function onMoreContentLoaded() {
    applyHoverEffectToNewContent();
    document.querySelectorAll('.dropdown').forEach(dropDownElem => {
        dropDownElem.classList.remove('hover-effect');
        void dropDownElem.offsetWidth;
        dropDownElem.classList.add('hover-effect');
    });
    if (isCustomLayoutEnabled) { // 此变量在其他脚本中定义
        applyNewLayoutToNewContent();
    }
    replyAny(); // 此函数在其他脚本中定义
}

//剩余部分
document.addEventListener("DOMContentLoaded", function() {
    adjustTextareaHeight();
    initializePage();
});

function adjustTextareaHeight() {
    var textareas = document.getElementsByTagName("textarea");
    for (var i = 0; i < textareas.length; i++) {
        textareas[i].addEventListener("input", autoAdjustHeight);
        autoAdjustHeight.call(textareas[i]);
    }
}

function autoAdjustHeight() {
    this.style.height = "auto";
    this.style.height = this.scrollHeight + "px";
}

function initializePage() {
    sticky();
    asyncReply();
    replyAny();
    asyncComment();
    resetReplyAnyListener();
}

function resetReplyAnyListener() {
    var recontent = document.getElementsByClassName("recontent")[0];
    if (recontent) {
        recontent.removeEventListener('click', replyAny);
        replyAny();
    }
}
