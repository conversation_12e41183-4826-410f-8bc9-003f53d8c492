#!/usr/bin/env node
/**
 * watch-with-version.js - 带版本更新的 Tailwind CSS 监听脚本
 * 在文件变化时自动更新CSS版本号
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 启动带版本更新的 Tailwind CSS 监听模式...');

// 启动 Tailwind CSS watch 进程
// 使用 npm run 命令，跨平台兼容
const tailwindProcess = spawn('npm', ['run', 'watch:tailwind'], {
    stdio: 'inherit',
    cwd: __dirname,
    shell: true // 确保在所有平台上都能正常工作
});

// 监听输出CSS文件变化
const outputCSSPath = path.resolve(__dirname, '../Template/CSS/output.css');

let isUpdating = false;

const updateVersion = () => {
    if (isUpdating) return;
    isUpdating = true;
    
    setTimeout(() => {
        try {
            console.log('📝 检测到CSS文件变化，更新版本号...');
            require('./update-css-version.js');
        } catch (error) {
            console.error('❌ 版本更新失败:', error.message);
        } finally {
            isUpdating = false;
        }
    }, 500); // 延迟500ms，确保文件写入完成
};

// 监听CSS输出文件
if (fs.existsSync(outputCSSPath)) {
    fs.watchFile(outputCSSPath, { interval: 1000 }, updateVersion);
    console.log('👀 正在监听CSS文件变化...');
} else {
    console.log('⚠️  CSS输出文件不存在，将在首次构建后开始监听');
    // 等待文件创建
    const checkFile = setInterval(() => {
        if (fs.existsSync(outputCSSPath)) {
            fs.watchFile(outputCSSPath, { interval: 1000 }, updateVersion);
            console.log('👀 开始监听CSS文件变化...');
            clearInterval(checkFile);
        }
    }, 1000);
}

// 处理进程退出
process.on('SIGINT', () => {
    console.log('\n🛑 停止监听...');
    if (fs.existsSync(outputCSSPath)) {
        fs.unwatchFile(outputCSSPath);
    }
    tailwindProcess.kill();
    process.exit(0);
});

process.on('SIGTERM', () => {
    tailwindProcess.kill();
    process.exit(0);
});

tailwindProcess.on('close', (code) => {
    console.log(`Tailwind CSS 进程退出，代码: ${code}`);
    process.exit(code);
});
