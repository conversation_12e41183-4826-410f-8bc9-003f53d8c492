﻿using System;
using System.Data;
using System.Data.SqlClient;
using KeLin.ClassManager;
using KeLin.ClassManager.ExUtility;
using YaoHuo.Plugin.WebSite;

namespace YaoHuo.Plugin
{
	public class WapLogout : MyPageWap
    {
		public string isGO = "";

		public static string _InstanceName = PubConstant.GetAppString("InstanceName");

		public static string _ConnStr = PubConstant.GetConnectionString(_InstanceName);

		protected void Page_Load(object sender, EventArgs e)
		{
			IsLogin(userid, "waplogout.aspx?siteid=" + siteid);
			isGO = GetRequestValue("isGO");
			if (isGO == "OK")
			{
				// 使用参数化查询删除在线记录
				string deleteFcountSql = "DELETE FROM [fcount] WHERE userid = @userid";
				SqlParameter[] deleteFcountParams = {
					new SqlParameter("@userid", SqlDbType.BigInt) { Value = long.Parse(userid) }
				};
				DbHelperSQL.ExecuteNonQuery(_ConnStr, CommandType.Text, deleteFcountSql, deleteFcountParams);

				// 使用参数化查询清空会话超时标识
				string updateUserSql = "UPDATE [user] SET SidTimeOut = NULL WHERE userid = @userid";
				SqlParameter[] updateUserParams = {
					new SqlParameter("@userid", SqlDbType.BigInt) { Value = long.Parse(userid) }
				};
				DbHelperSQL.ExecuteNonQuery(_ConnStr, CommandType.Text, updateUserSql, updateUserParams);

				string text = base.Request.ServerVariables["HTTP_HOST"].Split('.')[0];
				base.Response.Cookies["sid" + text].Value = null;
				base.Response.Cookies["GET" + userid].Value = null;
				Session["sid1"] = "";
				Session["check_userid"] = null;
				Session["KL_LOGIN_IS_ADMIN" + userid] = null;
				base.Response.Redirect(http_start + "WapLogin.aspx");
			}
		}
	}
}