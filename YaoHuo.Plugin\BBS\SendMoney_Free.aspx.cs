﻿using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using System;
using Dapper;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class SendMoney_Free : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string reid = "";

        public string page = "";

        public string lpage = "";

        public string ot = "";

        public string INFO = "";

        public string ERROR = "";

        public wap_bbsre_Model bbsReVo = null;

        public wap_bbs_Model bbsVo = null;

        public string touserid = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
            }
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            reid = GetRequestValue("reid");
            page = GetRequestValue("page");
            lpage = GetRequestValue("lpage");
            ot = GetRequestValue("ot");
            touserid = GetRequestValue("touserid");
            if (!WapTool.IsNumeric(touserid))
            {
                touserid = "0";
            }
            IsCheckManagerLvl("|00|01|03|04|", classVo.adminusername, GetUrlQueryString());
            needPassWordToAdmin();
            if (!(action == "gomod"))
            {
                return;
            }
            try
            {
                string requestValue = GetRequestValue("sendmoney");
                if (!WapTool.IsNumeric(requestValue) || touserid == "0" || touserid == userVo.userid.ToString())
                {
                    INFO = "ERR";
                    return;
                }
                if (long.Parse(requestValue) < 1L)
                {
                    INFO = "ERR";
                    return;
                }
                if (userVo.money < long.Parse(requestValue))
                {
                    INFO = "ERR";
                    return;
                }
                // ✅ 使用TransactionHelper进行安全的事务性资金操作
                string connectionString = PubConstant.GetConnectionString(a);
                long moneyAmount = long.Parse(requestValue);
                long userIdLong = DapperHelper.SafeParseLong(userid, "用户ID");
                long toUserIdLong = DapperHelper.SafeParseLong(touserid, "目标用户ID");
                long replyIdLong = DapperHelper.SafeParseLong(reid, "回复ID");

                TransactionHelper.ExecuteMoneyTransaction(connectionString, (connection, transaction) =>
                {
                    // 1. 扣除发送者金币
                    string deductSql = "UPDATE [user] SET money = money - @Amount WHERE userid = @UserId";
                    connection.Execute(deductSql, new {
                        Amount = moneyAmount,
                        UserId = userIdLong
                    }, transaction);

                    // 2. 增加接收者金币
                    string addSql = "UPDATE [user] SET money = money + @Amount WHERE userid = @UserId";
                    connection.Execute(addSql, new {
                        Amount = moneyAmount,
                        UserId = toUserIdLong
                    }, transaction);

                    // 3. 更新回复获得金币
                    string updateReplySql = "UPDATE [wap_bbsre] SET mygetmoney = mygetmoney + @Amount WHERE id = @ReplyId AND userid = @UserId";
                    connection.Execute(updateReplySql, new {
                        Amount = moneyAmount,
                        ReplyId = replyIdLong,
                        UserId = toUserIdLong
                    }, transaction);

                    // 4. 发送系统消息
                    string messageTitle = "恭喜您，" + userVo.nickname + "奖励" + requestValue + "个币给您！";
                    string remark = GetRequestValue("remark");
                    string remarkText = string.IsNullOrEmpty(remark) ? "您的回帖得到奖励！" : remark;
                    string messageContent = "原因:" + remarkText + "[url=" + http_start + "bbs/book_view.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;id=" + id + "]查看[/url]";

                    string insertMessageSql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                                               VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, 1)";
                    connection.Execute(insertMessageSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = userIdLong,
                        Nickname = DapperHelper.LimitLength(userVo.nickname, 50),
                        Title = DapperHelper.LimitLength(messageTitle, 100),
                        Content = DapperHelper.LimitLength(messageContent, 500),
                        ToUserId = toUserIdLong
                    }, transaction);
                });

                // ✅ 先计算新余额，避免SaveBankLog中的SELECT操作导致死锁
                string bankLogConnectionString = PubConstant.GetConnectionString(a);

                // 转币者扣费后的余额
                long senderNewBalance = userVo.money - moneyAmount;

                // 获取接收者当前余额（事务已更新）
                string getReceiverMoneySql = "SELECT money FROM [user] WHERE userid = @UserId AND siteid = @SiteId";
                long receiverCurrentMoney = DapperHelper.ExecuteScalar<long>(bankLogConnectionString, getReceiverMoneySql, new {
                    UserId = toUserIdLong,
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID")
                });

                // ✅ 使用SaveBankLogWithBalance替换SaveBankLog，避免死锁
                SaveBankLogWithBalance(touserid, "转币操作", requestValue.ToString(), userid, nickname, "回帖奖励给我", receiverCurrentMoney);
                SaveBankLogWithBalance(userid, "转币操作", "-" + requestValue.ToString(), userid, nickname, "我奖币给会员ID(" + touserid + ")", senderNewBalance);
                INFO = "OK";
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}