/**
 * BookViewScript.js - Book_View.aspx页面特有的脚本逻辑
 * 包含异步加载更多回复、Book_View页面特有的初始化和回调处理
 */
(function () {
    'use strict';

    // 全局变量
    var xmlhttp = null;
    var YH_now_currpage = 0;
    var YH_page_total = 0;
    var YH_scoll_downEnd = 0;
    var YH_previousRepliesCount = 0; // 记录已加载的回复数量

    /**
     * 异步加载下一页回复
     * @param {Number} YH_total 总页数
     * @param {Number} YH_pagesize 每页大小
     * @param {Number} YH_currpage 当前页码
     * @param {String} tourl 请求URL基础部分
     * @param {String} pagetype 分页参数名
     */
    function YH_show_next(YH_total, YH_pagesize, YH_currpage, tourl, pagetype) {
        var total = parseInt(YH_total);
        YH_page_total = total;
        var pagesize = parseInt(YH_pagesize);
        var currpage = parseInt(YH_currpage);
        var isCustomLayoutEnabled = localStorage.getItem('customLayoutEnabled') === 'true';

        if (YH_now_currpage === 0) {
            YH_now_currpage = currpage + 1;
        } else {
            YH_now_currpage += 1;
        }

        document.getElementById("YH_show_loadimg").innerHTML =
            '<span id="loadimg"></span>';
        document.getElementById("YH_show_tip").innerHTML =
            "正在加载(" + YH_now_currpage + "/" + total + ")";

        if (YH_now_currpage > total) {
            document.getElementById("YH_show_loadimg").innerHTML = "";
            document.getElementById("YH_show_tip").innerHTML = "没有更多了";
        } else {
            tourl = tourl + "&" + pagetype + "=" + YH_now_currpage;
            // 设置"正在加载"文字的颜色
            document.getElementById("YH_show_tip").innerHTML =
                '<span style="color: #1abc9c;">正在加载(' + YH_now_currpage + '/' + total + ')</span>';

            LoadXML_Async(tourl);
        }
    }

    /**
     * 发送异步请求加载回复内容
     * @param {String} tourl 请求URL
     */
    function LoadXML_Async(tourl) {
        try {
            if (window.XMLHttpRequest) {
                xmlhttp = new XMLHttpRequest();
                if (xmlhttp.overrideMimeType) {
                    xmlhttp.overrideMimeType("text/xml");
                }
            } else if (window.ActiveXObject) {
                var activexName = ["MSXML2.XMLHTTP", "Microsoft.XMLHTTP", ""];
                for (var i = 0; i < activexName.length; i++) {
                    try {
                        xmlhttp = new ActiveXObject(activexName[i]);
                        break;
                    } catch (e) { }
                }
            }

            xmlhttp.onreadystatechange = YH_CallBack;
            xmlhttp.open("GET", tourl, true);
            xmlhttp.send(null);
        } catch (e) {
            document.getElementById("YH_show_loadimg").innerHTML =
                '<span id="loadimg"></span>';
            document.getElementById("YH_show_tip").innerHTML = "加载出错了！";
        }
    }

    /**
     * 处理异步请求响应
     */
    function YH_CallBack() {
        if (xmlhttp == null) {
            return;
        }

        if (xmlhttp.readyState == 4) {
            if (xmlhttp.status == 200) {
                var responseText = xmlhttp.responseText;
                var st = responseText.indexOf("<!--listS-->");
                var et = responseText.indexOf("<!--listE-->");
                var isCustomLayoutEnabled = localStorage.getItem('customLayoutEnabled') === 'true' || window.isCustomLayoutEnabled;

                if (st < 0 || et < 0) {
                    document.getElementById("YH_show_loadimg").innerHTML = "";
                    document.getElementById("YH_show_tip").innerHTML = "没有更多了";
                } else {
                    responseText = responseText.substring(st + 12, et);

                    var tempDiv = document.createElement('div');
                    tempDiv.innerHTML = responseText;
                    var hasReply = tempDiv.querySelector('.list-reply') !== null;

                    if (!hasReply && responseText.trim() === "") {
                        document.getElementById("YH_show_loadimg").innerHTML = "";
                        document.getElementById("YH_show_tip").innerHTML = "没有更多了";
                        return;
                    }

                    var recontentElement = document.getElementsByClassName("recontent")[0];
                    if (recontentElement) {
                        recontentElement.style.display = "block";
                        recontentElement.innerHTML += responseText;
                    } else {
                        document.getElementById("YH_show_loadimg").innerHTML = "";
                        document.getElementById("YH_show_tip").innerHTML = "加载内容区域出错";
                        return;
                    }

                    if (isCustomLayoutEnabled) {
                        var newElements = document.querySelectorAll('.list-reply:not(.forum-post)');
                        if (newElements.length > 0 && window.extractData && window.buildNewLayout && window.replaceContent) {
                            newElements.forEach(function (newElement) {
                                var data = window.extractData(newElement);
                                var newLayout = window.buildNewLayout(data);
                                window.replaceContent(newElement, newLayout);
                            });
                        }
                        else if (typeof applyNewLayoutToNewContent === 'function') {
                            applyNewLayoutToNewContent();
                        }
                    }

                    if (typeof hideRepliesContainingWord === 'function') {
                        hideRepliesContainingWord();
                    }

                    var retextElements = recontentElement.querySelectorAll('.retext');
                    retextElements.forEach((element) => {
                        if (typeof processTextContent === 'function') {
                            processTextContent(element);
                        }
                    });

                    if (window.ReplyForm) {
                        window.ReplyForm.resetReplyAnyBinding();
                        window.ReplyForm.replyAny({});
                    }

                    onMoreContentLoaded();

                    document.getElementById("YH_show_loadimg").innerHTML = "";
                    document.getElementById("YH_show_tip").innerHTML =
                        "加载更多(" + YH_now_currpage + "/" + YH_page_total + ")";

                    if (typeof window.callHighlightAndScroll === 'function') {
                        setTimeout(window.callHighlightAndScroll, 100);
                    }
                }
            }
        }
    }

    /**
     * 加载更多内容后的处理函数
     */
    function onMoreContentLoaded() {
        if (window.DomHelpers) {
            window.DomHelpers.applyHoverEffectToNewContent();
            window.DomHelpers.refreshAllHoverEffects();
        } else {
            applyHoverEffectToNewContent();
            document.querySelectorAll('.dropdown').forEach(function (dropDownElem) {
                dropDownElem.classList.remove('hover-effect');
                void dropDownElem.offsetWidth;
                dropDownElem.classList.add('hover-effect');
            });
        }

        if (localStorage.getItem('customLayoutEnabled') === 'true') {
            if (typeof applyNewLayoutToNewContent === 'function') {
                applyNewLayoutToNewContent();
            }
        }

        if (window.ReplyForm) {
            window.ReplyForm.resetReplyAnyBinding();
            window.ReplyForm.replyAny({});
        }

        if (typeof hideRepliesContainingWord === 'function') {
            hideRepliesContainingWord();
        }
    }

    /**
     * 初始化异步更多回复功能
     * 使用 KL_common.js 中的通用异步加载器 (UAL)。
     */
    function asyncReply() {
        let moreDiv = document.querySelector(".more"); // 包含触发器和状态的div
        if (!moreDiv) return;

        // 查找包含总数信息的链接
        let originalMoreLink = moreDiv.querySelector("a[href*='getTotal']");
        if (!originalMoreLink) {
            originalMoreLink = moreDiv.querySelector("a");
            if (!originalMoreLink) {
                return;
            }
        }

        // 从链接中提取总项目数
        const getTotalMatch = originalMoreLink.href.match(/getTotal=(\d+)/);
        if (!getTotalMatch || getTotalMatch.length < 2) {
            moreDiv.innerHTML = "无法确定总回复数。";
            return;
        }
        const totalItems = parseInt(getTotalMatch[1], 10);
        const itemsPerPage = 30; // 每页项目数，与后端配置一致

        // 构建基础URL，移除page和getTotal参数
        let baseUrl = originalMoreLink.href.replace(/&page=\d+/, "").replace(/getTotal=\d+&?/, "");
        baseUrl = baseUrl.replace(/[?&]page=\d+/, "").replace(/[?&]getTotal=\d+/, "");
        if (baseUrl.endsWith("&")) baseUrl = baseUrl.slice(0, -1);
        if (baseUrl.endsWith("?")) baseUrl = baseUrl.slice(0, -1);


        // 创建 UAL 需要的独立元素
        let ualTrigger = document.createElement("a");
        ualTrigger.id = "ual_trigger_link";
        ualTrigger.href = "javascript:void(0);";

        let ualLoadingIndicator = document.createElement("span");
        ualLoadingIndicator.id = "ual_loading_indicator";
        ualTrigger.appendChild(ualLoadingIndicator);

        let ualStatusText = document.createElement("span");
        ualStatusText.id = "ual_status_text";
        ualTrigger.appendChild(ualStatusText);

        // 清空 .more div 并添加新创建的 UAL 触发器和原有的 "全部回帖" 链接
        moreDiv.innerHTML = '';
        ualTrigger.style.width = "50%";
        originalMoreLink.style.width = "50%";

        moreDiv.appendChild(ualTrigger);
        moreDiv.appendChild(originalMoreLink); // 保留 "全部回帖" 链接

        // 初始化 UAL
        UAL_init({
            triggerSelector: "#ual_trigger_link", // 触发元素选择器
            contentContainerSelector: ".recontent", // 内容追加容器选择器
            loadingIndicatorSelector: "#ual_loading_indicator", // 加载指示器选择器
            statusTextSelector: "#ual_status_text", // 状态文本选择器
            baseUrl: baseUrl, // 基础URL
            pageParamName: "page", // 分页参数名
            itemsPerPage: itemsPerPage, // 每页项目数
            totalItems: totalItems, // 总项目数
            initialPage: 1, // 初始已加载页码设为 1 (假设主贴+预览为第一页内容)

            // 内容加载成功后（追加到DOM前）的回调，UAL 会处理DOM追加
            onContentLoaded: function (newRawHtml, tempDivWithNewContent) {
                // 此处可对 newRawHtml 或 tempDivWithNewContent 进行处理，但 UAL 默认会将 tempDivWithNewContent 的子节点追加到 contentContainerSelector
                // BookViewScript 特定的后处理逻辑放在 onSuccess 中执行更合适，因为需要操作已经存在于主DOM中的元素。
            },

            // 内容成功追加到DOM后执行的回调
            onSuccess: function () {
                var isCustomLayoutEnabled = localStorage.getItem('customLayoutEnabled') === 'true' || window.isCustomLayoutEnabled;
                const recontentElement = document.querySelector(".recontent");

                if (isCustomLayoutEnabled) {
                    // 调用 NewReplyUI.js 中的函数应用新布局
                    if (window.applyNewLayoutToNewContent && typeof window.applyNewLayoutToNewContent === 'function') {
                        window.applyNewLayoutToNewContent();
                    }
                }

                // 如果存在 hideRepliesContainingWord 函数，执行过滤
                if (typeof hideRepliesContainingWord === 'function') {
                    hideRepliesContainingWord();
                }

                // 对 .retext 元素应用文本处理 (来自 SuperLink.js)
                if (recontentElement) {
                    const retextElements = recontentElement.querySelectorAll('.retext');
                    retextElements.forEach((element) => {
                        if (typeof processTextContent === 'function') {
                            processTextContent(element);
                        }
                    });
                }

                // 重置 ReplyForm 绑定
                if (window.ReplyForm) {
                    // 第一次 resetFormUI 已经执行过了，这里不再重复调用
                    // if (typeof window.ReplyForm.resetFormUI === 'function') {
                    // window.ReplyForm.resetFormUI();
                    // }
                    window.ReplyForm.resetReplyAnyBinding();
                    window.ReplyForm.replyAny({});
                }

                // 执行 BookView 特有的加载后处理
                bookViewOnMoreContentLoaded();

                // 调用高亮和滚动函数 (来自 NewReplyUI.js)
                if (typeof window.callHighlightAndScroll === 'function') {
                    setTimeout(window.callHighlightAndScroll, 100);
                }
            },

            // 格式化状态文本
            statusTextFormatter: function (pageToLoad, totalPages, state) {
                // pageToLoad 是将要加载的页码 (UAL_currentPage + 1)
                // UAL_currentPage 是当前已成功加载的页数 (包含初始页)

                if (state === 'initial') {
                    // 初始显示时，显示已加载页数/总页数 (例如 1/10)
                    return `加载更多(${UAL_currentPage}/${totalPages})`;
                } else if (state === 'loading') {
                    // 加载中，显示正在加载的页码/总页数 (例如 正在加载(2/10))
                    return `<span style="color: #1abc9c;">正在加载(${pageToLoad}/${totalPages})</span>`;
                } else if (state === 'loaded') {
                    // 加载成功后，UAL_currentPage 已更新，显示新的已加载页数/总页数 (例如 2/10)
                    return `加载更多(${UAL_currentPage}/${totalPages})`;
                }
                // 默认回退
                return `加载更多(${UAL_currentPage}/${totalPages})`;
            }
        });
    }

    /**
     * BookView 特有的加载更多内容后的处理函数。
     * 包含原始 onMoreContentLoaded 中的部分逻辑。
     */
    function bookViewOnMoreContentLoaded() {
        if (window.DomHelpers) {
            window.DomHelpers.applyHoverEffectToNewContent();
            window.DomHelpers.refreshAllHoverEffects();
        } else {
            // 如果 DomHelpers.js 不可用时的备用逻辑
            document.querySelectorAll('.dropdown').forEach(function (dropDownElem) {
                dropDownElem.classList.remove('hover-effect');
                void dropDownElem.offsetWidth;
                dropDownElem.classList.add('hover-effect');
            });
        }
    }

    /**
     * 异步提交回复。
     * 使用 QuickReplyAjax.js (未修改)。
     */
    function asyncComment() {
        let form = document.getElementsByName('f')[0];
        if (!form) {
            return;
        }

        if (!window.QuickReplyAjax || !window.QuickReplyAjax.initAsyncComment) {
            return;
        }

        window.QuickReplyAjax.initAsyncComment({
            form: form,
            onSuccess: function (result, html) {
                var tipElement = window.QuickReplyAjax.createTipUI(
                    result.isSuccess ? '获得妖晶' + result.yaogem + '，经验' + result.exp : result.message,
                    result.isSuccess ? '回复成功' : '提示',
                    result.timeoutDuration
                );
                document.body.appendChild(tipElement);

                if (result.isSuccess) {
                    var bookId = form.querySelector('[name="id"]').value;

                    // 获取当前页面参数构建刷新URL
                    // 优先从全局变量获取 siteid 和 classid，然后从URL参数，最后从表单隐藏字段
                    let siteidValue = window.siteid || (new URLSearchParams(window.location.search).get("siteid")) || (form.querySelector('[name="siteid"]')?.value);
                    let classidValue = window.classid || (new URLSearchParams(window.location.search).get("classid")) || (form.querySelector('[name="classid"]')?.value);

                    // 如果仍然无法获取到 siteid 或 classid，则给出错误提示或使用默认值（如果适用）
                    if (!siteidValue || !classidValue) {
                        // 在这里可以决定是中止操作，还是尝试使用默认值（但不推荐，因为可能导致错误上下文）
                        // 为了安全起见，如果关键参数缺失，可能不应该继续刷新，避免潜在问题
                        return; // 或者根据产品逻辑决定是否需要更友好的用户提示
                    }

                    const fetchUrl = `/bbs/book_view.aspx?siteid=${siteidValue}&classid=${classidValue}&id=${bookId}`;

                    fetch(fetchUrl)
                        .then(res => res.text())
                        .then(responseHtml => {
                            // 在替换DOM前，保存当前的rewardPageConfig
                            const savedRewardConfig = window.rewardPageConfig;
                            
                            var parser = new DOMParser();
                            var doc = parser.parseFromString(responseHtml, 'text/html');
                            var newRecontent = doc.querySelector('.recontent');

                            if (newRecontent) {
                                var recontentElement = document.querySelector('.recontent');
                                if (recontentElement) {
                                    // 用新内容替换回复区域的HTML
                                    recontentElement.innerHTML = newRecontent.innerHTML;
                                    
                                    // 确保保存的rewardPageConfig被恢复
                                    if (savedRewardConfig) {
                                        window.rewardPageConfig = savedRewardConfig;
                                    }
                                    
                                    // 刷新内容后的后处理
                                    // 首先重置ReplyForm的UI状态，清除"回复X楼"和置顶样式
                                    if (window.ReplyForm && typeof window.ReplyForm.resetFormUI === 'function') {
                                        window.ReplyForm.resetFormUI();
                                    }

                                    var isCustomLayoutEnabled = localStorage.getItem('customLayoutEnabled') === 'true' || window.isCustomLayoutEnabled;
                                    if (isCustomLayoutEnabled) {
                                        // 调用 NewReplyUI.js 中的函数应用新布局
                                        if (window.applyNewLayoutToNewContent && typeof window.applyNewLayoutToNewContent === 'function') {
                                            window.applyNewLayoutToNewContent();
                                        }
                                    }
                                    // 对 .retext 元素应用文本处理
                                    var retextElements = recontentElement.querySelectorAll('.retext');
                                    retextElements.forEach((element) => {
                                        if (typeof processTextContent === 'function') {
                                            processTextContent(element);
                                        }
                                    });
                                    // 重置 ReplyForm 绑定
                                    if (window.ReplyForm) {
                                        // 第一次 resetFormUI 已经执行过了，这里不再重复调用
                                        // if (typeof window.ReplyForm.resetFormUI === 'function') {
                                        // window.ReplyForm.resetFormUI();
                                        // }
                                        window.ReplyForm.resetReplyAnyBinding();
                                        window.ReplyForm.replyAny({});
                                    }
                                    // 执行 BookView 特有的加载后处理
                                    bookViewOnMoreContentLoaded();
                                    // 调用高亮和滚动函数
                                    if (typeof window.callHighlightAndScroll === 'function') {
                                        setTimeout(window.callHighlightAndScroll, 100);
                                    }
                                }
                            }
                        })
                        .catch(error => {
                            // 刷新失败，静默处理
                        });
                }
            },
            onError: function (error) {
                var tipElement = window.QuickReplyAjax.createTipUI(
                    '提交回复失败，请稍后重试',
                    '错误',
                    2000
                );
                document.body.appendChild(tipElement);
            }
        });
    }

    /**
     * 页面初始化。
     */
    function init() {
        // 设置自适应文本框
        if (window.DomHelpers) {
            window.DomHelpers.setupAutoResizeTextareas();
        }

        // 初始化异步回复加载器
        asyncReply();
        // 初始化异步评论提交
        asyncComment();

        // 初始化 ReplyForm 置顶功能
        setTimeout(function () {
            if (window.ReplyForm) {
                var viewContent = document.querySelector(".viewContent");
                var form = document.querySelector("form[name='f']");

                if (viewContent && form) {
                    window.ReplyForm.sticky({
                        container: viewContent,
                        form: form
                    });
                    // 初始化 ReplyForm 其他功能
                    initReplyForm();
                }
            } else {
                setTimeout(initReplyForm, 50);
            }
        }, 10);
    }

    document.addEventListener("DOMContentLoaded", init);

    // 确保 ReplyForm 模块可用
    function ensureReplyFormAvailable() {
        if (!window.ReplyForm) {
            setTimeout(function () {
                initReplyForm();
            }, 10);
            return false;
        }
        return true;
    }

    // 初始化 ReplyForm 特定功能
    function initReplyForm() {
        if (!ensureReplyFormAvailable()) {
            return;
        }
        var stickyElement = document.querySelector(".sticky");
        var recontentElement = document.querySelector(".recontent");

        window.ReplyForm.resetReplyAnyBinding();
        window.ReplyForm.replyAny({
            stickyElement: stickyElement,
            recontentElement: recontentElement
        });
    }

})(); 