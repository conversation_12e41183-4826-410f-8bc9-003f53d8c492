using System;
using System.Web;
using System.Web.Hosting;
using System.Threading;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.WebSite.Services.Config;
using YaoHuo.Plugin.OAuth;
using Serilog;

namespace YaoHuo.Plugin
{
    public class Global : HttpApplication
    {
        protected void Application_Start(object sender, EventArgs e)
        {
            // 统一初始化日志系统
            LoggingConfigLoader.Initialize();
            
            try
            {
                // 记录系统启动日志
                OAuthLogger.LogAdminAction("system_startup", "SYSTEM", 0, "OAuth模块启动", "localhost", "System");

                // 使用 HostingEnvironment.QueueBackgroundWorkItem 替代 Timer
                ScheduleErrorCountCleanup();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "OAuth日志系统初始化异常");
            }

            try
            {
                // 触发 TemplateService 静态构造函数的执行
                WebSite.Tool.TemplateService.ClearCache();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化模板引擎异常，{ex.GetType().Name}，信息：{ex.Message}");
                System.Diagnostics.Debug.WriteLine($"{ex.StackTrace}");
                throw;
            }

            try
            {
                // 预加载配置文件到缓存
                PreloadConfigurations();
            }
            catch
            {
                // 不抛出异常，避免阻止应用程序启动
            }
        }
        
        /// <summary>
        /// 调度错误计数清理任务
        /// </summary>
        private void ScheduleErrorCountCleanup()
        {
            // 首次执行（5分钟后）
            HostingEnvironment.QueueBackgroundWorkItem(ct =>
            {
                Thread.Sleep(TimeSpan.FromMinutes(5));
                if (!ct.IsCancellationRequested)
                {
                    OAuthLogger.CleanupErrorCounts();
                    // 继续调度后续执行
                    ScheduleRecurringCleanup();
                }
            });
        }
        
        /// <summary>
        /// 调度定期执行的清理任务
        /// </summary>
        private void ScheduleRecurringCleanup()
        {
            HostingEnvironment.QueueBackgroundWorkItem(ct =>
            {
                try
                {
                    // 每小时执行一次
                    Thread.Sleep(TimeSpan.FromHours(1));
                    
                    if (!ct.IsCancellationRequested)
                    {
                        OAuthLogger.CleanupErrorCounts();
                        Log.Information("执行OAuth错误计数清理");
                        
                        // 递归调度下一次执行
                        ScheduleRecurringCleanup();
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "OAuth错误计数清理异常");
                    // 即使发生异常，也尝试调度下一次执行
                    ScheduleRecurringCleanup();
                }
            });
        }

        /// <summary>
        /// 预加载配置文件到缓存
        /// 利用服务器充足内存，在应用启动时预加载常用配置
        /// </summary>
        private void PreloadConfigurations()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== 开始预加载配置文件 ===");

                // 使用新的通用配置服务预加载配置
                System.Diagnostics.Debug.WriteLine("开始使用通用配置服务预加载配置...");

                // 预加载身份配置
                var identityOptions = BBSConfigService.GetIdentityOptions();
                System.Diagnostics.Debug.WriteLine($"预加载身份配置完成，共 {identityOptions.Count} 个选项");

                // 预加载动态图标配置
                var dynamicIconConfig = BBSConfigService.GetDynamicIconConfig();
                System.Diagnostics.Debug.WriteLine($"预加载动态图标配置完成，共 {dynamicIconConfig.IconRules.Count} 条规则");

                // 获取身份配置信息
                var identityConfigInfo = BBSConfigService.GetIdentityConfigInfo();
                if (identityConfigInfo != null)
                {
                    System.Diagnostics.Debug.WriteLine($"身份配置版本: {identityConfigInfo.Version}, 环境: {identityConfigInfo.Environment}");
                }

                // 预加载勋章配置
                var applyMedals = BBSConfigService.GetApplyMedals();
                var purchaseMedals = BBSConfigService.GetPurchaseMedals();
                System.Diagnostics.Debug.WriteLine($"预加载勋章配置完成，申请勋章 {applyMedals.Count} 个，购买勋章 {purchaseMedals.Count} 个");

                // 获取BBS配置统计信息
                var bbsStats = BBSConfigService.GetBBSConfigStats();
                System.Diagnostics.Debug.WriteLine($"BBS配置统计: {bbsStats}");

                // 获取通用配置服务统计信息
                var allConfigStats = ConfigService.GetAllConfigStats();
                System.Diagnostics.Debug.WriteLine($"通用配置服务统计:\n{allConfigStats}");

                // 🚀 HashSet优化：性能测试
                System.Diagnostics.Debug.WriteLine("=== 勋章比对性能测试 ===");
                string testUserMedals = "靓号.gif|/bbs/medal/67.gif|/XinZhang/upload/1000/1000_1003130.gif|初级勋章.gif|认真学习.gif";
                var performanceResult = BBSConfigService.PerformanceBenchmark(testUserMedals, 50);
                System.Diagnostics.Debug.WriteLine(performanceResult);
                System.Diagnostics.Debug.WriteLine("=== 性能测试完成 ===");

                // 获取缓存统计信息
                var cacheStats = ConfigCacheService.GetCacheStats();
                System.Diagnostics.Debug.WriteLine($"缓存统计: {cacheStats}");

                System.Diagnostics.Debug.WriteLine("=== 配置文件预加载完成 ===");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"配置文件预加载失败: {ex.Message}");
                // 预加载失败不影响应用程序启动
            }
        }

        protected void Session_Start(object sender, EventArgs e)
        {

        }

        protected void Application_BeginRequest(object sender, EventArgs e)
        {

        }

        protected void Application_AuthenticateRequest(object sender, EventArgs e)
        {

        }

        protected void Application_Error(object sender, EventArgs e)
        {

        }

        protected void Session_End(object sender, EventArgs e)
        {

        }

        protected void Application_End(object sender, EventArgs e)
        {
            // 关闭 Serilog
            OAuthLogger.Close();
            Log.CloseAndFlush();
        }
    }
} 