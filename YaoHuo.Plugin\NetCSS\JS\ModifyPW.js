﻿const vm = new Vue({
  el: "#app",
  data: {
    oldPassword: "",
    newPassword1: "",
    newPassword2: "",
    minCharacterNumber: { min: 8, max: 50 },
    labels: {
      title: "更改密码",
      subtitle: "新密码必须包含：",
      save: "确认",
      cancel: "取消",
      atLeastSixCharacters: "8-50个字符",
      oneNumber: "至少1个数字 (0-9)",
      oneUpperCaseletter: "至少1个大写字母 (A-Z)",
      oneLowerCaseLetter: "至少1个小写字母 (a-z)",
      passwordsMatch: "两次新密码一致"
    },
  },
  computed: {
    passwordLength() {
      return this.newPassword1.length;
    },
    isPasswordLengthValid() {
      const length = this.passwordLength;
      return length >= this.minCharacterNumber.min && length <= this.minCharacterNumber.max;
    },
    isOldPasswordFilled() {
      return Boolean(this.oldPassword.length);
    },
    arePasswordsEqual() {
      if (!this.newPassword1 || !this.newPassword2) return false;
      return this.newPassword1 === this.newPassword2;
    },
    hasOneUpperCaseLetter() {
      const rule = /(.*[A-Z].*)/;
      return this.newPassword1.match(rule);
    },
    hasOneLowerCaseLetter() {
      const rule = /(.*[a-z].*)/;
      return this.newPassword1.match(rule);
    },
    hasOneNumber() {
      const rule = /[0-9]{1}/;
      return this.newPassword1.match(rule);
    },
    isPasswordError() {
      const chineseCharacterRule = /[\u4e00-\u9fa5]/;
      return this.newPassword1.match(chineseCharacterRule);
    },
  },
});

const saveButton = document.querySelector('.modal__password__save');
const alertTitle = document.querySelector('.ui__title');
const alertMessage = document.getElementById('alertMessage');

saveButton.addEventListener('click', function (event) {
  event.preventDefault();

  if (vm.isPasswordError) {
    alertTitle.textContent = '密码禁止中文字符';
    alertMessage.style.display = 'block';
    setTimeout(function () {
      alertMessage.style.display = 'none';
    }, 1000);
  } else if (!isPasswordValid()) {
    alertTitle.textContent = '密码不符合要求';
    alertMessage.style.display = 'block';
    setTimeout(function () {
      alertMessage.style.display = 'none';
    }, 1000);
  } else if (!vm.arePasswordsEqual) {
    alertTitle.textContent = '两次新密码不一致';
    alertMessage.style.display = 'block';
    setTimeout(function () {
      alertMessage.style.display = 'none';
    }, 1000);
  } else if (!vm.isOldPasswordFilled) {
    alertTitle.textContent = '请填写旧密码';
    alertMessage.style.display = 'block';
    setTimeout(function () {
      alertMessage.style.display = 'none';
    }, 1000);
  } else if (vm.oldPassword === vm.newPassword1) {
    alertTitle.textContent = '新旧密码不能相同';
    alertMessage.style.display = 'block';
    setTimeout(function () {
      alertMessage.style.display = 'none';
    }, 1000);
  } else {
    const form = document.forms.f;
    form.submit();
  }
});

function isPasswordValid() {
  return (
    vm.isPasswordLengthValid &&
    vm.hasOneUpperCaseLetter &&
    vm.hasOneLowerCaseLetter &&
    vm.hasOneNumber
  );
}
