const fs = require('fs');
const path = require('path');

const layoutPath = path.resolve(__dirname, '../Template/Layouts/MainLayout.hbs');

try {
    let layoutContent = fs.readFileSync(layoutPath, 'utf8');
    const newVersion = Date.now(); // 使用当前时间戳作为新的版本号

    const updatedContent = layoutContent.replace(
        /href=\"(\/Template\/CSS\/output\.css)\?(\d+)?\"/,
        `href=\"$1?${newVersion}\"`
    );

    fs.writeFileSync(layoutPath, updatedContent, 'utf8');
    console.log(`Updated MainLayout.hbs with new CSS version: ${newVersion}`);
} catch (error) {
    console.error(`Error updating CSS version in MainLayout.hbs: ${error.message}`);
} 