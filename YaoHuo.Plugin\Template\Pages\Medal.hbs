{{#if Message.HasMessage}}
{{#unless Message.IsSuccess}}
<div class="message {{Message.Type}}">
    {{Message.Content}}
</div>
{{/unless}}
{{/if}}

<!-- 功能切换和分类筛选栏 -->
<div class="card" id="filterCard">
    <div class="card-header">
        <div class="card-title">
            <i data-lucide="award" class="card-icon"></i>
            勋章中心
        </div>
    </div>
    
    <!-- 功能切换标签 -->
    <div class="flex gap-2 p-4 border-b border-border-light">
        <div class="function-tab {{#if IsApplyPage}}active{{/if}}" data-function="apply">申请勋章</div>
        <div class="function-tab {{#if IsBuyPage}}active{{/if}}" data-function="purchase">购买勋章</div>
    </div>

    <!-- 分类筛选标签 -->
    <div class="filter-tabs" id="applyFilterTabs" {{#if IsBuyPage}}style="display: none;"{{/if}}>
        <div class="filter-tab active" data-category="all">全部</div>
        <div class="filter-tab" data-category="achievement">成就</div>
        <div class="filter-tab" data-category="certification">认证</div>
        <div class="filter-tab" data-category="sponsor">赞助</div>
        <div class="filter-tab" data-category="special">特殊</div>
    </div>

    <!-- 购买勋章分类筛选标签 -->
    <div class="filter-tabs" id="purchaseFilterTabs" {{#if IsApplyPage}}style="display: none;"{{/if}}>
        <div class="filter-tab active" data-category="all">全部</div>
        <div class="filter-tab" data-category="popular">热门</div>
        <div class="filter-tab" data-category="limited">限定</div>
        <div class="filter-tab" data-category="premium">高端</div>
    </div>

    <!-- 分割线 -->
    <div class="border-t border-border-light"></div>

    <!-- 勋章列表区域 -->
    <div class="card-body p-0">
        <!-- 申请勋章列表 -->
        <div id="applyMedals" class="medal-list" style="display: {{#if IsApplyPage}}block{{else}}none{{/if}};">
            {{#each ApplyMedals}}
            <div class="medal-item apply-medal" data-category="{{Category}}">
                <div class="medal-icon">
                    <img src="{{IconUrl}}" alt="{{Name}}" loading="lazy">
                </div>
                <div class="medal-info">
                    <div class="medal-header">
                        <h3 class="medal-title">
                            {{Name}}
                            {{#if IsOwned}}
                            <span class="owned-badge">已拥有</span>
                            {{/if}}
                        </h3>
                    </div>
                    <p class="medal-description">
                        {{Description}}
                        {{#if HasConditionUrl}}
                        <a href="{{ConditionUrl}}" class="medal-link" title="查看详细申请条件" target="_blank">
                            <i data-lucide="external-link" class="w-3 h-3"></i>
                        </a>
                        {{/if}}
                    </p>
                </div>
            </div>
            {{/each}}
        </div>

        <!-- 购买勋章列表 -->
        <div id="purchaseMedals" class="medal-list" style="display: {{#if IsBuyPage}}block{{else}}none{{/if}};">
            {{#each PurchaseMedals}}
            <div class="medal-item purchase-medal" data-category="{{Category}}">
                <div class="medal-icon">
                    <img src="{{IconUrl}}" alt="{{Name}}" loading="lazy">
                </div>
                <div class="medal-info">
                    <div class="medal-header">
                        <h3 class="medal-title">{{Name}}</h3>
                    </div>
                    <p class="medal-description">{{Description}}</p>
                    <div class="medal-price">{{Price}} 妖晶</div>
                </div>
                <div class="medal-actions">
                    {{#if IsOwned}}
                    <button class="buy-button disabled" disabled>已购买</button>
                    {{else}}
                    <a href="{{BuyUrl}}" class="buy-button">购买</a>
                    {{/if}}
                </div>
            </div>
            {{/each}}
        </div>
    </div>
</div>

<!-- 申请流程说明弹窗 -->
<div id="helpModal" class="modal-overlay" onclick="hideHelpModal()">
    <div class="modal-content help-modal" onclick="event.stopPropagation()">
        <div class="modal-header">
            <h3 class="modal-title">
                <i data-lucide="help-circle" class="w-5 h-5"></i>
                申请流程
            </h3>
            <button class="modal-close" onclick="hideHelpModal()">
                <i data-lucide="x" class="w-5 h-5"></i>
            </button>
        </div>
        <div class="modal-body pb-0">
            <div class="help-content">
                <div class="help-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>确认申请条件</h4>
                        <p>请仔细查看各勋章的申请条件，确保您符合相应的要求。</p>
                    </div>
                </div>
                <div class="help-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>前往站务处理区</h4>
                        <p>符合申请条件者，可到<strong><a href="/bbs/book_view_add.aspx?classid=199" class="text-primary hover:text-primary-dark no-underline px-1" target="_blank">站务处理</a></strong>区发一篇申请帖。</p>
                    </div>
                </div>
                <div class="help-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>发布申请帖</h4>
                        <p>申请帖标题统一为：<strong>申请XX勋章</strong></p>
                        <p class="help-note">例如：申请新人进步勋章、申请认真学习勋章等</p>
                    </div>
                </div>
                <div class="help-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h4>等待审核</h4>
                        <p>管理员会根据您的申请情况进行审核，符合条件的申请将会通过。</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-primary" onclick="hideHelpModal()">我知道了</button>
        </div>
    </div>
</div>

{{!-- 所有样式已迁移到 style.css 组件系统，移除自定义 style 标签 --}}

<!-- TypeScript模块加载 -->
<script type="module">
    // 加载Medal页面的TypeScript模块
    import '/Template/TS/pages/Medal.js?v=1752033421938';

    console.log('Medal页面TypeScript模块已加载');
</script>

<!-- 页面配置数据 -->
<div id="page-config"
     data-page-type="{{PageType}}"
     data-is-experience-mode="{{#if IsExperienceMode}}true{{else}}false{{/if}}"
     style="display: none;">
</div>
