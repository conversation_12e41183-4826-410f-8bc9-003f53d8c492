---
description: 数据库操作、安全防护、DapperHelper使用和SQL优化的综合规范
globs: *.cs,*.aspx.cs,*.ascx.cs
alwaysApply: false
---
## 数据库与安全综合规范

### 1. 数据库访问优先级

#### 1.1 推荐的数据访问方式（按优先级）
1. **DapperHelper** (首选): 现代化的参数化查询接口
2. **OptimizedTransactionHelper**: 复杂事务和批量操作
3. **UserInfoCacheService**: 用户信息查询（带缓存）
4. **BLL/DAL层** (KeLin.ClassManager): 传统业务逻辑层
5. **直接SQL执行**: 仅在特殊场景使用

#### 1.2 DapperHelper 使用规范
```csharp
// 推荐：查询操作
var users = DapperHelper.Query<UserModel>(
    PubConstant.GetConnectionString("KeLin"), 
    "SELECT * FROM users WHERE id = @id", 
    new { id = userId }
);

// 推荐：执行操作
int rowsAffected = DapperHelper.Execute(
    connectionString, 
    "INSERT INTO table (col1, col2) VALUES (@val1, @val2)",
    new { val1 = value1, val2 = value2 }
);

// 推荐：标量查询
var count = DapperHelper.ExecuteScalar<int>(
    connectionString,
    "SELECT COUNT(*) FROM table WHERE condition = @param",
    new { param = value }
);
```

### 2. SQL注入防护（强制要求）

#### 2.1 核心原则
- **永远不要将用户输入直接拼接到SQL字符串中**
- **所有数据库操作必须使用参数化查询**
- **严禁字符串拼接构建SQL语句**

#### 2.2 安全示例对比
```csharp
// ❌ 危险：SQL注入风险
string sql = "SELECT * FROM users WHERE name = '" + userName + "'";

// ✅ 安全：DapperHelper参数化
var users = DapperHelper.Query<User>(connectionString, 
    "SELECT * FROM users WHERE name = @name", 
    new { name = userName });

// ✅ 安全：传统参数化
SqlParameter[] parameters = {
    new SqlParameter("@name", SqlDbType.NVarChar, 50) { Value = userName }
};
```

#### 2.3 特殊场景处理
- **动态WHERE条件**: 使用QueryBuilder构建复杂搜索查询
- **ORDER BY字段**: 使用白名单验证，禁止直接拼接用户输入
- **IN子句**: 使用参数数组或Table-Valued Parameters

### 3. 数据库性能优化

#### 3.1 查询优化
- **使用NOLOCK提示**: 避免读取死锁，适用于允许脏读的场景
- **避免N+1查询**: 合理使用JOIN或批量查询
- **索引优化**: 确保WHERE、ORDER BY字段有适当索引
- **分页查询**: 使用ROW_NUMBER()或OFFSET/FETCH语法

#### 3.2 事务管理
- **短事务**: 保持事务尽可能短，减少锁定时间
- **批量操作**: 使用OptimizedTransactionHelper处理批量更新
- **异步处理**: 非关键操作考虑异步执行
- **连接管理**: 及时释放数据库连接

#### 3.3 缓存策略
- **用户信息缓存**: 使用UserInfoCacheService，5-10分钟过期
- **配置缓存**: 使用ConfigService管理JSON配置
- **查询结果缓存**: 对频繁查询的静态数据进行缓存

### 4. 安全编码实践

#### 4.1 输入验证
- **服务端验证**: 所有用户输入必须在服务端验证
- **数据类型检查**: 确保输入数据类型正确
- **长度限制**: 检查字符串长度，防止缓冲区溢出
- **特殊字符处理**: 对特殊字符进行适当转义

#### 4.2 输出编码
- **HTML编码**: 输出到HTML时使用HttpUtility.HtmlEncode
- **JavaScript编码**: 输出到JavaScript时进行适当转义
- **URL编码**: URL参数使用HttpUtility.UrlEncode
- **JSON安全**: 使用Newtonsoft.Json等安全的JSON库

#### 4.3 权限控制
- **身份验证**: 所有敏感操作需校验用户身份
- **权限检查**: 验证用户是否有执行操作的权限
- **数据隔离**: 确保用户只能访问自己的数据
- **管理员权限**: 管理功能需要额外的权限验证

### 5. 错误处理与日志

#### 5.1 异常处理
- **数据库异常**: 捕获并记录数据库连接和查询异常
- **用户友好**: 向用户显示友好的错误信息，不暴露技术细节
- **日志记录**: 记录详细的错误信息用于调试
- **事务回滚**: 异常时确保事务正确回滚

#### 5.2 审计日志
- **操作记录**: 记录重要的数据修改操作
- **用户行为**: 记录用户的关键操作
- **安全事件**: 记录登录失败、权限违规等安全事件
- **性能监控**: 记录慢查询和性能问题

### 6. 配置管理

#### 6.1 连接字符串管理
- **统一获取**: 使用PubConstant.GetConnectionString()获取连接字符串
- **环境区分**: 开发、测试、生产环境使用不同配置
- **安全存储**: 连接字符串加密存储，避免明文

#### 6.2 JSON配置系统
- **ConfigService**: 使用统一的JSON配置管理服务
- **配置缓存**: 配置信息进行缓存，减少文件读取
- **热更新**: 支持配置文件的热更新机制
- **版本控制**: 配置变更进行版本控制和审计

### 7. 开发检查清单
- [ ] 所有SQL操作使用参数化查询
- [ ] 用户输入进行服务端验证
- [ ] 敏感操作验证用户权限
- [ ] 数据库连接及时释放
- [ ] 异常处理完善，有日志记录
- [ ] 使用适当的缓存策略
- [ ] 配置信息统一管理
- [ ] 性能关键查询已优化