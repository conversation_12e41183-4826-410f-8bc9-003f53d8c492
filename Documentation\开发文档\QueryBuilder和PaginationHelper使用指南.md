# QueryBuilder和PaginationHelper使用指南

## 📋 概述

本文档介绍新增的`QueryBuilder`和`PaginationHelper`工具类，用于简化数据库查询构建和分页操作。

## 🎯 设计目标

### 1. 统一查询构建器 (QueryBuilder)
- ✅ 支持链式调用的WHERE条件构建
- ✅ 自动参数命名和管理，避免参数冲突
- ✅ 支持条件判断的WhereIf方法
- ✅ 与现有DapperHelper完全兼容
- ✅ 消除重复的条件构建代码

### 2. 泛型分页查询 (PaginationHelper)
- ✅ 泛型分页查询支持
- ✅ 返回包含数据和分页信息的结果对象
- ✅ 与现有PaginationModel兼容
- ✅ 支持SQL Server的OFFSET/FETCH语法
- ✅ 统一分页逻辑，减少重复代码

## 🔧 QueryBuilder 使用方法

### 基础用法

```csharp
// 创建查询构建器
var queryBuilder = new QueryBuilder();

// 添加条件
queryBuilder
    .Where("siteid = @ParamN", siteId)
    .Where("userid = @ParamN", userId)
    .WhereIf(!string.IsNullOrEmpty(searchText), "content LIKE @ParamN", $"%{searchText}%");

// 构建SQL和参数
var (sql, parameters) = queryBuilder.Build("SELECT * FROM wap_bankLog");

// 执行查询
var result = DapperHelper.Query<wap_bankLog_Model>(connectionString, sql, parameters);
```

### 高级用法 - 同时构建COUNT和数据查询

```csharp
var queryBuilder = new QueryBuilder()
    .Where("siteid = @ParamN", siteId)
    .WhereIf(isAdmin && !string.IsNullOrEmpty(userId), "userid = @ParamN", userId)
    .WhereIf(!string.IsNullOrEmpty(searchText), "actionname LIKE @ParamN", $"%{searchText}%");

// 构建COUNT和数据查询
var (countSql, dataSql, parameters) = queryBuilder.BuildWithCount(
    "SELECT *", 
    "wap_bankLog", 
    "ORDER BY id DESC"
);

// 执行COUNT查询
var total = DapperHelper.ExecuteScalar<long>(connectionString, countSql, parameters);

// 执行数据查询（带分页）
var dataSqlWithPaging = $@"{dataSql} 
                          OFFSET {offset} ROWS 
                          FETCH NEXT {pageSize} ROWS ONLY";
var data = DapperHelper.Query<wap_bankLog_Model>(connectionString, dataSqlWithPaging, parameters);
```

## 🔧 PaginationHelper 使用方法

### 基础分页查询

```csharp
// 简单分页查询
var result = PaginationHelper.GetPagedData<wap_bankLog_Model>(
    connectionString,
    "SELECT * FROM wap_bankLog WHERE siteid = @SiteId",
    new { SiteId = siteId },
    page: 1,
    pageSize: 10,
    orderBy: "ORDER BY id DESC"
);

// 访问结果
Console.WriteLine($"总记录数: {result.Total}");
Console.WriteLine($"当前页: {result.Page}/{result.TotalPages}");
Console.WriteLine($"数据条数: {result.Data.Count}");

// 转换为PaginationModel（用于模板）
var paginationModel = result.ToPaginationModel();
```

### 结合QueryBuilder的分页查询

```csharp
// 创建查询构建器
var queryBuilder = new QueryBuilder()
    .Where("siteid = @ParamN", siteId)
    .WhereIf(!string.IsNullOrEmpty(searchKey), "actionname LIKE @ParamN", $"%{searchKey}%")
    .WhereIf(year > 0, "YEAR(addtime) = @ParamN", year);

// 执行分页查询
var result = PaginationHelper.GetPagedDataWithBuilder<wap_bankLog_Model>(
    connectionString,
    "SELECT *",
    "wap_bankLog",
    queryBuilder,
    page: currentPage,
    pageSize: 10,
    orderBy: "ORDER BY id DESC"
);

// 使用结果
var bankLogList = result.Data;
var pagination = result.ToPaginationModel();
```

## 📊 改造前后对比

### 改造前 - 手动构建条件（Banklist.aspx.cs）

```csharp
// 复杂的条件构建逻辑
private (string CountSql, string ListSql, dynamic Parameters) BuildSafeQueryParameters(
    string siteId, string key, string userId, bool isAdmin,
    string typekey, string typeid, string toyear, string tomonth)
{
    var whereConditions = new List<string>();
    var parameters = new Dictionary<string, object>();

    // 基础条件：站点ID
    whereConditions.Add("siteid = @SiteId");
    parameters["SiteId"] = DapperHelper.SafeParseLong(siteId, "站点ID");

    // 用户ID条件
    if (isAdmin)
    {
        if (!string.IsNullOrEmpty(key) && key.Trim() != "0" && WapTool.IsNumeric(key))
        {
            whereConditions.Add("userid = @UserId");
            parameters["UserId"] = DapperHelper.SafeParseLong(key, "用户ID");
        }
    }
    else
    {
        whereConditions.Add("userid = @UserId");
        parameters["UserId"] = DapperHelper.SafeParseLong(userId, "用户ID");
    }

    // 搜索条件
    if (!string.IsNullOrEmpty(typekey) && !string.IsNullOrEmpty(typeid))
    {
        switch (typeid)
        {
            case "1": // 项目名称
                whereConditions.Add("actionname LIKE @SearchText");
                parameters["SearchText"] = $"%{typekey}%";
                break;
            // ... 更多条件
        }
    }

    // 时间条件
    if (WapTool.IsNumeric(toyear))
    {
        int year = int.Parse(toyear);
        if (year >= 2000 && year <= DateTime.Now.Year + 1)
        {
            whereConditions.Add("YEAR(addtime) = @Year");
            parameters["Year"] = year;
        }
    }

    string whereClause = string.Join(" AND ", whereConditions);
    string countSql = $"SELECT COUNT(*) FROM wap_bankLog WHERE {whereClause}";
    string listSql = $@"SELECT * FROM wap_bankLog
                       WHERE {whereClause}
                       ORDER BY id DESC
                       OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

    return (countSql, listSql, parameters);
}

// 分页逻辑
total = DapperHelper.ExecuteScalar<long>(connectionString, queryParams.CountSql, queryParams.Parameters);
CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
index = pageSize * (CurrentPage - 1L);
var listParameters = new {
    // ... 复制所有参数
    Offset = (CurrentPage - 1) * pageSize,
    PageSize = pageSize
};
var bankLogList = DapperHelper.Query<wap_bankLog_Model>(connectionString, queryParams.ListSql, listParameters);
```

### 改造后 - 使用新工具类

```csharp
// 简洁的查询构建
var queryBuilder = new QueryBuilder()
    .Where("siteid = @ParamN", DapperHelper.SafeParseLong(siteId, "站点ID"))
    .WhereIf(isAdmin && !string.IsNullOrEmpty(key) && key.Trim() != "0", 
             "userid = @ParamN", DapperHelper.SafeParseLong(key, "用户ID"))
    .WhereIf(!isAdmin, "userid = @ParamN", DapperHelper.SafeParseLong(userId, "用户ID"))
    .WhereIf(typeid == "1" && !string.IsNullOrEmpty(typekey), 
             "actionname LIKE @ParamN", $"%{typekey}%")
    .WhereIf(WapTool.IsNumeric(toyear), "YEAR(addtime) = @ParamN", int.Parse(toyear));

// 一行代码完成分页查询
var result = PaginationHelper.GetPagedDataWithBuilder<wap_bankLog_Model>(
    connectionString,
    "SELECT *",
    "wap_bankLog",
    queryBuilder,
    currentPage,
    pageSize,
    "ORDER BY id DESC"
);

// 直接使用结果
var bankLogList = result.Data;
var pagination = result.ToPaginationModel();
```

## ✅ 优势总结

### 代码简化
- **减少70%的条件构建代码**
- **消除重复的分页逻辑**
- **统一的参数命名规则**

### 安全性提升
- **自动参数命名，避免冲突**
- **保持完整的参数化查询**
- **类型安全的泛型支持**

### 可维护性
- **链式调用，代码更清晰**
- **统一的API设计**
- **完整的注释和文档**

## 🔄 迁移建议

### 优先级
1. **高优先级**：复杂条件查询的页面（如Banklist.aspx.cs）
2. **中优先级**：简单分页查询的页面
3. **低优先级**：单一条件查询的页面

### 迁移步骤
1. **保留原有方法**作为备份
2. **新增使用新工具类的方法**
3. **测试验证功能一致性**
4. **逐步替换调用点**
5. **删除旧方法**

## 📝 注意事项

### QueryBuilder
- 使用`@ParamN`作为占位符，系统会自动替换为实际参数名
- 支持重置状态，可重复使用同一个实例
- 条件之间默认使用AND连接

### PaginationHelper
- 页码从1开始计算
- 自动处理边界情况（页码小于1等）
- 返回的PagedResult可直接转换为PaginationModel

### 性能考虑
- QueryBuilder构建过程在内存中进行，性能开销极小
- PaginationHelper使用标准的COUNT + 分页查询模式
- 与原有实现性能基本一致
