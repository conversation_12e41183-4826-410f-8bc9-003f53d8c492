using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using KeLin.WebSite;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using Dapper;
using Wuqi.Webdiyer;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class ViewUser : BasePage
    {
        protected HtmlForm form1;

        protected Repeater TopRepeater;

        protected Repeater SiteRepeater;

        protected AspNetPager AspNetPager1;

        protected TextBox tb_title;

        protected TextBox tb_rec;

        protected Button bt_revert;

        private string string_36 = PubConstant.GetAppString("InstanceName");

        public string strCommandType = "";

        public string strCommandResult = "";

        public string strHangBiaoShi = "";

        public string domain = "";

        public string loadpagetime = "";

        public static string bookid = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            DateTime now = DateTime.Now;
            if (!Page.IsPostBack)
            {
                CheckManagerLvl("04");
                bookid = GetRequestValue("bookid");
                method_2();
            }
            strCommandType = GetRequestValue("CommandType");
            switch (strCommandType.ToLower())
            {
                default:
                    // 暂无处理
                    break;
            }
            domain = base.Domain;
            DateTime now2 = DateTime.Now;
            loadpagetime = (now2 - now).TotalMilliseconds.ToString();
        }

        private void method_2()
        {
            // ✅ 使用DapperHelper替换BLL调用，修复SQL注入漏洞
            string connectionString = PubConstant.GetConnectionString(string_36);

            // 修复主帖查询
            var postQueryBuilder = new QueryBuilder()
                .Where("id = @ParamN", DapperHelper.SafeParseLong(bookid, "帖子ID"));

            if (base.ManagerLvl != "00")
            {
                postQueryBuilder.Where("userid = @ParamN", DapperHelper.SafeParseLong(base.SiteId, "站点ID"));
            }

            var (postSql, postParameters) = postQueryBuilder.Build("SELECT * FROM wap_bbs WITH (NOLOCK)");
            var postList = DapperHelper.Query<wap_bbs_Model>(connectionString, postSql, postParameters);
            TopRepeater.DataSource = postList;
            TopRepeater.DataBind();

            // 修复回复查询
            string orderfldName = "id";
            string value = "1";

            // ✅ 安全改进：对排序字段进行白名单验证，防止SQL注入
            if (ViewState["OrderColunmName"] != null && ViewState["OrderColunmName"].ToString() != "")
            {
                string requestedOrderField = ViewState["OrderColunmName"].ToString();
                orderfldName = ValidateOrderField(requestedOrderField);
            }

            if (ViewState["OrderType"] != null && ViewState["OrderType"].ToString() != "")
            {
                string requestedOrderType = ViewState["OrderType"].ToString();
                value = ValidateOrderType(requestedOrderType);
            }
            else
            {
                ViewState["OrderType"] = value;
            }

            if (!string.IsNullOrEmpty(bookid))
            {
                // 使用DapperHelper进行安全的回复查询
                string countSql = "SELECT COUNT(*) FROM wap_bookre WHERE bookid = @BookId";
                int listCount = DapperHelper.ExecuteScalar<int>(connectionString, countSql, new
                {
                    BookId = DapperHelper.SafeParseLong(bookid, "帖子ID")
                });
                AspNetPager1.RecordCount = listCount;

                // 构建分页查询
                string orderDirection = value == "1" ? "DESC" : "ASC";
                string listSql = $@"SELECT * FROM wap_bookre
                                   WHERE bookid = @BookId
                                   ORDER BY {orderfldName} {orderDirection}
                                   OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

                var replyList = DapperHelper.Query<wap_bookre_Model>(connectionString, listSql, new
                {
                    BookId = DapperHelper.SafeParseLong(bookid, "帖子ID"),
                    Offset = (AspNetPager1.CurrentPageIndex - 1) * AspNetPager1.PageSize,
                    PageSize = AspNetPager1.PageSize
                });

                SiteRepeater.DataSource = replyList;
                SiteRepeater.DataBind();
            }
            else
            {
                AspNetPager1.RecordCount = 0;
                SiteRepeater.DataSource = null;
                SiteRepeater.DataBind();
            }
        }

        private string method_4()
        {
            string result = "";
            wap_book_BLL wap_book_BLL = new wap_book_BLL(string_36);
            string requestValue = GetRequestValue("hidHangBiaoShis");
            if (requestValue != "")
            {
                string[] array = requestValue.Split(',');
                if (array != null && array.Length > 0)
                {
                    try
                    {
                        // ✅ 使用TransactionHelper进行安全的事务性批量删除操作
                        string connectionString = PubConstant.GetConnectionString(string_36);

                        TransactionHelper.ExecuteMoneyTransaction(connectionString, (connection, transaction) =>
                        {
                            for (int i = 0; i < array.Length; i++)
                            {
                                long bookId = DapperHelper.SafeParseLong(array[i].ToString(), "帖子ID");

                                // 1. 删除主帖（通过BLL）
                                wap_book_BLL.Delete(bookId);

                                // 2. 删除相关回复
                                string deleteReplySql = "DELETE FROM wap_bookre WHERE bookid = @BookId";
                                connection.Execute(deleteReplySql, new { BookId = bookId }, transaction);
                            }
                        });
                    }
                    catch (Exception ex)
                    {
                        result = ex.Message;
                    }
                    finally
                    {
                    }
                }
            }
            return result;
        }

        private string method_5()
        {
            string result = "";
            Page.ClientScript.RegisterClientScriptBlock(GetType(), "clientScript", "<script language=javascript>document.body.style.cursor='wait';</script>", addScriptTags: true);
            string requestValue = GetRequestValue("hidHangBiaoShis");
            if (requestValue != "")
            {
                string[] array = requestValue.Split(',');
                SqlConnection sqlConnection = new SqlConnection(PubConstant.GetConnectionString(string_36));
                sqlConnection.Open();
                if (array != null && array.Length > 0)
                {
                    try
                    {
                        // ✅ 使用DapperHelper进行安全的批量审核通过操作
                        string connectionString = PubConstant.GetConnectionString(string_36);

                        for (int i = 0; i < array.Length; i++)
                        {
                            string updateSql = "UPDATE wap_bbs SET ischeck = 0 WHERE id = @PostId";
                            DapperHelper.Execute(connectionString, updateSql, new
                            {
                                PostId = DapperHelper.SafeParseLong(array[i].ToString(), "帖子ID")
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        result = ex.Message;
                    }
                    finally
                    {
                        Page.ClientScript.RegisterClientScriptBlock(GetType(), "clientScript", "<script language=javascript>document.body.style.cursor='default';</script>", addScriptTags: true);
                    }
                }
                sqlConnection.Close();
            }
            return result;
        }

        private string method_6()
        {
            string result = "";
            string requestValue = GetRequestValue("hidHangBiaoShis");
            if (requestValue != "")
            {
                string[] array = requestValue.Split(',');
                SqlConnection sqlConnection = new SqlConnection(PubConstant.GetConnectionString(string_36));
                sqlConnection.Open();
                if (array != null && array.Length > 0)
                {
                    try
                    {
                        // ✅ 使用DapperHelper进行安全的批量审核拒绝操作
                        string connectionString = PubConstant.GetConnectionString(string_36);

                        for (int i = 0; i < array.Length; i++)
                        {
                            string updateSql = "UPDATE wap_bbs SET ischeck = 1 WHERE id = @PostId";
                            DapperHelper.Execute(connectionString, updateSql, new
                            {
                                PostId = DapperHelper.SafeParseLong(array[i].ToString(), "帖子ID")
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        result = ex.Message;
                    }
                    finally
                    {
                    }
                }
                sqlConnection.Close();
            }
            return result;
        }

        private string method_7()
        {
            return "";
        }

        /// <summary>
        /// ✅ 安全验证：对排序字段进行白名单验证，防止SQL注入
        /// </summary>
        /// <param name="requestedField">请求的排序字段</param>
        /// <returns>安全的排序字段</returns>
        private string ValidateOrderField(string requestedField)
        {
            // 定义允许的排序字段白名单
            var allowedFields = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "id",           // 回复ID
                "userid",       // 用户ID
                "content",      // 回复内容
                "redate",       // 回复时间
                "book_top"      // 置顶状态
            };

            // 如果请求的字段在白名单中，返回该字段；否则返回默认的"id"
            return allowedFields.Contains(requestedField) ? requestedField : "id";
        }

        /// <summary>
        /// ✅ 安全验证：对排序方向进行验证，防止SQL注入
        /// </summary>
        /// <param name="requestedType">请求的排序方向</param>
        /// <returns>安全的排序方向</returns>
        private string ValidateOrderType(string requestedType)
        {
            // 只允许 "0" (ASC) 和 "1" (DESC)
            return (requestedType == "0" || requestedType == "1") ? requestedType : "1";
        }

        protected void AspNetPager1_PageChanged(object sender, PageChangedEventArgs e)
        {
            AspNetPager1.CurrentPageIndex = e.NewPageIndex;
            method_2();
        }
    }
}