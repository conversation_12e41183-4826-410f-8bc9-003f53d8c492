// 免刷新提交聊天消息脚本 for ChuiNiu/Index.aspx
// 依赖页面结构：
// 聊天表单：form[action$='games/chat/book_re.aspx']
// 聊天消息容器：#chat-messages-container
// 挑战列表容器：#challenges-list-container

(function () {
    // 获取聊天表单和消息容器
    const chatForm = document.querySelector("form[action$='games/chat/book_re.aspx']");
    const chatInput = chatForm ? chatForm.querySelector("input[name='content']") : null;
    const chatContainer = document.getElementById('chat-messages-container');
    const challengesContainer = document.getElementById('challenges-list-container');
    if (!chatForm || !chatInput || !chatContainer) return;
    // 聊天框父容器，用于插入 toast，并设为相对定位
    const chatWrapper = chatContainer.parentElement;
    chatWrapper.style.position = 'relative';
    // 挑战列表父容器，用于插入专用toast
    const challengesWrapper = challengesContainer ? challengesContainer.parentElement : null;
    if (challengesWrapper) {
        challengesWrapper.style.position = 'relative';
    }

    // 添加样式
    function addStyles() {
        const style = document.createElement('style');
        style.textContent = `
            #chat-fullscreen-btn {
                position: absolute;
                top: 5px;
                right: 5px;
                z-index: 4; /* 确保低于header的z-index */
                width: 32px;
                //height: 32px;
                background: rgba(255, 255, 255, 0.7);
                color: #4a5568;
                border: none;
                border-radius: 50%;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                #box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                transition: all 0.2s ease;
            }
            #chat-fullscreen-btn:hover {
                background: rgba(255, 255, 255, 0.9);
                color: #38b2ac;
                #box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
            }
            
            /* 确保header置顶 */
            header.bg-gradient-to-r.from-teal-500.to-teal-700 {
                z-index: 10 !important;
            }
        `;
        document.head.appendChild(style);
    }
    addStyles();

    // 全屏切换状态
    let isFullscreen = localStorage.getItem('chat-fullscreen') === 'true';

    // 动态插入刷新按钮到"游戏闲聊"和"进行中的挑战"标题所在flex容器的最右侧
    function insertButtons() {
        // 查找包含h2"游戏闲聊"的flex容器
        const flexDivs = document.querySelectorAll('div.flex.justify-between.items-center.mb-3');
        let chatTitleDiv = null;
        let challengesTitleDiv = null;

        flexDivs.forEach(div => {
            const h2 = div.querySelector('h2');
            if (h2) {
                if (h2.textContent.includes('游戏闲聊')) {
                    chatTitleDiv = div;
                } else if (h2.textContent.includes('进行中的挑战')) {
                    challengesTitleDiv = div;
                }
            }
        });

        // 插入挑战列表刷新按钮
        if (challengesTitleDiv && !document.getElementById('challenges-refresh-btn')) {
            // 创建刷新按钮
            const challengesBtn = document.createElement('button');
            challengesBtn.id = 'challenges-refresh-btn';
            challengesBtn.type = 'button';
            challengesBtn.title = '刷新挑战列表';
            challengesBtn.className = 'ml-auto rounded-full w-8 h-8 flex items-center justify-center text-gray-400 transition-colors hover:bg-gray-200';
            challengesBtn.innerHTML = '<i class="fas fa-rotate"></i>';

            // 将"X个挑战"的span移动到h2后面，将刷新按钮放在最右侧
            const challengeCountSpan = challengesTitleDiv.querySelector('span');
            if (challengeCountSpan) {
                // 先将span从DOM中移除
                challengeCountSpan.remove();

                // 获取h2元素
                const h2 = challengesTitleDiv.querySelector('h2');

                // 在h2后面插入span，在标题行最右侧插入刷新按钮
                if (h2) {
                    h2.after(challengeCountSpan);
                    // 给span添加左侧margin
                    challengeCountSpan.classList.add('ml-2');
                    // 移除可能有的ml-auto
                    challengeCountSpan.classList.remove('ml-auto');
                }

                // 将刷新按钮添加到最右侧
                challengesTitleDiv.appendChild(challengesBtn);
            } else {
                // 如果没有找到span，直接添加到最右侧
                challengesTitleDiv.appendChild(challengesBtn);
            }

            // 防抖变量
            let lastChallengesClick = 0;
            // 绑定刷新事件
            challengesBtn.addEventListener('click', function () {
                const now = Date.now();
                if (now - lastChallengesClick < 5000) return;
                lastChallengesClick = now;
                const icon = challengesBtn.querySelector('i');
                // 变绿色
                challengesBtn.classList.remove('text-gray-400');
                challengesBtn.classList.add('text-teal-600');
                if (icon) {
                    icon.classList.add('animate-spin');
                    setTimeout(() => {
                        icon.classList.remove('animate-spin');
                        challengesBtn.disabled = true;
                        challengesBtn.style.opacity = '0.5';
                        // 恢复灰色
                        challengesBtn.classList.remove('text-teal-600');
                        challengesBtn.classList.add('text-gray-400');
                    }, 1000);
                } else {
                    challengesBtn.disabled = true;
                    challengesBtn.style.opacity = '0.5';
                    challengesBtn.classList.remove('text-teal-600');
                    challengesBtn.classList.add('text-gray-400');
                }
                reloadChallengesList();
                setTimeout(() => {
                    challengesBtn.disabled = false;
                    challengesBtn.style.opacity = '';
                }, 5000);
            });
        }

        // 插入聊天刷新按钮
        if (chatTitleDiv && !document.getElementById('chat-refresh-btn')) {
            // 创建刷新按钮
            const btn = document.createElement('button');
            btn.id = 'chat-refresh-btn';
            btn.type = 'button';
            btn.title = '刷新聊天';
            btn.className = 'ml-auto rounded-full w-8 h-8 flex items-center justify-center text-gray-400 transition-colors hover:bg-gray-200';
            btn.innerHTML = '<i class="fas fa-rotate"></i>';
            // 插入到flex容器最后
            chatTitleDiv.appendChild(btn);

            // 创建全屏按钮 - 插入到聊天容器右上角
            const fullscreenBtn = document.createElement('button');
            fullscreenBtn.id = 'chat-fullscreen-btn';
            fullscreenBtn.type = 'button';
            fullscreenBtn.title = isFullscreen ? '收起聊天' : '全屏聊天';
            fullscreenBtn.innerHTML = isFullscreen ?
                '<i class="fas fa-compress-alt"></i>' :
                '<i class="fas fa-expand-alt"></i>';

            // 插入到聊天容器的父元素
            chatWrapper.appendChild(fullscreenBtn);

            // 应用初始状态
            if (isFullscreen) {
                // 直接修改classList，切换max-h-64和max-h-full
                chatContainer.classList.remove('max-h-64');
                chatContainer.classList.add('max-h-full');
            }

            // 绑定全屏切换事件
            fullscreenBtn.addEventListener('click', function () {
                isFullscreen = !isFullscreen;
                // 更新图标
                fullscreenBtn.innerHTML = isFullscreen ?
                    '<i class="fas fa-compress-alt"></i>' :
                    '<i class="fas fa-expand-alt"></i>';
                fullscreenBtn.title = isFullscreen ? '收起聊天' : '全屏聊天';

                // 直接切换max-h-80和max-h-full类
                if (isFullscreen) {
                    chatContainer.classList.remove('max-h-64');
                    chatContainer.classList.add('max-h-full');
                } else {
                    chatContainer.classList.remove('max-h-full');
                    chatContainer.classList.add('max-h-64');
                }

                // 保存状态到 localStorage
                localStorage.setItem('chat-fullscreen', isFullscreen);

                // 滚动到底部
                setTimeout(() => {
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                }, 100);
            });

            // 防抖变量
            let lastClick = 0;
            // 绑定刷新事件
            btn.addEventListener('click', function () {
                const now = Date.now();
                if (now - lastClick < 5000) return;
                lastClick = now;
                const icon = btn.querySelector('i');
                // 变绿色
                btn.classList.remove('text-gray-400');
                btn.classList.add('text-teal-600');
                if (icon) {
                    icon.classList.add('animate-spin');
                    setTimeout(() => {
                        icon.classList.remove('animate-spin');
                        btn.disabled = true;
                        btn.style.opacity = '0.5';
                        // 恢复灰色
                        btn.classList.remove('text-teal-600');
                        btn.classList.add('text-gray-400');
                    }, 1000);
                } else {
                    btn.disabled = true;
                    btn.style.opacity = '0.5';
                    btn.classList.remove('text-teal-600');
                    btn.classList.add('text-gray-400');
                }
                // 传入true，确保显示提示消息
                reloadChatMessages(true);
                setTimeout(() => {
                    btn.disabled = false;
                    btn.style.opacity = '';
                }, 5000);
            });
        }
    }
    insertButtons();

    // 防止重复绑定
    if (chatForm.dataset.fastpostBound) return;
    chatForm.dataset.fastpostBound = '1';

    // 监听表单提交
    let lastSend = 0;
    chatForm.addEventListener('submit', function (e) {
        e.preventDefault();
        const now = Date.now();
        if (now - lastSend < 3000) return;
        lastSend = now;
        const content = chatInput.value.trim();
        if (!content) return;
        // 构造FormData
        const formData = new FormData(chatForm);
        // 禁用输入和按钮，防止重复提交
        chatInput.disabled = true;
        const submitBtn = chatForm.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
            submitBtn.style.opacity = '0.5';
        }
        // 取表单 action 属性
        const postUrl = chatForm.getAttribute('action');
        // 发送AJAX请求
        fetch(postUrl, {
            method: 'POST',
            body: formData
        })
            .then(res => res.text())
            .then(html => {
                // 简单判断是否成功（可根据后端返回内容调整）
                if (html.includes('内容不能为空') || html.includes('不能为空')) {
                    showToast('内容不能为空！');
                } else if (html.includes('请再过')) {
                    showToast('操作过快，请稍后再试！');
                } else if (html.includes('请不要发重复内容')) {
                    showToast('请不要发重复内容！');
                } else {
                    // 提交成功，刷新聊天消息，不显示额外提示
                    reloadChatMessages(false);
                    chatInput.value = '';
                }
            })
            .catch(() => {
                showToast('发送失败，请重试！');
            })
            .finally(() => {
                chatInput.disabled = false;
                if (submitBtn) {
                    setTimeout(() => {
                        submitBtn.disabled = false;
                        submitBtn.style.opacity = '';
                    }, 3000);
                }
                chatInput.focus();
            });
    });

    // 免刷新加载聊天消息
    function reloadChatMessages(showNotification = true) {
        // 获取当前页面必要参数
        const classid = chatForm.querySelector("input[name='classid']")?.value;
        const siteid = chatForm.querySelector("input[name='siteid']")?.value;
        const nid = chatForm.querySelector("input[name='nid']")?.value;
        if (!classid || !siteid || !nid) return;

        // 显示加载提示（仅当需要显示通知时）
        if (showNotification) {
            showToast('正在刷新聊天...');
        }

        // 构造获取聊天消息的URL（与后端实际URL保持一致）
        // 这里假设聊天消息可通过当前页面重新加载，或有专门的API
        // 采用重新请求当前页面并提取聊天区域的方式
        fetch(window.location.href, { cache: 'reload' })
            .then(res => res.text())
            .then(html => {
                // 用DOM解析器提取聊天区域
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newChat = doc.getElementById('chat-messages-container');
                if (newChat) {
                    // 保存原来的类
                    const classes = chatContainer.className;
                    // 更新内容
                    chatContainer.innerHTML = newChat.innerHTML;
                    // 恢复类
                    chatContainer.className = classes;
                    // 滚动到底部
                    chatContainer.scrollTop = chatContainer.scrollHeight;

                    // 显示成功提示（仅当需要显示通知时）
                    if (showNotification) {
                        showToast('聊天消息已更新');
                    }
                } else if (showNotification) {
                    showToast('刷新失败，请重试');
                }
            })
            .catch(err => {
                console.error('刷新聊天失败:', err);
                if (showNotification) {
                    showToast('刷新失败，请重试');
                }
            });
    }

    // 免刷新加载挑战列表
    function reloadChallengesList() {
        // 检查挑战列表容器是否存在
        if (!challengesContainer) return;

        // 显示加载提示或状态
        showChallengesListToast('正在刷新列表...');

        // 采用重新请求当前页面并提取挑战列表区域的方式
        fetch(window.location.href, { cache: 'reload' })
            .then(res => res.text())
            .then(html => {
                // 用DOM解析器提取挑战列表区域
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const newChallengesList = doc.getElementById('challenges-list-container');

                if (newChallengesList) {
                    // 保存原来的类
                    const classes = challengesContainer.className;
                    // 更新内容
                    challengesContainer.innerHTML = newChallengesList.innerHTML;
                    // 恢复类
                    challengesContainer.className = classes;

                    // 更新挑战数量显示
                    const flexDivs = document.querySelectorAll('div.flex.justify-between.items-center.mb-3');
                    let challengesTitleDiv = null;
                    flexDivs.forEach(div => {
                        const h2 = div.querySelector('h2');
                        if (h2 && h2.textContent.includes('进行中的挑战')) {
                            challengesTitleDiv = div;
                        }
                    });

                    if (challengesTitleDiv) {
                        const oldCountSpan = challengesTitleDiv.querySelector('span.bg-amber-100');
                        const newCountSpan = doc.querySelector('div.flex.justify-between.items-center.mb-3 span.bg-amber-100');
                        if (oldCountSpan && newCountSpan) {
                            oldCountSpan.textContent = newCountSpan.textContent;
                        }
                    }

                    showChallengesListToast('挑战列表已更新');
                }
            })
            .catch(err => {
                console.error('加载挑战列表失败:', err);
                showChallengesListToast('加载挑战列表失败');
            });
    }

    // 页面加载时自动滚动到底部
    window.addEventListener('DOMContentLoaded', function () {
        chatContainer.scrollTop = chatContainer.scrollHeight;
    });

    // 确保header的z-index高于聊天框
    function ensureHeaderZIndex() {
        const header = document.querySelector('header.bg-gradient-to-r.from-teal-500.to-teal-700');
        if (header) {
            // 确保header比聊天框按钮的z-index高
            header.style.zIndex = '10';
        }
    }
    ensureHeaderZIndex();

    // 简易toast提示 - 聊天区域
    function showToast(msg) {
        let toast = document.getElementById('chat-toast');
        if (!toast) {
            toast = document.createElement('div');
            toast.id = 'chat-toast';
            Object.assign(toast.style, {
                position: 'absolute',
                background: 'rgba(60,60,60,0.95)',
                color: '#fff',
                padding: '8px 20px',
                borderRadius: '8px',
                fontSize: '15px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.12)',
                textAlign: 'center',
                zIndex: '10',
                opacity: '0',
                transition: 'opacity 0.3s ease',
            });
            chatWrapper.appendChild(toast);
        }
        toast.textContent = msg;
        // 定位到输入框上方
        const inputHeight = chatForm.offsetHeight;
        toast.style.bottom = (inputHeight + 24) + 'px';
        toast.style.left = '50%';
        toast.style.transform = 'translateX(-50%)';
        // 显示动画
        requestAnimationFrame(() => {
            toast.style.opacity = '1';
        });
        clearTimeout(toast._timer);
        toast._timer = setTimeout(() => {
            toast.style.opacity = '0';
        }, 1800);
    }

    // 简易toast提示 - 挑战列表区域
    function showChallengesListToast(msg) {
        if (!challengesWrapper) return;

        let toast = document.getElementById('challenges-toast');
        if (!toast) {
            toast = document.createElement('div');
            toast.id = 'challenges-toast';
            Object.assign(toast.style, {
                position: 'absolute',
                background: 'rgba(60,60,60,0.95)',
                color: '#fff',
                padding: '8px 20px',
                borderRadius: '8px',
                fontSize: '15px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.12)',
                textAlign: 'center',
                zIndex: '10',
                opacity: '0',
                transition: 'opacity 0.3s ease',
                top: '-40px', // 显示在挑战列表上方
                left: '50%',
                transform: 'translateX(-50%)',
            });
            challengesWrapper.appendChild(toast);
        }
        toast.textContent = msg;

        // 显示动画
        requestAnimationFrame(() => {
            toast.style.opacity = '1';
        });
        clearTimeout(toast._timer);
        toast._timer = setTimeout(() => {
            toast.style.opacity = '0';
        }, 1800);
    }
})();