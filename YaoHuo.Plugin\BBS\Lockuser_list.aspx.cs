using System;
using System.Collections.Generic;
using System.Data;
using System.Text;
using Dapper;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
	public class LockUser_List : MyPageWap
    {
		private string string_10 = PubConstant.GetAppString("InstanceName");

		public string action = "";

		public string linkURL = "";

		public string condition = "";

		public string ERROR = "";

		public List<user_lock_Model> listVo = null;

		public StringBuilder strhtml = new StringBuilder();

		public long long_0 = 1L;

		public long index = 0L;

		public long total = 0L;

		public long pageSize = 10L;

		public long CurrentPage = 1L;

		public string toclassid = "";

		public string touserid = "";

		public string backurlid = "";

		public string id = "";

		protected void Page_Load(object sender, EventArgs e)
		{
			action = GetRequestValue("action");
			backurlid = GetRequestValue("backurlid");
			id = GetRequestValue("id");
			string text = action;
			if (text != null && text == "class")
			{
				showclass();
			}
			else
			{
				showclass();
			}
		}

		public void showclass()
		{
			toclassid = GetRequestValue("toclassid").Trim();
			touserid = GetRequestValue("touserid").Trim();
			if (toclassid == "")
			{
				toclassid = classid;
			}

			try
			{
				if (classVo.ismodel < 1L)
				{
					pageSize = siteVo.MaxPerPage_Default;
				}
				else
				{
					pageSize = classVo.ismodel;
				}

				// ✅ 使用QueryBuilder构建安全的查询条件
				var queryBuilder = new QueryBuilder()
					.Where("siteid = @ParamN", DapperHelper.SafeParseLong(siteid, "站点ID"));

				// 添加版块条件
				if (!string.IsNullOrEmpty(toclassid) && toclassid != "0")
				{
					queryBuilder.Where("classid = @ParamN", DapperHelper.SafeParseLong(toclassid, "版块ID"));
				}

				// 添加用户条件
				if (!string.IsNullOrEmpty(touserid) && touserid != "0")
				{
					queryBuilder.Where("lockuserid = @ParamN", DapperHelper.SafeParseLong(touserid, "被封用户ID"));
				}

				// ✅ 使用安全的分页查询
				string connectionString = PubConstant.GetConnectionString(string_10);

				if (GetRequestValue("getTotal") != "")
				{
					total = long.Parse(GetRequestValue("getTotal"));
				}
				else
				{
					// 获取总数
					var (countSql, countParameters) = queryBuilder.Build("SELECT COUNT(*) FROM user_lock");
					total = DapperHelper.ExecuteScalar<long>(connectionString, countSql, countParameters);
				}

				if (GetRequestValue("page") != "")
				{
					CurrentPage = long.Parse(GetRequestValue("page"));
				}
				CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
				index = pageSize * (CurrentPage - 1L);

				// 构建分页链接
				linkURL = http_start + "bbs/lockuser_list.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=" + classid + "&amp;getTotal=" + total + "&amp;backurlid=" + backurlid + "&amp;toclassid=" + toclassid + "&amp;touserid=" + touserid + "&amp;id=" + id;
				linkURL = WapTool.GetPageLink(ver, lang, Convert.ToInt32(total), pageSize, CurrentPage, linkURL);

				// ✅ 使用安全的分页数据查询
				var result = PaginationHelper.GetPagedDataWithBuilder<user_lock_Model>(
					connectionString,
					"SELECT *",
					"user_lock",
					queryBuilder,
					(int)CurrentPage,
					(int)pageSize,
					"id DESC"
				);

				listVo = result.Data;
			}
			catch (Exception ex)
			{
				ERROR = ex.ToString();
			}
		}
	}
}