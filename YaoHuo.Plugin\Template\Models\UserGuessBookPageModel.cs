using System;
using System.Collections.Generic;

namespace YaoHuo.Plugin.Template.Models
{
    /// <summary>
    /// 空间留言板页面数据模型
    /// </summary>
    public class UserGuessBookPageModel : BasePageModelWithPagination
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public UserGuessBookPageModel()
        {
            PageTitle = "空间留言板";
        }

        /// <summary>
        /// 目标用户ID
        /// </summary>
        public string TargetUserId { get; set; } = "";

        /// <summary>
        /// 目标用户昵称
        /// </summary>
        public string TargetUserNickname { get; set; } = "";

        /// <summary>
        /// 是否为自己的空间
        /// </summary>
        public bool IsOwnSpace { get; set; }

        /// <summary>
        /// 留言列表
        /// </summary>
        public List<GuessBookMessageModel> MessagesList { get; set; } = new List<GuessBookMessageModel>();

        /// <summary>
        /// 留言表单数据
        /// </summary>
        public GuessBookFormModel FormData { get; set; } = new GuessBookFormModel();

        /// <summary>
        /// 排序方式（ot参数：0=最新，1=最早）
        /// </summary>
        public string SortOrder { get; set; } = "0";

        /// <summary>
        /// 留言总数
        /// </summary>
        public long TotalMessages { get; set; }

        /// <summary>
        /// 是否显示排序选项卡
        /// </summary>
        public bool ShowSortTabs { get; set; } = true;

        /// <summary>
        /// 当前用户ID（用于权限判断）
        /// </summary>
        public string CurrentUserId { get; set; } = "";

        /// <summary>
        /// 当前用户是否为管理员
        /// </summary>
        public bool IsCurrentUserAdmin { get; set; }

        /// <summary>
        /// 当前用户头像URL
        /// </summary>
        public string CurrentUserAvatarUrl { get; set; } = "";

        /// <summary>
        /// 当前用户是否使用默认头像
        /// </summary>
        public bool CurrentUserIsDefaultAvatar { get; set; } = true;

        /// <summary>
        /// 错误信息（兼容旧版）
        /// </summary>
        public string Error { get; set; } = "";

        /// <summary>
        /// 提示信息（兼容旧版）
        /// </summary>
        public string Info { get; set; } = "";

        /// <summary>
        /// 栏目ID
        /// </summary>
        public string ClassId { get; set; } = "";
    }

    /// <summary>
    /// 单条留言数据模型
    /// </summary>
    public class GuessBookMessageModel
    {
        /// <summary>
        /// 留言ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 留言者昵称
        /// </summary>
        public string AuthorNickname { get; set; } = "";

        /// <summary>
        /// 留言者用户ID
        /// </summary>
        public string AuthorUserId { get; set; } = "";

        /// <summary>
        /// 留言内容（已处理UBB和HTML）
        /// </summary>
        public string Content { get; set; } = "";

        /// <summary>
        /// 留言时间
        /// </summary>
        public DateTime AddTime { get; set; }

        /// <summary>
        /// 友好时间显示（如：刚刚、1小时前、昨天等）
        /// </summary>
        public string FriendlyTime { get; set; } = "";

        /// <summary>
        /// 详细时间显示（如：2024-12-19 14:30:25）
        /// </summary>
        public string DetailTime { get; set; } = "";

        /// <summary>
        /// 留言者空间链接
        /// </summary>
        public string AuthorSpaceUrl { get; set; } = "";



        /// <summary>
        /// 是否可删除（空间主人或管理员）
        /// </summary>
        public bool CanDelete { get; set; }

        /// <summary>
        /// 留言者头像URL（占位符，未来扩展）
        /// </summary>
        public string AuthorAvatarUrl { get; set; } = "";

        /// <summary>
        /// 是否为默认头像（占位符，未来扩展）
        /// </summary>
        public bool IsDefaultAvatar { get; set; } = true;

        /// <summary>
        /// 留言序号（在当前页面中的位置）
        /// </summary>
        public int MessageIndex { get; set; }
    }

    /// <summary>
    /// 留言表单数据模型
    /// </summary>
    public class GuessBookFormModel
    {
        /// <summary>
        /// 留言内容
        /// </summary>
        public string Content { get; set; } = "";

        /// <summary>
        /// 表情选择
        /// </summary>
        public string Face { get; set; } = "face";

        /// <summary>
        /// 返回地址
        /// </summary>
        public string BackUrl { get; set; } = "";

        /// <summary>
        /// 表情选项列表
        /// </summary>
        public List<FaceOptionModel> FaceOptions { get; set; } = new List<FaceOptionModel>();

        /// <summary>
        /// 表单提交地址
        /// </summary>
        public string ActionUrl { get; set; } = "";

        /// <summary>
        /// 是否显示表情选择（占位符，未来扩展）
        /// </summary>
        public bool ShowFaceSelector { get; set; } = false;

        /// <summary>
        /// 最大字符数限制
        /// </summary>
        public int MaxLength { get; set; } = 1000;

        /// <summary>
        /// 最小字符数限制
        /// </summary>
        public int MinLength { get; set; } = 2;
    }

    /// <summary>
    /// 表情选项模型
    /// </summary>
    public class FaceOptionModel
    {
        /// <summary>
        /// 表情值
        /// </summary>
        public string Value { get; set; } = "";

        /// <summary>
        /// 表情显示名称
        /// </summary>
        public string Text { get; set; } = "";

        /// <summary>
        /// 表情图片路径
        /// </summary>
        public string ImagePath { get; set; } = "";

        /// <summary>
        /// 是否为当前选中
        /// </summary>
        public bool IsSelected { get; set; }
    }
}
