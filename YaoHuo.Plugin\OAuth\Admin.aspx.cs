﻿using System;
using System.Collections.Generic;
using System.Text;
using YaoHuo.Plugin.WebSite.Services.Config;

namespace YaoHuo.Plugin.OAuth
{
    /// <summary>
    /// 关联授权应用
    /// </summary>
    public partial class Admin : OAuthBasePage
    {



        /// <summary>
        /// 执行动作
        /// </summary>
        public string Action
        {
            get
            {
                var action = GetRequestValue("action") ?? "default";
                return action.ToLower();
            }
        }

        /// <summary>
        /// 输出网页字符
        /// </summary>
        public StringBuilder strhtml { get; } = new StringBuilder();

        public List<OAuthClient> listVo { get; set; } = null;

        public long kk { get; set; } = 1L;

        public long index { get; set; } = 0L;

        /// <summary>
        /// 加载时
        /// </summary>
        protected void Page_Load(object sender, EventArgs e)
        {
            // 使用基类方法验证用户登录状态
            var userInfo = EnsureUserAuthenticated();
            if (userInfo == null) return;

            // 使用基类方法验证管理员权限 - 只允许超级管理员访问
            if (!EnsureManagerPermission(userInfo, OAuthConstants.MANAGER_SUPER_ADMIN))
                return;
            //执行动作
            strhtml.Append("<div class='title'>");
            strhtml.Append(this.GetLang("授权应用|授權程式|Authorization App"));
            strhtml.Append("</div>");
            if (Action == "addview")
            {
                //新增视图
                strhtml.Append("<div class='tip'>新增授权应用</div>");
                strhtml.Append("<form method='post' action='?action=add'>");

                // 添加 CSRF 保护
                string csrfToken = GenerateFormToken(OAuthConstants.CSRF_ADMIN_FORM);
                strhtml.Append($"<input type='hidden' name='__CSRFToken' value='{csrfToken}' />");

                strhtml.Append("<div class='form-group'>");
                strhtml.Append("<label>应用名称</label>");
                strhtml.Append("<input type='text' name='name' class='form-control' required />");
                strhtml.Append("</div>");
                strhtml.Append("<div class='form-group'>");
                strhtml.Append("<label>应用说明</label>");
                strhtml.Append("<textarea name='remark' class='form-control'></textarea>");
                strhtml.Append("</div>");
                strhtml.Append("<div class='form-group'>");
                strhtml.Append("<label>重定向URL白名单</label>");
                strhtml.Append("<input type='text' name='redirectUris' class='form-control' placeholder='多个URL用分号分隔，如：https://example.com/callback;https://app.com/auth' required />");
                strhtml.Append("</div>");
                strhtml.Append("<button type='submit' class='btn btn-primary'>提交</button>");
                strhtml.Append("</form>");
            }
            else if (Action == "add")
            {
                //限制请求类型
                if (base.Request.HttpMethod.ToLower() != "post")
                {
                    strhtml.Append("<div class='tip'>请求无效</div>");
                    return;
                }

                // 验证 CSRF Token
                string submittedToken = GetRequestValue("__CSRFToken");
                if (!ValidateFormToken(OAuthConstants.CSRF_ADMIN_FORM, submittedToken))
                {
                    strhtml.Append("<div class='tip'>安全验证失败，请重新提交</div>");
                    LogSecurityEvent($"CSRF验证失败: UserID={userInfo.userid}, IP={Request.UserHostAddress}", userInfo.userid.ToString());
                    return;
                }

                //读取参数
                var dateTimeNow = DateTime.Now;
                var name = GetRequestValue("name");
                var remark = GetRequestValue("remark");
                var redirectUris = GetRequestValue("redirectUris");

                if (string.IsNullOrEmpty(name))
                {
                    strhtml.Append("<div class='tip'>应用名称不能为空</div>");
                    return;
                }

                if (string.IsNullOrEmpty(redirectUris))
                {
                    strhtml.Append("<div class='tip'>重定向URL白名单不能为空</div>");
                    return;
                }

                // 验证重定向URL格式
                if (!ValidateRedirectUris(redirectUris))
                {
                    strhtml.Append("<div class='tip'>重定向URL格式无效，请确保使用HTTPS协议</div>");
                    return;
                }

                //新增操作 - 使用新的OAuth服务
                var (client, plainAppKey) = OAuthService.CreateClient(name, remark, redirectUris);
                var getCount = 1; // 新架构中CreateClient成功执行即表示添加成功
                //提示内容
                var msgStr = "新增失败";
                if (getCount > 0)
                {
                    msgStr = "新增成功";

                    // 记录OAuth管理日志
                    OAuthLogger.LogAdminAction("create_client", client.AppId, userInfo.userid, $"创建应用: {name}", Request.UserHostAddress, Request.UserAgent);

                    LogSecurityEvent($"新增OAuth应用: AppName={name}, AppID={client.AppId}, UserID={userInfo.userid}", userInfo.userid.ToString());

                    // 一次性展示完整密钥
                    strhtml.Append("<div class='tip'>");
                    strhtml.Append("✅ 应用创建成功！<br/><br/>");
                    strhtml.Append("<strong style='color: red;'>⚠️ 重要：以下密钥仅显示一次，请立即复制保存！</strong><br/><br/>");
                    strhtml.Append($"<strong>应用ID:</strong> {client.AppId}<br/>");
                    strhtml.Append($"<strong>应用密钥:</strong> <span id='appkey_{client.AppId}' style='background: #f0f0f0; padding: 5px; font-family: monospace;'>{plainAppKey}</span>");
                    strhtml.Append($"<button onclick='copyToClipboard(\"{plainAppKey}\")' style='margin-left: 10px;'>复制密钥</button><br/><br/>");
                    strhtml.Append("<strong style='color: orange;'>注意：刷新页面后将无法再次查看完整密钥！</strong>");
                    strhtml.Append("</div>");

                    // 注意：密钥已经以Hash形式安全存储
                }
                else
                {
                    strhtml.Append($"<div class='tip'>{msgStr}</div>");
                }
            }
            else if (Action == "del")
            {
                //删除操作 - 添加CSRF保护
                var id = GetRequestValue("aid");
                var csrfToken = GetRequestValue("token");

                if (!ValidateFormToken(OAuthConstants.CSRF_INDEX_DELETE, csrfToken))
                {
                    strhtml.Append("<div class='tip'>安全验证失败</div>");
                    LogSecurityEvent($"删除应用CSRF验证失败: AppID={id}, UserID={userInfo.userid}", userInfo.userid.ToString());
                    return;
                }

                var success = OAuthService.DeleteClient(id);
                //提示内容
                var msgStr = "删除失败";
                if (success)
                {
                    msgStr = "删除成功";

                    // 记录OAuth管理日志
                    OAuthLogger.LogAdminAction("delete_client", id, userInfo.userid, "删除应用", Request.UserHostAddress, Request.UserAgent);

                    LogSecurityEvent($"删除OAuth应用: AppID={id}, UserID={userInfo.userid}", userInfo.userid.ToString());
                }
                else
                {
                    // 记录删除失败
                    OAuthLogger.LogAdminAction("delete_client_failed", id, userInfo.userid, "删除应用失败", Request.UserHostAddress, Request.UserAgent);
                }
                strhtml.Append($"<div class='tip'>{msgStr}</div>");
            }

            else
            {
                //默认操作 - 显示应用列表和新增链接
                var clients = OAuthService.GetAllClients();

                // 直接使用新的客户端列表
                listVo = clients;

                // 新架构中所有应用都使用Hash存储，无需迁移提示

                // 添加新增应用的链接
                strhtml.Append("<div class='btBox'>");
                strhtml.Append("<div class='bt2'>");
                strhtml.Append("<a href='/OAuth/Admin.aspx?action=addview'>新增授权应用</a>");
                strhtml.Append("</div>");
                strhtml.Append("</div>");
            }
        }

        #region 安全相关辅助方法

        // 注意：应用ID和密钥生成已移至 OAuthService 中

        /// <summary>
        /// 验证重定向URL格式（优化验证逻辑）
        /// </summary>
        /// <param name="redirectUris"></param>
        /// <returns></returns>
        private bool ValidateRedirectUris(string redirectUris)
        {
            if (string.IsNullOrEmpty(redirectUris))
                return false;

            var uris = redirectUris.Split(';');
            foreach (var uriStr in uris)
            {
                var trimmedUri = uriStr.Trim();
                if (string.IsNullOrEmpty(trimmedUri))
                    continue;

                // 基础 URL 格式验证
                if (!Uri.TryCreate(trimmedUri, UriKind.Absolute, out Uri uri))
                    return false;

                // 验证协议安全性（使用配置注入）
                bool isLocalhost = uri.Host.ToLower() == "localhost" || uri.Host == "127.0.0.1";
                bool requireHttps = OAuthConfigService.RequireHttps();
                bool allowLocalhostHttp = OAuthConfigService.AllowLocalhostHttp();

                bool isValidScheme = uri.Scheme == "https" ||
                                    (!requireHttps || (isLocalhost && allowLocalhostHttp && uri.Scheme == "http"));

                if (!isValidScheme)
                    return false;

                // 验证主机名不为空
                if (string.IsNullOrEmpty(uri.Host))
                    return false;
            }
            return true;
        }

        #endregion
    }
}