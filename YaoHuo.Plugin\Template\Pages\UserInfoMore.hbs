<!-- Font Awesome 图标库 -->
<link href="//lf6-cdn-tos.bytecdntp.com/cdn/expire-1-y/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

<!-- 详细资料展示区域 -->
<section class="p-4">
    <!-- 论坛资料卡片 -->
    <div class="bg-white rounded-xl card-shadow mb-6">
        <div class="p-4 border-b border-gray-100">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-user-circle text-primary mr-2"></i>
                论坛资料
            </h3>
        </div>
        <div>
            <!-- ID号 -->
            <div class="info-item">
                <span class="info-label">ID号</span>
                <span class="info-value">{{BasicInfo.UserId}}</span>
            </div>

            <!-- 昵称 -->
            <div class="info-item">
                <span class="info-label">昵称</span>
                <span class="info-value">{{BasicInfo.DisplayNickname}}</span>
            </div>

            <!-- 妖晶 -->
            <div class="info-item">
                <span class="info-label">妖晶</span>
                <span class="info-value">{{BasicInfo.MoneyDisplay}}</span>
            </div>

            <!-- 经验 -->
            <div class="info-item">
                <span class="info-label">经验</span>
                <span class="info-value">{{BasicInfo.Experience}}</span>
            </div>

            <!-- 等级 -->
            <div class="info-item">
                <span class="info-label">等级</span>
                <span class="info-value">{{#if BasicInfo.Level}}{{BasicInfo.Level}}{{else}}0级{{/if}}</span>
            </div>

            <!-- 头衔 -->
            <div class="info-item">
                <span class="info-label">头衔</span>
                <span class="info-value">{{BasicInfo.Title}}</span>
            </div>

            <!-- 身份 -->
            <div class="info-item">
                <span class="info-label">身份</span>
                <span class="info-value">{{{BasicInfo.Identity}}}</span>
            </div>

            <!-- 权限 -->
            <div class="info-item">
                <span class="info-label">权限</span>
                <span class="info-value">{{{BasicInfo.Permission}}}</span>
            </div>

            <!-- 在线状态 -->
            <div class="info-item">
                <span class="info-label">在线状态</span>
                <span class="info-value">{{{BasicInfo.OnlineStatus}}}</span>
            </div>

            <!-- 累计在线 -->
            <div class="info-item">
                <span class="info-label">累计在线</span>
                <span class="info-value">{{BasicInfo.LoginTimeDisplay}}</span>
            </div>

            <!-- 注册时间 -->
            <div class="info-item">
                <span class="info-label">注册时间</span>
                <span class="info-value">{{BasicInfo.RegisterTime}}</span>
            </div>
        </div>
    </div>

    <!-- 个人信息卡片 -->
    <div class="bg-white rounded-xl card-shadow mb-6">
        <div class="p-4 border-b border-gray-100">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-id-card text-primary mr-2"></i>
                个人信息
            </h3>
        </div>
        <div>
            <!-- 性别 -->
            <div class="info-item">
                <span class="info-label">性别</span>
                <span class="info-value">{{PersonalInfo.SexDisplay}}</span>
            </div>

            <!-- 年龄 -->
            <div class="info-item">
                <span class="info-label">年龄</span>
                <span class="info-value">{{PersonalInfo.Age}}岁</span>
            </div>

            <!-- 身高 -->
            {{#if PersonalInfo.Height}}
            <div class="info-item">
                <span class="info-label">身高</span>
                <span class="info-value">{{PersonalInfo.Height}}</span>
            </div>
            {{/if}}

            <!-- 体重 -->
            {{#if PersonalInfo.Weight}}
            <div class="info-item">
                <span class="info-label">体重</span>
                <span class="info-value">{{PersonalInfo.Weight}}</span>
            </div>
            {{/if}}

            <!-- 星座 -->
            {{#if PersonalInfo.Constellation}}
            <div class="info-item">
                <span class="info-label">星座</span>
                <span class="info-value">{{PersonalInfo.Constellation}}</span>
            </div>
            {{/if}}

            <!-- 爱好 -->
            {{#if PersonalInfo.Hobby}}
            <div class="info-item">
                <span class="info-label">爱好</span>
                <span class="info-value">{{PersonalInfo.Hobby}}</span>
            </div>
            {{/if}}

            <!-- 婚否 -->
            {{#if PersonalInfo.MaritalStatus}}
            <div class="info-item">
                <span class="info-label">婚否</span>
                <span class="info-value">{{PersonalInfo.MaritalStatus}}</span>
            </div>
            {{/if}}

            <!-- 职业 -->
            {{#if PersonalInfo.Profession}}
            <div class="info-item">
                <span class="info-label">职业</span>
                <span class="info-value">{{PersonalInfo.Profession}}</span>
            </div>
            {{/if}}

            <!-- 城市 -->
            {{#if PersonalInfo.City}}
            <div class="info-item">
                <span class="info-label">城市</span>
                <span class="info-value">{{PersonalInfo.City}}</span>
            </div>
            {{/if}}
        </div>
    </div>
</section>

