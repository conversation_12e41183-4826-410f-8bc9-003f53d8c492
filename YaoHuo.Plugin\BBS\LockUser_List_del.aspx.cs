﻿using System;
using Dapper;
using KeLin.ClassManager;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class LockUser_List_del : MyPageWap
    {
        private string a = PubConstant.GetAppString("InstanceName");

        public string action = "";

        public string id = "";

        public string toclassid = "";

        public string touserid = "";

        public string backurlid = "";

        public string delid = "";

        public string lpage = "";

        public string INFO = "";

        public string ERROR = "";

        protected void Page_Load(object sender, EventArgs e)
        {
            while (true)
            {
                action = GetRequestValue("action");
                id = GetRequestValue("id");
                toclassid = GetRequestValue("toclassid");
                touserid = GetRequestValue("touserid");
                backurlid = GetRequestValue("backurlid");
                delid = GetRequestValue("delid");
                CheckManagerLvl("04", classVo.adminusername, "bbs/book_list.aspx?siteid=" + siteid + "&amp;classid=" + classid + "&amp;page=" + lpage + "&amp;id=" + id);
                needPassWordToAdmin();
                bool flag = !(action == "godel");
                int num = 1;
                while (true)
                {
                    switch (num)
                    {
                        case 1:
                            if (true)
                            {
                            }
                            if (!flag)
                            {
                                num = 2;
                                continue;
                            }
                            return;
                        case 3:
                            try
                            {
                                // ✅ 使用DapperHelper安全删除和记录日志，避免SQL注入
                                UnlockUserSafely();
                                INFO = "OK";
                            }
                            catch (Exception ex)
                            {
                                ERROR = ex.ToString();
                            }
                            num = 0;
                            continue;
                        case 2:
                            num = 3;
                            continue;
                        case 0:
                            return;
                    }
                    break;
                }
            }
        }

        /// <summary>
        /// 使用DapperHelper安全解封用户并记录日志，避免SQL注入
        /// </summary>
        private void UnlockUserSafely()
        {
            string unlockConnectionString = PubConstant.GetConnectionString(a);

            // 使用事务确保数据一致性
            DapperHelper.ExecuteInTransaction(unlockConnectionString, (connection, transaction) =>
            {
                // 删除用户封禁记录
                string deleteSql = "DELETE FROM user_lock WHERE siteid = @SiteId AND id = @DelId";
                connection.Execute(deleteSql, new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    DelId = DapperHelper.SafeParseLong(delid, "封禁记录ID")
                }, transaction);

                // 记录操作日志
                string logSql = @"INSERT INTO wap_log(siteid, oper_userid, oper_nickname, oper_type, log_info, oper_ip)
                                VALUES (@SiteId, @UserId, @Nickname, 0, @LogInfo, @IP)";
                connection.Execute(logSql, new {
                    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                    UserId = DapperHelper.SafeParseLong(userid, "操作用户ID"),
                    Nickname = DapperHelper.LimitLength(nickname, 50),
                    LogInfo = "解除加黑用户ID" + DapperHelper.LimitLength(touserid, 20),
                    IP = DapperHelper.LimitLength(IP, 50)
                }, transaction);
            });
        }
    }
}