/**
 * 勋章页面
 * 应用TabSwitchService和FilterService实现标签切换和分类筛选
 * 替换原有的内联JavaScript，提供更好的类型安全和代码复用
 * 
 * @version 1.0
 * <AUTHOR>
 * @date 2025-01-07
 */

import { TabSwitchService, TabGroupConfig } from '../services/TabSwitchService.js';
import { FilterService, FilterConfig } from '../services/FilterService.js';

/**
 * Medal页面类
 * 负责勋章页面的标签切换、分类筛选和模态框管理
 */
export class MedalPage {
    private static instance: MedalPage;
    private currentFunction: string = 'apply';
    private isExperienceMode: boolean = false;

    /**
     * 获取单例实例
     */
    public static getInstance(): MedalPage {
        if (!MedalPage.instance) {
            MedalPage.instance = new MedalPage();
        }
        return MedalPage.instance;
    }

    /**
     * 初始化页面
     */
    public static init(): void {
        MedalPage.getInstance().initialize();
    }

    /**
     * 显示帮助模态框
     */
    public static showHelp(): void {
        MedalPage.getInstance().showHelpModal();
    }

    /**
     * 隐藏帮助模态框
     */
    public static hideHelp(): void {
        MedalPage.getInstance().hideHelpModal();
    }

    /**
     * 初始化页面功能
     */
    public initialize(): void {
        console.log('Medal页面初始化开始');

        // 读取页面配置
        this.loadPageConfig();

        // 初始化Lucide图标
        this.initLucideIcons();

        // 初始化功能切换标签
        this.initFunctionTabs();

        // 初始化分类筛选
        this.initCategoryFilters();

        // 绑定帮助模态框事件
        this.bindHelpModalEvents();

        console.log('Medal页面初始化完成');
    }

    /**
     * 加载页面配置
     */
    private loadPageConfig(): void {
        // 从页面配置元素读取配置
        const configElement = document.getElementById('page-config') as HTMLElement;
        if (configElement) {
            this.currentFunction = configElement.dataset.pageType || 'apply';
            this.isExperienceMode = configElement.dataset.isExperienceMode === 'true';
        } else {
            // 降级方案：从URL参数读取
            const urlParams = new URLSearchParams(window.location.search);
            this.currentFunction = urlParams.get('type') || 'apply';
            this.isExperienceMode = urlParams.has('ui');
        }

        console.log(`Medal页面配置: 功能=${this.currentFunction}, 体验模式=${this.isExperienceMode}`);
    }

    /**
     * 初始化Lucide图标
     */
    private initLucideIcons(): void {
        if (typeof (window as any).lucide !== 'undefined') {
            (window as any).lucide.createIcons();
            console.log('Medal页面: Lucide图标初始化完成');
        }
    }

    /**
     * 初始化功能切换标签
     */
    private initFunctionTabs(): void {
        const functionTabConfig: TabGroupConfig = {
            groupSelector: '.function-tab',
            activeClass: 'active',
            updateUrl: true,
            preserveParams: this.isExperienceMode ? ['ui'] : [],
            tabs: [
                {
                    id: 'apply',
                    selector: '[data-function="apply"]',
                    contentSelector: '#applyMedals',
                    urlParam: 'type',
                    urlValue: 'apply',
                    onActivate: () => {
                        this.onApplyTabActivate();
                    }
                },
                {
                    id: 'purchase',
                    selector: '[data-function="purchase"]',
                    contentSelector: '#purchaseMedals',
                    urlParam: 'type',
                    urlValue: 'buy',
                    onActivate: () => {
                        this.onPurchaseTabActivate();
                    }
                }
            ],
            onChange: (activeTab) => {
                this.currentFunction = activeTab.id;
                console.log(`功能切换到: ${activeTab.id}`);
                
                // 重置对应的分类筛选
                this.resetCategoryFilter();
            }
        };

        TabSwitchService.initTabGroup('function-tabs', functionTabConfig);
    }

    /**
     * 初始化分类筛选
     */
    private initCategoryFilters(): void {
        // 申请勋章分类筛选
        const applyFilterConfig: FilterConfig = {
            filterId: 'apply-filter',
            filterTabsSelector: '#applyFilterTabs .filter-tab',
            itemsConfig: {
                selector: '#applyMedals .medal-item',
                dataAttribute: 'data-category'
            },
            activeClass: 'active',
            defaultFilter: 'all',
            onFilterChange: (filter, count) => {
                console.log(`申请勋章筛选: ${filter}, 显示数量: ${count}`);
            }
        };

        // 购买勋章分类筛选
        const purchaseFilterConfig: FilterConfig = {
            filterId: 'purchase-filter',
            filterTabsSelector: '#purchaseFilterTabs .filter-tab',
            itemsConfig: {
                selector: '#purchaseMedals .medal-item',
                dataAttribute: 'data-category'
            },
            activeClass: 'active',
            defaultFilter: 'all',
            onFilterChange: (filter, count) => {
                console.log(`购买勋章筛选: ${filter}, 显示数量: ${count}`);
            }
        };

        FilterService.initFilter(applyFilterConfig);
        FilterService.initFilter(purchaseFilterConfig);
    }

    /**
     * 申请标签激活时的处理
     */
    private onApplyTabActivate(): void {
        // 显示申请相关的筛选标签
        const applyFilterTabs = document.getElementById('applyFilterTabs');
        const purchaseFilterTabs = document.getElementById('purchaseFilterTabs');
        
        if (applyFilterTabs) applyFilterTabs.style.display = 'flex';
        if (purchaseFilterTabs) purchaseFilterTabs.style.display = 'none';
    }

    /**
     * 购买标签激活时的处理
     */
    private onPurchaseTabActivate(): void {
        // 显示购买相关的筛选标签
        const applyFilterTabs = document.getElementById('applyFilterTabs');
        const purchaseFilterTabs = document.getElementById('purchaseFilterTabs');
        
        if (applyFilterTabs) applyFilterTabs.style.display = 'none';
        if (purchaseFilterTabs) purchaseFilterTabs.style.display = 'flex';
    }

    /**
     * 重置分类筛选
     */
    private resetCategoryFilter(): void {
        if (this.currentFunction === 'apply') {
            FilterService.resetFilter('apply-filter');
        } else {
            FilterService.resetFilter('purchase-filter');
        }
    }

    /**
     * 绑定帮助模态框事件
     */
    private bindHelpModalEvents(): void {
        // 将方法暴露到全局作用域，供HTML onclick调用
        (window as any).showHelpModal = () => this.showHelpModal();
        (window as any).hideHelpModal = () => this.hideHelpModal();
    }

    /**
     * 显示帮助模态框
     */
    public showHelpModal(): void {
        const modal = document.getElementById('helpModal') as HTMLElement;
        if (modal) {
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            console.log('显示申请流程说明弹窗');
        }
    }

    /**
     * 隐藏帮助模态框
     */
    public hideHelpModal(): void {
        const modal = document.getElementById('helpModal') as HTMLElement;
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
            console.log('隐藏申请流程说明弹窗');
        }
    }

    /**
     * 获取当前功能状态
     */
    public getCurrentFunction(): string {
        return this.currentFunction;
    }

    /**
     * 获取当前筛选状态
     */
    public getCurrentFilter(): string | undefined {
        const filterId = this.currentFunction === 'apply' ? 'apply-filter' : 'purchase-filter';
        return FilterService.getCurrentFilter(filterId);
    }

    /**
     * 获取页面统计信息
     */
    public getPageStats(): { function: string; filter: string; applyCount: number; purchaseCount: number } {
        const applyStats = FilterService.getInstance().getFilterStats('apply-filter');
        const purchaseStats = FilterService.getInstance().getFilterStats('purchase-filter');
        
        return {
            function: this.currentFunction,
            filter: this.getCurrentFilter() || 'all',
            applyCount: applyStats.visible,
            purchaseCount: purchaseStats.visible
        };
    }
}

// ==================== 页面初始化 ====================

/**
 * 页面DOM加载完成后自动初始化
 */
document.addEventListener('DOMContentLoaded', () => {
    MedalPage.init();
});

// ==================== 全局函数，供模板调用（向后兼容） ====================

/**
 * 显示帮助模态框（向后兼容）
 */
export function showHelpModal(): void {
    MedalPage.showHelp();
}

/**
 * 隐藏帮助模态框（向后兼容）
 */
export function hideHelpModal(): void {
    MedalPage.hideHelp();
}

/**
 * 获取页面实例
 */
export function getMedalPage(): MedalPage {
    return MedalPage.getInstance();
}

// 导出默认类
export default MedalPage;
