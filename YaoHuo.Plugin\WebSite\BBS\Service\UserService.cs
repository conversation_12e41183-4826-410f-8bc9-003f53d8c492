using System;
using System.Collections.Generic;
using System.Linq;
using KeLin.ClassManager.Model;
using KeLin.ClassManager.BLL;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.WebSite.BBS.Service
{
    /// <summary>
    /// 用户信息安全查询服务
    /// 集中化用户信息查询逻辑，消除重复代码，提供统一的安全查询接口
    /// </summary>
    public static class UserService
    {
        /// <summary>
        /// 安全获取单个用户信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">站点ID</param>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <returns>用户信息模型，不存在返回null</returns>
        public static user_Model GetUserInfoSafely(string userId, string siteId, string connectionString)
        {
            try
            {
                // 输入验证
                if (string.IsNullOrEmpty(userId) || !WapTool.IsNumeric(userId))
                    return null;

                if (string.IsNullOrEmpty(siteId) || !WapTool.IsNumeric(siteId))
                    return null;

                if (string.IsNullOrEmpty(connectionString))
                    return null;

                // ✅ 使用DapperHelper进行安全的参数化查询
                string userSql = "SELECT * FROM [user] WHERE userid = @UserId AND siteid = @SiteId";

                var result = DapperHelper.Query<user_Model>(connectionString, userSql, new {
                    UserId = DapperHelper.SafeParseLong(userId, "用户ID"),
                    SiteId = DapperHelper.SafeParseLong(siteId, "站点ID")
                });

                var userInfo = result?.FirstOrDefault();

                // 验证返回结果的站点ID匹配（防止跨站点数据访问）
                if (userInfo != null && userInfo.siteid.ToString() != siteId)
                {
                    System.Diagnostics.Debug.WriteLine($"UserService: 站点ID不匹配，用户ID={userId}，期望站点={siteId}，实际站点={userInfo.siteid}");
                    return null;
                }

                return userInfo;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"UserService.GetUserInfoSafely失败: userId={userId}, siteId={siteId}, error={ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 安全获取用户列表（批量查询）
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <returns>用户信息列表</returns>
        public static List<user_Model> GetUserListByIds(List<long> userIds, string connectionString)
        {
            try
            {
                if (userIds == null || userIds.Count == 0)
                    return new List<user_Model>();

                if (string.IsNullOrEmpty(connectionString))
                    return new List<user_Model>();

                // 验证所有用户ID都是有效的正数
                var validUserIds = userIds.Where(id => id > 0).Distinct().ToList();
                if (!validUserIds.Any())
                    return new List<user_Model>();

                // ✅ 使用DapperHelper进行安全的批量查询，只查询必要字段提升性能
                string sql = @"SELECT userid, nickname, siteid, endTime, SessionTimeout,
                               LastLoginTime, headimg, idname, managerlvl
                               FROM [user] WHERE userid IN @UserIds";

                var result = DapperHelper.Query<user_Model>(connectionString, sql, new { UserIds = validUserIds });

                System.Diagnostics.Debug.WriteLine($"UserService.GetUserListByIds: 查询{validUserIds.Count}个用户，返回{result?.Count() ?? 0}个结果");

                return result?.ToList() ?? new List<user_Model>();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"UserService.GetUserListByIds失败: userIds.Count={userIds?.Count ?? 0}, error={ex.Message}");
                return new List<user_Model>();
            }
        }

        /// <summary>
        /// 安全获取用户信息（使用现有BLL方法确保功能完整性）
        /// 适用于需要完整用户信息（包含isonline、idname等字段）的场景
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="siteId">站点ID</param>
        /// <param name="instanceName">实例名称</param>
        /// <returns>用户信息模型</returns>
        public static user_Model GetUserInfoWithBLL(string userId, string siteId, string instanceName)
        {
            try
            {
                // 输入验证
                if (string.IsNullOrEmpty(userId) || !WapTool.IsNumeric(userId))
                    return null;

                if (string.IsNullOrEmpty(siteId) || !WapTool.IsNumeric(siteId))
                    return null;

                if (string.IsNullOrEmpty(instanceName))
                    return null;

                // 使用现有BLL方法获取完整用户信息（包含isonline、idname等字段）
                var userBll = new user_BLL(instanceName);
                var userInfo = userBll.getUserInfo(userId, siteId);

                // 验证返回结果的站点ID匹配（防止跨站点数据访问）
                if (userInfo != null && userInfo.siteid.ToString() != siteId)
                {
                    System.Diagnostics.Debug.WriteLine($"UserService: BLL查询站点ID不匹配，用户ID={userId}，期望站点={siteId}，实际站点={userInfo.siteid}");
                    return null;
                }

                return userInfo;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"UserService.GetUserInfoWithBLL失败: userId={userId}, siteId={siteId}, error={ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 快速获取用户昵称映射（仅查询昵称，性能优化）
        /// </summary>
        /// <param name="userIds">用户ID列表</param>
        /// <param name="connectionString">数据库连接字符串</param>
        /// <returns>用户ID到昵称的映射字典</returns>
        public static Dictionary<long, string> GetUserNicknameMap(List<long> userIds, string connectionString)
        {
            try
            {
                if (userIds == null || !userIds.Any() || string.IsNullOrEmpty(connectionString))
                    return new Dictionary<long, string>();

                // 过滤有效的用户ID
                var validUserIds = userIds.Where(id => id > 0).Distinct().ToList();
                if (!validUserIds.Any())
                    return new Dictionary<long, string>();

                // ✅ 只查询必要的字段，提升性能
                string sql = "SELECT userid, nickname FROM [user] WHERE userid IN @UserIds";

                var result = DapperHelper.Query<dynamic>(connectionString, sql, new { UserIds = validUserIds });

                var nicknameMap = new Dictionary<long, string>();
                foreach (var item in result)
                {
                    long userId = Convert.ToInt64(item.userid);
                    string nickname = item.nickname?.ToString() ?? userId.ToString();
                    nicknameMap[userId] = nickname;
                }

                return nicknameMap;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"UserService.GetUserNicknameMap失败: userIds.Count={userIds?.Count ?? 0}, error={ex.Message}");
                return new Dictionary<long, string>();
            }
        }

        /// <summary>
        /// 获取服务统计信息（用于监控和调试）
        /// </summary>
        /// <returns>统计信息字符串</returns>
        public static string GetServiceStats()
        {
            try
            {
                return $"UserService统计信息 - 服务正常运行，最后更新时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
            }
            catch (Exception ex)
            {
                return $"UserService统计信息获取失败: {ex.Message}";
            }
        }
    }
}
