﻿<%@ Page Language="C#" AutoEventWireup="true" CodeBehind="Add.aspx.cs" Inherits="YaoHuo.Plugin.Games.ChuiNiu.Add" %>
<%@ Import Namespace="YaoHuo.Plugin.Tool" %>
<%
    System.Text.StringBuilder strhtml = new System.Text.StringBuilder();
string[] arryQuestion = { 
    "我是不是最帅的？",
    "今晚吃什么好？",
    "AI会统治世界吗？",
    "明天是什么天气？",
    "你幸福吗？",
    "我是吹牛大神！",
    "爱生活还是爱妖火？"
};
string[] arryAnswer1 = { 
    "那必须是",
    "火锅",
    "当然会",
    "晴天",
    "幸福",
    "不是",
    "爱生活"
};
string[] arryAnswer2 = { 
    "你想多了",
    "随便",
    "不可能",
    "雨天",
    "姓曾",
    "当然",
    "爱妖火"
};
if (action == "")
{
    Random rnd1 = new Random();
    int showid = rnd1.Next(0, arryQuestion.Length);
    question = arryQuestion[showid];
    answer1 = arryAnswer1[showid];
    answer2 = arryAnswer2[showid];
}
    // 添加必要的CSS和JS
    string headHtml = "<meta charset=\"UTF-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no\">\n<link rel=\"stylesheet\" href=\"/games/chuiniu/tailwind.min.css?3\"/> <link rel=\"stylesheet\" href=\"/games/chuiniu/styles.css?25\"/>\n<link rel=\"stylesheet\" href=\"//lf6-cdn-tos.bytecdntp.com/cdn/expire-1-y/font-awesome/6.0.0/css/all.min.css\"/>";
    Response.Write(WapTool.showTop(this.GetLang("发起挑战- 吹牛|公开挑战|content add"), wmlVo, false, headHtml));
    
    // 添加新的头部导航
    strhtml.Append("<header class=\"bg-gradient-to-r from-teal-500 to-teal-700 shadow-md p-4 flex items-center sticky top-0 z-10 text-white\">");
    strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/index.aspx\" class=\"text-white mr-4\">");
    strhtml.Append("<i class=\"fas fa-arrow-left\"></i>");
    strhtml.Append("</a>");
    strhtml.Append("<h1 class=\"text-xl font-bold\">发起挑战</h1>");
    strhtml.Append("</header>");
    
    // 主要内容区域
    strhtml.Append("<main class=\"p-4 pb-20 max-w-lg mx-auto\">");
    
    // 显示错误信息
    if (!string.IsNullOrEmpty(this.ERROR))
    {
        strhtml.Append("<div class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg relative mb-4\" role=\"alert\">");
        strhtml.Append(this.ERROR);
        strhtml.Append("</div>");
    }
    
    // 根据INFO显示不同的提示信息
    if (this.INFO == "NULL")
    {
        strhtml.Append("<div class=\"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg relative mb-4\" role=\"alert\">");
        strhtml.Append("<span class=\"font-medium\">提示：</span>所有项不能为空！");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "WAITING")
    {
        strhtml.Append("<div class=\"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg relative mb-4\" role=\"alert\">");
        strhtml.Append("<span class=\"font-medium\">提示：</span>今天您发布挑战已超过" + this.cou + "个了，请明天再来！");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "NUMBER")
    {
        strhtml.Append("<div class=\"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg relative mb-4\" role=\"alert\">");
        strhtml.Append("<span class=\"font-medium\">提示：</span>只能录入数值！最小赌注为" + min + "妖晶，最大赌注为" + max + "妖晶");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "NOMONEY")
    {
        strhtml.Append("<div class=\"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded-lg relative mb-4\" role=\"alert\">");
        strhtml.Append("<span class=\"font-medium\">提示：</span>抱歉，你的" + siteVo.sitemoneyname + "不够了！");
        strhtml.Append("</div>");
    }
    else if (this.INFO == "OK")
    {
        // 美化后的成功状态提示 - 放大卡片和图标
        strhtml.Append("<div class=\"bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-8 mx-auto\">");
        // 顶部状态横幅 - 模仿挑战进行中的样式 - 放大打钩图标
        strhtml.Append("<div class=\"bg-gradient-to-r from-teal-500 to-teal-600 p-8 text-white text-center\">");
        strhtml.Append("<div class=\"flex items-center justify-center\">");
        strhtml.Append("<div class=\"bg-teal-400/30 rounded-full w-24 h-24 flex items-center justify-center mb-5\">");
        strhtml.Append("<i class=\"fas fa-check text-white text-4xl\"></i>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("<span class=\"font-bold text-2xl\">挑战创建成功</span>");
        strhtml.Append("</div>");
        
        // 成功信息主体内容
        strhtml.Append("<div class=\"p-8\">");
        strhtml.Append("<div class=\"detail-item\">");
        strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/book_list.aspx?type=0&touserid=" + this.userid + "\" class=\"inline-block px-0 py-1 pl-0.5 rounded-full text-teal-700 hover:text-teal-500 text-sm font-medium mb-2 transition-all\">查看挑战</a>");
        strhtml.Append("<div class=\"bg-gray-50 p-4 rounded-lg\">");
        strhtml.Append("<p class=\"text-gray-800 font-medium\">您已成功创建挑战，请等待其他友友应战！</p>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        // 添加一个查看挑战的按钮 - 改为蓝色背景
        strhtml.Append("<div class=\"mt-6\">");
        strhtml.Append("<a href=\"" + this.http_start + "games/chuiniu/add.aspx\" class=\"w-full bg-gradient-to-r from-teal-500 to-teal-700 hover:from-teal-600 hover:to-teal-800 text-white font-bold py-4 px-4 rounded-lg shadow-md transition-all flex items-center justify-center\">");
        strhtml.Append("<i class=\"fas fa-plus-circle mr-2 text-white\"></i>");
        strhtml.Append("继续发起挑战");
        strhtml.Append("</a>");
        strhtml.Append("</div>");
        
        strhtml.Append("</div>");
        strhtml.Append("</div>");
    }
    
    // 如果不是OK状态，显示表单
    if (this.INFO != "OK")
    {
        strhtml.Append("<form action=\"" + http_start + "games/chuiniu/add.aspx\" method=\"post\" class=\"mb-6\">");
        strhtml.Append("<div class=\"bg-white rounded-xl shadow-sm p-6 border border-gray-100\">");
        // 赌注金额
        strhtml.Append("<div class=\"input-group\">");
        strhtml.Append("<label for=\"mymoney\" class=\"input-label flex justify-between items-center\">");
        strhtml.Append("<span>赌注金额</span>");
        strhtml.Append("<a href=\"javascript:void(0)\" onclick=\"randomBet();\" class=\"bg-gray-100 hover:bg-gray-200 text-gray-700 py-1 px-3 rounded-full text-xs font-medium transition-all flex items-center\">");
        strhtml.Append("<i class=\"fas fa-random mr-1\"></i>随机");
        strhtml.Append("</a>");
        strhtml.Append("</label>");
        strhtml.Append("<input type=\"number\" id=\"mymoney\" name=\"mymoney\" class=\"form-input\" placeholder=\"输入赌注\" min=\"" + min + "\" max=\"" + max + "\" value=\"" + min + "\" required>");
        strhtml.Append("</div>");
        
        // 挑战问题
        strhtml.Append("<div class=\"input-group\">");
        strhtml.Append("<label for=\"question\" class=\"input-label flex justify-between\">");
        strhtml.Append("<span>挑战问题</span>");
        strhtml.Append("<span class=\"text-sm text-gray-500 font-normal\">最多<span id=\"questionLength\">0</span>/20字</span>");
        strhtml.Append("</label>");
        strhtml.Append("<input type=\"text\" id=\"question\" name=\"question\" class=\"form-input\" placeholder=\"例如：我是不是最帅的？\" maxlength=\"20\" value=\"" + question + "\" required>");
        strhtml.Append("<div class=\"text-right text-xs text-gray-500 mt-1\">");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        // 答案一和答案二（并排显示）
        strhtml.Append("<div class=\"grid grid-cols-2 gap-4\">");
        // 答案一（左侧）
        strhtml.Append("<div class=\"input-group\">");
        strhtml.Append("<label for=\"answer1\" class=\"input-label\">");
        strhtml.Append("<span>答案一</span>");
        strhtml.Append("</label>");
        strhtml.Append("<input type=\"text\" id=\"answer1\" name=\"answer1\" class=\"form-input\" placeholder=\"例如：那必须是\" maxlength=\"10\" value=\"" + answer1 + "\" required>");
        strhtml.Append("</div>");
        
        // 答案二（右侧）
        strhtml.Append("<div class=\"input-group\">");
        strhtml.Append("<label for=\"answer2\" class=\"input-label\">");
        strhtml.Append("<span>答案二</span>");
        strhtml.Append("</label>");
        strhtml.Append("<input type=\"text\" id=\"answer2\" name=\"answer2\" class=\"form-input\" placeholder=\"例如：你想多了\" maxlength=\"10\" value=\"" + answer2 + "\" required>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        // 正确答案选择
        strhtml.Append("<div class=\"input-group\">");
        strhtml.Append("<label for=\"myanswer\" class=\"input-label flex justify-between items-center\">");
        strhtml.Append("<span>设置答案</span>");
        strhtml.Append("<a href=\"javascript:void(0)\" onclick=\"randomAnswer();\" class=\"bg-gray-100 hover:bg-gray-200 text-gray-700 py-1 px-3 rounded-full text-xs font-medium transition-all flex items-center\">");
        strhtml.Append("<i class=\"fas fa-random mr-1\"></i>随机");
        strhtml.Append("</a>");
        strhtml.Append("</label>");
        strhtml.Append("<div class=\"relative\">");
        strhtml.Append("<select id=\"myanswer\" name=\"myanswer\" class=\"form-input bg-white appearance-none pr-8\" required>");
        strhtml.Append("<option value=\"\" disabled selected>请选择正确答案</option>");
        strhtml.Append("<option value=\"1\">答案一</option>");
        strhtml.Append("<option value=\"2\">答案二</option>");
        strhtml.Append("</select>");
        strhtml.Append("<div class=\"pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700\">");
        strhtml.Append("<i class=\"fas fa-chevron-down text-xs\"></i>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        
        // 隐藏字段
        strhtml.Append("<input type=\"hidden\" name=\"action\" value=\"gomod\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"classid\" value=\"" + classid + "\"/>");
        strhtml.Append("<input type=\"hidden\" name=\"siteid\" value=\"" + siteid + "\"/>");
        
        // 提交按钮
        strhtml.Append("<button type=\"submit\" class=\"w-full py-4 px-4 rounded-lg gradient-btn font-bold shadow-sm text-white transition-all flex items-center justify-center mt-6\">");
        strhtml.Append("<i class=\"fas fa-paper-plane mr-2\"></i>确认发起");
        strhtml.Append("</button>");
        
        strhtml.Append("</div>");
        strhtml.Append("</form>");
        // 合并后的余额+规则说明卡片
        strhtml.Append("<div class=\"bg-white rounded-xl shadow-sm p-6 border border-gray-100 mt-6\">");
        strhtml.Append("<div class=\"flex items-center justify-between p-3 bg-amber-50 rounded-lg mb-4\">");
        strhtml.Append("<div class=\"text-sm text-gray-700\">您的余额</div>");
        strhtml.Append("<div class=\"font-bold text-lg text-amber-600 flex items-center\">");
        strhtml.Append(Convert.ToInt64(userVo.money).ToString("N0"));
        strhtml.Append("<span class=\"coin-icon ml-1\"><i class=\"fas fa-coins text-xs\"></i></span>");
        strhtml.Append("</div>");
        strhtml.Append("</div>");
        strhtml.Append("<div class=\"flex items-center text-gray-800 font-medium mb-3\">");
        strhtml.Append("<i class=\"fas fa-info-circle text-teal-500 mr-2\"></i>");
        strhtml.Append("规则说明");
        strhtml.Append("</div>");
        strhtml.Append("<ul class=\"text-sm text-gray-600 space-y-2\">");
        strhtml.Append("<li class=\"flex items-start\">");
        strhtml.Append("<i class=\"fas fa-circle text-teal-500 text-xs mt-1 mr-2\"></i>");
        strhtml.Append("<span>发起挑战将预先扣除赌注金额。</span>");
        strhtml.Append("</li>");
        strhtml.Append("<li class=\"flex items-start\">");
        strhtml.Append("<i class=\"fas fa-circle text-teal-500 text-xs mt-1 mr-2\"></i>");
        strhtml.Append("<span>若对方获胜，您的赌注将归对方。</span>");
        strhtml.Append("</li>");
        strhtml.Append("<li class=\"flex items-start\">");
        strhtml.Append("<i class=\"fas fa-circle text-teal-500 text-xs mt-1 mr-2\"></i>");
        strhtml.Append("<span>若您获胜，将赢取对方 <span class=\"font-semibold text-teal-600\">" + per + "%</span> 的赌注 。</span>");
        strhtml.Append("</li>");
        strhtml.Append("</ul>");
        strhtml.Append("</div>");
    }
    
    strhtml.Append("</main>");
    
    // 添加main.js引用和随机按钮的JavaScript功能
    strhtml.Append("<script src='main.js'></script>");
    strhtml.Append("<script type=\"text/javascript\">");
    strhtml.Append("function randomAnswer() {");
    strhtml.Append("  var randomValue = Math.floor(Math.random() * 2) + 1;");
    strhtml.Append("  document.getElementById('myanswer').value = randomValue;");
    strhtml.Append("}");
    strhtml.Append("function randomBet() {");
    strhtml.Append("  var userBalance = " + Convert.ToInt64(userVo.money) + ";"); 
    strhtml.Append("  var minBet = " + min + ";");
    strhtml.Append("  var maxBet = Math.min(10000, userBalance);");
    strhtml.Append("  if (maxBet < minBet) maxBet = minBet;");
    strhtml.Append("  var minMultiple = Math.ceil(minBet / 100);");
    strhtml.Append("  var maxMultiple = Math.floor(maxBet / 100);");
    strhtml.Append("  var randomMultiple = Math.floor(Math.random() * (maxMultiple - minMultiple + 1)) + minMultiple;");
    strhtml.Append("  var randomValue = randomMultiple * 100;");
    strhtml.Append("  document.getElementById('mymoney').value = randomValue;");
    strhtml.Append("}");
    strhtml.Append("</script>");
    
    Response.Write(strhtml.ToString());
    Response.Write(WapTool.showDown(wmlVo));
%>