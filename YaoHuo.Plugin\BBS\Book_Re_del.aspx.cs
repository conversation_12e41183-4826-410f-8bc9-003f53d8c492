﻿using System;
using System.Linq;
using System.Text;
using KeLin.ClassManager;
using KeLin.ClassManager.BLL;
using KeLin.ClassManager.Model;
using YaoHuo.Plugin.Tool;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public class Book_re_del : MyPageWap
    {
        private readonly string string_10 = PubConstant.GetAppString("InstanceName");
        public string formToken = "";
        public bool hasPermission = false;

        public string action = "";

        public string id = "";

        public string reid = "";

        public string page = "";

        public string lpage = "";

        public string ot = "";

        public string INFO = "";

        public string ERROR = "";

        public string sub = "";

        public wap_bbsre_Model bbsReVo = null;

        protected void Page_Load(object sender, EventArgs e)
        {
            action = GetRequestValue("action");
            id = GetRequestValue("id");
            reid = GetRequestValue("reid");
            page = GetRequestValue("page");
            lpage = GetRequestValue("lpage");
            ot = GetRequestValue("ot");
            sub = GetRequestValue("sub");

            if (classid != "0" && classVo.typePath.ToLower() != "bbs/index.aspx")
            {
                ShowTipInfo("抱歉，当前访问的栏目ID非论坛模块。", "");
                return;
            }

            IsLogin(userid, GetUrlQueryString());

            try
            {
                if (string.IsNullOrEmpty(id) || string.IsNullOrEmpty(reid))
                {
                    ShowTipInfo("参数无效！", "");
                    return;
                }

                wap_bbs_BLL wap_bbs_BLL = new wap_bbs_BLL(string_10);
                wap_bbs_Model model = wap_bbs_BLL.GetModel(long.Parse(id));
                if (model == null)
                {
                    ShowTipInfo("不存在主题帖！", "");
                    return;
                }

                wap_bbsre_BLL wap_bbsre_BLL = new wap_bbsre_BLL(string_10);
                bbsReVo = wap_bbsre_BLL.GetModel(long.Parse(reid));
                if (bbsReVo == null)
                {
                    ShowTipInfo("不存在此回复！", "bbs/book_re.aspx?action=class&siteid=" + siteid + "&classid=" + classid + "&lpage=" + lpage + "&page=" + page + "&ot=" + ot + "&id=" + id);
                    return;
                }

                if ((model.reShow > 0L || model.freeMoney > 0L) && !IsCheckManagerLvl("|00|01|", ""))
                {
                    ShowTipInfo("抱歉，每日签到送币帖、派币帖的回复只能由站长权限删除。", "bbs/book_re.aspx?action=class&siteid=" + siteid + "&classid=" + classid + "&lpage=" + lpage + "&page=" + page + "&ot=" + ot + "&id=" + id);
                    return;
                }

                if (userid == bbsReVo.userid.ToString())
                {
                    hasPermission = true;
                }
                else if (IsCheckManagerLvl("|00|01|03|04|", classVo.adminusername))
                {
                    hasPermission = true;
                }

                if (!hasPermission)
                {
                    ShowTipInfo("您没有权限删除此回复！", "bbs/book_re.aspx?action=class&siteid=" + siteid + "&classid=" + classid + "&lpage=" + lpage + "&page=" + page + "&ot=" + ot + "&id=" + id);
                    return;
                }

                if (!CheckManagerLvl(userVo.managerlvl, classVo.adminusername) &&
                    WapTool.GetSiteDefault(siteVo.Version, 26) == "1")
                {
                    ShowTipInfo("站长已关闭此功能！", "bbs/book_re.aspx?action=class&siteid=" + siteid + "&classid=" + classid + "&lpage=" + lpage + "&page=" + page + "&ot=" + ot + "&id=" + id);
                    return;
                }

                if (action == "godel")
                {
                    string token = Request["token"];
                    string tokenKey = "formTokenList_re_del_" + id + "_" + reid;
                    if (!ValidateFormToken(tokenKey, token))
                    {
                        ShowTipInfo("安全验证失败，请刷新页面重试", "bbs/book_re.aspx?action=class&siteid=" + siteid + "&classid=" + classid + "&lpage=" + lpage + "&page=" + page + "&ot=" + ot + "&id=" + id);
                        return;
                    }

                    long requiredMoney = WapTool.GetMoneyRegular(siteVo.moneyregular, 1) * 2L;
                    string moneyName = WapTool.GetSiteMoneyName(siteVo.sitemoneyname, this.lang);

                    if (userVo.money < 100 || userVo.money < requiredMoney)
                    {
                        ShowTipInfo($"删除失败，您的{moneyName}不足！删除回帖需要扣除{requiredMoney}{moneyName}，且保留最少100{moneyName}。当前余额:{userVo.money}",
                            "bbs/book_re.aspx?action=class&amp;siteid=" + siteid + "&amp;classid=" + classid +
                            "&amp;lpage=" + lpage + "&amp;page=" + page + "&amp;ot=" + ot + "&amp;id=" + id);
                        return;
                    }

                    WapTool.ClearDataBBSRe("bbsRe" + siteid + id);

                    // ✅ 修复SQL注入漏洞：使用DapperHelper替换BLL调用
                    string connectionString = PubConstant.GetConnectionString(string_10);
                    string attachmentSql = "SELECT * FROM wap2_attachment WHERE siteid = @SiteId AND book_type = @BookType AND book_id = @BookId";
                    var list2 = DapperHelper.Query<wap2_attachment_Model>(connectionString, attachmentSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        BookType = "bbsre",
                        BookId = DapperHelper.SafeParseLong(reid, "回帖ID")
                    })?.ToList();

                    StringBuilder stringBuilder = new StringBuilder();
                    int num = 0;
                    while (list2 != null && num < list2.Count)
                    {
                        stringBuilder.Append(list2[num].book_file + "|");
                        num++;
                    }
                    DeleteFile("bbs", stringBuilder.ToString(), GetUrlQueryString().Replace("godel", "go"));

                    // 删除附件
                    string deleteAttachmentSql = "DELETE FROM wap2_attachment WHERE siteid = @SiteId AND book_type = @BookType AND book_id = @BookId";
                    DapperHelper.Execute(connectionString, deleteAttachmentSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        BookType = "bbsre",
                        BookId = DapperHelper.SafeParseLong(reid, "回帖ID")
                    });

                    // 更新回帖状态（2=用户删除，3=管理员删除）
                    int deleteStatus = (userid == bbsReVo.userid.ToString()) ? 2 : 3;
                    string updateReplySql = "UPDATE wap_bbsre SET isCheck = @DeleteStatus WHERE id = @ReplyId";
                    DapperHelper.Execute(connectionString, updateReplySql, new {
                        DeleteStatus = deleteStatus,
                        ReplyId = DapperHelper.SafeParseLong(reid, "回帖ID")
                    });

                    // 更新帖子回复数
                    string updatePostSql = "UPDATE wap_bbs SET book_re = (SELECT COUNT(*) FROM wap_bbsre WHERE bookid = @PostId AND isCheck = 0) WHERE id = @PostId";
                    DapperHelper.Execute(connectionString, updatePostSql, new {
                        PostId = DapperHelper.SafeParseLong(id, "帖子ID")
                    });

                    long num2 = WapTool.GetMoneyRegular(siteVo.moneyregular, 1);
                    long num3 = WapTool.GetLvLRegular(siteVo.lvlRegular, 1);
                    if (userid == bbsReVo.userid.ToString())
                    {
                        num2 *= 2L;
                        num3 *= 2L;
                    }
                    else if (sub == "2")
                    {
                        num2 *= 2L;
                        num3 *= 2L;
                    }
                    else if (sub == "0")
                    {
                        num2 = 0L;
                        num3 = 0L;
                    }
                    // ✅ 使用DapperHelper进行安全的参数化更新用户金币和经验
                    string updateUserSql = "UPDATE [user] SET money = money - @DeductMoney, expR = expR - @DeductExp WHERE siteid = @SiteId AND userid = @UserId";
                    DapperHelper.Execute(connectionString, updateUserSql, new {
                        DeductMoney = num2,
                        DeductExp = num3,
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = bbsReVo.userid
                    });

                    // ✅ 先获取回帖作者当前余额，避免SaveBankLog中的SELECT操作导致死锁
                    string getAuthorMoneySql = "SELECT money FROM [user] WHERE userid = @UserId AND siteid = @SiteId";
                    long authorCurrentMoney = DapperHelper.ExecuteScalar<long>(connectionString, getAuthorMoneySql, new {
                        UserId = bbsReVo.userid,
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID")
                    });
                    long authorNewBalance = authorCurrentMoney; // 用户余额已在上面更新，这里记录更新后的余额

                    // ✅ 使用SaveBankLogWithBalance替换SaveBankLog，避免死锁
                    SaveBankLogWithBalance(bbsReVo.userid.ToString(), "删除回帖", "-" + num2, siteid, "系统", "回帖ID" + bbsReVo.id, authorNewBalance);

                    // ✅ 使用DapperHelper进行安全的参数化插入消息
                    string messageTitle = "您的一条回复删除，扣除" + num2 + "妖晶、" + num3 + "经验";
                    string messageText = userid == bbsReVo.userid.ToString() ? "您删除了自己的回帖" : "";
                    string messageContent = messageText + "[br]删除时间：" + DateTime.Now + "[br]论坛主题：[url=/bbs-" + id + ".html]点此查看[/url]";

                    string insertMessageSql = @"INSERT INTO wap_message(siteid,userid,nickname,title,content,touserid,issystem)
                                              VALUES (@SiteId, @UserId, @Nickname, @Title, @Content, @ToUserId, 1)";
                    DapperHelper.Execute(connectionString, insertMessageSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
                        Nickname = DapperHelper.LimitLength(nickname, 50),
                        Title = DapperHelper.LimitLength(messageTitle, 100),
                        Content = DapperHelper.LimitLength(messageContent, 500),
                        ToUserId = bbsReVo.userid
                    });

                    // ✅ 使用DapperHelper进行安全的参数化插入日志
                    string insertLogSql = @"INSERT INTO wap_log(siteid,oper_userid,oper_nickname,oper_type,log_info,oper_ip)
                                          VALUES (@SiteId, @OperUserId, @OperNickname, 0, @LogInfo, @OperIp)";
                    DapperHelper.Execute(connectionString, insertLogSql, new {
                        SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
                        OperUserId = DapperHelper.SafeParseLong(userid, "操作用户ID"),
                        OperNickname = DapperHelper.LimitLength(nickname, 50),
                        LogInfo = DapperHelper.LimitLength("删除用户" + bbsReVo.userid + "发表的ID=" + reid + "回复:" + bbsReVo.content, 500),
                        OperIp = DapperHelper.LimitLength(IP, 50)
                    });
                    INFO = "OK";

                    Session["formToken"] = null;
                    ClearFormToken(tokenKey);
                }
                else
                {
                    string tokenKey = "formTokenList_re_del_" + id + "_" + reid;
                    formToken = GenerateFormToken(tokenKey);
                }
            }
            catch (Exception ex)
            {
                ERROR = ex.ToString();
            }
        }
    }
}