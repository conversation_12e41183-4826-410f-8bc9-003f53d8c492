using System;
using System.Web;
using System.Net;
using System.Text;
using System.IO;
using KeLin.ClassManager;
using System.Security.Cryptography;
using Newtonsoft.Json.Linq;
using System.Web.SessionState;

public class GoCaptchaProxy : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IRequiresSessionState
{
    public bool IsReusable => false;

    public void ProcessRequest(HttpContext context)
    {
        // 从 Web.config 读取配置
        string goCaptchaServiceUrl = PubConstant.GetAppString("GoCaptchaServiceUrl");
        string goCaptchaApiKey = PubConstant.GetAppString("GoCaptchaApiKey");
        string goCaptchaEnabled = PubConstant.GetAppString("GoCaptchaEnabled");

        if (goCaptchaEnabled != "1" || string.IsNullOrEmpty(goCaptchaServiceUrl))
        {
            context.Response.StatusCode = (int)HttpStatusCode.ServiceUnavailable;
            context.Response.ContentType = "application/json";
            context.Response.Write("{\"error\":\"GoCaptcha service not enabled or misconfigured.\"}");
            return;
        }

        // 确定请求路径
        string requestPath = context.Request.QueryString["path"];
        string targetUrl = "";
        
        // 根据请求类型确定目标URL
        if (requestPath == "get-data" && context.Request.HttpMethod == "GET")
        {
            targetUrl = goCaptchaServiceUrl.TrimEnd('/') + "/api/v1/public/get-data";
        }
        else if (requestPath == "check-data" && context.Request.HttpMethod == "POST")
        {
            targetUrl = goCaptchaServiceUrl.TrimEnd('/') + "/api/v1/public/check-data";
        }
        else
        {
            context.Response.StatusCode = (int)HttpStatusCode.BadRequest;
            context.Response.ContentType = "application/json";
            context.Response.Write("{\"error\":\"Invalid request method or path.\"}");
            return;
        }

        // 将前端请求中的其他查询字符串参数附加到目标URL
        StringBuilder queryString = new StringBuilder();
        foreach (string key in context.Request.QueryString.AllKeys)
        {
            if (!string.IsNullOrEmpty(key) && key.ToLower() != "path")
            {
                string[] values = context.Request.QueryString.GetValues(key);
                if (values != null)
                {
                    foreach (string value in values)
                    {
                        if (queryString.Length > 0) queryString.Append("&");
                        queryString.Append(Uri.EscapeDataString(key));
                        queryString.Append("=");
                        queryString.Append(Uri.EscapeDataString(value));
                    }
                }
            }
        }

        if (queryString.Length > 0)
        {
            targetUrl += "?" + queryString.ToString();
        }

        try
        {
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
            
            // 创建请求
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(targetUrl);
            request.Method = context.Request.HttpMethod;
            
            // 设置API密钥头
            if (!string.IsNullOrEmpty(goCaptchaApiKey))
            {
                request.Headers.Add("X-API-Key", goCaptchaApiKey);
            }

            // 转发内容类型和数据
            if (context.Request.HttpMethod == "POST")
            {
                request.ContentType = context.Request.ContentType;
                using (Stream requestStream = request.GetRequestStream())
                {
                    context.Request.InputStream.Position = 0;
                    context.Request.InputStream.CopyTo(requestStream);
                }
            }

            // 获取响应
            using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
            {
                string responseContent;
                using (StreamReader reader = new StreamReader(response.GetResponseStream()))
                {
                    responseContent = reader.ReadToEnd();
                }

                if (string.IsNullOrEmpty(responseContent))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.BadGateway;
                    context.Response.ContentType = "application/json";
                    context.Response.Write("{\"error\":\"Empty response from GoCaptcha backend service\"}");
                    return;
                }

                // 处理验证结果，如果是验证成功的响应，生成安全令牌
                if (requestPath == "check-data" && context.Request.HttpMethod == "POST")
                {
                    try
                    {
                        var jsonResponse = JObject.Parse(responseContent);
                        var code = jsonResponse["code"]?.Value<int>();
                        var data = jsonResponse["data"]?.Value<string>();
                        
                        // 检查是否验证成功
                        if (code == 200 && data == "ok")
                        {
                            // 检查 Session 是否可用
                            if (context.Session != null)
                            {
                                // 生成安全的验证令牌
                                string verificationToken = GenerateSecureToken();
                                
                                // 将令牌存储在 Session 中，设置5分钟过期时间
                                context.Session[$"GoCaptcha_Token_{verificationToken}"] = DateTime.Now.AddMinutes(5);
                                
                                // 修改响应，添加令牌信息
                                jsonResponse["verificationToken"] = verificationToken;
                                responseContent = jsonResponse.ToString();
                            }
                            else
                            {
                                // Session 不可用时的回退处理
                                System.Diagnostics.Debug.WriteLine("GoCaptchaProxy: Session is not available, cannot generate secure token");
                                // 保持原有响应，但不添加验证令牌
                            }
                        }
                    }
                    catch (Exception)
                    {
                        // 如果解析失败，保持原响应不变
                    }
                }

                // 设置响应头和状态码，将后端响应转发给前端
                context.Response.Clear();
                context.Response.BufferOutput = true;
                context.Response.StatusCode = (int)response.StatusCode;
                context.Response.ContentType = response.ContentType;
                context.Response.AppendHeader("Access-Control-Allow-Origin", "*");
                context.Response.AppendHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
                context.Response.AppendHeader("Access-Control-Allow-Headers", "Content-Type, X-API-Key");

                byte[] responseBytes = Encoding.UTF8.GetBytes(responseContent);
                context.Response.AddHeader("Content-Length", responseBytes.Length.ToString());
                context.Response.BinaryWrite(responseBytes);

                context.Response.Flush();
                HttpContext.Current.ApplicationInstance.CompleteRequest();
            }
        }
        catch (WebException ex)
        {
            if (ex.Response != null)
            {
                using (StreamReader reader = new StreamReader(ex.Response.GetResponseStream()))
                {
                    string errorContent = reader.ReadToEnd();
                    context.Response.StatusCode = (int)((HttpWebResponse)ex.Response).StatusCode;
                    context.Response.ContentType = "application/json";
                    context.Response.Write(errorContent);
                    return;
                }
            }
            
            context.Response.StatusCode = (int)HttpStatusCode.BadGateway;
            context.Response.ContentType = "application/json";
            context.Response.Write("{\"error\":\"Failed to connect to GoCaptcha backend service or received no response: " + ex.Message + "\"}");
        }
        catch (System.Threading.ThreadAbortException)
        {
            // 正常的线程中止，忽略
        }
        catch (Exception ex)
        {
            context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
            context.Response.ContentType = "application/json";
            context.Response.Write("{\"error\":\"An error occurred while processing your request: " + ex.Message + "\"}");
        }
    }

    /// <summary>
    /// 生成密码学安全的随机令牌
    /// </summary>
    /// <returns>Base64 编码的安全令牌</returns>
    private string GenerateSecureToken()
    {
        using (var rng = new RNGCryptoServiceProvider())
        {
            byte[] tokenBytes = new byte[32]; // 256位随机数
            rng.GetBytes(tokenBytes);
            return Convert.ToBase64String(tokenBytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
        }
    }

    /// <summary>
    /// 验证并消费验证令牌（静态方法，供其他地方调用）
    /// </summary>
    /// <param name="token">要验证的令牌</param>
    /// <param name="session">HttpSessionState 实例</param>
    /// <returns>令牌是否有效</returns>
    public static bool ValidateAndConsumeToken(string token, System.Web.SessionState.HttpSessionState session)
    {
        if (string.IsNullOrEmpty(token) || session == null)
        {
            return false;
        }

        string sessionKey = $"GoCaptcha_Token_{token}";
        object storedExpiry = session[sessionKey];
        
        if (storedExpiry == null)
        {
            return false; // 令牌不存在
        }

        DateTime expiry = (DateTime)storedExpiry;
        if (DateTime.Now > expiry)
        {
            // 令牌已过期，清理并返回false
            session.Remove(sessionKey);
            return false;
        }

        // 令牌有效，消费它（移除以防重复使用）
        session.Remove(sessionKey);
        return true;
    }
}