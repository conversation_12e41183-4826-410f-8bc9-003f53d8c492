﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Transactions;
using Newtonsoft.Json;
using KeLin.ClassManager;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.WebSite
{
    public class GuessData
    {
        public long Id { get; set; }
        public long BbsId { get; set; }
        public string Title { get; set; }
        public List<GuessingOption> Options { get; set; }
        public DateTime Deadline { get; set; }
        public bool IsClosed { get; set; }
        public int? ResultOptionId { get; set; }
        public int? WinningOptionId { get; set; }
        public string WinningOptionText { get; set; }

    }

    public class GuessingOption
    {
        public string Text { get; set; }
        public int Amount { get; set; }
        public bool IsWinner { get; set; }
    }

    public class BetInfo
    {
        public long UserId { get; set; }
        public int OptionId { get; set; }
        public decimal Amount { get; set; }
    }

    public class GuessManager
    {
        private string connString;

        public GuessManager(string instanceName)
        {
            connString = PubConstant.GetConnectionString(instanceName);
        }

        public long AddGuessing(long bbsId, string title, string options, DateTime deadline)
        {
            try
            {
                string sql = @"
                    INSERT INTO bbs_guessing (bbs_id, title, options, deadline, is_closed, created_at)
                    VALUES (@BbsId, @Title, @Options, @Deadline, 0, @CreatedAt);
                    SELECT SCOPE_IDENTITY();";

                var parameters = new
                {
                    BbsId = bbsId,
                    Title = DapperHelper.LimitLength(title, 100),
                    Options = DapperHelper.LimitLength(options, 4000),
                    Deadline = deadline,
                    CreatedAt = DateTime.Now
                };

                return DapperHelper.ExecuteScalar<long>(connString, sql, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"AddGuessing error: {ex.Message}", ex);
            }
        }

        public GuessData GetGuessingById(long guessId)
        {
            try
            {
                string sql = @"
                    SELECT id, bbs_id, title, options, deadline, is_closed, result_option_id
                    FROM bbs_guessing
                    WHERE id = @GuessId";

                var parameters = new
                {
                    GuessId = guessId
                };

                var result = DapperHelper.Query<dynamic>(connString, sql, parameters).FirstOrDefault();

                if (result != null)
                {
                    var options = JsonConvert.DeserializeObject<List<GuessingOption>>(result.options);
                    int? resultOptionId = result.result_option_id;

                    var guessData = new GuessData
                    {
                        Id = result.id,
                        BbsId = result.bbs_id,
                        Title = result.title,
                        Options = options,
                        Deadline = result.deadline,
                        IsClosed = result.is_closed,
                        ResultOptionId = resultOptionId
                    };

                    // 设置新的属性
                    if (resultOptionId.HasValue && options != null && options.Count >= resultOptionId.Value)
                    {
                        guessData.WinningOptionId = resultOptionId.Value;
                        guessData.WinningOptionText = options[resultOptionId.Value - 1].Text;
                    }
                    else
                    {
                        guessData.WinningOptionId = null;
                        guessData.WinningOptionText = null;
                    }

                    return guessData;
                }
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"GetGuessingById error: {ex.Message}", ex);
            }
        }

        public bool HasUserBet(long guessId, long userId)
        {
            try
            {
                string sql = @"
                    SELECT COUNT(*)
                    FROM bbs_guessing_bets
                    WHERE guessing_id = @GuessId AND userid = @UserId";

                var parameters = new
                {
                    GuessId = guessId,
                    UserId = userId
                };

                int count = DapperHelper.ExecuteScalar<int>(connString, sql, parameters);
                return count > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"HasUserBet error: {ex.Message}", ex);
            }
        }

        public bool UpdateGuessingVote(long guessId, string selectedOption, int betAmount, long userId)
        {
            try
            {
                using (var scope = new TransactionScope())
                {
                    // 1. 检查竞猜状态
                    var (isClosed, isDeadlinePassed) = CheckGuessingStatus(guessId);

                    if (isClosed)
                    {
                        throw new Exception("竞猜已结束，无法继续下注");
                    }

                    if (isDeadlinePassed)
                    {
                        throw new Exception("截止时间已到，无法继续下注");
                    }

                    // 2. 获取当前选项数据
                    string selectSql = @"SELECT options FROM bbs_guessing WHERE id = @GuessId";
                    var selectParams = new { GuessId = guessId };
                    string optionsJson = DapperHelper.ExecuteScalar<string>(connString, selectSql, selectParams);

                    List<GuessingOption> options = JsonConvert.DeserializeObject<List<GuessingOption>>(optionsJson);
                    int selectedOptionIndex = options.FindIndex(o => o.Text == selectedOption);

                    if (selectedOptionIndex == -1)
                    {
                        throw new Exception("选项不存在");
                    }

                    // 3. 更新选项金额
                    options[selectedOptionIndex].Amount += betAmount;
                    string updatedOptionsJson = JsonConvert.SerializeObject(options);

                    // 4. 更新竞猜选项数据
                    string updateSql = @"UPDATE bbs_guessing SET options = @Options WHERE id = @GuessId";
                    var updateParams = new
                    {
                        Options = DapperHelper.LimitLength(updatedOptionsJson, 4000),
                        GuessId = guessId
                    };
                    DapperHelper.Execute(connString, updateSql, updateParams);

                    // 5. 插入下注记录
                    string insertBetSql = @"INSERT INTO bbs_guessing_bets (guessing_id, userid, option_id, amount)
                                           VALUES (@GuessingId, @UserId, @OptionId, @Amount)";
                    var insertParams = new
                    {
                        GuessingId = guessId,
                        UserId = userId,
                        OptionId = selectedOptionIndex + 1, // 将索引转换为从1开始的ID
                        Amount = betAmount
                    };
                    DapperHelper.Execute(connString, insertBetSql, insertParams);

                    scope.Complete();
                    return true;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"UpdateGuessingVote error: {ex.Message}", ex);
            }
        }

        public List<BetInfo> GetBetsForGuessing(long guessingId)
        {
            string sql = @"
                SELECT userid, option_id, amount
                FROM bbs_guessing_bets
                WHERE guessing_id = @GuessingId";

            var parameters = new
            {
                GuessingId = guessingId
            };

            var results = DapperHelper.Query<dynamic>(connString, sql, parameters);

            return results.Select(r => new BetInfo
            {
                UserId = r.userid,
                OptionId = r.option_id,
                Amount = r.amount
            }).ToList();
        }

        public void UpdateGuessingResult(long guessingId, int winningOptionId)
        {
            string sql = @"UPDATE [bbs_guessing]
                           SET [result_option_id] = @WinningOptionId,
                               [is_closed] = 1,
                               [updated_at] = GETDATE()
                           WHERE [id] = @GuessingId";

            var parameters = new
            {
                WinningOptionId = winningOptionId,
                GuessingId = guessingId
            };

            DapperHelper.Execute(connString, sql, parameters);
        }

        public void UpdateUserMoney(long userId, int amount, string siteId)
        {
            string sql = @"
                UPDATE [user]
                SET [money] = [money] + @Amount
                WHERE userid = @UserId AND siteid = @SiteId";

            var parameters = new
            {
                Amount = amount,
                UserId = userId,
                SiteId = DapperHelper.LimitLength(siteId, 50)
            };

            DapperHelper.Execute(connString, sql, parameters);
        }

        public long GetAuthorIdByBbsId(long bbsId)
        {
            string sql = "SELECT book_pub FROM wap_bbs WHERE id = @BbsId";

            var parameters = new
            {
                BbsId = bbsId
            };

            return DapperHelper.ExecuteScalar<long>(connString, sql, parameters);
        }

        public void SendSystemMessage(string siteId, long userId, string title, string content)
        {
            string sql = @"
                INSERT INTO wap_message (siteid, userid, nickname, title, content, touserid, issystem)
                VALUES (@SiteId, @SiteId, '系统消息', @Title, @Content, @UserId, 1)";

            var parameters = new
            {
                SiteId = DapperHelper.LimitLength(siteId, 50),
                Title = DapperHelper.LimitLength(title, 200),
                Content = DapperHelper.LimitLength(content, 4000),
                UserId = userId
            };

            DapperHelper.Execute(connString, sql, parameters);
        }

        public GuessData GetGuessingByBbsId(long guessId)
        {
            try
            {
                string sql = @"
                    SELECT id, bbs_id, title, options, deadline, is_closed, result_option_id
                    FROM bbs_guessing
                    WHERE bbs_id = @BbsId";

                var parameters = new
                {
                    BbsId = guessId
                };

                var result = DapperHelper.Query<dynamic>(connString, sql, parameters).FirstOrDefault();

                if (result != null)
                {
                    var options = JsonConvert.DeserializeObject<List<GuessingOption>>(result.options);
                    int? resultOptionId = result.result_option_id;

                    var guessData = new GuessData
                    {
                        Id = result.id,
                        BbsId = guessId,
                        Title = result.title,
                        Options = options,
                        Deadline = result.deadline,
                        IsClosed = result.is_closed,
                        ResultOptionId = resultOptionId
                    };

                    // 设置新的属性
                    if (resultOptionId.HasValue && options != null && options.Count >= resultOptionId.Value)
                    {
                        guessData.WinningOptionId = resultOptionId.Value;
                        guessData.WinningOptionText = options[resultOptionId.Value - 1].Text;
                    }
                    else
                    {
                        guessData.WinningOptionId = null;
                        guessData.WinningOptionText = null;
                    }

                    return guessData;
                }
                return null;
            }
            catch (Exception ex)
            {
                throw new Exception($"GetGuessingByBbsId error: {ex.Message}", ex);
            }
        }

        // 添加新方法
        public BetInfo GetUserBet(long guessId, long userId)
        {
            string sql = @"
                SELECT option_id, amount
                FROM bbs_guessing_bets
                WHERE guessing_id = @GuessId AND userid = @UserId";

            var parameters = new
            {
                GuessId = guessId,
                UserId = userId
            };

            var result = DapperHelper.Query<dynamic>(connString, sql, parameters).FirstOrDefault();

            if (result != null)
            {
                return new BetInfo
                {
                    UserId = userId,
                    OptionId = result.option_id,
                    Amount = result.amount
                };
            }
            return null;
        }

        public void UpdateGuessingResultWithTransaction(long guessingId, int winningOptionId, Action<long, int, string, string, long, long, string> saveBankLogAction, string siteId, string adminUserId, string adminNickname)
        {
            using (var scope = new TransactionScope())
            {
                try
                {
                    // 1. 更新竞猜结果
                    UpdateGuessingResult(guessingId, winningOptionId);

                    // 2. 获取所有下注信息
                    List<BetInfo> bets = GetBetsForGuessing(guessingId);

                    // 3. 计算总下注金额和获胜选项的下注金额
                    decimal totalBetAmount = bets.Sum(b => b.Amount);
                    decimal winningBetAmount = bets.Where(b => b.OptionId == winningOptionId).Sum(b => b.Amount);

                    // 4. 获取竞猜数据以获取获胜选项文本
                    GuessData guessData = GetGuessingById(guessingId);
                    string winningOptionText = guessData.Options[winningOptionId - 1].Text;

                    foreach (var bet in bets.Where(b => b.OptionId == winningOptionId))
                    {
                        // 使用精确的decimal计算，避免精度损失
                        decimal rewardAmount = CalculateReward(bet.Amount, winningBetAmount, totalBetAmount);

                        // 计算税后奖励金额（使用银行家舍入）
                        decimal taxAmount = Math.Round(rewardAmount * 0.1m, 2, MidpointRounding.ToEven);
                        decimal afterTaxReward = rewardAmount - taxAmount;

                        // 转换为整数（妖晶不支持小数）
                        int afterTaxRewardAmount = (int)Math.Floor(afterTaxReward);

                        // 5. 更新用户金币
                        UpdateUserMoney(bet.UserId, afterTaxRewardAmount, siteId);

                        // 6. 记录银行日志
                        saveBankLogAction(bet.UserId, afterTaxRewardAmount, adminUserId, adminNickname, guessData.BbsId, guessingId, winningOptionText);

                        // 7. 发送消息通知
                        string messageTitle = "恭喜您在竞猜中获胜！";
                        string messageContent = $"您在帖子[<a href=\"/bbs-{guessData.BbsId}.html\">{guessData.BbsId}</a>]的竞猜中选择了正确选项[{winningOptionText}]，获得奖励{afterTaxRewardAmount}妖晶！";
                        SendSystemMessage(siteId, bet.UserId, messageTitle, messageContent);
                    }

                    // 提交事务
                    scope.Complete();
                }
                catch (Exception ex)
                {
                    // 记录错误日志
                    System.Diagnostics.Debug.WriteLine($"Error in UpdateGuessingResultWithTransaction: {ex.Message}");
                    // 事务会自动回滚
                    throw;
                }
            }
        }

        public (bool IsClosed, bool IsDeadlinePassed) CheckGuessingStatus(long guessId)
        {
            string sql = @"
                SELECT is_closed, deadline
                FROM bbs_guessing
                WHERE id = @GuessId";

            var parameters = new
            {
                GuessId = guessId
            };

            var result = DapperHelper.Query<dynamic>(connString, sql, parameters).FirstOrDefault();

            if (result != null)
            {
                bool isClosed = result.is_closed;
                DateTime deadline = result.deadline;
                bool isDeadlinePassed = DateTime.Now > deadline;

                return (isClosed, isDeadlinePassed);
            }

            throw new Exception("未找到指定的竞猜");
        }

        /// <summary>
        /// 精确计算奖励金额
        /// </summary>
        private decimal CalculateReward(decimal betAmount, decimal winningBetAmount, decimal totalBetAmount)
        {
            if (winningBetAmount <= 0)
                throw new InvalidOperationException("获胜下注金额必须大于0");

            if (totalBetAmount <= 0)
                throw new InvalidOperationException("总下注金额必须大于0");

            // 使用 decimal 进行精确计算
            decimal rewardRatio = betAmount / winningBetAmount;
            decimal grossReward = rewardRatio * totalBetAmount;

            // 使用银行家舍入，保留2位小数
            return Math.Round(grossReward, 2, MidpointRounding.ToEven);
        }
    }


}