document.addEventListener('DOMContentLoaded', function() {
    const textarea = document.querySelector('textarea[name="book_content"]');
    if (!textarea) return;

    // 设置初始最小高度
    textarea.style.minHeight = '25vh';
    
    let lastScrollHeight = textarea.scrollHeight;
    let lastRows = textarea.value.split('\n').length;
    let isComposing = false;

    // 自适应高度函数
    function autoResize(preserveFocus = false) {
        // 记录当前是否处于focus状态
        const wasFocused = document.activeElement === textarea;
        const position = textarea.selectionStart;
        const currentRows = textarea.value.split('\n').length;
        
        if (currentRows !== lastRows || 
            Math.abs(textarea.scrollHeight - lastScrollHeight) > 30) {
            
            const newHeight = textarea.scrollHeight + 10;
            textarea.style.height = newHeight + 'px';
            
            lastScrollHeight = textarea.scrollHeight;
            lastRows = currentRows;
        }
        
        // 只在需要时恢复光标位置
        if (wasFocused || preserveFocus) {
            textarea.setSelectionRange(position, position);
        }
    }
    
    // 绑定事件
    textarea.addEventListener('input', () => {
        if (!isComposing) autoResize(true);
    });

    // 监听组合输入事件
    textarea.addEventListener('compositionstart', () => {
        isComposing = true;
    });

    textarea.addEventListener('compositionend', () => {
        isComposing = false;
        autoResize(true);
    });
    
    // 页面加载时调整高度，但不保持focus
    autoResize(false);
    setTimeout(() => autoResize(false), 100);
});