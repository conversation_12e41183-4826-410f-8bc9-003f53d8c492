using System;
using System.Collections.Generic;

namespace YaoHuo.Plugin.WebSite.Services.Config.Models
{
    /// <summary>
    /// 派币帖验证码配置根模型
    /// </summary>
    public class FreeMoneyPostCaptchaConfig
    {
        /// <summary>
        /// 配置元数据
        /// </summary>
        public ConfigMetadata Config { get; set; } = new ConfigMetadata();

        /// <summary>
        /// 是否启用派币帖验证码功能
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 需要验证码的用户ID列表
        /// </summary>
        public List<long> UserIds { get; set; } = new List<long>();

        /// <summary>
        /// 验证码提供商 (gocaptcha, cloudflare, tencentcloud)
        /// </summary>
        public string CaptchaProvider { get; set; } = "gocaptcha";

        /// <summary>
        /// 触发验证码的最小派币金额
        /// </summary>
        public long MinFreeMoneyAmount { get; set; } = 1;

        /// <summary>
        /// 派币完成后是否跳过验证码
        /// </summary>
        public bool SkipCaptchaWhenCompleted { get; set; } = true;

        /// <summary>
        /// 免验证码的用户ID列表（白名单）
        /// </summary>
        public List<long> ExemptUserIds { get; set; } = new List<long>();

        /// <summary>
        /// 调试模式
        /// </summary>
        public bool DebugMode { get; set; } = false;

        /// <summary>
        /// 频率限制配置
        /// </summary>
        public RateLimitConfig RateLimit { get; set; } = new RateLimitConfig();

        /// <summary>
        /// 降级行为配置
        /// </summary>
        public FallbackBehaviorConfig FallbackBehavior { get; set; } = new FallbackBehaviorConfig();
    }

    /// <summary>
    /// 频率限制配置
    /// </summary>
    public class RateLimitConfig
    {
        /// <summary>
        /// 是否启用频率限制
        /// </summary>
        public bool Enabled { get; set; } = true;

        /// <summary>
        /// 每分钟最大尝试次数
        /// </summary>
        public int MaxAttemptsPerMinute { get; set; } = 5;

        /// <summary>
        /// 阻止时长（分钟）
        /// </summary>
        public int BlockDurationMinutes { get; set; } = 10;
    }

    /// <summary>
    /// 降级行为配置
    /// </summary>
    public class FallbackBehaviorConfig
    {
        /// <summary>
        /// 配置错误时的行为 (disable, allow, deny)
        /// </summary>
        public string OnConfigError { get; set; } = "disable";

        /// <summary>
        /// 验证码服务错误时的行为 (allow, deny)
        /// </summary>
        public string OnCaptchaServiceError { get; set; } = "allow";

        /// <summary>
        /// 是否记录错误日志
        /// </summary>
        public bool LogErrors { get; set; } = true;
    }
}
