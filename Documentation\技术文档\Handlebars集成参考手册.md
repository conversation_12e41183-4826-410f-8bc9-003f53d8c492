# Handlebars.NET 集成参考指南

## 1. 概述

本文档提供在 ASP.NET Web Forms 项目中集成 Handlebars.NET 的核心指南，实现新旧UI并存的渐进式现代化改造。

### 1.1 核心目标

* **渐进式改造**：新旧 UI 并存，逐步迁移
* **统一架构**：标准化的页面渲染模式
* **稳定性优先**：确保模板编译和渲染的一致性

## 2. TemplateService 核心架构

### 2.1 设计原则

**全局唯一的 IHandlebars 实例**：通过静态构造函数创建全局唯一的 Handlebars 环境，确保 Helper 注册和模板编译的一致性。

### 2.2 核心方法

```csharp
// 推荐的页面渲染方法
public static string RenderPageWithLayout(
    string pageTemplatePath,
    object pageModel,
    string pageTitle,
    HeaderOptionsModel headerOptions,
    string pageSpecificCss = null,
    string mainLayoutPath = "~/Template/Layouts/MainLayout.hbs")
```

### 2.3 关键特性

- **一次性初始化**：静态构造函数确保只执行一次
- **模板缓存**：提高重复渲染性能
- **统一渲染模式**：页面+布局的二次渲染

## 3. 页面适配标准模式

### 3.1 必需的using语句

```csharp
using YaoHuo.Plugin.WebSite.Tool;        // TemplateService
using YaoHuo.Plugin.BBS.Models;          // HeaderOptionsModel
```

### 3.2 标准的RenderWithHandlebars方法

```csharp
private void RenderWithHandlebars()
{
    try
    {
        // 构建页面数据模型
        var pageModel = BuildPageModel();

        // ✅ 推荐：直接调用，避免反射
        string finalHtml = TemplateService.RenderPageWithLayout(
            "~/Template/Pages/PageName.hbs",
            pageModel,
            "页面标题",
            new HeaderOptionsModel { ShowViewModeToggle = false }
        );

        // 输出渲染结果
        Response.Clear();
        Response.ContentType = "text/html; charset=utf-8";
        Response.Write(finalHtml);
        Response.End();
    }
    catch (System.Threading.ThreadAbortException)
    {
        throw; // Response.End()的正常行为
    }
    catch (Exception ex)
    {
        // 错误处理
        Response.Clear();
        Response.ContentType = "text/html; charset=utf-8";
        Response.Write($"<div style='color:red'>页面渲染时发生严重错误: {ex.Message}</div>");
        HttpContext.Current.ApplicationInstance.CompleteRequest();
    }
}
```

### 3.3 UI偏好检查模式

```csharp
private bool CheckAndHandleUIPreference()
{
    string uiPreference = Request.Cookies["ui_preference"]?.Value ?? "old";

    if (uiPreference == "new")
    {
        try
        {
            RenderWithHandlebars();
            return true;
        }
        catch (System.Threading.ThreadAbortException)
        {
            return true; // 成功渲染
        }
        catch (Exception ex)
        {
            ERROR = "新版模板加载失败: " + ex.Message;
            return false;
        }
    }
    return false;
}
```

## 4. 文件结构

### 4.1 目录结构

```
YaoHuo.Plugin/
├── Template/
│   ├── Layouts/              # 主布局模板
│   │   └── MainLayout.hbs
│   ├── Pages/                # 页面级模板
│   │   ├── MyFile.hbs
│   │   └── ...
│   ├── Partials/             # 可复用组件
│   │   └── Header.hbs
│   ├── Models/               # 数据模型
│   │   └── CommonModels.cs
│   └── CSS/                  # CSS输出目录
│       └── output.css
└── build-tools/              # 前端构建工具
    ├── package.json
    ├── tailwind.config.js
    └── style.css             # Tailwind输入文件
```

### 4.2 模板层次

- **MainLayout.hbs**：主布局框架
- **Pages/**：页面主体内容
- **Partials/**：可复用组件

## 5. Tailwind CSS 本地化

### 5.1 快速设置

1. **安装依赖**：
   ```bash
   npm install -D tailwindcss postcss autoprefixer
   npx tailwindcss init -p
   ```

2. **配置文件**：
   ```javascript
   // tailwind.config.js
   content: ["./**/*.{html,js,aspx,hbs}", "./Template/**/*.hbs"]
   ```

3. **构建脚本**：
   ```json
   // package.json
   "scripts": {
     "build:tailwind": "tailwindcss -i ./style.css -o ../Template/CSS/output.css --minify",
     "watch:tailwind": "tailwindcss -i ./style.css -o ../Template/CSS/output.css --watch"
   }
   ```

4. **输入文件** (`build-tools/style.css`)：
   ```css
   @tailwind base;
   @tailwind components;
   @tailwind utilities;
   ```

### 5.2 部署注意

- 只需部署 `Template/CSS/output.css`
- 不需要上传 `build-tools` 目录

## 6. Helper 系统

### 6.1 内置 Helper

- **eq**: 等值比较 `{{#if (eq Status 'Active')}}`
- **formatNumber**: 数字格式化 `{{formatNumber Experience}}`
- **hasPermission**: 权限检查 `{{#hasPermission UserPermission}}`

### 6.2 使用示例

```handlebars
{{#if (eq Status 'Active')}}
    <span class="status-active">激活</span>
{{else}}
    <span class="status-inactive">未激活</span>
{{/if}}

<span>经验值: {{formatNumber Experience}}</span>
```

## 7. 数据模型设计

### 7.1 使用公共模型

**重要**：使用 `CommonModels.cs` 中定义的公共模型类：
- `MessageModel` - 消息提示
- `SiteInfoModel` - 站点信息
- `PaginationModel` - 分页
- `OptionItem` - 选项项

### 7.2 基础模型抽象架构

项目采用基础模型抽象模式，所有页面模型都继承自公共基类：

```csharp
// 基础页面模型 - 包含所有页面的公共属性
public abstract class BasePageModel
{
    public string PageTitle { get; set; }
    public MessageModel Message { get; set; } = new MessageModel();
    public SiteInfoModel SiteInfo { get; set; } = new SiteInfoModel();
    public HiddenFieldsModel HiddenFields { get; set; } = new HiddenFieldsModel();
}

// 带分页的基础页面模型
public abstract class BasePageModelWithPagination : BasePageModel
{
    public PaginationModel Pagination { get; set; } = new PaginationModel();
}
```

### 7.3 页面模型继承模式

#### **简单页面模型**（继承 BasePageModel）：
```csharp
public class EditProfilePageModel : BasePageModel
{
    public EditProfilePageModel()
    {
        PageTitle = "编辑资料";
    }

    // 只需要定义页面特有的属性
    public EditProfileFormModel FormData { get; set; } = new EditProfileFormModel();
    public OptionListsModel OptionLists { get; set; } = new OptionListsModel();
}
```

#### **带分页的页面模型**（继承 BasePageModelWithPagination）：
```csharp
public class FriendListPageModel : BasePageModelWithPagination
{
    public FriendListPageModel()
    {
        PageTitle = "好友列表";
    }

    // 只需要定义页面特有的属性
    public List<FriendItemModel> FriendsList { get; set; } = new List<FriendItemModel>();
    public string FriendType { get; set; }
}
```

### 7.4 架构优势

- ✅ **减少重复代码**：公共属性统一管理，减少约200+行重复代码
- ✅ **提高开发效率**：新页面只需继承基类，减少60%代码量
- ✅ **增强维护性**：公共属性修改只需改一处
- ✅ **保持一致性**：统一的数据结构便于模板复用

### 7.5 已重构的页面模型

以下页面已成功重构为使用基础模型抽象：

| 页面模型 | 继承基类 | 减少代码行数 | 优化效果 |
|----------|----------|-------------|----------|
| `ModifyPasswordPageModel` | `BasePageModel` | 11行 (25%) | ✅ 完成 |
| `EditProfilePageModel` | `BasePageModel` | 17行 (9%) | ✅ 完成 |
| `FriendListPageModel` | `BasePageModelWithPagination` | 11行 (7%) | ✅ 完成 |
| `BankListPageModel` | `BasePageModelWithPagination` | 18行 (7%) | ✅ 完成 |
| `MyFilePageModel` | `BasePageModel` | 新增强类型模型 | ✅ 完成 |
| `RMBtoMoneyPageModel` | `BasePageModel` | 87行 (35%) | ✅ 完成 |

### 7.6 必需变量定义

每个页面类必须定义ERROR和INFO变量：

```csharp
public partial class PageName : MyPageWap
{
    public string ERROR = "";
    public string INFO = "";
    // ...
}
```

## 8. UI 切换机制

### 8.1 Cookie 控制

- `ui_preference = "new"`：使用 Handlebars 新版 UI
- `ui_preference = "old"` 或空：使用传统 Web Forms UI

### 8.2 Header 配置

```csharp
new HeaderOptionsModel { ShowViewModeToggle = false }  // 不显示切换按钮
new HeaderOptionsModel()                               // 默认显示切换按钮
```

## 9. 常见问题

### 9.1 ThreadAbortException 处理

```csharp
catch (System.Threading.ThreadAbortException)
{
    throw; // Response.End()的正常行为
}
```

### 9.2 内容拼接问题

使用 bool 返回值控制执行流程：

```csharp
bool newVersionRendered = CheckAndHandleUIPreference();
if (newVersionRendered)
{
    return; // 阻止旧版代码执行
}
```

**详细错误解决方案请参考**：[Handlebars常见问题解决方案](../开发指南/Handlebars常见问题解决方案.md)

## 10. 性能优化

### 10.1 模板缓存

TemplateService 自动缓存编译后的模板，提高重复渲染性能。

### 10.2 数据模型优化

- 避免在模板中进行复杂计算
- 预先在 C# 中计算好所有需要的数据
- 使用计算属性替代复杂的Helper嵌套

### 10.3 部分渲染

对于复杂页面，使用 Partial 拆分：

```handlebars
<div class="user-info">
    {{> UserInfoCard UserInfo=UserInfo}}
</div>
```

## 11. 调试与排查

### 11.1 Debug 日志

TemplateService 提供完整的日志输出，便于排查问题。

### 11.2 常见调试场景

1. **Helper 未注册**：检查静态构造函数日志
2. **模板未找到**：检查文件路径
3. **ThreadAbortException**：确认是成功标志而非错误
4. **数据绑定问题**：检查数据模型结构

### 11.3 错误恢复

```csharp
catch (Exception ex)
{
    System.Diagnostics.Debug.WriteLine($"模板渲染错误，回退到旧版");
    return false; // 回退到旧版
}
```

## 12. 最佳实践

### 12.1 命名规范

```
页面类名        →  模板文件名       →  数据模型名
MyFile.aspx.cs  →  MyFile.hbs       →  MyFilePageModel
EditProfile     →  EditProfile.hbs  →  EditProfilePageModel
```

### 12.2 HeaderOptionsModel 使用

```csharp
using YaoHuo.Plugin.BBS.Models;

new HeaderOptionsModel {
    ShowViewModeToggle = false,
    CustomButtonIcon = "settings"
}
```

### 12.3 模板验证

- 确保 Handlebars 语法正确
- 检查数据绑定属性存在
- 确认 Helper 已注册
- 验证 Partial 文件存在

## 13. 部署与扩展

### 13.1 部署注意事项

- 确保 `.hbs` 文件正确部署到 `Template` 目录
- 确保 CSS 文件可访问
- 确保 Web 应用有读取模板文件的权限

### 13.2 新页面适配流程

1. **创建页面数据模型** (`PageModel.cs`)
   - 继承 `BasePageModel` 或 `BasePageModelWithPagination`
   - 在构造函数中设置 `PageTitle`
   - 只定义页面特有的属性

2. **创建 Handlebars 模板** (`PageName.hbs`)
   - 使用统一的数据结构（Message, SiteInfo, HiddenFields等）
   - 利用基础模型提供的公共属性

3. **修改页面 `.aspx.cs`** 应用标准适配模式
   - 添加必要的 using 语句
   - 使用直接调用而非反射
   - 实现强类型的 `BuildPageModel()` 方法

4. **测试新版 UI 功能**
   - 验证数据绑定正确
   - 确认所有功能正常工作

## 14. 快速参考指南

### 14.1 新页面开发检查

开发新页面时，请确保：

1. **✅ 添加正确的using语句**
```csharp
using YaoHuo.Plugin.WebSite.Tool;        // TemplateService
using YaoHuo.Plugin.BBS.Models;          // HeaderOptionsModel
using YaoHuo.Plugin.Template.Models;     // BasePageModel
```

2. **✅ 使用基础模型抽象**
```csharp
// 简单页面
public class YourPageModel : BasePageModel
{
    public YourPageModel() { PageTitle = "页面标题"; }
    // 页面特有属性...
}

// 带分页的页面
public class YourListPageModel : BasePageModelWithPagination
{
    public YourListPageModel() { PageTitle = "列表页面"; }
    // 页面特有属性...
}
```

3. **✅ 使用直接调用而非反射**
```csharp
string finalHtml = TemplateService.RenderPageWithLayout(
    "~/Template/Pages/YourPage.hbs",
    pageModel,
    pageModel.PageTitle,  // 使用模型中的标题
    new HeaderOptionsModel { ShowViewModeToggle = false }
);
```

4. **✅ 标准错误处理**
```csharp
catch (System.Threading.ThreadAbortException) { throw; }
catch (Exception ex) { /* 错误处理 */ }
```

### 14.2 故障排除快速指南

| 错误类型 | 快速解决方案 |
|----------|-------------|
| `TargetParameterCountException` | 使用直接调用替代反射 |
| `FileNotFoundException` | 检查模板文件路径 |
| `CS0103: 当前上下文中不存在名称` | 添加对应的using语句 |
| `Helper cannot be resolved` | 检查Helper是否已注册 |
| `ThreadAbortException` | 正常现象，直接重新抛出 |

### 14.3 相关文档

- 📋 [Handlebars新页面开发清单](../开发指南/Handlebars新页面开发清单.md)
- 🔧 [Handlebars常见问题解决方案](../开发指南/Handlebars常见问题解决方案.md)
- 🏗️ [Handlebars模型重构记录](../历史参考/Handlebars模型重构记录.md)
- 📖 [JavaScript开发规范](../开发规范/JavaScript开发规范.md)

---

**注意**: 本文档已精简为核心集成指南。详细的错误解决方案和开发检查清单请参考上述相关文档。
