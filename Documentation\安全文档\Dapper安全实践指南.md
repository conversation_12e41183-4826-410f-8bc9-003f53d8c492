# Dapper安全修复实战指南

## 文档概述

**目标**：基于实际修复经验，提供完整的Dapper安全修复实战指南。

**修复成果**：
- 修复了多个文件中的SQL注入漏洞
- 代码简化79%，安全性显著提升
- 新增QueryBuilder和PaginationHelper工具

## 核心设计原则

### 统一封装原则
- 创建`DapperHelper.cs`作为唯一的数据访问入口
- 封装层提供类型安全和资源管理

### 真正参数化查询原则
- 在执行点同时传入完整SQL和参数对象
- **绝不**返回SQL字符串片段进行拼接

### 最小修改原则
- 保留所有现有SQL逻辑和业务规则
- 仅替换不安全的执行方式

## DapperHelper.cs 核心实现

### 基础安全方法
```csharp
/// <summary>
/// Dapper数据访问辅助类 - 统一封装，确保安全
/// </summary>
public static class DapperHelper
{
    /// <summary>
    /// 获取数据库连接（private防止外部误用）
    /// </summary>
    private static IDbConnection GetConnection(string connectionString)
    {
        return new SqlConnection(connectionString);
    }

    /// <summary>
    /// 执行SQL命令（INSERT、UPDATE、DELETE）
    /// </summary>
    public static int Execute(string connectionString, string sql, object parameters = null)
    {
        using (var connection = GetConnection(connectionString))
        {
            return connection.Execute(sql, parameters);
        }
    }

    /// <summary>
    /// 执行SQL查询并返回单个值
    /// </summary>
    public static T ExecuteScalar<T>(string connectionString, string sql, object parameters = null)
    {
        using (var connection = GetConnection(connectionString))
        {
            return connection.ExecuteScalar<T>(sql, parameters);
        }
    }

    /// <summary>
    /// 执行SQL查询并返回结果集
    /// </summary>
    public static IEnumerable<T> Query<T>(string connectionString, string sql, object parameters = null)
    {
        using (var connection = GetConnection(connectionString))
        {
            return connection.Query<T>(sql, parameters);
        }
    }

    /// <summary>
    /// 安全的长整型转换（包含验证）
    /// </summary>
    public static long SafeParseLong(string value, string fieldName)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException($"{fieldName}不能为空");

        if (!long.TryParse(value, out long result))
            throw new ArgumentException($"{fieldName}必须是有效的数字");

        return result;
    }

    /// <summary>
    /// 安全的整型转换（包含验证）
    /// </summary>
    public static int SafeParseInt(string value, string fieldName)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException($"{fieldName}不能为空");

        if (!int.TryParse(value, out int result))
            throw new ArgumentException($"{fieldName}必须是有效的数字");

        return result;
    }

    /// <summary>
    /// 字符串长度限制
    /// </summary>
    public static string LimitLength(string value, int maxLength)
    {
        if (string.IsNullOrEmpty(value))
            return "";

        return value.Length > maxLength ? value.Substring(0, maxLength) : value;
    }
}
```

### 新增：QueryBuilder 查询构建器
```csharp
/// <summary>
/// 查询构建器 - 安全的条件构建，支持链式调用
/// </summary>
public class QueryBuilder
{
    private readonly List<string> _whereConditions = new List<string>();
    private readonly Dictionary<string, object> _parameters = new Dictionary<string, object>();
    private int _parameterIndex = 1;

    /// <summary>
    /// 添加WHERE条件
    /// </summary>
    public QueryBuilder Where(string condition, object value)
    {
        string paramName = $"Param{_parameterIndex++}";
        _whereConditions.Add(condition.Replace("@ParamN", $"@{paramName}"));
        _parameters[paramName] = value;
        return this;
    }

    /// <summary>
    /// 条件性添加WHERE条件
    /// </summary>
    public QueryBuilder WhereIf(bool condition, string whereClause, object value)
    {
        if (condition)
        {
            Where(whereClause, value);
        }
        return this;
    }

    /// <summary>
    /// 构建最终的SQL和参数
    /// </summary>
    public (string Sql, object Parameters) Build(string baseQuery)
    {
        string sql = baseQuery;
        if (_whereConditions.Count > 0)
        {
            sql += " WHERE " + string.Join(" AND ", _whereConditions);
        }
        return (sql, _parameters);
    }
}
```

### 新增：PaginationHelper 分页助手
```csharp
/// <summary>
/// 分页查询助手 - 统一的分页逻辑
/// </summary>
public static class PaginationHelper
{
    /// <summary>
    /// 执行分页查询
    /// </summary>
    public static PagedResult<T> GetPagedData<T>(
        string connectionString,
        string baseQuery,
        object parameters,
        int page,
        int pageSize)
    {
        // 构建计数查询
        string countQuery = $"SELECT COUNT(*) FROM ({baseQuery}) AS CountQuery";

        // 构建分页查询
        string pagedQuery = $@"
            SELECT * FROM ({baseQuery}) AS PagedQuery
            ORDER BY (SELECT NULL)
            OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";

        using (var connection = GetConnection(connectionString))
        {
            // 获取总数
            int total = connection.ExecuteScalar<int>(countQuery, parameters);

            // 获取分页数据
            var pagedParameters = new DynamicParameters(parameters);
            pagedParameters.Add("Offset", (page - 1) * pageSize);
            pagedParameters.Add("PageSize", pageSize);

            var data = connection.Query<T>(pagedQuery, pagedParameters).ToList();

            return new PagedResult<T>(data, total, page, pageSize);
        }
    }

    /// <summary>
    /// 与QueryBuilder结合的分页查询
    /// </summary>
    public static PagedResult<T> GetPagedDataWithBuilder<T>(
        string connectionString,
        string selectClause,
        string fromClause,
        QueryBuilder queryBuilder,
        int page,
        int pageSize)
    {
        var (whereClause, parameters) = queryBuilder.Build("");
        string baseQuery = $"{selectClause} FROM {fromClause} {whereClause}";

        return GetPagedData<T>(connectionString, baseQuery, parameters, page, pageSize);
    }
}
```

## 常见错误模式与正确修复

### 错误模式1：字符串拼接SQL
```csharp
// 危险！直接拼接用户输入
MainBll.UpdateSQL("update [user] set nickname='" + nickname + "' where userid=" + userid);
```

### 错误模式2：返回SQL片段
```csharp
// 危险！返回SQL字符串片段
private string BuildSafeCondition(string userId)
{
    return $"userid={userId}"; // 仍然是字符串拼接！
}
string sql = "SELECT * FROM table WHERE " + BuildSafeCondition(userId);
```

### 错误模式3：假参数化
```csharp
// 危险！只是改变了拼接方式
condition = "siteid=@SiteId and userid=@UserId"; // 这只是字符串！
int count = SomeBLL.GetListCount(condition); // BLL内部可能仍然拼接
```

### 正确模式：真正的参数化查询
```csharp
// 安全！在执行点同时传入SQL和参数
private void UpdateNicknameSafely(string nickname, string siteId, string userId)
{
    string connectionString = PubConstant.GetConnectionString(a);
    string sql = "UPDATE [user] SET nickname = @Nickname WHERE siteid = @SiteId AND userid = @UserId";

    // 关键：在执行点同时传入完整SQL和参数对象
    DapperHelper.Execute(connectionString, sql, new { 
        Nickname = nickname, 
        SiteId = DapperHelper.SafeParseLong(siteId, "站点ID"), 
        UserId = DapperHelper.SafeParseLong(userId, "用户ID") 
    });
}
```

## 实际修复案例

### 案例1：简单UPDATE操作
**文件**：ModifyUserName.aspx.cs

**原始代码（危险）**：
```csharp
MainBll.UpdateSQL("update [user] set username='" + tousername + "' where siteid=" + siteid + " and userid=" + userid);
```

**修复后（安全）**：
```csharp
private void UpdateUsernameSafely(string username, string siteId, string userId)
{
    string connectionString = PubConstant.GetConnectionString(string_10);
    string sql = "UPDATE [user] SET username = @Username WHERE siteid = @SiteId AND userid = @UserId";

    DapperHelper.Execute(connectionString, sql, new { 
        Username = username, 
        SiteId = DapperHelper.SafeParseLong(siteId, "站点ID"), 
        UserId = DapperHelper.SafeParseLong(userId, "用户ID") 
    });
}
```

### 案例2：复杂查询替换
**文件**：Book_list_log.aspx.cs

**原始代码（危险）**：
```csharp
condition = "siteid=" + siteid + " and oper_type=1 and oper_userid=" + touserid;
total = wap_log_BLL.GetListCount(condition); // BLL内部进行字符串拼接
listVo = wap_log_BLL.GetListVo(pageSize, CurrentPage, condition, "*", "id", total, 1);
```

**修复后（安全）**：
```csharp
// 完全绕过不安全的BLL方法，直接使用DapperHelper
string countSql = "SELECT COUNT(*) FROM wap_log WHERE siteid=@SiteId AND oper_type=1 AND oper_userid=@ToUserId";
total = DapperHelper.ExecuteScalar<long>(connectionString, countSql, new {
    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
    ToUserId = DapperHelper.SafeParseLong(touserid, "用户ID")
});

string listSql = @"SELECT * FROM wap_log 
                   WHERE siteid=@SiteId AND oper_type=1 AND oper_userid=@ToUserId
                   ORDER BY id DESC
                   OFFSET @Offset ROWS FETCH NEXT @PageSize ROWS ONLY";
var logList = DapperHelper.Query<wap_log_Model>(connectionString, listSql, new {
    SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
    ToUserId = DapperHelper.SafeParseLong(touserid, "用户ID"),
    Offset = (CurrentPage - 1) * pageSize,
    PageSize = pageSize
});
listVo = logList?.ToList() ?? new List<wap_log_Model>();
```

### 案例3：复杂分页查询（使用新工具）
**文件**：Banklist.aspx.cs

**原始代码（危险且复杂）**：
```csharp
// 150+行的复杂条件构建和手动分页处理
private (string CountSql, string ListSql, dynamic Parameters) BuildSafeQueryParameters(...)
{
    var whereConditions = new List<string>();
    var parameters = new Dictionary<string, object>();

    // 100+行的条件构建代码...

    return (countSql, listSql, parameters);
}

// 手动分页处理
total = DapperHelper.ExecuteScalar<long>(connectionString, queryParams.CountSql, queryParams.Parameters);
CurrentPage = WapTool.CheckCurrpage(total, pageSize, CurrentPage);
// 更多分页代码...
```

**修复后（安全且简洁）**：
```csharp
// 31行简洁实现，代码减少79%
private QueryBuilder BuildSafeQueryBuilder(...)
{
    return new QueryBuilder()
        .Where("siteid = @ParamN", DapperHelper.SafeParseLong(siteId, "站点ID"))
        .WhereIf(isAdmin && !string.IsNullOrEmpty(key), "userid = @ParamN", userId)
        .WhereIf(typeid == "1", "actionname LIKE @ParamN", $"%{typekey}%");
}

// 一行完成分页查询
var result = PaginationHelper.GetPagedDataWithBuilder<wap_bankLog_Model>(
    connectionString, "SELECT *", "wap_bankLog", queryBuilder, currentPage, pageSize);

// 自动设置分页信息
listVo = result.Data;
total = result.Total;
CurrentPage = result.CurrentPage;
```

### 案例4：搜索功能优化
**文件**：Book_Re_my.aspx.cs

**原始代码（复杂的搜索逻辑）**：
```csharp
// 复杂的手动搜索条件构建
if (!string.IsNullOrEmpty(key))
{
    // 多关键词搜索逻辑
    string[] keywords = key.Split(' ');
    // 复杂的条件拼接...
}
// 手动分页处理...
```

**修复后（使用QueryBuilder）**：
```csharp
private QueryBuilder BuildSearchQueryBuilder(string searchKey, string touserid)
{
    var queryBuilder = new QueryBuilder()
        .Where("siteid = @ParamN", DapperHelper.SafeParseLong(siteid, "站点ID"))
        .WhereIf(!string.IsNullOrEmpty(touserid), "touserid = @ParamN",
                 DapperHelper.SafeParseLong(touserid, "目标用户ID"));

    // 多关键词搜索支持
    if (!string.IsNullOrEmpty(searchKey))
    {
        string[] keywords = searchKey.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
        foreach (string keyword in keywords)
        {
            queryBuilder.Where("content LIKE @ParamN", $"%{keyword.Trim()}%");
        }
    }

    return queryBuilder;
}

// 使用PaginationHelper执行分页查询
var result = PaginationHelper.GetPagedDataWithBuilder<wap_book_re_Model>(
    connectionString, "SELECT *", "wap_book_re", queryBuilder, currentPage, pageSize);
```

## 常见陷阱与避免方法

### 陷阱1：假参数化查询
**问题**：只是把SQL字符串改成包含@参数的形式，但仍然传给不支持参数化的方法。

**避免方法**：确保在执行点同时传入SQL和参数对象，绕过所有不安全的BLL方法。

### 陷阱2：遗漏的SQL注入点
**问题**：修复了主要查询，但遗漏了统计查询或其他辅助查询。

**避免方法**：系统性搜索所有SQL相关的方法调用：
```bash
grep -r "GetListCount.*\+" *.cs
grep -r "UpdateSQL.*\+" *.cs
grep -r "MainBll\." *.cs
```

### 陷阱3：编译错误处理 
**常见编译错误及解决方案**：

- **ToList()方法不可用**：添加 `using System.Linq;`
- **调用已删除的方法**：彻底清理所有对已删除方法的调用
- **类型引用问题**：添加 `using KeLin.ClassManager.Model;`

**精确的using语句管理** ：

**基本原则**：只添加实际使用的using语句，避免"预防性"添加

**常见需要的using**：
- `using System.Linq;` - 当使用ToList()、FirstOrDefault()等LINQ方法时
- `using YaoHuo.Plugin.WebSite.Tool;` - 当使用DapperHelper时
- `using KeLin.ClassManager.Model;` - 当直接引用Model类时（如user_Model）

**常见不需要的using**：
- `using Dapper;` - 我们使用DapperHelper封装，不直接使用Dapper
- `using System.Data;` - 如果只使用DapperHelper，不需要SqlDbType等
- `using System.Data.SqlClient;` - 如果不直接创建SqlConnection，不需要

**验证方法**：
1. 在VS中，灰色的using语句表示未使用，应该删除
2. 编译时如果缺少using，编译器会明确提示需要哪个命名空间
3. 使用VS的"删除未使用的using"功能（Ctrl+R, Ctrl+G）

**修复清单**：
1. **删除旧代码**：完全移除不安全的实现
2. **统一实现**：确保同一功能只有一种实现方式
3. **验证调用**：确保所有方法调用都指向新的安全方法

## 修复效果评估

### 安全性提升
- **零SQL注入风险** - 所有查询都是真正的参数化查询
- **类型安全** - 强类型参数映射，编译时检查
- **自动参数管理** - QueryBuilder自动处理参数命名，避免冲突

### 代码质量提升
- **大幅代码简化** - 复杂查询从150行减少到31行（减少79%）
- **统一模式** - 所有数据访问都使用相同的安全模式
- **链式调用** - QueryBuilder支持流畅的API设计

### 开发效率提升
- **一行分页** - PaginationHelper一行完成分页查询
- **条件构建** - QueryBuilder链式构建复杂条件
- **自动计算** - 分页信息自动计算，无需手动处理

## 修复验证清单

### 安全性验证 
- [ ] 所有SQL都使用参数化查询
- [ ] 没有任何SQL字符串拼接
- [ ] 删除了所有返回SQL片段的方法
- [ ] 绕过了不安全的BLL方法
- [ ] QueryBuilder自动管理参数命名，避免冲突

### 代码质量验证 
- [ ] 移除了冗余的ValidateNumeric调用
- [ ] 正确的using语句管理
- [ ] connectionString作用域正确
- [ ] 所有编译错误已解决
- [ ] 使用QueryBuilder简化条件构建
- [ ] 使用PaginationHelper统一分页逻辑

### 功能完整性验证 
- [ ] 保留了所有原有的业务逻辑
- [ ] 保留了所有原有的SQL逻辑
- [ ] 错误处理机制完整
- [ ] 参数验证充分
- [ ] 分页功能正常工作
- [ ] 搜索功能保持一致

### 新工具验证 
- [ ] QueryBuilder链式调用正常
- [ ] PaginationHelper分页计算正确
- [ ] PagedResult模型数据完整
- [ ] 与现有代码兼容性良好

## 相关阅读

- [Dapper安全修复实战指南](../安全文档/Dapper_Security_Practices.md)
- [DapperHelper & TransactionHelper 使用手册](../安全文档/Dapper_Transaction_Guide.md)
- [OWASP SQL Injection Cheat Sheet (中文速查版)](../安全文档/OWASP_SQL_Injection_Cheat_Sheet_CN.md)
- [QueryBuilder和PaginationHelper使用指南](../安全文档/QueryBuilder_PaginationHelper_Guide.md)
