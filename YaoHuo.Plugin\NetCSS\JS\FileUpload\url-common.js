// 折叠/展开功能
function toggleCollapse(header) {
    const section = header.closest('.file-upload-section');
    const content = section.querySelector('.form-content');
    const chevron = header.querySelector('svg');
    header.classList.toggle('active');
    
    if (content.style.display === 'none') {
        content.style.display = 'block';
        chevron.style.transform = 'rotate(0deg)';
        section.style.paddingBottom = '1rem';
    } else {
        content.style.display = 'none';
        chevron.style.transform = 'rotate(180deg)';
        section.style.paddingBottom = '0';
    }
}

// URL验证
function validateUrl(input) {
    const url = input.value.trim();
    if (url && !url.match(/^https?:\/\//i)) {
        document.getElementById('urlDialog').showModal();
        input.value = '';
        return false;
    }
    return true;
}

// 关闭对话框
function closeUrlDialog() {
    document.getElementById('urlDialog').close();
}

// 更新数量
function updateNum(delta) {
    var input = document.getElementById('numInput');
    var currentValue = parseInt(input.value);
    var newValue = currentValue + delta;
    
    if (newValue >= 1 && newValue <= 9) {
        input.value = newValue;
        input.setAttribute('data-current', newValue);
        updateFileUploadSections(newValue);
        updateButtonStates(newValue);
        
        // 更新隐藏的num输入
        var hiddenNumInput = document.querySelector('input[name="num"]');
        if (hiddenNumInput) {
            hiddenNumInput.value = newValue;
        }
    }
}

// 更新按钮状态
function updateButtonStates(value) {
    const increaseBtn = document.querySelector('.num-btn:first-of-type');
    const decreaseBtn = document.querySelector('.num-btn:last-of-type');
    
    decreaseBtn.disabled = false;
    increaseBtn.disabled = false;
    decreaseBtn.classList.remove('disabled');
    increaseBtn.classList.remove('disabled');
    
    if (value <= 1) {
        decreaseBtn.disabled = true;
        decreaseBtn.classList.add('disabled');
    }
    if (value >= 9) {
        increaseBtn.disabled = true;
        increaseBtn.classList.add('disabled');
    }
}

// 更新文件上传区域
function updateFileUploadSections(num) {
    var form = document.querySelector('form[name="f"]');
    if (!form) return;

    var existingSections = form.querySelectorAll('.file-upload-section');
    existingSections.forEach(function(section) {
        section.remove();
    });

    var submitBtn = form.querySelector('#submitBtn');
    
    for (var i = 0; i < num; i++) {
        var section = createFileUploadSection(i);
        if (submitBtn) {
            form.insertBefore(section, submitBtn);
        } else {
            form.appendChild(section);
        }
    }

    var hiddenNumInput = form.querySelector('input[name="num"]');
    if (hiddenNumInput) {
        hiddenNumInput.value = num;
    }
}

// 创建文件上传区域
function createFileUploadSection(index) {
    var section = document.createElement('div');
    section.className = 'file-upload-section';
    
    var html = '<div class="file-header-url" onclick="toggleCollapse(this)">' +
        '<div class="file-title-group">' +
        '<div class="file-number">' + (index + 1) + '</div>' +
        '<div class="file-title-url">资源文件<span style="padding-left: 2px;">' + (index + 1) + '</span></div>' +
        '</div>' +
        '<div class="collapse-btn">' +
        '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="chevron-up"><polyline points="18 15 12 9 6 15"></polyline></svg>' +
        '</div>' +
        '</div>' +
        '<div class="form-content">' +
        createFormFields() +
        '</div>';
    
    section.innerHTML = html;
    return section;
}

// 创建表单字段
function createFormFields() {
    return '<div class="form-group">' +
        '<label>资源名称</label>' +
        '<input type="text" maxlength="35" placeholder="必填项" required="required" name="file_title" class="form-control"/>' +
        '</div>' +
        '<div class="form-group">' +
        '<label>链接地址</label>' +
        '<input type="text" placeholder="http 或 https 开头的链接" required="required" name="file_url" class="form-control"/>' +
        '</div>' +
        '<div class="form-row">' +
        '<div class="form-group half">' +
        '<label>文件大小</label>' +
        '<input type="text" maxlength="7" placeholder="选填，例如: 8MB" name="file_size" class="form-control"/>' +
        '</div>' +
        '<div class="form-group half">' +
        '<label>文件后缀</label>' +
        '<input type="text" maxlength="5" placeholder="选填，例如: zip" name="file_ext" class="form-control"/>' +
        '</div>' +
        '</div>' +
        '<div class="form-group">' +
        '<label>附件说明</label>' +
        '<textarea name="file_info" oninput="adjustTextareaHeight(this)" placeholder="选填备注信息，例如网盘提取密码、文件解压密码" class="form-control" rows="2"></textarea>' +
        '</div>';
}

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 表单提交验证
    document.querySelector('form[name="f"]')?.addEventListener('submit', function(e) {
        const urlInputs = this.querySelectorAll('input[name="file_url"]');
        for (let input of urlInputs) {
            if (!validateUrl(input)) {
                e.preventDefault();
                break;
            }
        }
    });

    // 对话框点击事件
    const dialog = document.getElementById('urlDialog');
    if (dialog) {
        dialog.addEventListener('click', (e) => {
            const dialogDimensions = dialog.getBoundingClientRect();
            if (
                e.clientX < dialogDimensions.left ||
                e.clientX > dialogDimensions.right ||
                e.clientY < dialogDimensions.top ||
                e.clientY > dialogDimensions.bottom
            ) {
                dialog.close();
            }
        });
    }

    // 初始化按钮状态
    const initialValue = parseInt(document.getElementById('numInput')?.value || '1');
    updateButtonStates(initialValue);
});