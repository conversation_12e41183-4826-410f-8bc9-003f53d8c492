﻿using System;
using System.Collections.Generic;
using System.Web;
using System.Web.UI.WebControls;
using System.Linq;
using KeLin.ClassManager;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.BBS
{
    public partial class GuessVote : MyPageWap
    {
        protected Literal litGuessTitle;
        protected Literal litSelectedOption;
        protected long guessId;
        protected string selectedOption;
        protected GuessData guessingData;
        protected DropDownList ddlBetAmount;

        // 添加有效下注金额列表
        private static readonly int[] ValidBetAmounts = { 200, 300, 500, 1000, 2000, 3000, 5000, 8000, 10000, 20000, 30000, 50000, 80000, 100000 };

        protected void Page_Load(object sender, EventArgs e)
        {
            IsLogin(userid, "");

            System.Diagnostics.Debug.WriteLine($"Page_Load - Start");
            System.Diagnostics.Debug.WriteLine($"IsPostBack: {IsPostBack}");

            // 始终解析 guessId，不论是否回发
            if (!ParseGuessId())
            {
                ShowTipInfo("无效的竞猜ID", "");
                return;
            }

            // 获取竞猜数据
            guessingData = GetGuessingData(guessId);
            if (guessingData == null)
            {
                ShowTipInfo("未找到指定的竞猜", "");
                return;
            }

            // 使用新方法检查竞猜状态
            GuessManager guessManager = new GuessManager(PubConstant.GetAppString("InstanceName"));
            var (isClosed, isDeadlinePassed) = guessManager.CheckGuessingStatus(guessId);

            if (isClosed)
            {
                ShowTipInfo("该竞猜已结束，无法继续下注", $"bbs-{guessingData.BbsId}.html");
                return;
            }

            if (isDeadlinePassed)
            {
                ShowTipInfo("截止时间已到，无法继续下注", $"bbs-{guessingData.BbsId}.html");
                return;
            }

            if (!IsPostBack)
            {
                selectedOption = HttpUtility.UrlDecode(HttpContext.Current.Request.QueryString["option"]);

                if (guessId == 0 || string.IsNullOrEmpty(selectedOption))
                {
                    ShowTipInfo("无效的请求参数", "");
                    return;
                }

                litGuessTitle.Text = guessingData.Title;
                litSelectedOption.Text = selectedOption;

                // 注册有效的下注金额
                foreach (int amount in ValidBetAmounts)
                {
                    ddlBetAmount.Items.Add(new ListItem(amount.ToString(), amount.ToString()));
                }
            }

            System.Diagnostics.Debug.WriteLine($"Page_Load - End");
        }

        private bool ParseGuessId()
        {
            string idParam = HttpContext.Current.Request.QueryString["id"];
            if (!long.TryParse(idParam, out guessId))
            {
                guessId = 0;
                return false;
            }
            return true;
        }

        protected void btnConfirm_Click(object sender, EventArgs e)
        {
            if (!Page.IsValid)
            {
                return; // 如果验证失败，不继续处理
            }

            // 添加日志
            System.Diagnostics.Debug.WriteLine($"btnConfirm_Click - guessId: {guessId}");

            // 重新获取最新的竞猜数据
            guessingData = GetGuessingData(guessId);
            if (guessingData == null)
            {
                ShowTipInfo("竞猜数据不存在", "");
                return;
            }

            // 检查竞猜是否已关闭
            if (guessingData.IsClosed)
            {
                ShowTipInfo("该竞猜已结束，无法继续下注", $"bbs-{guessingData.BbsId}.html");
                return;
            }

            // 检查当前时间是否超过截止时间
            if (DateTime.Now > guessingData.Deadline)
            {
                ShowTipInfo("截止时间已到，无法继续下注", $"bbs-{guessingData.BbsId}.html");
                return;
            }

            if (string.IsNullOrEmpty(selectedOption))
            {
                selectedOption = HttpUtility.UrlDecode(HttpContext.Current.Request.QueryString["option"]);
            }

            // 确保选项被正确解码并验证
            selectedOption = HttpUtility.UrlDecode(selectedOption);

            if (!ValidateSelectedOption(selectedOption, guessingData))
            {
                ShowTipInfo("无效的选项", $"bbs-{guessingData.BbsId}.html");
                return;
            }

            int betAmount;
            if (ddlBetAmount == null || !int.TryParse(ddlBetAmount.SelectedValue, out betAmount) || !IsValidBetAmount(betAmount))
            {
                ShowTipInfo("无效的下注金额，请选择有效的金额。", $"GuessVote.aspx?id={guessId}&option={HttpUtility.UrlEncode(selectedOption)}");
                return;
            }

            long userIdLong;
            if (!long.TryParse(userid, out userIdLong))
            {
                ShowTipInfo("无效的用户ID", $"bbs-{guessingData.BbsId}.html");
                return;
            }

            // 使用事务性下注操作，解决竞态条件问题
            try
            {
                // 事务性下注，包含余额检查、扣款、记录下注
                bool success = PlaceBetWithTransaction(guessId, selectedOption, betAmount, userIdLong, siteid);

                if (success)
                {
                    // ✅ 先计算新余额，避免SaveBankLog中的SELECT操作导致死锁
                    long newBalance = userVo.money - betAmount;

                    // ✅ 使用SaveBankLogWithBalance替换SaveBankLog，避免死锁
                    SaveBankLogWithBalance(userIdLong.ToString(), "竞猜下注", $"-{betAmount}", userIdLong.ToString(), userVo.nickname, $"参与帖子ID[{guessingData.BbsId}]竞猜ID[{guessId}],选项[{selectedOption}]", newBalance);

                    // 更新用户对象中的金币数量
                    userVo.money -= betAmount;

                    ShowTipInfo("下注成功", $"bbs-{guessingData.BbsId}.html");
                }
                else
                {
                    ShowTipInfo("下注失败，请稍后重试", $"bbs-{guessingData.BbsId}.html");
                }
            }
            catch (InvalidOperationException ex)
            {
                // 业务逻辑异常（余额不足、已下注等）
                ShowTipInfo(ex.Message, $"bbs-{guessingData.BbsId}.html");
            }
            catch (Exception ex)
            {
                // 其他异常
                System.Diagnostics.Debug.WriteLine($"下注异常: {ex.Message}");
                ShowTipInfo("下注失败，请稍后重试", $"bbs-{guessingData.BbsId}.html");
            }
        }

        // 添加验证下注金额的方法
        private bool IsValidBetAmount(int amount)
        {
            return ValidBetAmounts.Contains(amount);
        }

        /// <summary>
        /// 验证选项的安全性和有效性
        /// </summary>
        private bool ValidateSelectedOption(string selectedOption, GuessData guessingData)
        {
            // 基础验证
            if (string.IsNullOrWhiteSpace(selectedOption))
                return false;

            // 长度验证
            if (selectedOption.Length > 50)
                return false;

            // XSS防护 - 检查是否包含危险字符
            var dangerousChars = new[] { '<', '>', '"', '\'', '&', ';', '(', ')', '{', '}' };
            if (selectedOption.Any(c => dangerousChars.Contains(c)))
                return false;

            // 验证选项是否存在于竞猜数据中
            if (guessingData?.Options == null)
                return false;

            return guessingData.Options.Any(o => o.Text == selectedOption);
        }

        /// <summary>
        /// 事务性下注操作，解决竞态条件问题
        /// </summary>
        private bool PlaceBetWithTransaction(long guessId, string selectedOption, int betAmount, long userId, string siteId)
        {
            using (var scope = new System.Transactions.TransactionScope(System.Transactions.TransactionScopeOption.Required,
                new System.Transactions.TransactionOptions { IsolationLevel = System.Transactions.IsolationLevel.ReadCommitted, Timeout = TimeSpan.FromSeconds(30) }))
            {
                try
                {
                    string connectionString = PubConstant.GetConnectionString(PubConstant.GetAppString("InstanceName"));

                    // 1. 锁定用户记录并检查余额
                    var currentBalance = GetUserBalanceWithLock(userId, siteId, connectionString);
                    if (currentBalance < betAmount)
                    {
                        throw new InvalidOperationException("余额不足，无法下注");
                    }

                    // 2. 检查竞猜状态
                    ValidateGuessingStatusInTransaction(guessId, connectionString);

                    // 3. 检查是否已下注
                    if (HasUserBetInTransaction(guessId, userId, connectionString))
                    {
                        throw new InvalidOperationException("您已经在这个竞猜中下过注了");
                    }

                    // 4. 验证选项有效性
                    int optionId = ValidateAndGetOptionIdInTransaction(guessId, selectedOption, connectionString);
                    if (optionId == -1)
                    {
                        throw new InvalidOperationException("选项不存在");
                    }

                    // 5. 扣除余额
                    UpdateUserMoneyInTransaction(userId, siteId, -betAmount, connectionString);

                    // 6. 记录下注
                    RecordBetInTransaction(guessId, optionId, betAmount, userId, connectionString);

                    scope.Complete();
                    return true;
                }
                catch (Exception)
                {
                    // 事务自动回滚
                    throw;
                }
            }
        }

        protected void btnCancel_Click(object sender, EventArgs e)
        {
            Response.Redirect($"/bbs-{guessingData.BbsId}.html");
        }

        private GuessData GetGuessingData(long guessId)
        {
            string instanceName = PubConstant.GetAppString("InstanceName");
            GuessManager guessManager = new GuessManager(instanceName);
            return guessManager.GetGuessingById(guessId);
        }

        private bool UpdateGuessingVote(long guessId, string selectedOption, int betAmount, long userId)
        {
            string instanceName = PubConstant.GetAppString("InstanceName");
            GuessManager guessManager = new GuessManager(instanceName);
            return guessManager.UpdateGuessingVote(guessId, selectedOption, betAmount, userId);
        }

        // 添加这个新方法来获取选项ID
        private int GetOptionId(long guessId, string selectedOption)
        {
            GuessManager guessManager = new GuessManager(PubConstant.GetAppString("InstanceName"));
            GuessData guessingData = guessManager.GetGuessingById(guessId);

            if (guessingData != null && guessingData.Options != null)
            {
                for (int i = 0; i < guessingData.Options.Count; i++)
                {
                    if (guessingData.Options[i].Text == selectedOption)
                    {
                        return i; // 修改这里，直接返回索引 i，而不是 i + 1
                    }
                }
            }

            return -1; // 如果没有找到匹配的选项，返回-1（而不是0）
        }

        // 添加这个新方法来检查用户是否已经下注
        private bool HasUserAlreadyBet(long guessId, long userId)
        {
            string instanceName = PubConstant.GetAppString("InstanceName");
            GuessManager guessManager = new GuessManager(instanceName);
            return guessManager.HasUserBet(guessId, userId);
        }

        protected void cvBetAmount_ServerValidate(object source, ServerValidateEventArgs args)
        {
            int betAmount;
            if (int.TryParse(args.Value, out betAmount))
            {
                args.IsValid = IsValidBetAmount(betAmount);
            }
            else
            {
                args.IsValid = false;
            }
        }

        #region 事务性操作辅助方法

        private decimal GetUserBalanceWithLock(long userId, string siteId, string connectionString)
        {
            string sql = @"SELECT money FROM [user] WITH (UPDLOCK, ROWLOCK)
                           WHERE userid = @UserId AND siteid = @SiteId";

            var parameters = new
            {
                UserId = userId,
                SiteId = DapperHelper.LimitLength(siteId, 50)
            };

            return DapperHelper.ExecuteScalar<decimal>(connectionString, sql, parameters);
        }

        private void ValidateGuessingStatusInTransaction(long guessId, string connectionString)
        {
            string sql = @"SELECT is_closed, deadline FROM bbs_guessing WHERE id = @GuessId";

            var parameters = new
            {
                GuessId = guessId
            };

            var result = DapperHelper.Query<dynamic>(connectionString, sql, parameters).FirstOrDefault();

            if (result != null)
            {
                bool isClosed = result.is_closed;
                DateTime deadline = result.deadline;

                if (isClosed)
                {
                    throw new InvalidOperationException("竞猜已结束，无法继续下注");
                }

                if (DateTime.Now > deadline)
                {
                    throw new InvalidOperationException("截止时间已到，无法继续下注");
                }
            }
            else
            {
                throw new InvalidOperationException("未找到指定的竞猜");
            }
        }

        private bool HasUserBetInTransaction(long guessId, long userId, string connectionString)
        {
            string sql = @"SELECT COUNT(*) FROM bbs_guessing_bets
                           WHERE guessing_id = @GuessId AND userid = @UserId";

            var parameters = new
            {
                GuessId = guessId,
                UserId = userId
            };

            int count = DapperHelper.ExecuteScalar<int>(connectionString, sql, parameters);
            return count > 0;
        }

        private int ValidateAndGetOptionIdInTransaction(long guessId, string selectedOption, string connectionString)
        {
            string sql = @"SELECT options FROM bbs_guessing WHERE id = @GuessId";

            var parameters = new
            {
                GuessId = guessId
            };

            string optionsJson = DapperHelper.ExecuteScalar<string>(connectionString, sql, parameters);
            if (string.IsNullOrEmpty(optionsJson))
                return -1;

            var options = Newtonsoft.Json.JsonConvert.DeserializeObject<List<GuessingOption>>(optionsJson);

            for (int i = 0; i < options.Count; i++)
            {
                if (options[i].Text == selectedOption)
                {
                    return i + 1; // 返回从1开始的ID
                }
            }

            return -1;
        }

        private void UpdateUserMoneyInTransaction(long userId, string siteId, decimal amount, string connectionString)
        {
            string sql = @"UPDATE [user]
                           SET [money] = [money] + @Amount
                           WHERE userid = @UserId AND siteid = @SiteId";

            var parameters = new
            {
                Amount = amount,
                UserId = userId,
                SiteId = DapperHelper.LimitLength(siteId, 50)
            };

            DapperHelper.Execute(connectionString, sql, parameters);
        }

        private void RecordBetInTransaction(long guessId, int optionId, int betAmount, long userId, string connectionString)
        {
            string sql = @"INSERT INTO bbs_guessing_bets (guessing_id, userid, option_id, amount)
                           VALUES (@GuessingId, @UserId, @OptionId, @Amount)";

            var parameters = new
            {
                GuessingId = guessId,
                UserId = userId,
                OptionId = optionId,
                Amount = betAmount
            };

            DapperHelper.Execute(connectionString, sql, parameters);
        }

        #endregion
    }
}