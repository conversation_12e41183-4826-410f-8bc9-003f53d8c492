using System;
using System.Collections.Generic;
using YaoHuo.Plugin.BBS.Models;

namespace YaoHuo.Plugin.Template.Models
{
    /// <summary>
    /// Book_Re_my 页面数据模型
    /// </summary>
    public class BookReMyPageModel : BasePageModelWithPagination
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public BookReMyPageModel()
        {
            PageTitle = "查看回复";
        }

        /// <summary>
        /// 头部选项
        /// </summary>
        public HeaderOptionsModel HeaderOptions { get; set; } = new HeaderOptionsModel();

        /// <summary>
        /// 搜索表单
        /// </summary>
        public BookReMySearchFormModel SearchForm { get; set; } = new BookReMySearchFormModel();

        /// <summary>
        /// 排序选项
        /// </summary>
        public BookReMySortModel Sort { get; set; } = new BookReMySortModel();

        /// <summary>
        /// 回复列表
        /// </summary>
        public List<ReplyItemModel> ReplyList { get; set; } = new List<ReplyItemModel>();

        /// <summary>
        /// 权限信息
        /// </summary>
        public BookReMyPermissionModel Permissions { get; set; } = new BookReMyPermissionModel();

        /// <summary>
        /// 管理员操作
        /// </summary>
        public BookReMyAdminActionsModel AdminActions { get; set; } = new BookReMyAdminActionsModel();
    }

    /// <summary>
    /// 搜索表单模型
    /// </summary>
    public class BookReMySearchFormModel
    {
        /// <summary>
        /// 是否可以搜索
        /// </summary>
        public bool CanSearch { get; set; }

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string SearchKey { get; set; }

        /// <summary>
        /// 是否强制使用CHARINDEX搜索
        /// </summary>
        public bool IsForceCharIndex { get; set; }

        /// <summary>
        /// 搜索表单提交URL
        /// </summary>
        public string SearchUrl { get; set; }

        /// <summary>
        /// 是否有搜索结果
        /// </summary>
        public bool HasSearchResult { get; set; }

        /// <summary>
        /// 搜索提示信息
        /// </summary>
        public string SearchHint { get; set; }
    }

    /// <summary>
    /// 排序模型
    /// </summary>
    public class BookReMySortModel
    {
        /// <summary>
        /// 当前排序方式 (0=最新, 1=最早)
        /// </summary>
        public string CurrentSort { get; set; } = "0";

        /// <summary>
        /// 是否按最新排序
        /// </summary>
        public bool IsNewest => CurrentSort == "0";

        /// <summary>
        /// 是否按最早排序
        /// </summary>
        public bool IsOldest => CurrentSort == "1";

        /// <summary>
        /// 按最新回复排序URL
        /// </summary>
        public string NewestUrl { get; set; }

        /// <summary>
        /// 按最早回复排序URL
        /// </summary>
        public string OldestUrl { get; set; }

        /// <summary>
        /// 是否显示排序选项
        /// </summary>
        public bool ShowSort { get; set; } = true;
    }

    /// <summary>
    /// 回复项模型
    /// </summary>
    public class ReplyItemModel
    {
        /// <summary>
        /// 回复ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 显示序号
        /// </summary>
        public long Index { get; set; }

        /// <summary>
        /// 用户ID
        /// </summary>
        public long UserId { get; set; }

        /// <summary>
        /// 用户昵称
        /// </summary>
        public string Nickname { get; set; }

        /// <summary>
        /// 回复内容（已处理过UBB标签和emoji）
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 原始回复内容（未处理的）
        /// </summary>
        public string RawContent { get; set; }

        /// <summary>
        /// 回复时间
        /// </summary>
        public DateTime ReplyDate { get; set; }

        /// <summary>
        /// 格式化的回复时间
        /// </summary>
        public string FormattedDate { get; set; }

        /// <summary>
        /// 帖子ID
        /// </summary>
        public long BookId { get; set; }

        /// <summary>
        /// 查看帖子URL
        /// </summary>
        public string ViewUrl { get; set; }

        /// <summary>
        /// 用户信息URL
        /// </summary>
        public string UserInfoUrl { get; set; }
    }

    /// <summary>
    /// 权限模型
    /// </summary>
    public class BookReMyPermissionModel
    {
        /// <summary>
        /// 是否是管理员
        /// </summary>
        public bool IsAdmin { get; set; }

        /// <summary>
        /// 是否可以查看他人回复
        /// </summary>
        public bool CanViewOthers { get; set; }

        /// <summary>
        /// 是否在查看自己的回复
        /// </summary>
        public bool IsViewingSelf { get; set; }

        /// <summary>
        /// 当前用户ID
        /// </summary>
        public string CurrentUserId { get; set; }

        /// <summary>
        /// 目标用户ID
        /// </summary>
        public string TargetUserId { get; set; }

        /// <summary>
        /// 是否可以搜索
        /// </summary>
        public bool CanSearch { get; set; }

        /// <summary>
        /// 是否为受限用户（普通用户访问ID=1000时）
        /// </summary>
        public bool IsRestrictedUser { get; set; }
    }

    /// <summary>
    /// 管理员操作模型
    /// </summary>
    public class BookReMyAdminActionsModel
    {
        /// <summary>
        /// 是否可以清空回复
        /// </summary>
        public bool CanClearReplies { get; set; }

        /// <summary>
        /// 清空回复URL
        /// </summary>
        public string ClearRepliesUrl { get; set; }

        /// <summary>
        /// 目标用户ID
        /// </summary>
        public string TargetUserId { get; set; }

        /// <summary>
        /// 是否显示管理员操作
        /// </summary>
        public bool ShowAdminActions { get; set; }
    }
} 