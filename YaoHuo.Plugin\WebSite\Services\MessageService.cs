using System;
using System.Runtime.Caching;
using KeLin.ClassManager;
using YaoHuo.Plugin.WebSite.Tool;

namespace YaoHuo.Plugin.WebSite.Services
{
    /// <summary>
    /// 消息服务：提供未读消息计数
    /// </summary>
    public static class MessageService
    {
        private const int CacheSeconds = 30;   // 30秒缓存，平衡性能与一致性
        
        // 创建带大小限制的内存缓存
        private static readonly MemoryCache _cache = new MemoryCache("MessageService", new System.Collections.Specialized.NameValueCollection
        {
            { "cacheMemoryLimitMegabytes", "50" }, // 限制50MB内存使用
            { "physicalMemoryLimitPercentage", "10" } // 限制物理内存10%
        });

        /// <summary>
        /// 获取用户未读数量（带30秒缓存）
        /// </summary>
        public static int GetUnreadCountCached(long siteId, long userId)
        {
            if (userId <= 0) return 0;

            string cacheKey = $"unreadCount:{siteId}:{userId}";
            if (_cache.Get(cacheKey) is int cached)
            {
                return cached;
            }

            int unread = GetUnreadCountFromDb(siteId, userId);
            
            // 设置缓存项，带过期时间和大小限制
            var policy = new CacheItemPolicy
            {
                AbsoluteExpiration = DateTimeOffset.Now.AddSeconds(CacheSeconds),
                Priority = CacheItemPriority.Default
            };
            
            _cache.Set(cacheKey, unread, policy);
            return unread;
        }

        private static int GetUnreadCountFromDb(long siteId, long userId)
        {
            try
            {
                string conn = PubConstant.GetConnectionString(PubConstant.GetAppString("InstanceName"));
                string sql = "SELECT COUNT(id) FROM wap_message WITH(NOLOCK) WHERE siteid=@SiteId AND touserid=@UserId AND isnew=1";
                return DapperHelper.ExecuteScalar<int>(conn, sql, new { SiteId = siteId, UserId = userId });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[MessageService] DB查询失败: {ex.Message}");
                return 0;
            }
        }
    }
} 