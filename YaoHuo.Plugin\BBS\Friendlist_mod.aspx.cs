﻿using KeLin.ClassManager;
using KeLin.ClassManager.Model;
using System;
using System.Linq;
using System.Web;
using YaoHuo.Plugin.WebSite;
using YaoHuo.Plugin.WebSite.Tool;
using YaoHuo.Plugin.Tool;

namespace YaoHuo.Plugin.BBS
{
	public class FriendList_Mod : MyPageWap
    {
		private string string_10 = PubConstant.GetAppString("InstanceName");

		public string action = "";

		public string linkURL = "";

		public string condition = "";

		public string ERROR = "";

		public string string_11 = "";

		public string friendtype = "";

		public string id = "";

		public string backurl = "";

		public string INFO = "";

		public string page = "";

		public string touserid = "";

		public wap_friends_Model bookVo = null;

		protected void Page_Load(object sender, EventArgs e)
		{
			action = GetRequestValue("action");
			id = GetRequestValue("id");
			touserid = GetRequestValue("touserid");
			page = GetRequestValue("page");
			friendtype = GetRequestValue("friendtype");
			backurl = GetRequestValue("backurl");
			if (backurl == null || backurl == "")
			{
				backurl = base.Request.Form.Get("backurl");
			}
			if (backurl == null || backurl == "")
			{
				backurl = "myfile.aspx?siteid=" + siteid;
			}
			backurl = ToHtm(backurl);
			backurl = HttpUtility.UrlDecode(backurl);
			backurl = WapTool.URLtoWAP(backurl);
			IsLogin(userid, backurl);
			// ✅ 使用DapperHelper进行安全的参数化查询，消除SQL注入风险
			string connectionString = PubConstant.GetConnectionString(string_10);

			if (id != "")
			{
				// 通过ID查询好友记录
				string selectByIdSql = "SELECT * FROM wap_friends WHERE id = @FriendId";
				bookVo = DapperHelper.Query<wap_friends_Model>(connectionString, selectByIdSql, new {
					FriendId = DapperHelper.SafeParseLong(id, "好友记录ID")
				}).FirstOrDefault();
			}
			else if (touserid != "")
			{
				// 通过用户ID查询好友记录
				string selectByUserSql = @"SELECT * FROM wap_friends
				                          WHERE siteid = @SiteId AND userid = @UserId
				                          AND friendtype = 0 AND frienduserid = @FriendUserId";
				bookVo = DapperHelper.Query<wap_friends_Model>(connectionString, selectByUserSql, new {
					SiteId = DapperHelper.SafeParseLong(siteid, "站点ID"),
					UserId = DapperHelper.SafeParseLong(userid, "用户ID"),
					FriendUserId = DapperHelper.SafeParseLong(touserid, "好友用户ID")
				}).FirstOrDefault();
			}
			if (bookVo == null)
			{
				ShowTipInfo("Ta还不是我的好友，请先添加为好友。", backurl);
			}
			if (bookVo.siteid != siteVo.siteid)
			{
				ShowTipInfo("非本站记录", backurl);
			}
			if (bookVo.userid != userVo.userid)
			{
				ShowTipInfo("Ta还不是我的好友，请先添加为好友。", backurl);
			}
			if (action == "gomod")
			{
				// ✅ 使用DapperHelper进行安全的参数化更新操作
				string updateSql = "UPDATE wap_friends SET friendusername = @FriendUsername WHERE id = @FriendId";
				DapperHelper.Execute(connectionString, updateSql, new {
					FriendUsername = DapperHelper.LimitLength(WapTool.Left(GetRequestValue("remark"), 30), 50),
					FriendId = bookVo.id
				});
				INFO = "OK";
			}
		}
	}
}